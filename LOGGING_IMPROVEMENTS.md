# Logging System Improvements

## Changes Made to Fix Color Display Issues

We've made several improvements to the logging system to ensure proper display of colors in the console:

1. **Switched from Loguru Tags to ANSI Escape Codes**:
   - Replaced Loguru's tag-based color formatting (`<red>text</red>`) with direct ANSI escape codes
   - Created a `Colors` class with constants for all common color codes
   - Modified all formatting functions to use ANSI codes directly

2. **Updated Node Execution Formatting**:
   - Modified `format_node_start()`, `format_node_end()`, and `format_node_error()` to use ANSI codes
   - Ensured proper color resets to prevent color bleed in the terminal

3. **Enhanced NRQL Query Formatting**:
   - Updated `format_nrql_query()` and `format_nrql_results()` to use ANSI codes
   - Improved box drawing and alignment for better readability

4. **Added Warning Filters**:
   - Added filters to suppress Pydantic JSON schema warnings that were cluttering the logs

5. **Created Testing and Documentation Tools**:
   - Added `test_colors.py` script to verify color display in different terminals
   - Created `fix_logging.py` with instructions for updating code using the old formatting
   - Updated `LOGGING.md` with detailed guidance on using the new color system

## Files Modified

- `lib/new_relic/logger.py`: Core logging implementation with ANSI colors
- `ai_incident_manager/workflow/test_incremental_graph.py`: Node execution logging
- `lib/new_relic/query.py`: NRQL query logging

## How to Use the New System

### Basic Color Usage

```python
from lib.new_relic.logger import Colors, get_logger

logger = get_logger(__name__)
logger.info(f"{Colors.GREEN}Success!{Colors.RESET} Operation completed.")
```

### Formatting Functions

Continue using the existing formatting functions which now use ANSI colors internally:

```python
from lib.new_relic.logger import format_node_start, format_nrql_query

logger.info(format_node_start("my_node", "incident-123"))
logger.info(format_nrql_query("SELECT * FROM Transaction"))
```

## Testing the Changes

Run the following scripts to test the changes:

1. Basic color display: `python test_colors.py`
2. Full logging example: `python lib/new_relic/logging_example.py`

## Troubleshooting

If you encounter issues with color display in your terminal:

1. Ensure your terminal supports ANSI escape codes
2. Try setting `export TERM=xterm-color` in your shell
3. For complex multi-line colored output, consider using `print()` to stderr instead of logger

See `fix_logging.py` for additional instructions on updating code that uses the old color formatting. 