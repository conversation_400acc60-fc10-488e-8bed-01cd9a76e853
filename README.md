# AI Incident Management System

An AI-powered incident management system using LangGraph workflows and Pydantic AI Agents to orchestrate comprehensive alert analysis, root cause analysis (RCA), remediation, and structured record-keeping.

## Overview

This system integrates with New Relic to process alerts, perform in-depth analysis, and report findings through Azure DevOps tickets and Microsoft Teams notifications. The process includes:

1. Alert ingestion and parsing
2. Entity analysis and relationship mapping
3. Runbook execution
4. Root cause analysis
5. Topology generation
6. Reporting results to Azure DevOps and Microsoft Teams

## Setup

### Prerequisites

- Python 3.10+
- [Rye](https://rye-up.com/) for Python dependency management
- Azure OpenAI API access (GPT-4o model)
- New Relic API access
- Azure DevOps access
- Microsoft Teams webhook

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# New Relic
NEWRELIC_API_KEY=your_newrelic_api_key
NEWRELIC_ACCOUNT_ID=your_newrelic_account_id

# Azure OpenAI
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_DEPLOYMENT=gpt-4o
AZURE_OPENAI_API_VERSION=2023-05-15

# Azure DevOps
ADO_PERSONAL_ACCESS_TOKEN=your_ado_personal_access_token
ADO_ORGANIZATION=your_organization
ADO_PROJECT=your_project
ADO_API_VERSION=7.0
ADO_AREA_PATH=your_area_path

# Microsoft Teams
TEAMS_WEBHOOK_URL=your_teams_webhook_url

# MongoDB (optional, for enhanced data storage)
MONGODB_CONNECTION_STRING=your_mongodb_connection_string
```

### Installation

Using Rye:

```bash
# Clone the repository
git clone https://github.com/your-org/ai-incident-management.git
cd ai-incident-management

# Install dependencies
rye sync
```

### Docker Setup

```bash
# Build the Docker image
docker build -t ai-incident-management .

# Run the container with environment variables
docker run -d --name incident-manager \
  --env-file .env \
  ai-incident-management
```

## Using the System

### Running the Test Workflow

```bash
python -m ai_incident_manager.workflow.test_incremental_graph
```

This will run the workflow with a test alert and demonstrate the full incident analysis pipeline.

### Integration with Azure DevOps

The system will:

1. Create a ticket when a new incident is detected
2. Update the ticket with a detailed HTML report once analysis is complete
3. Include formatted findings with root cause, remediation actions, and entity details

### Integration with Microsoft Teams

The system will send an adaptive card to the specified Teams webhook with:

1. Incident summary
2. Root cause
3. Key runbook steps executed
4. Link to the Azure DevOps ticket for detailed information

## Architecture

The system uses a modular design with:

- **LangGraph Workflow**: Orchestrates the analysis process through clearly defined steps
- **Pydantic AI Agents**: Specialized agents for different tasks like alert parsing, entity analysis, etc.
- **Service Classes**: Modular services to handle Azure DevOps, Microsoft Teams, and other integrations
- **Reporting Agent**: Handles formatting and sending detailed reports across different platforms

## Extending the System

### Adding New Alert Sources

Implement a new parser in `ai_incident_manager/agents/alert_parser_agent.py` to support additional alert sources.

### Adding New Entity Types

Add new entity types in `ai_incident_manager/config/entity_types.yaml`.

### Adding New Runbooks

Add new runbooks in `ai_incident_manager/config/runbooks.yaml`.

## Troubleshooting

- **API Connection Issues**: Check your API keys and network connectivity
- **AI Response Quality**: Review the logs for any LLM request issues
- **MongoDB Storage**: Ensure MongoDB is running and accessible
- **Reporting Failures**: Check Azure DevOps permissions and Teams webhook URL