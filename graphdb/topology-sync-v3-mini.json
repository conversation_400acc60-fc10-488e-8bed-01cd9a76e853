{"nodes": [{"additionalInfo": "Entity in the network: GENERIC_INFRASTRUCTURE_ENTITY - INFRA", "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "group": 1, "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "id": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "issue": true, "name": "primary-na1-eks", "tags": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "displayName": ["primary-na1-eks"], "instrumentation.provider": ["newRelic"], "trustedAccountId": ["1093620"]}, "type": "KUBERNETESCLUSTER"}, {"additionalInfo": "Entity in the network: GENERIC_INFRASTRUCTURE_ENTITY - INFRA", "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "group": 1, "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "id": "MTA5MzYyMHxJTkZSQXxOQXwzOTQwNzk3NDU5NTkyMzU2Nzg2", "issue": false, "name": "kong-provisioning", "tags": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "k8s.clusterName": ["primary-na1-eks"], "k8s.jobName": ["kong-provisioning"], "k8s.namespaceName": ["na1-kong-100111-kong-00002"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.24.1"], "trustedAccountId": ["1093620"]}, "type": "KUBERNETES_JOB"}, {"additionalInfo": "Entity in the network: GENERIC_INFRASTRUCTURE_ENTITY - INFRA", "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "group": 1, "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "id": "MTA5MzYyMHxJTkZSQXxOQXwxNDA2NDYxMjk4NDE5MjI4NTY", "issue": false, "name": "healthcheck", "tags": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "k8s.clusterName": ["primary-na1-eks"], "k8s.deploymentName": ["healthcheck"], "k8s.namespaceName": ["na1-kong-100111-kong-00002"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.24.1"], "trustedAccountId": ["1093620"]}, "type": "KUBERNETES_DEPLOYMENT"}, {"additionalInfo": "Entity in the network: GENERIC_INFRASTRUCTURE_ENTITY - INFRA", "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "group": 1, "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "id": "MTA5MzYyMHxJTkZSQXxOQXw3MjM2Nzg2OTA0MjAyNTI4MTY2", "issue": false, "name": "vault-config-update-job-na1-kong-100111-kong-00002-con", "tags": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "k8s.clusterName": ["primary-na1-eks"], "k8s.jobName": ["vault-config-update-job-na1-kong-100111-kong-00002-con"], "k8s.namespaceName": ["na1-vault-001"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.24.1"], "trustedAccountId": ["1093620"]}, "type": "KUBERNETES_JOB"}, {"additionalInfo": "Entity in the network: GENERIC_INFRASTRUCTURE_ENTITY - INFRA", "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "group": 1, "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "id": "MTA5MzYyMHxJTkZSQXxOQXw4MzMwNjU0MDQxNzQ1NjE3ODM0", "issue": false, "name": "kong-connector-provisioning", "tags": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "k8s.clusterName": ["primary-na1-eks"], "k8s.jobName": ["kong-connector-provisioning"], "k8s.namespaceName": ["na1-kong-100111-kong-00002"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.24.1"], "trustedAccountId": ["1093620"]}, "type": "KUBERNETES_JOB"}, {"additionalInfo": "Entity in the network: GENERIC_INFRASTRUCTURE_ENTITY - INFRA", "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "group": 1, "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "id": "MTA5MzYyMHxJTkZSQXxOQXwzMjg5OTA4ODI3NTY4MjIzODgy", "issue": false, "name": "connector-service-6fcb6fb6b5-6mdjd", "tags": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "app": ["kong-control-plane"], "app.kubernetes.io/instance": ["kong-connector"], "app.kubernetes.io/managed-by": ["<PERSON><PERSON>"], "app.kubernetes.io/name": ["connector-service"], "helm.sh/chart": ["kong-connector-104.0.0-10.release"], "k8s-app": ["kong-control-plane"], "k8s.clusterName": ["primary-na1-eks"], "k8s.createdBy": ["connector-service-6fcb6fb6b5"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["connector-service"], "k8s.namespaceName": ["na1-kong-100111-kong-00002"], "k8s.nodeName": ["ip-172-19-147-147.ec2.internal"], "k8s.podName": ["connector-service-6fcb6fb6b5-6mdjd"], "k8s.replicasetName": ["connector-service-6fcb6fb6b5"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.24.1"], "pod-template-hash": ["6fcb6fb6b5"], "trustedAccountId": ["1093620"]}, "type": "KUBERNETES_POD"}], "links": [{"source": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "target": "MTA5MzYyMHxJTkZSQXxOQXwzOTQwNzk3NDU5NTkyMzU2Nzg2", "value": 1}, {"source": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "target": "MTA5MzYyMHxJTkZSQXxOQXwxNDA2NDYxMjk4NDE5MjI4NTY", "value": 1}, {"source": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "target": "MTA5MzYyMHxJTkZSQXxOQXw3MjM2Nzg2OTA0MjAyNTI4MTY2", "value": 1}, {"source": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "target": "MTA5MzYyMHxJTkZSQXxOQXw4MzMwNjU0MDQxNzQ1NjE3ODM0", "value": 1}, {"source": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "target": "MTA5MzYyMHxJTkZSQXxOQXwzMjg5OTA4ODI3NTY4MjIzODgy", "value": 1}, {"source": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "target": "MTA5MzYyMHxJTkZSQXxOQXwtMjM2NjQ1MjA0MzMyMDg2OTkyOQ", "value": 1}]}