import logging
import random
import re
import json
import traceback
import requests
from thefuzz import process as fuzz_process
from rapidfuzz import process as rapidfuzz_process
import os
from dotenv import load_dotenv
import asyncio
from gremlin_python.driver import client, serializer
from gremlin_python.driver.protocol import GremlinServerError
from gremlin_python.driver.aiohttp.transport import AiohttpTransport
from concurrent.futures import ThreadPoolExecutor
import aiohttp
from azure.cosmos import CosmosClient
from azure.cosmos.exceptions import CosmosHttpResponseError
import time
from datetime import timedelta

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv(override=True)

# Cosmos DB configuration
endpoint = os.getenv('COSMOS_GREMLIN_ENDPOINT')
database = "neurons"
collection = "nvu"
key = os.getenv('COSMOS_GREMLIN_KEY')

# Global thread pool executor
thread_pool = ThreadPoolExecutor(max_workers=10)  # Adjust this number based on your system's capabilities
concurrency_semaphore = asyncio.Semaphore(10)

def get_client():
    return client.Client(
        f'{endpoint}',
        'g',
        username=f"/dbs/{database}/colls/{collection}",
        password=key,
        message_serializer=serializer.GraphSONSerializersV2d0(),
        transport_factory=lambda: AiohttpTransport(call_from_event_loop=True)
    )

def parse_retry_after(retry_after):
    try:
        # Try parsing as float (milliseconds)
        return float(retry_after) / 1000.0
    except ValueError:
        # If it's not a float, assume it's a time span string
        try:
            t = timedelta(**{f"{k}s": float(v) for k, v in (x.split('=') for x in retry_after.split(':'))})
            return t.total_seconds()
        except:
            # If all else fails, return a default value
            return 1.0

async def execute_query_with_retry(client, query, bindings=None, max_retries=5):
    retry_count = 0
    while retry_count < max_retries:
        try:
            async with concurrency_semaphore:
                loop = asyncio.get_running_loop()
                future = await loop.run_in_executor(thread_pool, client.submitAsync, query, bindings)
                result_set = await loop.run_in_executor(thread_pool, future.result)
                results = await loop.run_in_executor(thread_pool, result_set.all().result)
            return results
        except GremlinServerError as e:
            if e._status_attributes['x-ms-status-code'] == 429:
                retry_count += 1
                retry_after = parse_retry_after(e.status_attributes.get('x-ms-retry-after-ms', '00:00:01'))
                logger.warning(f"Rate limited. Retrying in {retry_after} seconds... (Attempt {retry_count}/{max_retries})")
                await asyncio.sleep(retry_after)
            else:
                logger.error(f"Query failed: {e.status_code} {e.status_message}")
                raise
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            raise
    logger.error(f"Max retries exceeded for query: {query}")
    raise Exception(f"Max retries exceeded for query: {query}")

async def store_node(client, node, update_existing=True):
    try:
        # Check if node exists
        query = f"g.V('{node['id']}').count()"
        count_result = await execute_query_with_retry(client, query)
        exists = count_result[0] > 0

        if exists:
            if update_existing:
                logger.info(f"Node {node['id']} already exists. Updating properties.")
                update_query = f"g.V('{node['id']}')"
                for key, value in node.items():
                    if key not in ['id', 'type', 'entity_id']:
                        if isinstance(value, (list, dict)):
                            value = json.dumps(value)
                        elif isinstance(value, bool):
                            value = 'true' if value else 'false'
                        elif isinstance(value, (int, float)):
                            value = str(value)
                        elif value is None:
                            continue
                        else:
                            # value = escape_string(str(value))
                            value = str(value)
                        update_query += f".property('{key}', '{value}')"
                result = await execute_query_with_retry(client, update_query)
                logger.info(f"Successfully updated node: {node['id']}")
                return result
            else:
                logger.info(f"Node {node['id']} already exists. Skipping update.")
                return None
        else:
            # skip if type is ISSUE
            if node['type'] == 'ISSUE':
                logger.info(f"Skipping issue node: {node['id']}")
                return None
            # Add new node
            add_query = f"g.addV('{node['type']}')"
            for key, value in node.items():
                if key != 'type':  # Include 'id' for new nodes
                    if isinstance(value, (list, dict)):
                        value = json.dumps(value)
                    elif isinstance(value, bool):
                        value = 'true' if value else 'false'
                    elif isinstance(value, (int, float)):
                        value = str(value)
                    elif value is None:
                        continue
                    else:
                        # value = escape_string(str(value))
                        value = str(value)
                    add_query += f".property('{key}', '{value}')"
            result = await execute_query_with_retry(client, add_query)
            logger.info(f"Successfully stored new node: {node['id']}")
            return result
    except Exception as e:
        logger.error(f"Error storing node {node['id']}: {str(e)}")
        # print tried query
        logger.info(f"Tried query: {add_query}")
    return None

async def store_link(client, link, update_existing=True):
    try:
        # Check if the link already exists
        check_query = f"g.V('{link['source']}').outE('{link['link_type']}').where(inV().has('id', '{link['target']}')).count()"
        count_result = await execute_query_with_retry(client, check_query)
        exists = count_result[0] > 0

        if exists:
            if update_existing:
                logger.info(f"Link from {link['source']} to {link['target']} already exists. Updating properties.")
                update_query = f"g.V('{link['source']}').outE('{link['link_type']}').where(inV().has('id', '{link['target']}'))"
                for key, value in link.items():
                    if key not in ['source', 'target', 'link_type', 'id']:
                        if isinstance(value, (list, dict)):
                            value = json.dumps(value)
                        elif isinstance(value, bool):
                            value = 'true' if value else 'false'
                        elif isinstance(value, (int, float)):
                            value = str(value)
                        elif value is None:
                            continue  # Skip None values
                        else:
                            # value = escape_string(str(value))
                            value = str(value)
                        update_query += f".property('{key}', '{value}')"
                result = await execute_query_with_retry(client, update_query)
                logger.info(f"Successfully updated link: {link['source']} -> {link['target']}")
                return result
            else:
                logger.info(f"Link from {link['source']} to {link['target']} already exists. Skipping update.")
                return None
        else:
            # Add new link
            add_query = f"g.V('{link['source']}').addE('{link['link_type']}').to(g.V('{link['target']}'))"
            for key, value in link.items():
                if key not in ['source', 'target', 'link_type', 'id']:
                    if isinstance(value, (list, dict)):
                        value = json.dumps(value)
                    elif isinstance(value, bool):
                        value = 'true' if value else 'false'
                    elif isinstance(value, (int, float)):
                        value = str(value)
                    elif value is None:
                        continue  # Skip None values
                    else:
                        # value = escape_string(str(value))
                        value = str(value)
                    add_query += f".property('{key}', '{value}')"

            result = await execute_query_with_retry(client, add_query)
            logger.info(f"Successfully stored new link: {link['source']} -> {link['target']}")
            return result
    except Exception as e:
        logger.error(f"Error storing link {link['source']} -> {link['target']}: {str(e)}")
        return None

async def process_batch(client, batch, update_existing=True):
    semaphore = asyncio.Semaphore(10)  # Adjust as needed

    async def process_item(item):
        async with semaphore:
            if 'source' in item:
                await store_link(client, item, update_existing)
            else:
                await store_node(client, item, update_existing)

    tasks = [process_item(item) for item in batch]
    await asyncio.gather(*tasks)

async def store_topology_data(client, topology_data, update_existing=False):
    batch_size = 50  # Adjust this value based on your system's capabilities
    nodes = []
    links = []
    for source, data in topology_data:
        logger.info(f"Processing source: {source}")
        nodes.extend(data.get('nodes', []))
        links.extend(data.get('links', []))

    # Process nodes first
    node_batches = [nodes[i:i + batch_size] for i in range(0, len(nodes), batch_size)]
    semaphore = asyncio.Semaphore(3)  # Limit concurrent batches

    async def process_batch_with_semaphore(batch):
        async with semaphore:
            return await process_batch(client, batch, update_existing)

    node_tasks = [process_batch_with_semaphore(batch) for batch in node_batches]
    await asyncio.gather(*node_tasks)

    # Then process links
    link_batches = [links[i:i + batch_size] for i in range(0, len(links), batch_size)]
    link_tasks = [process_batch_with_semaphore(batch) for batch in link_batches]
    await asyncio.gather(*link_tasks)

async def get_node_by_id(client, node_id):
    try:
        query = f"g.V('{node_id}').valueMap(true)"
        result = await execute_query_with_retry(client, query)
        return result[0] if result else None
    except Exception as e:
        logger.error(f"Error getting node by ID {node_id}: {str(e)}")
        return None

async def get_node_by_name(client, name, threshold=80):
    try:
        # First, try to get the node by name directly
        query = f"g.V().has('name', '{escape_string(name)}').valueMap(true)"
        results = await execute_query_with_retry(client, query)

        if results:
            return results[0]  # Return the node details if found

        # If not found, get all node names and perform fuzzy matching
        query = "g.V().values('name')"
        all_names = await execute_query_with_retry(client, query)

        best_match = fuzz_process.extractOne(name, all_names)
        if best_match and best_match[1] >= threshold:
            # If a close match is found, get the node details associated with that name
            closest_name = best_match[0]
            query = f"g.V().has('name', '{escape_string(closest_name)}').valueMap(true)"
            results = await execute_query_with_retry(client, query)
            return results[0] if results else None

        return None  # No match found
    except Exception as e:
        logger.error(f"Error getting node by name {name}: {str(e)}")
        return None

async def get_subgraph(client, start_node_id, depth, batch_size=100):
    try:
        nodes = set()
        edges = []

        # Get the starting node
        query = f"g.V('{start_node_id}').valueMap(true)"
        result = await execute_query_with_retry(client, query)
        if result:
            nodes.add((start_node_id, json.dumps(result[0])))

        # BFS to get nodes and edges
        current_depth = 0
        frontier = {start_node_id}
        while current_depth < depth and frontier:
            next_frontier = set()
            for node_id in frontier:
                # Get connected nodes
                query = f"""
                g.V('{node_id}').both().dedup()
                    .project('id', 'properties')
                    .by(id())
                    .by(valueMap(true))
                """
                connected_nodes = await execute_query_with_retry(client, query)

                for connected_node in connected_nodes:
                    node_id = connected_node['id']
                    if node_id not in nodes:
                        nodes.add((node_id, json.dumps(connected_node['properties'])))
                        next_frontier.add(node_id)

                # Get edges
                query = f"""
                g.V('{node_id}').bothE()
                    .project('id', 'source', 'target', 'label', 'properties')
                    .by(id())
                    .by(outV().id())
                    .by(inV().id())
                    .by(label())
                    .by(valueMap())
                """
                connected_edges = await execute_query_with_retry(client, query)
                edges.extend(connected_edges)

            frontier = next_frontier
            current_depth += 1

        # Convert sets to lists for JSON serialization and process node properties
        nodes_list = []
        node_ids = set()
        for node_id, node_properties in nodes:
            node_data = {'id': node_id, **json.loads(node_properties)}
            for key, value in node_data.items():
                if isinstance(value, list):
                    if key == 'properties':
                        # Parse the 'properties' string and convert its values
                        props = json.loads(value[0])
                        for prop_key, prop_value in props.items():
                            if isinstance(prop_value, list):
                                props[prop_key] = prop_value[0] if prop_value else ''
                        node_data[key] = json.dumps(props)
                    else:
                        node_data[key] = value[0] if value else ''
            nodes_list.append(node_data)
            node_ids.add(node_id)

        # Filter and simplify edge properties
        filtered_edges = []
        for edge in edges:
            if edge['source'] in node_ids and edge['target'] in node_ids:
                for key, value in edge['properties'].items():
                    if isinstance(value, list) and len(value) == 1:
                        edge['properties'][key] = value[0]
                filtered_edges.append(edge)

        return {
            'nodes': nodes_list,
            'edges': filtered_edges
        }

    except Exception as e:
        logger.error(f"Error getting subgraph for node {start_node_id}: {str(e)}")
        return None

def escape_string(s):
    """Escape special characters in a string for Gremlin query."""
    return re.sub(r'(["\\\'])', r'\\\1', str(s))

async def get_labels(client):
    try:
        query = "g.V().label().dedup()"
        results = await execute_query_with_retry(client, query)
        return results
    except Exception as e:
        logger.error(f"Error getting labels: {str(e)}")
        raise

async def drop_graph(client):
    try:
        # Get all labels
        labels = await get_labels(client)
        logger.info(f"Found labels: {labels}")

        # Drop vertices and edges for each label
        for label in labels:
            offset = 0
            batch_size = 50  # Reduced batch size
            while True:
                vertex_query = f"g.V().hasLabel('{label}').range({offset}, {offset + batch_size}).drop()"
                try:
                    await execute_query_with_retry(client, vertex_query, max_retries=5)
                    logger.info(f"Dropped batch of vertices with label: {label}, offset: {offset}")
                    offset += batch_size
                except GremlinServerError as e:
                    if e._status_attributes['x-ms-status-code'] == 429:
                        retry_after = parse_retry_after(e.status_attributes.get('x-ms-retry-after-ms', '00:00:01'))
                        logger.warning(f"Rate limited. Waiting for {retry_after} seconds before retrying...")
                        await asyncio.sleep(retry_after)
                        continue
                    else:
                        raise

                # Check if we've processed all vertices of this label
                count_query = f"g.V().hasLabel('{label}').count()"
                remaining_count = await execute_query_with_retry(client, count_query)
                if remaining_count[0] == 0:
                    break

                # Add a small delay between batches to avoid rate limiting
                await asyncio.sleep(1)

        logger.info("Graph database cleared successfully.")
    except Exception as e:
        logger.error(f"Error dropping graph: {str(e)}")
        raise

async def main(cleanup=False):
    client = None
    try:
        # Create a single client instance
        client = get_client()

        # Drop the existing graph before storing new data if cleanup is True
        if cleanup:
            logger.info("Cleaning up existing graph data...")
            await drop_graph(client)
        else:
            logger.info("Skipping graph cleanup.")

        # Load topology data from file
        with open(os.path.join(os.path.dirname(__file__), 'topology_nvu_v3.json'), 'r') as f:
            topology_data = json.load(f)

        # Store topology data
        await store_topology_data(client, topology_data, update_existing=not cleanup)

        # # Query node by ID
        # node = await get_node_by_id(client, 'MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz')
        # print("Node by ID:", node)

        # Query node by name (fuzzy search)
        # node = await get_node_by_name(client, 'action-svc')
        # print("Node by name:", node)

        # Get subgraph
        subgraph = await get_subgraph(client, 'MTA5MzYyMHxJTkZSQXxOQXwtMjg2NTg5NDE4MDIyMTM3MzMwNQ', 1)
        # in file from current directory
        with open(os.path.join(os.path.dirname(__file__), 'nvu_subgraph_v4.json'), 'w') as f:
            json.dump(subgraph, f, indent=2)
        print("Subgraph:", json.dumps(subgraph, indent=2))

        # posting to d3 server

        issue_id = "f512614b-e3d0-496c-9e6f-6f839a31d019"

        # Get HTML reports for the issue and RCA details
        topo_link = f"{os.environ['D3_SERVER_GLOBAL_URL']}/{issue_id}"
        # ticket_id_html, topo_link_html, issue_details_html, rca_details_html  = generate_html_report(topo_link, issue_details, rca_details, ticket_id)

        data = {"nodes": subgraph['nodes'],
                "links": subgraph['edges'],
                "rca": "<div style=\"background-color: white; padding: 24px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\">\n        <table style=\"width: 100%; margin-bottom: 32px; border-collapse: collapse; table-layout: auto; border: 1px solid #ddd;\">\n            <tr style=\"border-bottom: 1px solid #ddd;\">\n                <td style=\"border-bottom: 1px solid #ddd; font-weight: bold; width: 50%; padding-right: 16px; padding-bottom: 8px;\">Problem Reference:</td>\n                <td style=\"border-bottom: 1px solid #ddd; width: 50%; padding-bottom: 8px;\">f512614b-e3d0-496c-9e6f-6f839a31d019</td>\n            </tr>\n            <tr>\n                <td style=\"border: 1px solid #ddd; font-weight: bold; width: 50%; padding-right: 16px; padding-bottom: 8px;\">Customer affected:</td>\n                <td style=\"border: 1px solid #ddd; width: 50%; padding-bottom: 8px;\">aquila-na1</td>\n            </tr>\n            <tr>\n                <td style=\"border: 1px solid #ddd; font-weight: bold; width: 50%; padding-right: 16px; padding-bottom: 8px;\">Date Incident Began:</td>\n                <td style=\"border: 1px solid #ddd; width: 50%; padding-bottom: 8px;\">Timestamp not converted</td>\n            </tr>\n            <tr>\n                <td style=\"border: 1px solid #ddd; font-weight: bold; width: 50%; padding-right: 16px; padding-bottom: 8px;\">Date Incident Resolved:</td>\n                <td style=\"border: 1px solid #ddd; width: 50%; padding-bottom: 8px;\">Timestamp not converted</td>\n            </tr>\n            <tr>\n                <td style=\"border: 1px solid #ddd; font-weight: bold; width: 50%; padding-right: 16px;\">Teams(s) Involved:</td>\n                <td style=\"border: 1px solid #ddd; width: 50%;\">Database Management Team</td>\n            </tr>\n        </table>\n        <div style=\"border-top: 2px solid black; padding-top: 24px; margin-bottom: 24px;\">\n            <div style=\"background-color: #e2e8f0; padding: 8px;\">\n                <p style=\"font-weight: bold;\">Customer Impact</p>\n            </div>\n            <div style=\"border: 1px solid #d1d5db; padding: 16px; margin-bottom: 16px;\">\n                <p>Performance degradation due to high swap usage</p>\n            </div>\n        </div>\n        <div style=\"border-top: 2px solid black; padding-top: 24px; margin-bottom: 24px;\">\n            <div style=\"background-color: #e2e8f0; padding: 8px;\">\n                <p style=\"font-weight: bold;\">Root Cause</p>\n            </div>\n            <div style=\"border: 1px solid #d1d5db; padding: 16px; margin-bottom: 16px;\">\n                <p>The high swap usage on the RDS instance is likely due to insufficient memory allocation or inefficient memory management. When the allocated memory is exhausted, the system resorts to using swap space, which is significantly slower. This can be exacerbated by high IOPS or CPU usage, leading to performance bottlenecks. Additionally, certain configurations or workloads might not be optimized, causing excessive memory consumption. Monitoring and adjusting these parameters can help alleviate the issue.</p>\n            </div>\n        </div>\n        <div style=\"border-top: 2px solid black; padding-top: 24px; margin-bottom: 24px;\">\n            <div style=\"background-color: #e2e8f0; padding: 8px;\">\n                <p style=\"font-weight: bold;\">Remedy</p>\n            </div>\n            <div style=\"border: 1px solid #d1d5db; padding: 16px; margin-bottom: 16px;\">\n                <p>To address the high swap usage, consider increasing the instance size to provide more RAM, optimizing database queries, and reviewing the configuration settings for memory-intensive operations. Implementing enhanced monitoring can help identify specific processes or queries that are consuming excessive memory.</p>\n            </div>\n        </div>\n        <div style=\"border-top: 2px solid black; padding-top: 24px;\">\n            <div style=\"background-color: #e2e8f0; padding: 8px;\">\n                <p style=\"font-weight: bold;\">Preventive Action</p>\n            </div>\n            <div style=\"border: 1px solid #d1d5db; padding: 16px; margin-bottom: 16px;\">\n                <p>Regularly monitor memory usage and swap metrics to identify trends before they become critical. Implement automated alerts for high swap usage and review database configurations periodically. Consider using performance insights to optimize queries and workloads, ensuring efficient memory utilization.</p>\n            </div>\n        </div>\n    </div>\n",
                "issue_info": "<body style=\"font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 20px;\">\n        <div style=\"background-color: #fff; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); margin: 20px auto; padding: 20px;\">\n            <h2 style=\"color: #d9534f;\">New Relic Issue: Critical</h2>\n            <table style=\"width: 100%; border-collapse: collapse;\">\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Issue ID</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">f512614b-e3d0-496c-9e6f-6f839a31d019</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Issue URL</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\"><a href=\"https://radar-api.service.newrelic.com/accounts/1093620/issues/f512614b-e3d0-496c-9e6f-6f839a31d019?notifier=WEBHOOK\" style=\"color: #0275d8;\">Issue Link</a></td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Title</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">aquila-na1 query result is > 1.0E8 for 30 minutes on 'Swap Usage High on RDS Instance'</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Priority</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; color: #d9534f;\"><strong>CRITICAL</strong></td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Impacted Entities</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">aquila-na1</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Total Incidents</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">1</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">State</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; color: #d9534f;\">ACTIVATED</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Trigger</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">STATE_CHANGE</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Is Correlated</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">false</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Created At</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">1724669187154</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Updated At</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">1724669187154</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Sources</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">newrelic</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Alert Policy Names</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">MI Alert - RDS</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Alert Condition Names</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">Swap Usage High on RDS Instance</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Workflow Name</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">obv-ai-processing</td>\n                </tr>\n                <tr>\n                    <td style=\"border: 1px solid #ddd; padding: 8px; background-color: #f9f9f9;\"><strong style=\"color: #5bc0de;\">Chart</strong></td>\n                    <td style=\"border: 1px solid #ddd; padding: 8px;\">\n                        <a href=\"https://gorgon.nr-assets.net/image/b5241313-03ab-4af8-91d0-534a8ab4895c?config.legend.enabled=false&width=400&height=210\">\n                            <img src=\"https://gorgon.nr-assets.net/image/b5241313-03ab-4af8-91d0-534a8ab4895c?config.legend.enabled=false&width=400&height=210\" alt=\"Chart\" style=\"max-width: 100%; height: auto;\n                            border-radius: 4px;\"/>\n                        </a>\n                    </td>\n                </tr>\n            </table>\n        </div>\n    </body>\n    ",
                "tags": "{\n    \"aws.rds.dbInstanceClass\": \"db.t3.medium\",\n    \"aws.rds.caCertificateIdentifier\": \"rds-ca-rsa2048-g1\",\n    \"engine\": \"postgres\",\n    \"aws.rds.engine\": \"postgres\",\n    \"aws.Arn\": \"arn:aws:rds:us-east-1:************:db:aquila-na1\",\n    \"newrelic.cloudIntegrations.providerAccountId\": \"188490\",\n    \"tags.Owner\": \"<EMAIL>\",\n    \"displayName\": \"aquila-na1\",\n    \"aws.rds.storageEncrypted\": \"true\",\n    \"aws.rds.endpoint\": \"aquila-na1.cb6ktf3dtynu.us-east-1.rds.amazonaws.com\",\n    \"tags.Component\": \"aquila\",\n    \"aws.accountId\": \"************\",\n    \"aws.rds.dbSubnetGroup\": \"sre-polaris-primary-aws-na-us-east-1-vpc-dbsubnetgroup-1imjnpknvwwlv\",\n    \"aws.rds.dbInstanceStatus\": \"available\",\n    \"aws.rds.allocatedStorageBytes\": \"2.147483648E10\",\n    \"aws.rds.storageType\": \"gp2\",\n    \"newrelic.cloudIntegrations.providerExternalId\": \"************\",\n    \"trustedAccountId\": \"1093620\",\n    \"tags.Downtime\": \"off\",\n    \"tags.Project\": \"phoenix\",\n    \"account\": \"IvantiCloud - US\",\n    \"aws.region\": \"us-east-1\",\n    \"tags.Env\": \"prod\",\n    \"collector.name\": \"cloudwatch-metric-streams\",\n    \"instrumentation.provider\": \"aws\",\n    \"tags.Team\": \"51\",\n    \"aws.rds.DBInstanceIdentifier\": \"aquila-na1\",\n    \"aws.rds.licenseModel\": \"postgresql-license\",\n    \"aws.rds.dbInstancePort\": \"0\",\n    \"tags.Name\": \"prod_phoenix_aquila_us-east-1_01\",\n    \"aws.awsRegion\": \"us-east-1\",\n    \"newrelic.cloudIntegrations.providerAccountName\": \"51-Prod-MICloud-IE_TYO_AUS-************-RW\",\n    \"aws.rds.publiclyAccessible\": \"false\",\n    \"providerAccountName\": \"51-Prod-MICloud-IE_TYO_AUS-************-RW\",\n    \"aws.rds.engineVersion\": \"13.11\",\n    \"aws.rds.multiAz\": \"true\",\n    \"tags.CopyDBSnapshot\": \"True\",\n    \"tags.Service\": \"mi_cloud_na1\",\n    \"accountId\": \"1093620\"\n}"}
        # print(json.dumps(data, indent=4))

        # push to cosmos db
        # Store all the data in cosmos db based on issue_id
        cosmos_client = CosmosClient(os.getenv("COSMOS_ENDPOINT"), os.getenv("COSMOS_KEY"))
        database_name = "d3"
        container_name = "d3"
        database = cosmos_client.get_database_client(database_name)
        container = database.get_container_client(container_name)
        container.upsert_item({
            "id": issue_id,
            "nodes": data["nodes"],
            "links": data["links"],
            "rca": data["rca"],
            "issue_info": data["issue_info"],
            "tags": data["tags"]
        })

    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        logger.info(f"Exception details: {traceback.format_exc()}")
        logger.exception("Exception details:")
    finally:
        if client:
            client.close()
        thread_pool.shutdown()

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Process graph data with optional cleanup.")
    parser.add_argument('--cleanup', action='store_true', help='Clean up existing graph data before processing')
    args = parser.parse_args()

    asyncio.run(main(cleanup=args.cleanup))