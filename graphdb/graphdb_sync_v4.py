import time
import json
import os
import re
import logging
import requests
from collections import deque, defaultdict
from concurrent.futures import ThreadPoolExecutor
import concurrent.futures
from dataclasses import dataclass, field
from typing import List, Dict, Any, Union, Tuple, Callable
from dotenv import load_dotenv
import threading
from azure.cosmos import CosmosClient
import math

from utils.misc import light_gpt4_wrapper_autogen, light_gpt3_wrapper_autogen

# Load environment variables
load_dotenv(override=True)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Tag:
    key: str
    values: List[str]

@dataclass
class Entity:
    guid: str
    name: str
    type: str
    entityType: str
    domain: str
    tags: List[Tag] = field(default_factory=list)

    def get_tag_value(self, key: str) -> List[str]:
        for tag in self.tags:
            if tag.key == key:
                return tag.values
        return []

icons = {
    "default": "static/icons/svc.svg",
    "aws_redis": "static/icons/aws_redis.png",
    "aws_opensearch": "static/icons/aws_opensearch.png",
    "elasticsearch": "static/icons/elasticsearch.png",
    "redis": "https://redis.com/wp-content/themes/wpx/assets/images/logo-redis.svg",
    "database": "static/icons/database.png",
    "pod": "static/icons/pod.svg",
    "node": "static/icons/node.svg",
    "deploy": "static/icons/deploy.svg",
    "external": "static/icons/external-service.svg",
    "container": "static/icons/containerinstances.png",
}

class TopologyGenerator:
    def __init__(self, entity_id: str, depth: int = 2, filters: Dict[str, Any] = None, max_workers: int = 10):
        if not entity_id:
            raise ValueError("Entity ID cannot be `None` or empty")
        self.entity_id = entity_id
        self.depth = depth
        self.filters = filters if filters is not None else {}
        self.endpoint = os.getenv("NEWRELIC_API_ENDPOINT", "https://api.newrelic.com/graphql")
        self.api_key = os.getenv("NEWRELIC_API_KEY")
        self.account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
        self.icons = {
            "default": "static/icons/svc.svg",
            "aws_redis": "static/icons/aws_redis.png",
            "aws_opensearch": "static/icons/aws_opensearch.png",
            "elasticsearch": "static/icons/elasticsearch.png",
            "redis": "https://redis.com/wp-content/themes/wpx/assets/images/logo-redis.svg",
            "database": "static/icons/database.png",
            "pod": "static/icons/pod.svg",
            "node": "static/icons/node.svg",
            "deploy": "static/icons/deploy.svg",
            "external": "static/icons/external-service.svg",
            "container": "static/icons/containerinstances.png",
        }
        self.topology = {"nodes": [], "links": []}
        self.processed_entities = set()
        
        # Initialize the queue with the root entity details
        root_entity = self.fetch_entity_details(self.entity_id)
        self.entities_to_process = deque([(root_entity, 0)])  # Initialize with Entity object
    
        self.max_workers = max_workers
        self.lock = threading.Lock()
    
    def fetch_entity_relationships(self, entity_id: str) -> Dict[str, List[Dict[str, Any]]]:
        headers = {"Content-Type": "application/json", "API-Key": self.api_key}
        query = self.build_graphql_query(entity_id)
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.post(self.endpoint, headers=headers, json=query)
                response.raise_for_status()  # Raise an error for bad responses
                return self.parse_graphql_response(response.json())
            except requests.exceptions.ConnectionError as e:
                logger.error(f"Connection error on attempt {attempt + 1} for entity {entity_id}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait before retrying
                else:
                    logger.error(f"Max retries exceeded for entity {entity_id}.")
                    return {}
            except requests.exceptions.HTTPError as e:
                logger.error(f"HTTP error for entity {entity_id}: {e}")
                return {}

    def fetch_entity_details(self, entity_id: str) -> Entity:
        """Fetch the entity details for the root entity."""
        entity_relationships = self.fetch_entity_relationships(entity_id)
        # Get the root entity details from the relationships (source or target)
        for key in entity_relationships:
            for relationship in entity_relationships[key]:
                if relationship["source"].guid == entity_id:
                    return relationship["source"]
                if relationship["target"].guid == entity_id:
                    return relationship["target"]
        # Fallback in case entity not found in relationships
        return Entity(guid=entity_id, name="Unknown", type="", entityType="", domain="")

    def build_graphql_query(self, entity_id: str) -> Dict[str, str]:
        query = {
            "query": f"""
            {{
            actor {{
                entity(guid: "{entity_id}") {{
                name
                relatedEntities {{
                    results {{
                    source {{
                        entity {{
                        guid
                        name
                        type
                        entityType
                        domain
                        tags {{
                            key
                            values
                        }}
                        }}
                    }}
                    target {{
                        entity {{
                        guid
                        name
                        type
                        entityType
                        domain
                        tags {{
                            key
                            values
                        }}
                        }}
                    }}
                    type
                    }}
                }}
                }}
            }}
            }}
            """
        }
        return query

    def parse_graphql_response(self, response_json: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        entity_relationships = {}
        try:
            results = response_json["data"]["actor"]["entity"]["relatedEntities"]["results"]
            for relationship in results:
                source_data = relationship["source"]["entity"]
                target_data = relationship["target"]["entity"]

                source_entity = self.create_entity_from_data(source_data)
                target_entity = self.create_entity_from_data(target_data)

                if source_entity.guid not in entity_relationships:
                    entity_relationships[source_entity.guid] = []

                entity_relationships[source_entity.guid].append({
                    "source": source_entity,
                    "target": target_entity,
                })

        except KeyError:
            logger.warning("Unexpected response structure")
        return entity_relationships

    def create_entity_from_data(self, data: Dict[str, Any]) -> Entity:
        tags = [Tag(tag["key"], tag["values"]) for tag in data.get("tags", [])]
        return Entity(
            guid=data["guid"],
            name=data["name"],
            type=data["type"],
            entityType=data["entityType"],
            domain=data["domain"],
            tags=tags
        )

    def filter_entity(self, entity: Entity) -> bool:
        # Exclude by entity type
        if "types" in self.filters and entity.type in self.filters["types"]:
            return False

        # Exclude by entity ID
        if "ids" in self.filters and entity.guid in self.filters["ids"]:
            return False

        # Exclude by name patterns (supports partial matches)
        if "name_patterns" in self.filters:
            if any(re.search(pattern, entity.name, re.IGNORECASE) for pattern in self.filters["name_patterns"]):
                return False
        
        # Exclude by tags
        if "tags" in self.filters:
            for key, excluded_values in self.filters["tags"].items():
                tag_values = entity.get_tag_value(key)
                logger.info(f"Checking tag '{key}' with values: {tag_values} against excluded values: {excluded_values}")
                if any(value in excluded_values for value in tag_values):
                    logger.info(f"Entity filtered out due to tag: {entity.name} ({entity.guid})")
                    return False

        return True

    def determine_icon(self, entity: Entity) -> str:
        type_icon_mapping = {
            "KUBERNETES_POD": self.icons["pod"],
            "KUBERNETES_DEPLOYMENT": self.icons["deploy"],
            "CONTAINER": self.icons["container"],
            "HOST": self.icons["node"],
            # Add more mappings as needed
        }

        # Check for specific name patterns to determine the icon
        if entity.name.endswith("cache.amazonaws.com"):
            return self.icons["aws_redis"]
        elif "es.amazonaws.com" in entity.name:
            return self.icons["aws_opensearch"]
        elif "redis" in entity.name:
            return self.icons["redis"]
        
        # If name match fails, check the entity type
        if entity.type in type_icon_mapping:
            return type_icon_mapping[entity.type]

        return self.icons["default"]  # Fallback to default icon

    def add_node(self, entity: Entity):
        with self.lock:
            if entity and not any(node["id"] == entity.guid for node in self.topology["nodes"]):
                if not self.filter_entity(entity):
                    logger.info(f"Entity filtered out: {entity.name} ({entity.guid})")
                    return
                
                # # Create a dictionary for additional_info with individual tag entries
                # additional_info = {
                #     "entityType": entity.entityType,
                #     "domain": entity.domain,
                # }

                # # Add each tag as a separate entry in additional_info
                # for tag in entity.tags:
                #     additional_info[tag.key] = tag.values  # Add tag key and values

                self.topology["nodes"].append({
                    "id": entity.guid,
                    "name": entity.name,
                    # "additionalInfo": additional_info,  # Changed to dict
                    "issue": False,
                    "group": 1,
                    "icon": self.determine_icon(entity),
                    "properties": {
                        "account": entity.get_tag_value("account"),
                        "accountId": entity.get_tag_value("accountId"),
                        "domain": entity.domain,
                        "entityType": entity.entityType,
                        "type": entity.type,
                        **{tag.key: tag.values for tag in entity.tags},
                    },
                    "alt_names": [],  # You can populate alternative names here if available
                    "entity_id": entity.guid,
                    "type": entity.type,
                    "entityType": entity.entityType,
                    "domain": entity.domain,
                    "data_source": "live",  # This can be adjusted as needed
                })
    
    def add_link(self, source_id: str, target_id: str):
        with self.lock:
            # Ensure that both source and target entities pass the filter
            source_entity = next((node for node in self.topology["nodes"] if node["id"] == source_id), None)
            target_entity = next((node for node in self.topology["nodes"] if node["id"] == target_id), None)
            
            if source_entity and target_entity:
                if not any(link["source"] == source_id and link["target"] == target_id for link in self.topology["links"]):
                    self.topology["links"].append({
                        "id": f"{source_id}->{target_id}",
                        "source": source_id,
                        "target": target_id,
                        "link_type": "contains",  # This can be customized as needed
                        "properties": {},
                        "data_source": "live",
                        "value": 1,
                    })
            else:
                logger.info(f"Link filtered out: {source_id} -> {target_id}")

    def process_entity_relationships(self, entity: Entity, current_depth: int):
        if current_depth > self.depth:
            logger.info(f"Max depth reached for entity: {entity.name} ({entity.guid})")
            return []

        with self.lock:
            if entity.guid in self.processed_entities:
                logger.info(f"Already processed entity: {entity.name} ({entity.guid})")
                return []
            self.processed_entities.add(entity.guid)

        entity_relationships = self.fetch_entity_relationships(entity.guid)
        new_entities = []

        for relationship in entity_relationships.get(entity.guid, []):
            source_entity = relationship["source"]
            target_entity = relationship["target"]

            if source_entity and target_entity:
                self.add_node(source_entity)
                self.add_node(target_entity)
                self.add_link(source_entity.guid, target_entity.guid)
                logger.info(f"Added link: {source_entity.name} -> {target_entity.name}")

                if target_entity.guid not in self.processed_entities:
                    new_entities.append((target_entity, current_depth + 1))

        return new_entities

    def generate(self):
        entity_count = 0
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(self.process_entity_relationships, entity, depth) for entity, depth in self.entities_to_process]
            
            while futures:
                done, futures = concurrent.futures.wait(futures, return_when=concurrent.futures.FIRST_COMPLETED)
                
                for future in done:
                    new_entities = future.result()
                    for new_entity, new_depth in new_entities:
                        futures.add(executor.submit(self.process_entity_relationships, new_entity, new_depth))
                    
                    entity_count += 1

        logger.info(f"Processed {entity_count} entities")
        data = {"nodes": self.topology["nodes"], "links": self.topology["links"]}
        
        # with open("topology-sync-v4_3.json", "w") as outfile:
        #     json.dump(data, outfile, indent=2)
        #     logger.info("Topology data saved to topology-sync-v4_3.json")
        
        return data
        
# Merge data logic
# Enhanced matching rules with patterns for both source and target

# Custom matching rules
CUSTOM_MATCH_RULES = [
   {
        "source_contains": "debezium",
        "target_contains": "es.amazonaws.com",
        "match_type": "partial"
    },
    {
        "source_contains": "db-postgres",
        "target_contains": "postgresql",
        "match_type": "partial"
    },
    # {
    #     "source_matches": r"cache-redis-\d+",
    #     "target_matches": r"redis-\w+",
    #     "match_type": "regex"
    # },
    # {
    #     "source_matches": r"\w+-phx-aquila-aquila-postgresql",
    #     "target_matches": r"aquila",
    #     "match_type": "regex"
    # },
    {
        "source_matches": r"migration-auriga-service",
        "target_matches": r"(\w+migrdsrpt|mig-primary-\w+-eks-rds|\w+migrdspolaris)",
        "match_type": "regex"
    },
    {
        "source_matches": r"polaris-(champion|challenger)",
        "target_matches": r"(pm1ixc2v1jjvu2h|pmlvchh2j9wc2h)",
        "match_type": "regex"
    },
    # {
    #     "source_matches": "kong-data-plane",
    #     "target_matches": r"primary-\w+-eks(-\d+)?-kong-rds-db",
    #     "match_type": "regex"
    # },
    {
        "source_matches": r"polaris-(champion|challenger)",
        "target_matches": r"polaris-enc-\w+(-read-replica[2]?)?",
        "match_type": "regex"
    },
    {
        "source_matches": r"polaris-(champion|challenger)",
        "target_matches": r"polaris-\w+-r\d+-enc(-read-replica[2]?)?",
        "match_type": "regex"
    },
    {
        "source_matches": r"polaris-(champion|challenger)",
        "target_matches": r"polarisrpt-\w+",
        "match_type": "regex"
    },
    # {
    #     "source_matches": "aquila",
    #     "target_matches": r"sre-polaris-primary-aws-\w+-\w+-\d+-aquila-postgresql",
    #     "match_type": "regex"
    # },
    {
        "source_matches": r"polaris-(champion|challenger)",
        "target_matches": r"\w+prodrdspolaris",
        "match_type": "regex"
    },
    {
        "source_matches": r"polaris-(champion|challenger)",
        "target_matches": r"\w+prodrdsrpt",
        "match_type": "regex"
    }
]

def custom_match(source_value: str, target_value: str) -> bool:
    for rule in CUSTOM_MATCH_RULES:
        if rule["match_type"] == "partial":
            if (rule.get("source_contains", "") in source_value and 
                rule.get("target_contains", "") in target_value):
                logger.info(f"Custom match (partial): {source_value} -> {target_value}")
                return True
        elif rule["match_type"] == "regex":
            if (re.search(rule.get("source_matches", ""), source_value) and 
                re.search(rule.get("target_matches", ""), target_value)):
                logger.info(f"Custom match (regex): {source_value} -> {target_value}")
                return True
    return False

MATCHING_RULES = [
    {
        "source_data": "K8S",
        "target_data": "APM",
        "source_type": "CONTAINER",
        "target_type": "APPLICATION",
        "source_pattern": r"polaris-(.+)",
        "target_pattern": r"nmdm-\w+-\d+-polaris-(.+)",
        "match_field": "name"
    },
    {
        "source_data": "K8S",
        "target_data": "APM",
        "source_type": "CONTAINER",
        "target_type": "SERVICE",
        "source_pattern": r"(jasperreports)",
        "target_pattern": r"(jasperreports)",
        "match_field": "name"
    },
    {
        "source_data": "K8S",
        "target_data": "APM",
        "source_type": "CONTAINER",
        "target_type": "SERVICE",
        "source_pattern": r"(debezium)",
        "target_pattern": r"vpc.+(es\.amazonaws\.com)",
        "match_field": "name",
        "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(aquila)",
        "target_pattern": r"(aquila)-\w+",
        "match_field": "name",
        # "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(migration-auriga-service)",
        "target_pattern": r"(mig-primary-\w+-eks-rds)",
        "match_field": "name",
        "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(polaris)-(champion|challenger)",
        "target_pattern": r"\w+prodrds(polaris).*",
        "match_field": "name",
        # "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"polaris-(champion|challenger)",
        "target_pattern": r"(\w+prodrdsrpt)",
        "match_field": "name",
        "custom_match": custom_match
    },
    {
        "source_data": "CUSTOM_RDS",
        "target_data": "K8S",
        "source_type": "AWSRDSDBINSTANCE",
        "target_type": "CONTAINER",
        "source_pattern": r"\w+-phx-(aquila)-aquila-postgresql",
        "target_pattern": r"(aquila)",
        "match_field": "name",
        # "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(migration-auriga-service)",
        "target_pattern": r"(\w+migrdsrpt)",
        "match_field": "name",
        "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(migration-auriga-service)",
        "target_pattern": r"(\w+migrdspolaris)",
        "match_field": "name",
        "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(polaris-(champion|challenger))",
        "target_pattern": r"(pm1ixc2v1jjvu2h|pmlvchh2j9wc2h)",  # Matches pmlvchh2j9wc2h and pm1ixc2v1jjvu2h
        "match_field": "name",
        "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(kong)-data-plane",
        "target_pattern": r"primary-\w+-eks-(kong)-rds-db",
        "match_field": "name",
        # "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(polaris-(champion|challenger))",
        "target_pattern": r"(polaris-enc-\w+-(read-replica[2]?)?)",
        "match_field": "name",
        "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(polaris-(champion|challenger))",
        "target_pattern": r"(polaris-\w+-r\d+-enc(-read-replica[2]?)?)",
        "match_field": "name",
        "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(polaris-(champion|challenger))",
        "target_pattern": r"(polarisrpt-\w+)",
        "match_field": "name",
        "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(kong)-data-plane",
        "target_pattern": r"primary-\w+-eks-\d+-(kong)-rds-db",
        "match_field": "name",
        # "custom_match": custom_match
    },
    {
        "source_data": "K8S",
        "target_data": "CUSTOM_RDS",
        "source_type": "CONTAINER",
        "target_type": "AWSRDSDBINSTANCE",
        "source_pattern": r"(aquila)",
        "target_pattern": r"sre-polaris-primary-aws-\w+-\w+-\d+-(aquila)-postgresql",
        "match_field": "name",
        # "custom_match": custom_match
    },
]



def load_json(file_path: str) -> Dict:
    with open(file_path, 'r') as file:
        data = json.load(file)
    logger.info(f"Loaded JSON file: {file_path}")
    return data

def extract_pattern(value: str, pattern: str) -> Union[str, None]:
    match = re.match(pattern, value)
    return match.group(1) if match else None

def match_nodes(source_node: Dict, target_node: Dict, rule: Dict) -> bool:
    if (rule["source_type"] == "ANY" or source_node.get("type") == rule["source_type"]) and \
       (rule["target_type"] == "ANY" or target_node.get("type") == rule["target_type"]):
        
        source_value = source_node.get(rule['match_field'])
        target_value = target_node.get(rule['match_field'])
        
        if source_value and target_value:
            # Try pattern matching first
            source_extract = extract_pattern(source_value, rule['source_pattern'])
            target_extract = extract_pattern(target_value, rule['target_pattern'])
            
            if source_extract and target_extract:
                if source_extract.lower() == target_extract.lower():
                    logger.info(f"Pattern match: {source_value} -> {target_value}")
                    return True
            
            # If pattern matching fails, try custom matching if defined
            if 'custom_match' in rule and callable(rule['custom_match']):
                return rule['custom_match'](source_value, target_value)
    
    return False

def merge_topologies(topology_data: List[Tuple[str, Dict]]) -> Dict:
    merged_data = {"nodes": [], "links": []}
    node_map = {}  # Map to store nodes by their id
    source_nodes = defaultdict(list)  # Map to store nodes by their source

    # First pass: Collect all nodes and organize them by source
    for source, data in topology_data:
        for node in data.get("nodes", []):
            node_id = node["id"]
            if node_id not in node_map:
                merged_data["nodes"].append(node)
                node_map[node_id] = node
                source_nodes[source].append(node)
        logger.info(f"Collected {len(data.get('nodes', []))} nodes from source: {source}")

    # Second pass: Collect all existing links
    for _, data in topology_data:
        merged_data["links"].extend(data.get("links", []))
    logger.info(f"Collected {len(merged_data['links'])} existing links")

    # Third pass: Create new links based on matching rules
    new_links_count = 0
    for rule in MATCHING_RULES:
        logger.info(f"Applying matching rule: {rule['source_data']} -> {rule['target_data']}")
        for source_node in source_nodes[rule["source_data"]]:
            for target_node in source_nodes[rule["target_data"]]:
                if match_nodes(source_node, target_node, rule):
                    merged_data["links"].append({
                        "id": f"{source_node['id']}->{target_node['id']}",
                        "source": source_node["id"],
                        "target": target_node["id"],
                        "value": 1,
                        "link_type": "contains",
                        "properties": {},
                        "data_source": "rule_match",
                    })
                    new_links_count += 1
                    logger.info(f"New link created: {source_node['name']} -> {target_node['name']}")
    
    logger.info(f"Created {new_links_count} new links based on matching rules")
    logger.info(f"Final topology: {len(merged_data['nodes'])} nodes, {len(merged_data['links'])} links")

    return merged_data

def process_topology_data(input_data: Union[List[Tuple[str, str]], List[Tuple[str, Dict]]]) -> Dict:
    topology_data = []
    for item in input_data:
        if isinstance(item[1], str):
            # Input is a tuple of (source, file path)
            topology_data.append((item[0], load_json(item[1])))
        elif isinstance(item[1], dict):
            # Input is a tuple of (source, dictionary)
            topology_data.append(item)
            logger.info(f"Loaded data from dictionary for source: {item[0]}")
        else:
            error_msg = "Input must be either (source, file path) or (source, dictionary) tuples"
            logger.error(error_msg)
            raise ValueError(error_msg)

    return merge_topologies(topology_data)

# Generate Topology for cluster from different sources and merge them
def generate_topology_for_cluster(cluster_id: str, filters: Dict, sources: Dict) -> List[Tuple[str, Dict]]:

    topology_data = []

    for source, entity_info in sources.items():
        generator = TopologyGenerator(
            entity_id=entity_info["entity_id"],
            depth=entity_info["depth"],
            filters=filters
        )
        result = generator.generate()
        topology_data.append((source, result))

    return topology_data

# Additional source of topology data
def generate_topology_by_type(node_type: dict):

    logger.info(f"Generating topology for node type: {node_type}")

    domain = node_type["domain"]
    infrastructure_integration_type = node_type["infrastructure_integration_type"]
    icon = node_type["icon"]

    # get the entities from newrelic by entity type
    entities = get_entities_by_type(
        domain=domain,
        infrastructure_integration_type=infrastructure_integration_type
    )

    # create nodes from the entities
    nodes = []
    for entity in entities:
        nodes.append({
            "id": entity["guid"],
            "name": entity["name"],
            "type": entity["type"],
            "domain": entity["domain"],
            "entityType": entity["entityType"],
            "issue": False,
            "group": 1,
            "icon": icons[icon],
            "data_source": "newrelic",
            "alt_names": [],
            "entity_id": entity["guid"],
            "properties": {
                "type": entity["type"],
                "domain": entity["domain"],
                "entityType": entity["entityType"],
                **{tag["key"]: tag["values"] for tag in entity["tags"]}
            }
        })

    # seperate topology by cluster for MDM
    mdm_clusters = {'na1', 'na2', 'ap1', 'ap2'}
    
    # use LLM to determine the cluster for each node
    system_message = """
    You are an AI assistant tasked with categorizing nodes based on their names and tags into specific cluster IDs and products. Follow these instructions carefully:

    1. Categorization Rules:
    a. Cluster IDs: Categorize nodes into one of these cluster IDs: na1, na2, ap1, ap2
    b. Products: Identify the product from these options: polaris, aquila, mig, kong
    c. Priority: Always prioritize the information in the name first, then consider the tags

    2. Cluster ID Identification:
    - Look for these patterns in the name or tags (examples are not exhaustive):
        * na1: "na1", "mig-primary-na1", "na1-mig", "na1migrds"
        * na2: "na2", "mig-primary-na2", "na2-mig", "na2migrds"
        * ap1: "ap1", "mig-primary-ap1", "ap1-mig", "ap1migrds"
        * ap2: "ap2", "mig-primary-ap2", "ap2-mig", "ap2migrds"

    3. Product Identification:
    - Look for these keywords in the name or tags:
        * polaris: "polaris", "polarisrpt"
        * aquila: "aquila"
        * mig: "mig", "migrds"
        * kong: "kong"

    4. Examples for reference:
    - "aquila-na1" -> cluster: na1, product: aquila
    - "na2migrdspolaris" -> cluster: na2, product: polaris
    - "mig-primary-ap1-eks-rds" -> cluster: ap1, product: mig
    - "ap1migrdspolaris" -> cluster: ap1, product: polaris
    - "sre-polaris-primary-aws-ap2-ap-southeast-2-aquila-postgresql" -> cluster: ap2, product: aquila
    - "polarisrpt-ap1" -> cluster: ap1, product: polaris
    - "kong-na1-test" -> cluster: na1, product: kong
    - "mig-secondary-na2-database" -> cluster: na2, product: mig

    5. Output Format:
    Return the result in JSON format with the following structure:
    {
        "na1": [{"name": "node_name", "id": "node_id", "product": "product_name"}, ...],
        "na2": [...],
        "ap1": [...],
        "ap2": [...],
        "other": [{"name": "node_name", "id": "node_id", "product": "unknown"}]
    }

    6. Special Instructions:
    - Process all nodes provided in the input.
    - If a node doesn't clearly fit into any cluster ID, place it in the "other" category.
    - If the product can't be determined, use "unknown" as the product value.
    - In case of conflicts (e.g., multiple products or clusters in the name), prioritize in this order: mig -> aquila -> kong -> polaris.

    Please categorize the provided nodes according to these instructions and return the results in the specified JSON format.
    """
    logger.info(f"Classifying nodes for: {domain} - {infrastructure_integration_type}")

    # Paginate the nodes
    node_chunks = chunk_nodes(nodes, chunk_size=20)
    cluster_results = []

    for chunk in node_chunks:
        chunk_result = light_gpt3_wrapper_autogen(json.dumps(chunk), return_json=True, system_message=system_message)
        cluster_results.append(chunk_result)

    # Merge the results
    cluster_nodes = merge_cluster_results(cluster_results)
    logger.info(f"Cluster nodes: {cluster_nodes}")

     # Reconstruct the nodes with the cluster information
    topology_data = {
        "na1": {"nodes": [], "links": []},
        "na2": {"nodes": [], "links": []},
        "ap1": {"nodes": [], "links": []},
        "ap2": {"nodes": [], "links": []},
        "other": {"nodes": [], "links": []}
    }

    for cluster, node_list in cluster_nodes.items():
        for node_info in node_list:
            node_id = node_info['id']
            original_node = next((n for n in nodes if n['id'] == node_id), None)
            if original_node:
                topology_data[cluster]['nodes'].append(original_node)

    return topology_data

def chunk_nodes(nodes, chunk_size=20):
    """Split nodes into chunks of specified size."""
    return [nodes[i:i + chunk_size] for i in range(0, len(nodes), chunk_size)]

def merge_cluster_results(results):
    """Merge multiple cluster results into a single result."""
    merged = {"na1": [], "na2": [], "ap1": [], "ap2": [], "other": []}
    for result in results:
        for cluster, nodes in result.items():
            merged[cluster].extend(nodes)
    return merged

def get_entities_by_type(domain: str, infrastructure_integration_type: str) -> List[Dict]:

    logger.info(f"Getting entities by type: {domain} - {infrastructure_integration_type}")
    
    api_key = os.getenv("NEWRELIC_API_KEY")
    url = "https://api.newrelic.com/graphql"
    
    query = f"""
    {{
      actor {{
        entitySearch(
          queryBuilder: {{domain: {domain}, infrastructureIntegrationType: {infrastructure_integration_type}}}
        ) {{
          results {{
            entities {{
              guid
              name
              type
              domain
              entityType
              tags {{
                key
                values
              }}
            }}
          }}
        }}
      }}
    }}
    """

    headers = {
        "Content-Type": "application/json",
        "API-Key": api_key,
    }

    try:
        response = requests.post(url, json={"query": query}, headers=headers)
        response.raise_for_status()  # Raises an HTTPError for bad responses
        
        # get the entities from the response
        entities = response.json()["data"]["actor"]["entitySearch"]["results"]["entities"]
        logger.info(f"Found {len(entities)} entities")
        if not entities:
            logger.warning("No entities found in the response.")
        return entities

    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed: {e}")
    except json.JSONDecodeError:
        logger.error("Failed to parse the response as JSON")
    except ValueError as e:
        logger.error(f"Error: {e}")
    except KeyError as e:
        logger.error(f"Key error while accessing entities: {e}")
            
    return []


# Example of how to use this class
if __name__ == "__main__":
    filters = {
        "types": ["KUBERNETES_JOB", "KUBERNETES_PERSISTENTVOLUME", "KUBERNETES_PERSISTENTVOLUMECLAIM", "KUBERNETES_CRONJOB"],  # Example: exclude entities of types "APPLICATION" and "SERVICE"
        "ids": ["MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "MTA5MzYyMHxJTkZSQXxOQXw0MjY5NTY2NDczOTc0NTUwMTcx", "MTA5MzYyMHxJTkZSQXxOQXw1ODQ4OTM1MDE3ODE5NDg2MTg3", "MTA5MzYyMHxJTkZSQXxOQXwzMDY0MDYzODIwODkxNDE3OTY"],  # Example: exclude specific IDs
        "name_patterns": [],  # Example: exclude entities with these name patterns
        "tags": {"k8s.namespaceName": ["kube-system", "newrelic-metrics", "newrelic-logging", "newrelic", "nr-custom-metrics"]}  # Example: exclude entities with specific tags
    }

    # all mdm kubernetes cluster
    sources = {
        "na1": {
            "K8S": {"entity_id": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "depth": 1},
            "APM": {"entity_id": "MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAyNTczMjUxNQ", "depth": 2}
        },
        "na2": {
            "K8S": {"entity_id": "MTA5MzYyMHxJTkZSQXxOQXw0MjY5NTY2NDczOTc0NTUwMTcx", "depth": 1},
            "APM": {"entity_id": "MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTA0Mzk4MTM1Mw", "depth": 2}
        },
        "ap1": {
            "K8S": {"entity_id": "MTA5MzYyMHxJTkZSQXxOQXw1ODQ4OTM1MDE3ODE5NDg2MTg3", "depth": 1},
            "APM": {"entity_id": "MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAxNjU5MzQ0NQ", "depth": 2}
        },
        "ap2": {
            "K8S": {"entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzMDY0MDYzODIwODkxNDE3OTY", "depth": 1},
            "APM": {"entity_id": "MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAwODIxODUwMA", "depth": 2}
        }
    }

    # generate topology by type for custom nodes
    node_type = {
            "domain": "INFRA",
            "infrastructure_integration_type": "AWS_RDS_DB_INSTANCE",
            "icon": "database"
    }
    custom_nodes_topology = generate_topology_by_type(node_type)

    # temp
    # sources = {}

    client = CosmosClient(os.getenv("COSMOS_ENDPOINT"), os.getenv("COSMOS_KEY"))
    database_name = "service-map"

    for cluster_id, entity_info in sources.items():
        topology_data = generate_topology_for_cluster(cluster_id=cluster_id, filters=filters, sources=entity_info)
        # print(json.dumps(topology_data, indent=2))

        # append custom nodes by type
        custom_nodes = custom_nodes_topology[cluster_id]
        topology_data.append(("CUSTOM_RDS", custom_nodes))

        merged_topology = process_topology_data(topology_data)
        # print(json.dumps(merged_topology, indent=2))

        # store it in file for reference
        filename = f"merged_topology_{cluster_id}.json"
        with open(filename, "w") as outfile:
            json.dump(merged_topology, outfile, indent=2)
            logger.info(f"Merged topology saved to {filename}")

        # store the merged topology to cosmos db
        container_name = cluster_id
        database = client.get_database_client(database_name)
        container = database.get_container_client(container_name)

        # add additional properties to the merged topology
        merged_topology["id"] = cluster_id
        merged_topology["node"] = cluster_id
        
        container.upsert_item(merged_topology)

        # sleep for 2 seconds
        time.sleep(2)

    # # generate topology by type
    # node_types = [
    #     {"domain": "INFRA", "infrastructure_integration_type": "AWS_RDS_DB_INSTANCE", "icon": "database"},
    # ]

    # for node_type in node_types:
    #     generate_topology_by_type(node_type)



    # # this is for NA1 MDM kubernetes cluster
    # generator = TopologyGenerator(
    #     entity_id="MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", 
    #     depth=1, 
    #     filters=filters
    # )
    # result_k8s = generator.generate()
    # # print(json.dumps(result, indent=2))

    # # this is for NA1 polaris APM
    # generator = TopologyGenerator(
    #     entity_id="MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAyNTczMjUxNQ", 
    #     depth=2, 
    #     filters=filters
    # )
    # result_apm = generator.generate()
    # # print(json.dumps(result, indent=2))

    # # Store the results in a single file
    # filename = "topology-sync-fn-v1.json"
    # with open(filename, "w") as outfile:
    #     json.dump({"k8s": result_k8s, "apm": result_apm}, outfile, indent=2)
    #     logger.info(f"Topology data saved to {filename}")

    # Example with file paths
    # input_data = [
    #     ("K8S", "kubernetes_topology.json"),
    #     ("APM", "apm_topology.json"),
    #     ("STATIC", "static_topology.json")
    # ]
    # merged_topology = process_topology_data(input_data)

    # Example with direct dictionary input (commented out)
    # input_data = [
    #     ("K8S", {"nodes": [...], "links": [...]}),
    #     ("APM", {"nodes": [...], "links": [...]}),
    #     ("STATIC", {"nodes": [...], "links": [...]})
    # ]
    # merged_topology = process_topology_data(input_data)

    # input_data = [
    #     ("K8S", result_k8s),
    #     ("APM", result_apm)
    # ]

    # temp
    # merged_topology = load_json("topology-sync-fn-v1.json")
    # k8s_topology = merged_topology["k8s"]
    # apm_topology = merged_topology["apm"]
    # input_data = [
    #     ("K8S", k8s_topology),
    #     ("APM", apm_topology)
    # ]

    # merged_topology = process_topology_data(input_data)

    # Save the merged topology to a new JSON file
    # filename = "merged_topology.json"
    # with open(filename, "w") as outfile:
    #     json.dump(merged_topology, outfile, indent=2)

    #     logger.info(f"Merged topology saved to {filename}")

    
