# Version 2 working *
import json
import requests
import os
from dotenv import load_dotenv

load_dotenv(override=True)

def generate_topology(entity_id, rca="RCA solution", issue_info="Issue Details", depth=3):

    if not entity_id:
        raise ValueError("issue id cannot be `None` or empty")

    def fetch_entity_relationships(entity_id):
        endpoint = "https://api.newrelic.com/graphql"
        account_id = os.environ["NEWRELIC_ACCOUNT_ID"]
        api_key = os.environ["NEWRELIC_API_KEY"]

        headers = {"Content-Type": "application/json", "API-Key": api_key}

        query = {
            "query": """
            {
            actor {
                entity(guid: "%s") {
                name
                relatedEntities {
                    results {
                    source {
                        entity {
                        guid
                        name
                        type
                        }
                    }
                    target {
                        entity {
                        guid
                        name
                        type
                        }
                    }
                    type
                    }
                }
                }
            }
            }
            """
            % entity_id
        }

        response = requests.post(endpoint, headers=headers, json=query)

        if response.status_code == 200:
            parsed_response = json.loads(response.text)
            if (
                parsed_response.get("data")
                and parsed_response["data"].get("actor")
                and parsed_response["data"]["actor"].get("entity")
                and parsed_response["data"]["actor"]["entity"].get("relatedEntities")
            ):
                entity_relationships = {}
                for relationship in parsed_response["data"]["actor"]["entity"]["relatedEntities"].get("results", []):
                    source_id = relationship.get("source", {}).get("entity", {}).get("guid")
                    if source_id:
                        if source_id not in entity_relationships:
                            entity_relationships[source_id] = []
                        entity_relationships[source_id].append(relationship)
                return entity_relationships

        return {}

    entity_relationships = fetch_entity_relationships(entity_id)

    icons = {
        "default": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png",
        "redis": "https://redis.com/wp-content/themes/wpx/assets/images/logo-redis.svg",
        "database": "https://upload.wikimedia.org/wikipedia/commons/c/c5/201603_database.png",
    }

    topology = {
        "nodes": [],
        "links": [],
    }

    processed_entities = set()
    entities_to_process = set([(entity_id, 0)])
    # add all the entities to process from the entity_relationships
    # entities_to_process = set()
    # for entity in entity_relationships.keys():
    #     entities_to_process.add((entity, 0))

    print("entities_to_process", entities_to_process)

    def process_entity_relationships(entity_id, current_depth):
        if current_depth > depth or entity_id in processed_entities:
            print("Skipping entity:", entity_id)
            return

        processed_entities.add(entity_id)

        if entity_id not in entity_relationships:
            # Fetch relationships for the target entity
            target_entity_relationships = fetch_entity_relationships(entity_id)
            entity_relationships[entity_id] = target_entity_relationships.get(entity_id, [])
            entities_to_process.update([(e, current_depth + 1) for e in target_entity_relationships.keys()])

        for relationship in entity_relationships[entity_id]:
            source = relationship.get("source", {}).get("entity")
            target = relationship.get("target", {}).get("entity")

            if source and target:
                add_node(source)
                add_node(target)
                add_link(source["guid"], target["guid"])

                print(f"Added link: {source['name']} -> {target['name']}")

                # skip if target type is SERVICE
                # if target["type"] == "SERVICE":
                #     continue
                entities_to_process.add((target["guid"], current_depth + 1))

    def add_node(entity):
        if entity and not any(node["id"] == entity["guid"] for node in topology["nodes"]):
            topology["nodes"].append(
                {
                    "id": entity["guid"],
                    "name": entity["name"],
                    "group": 1,
                    "icon": determine_icon(entity["name"]),
                    "additionalInfo": "Entity in the network",
                    "issue": entity["guid"] == entity_id,
                }
            )

    def add_link(source_id, target_id):
        if not any(
            link["source"] == source_id and link["target"] == target_id for link in topology["links"]
        ):
            topology["links"].append(
                {
                    "source": source_id,
                    "target": target_id,
                    "value": 1,
                }
            )

    def determine_icon(name):
        if name is None:
            return icons["default"]
        if "redis" in name.lower():
            return icons["redis"]
        elif "rds" in name.lower() or "database" in name.lower():
            return icons["database"]
        else:
            return icons["default"]

    while entities_to_process:
        entity_id, current_depth = entities_to_process.pop()
        process_entity_relationships(entity_id, current_depth)

    data = {"nodes": topology["nodes"], "links": topology["links"], "rca": rca, "issue_info": issue_info}

    # with open("topology.json", "w") as outfile:
    #     json.dump(data, outfile)

    # print("Generated topology data:", json.dumps(data))
    # response = requests.post("http://localhost:8005/data", json=data)

    # if response.status_code == 200:
    #     print(response.text)
    #     return response.text
    # else:
    #     print(response.status_code)
    #     return "Error in generating topology diagram\n" + response.text


generate_topology("MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAyNDk5MTQyMg", rca="<html>\n<head>\n    <title>Root Cause Analysis</title>\n    <style>\n        body {\n            font-family: Arial, sans-serif;\n        }\n        .issue {\n            margin: 10px 0;\n            padding: 10px;\n            border-left: 5px solid #f44336;\n            background-color: #fdd;\n        }\n        .issue-title {\n            font-weight: bold;\n        }\n    </style>\n</head>\n<body>\n    <h1>Root Cause Analysis</h1>\n    <div id=\"issues\">\n        <div class=\"issue\"><span class=\"issue-title\">Resource Constraints:</span> The application might be experiencing resource constraints, such as CPU or memory limits.</div>\n<div class=\"issue\"><span class=\"issue-title\">Inefficient Application Code:</span> There could be inefficient algorithms or unoptimized code within the application.</div>\n<div class=\"issue\"><span class=\"issue-title\">Database Performance:</span> The application's throughput could be affected by slow database queries or database performance issues.</div>\n<div class=\"issue\"><span class=\"issue-title\">Network Latency:</span> Network issues within the Kubernetes cluster or between the application and its dependencies could be causing increased latency.</div>\n<div class=\"issue\"><span class=\"issue-title\">Concurrency Issues:</span> The application might be facing thread contention or deadlock issues.</div>\n<div class=\"issue\"><span class=\"issue-title\">Dependency Failures:</span> External services or dependencies that the application relies on could be failing.</div>\n<div class=\"issue\"><span class=\"issue-title\">Configuration Errors:</span> Misconfiguration in the application or the Kubernetes deployment settings could lead to suboptimal performance.</div>\n<div class=\"issue\"><span class=\"issue-title\">Scaling Policies:</span> The Kubernetes autoscaling policies might not be responding quickly enough to increased load.</div>\n\n    </div>\n</body>\n</html>", issue_info="""<table> <tr> <td>"title":"Metric query deviated from the baseline for at least 5 minutes on 'High Application Response Time'"</td> </tr> <tr> <td>"priority":"CRITICAL"</td> </tr> <tr> <td>"conditionName":"High Application Response Time"</td> </tr> <tr> <td>"nrqlQuery":"SELECT average(newrelic.goldenmetrics.apm.application.responseTimeMs) FROM Metric FACET entity.guid, appName"</td> </tr> <tr> <td>"violationClosedCause":"EVALUATOR"</td> </tr> <tr> <td>"description":"Policy: 'Golden Signals'. Condition: 'High Application Response Time'"</td> </tr> <tr> <td>"state":"CLOSED"</td> </tr> </table>""")