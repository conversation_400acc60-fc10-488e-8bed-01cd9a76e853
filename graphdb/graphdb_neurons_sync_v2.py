import asyncio
import json
import os
import ssl
from typing import Dict, List, Optional, Union
from enum import Enum
from datetime import datetime, timezone
import uuid
import logging
from gremlin_python.driver import client, serializer
from gremlin_python.driver.protocol import GremlinServerError
from gremlin_python.driver.aiohttp.transport import <PERSON><PERSON>ttpT<PERSON>sport
from gremlin_python.driver.client import Client
from gremlin_python.driver.resultset import ResultSet
import dotenv

dotenv.load_dotenv(override=True)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataSource(Enum):
    STATIC = "static"
    NEW_RELIC = "new_relic"

class NodeType(Enum):
    KUBERNETES_CLUSTER = "KUBERNETESCLUSTER"
    KUBERNETES_DEPLOYMENT = "KUBERNETES_DEPLOYMENT"
    KUBERNETES_POD = "KUBERNETES_POD"
    NAMESPACE = "namespace"
    CONTAINER = "CONTAINER"
    DATABASE = "database"
    CACHE = "cache"
    LOAD_BALANCER = "load_balancer"
    API_SERVER = "api_server"
    STORAGE = "storage"
    KAFKA = "kafka"
    SEARCH = "search"
    APPLICATION = "APPLICATION"

class Node:
    def __init__(self, id: str, node_type: NodeType, name: str, properties: Dict[str, any] = None):
        self.id = id
        self.node_type = node_type
        self.name = name
        self.properties = properties or {}

    def to_dict(self):
        return {
            "id": self.id,
            "node_type": self.node_type.value,
            "name": self.name,
            "properties": self.properties
        }

class Link:
    def __init__(self, source_id: str, target_id: str, link_type: str):
        self.source_id = source_id
        self.target_id = target_id
        self.link_type = link_type

    def to_dict(self):
        return {
            "source": self.source_id,
            "target": self.target_id,
            "link_type": self.link_type
        }

class CosmosDBGraphManager:
    def __init__(self, config):
        self.config = config
        self.client = None

    async def connect(self):
        try:
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            self.client = Client(
                f'{self.config["endpoint"]}',
                'g',
                username=self.config['username'],
                password=self.config['password'],
                # message_serializer=serializer.GraphSONSerializersV2d0(),
                transport_factory=lambda: AiohttpTransport(
                    call_from_event_loop=True,
                    ssl=ssl_context
                )
            )
            logger.info("Successfully created Gremlin client")
        except Exception as e:
            logger.error(f"Failed to create Gremlin client: {e}")
            raise

    async def close(self):
        if self.client:
            await self.client.close()
            logger.info("Closed Gremlin client")

    async def execute_query(self, query):
        if not self.client:
            raise ValueError("Gremlin client is not initialized. Call connect() first.")

        logger.info(f"Executing Gremlin query: {query}")
        try:
            result = await self.client.submitAsync(query)
            return result
        except GremlinServerError as e:
            logger.error(f'Gremlin Server Error: {e}')
            raise
        except Exception as e:
            logger.error(f'Unexpected error during query execution: {e}')
            raise

    async def create_vertex(self, node: Node):
        properties = node.to_dict()
        query = f"g.addV('{node.node_type.value}')"
        for key, value in properties.items():
            if isinstance(value, (str, int, bool)):
                query += f".property('{key}', '{value}')"
            else:
                query += f".property('{key}', '{json.dumps(value)}')"
        return await self.execute_query(query)

    async def create_edge(self, link: Link):
        query = f"g.V('{link.source_id}').addE('{link.link_type}').to(g.V('{link.target_id}'))"
        return await self.execute_query(query)

    async def get_topology(self, start_node_id: str, max_depth: int = 3):
        query = f"""
        g.V('{start_node_id}').repeat(bothE().otherV().simplePath()).times({max_depth}).path().
          by(valueMap().with(WithOptions.tokens)).by(label)
        """
        result = await self.execute_query(query)
        return self._process_topology_result(result)

    def _process_topology_result(self, result):
        nodes = {}
        links = []
        for path in result:
            for i in range(0, len(path.objects), 2):
                node = path.objects[i]
                node_id = node.get('id', [None])[0]
                if node_id and node_id not in nodes:
                    nodes[node_id] = {
                        "id": node_id,
                        "name": node.get('name', [None])[0],
                        "type": node.get('node_type', [None])[0],
                        "entity_id": node.get('entity_id', [None])[0],
                        "properties": {k: v[0] if isinstance(v, list) and len(v) == 1 else v 
                                       for k, v in node.items() if k not in ['id', 'name', 'node_type']}
                    }
                if i + 1 < len(path.objects):
                    link_type = path.objects[i + 1]
                    source = path.objects[i].get('id', [None])[0]
                    target = path.objects[i + 2].get('id', [None])[0]
                    links.append({"source": source, "target": target, "type": link_type})
        
        return {"nodes": list(nodes.values()), "links": links}

async def create_graph_from_topology(topology_data: Dict, graph_manager: CosmosDBGraphManager):
    for node_data in topology_data.get('nodes', []):
        node = Node(
            id=node_data['id'],
            node_type=NodeType(node_data['type']),
            name=node_data['name'],
            properties=node_data.get('properties', {})
        )
        await graph_manager.create_vertex(node)

    for link_data in topology_data.get('links', []):
        link = Link(
            source_id=link_data['source'],
            target_id=link_data['target'],
            link_type=link_data['link_type']
        )
        await graph_manager.create_edge(link)

async def main():
    try:
        # Load the JSON data
        with open('merged_topology_nvu.json', 'r') as f:
            topology_data = json.load(f)

        endpoint = os.environ.get('COSMOS_GREMLIN_ENDPOINT')
        database = "neurons"
        collection = "nvu"
        primary_key = os.environ.get('GRAPHDB_PRIMARY_KEY')

        config = {
            'endpoint': endpoint,
            'username': f'/dbs/{database}/colls/{collection}',
            'password': primary_key,
        }

        # Initialize and connect the CosmosDBGraphManager
        graph_manager = CosmosDBGraphManager(config)
        await graph_manager.connect()

        try:
            # Create the graph from the topology data
            await create_graph_from_topology(topology_data[0][1], graph_manager)
            logger.info("Graph created successfully in Azure Cosmos DB")

            # Get and display the topology for a sample node
            sample_node_id = topology_data['nodes'][0]['id']  # Use the first node as an example
            retrieved_topology = await graph_manager.get_topology(sample_node_id)
            logger.info(f"Retrieved topology for node {sample_node_id}:")
            logger.info(json.dumps(retrieved_topology, indent=2))

        finally:
            # Close the connection
            await graph_manager.close()

    except Exception as e:
        logger.error(f"An error occurred: {e}")
        logger.exception("Exception details:")

if __name__ == "__main__":
    asyncio.run(main())