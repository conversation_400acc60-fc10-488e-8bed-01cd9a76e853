import asyncio
import json
import os
import ssl
from typing import Dict, List, Optional, Union
from enum import Enum
from datetime import datetime, timezone
import uuid
from gremlin_python.driver import client, serializer
from gremlin_python.driver.protocol import GremlinServerError
from gremlin_python.driver.aiohttp.transport import AiohttpTransport
import logging
from gremlin_python.driver.client import Client
from gremlin_python.driver.resultset import ResultSet

logging.basicConfig(level=logging.INFO)

class DataSource(Enum):
    STATIC = "static"
    NEW_RELIC = "new_relic"

class ComponentType(Enum):
    KUBERNETES_CLUSTER = "KUBERNETESCLUSTER"
    KUBERNETES_DEPLOYMENT = "KUBERNETES_DEPLOYMENT"
    KUBERNETES_POD = "KUBERNETES_POD"
    NAMESPACE = "namespace"
    CONTAINER = "CONTAINER"
    DATABASE = "database"
    CACHE = "cache"
    LOAD_BALANCER = "load_balancer"
    API_SERVER = "api_server"
    STORAGE = "storage"
    KAFKA = "kafka"
    SEARCH = "search"
    APPLICATION = "APPLICATION"

class ClusterSpecificProperties:
    def __init__(self, name: str, entity_id: str, group: int):
        self.name = name
        self.entity_id = entity_id
        self.group = group

    def to_dict(self):
        return {
            "name": self.name,
            "entity_id": self.entity_id,
            "group": self.group
        }

    @classmethod
    def from_dict(cls, data):
        return cls(
            name=data["name"],
            entity_id=data["entity_id"],
            group=data["group"]
        )

class Component:
    def __init__(
        self,
        id: str,
        component_type: ComponentType,
        namespace: Optional[str] = None,
        properties: Dict[str, any] = None,
        alt_names: List[str] = None,
        icon: str = "",
        cluster_properties: Dict[str, ClusterSpecificProperties] = None
    ):
        self.id = id
        self.component_type = component_type
        self.namespace = namespace
        self.properties = properties or {}
        self.alt_names = alt_names or []
        self.icon = icon
        self.cluster_properties = cluster_properties or {}
        self.data_source = DataSource.STATIC
        self.created_at = datetime.now(timezone.utc)
        self.updated_at = self.created_at

    def to_dict(self):
        return {
            "id": self.id,
            "component_type": self.component_type.value,
            "namespace": self.namespace,
            "properties": self.properties,
            "alt_names": self.alt_names,
            "icon": self.icon,
            "cluster_properties": {k: v.to_dict() for k, v in self.cluster_properties.items()},
            "data_source": self.data_source.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            component_type=ComponentType(data["component_type"]),
            namespace=data.get("namespace"),
            properties=data.get("properties", {}),
            alt_names=data.get("alt_names", []),
            icon=data.get("icon", ""),
            cluster_properties={k: ClusterSpecificProperties.from_dict(v) for k, v in data.get("cluster_properties", {}).items()}
        )

    def to_frontend_dict(self, cluster_name: str):
        cluster_props = self.cluster_properties.get(cluster_name)
        if not cluster_props:
            return None
        return {
            "id": self.id,
            "name": cluster_props.name,
            "group": cluster_props.group,
            "icon": self.icon,
            "additionalInfo": f"Alert Severity: {self.properties.get('alert_severity', 'N/A')}\nDomain: {self.properties.get('domain', 'N/A')}\nType: {self.component_type.value}",
            "issue": False
        }

class Connection:
    def __init__(
        self,
        id: str,
        source_id: str,
        target_id: str,
        connection_type: str,
        properties: Dict[str, any] = None
    ):
        self.id = id
        self.source_id = source_id
        self.target_id = target_id
        self.connection_type = connection_type
        self.properties = properties or {}
        self.data_source = DataSource.STATIC
        self.created_at = datetime.now(timezone.utc)
        self.updated_at = self.created_at

    def to_dict(self):
        return {
            "id": self.id,
            "source_id": self.source_id,
            "target_id": self.target_id,
            "connection_type": self.connection_type,
            "properties": self.properties,
            "data_source": self.data_source.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            source_id=data["source_id"],
            target_id=data["target_id"],
            connection_type=data["connection_type"],
            properties=data.get("properties", {})
        )

    def to_frontend_dict(self):
        return {
            "source": self.source_id,
            "target": self.target_id,
            "value": 1
        }

class ServiceArchitecture:
    def __init__(self):
        self.components: Dict[str, Component] = {}
        self.connections: List[Connection] = []

    def add_component(self, component: Component):
        self.components[component.id] = component
        return component.id

    def add_connection(self, connection: Connection):
        self.connections.append(connection)

    def to_dict(self):
        return {
            "components": [component.to_dict() for component in self.components.values()],
            "connections": [connection.to_dict() for connection in self.connections]
        }

    @classmethod
    def from_dict(cls, data):
        architecture = cls()
        for component_data in data["components"]:
            component = Component.from_dict(component_data)
            architecture.add_component(component)
        for connection_data in data["connections"]:
            connection = Connection.from_dict(connection_data)
            architecture.add_connection(connection)
        return architecture

    def to_json(self):
        return json.dumps(self.to_dict(), indent=2)

    @classmethod
    def from_json(cls, json_str):
        data = json.loads(json_str)
        return cls.from_dict(data)

    def to_frontend_dict(self, cluster_name: str, start_component_id: Optional[str] = None):
        nodes = []
        links = []
        for component in self.components.values():
            node_dict = component.to_frontend_dict(cluster_name)
            if node_dict:
                if component.id == start_component_id:
                    node_dict["issue"] = True
                nodes.append(node_dict)
        for connection in self.connections:
            links.append(connection.to_frontend_dict())
        return {"nodes": nodes, "links": links}
    
    def find_component(self, search_term: str, cluster_name: str) -> Optional[Component]:
        for component in self.components.values():
            cluster_props = component.cluster_properties.get(cluster_name)
            if cluster_props:
                if search_term == cluster_props.entity_id or search_term.lower() in cluster_props.name.lower():
                    return component
        return None

class CosmosDBGraphManager:
    def __init__(self, config):
        self.config = config
        self.client = None

    async def connect(self):
        try:
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            self.client = Client(
                f'wss://{self.config["endpoint"]}:443/',
                'g',
                username=self.config['username'],
                password=self.config['password'],
                message_serializer=serializer.GraphSONSerializersV2d0(),
                transport_factory=lambda: AiohttpTransport(
                    call_from_event_loop=True,
                    ssl=ssl_context
                )
            )
            logging.info("Successfully created Gremlin client")
        except Exception as e:
            logging.error(f"Failed to create Gremlin client: {e}")
            raise

    async def close(self):
        if self.client:
            try:
                await asyncio.get_event_loop().run_in_executor(None, self.client.close)
                logging.info("Successfully closed Gremlin client")
            except Exception as e:
                logging.error(f"Error closing Gremlin client: {e}")
        else:
            logging.warning("Attempted to close non-existent Gremlin client")

    async def execute_query(self, query):
        if not self.client:
            raise ValueError("Gremlin client is not initialized. Call connect() first.")
        
        logging.info(f"Executing Gremlin query: {query}")
        try:
            future = self.client.submitAsync(query)
            result_set = await asyncio.wrap_future(future)
            return list(result_set)  # Convert ResultSet to a list
        except GremlinServerError as e:
            logging.error(f'Gremlin Server Error: {e}')
            raise
        except Exception as e:
            logging.error(f'Unexpected error during query execution: {e}')
            raise
    
    def ensure_partition_key(self, properties):
        if 'node' not in properties:
            # generate if one is not provided
            properties['node'] = properties.get('node', uuid.uuid4().hex)
        return properties

    async def create_vertex(self, label: str, properties: Dict[str, any]):
        query = f"g.addV('{label}')"
        for key, value in properties.items():
            if isinstance(value, (str, int, bool)):
                query += f".property('{key}', {json.dumps(value)})"
            else:
                query += f".property('{key}', {json.dumps(json.dumps(value))})"
        return await self.execute_query(query)

    async def create_edge(self, label: str, from_id: str, to_id: str, properties: Dict[str, any]):
        query = f"g.V('{from_id}').addE('{label}').to(g.V('{to_id}')"
        for key, value in properties.items():
            if isinstance(value, (str, int, bool)):
                query += f".property('{key}', {json.dumps(value)})"
            else:
                query += f".property('{key}', {json.dumps(json.dumps(value))})"
        query += ")"
        return await self.execute_query(query)

    async def get_topology(self, cluster_name: str, start_component_id: str, max_depth: int = 3):
        query = f"""
        g.V('{start_component_id}').repeat(bothE().otherV().simplePath()).times({max_depth}).path().
          by(project('id', 'name', 'group', 'icon', 'additionalInfo', 'issue').
             by(id).
             by(coalesce(select('cluster_properties').select('{cluster_name}').select('name'), constant(''))).
             by(coalesce(select('cluster_properties').select('{cluster_name}').select('group'), constant(0))).
             by('icon').
             by(concat(
                 'Alert Severity: ', coalesce(select('properties').select('alert_severity'), constant('N/A')),
                 '\\nDomain: ', coalesce(select('properties').select('domain'), constant('N/A')),
                 '\\nType: ', select('component_type')
             )).
             by(constant(false))).
          by(project('source', 'target', 'value').
             by(outV().id()).by(inV().id()).by(constant(1)))
        """
        result = await self.execute_query(query)
        nodes = set()
        links = set()
        for path in result:
            for i, element in enumerate(path.objects):
                if i % 2 == 0:  # It's a vertex
                    node = element
                    node['issue'] = node['id'] == start_component_id
                    nodes.add(json.dumps(node))
                else:  # It's an edge
                    links.add(json.dumps(element))
        return {
            "nodes": [json.loads(node) for node in nodes],
            "links": [json.loads(link) for link in links]
        }

    async def find_component(self, search_term: str, cluster_name: str):
        # query = f"""
        # g.V().has('cluster_properties', containing('{cluster_name}')).
        # filter(
        #     values('cluster_properties').select('{cluster_name}').select('entity_id').is('{search_term}').
        #     or().
        #     values('cluster_properties').select('{cluster_name}').select('name').is(textContains('{search_term}'))
        # ).
        # limit(1)
        # """
        # query = f"""
        # g.V().has('cluster_properties', containing('{cluster_name}')).
        # where(
        #     values('cluster_properties').select('{cluster_name}').select('entity_id').is('{search_term}').
        #     or().
        #     values('cluster_properties').select('{cluster_name}').select('name').is(TextP.containing('{search_term}'))
        # ).
        # limit(1)
        # """
        query = f"""
        g.V().has('cluster_properties', '{cluster_name}').
        has('cluster_properties', '{cluster_name}', 'entity_id', '{search_term}').
        or().
        has('cluster_properties', '{cluster_name}', 'name', TextP.containing('{search_term}')).
        limit(1)
        """
        result = await self.execute_query(query)
        return next(iter(result), None)

    async def get_topology_from_search(self, search_term: str, cluster_name: str, max_depth: int = 3):
        component = await self.find_component(search_term, cluster_name)
        if not component:
            return None
        return await self.get_topology(cluster_name, component.id, max_depth)


# async def create_graph_from_architecture(architecture: ServiceArchitecture, graph_manager: CosmosDBGraphManager):
#     for component in architecture.components.values():
#         await graph_manager.create_vertex('Component', component.to_dict())
    
#     for connection in architecture.connections:
#         await graph_manager.create_edge('Connects', connection.source_id, connection.target_id, connection.to_dict())

async def create_graph_from_architecture(architecture: ServiceArchitecture, graph_manager: CosmosDBGraphManager):
    for component in architecture.components.values():
        properties = graph_manager.ensure_partition_key(component.to_dict())
        query = "g.addV('Component')"
        for key, value in properties.items():
            if isinstance(value, (str, int, bool)):
                query += f".property('{key}', {json.dumps(value)})"
            else:
                query += f".property('{key}', {json.dumps(json.dumps(value))})"
        
        await graph_manager.execute_query(query)

    for connection in architecture.connections:
        query = f"g.V('{connection.source_id}').addE('{connection.connection_type}').to(g.V('{connection.target_id}')"
        for key, value in connection.to_dict().items():
            if key not in ['id', 'source_id', 'target_id', 'connection_type']:
                if isinstance(value, (str, int, bool)):
                    query += f".property('{key}', {json.dumps(value)})"
                else:
                    query += f".property('{key}', {json.dumps(json.dumps(value))})"
        query += ")"
        
        await graph_manager.execute_query(query)

async def main():
    try:
        # Load the JSON data
        with open('na2_cluster_initial_data.json', 'r') as f:
            json_data = f.read()

        endpoint = "obv-ai-servicemap.gremlin.cosmos.azure.com"
        database = "mdm"
        collection = "na2"
        primary_key = os.environ["COSMOS_GREMLIN_KEY"]

        config = {
            'endpoint': endpoint,
            'username': f'/dbs/{database}/colls/{collection}',
            'password': primary_key,
        }

        # Create the ServiceArchitecture from JSON
        architecture = ServiceArchitecture.from_json(json_data)

        # Initialize and connect the CosmosDBGraphManager
        graph_manager = CosmosDBGraphManager(config)
        await graph_manager.connect()

        try:
            # Create the graph from the architecture
            # await create_graph_from_architecture(architecture, graph_manager)
            # logging.info("Graph created successfully in Azure Cosmos DB")

            # Search for a component and generate topology
            cluster_name = "na2"
            search_term = "polaris-champion"  # This could be an entity_id or partial name
            topology = await graph_manager.get_topology_from_search(search_term, cluster_name)

            if topology:
                logging.info(f"Retrieved topology for '{search_term}' in cluster '{cluster_name}':")
                logging.info(json.dumps(topology, indent=2))
            else:
                logging.warning(f"No component found for '{search_term}' in cluster '{cluster_name}'")

        finally:
            # Close the connection
            await graph_manager.close()

    except Exception as e:
        logging.error(f"An error occurred: {e}")

if __name__ == "__main__":
    asyncio.run(main())