{"nodes": [{"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "name": "aks-default-********-vmss0007qj", "issue": false, "group": 1, "icon": "static/icons/node.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "INFRASTRUCTURE_HOST_ENTITY", "type": "HOST", "agentName": ["Infrastructure"], "agentVersion": ["1.46.0"], "availabilityZone": ["3"], "clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "coreCount": ["4"], "fullHostname": ["aks-default-********-vmss0007qj"], "host.id": ["ce76bd5b-8f92-4bd2-a344-87faf2846364"], "hostStatus": ["running"], "hostname": ["aks-default-********-vmss0007qj"], "instanceType": ["Standard_D8s_v3"], "kernelVersion": ["5.15.0-1071-azure"], "linuxDistribution": ["Ubuntu 22.04.4 LTS"], "operatingSystem": ["linux"], "processorCount": ["8"], "regionName": ["eastus"], "subscriptionId": ["53f45c73-c7c2-4733-a772-6b3505573621"], "systemMemoryBytes": ["***********"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "type": "HOST", "entityType": "INFRASTRUCTURE_HOST_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2", "name": "rollout-update-handler-container", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["rollout-update-handler-container"], "container.state": ["Waiting"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["e65b8b8c493699aa23fd2acacfc7dbed1c438d98939ec2e609461f3dfcbd1c98"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/memapps/memappsrolloutupdatermessagehandler:1898931"], "k8s.containerName": ["rollout-update-handler-container"], "k8s.deploymentName": ["rollout-update-handler-container"], "k8s.namespaceName": ["apps-for-mem"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["rollout-update-handler-container-5bb48f95fb-kkx46"], "k8s.replicasetName": ["rollout-update-handler-container-5bb48f95fb"], "k8s.status": ["Waiting"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["5bb48f95fb"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}], "links": [{"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}]}