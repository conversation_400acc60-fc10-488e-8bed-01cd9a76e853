{"nodes": [{"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTA5MTEzOTg4MTA5MTc0NzQ0Nw", "node_type": "KUBERNETES_DEPLOYMENT", "namespace": "kong", "properties": {"alert_severity": "CRITICAL", "domain": "INFRA"}, "alt_names": [], "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "name": "kong-data-plane", "entity_id": "kong-data-plane-na2", "group": 1, "data_source": "static"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "node_type": "KUBERNETESCLUSTER", "namespace": null, "properties": {"alert_severity": "NOT_CONFIGURED", "domain": "INFRA"}, "alt_names": [], "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "name": "primary-na2-eks", "entity_id": "primary-na2-eks", "group": 4, "data_source": "static"}, {"id": "MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAyNDk5MTQyMg", "node_type": "APPLICATION", "namespace": null, "properties": {"alert_severity": "NOT_ALERTING", "domain": "APM"}, "alt_names": [], "icon": "static/App-Services.svg", "name": "nmdm-na2-r94-polaris-challenger", "entity_id": "nmdm-na2-r94-polaris-challenger", "group": 1, "data_source": "static"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNzY3MTE3MTc5MzI4MDMxMjQ3OA", "node_type": "CONTAINER", "namespace": null, "properties": {"alert_severity": "WARNING", "domain": "INFRA"}, "alt_names": [], "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/pod-256.png", "name": "polaris-challenger", "entity_id": "polaris-challenger-na2", "group": 1, "data_source": "static"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODY1MjUwMjY0MjMyMjI4MTM0NQ", "node_type": "CONTAINER", "namespace": null, "properties": {"alert_severity": "NOT_ALERTING", "domain": "INFRA"}, "alt_names": [], "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/pod-256.png", "name": "polaris-challenger-ui", "entity_id": "polaris-challenger-ui-na2", "group": 1, "data_source": "static"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNDM5NjY0MjkxOTg2NDY1OTc1NQ", "node_type": "CONTAINER", "namespace": null, "properties": {"alert_severity": "WARNING", "domain": "INFRA"}, "alt_names": [], "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/pod-256.png", "name": "polaris-champion", "entity_id": "polaris-champion-na2", "group": 1, "data_source": "static"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw1MzQyMjU4MzI1ODgwMTc3MzY3", "node_type": "KUBERNETES_POD", "namespace": null, "properties": {"alert_severity": "NOT_ALERTING", "domain": "INFRA"}, "alt_names": [], "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/pod-256.png", "name": "polaris-champion-6dc5dc6fb5-mgrjh", "entity_id": "polaris-champion-6dc5dc6fb5-mgrjh-na2", "group": 1, "data_source": "static"}], "links": [{"id": "conn1", "source_id": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "target_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTA5MTEzOTg4MTA5MTc0NzQ0Nw", "link_type": "contains", "properties": {}, "data_source": "static"}, {"id": "conn2", "source_id": "MTA5MzYyMHxJTkZSQXxOQXwtNzY3MTE3MTc5MzI4MDMxMjQ3OA", "target_id": "MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAyNDk5MTQyMg", "link_type": "runs", "properties": {}, "data_source": "static"}, {"id": "conn3", "source_id": "MTA5MzYyMHxJTkZSQXxOQXwtODY1MjUwMjY0MjMyMjI4MTM0NQ", "target_id": "MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAyNDk5MTQyMg", "link_type": "runs", "properties": {}, "data_source": "static"}, {"id": "conn4", "source_id": "MTA5MzYyMHxJTkZSQXxOQXwtNDM5NjY0MjkxOTg2NDY1OTc1NQ", "target_id": "MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAyNDk5MTQyMg", "link_type": "runs", "properties": {}, "data_source": "static"}, {"id": "conn5", "source_id": "MTA5MzYyMHxJTkZSQXxOQXwtNDM5NjY0MjkxOTg2NDY1OTc1NQ", "target_id": "MTA5MzYyMHxJTkZSQXxOQXw1MzQyMjU4MzI1ODgwMTc3MzY3", "link_type": "contains", "properties": {}, "data_source": "static"}]}