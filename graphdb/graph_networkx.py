import json
import networkx as nx

def create_subgraph_json(input_file, start_node_id, output_file, num_hops=2):
    # Load JSON data
    with open(input_file, 'r') as f:
        data = json.load(f)

    data = data[0][1]

    # Create a graph
    G = nx.Graph()

    # Add nodes
    for node in data['nodes']:
        G.add_node(node['id'], **node)

    # Add edges
    for link in data['links']:
        G.add_edge(link['source'], link['target'], **link)

    # Check if start_node_id exists in the graph
    if start_node_id not in G:
        raise ValueError(f"Start node {start_node_id} not found in the graph")

    # Get nodes within specified number of hops
    nodes_within_hops = set([start_node_id])
    for _ in range(num_hops):
        neighbors = set()
        for node in nodes_within_hops:
            neighbors.update(G.neighbors(node))
        nodes_within_hops.update(neighbors)

    # Create subgraph
    subgraph = G.subgraph(nodes_within_hops)

    # Prepare subgraph data in original format
    subgraph_data = {
        'nodes': [],
        'links': []
    }

    # Add nodes to subgraph data
    for node, data in subgraph.nodes(data=True):
        node_data = data.copy()
        node_data['id'] = node
        subgraph_data['nodes'].append(node_data)

    # Add links to subgraph data
    for source, target, data in subgraph.edges(data=True):
        edge_data = data.copy()
        edge_data['source'] = source
        edge_data['target'] = target
        subgraph_data['links'].append(edge_data)

    # Write subgraph data to JSON file
    with open(output_file, 'w') as f:
        json.dump(subgraph_data, f, indent=2)

    print(f"Subgraph data written to {output_file}")

# Example usage
input_file = 'topology_nvu_v3.json'
start_node_id = "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2"
output_file = 'nx_subgraph_nvu_v4_1hop.json'
num_hops = 1  # You can change this to any number of hops you want

create_subgraph_json(input_file, start_node_id, output_file, num_hops)