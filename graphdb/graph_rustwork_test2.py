import json
import rustworkx as rx

class NodesWithin2Hops(rx.visit.BFSVisitor):
    def __init__(self):
        self.nodes = set()
        self.depth = {}

    def discover_vertex(self, v):
        if v not in self.depth:
            self.depth[v] = 0
        if self.depth[v] <= 2:
            self.nodes.add(v)

    def tree_edge(self, e):
        u, v, _ = e
        self.depth[v] = self.depth[u] + 1
        if self.depth[v] <= 2:
            self.nodes.add(v)

def create_subgraph_json(input_file, start_node_id, output_file):
    # Load JSON data
    with open(input_file, 'r') as f:
        data = json.load(f)
    
    data = data[0][1]

    # Create a graph
    G = rx.PyGraph()

    # Add nodes
    node_map = {}
    reverse_node_map = {}
    for i, node in enumerate(data['nodes']):
        node_map[node['id']] = i
        reverse_node_map[i] = node['id']
        G.add_node(node)

    # Add edges
    for link in data['links']:
        source = node_map.get(link['source'])
        target = node_map.get(link['target'])
        if source is not None and target is not None:
            G.add_edge(source, target, link)
        else:
            print(f"Warning: Skipping edge {link['source']} -> {link['target']} due to missing node(s)")

    print(G.node_indices())
    print(G.edge_list())

    # Check if start_node_id exists in the graph
    if start_node_id not in node_map:
        raise ValueError(f"Start node {start_node_id} not found in the graph")

    # Get nodes within 2 hops
    start_node = node_map[start_node_id]
    visitor = NodesWithin2Hops()
    rx.graph_bfs_search(G, [start_node], visitor)
    subgraph_nodes = list(visitor.nodes)

    # Create subgraph
    subgraph = G.subgraph(subgraph_nodes)

    # Prepare subgraph data in original format
    subgraph_data = {
        'nodes': [],
        'links': []
    }

    # Add nodes to subgraph data
    for node in subgraph.node_indices():
        node_data = G[node].copy()
        node_data['id'] = reverse_node_map[node]
        subgraph_data['nodes'].append(node_data)

    # Add links to subgraph data
    for edge in subgraph.edge_list():
        source_id = reverse_node_map[edge[0]]
        target_id = reverse_node_map[edge[1]]
        try:
            edge_data = G.get_edge_data(edge[0], edge[1]).copy()
        except rx.NoEdgeBetweenNodes:
            print(f"Warning: No edge data found between {source_id} and {target_id}")
            edge_data = {}
        edge_data['source'] = source_id
        edge_data['target'] = target_id
        subgraph_data['links'].append(edge_data)

    # Write subgraph data to JSON file
    with open(output_file, 'w') as f:
        json.dump(subgraph_data, f, indent=2)

    print(f"Subgraph data written to {output_file}")

# Example usage
input_file = 'topology_nvu_v3.json'
start_node_id = "MTA5MzYyMHxJTkZSQXxOQXw0NTU4NDkzMjc2Mjg1MjU1ODY"
output_file = 'nx_subgraph_nvu_v3.json'

create_subgraph_json(input_file, start_node_id, output_file)