import json
from typing import Dict, List, Optional, Union, Any
from enum import Enum
from datetime import datetime
import uuid
import re
import asyncio
from gremlin_python.driver import client, serializer
from gremlin_python.driver.protocol import GremlinServerError
import sys
import traceback

class DataSource(Enum):
    STATIC = "static"
    NEW_RELIC = "new_relic"

class ComponentType(Enum):
    KUBERNETES_CLUSTER = "kubernetes_cluster"
    NAMESPACE = "namespace"
    POD = "pod"
    DATABASE = "database"
    CACHE = "cache"
    LOAD_BALANCER = "load_balancer"
    API_SERVER = "api_server"
    STORAGE = "storage"
    KAFKA = "kafka"
    SEARCH = "search"

class Component:
    def __init__(
        self,
        name: str,
        component_type: ComponentType,
        namespace: Optional[str] = None,
        properties: Dict[str, any] = None,
        id: Optional[str] = None,
        alt_names: List[str] = None,
        cluster_properties: Dict[str, Dict[str, any]] = None
    ):
        self.id = id or str(uuid.uuid4())
        self.name = name
        self.component_type = component_type
        self.namespace = namespace
        self.properties = properties or {}
        self.alt_names = alt_names or []
        self.cluster_properties = cluster_properties or {}
        self.data_source = DataSource.STATIC
        self.created_at = datetime.utcnow()
        self.updated_at = self.created_at

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "component_type": self.component_type.value,
            "namespace": self.namespace,
            "properties": self.properties,
            "alt_names": self.alt_names,
            "cluster_properties": self.cluster_properties,
            "data_source": self.data_source.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    @classmethod
    def from_dict(cls, data):
        component = cls(
            name=data["name"],
            component_type=ComponentType(data["component_type"]),
            namespace=data.get("namespace"),
            properties=data.get("properties", {}),
            id=data["id"],
            alt_names=data.get("alt_names", []),
            cluster_properties=data.get("cluster_properties", {})
        )
        component.data_source = DataSource(data["data_source"])
        component.created_at = datetime.fromisoformat(data["created_at"])
        component.updated_at = datetime.fromisoformat(data["updated_at"])
        return component

    def matches(self, pattern: str) -> bool:
        """Check if the component name or any alt_name matches the given pattern."""
        return any(re.match(pattern, name) for name in [self.name] + self.alt_names)

    def get_cluster_properties(self, cluster_name: str) -> Dict[str, any]:
        """Get properties specific to a cluster."""
        return self.cluster_properties.get(cluster_name, {})

class Connection:
    def __init__(
        self,
        source_id: str,
        target_id: str,
        connection_type: str,
        properties: Dict[str, any] = None,
        id: Optional[str] = None
    ):
        self.id = id or str(uuid.uuid4())
        self.source_id = source_id
        self.target_id = target_id
        self.connection_type = connection_type
        self.properties = properties or {}
        self.data_source = DataSource.STATIC
        self.created_at = datetime.utcnow()
        self.updated_at = self.created_at

    def to_dict(self):
        return {
            "id": self.id,
            "source_id": self.source_id,
            "target_id": self.target_id,
            "connection_type": self.connection_type,
            "properties": self.properties,
            "data_source": self.data_source.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    @classmethod
    def from_dict(cls, data):
        connection = cls(
            source_id=data["source_id"],
            target_id=data["target_id"],
            connection_type=data["connection_type"],
            properties=data.get("properties", {}),
            id=data["id"]
        )
        connection.data_source = DataSource(data["data_source"])
        connection.created_at = datetime.fromisoformat(data["created_at"])
        connection.updated_at = datetime.fromisoformat(data["updated_at"])
        return connection

class ServiceArchitecture:
    def __init__(self):
        self.components: Dict[str, Component] = {}
        self.connections: List[Connection] = []

    def add_component(self, component: Component):
        self.components[component.id] = component
        return component.id

    def add_connection(self, connection: Connection):
        self.connections.append(connection)

    def get_component_by_name_or_pattern(self, name_or_pattern: str) -> Optional[Component]:
        for component in self.components.values():
            if component.name == name_or_pattern or component.matches(name_or_pattern):
                return component
        return None

    def get_cluster_specific_architecture(self, cluster_name: str) -> 'ServiceArchitecture':
        cluster_arch = ServiceArchitecture()
        for component in self.components.values():
            cluster_component = Component(
                name=component.name,
                component_type=component.component_type,
                namespace=component.namespace,
                properties={**component.properties, **component.get_cluster_properties(cluster_name)},
                id=component.id,
                alt_names=component.alt_names
            )
            cluster_arch.add_component(cluster_component)
        
        for connection in self.connections:
            cluster_arch.add_connection(connection)
        
        return cluster_arch

    def to_dict(self):
        return {
            "components": [component.to_dict() for component in self.components.values()],
            "connections": [connection.to_dict() for connection in self.connections]
        }

    @classmethod
    def from_dict(cls, data):
        architecture = cls()
        for component_data in data["components"]:
            component = Component.from_dict(component_data)
            architecture.add_component(component)
        for connection_data in data["connections"]:
            connection = Connection.from_dict(connection_data)
            architecture.add_connection(connection)
        return architecture

    def to_json(self):
        return json.dumps(self.to_dict(), indent=2)

    @classmethod
    def from_json(cls, json_str):
        data = json.loads(json_str)
        return cls.from_dict(data)

# Function to create the initial architecture
def create_initial_architecture():
    architecture = ServiceArchitecture()

    # Add components
    mdm_aks_cluster = architecture.add_component(Component("MDM AKS Cluster", ComponentType.KUBERNETES_CLUSTER))

    # Kong Namespace
    kong_namespace = architecture.add_component(Component("Kong Namespaces", ComponentType.NAMESPACE, namespace="kong"))
    architecture.add_component(Component("kong-haproxy", ComponentType.POD, namespace="kong"))
    architecture.add_component(Component("kong-data-plane", ComponentType.POD, namespace="kong"))
    architecture.add_component(Component("kong-vault", ComponentType.POD, namespace="kong"))
    architecture.add_component(Component("kong-redis", ComponentType.CACHE, namespace="kong"))

    # kube-system Namespace
    kube_system_namespace = architecture.add_component(Component("kube-system", ComponentType.NAMESPACE, namespace="kube-system"))
    architecture.add_component(Component("cluster-autoscaler", ComponentType.POD, namespace="kube-system"))
    architecture.add_component(Component("coredns", ComponentType.POD, namespace="kube-system"))
    architecture.add_component(Component("efs-provisioner", ComponentType.POD, namespace="kube-system"))
    architecture.add_component(Component("metrics-server", ComponentType.POD, namespace="kube-system"))

    # mdm-core Namespace
    mdm_core_namespace = architecture.add_component(Component("mdm-core", ComponentType.NAMESPACE, namespace="mdm-core"))
    architecture.add_component(Component("activemq", ComponentType.POD, namespace="mdm-core"))
    architecture.add_component(Component("polaris-nginx", ComponentType.POD, namespace="mdm-core"))
    architecture.add_component(Component("aquila", ComponentType.POD, namespace="mdm-core"))
    architecture.add_component(Component("polaris", ComponentType.POD, namespace="mdm-core"))
    architecture.add_component(Component("jasper", ComponentType.POD, namespace="mdm-core"))
    architecture.add_component(Component("gqs", ComponentType.POD, namespace="mdm-core"))

    # debezium Namespace
    debezium_namespace = architecture.add_component(Component("debezium", ComponentType.NAMESPACE, namespace="debezium"))
    architecture.add_component(Component("Debezium Pods", ComponentType.POD, namespace="debezium"))

    # auriga Namespace
    auriga_namespace = architecture.add_component(Component("auriga", ComponentType.NAMESPACE, namespace="auriga"))
    architecture.add_component(Component("Auriga Service Pods", ComponentType.POD, namespace="auriga"))

    # External components
    architecture.add_component(Component("Azure Kubernetes Load Balancer", ComponentType.LOAD_BALANCER))
    architecture.add_component(Component("Kong Postgres", ComponentType.DATABASE))
    architecture.add_component(Component("MDM Storage Account MDM Audit Trails and CDN", ComponentType.STORAGE))
    architecture.add_component(Component("MDM Primary Redis", ComponentType.CACHE))
    architecture.add_component(Component("MDM Checkin Redis", ComponentType.CACHE))
    architecture.add_component(Component("HDInsight Kafka Brokers", ComponentType.KAFKA))
    architecture.add_component(Component("Elastic.co Search Cluster", ComponentType.SEARCH))
    architecture.add_component(Component("Aquila Postgres", ComponentType.DATABASE))
    architecture.add_component(Component("MDM Postgres", ComponentType.DATABASE))
    architecture.add_component(Component("Auriga Postgres", ComponentType.DATABASE))
    architecture.add_component(Component("Auriga Redis", ComponentType.CACHE))
    architecture.add_component(Component("AKS API Server", ComponentType.API_SERVER))

    # Add connections (simplified for brevity, you would need to add all connections shown in the diagram)
    lb = architecture.get_component_by_name("Azure Kubernetes Load Balancer")
    kong_haproxy = architecture.get_component_by_name("kong-haproxy")
    architecture.add_connection(Connection(lb.id, kong_haproxy.id, "http"))

    return architecture

# # Create the initial architecture
# initial_architecture = create_initial_architecture()

# # Convert to JSON
# json_data = initial_architecture.to_json()

# print("JSON representation of the architecture:")
# print(json_data)

# Example of how to update the architecture using JSON
def update_architecture_from_json(json_str):
    updated_architecture = ServiceArchitecture.from_json(json_str)
    # Here you can perform operations on the updated architecture
    # For example, add new components or connections based on New Relic data
    return updated_architecture

# Example usage:
# updated_arch = update_architecture_from_json(json_data)
# ... update the architecture with new data ...
# new_json_data = updated_arch.to_json()

# # Function to print the architecture (for verification)
# def print_architecture(arch: ServiceArchitecture):
#     print("Components:")
#     for component in arch.components.values():
#         print(f"  - {component.name} ({component.component_type.value})")
    
#     print("\nConnections:")
#     for connection in arch.connections:
#         source = arch.components[connection.source_id].name
#         target = arch.components[connection.target_id].name
#         print(f"  - {source} -> {target} ({connection.connection_type})")

# # Print the architecture
# print_architecture(architecture)

# Here you would add code to serialize this data and store it in Azure Cosmos DB
# For example:
# def save_to_cosmos_db(architecture: ServiceArchitecture):
#     # Code to connect to Cosmos DB and save the components and connections
#     pass

# save_to_cosmos_db(architecture)

# Define the initial architecture as a JSON string
INITIAL_ARCHITECTURE_JSON = '''
{
  "components": [
    {
      "id": "1",
      "name": "MDM AKS Cluster",
      "component_type": "kubernetes_cluster",
      "namespace": null,
      "properties": {},
      "alt_names": ["AKS-*", "Kubernetes-Cluster-*"],
      "cluster_properties": {
        "na1": {"location": "East US", "node_count": 5},
        "na2": {"location": "West US", "node_count": 3},
        "ap1": {"location": "Central US", "node_count": 4},
        "ap2": {"location": "South Central US", "node_count": 2},
        "eu1": {"location": "North Europe", "node_count": 2},
        "sb": {"location": "East US", "node_count": 3}
      },
      "data_source": "static"
    },
    {
      "id": "2",
      "name": "Kong Namespaces",
      "component_type": "namespace",
      "namespace": "kong",
      "properties": {},
      "data_source": "static"
    },
    {
      "id": "3",
      "name": "kong-haproxy",
      "component_type": "pod",
      "namespace": "kong",
      "properties": {},
      "data_source": "static"
    },
    {
      "id": "4",
      "name": "kong-data-plane",
      "component_type": "pod",
      "namespace": "kong",
      "properties": {},
      "data_source": "static"
    },
    {
      "id": "5",
      "name": "kong-vault",
      "component_type": "pod",
      "namespace": "kong",
      "properties": {},
      "data_source": "static"
    },
    {
      "id": "6",
      "name": "kong-redis",
      "component_type": "cache",
      "namespace": "kong",
      "properties": {},
      "data_source": "static"
    },
    {
      "id": "7",
      "name": "kube-system",
      "component_type": "namespace",
      "namespace": "kube-system",
      "properties": {},
      "data_source": "static"
    },
    {
      "id": "8",
      "name": "cluster-autoscaler",
      "component_type": "pod",
      "namespace": "kube-system",
      "properties": {},
      "data_source": "static"
    },
    {
      "id": "9",
      "name": "coredns",
      "component_type": "pod",
      "namespace": "kube-system",
      "properties": {},
      "data_source": "static"
    },
    {
      "id": "10",
      "name": "Azure Kubernetes Load Balancer",
      "component_type": "load_balancer",
      "namespace": null,
      "properties": {},
      "data_source": "static"
    }
  ],
  "connections": [
    {
      "id": "conn1",
      "source_id": "10",
      "target_id": "3",
      "connection_type": "http",
      "properties": {},
      "data_source": "static"
    }
  ]
}
'''

def create_initial_architecture_from_json(json_str):
    return ServiceArchitecture.from_json(json_str)




# You'll need to install the gremlin-python library:
# pip install gremlin-python

# Azure Cosmos DB Configuration
config = {
    'endpoint': 'YOUR_COSMOS_DB_ENDPOINT',
    'username': '/dbs/YOUR_DATABASE/colls/YOUR_GRAPH_CONTAINER',
    'password': 'YOUR_COSMOS_DB_KEY',
    'graph_name': 'YOUR_GRAPH_NAME'
}

class CosmosDBGraphManager:
    def __init__(self, config):
        self.config = config
        self.client = client.Client(
            f'wss://{config["endpoint"]}:443/',
            'g',
            username=config['username'],
            password=config['password'],
            message_serializer=serializer.GraphSONSerializersV2d0()
        )

    async def create_vertex(self, label: str, properties: Dict[str, Any]):
        query = f"g.addV('{label}')"
        for key, value in properties.items():
            if isinstance(value, str):
                query += f".property('{key}', '{value}')"
            else:
                query += f".property('{key}', {json.dumps(value)})"
        
        return await self.execute_query(query)

    async def create_edge(self, label: str, from_id: str, to_id: str, properties: Dict[str, Any]):
        query = f"g.V('{from_id}').addE('{label}').to(g.V('{to_id}')"
        for key, value in properties.items():
            if isinstance(value, str):
                query += f".property('{key}', '{value}')"
            else:
                query += f".property('{key}', {json.dumps(value)})"
        query += ")"
        
        return await self.execute_query(query)

    async def execute_query(self, query):
        print(f"Executing Gremlin query: {query}")
        try:
            return await self.client.submit(query)
        except GremlinServerError as e:
            print(f'Error: {e}')
            traceback.print_exc(file=sys.stdout)
            raise

    async def close(self):
        await self.client.close()

async def create_graph_from_architecture(architecture: ServiceArchitecture, graph_manager: CosmosDBGraphManager):
    # Create vertices for components
    for component in architecture.components.values():
        properties = {
            'id': component.id,
            'name': component.name,
            'component_type': component.component_type.value,
            'namespace': component.namespace,
            'properties': json.dumps(component.properties),
            'alt_names': json.dumps(component.alt_names),
            'cluster_properties': json.dumps(component.cluster_properties),
            'data_source': component.data_source.value
        }
        await graph_manager.create_vertex('Component', properties)

    # Create edges for connections
    for connection in architecture.connections:
        properties = {
            'id': connection.id,
            'connection_type': connection.connection_type,
            'properties': json.dumps(connection.properties),
            'data_source': connection.data_source.value
        }
        await graph_manager.create_edge('Connects', connection.source_id, connection.target_id, properties)

async def main():
    # Create the initial architecture from JSON
    initial_architecture = create_initial_architecture_from_json()

    # Initialize the CosmosDBGraphManager
    graph_manager = CosmosDBGraphManager(config)

    try:
        # Create the graph from the architecture
        await create_graph_from_architecture(initial_architecture, graph_manager)
        print("Graph created successfully in Azure Cosmos DB")

    except Exception as e:
        print(f"An error occurred: {e}")
        traceback.print_exc(file=sys.stdout)

    finally:
        # Close the connection
        await graph_manager.close()

if __name__ == "__main__":
    asyncio.get_event_loop().run_until_complete(main())
# Example usage
if __name__ == "__main__":

    # Define the initial architecture as a JSON string
    INITIAL_ARCHITECTURE_JSON = '''
    {
    "components": [
        {
        "id": "1",
        "name": "MDM AKS Cluster",
        "component_type": "kubernetes_cluster",
        "namespace": null,
        "properties": {},
        "data_source": "static"
        },
        {
        "id": "2",
        "name": "Kong Namespaces",
        "component_type": "namespace",
        "namespace": "kong",
        "properties": {},
        "data_source": "static"
        },
        {
        "id": "3",
        "name": "kong-haproxy",
        "component_type": "pod",
        "namespace": "kong",
        "properties": {},
        "data_source": "static"
        },
        {
        "id": "4",
        "name": "kong-data-plane",
        "component_type": "pod",
        "namespace": "kong",
        "properties": {},
        "data_source": "static"
        },
        {
        "id": "5",
        "name": "kong-vault",
        "component_type": "pod",
        "namespace": "kong",
        "properties": {},
        "data_source": "static"
        },
        {
        "id": "6",
        "name": "kong-redis",
        "component_type": "cache",
        "namespace": "kong",
        "properties": {},
        "data_source": "static"
        },
        {
        "id": "7",
        "name": "kube-system",
        "component_type": "namespace",
        "namespace": "kube-system",
        "properties": {},
        "data_source": "static"
        },
        {
        "id": "8",
        "name": "cluster-autoscaler",
        "component_type": "pod",
        "namespace": "kube-system",
        "properties": {},
        "data_source": "static"
        },
        {
        "id": "9",
        "name": "coredns",
        "component_type": "pod",
        "namespace": "kube-system",
        "properties": {},
        "data_source": "static"
        },
        {
        "id": "10",
        "name": "Azure Kubernetes Load Balancer",
        "component_type": "load_balancer",
        "namespace": null,
        "properties": {},
        "data_source": "static"
        }
    ],
    "connections": [
        {
        "id": "conn1",
        "source_id": "10",
        "target_id": "3",
        "connection_type": "http",
        "properties": {},
        "data_source": "static"
        }
    ]
    }
    '''

    

    # Create the initial architecture from JSON
    initial_architecture = create_initial_architecture_from_json(INITIAL_ARCHITECTURE_JSON)

    print("Initial Architecture Components:")
    for component in initial_architecture.components.values():
        print(f"- {component.name} ({component.component_type.value})")
        print(f"  Alt Names: {component.alt_names}")
        print(f"  Cluster Properties: {component.cluster_properties}")

    # Example of getting a component by pattern
    aks_component = initial_architecture.get_component_by_name_or_pattern("AKS-*")
    if aks_component:
        print(f"\nFound AKS component: {aks_component.name}")

    # Example of getting cluster-specific architecture
    cluster1_arch = initial_architecture.get_cluster_specific_architecture("cluster1")
    print("\nCluster 1 Specific Architecture:")
    for component in cluster1_arch.components.values():
        print(f"- {component.name}: {component.properties}")

    # Convert updated architecture to JSON
    updated_json = initial_architecture.to_json()
    print("\nUpdated Architecture JSON:")
    print(updated_json)
