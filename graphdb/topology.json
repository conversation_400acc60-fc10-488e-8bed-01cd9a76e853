{"nodes": [{"id": "MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAyNDk5MTQyMg", "name": "nmdm-na2-r94-polaris-challenger", "group": 1, "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "additionalInfo": "Entity in the network", "issue": true}, {"id": "MTA5MzYyMHxCUk9XU0VSfEFQUExJQ0FUSU9OfDExMDMzMDYyMzg", "name": "nmdm-na2-r94-polaris-challenger", "group": 1, "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "additionalInfo": "Entity in the network", "issue": false}], "links": [{"source": "MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAyNDk5MTQyMg", "target": "MTA5MzYyMHxCUk9XU0VSfEFQUExJQ0FUSU9OfDExMDMzMDYyMzg", "value": 1}], "rca": "RCA solution", "issue_info": "Issue Details"}