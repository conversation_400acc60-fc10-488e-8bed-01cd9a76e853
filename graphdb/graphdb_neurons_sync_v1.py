import logging
import re
from gremlin_python.driver import client, serializer
from gremlin_python.driver.protocol import GremlinServerError
import json
from thefuzz import process as fuzz_process
import os
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv(override=True)

# Cosmos DB configuration
endpoint = os.getenv('COSMOS_GREMLIN_ENDPOINT')
database = "neurons"
collection = "nvu"
key = os.getenv('COSMOS_GREMLIN_KEY')

# Initialize Gremlin client
client = client.Client(
    f'{endpoint}',
    'g',
    username=f"/dbs/{database}/colls/{collection}",
    password=key,
    message_serializer=serializer.GraphSONSerializersV2d0()
)

def store_topology_data(topology_data):
    for source, data in topology_data:
        logger.info(f"Processing source: {source}")
        for node in data.get('nodes', []):
            store_node(node)
        for link in data.get('links', []):
            store_link(link)

def flatten_dict(d, parent_key='', sep='_'):
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)

def escape_string(s):
    """Escape special characters in a string for Gremlin query."""
    return re.sub(r'(["\\\'])', r'\\\1', str(s))

def store_node(node):
    # First, check if the node already exists
    check_query = f"g.V('{node['id']}')"
    try:
        exists = client.submit(check_query).all().result(timeout=30)
        if exists:
            logger.info(f"Node {node['id']} already exists. Updating properties.")
            update_query = f"g.V('{node['id']}')"
            for key, value in node.items():
                if key not in ['id', 'type', 'entity_id']:
                    if isinstance(value, (list, dict)):
                        value = json.dumps(value)
                    elif isinstance(value, bool):
                        value = 'true' if value else 'false'
                    elif isinstance(value, (int, float)):
                        value = str(value)
                    elif value is None:
                        continue
                    else:
                        value = str(value)
                    update_query += f".property('{key}', '{value}')"
            logger.info(f"Executing update query: {update_query}")
            result = client.submit(update_query).all().result(timeout=30)
            logger.info(f"Successfully updated node: {node['id']}")
            return result
    except Exception as e:
        logger.error(f"Error checking if node {node['id']} exists: {str(e)}")
        return None

    # If the node doesn't exist, proceed with adding it
    query = f"g.addV('{node['type']}')"
    for key, value in node.items():
        if key not in ['type']:
            if isinstance(value, (list, dict)):
                value = json.dumps(value)
            elif isinstance(value, bool):
                value = 'true' if value else 'false'
            elif isinstance(value, (int, float)):
                value = str(value)
            elif value is None:
                continue
            else:
                value = str(value)
            query += f".property('{key}', '{value}')"
    
    logger.info(f"Executing query: {query}")
    
    try:
        result_set = client.submit(query)
        result = result_set.all().result(timeout=30)
        logger.info(f"Successfully stored node: {node['id']}")
        return result
    except Exception as e:
        logger.error(f"Error storing node {node['id']}: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Error args: {e.args}")
        try:
            status_attributes = result_set.status_attributes
            logger.error(f"Result set status attributes: {status_attributes}")
        except Exception as inner_e:
            logger.error(f"Error getting status attributes: {str(inner_e)}")
    return None

def store_link(link):
    query = f"g.V('{link['source']}').addE('{link['link_type']}').to(g.V('{link['target']}'))"
    
    # Flatten the properties
    # flat_properties = flatten_dict(link)
    
    for key, value in link.items():
        if key not in ['source', 'target', 'link_type']:
            if isinstance(value, (list, dict)):
                value = json.dumps(value)
            elif isinstance(value, bool):
                value = 'true' if value else 'false'
            elif isinstance(value, (int, float)):
                value = str(value)
            elif value is None:
                continue  # Skip None values
            else:
                value = str(value)
            
            # Escape any double quotes in the value
            # value = value.replace('"', '\\"')
            
            query += f'.property("{key}", "{value}")'
    
    logger.info(f"Executing query: {query}")
    
    try:
        result_set = client.submit(query)
        result = result_set.all().result()
        logger.info(f"Query result: {result}")
        logger.info(f"Successfully stored link: {link['source']} -> {link['target']}")
        return result
    except Exception as e:
        logger.error(f"Error storing link {link['source']} -> {link['target']}: {str(e)}")
        logger.error(f"Query: {query}")
        if hasattr(e, 'status_attributes'):
            logger.error(f"Status attributes: {e.status_attributes}")
        return None
    
def get_node_by_id(node_id):
    query = f"g.V('{node_id}')"
    try:
        result = client.submit(query).all().result()
        return result[0] if result else None
    except Exception as e:
        logger.error(f"Error getting node by ID {node_id}: {str(e)}")
        return None

def get_node_by_name(name, threshold=80):
    query = "g.V()"
    try:
        results = client.submit(query).all().result()
        
        def get_name(node):
            return node.properties.get('name', [''])[0]
        
        best_match = fuzz_process.extractOne(name, results, scorer=fuzz_process.partial_ratio, key=get_name)
        return best_match[0] if best_match and best_match[1] >= threshold else None
    except Exception as e:
        logger.error(f"Error getting node by name {name}: {str(e)}")
        return None

def get_subgraph(start_node_id, depth):
    query = f"""
    g.V('{start_node_id}').repeat(both().simplePath()).times({depth}).path().
      project('nodes', 'edges').
        by(unfold().dedup().valueMap().with(WithOptions.tokens)).
        by(unfold().bothE().dedup().project('id', 'source', 'target', 'label').
             by(id).by(outV().id()).by(inV().id()).by(label))
    """
    try:
        result = client.submit(query).all().result()
        
        if not result:
            return None
        
        subgraph = result[0]
        return subgraph
    except Exception as e:
        logger.error(f"Error getting subgraph for node {start_node_id}: {str(e)}")
        return None

# Example usage:
if __name__ == "__main__":
    try:
        # Load topology data from file
        with open('merged_topology_nvu.json', 'r') as f:
            topology_data = json.load(f)
        
        # Store topology data
        for source, data in topology_data:
            logger.info(f"Processing source: {source}")
            for node in data.get('nodes', []):
                result = store_node(node)
                if result is None:
                    logger.warning(f"Failed to store node: {node['id']}")
                else:
                    logger.info(f"Successfully stored node: {node['id']}")
        
        # Query node by ID
        node = get_node_by_id('MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz')
        print("Node by ID:", node)
        
        # Query node by name (fuzzy search)
        node = get_node_by_name('saas-inventory-client-app')
        print("Node by name:", node)
        
        # Get subgraph
        subgraph = get_subgraph('MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz', 1)
        print("Subgraph:", json.dumps(subgraph, indent=2))
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        logger.exception("Exception details:")