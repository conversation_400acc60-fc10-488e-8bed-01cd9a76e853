{"nodes": [{"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTA3MTMzMzQyNzkyOTE5OTkzNg", "name": "automation-management-webhost", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["automation-management-webhost"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["834a4c4039fd518b8eafaefcee6e81625ac22f74cc753d07ffa46fb957afdbae"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/automation-management/automation-management-webhost:1918689"], "k8s.containerName": ["automation-management-webhost"], "k8s.deploymentName": ["automation-management-webhost-deployment"], "k8s.namespaceName": ["automation-management"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["automation-management-webhost-deployment-fb889f59d-8f2wh"], "k8s.replicasetName": ["automation-management-webhost-deployment-fb889f59d"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["fb889f59d"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTA3MTMzMzQyNzkyOTE5OTkzNg", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODQ2Mzc0NTkyMzAxNzAzODg1Mg", "name": "settings-webapi-container-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["24fc3e80295478b844e8cbf5ccdfb3e3f5b084cdc4569dc2899721de14421c38"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/settings-server/settings.webapi.service:1906299"], "k8s.containerName": ["settings-webapi-container-service"], "k8s.deploymentName": ["settings-webapi-deployment"], "k8s.namespaceName": ["settings"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["settings-webapi-deployment-58d646b987-cx9f6"], "k8s.replicasetName": ["settings-webapi-deployment-58d646b987"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["58d646b987"], "service-pod-link": ["settings-webapi-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtODQ2Mzc0NTkyMzAxNzAzODg1Mg", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "name": "aks-default-********-vmss0007qj", "issue": false, "group": 1, "icon": "static/icons/node.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "INFRASTRUCTURE_HOST_ENTITY", "type": "HOST", "agentName": ["Infrastructure"], "agentVersion": ["1.46.0"], "availabilityZone": ["3"], "clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "coreCount": ["4"], "fullHostname": ["aks-default-********-vmss0007qj"], "host.id": ["ce76bd5b-8f92-4bd2-a344-87faf2846364"], "hostStatus": ["running"], "hostname": ["aks-default-********-vmss0007qj"], "instanceType": ["Standard_D8s_v3"], "kernelVersion": ["5.15.0-1071-azure"], "linuxDistribution": ["Ubuntu 22.04.4 LTS"], "operatingSystem": ["linux"], "processorCount": ["8"], "regionName": ["eastus"], "subscriptionId": ["53f45c73-c7c2-4733-a772-6b3505573621"], "systemMemoryBytes": ["***********"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "type": "HOST", "entityType": "INFRASTRUCTURE_HOST_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyNzM0ODA2NjU0MDQ1ODExODQx", "name": "login-app-login-service-agent-only", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["login-app-login-service"], "appWhenSplit": ["login-app-login-service-agent-only"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["9541c637657a4e57a18968997c73ff1be3d60d7ee3cb7e5b9098de8e87ec7e0f"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/login-app/login-app-login-service:1922913"], "k8s.containerName": ["login-app-login-service-agent-only"], "k8s.deploymentName": ["login-app-login-service-agent-only"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["login-app-login-service-agent-only-6c787495-9wvmx"], "k8s.replicasetName": ["login-app-login-service-agent-only-6c787495"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["6c787495"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwyNzM0ODA2NjU0MDQ1ODExODQx", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4MTQ0MzQ3NjM0MDM5MjA2NDc", "name": "automation-management-sendo-priority", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["automation-management-sendo-priority"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["47be23b3bbccb0df22eedd90ad1501c4032dba055ebeee4ec4280edbeac7347c"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/automation-management/automation-management-sendo:1918689"], "k8s.containerName": ["automation-management-sendo-priority"], "k8s.deploymentName": ["automation-management-sendo-priority-deployment"], "k8s.namespaceName": ["automation-management"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["automation-management-sendo-priority-deployment-7d9cbc67c89mmh8"], "k8s.replicasetName": ["automation-management-sendo-priority-deployment-7d9cbc67c8"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["7d9cbc67c8"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4MTQ0MzQ3NjM0MDM5MjA2NDc", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzNzUzMjExNDUzMDczOTg4NzIz", "name": "automation-management-enqueue-priority", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["automation-management-enqueue-priority"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["5431127c2da8bd9f28d429770b71e9da919933c11ef5dd26e9d7261338652e5e"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/automation-management/automation-management-enqueue:1918689"], "k8s.containerName": ["automation-management-enqueue-priority"], "k8s.deploymentName": ["automation-management-enqueue-priority-deployment"], "k8s.namespaceName": ["automation-management"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["automation-management-enqueue-priority-deployment-599f5654z75xl"], "k8s.replicasetName": ["automation-management-enqueue-priority-deployment-599f565447"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["599f565447"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzNzUzMjExNDUzMDczOTg4NzIz", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw0MzM5NjY4Njc5OTUzOTY4MDAx", "name": "device-sentiment-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["faa350f8a2ced2d41f5975f639140a69ba6964d01a1bfbbc7b10e8aa5713f01c"], "k8s.containerImage": ["apolloteam.azurecr.io/dexi/device-sentiment-service:********.1"], "k8s.containerName": ["device-sentiment-service"], "k8s.deploymentName": ["device-sentiment-service"], "k8s.namespaceName": ["dexi-services"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["device-sentiment-service-75474f955-t4kfz"], "k8s.replicasetName": ["device-sentiment-service-75474f955"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75474f955"], "service-pod-link": ["device-sentiment-service-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw0MzM5NjY4Njc5OTUzOTY4MDAx", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw3Nzc0MjAwMTQ0ODY5NDAxMzg3", "name": "login-app-admin-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["login-app-admin-service"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["baa8f0c1f8108b3253f484136a96274560b8f8eb72440b0fddd07127e8b5930e"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/login-app/login-app-admin-service:1922913"], "k8s.containerName": ["login-app-admin-service"], "k8s.deploymentName": ["login-app-admin-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["login-app-admin-service-8b4c5677b-dhqjn"], "k8s.replicasetName": ["login-app-admin-service-8b4c5677b"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["8b4c5677b"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw3Nzc0MjAwMTQ0ODY5NDAxMzg3", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMzUxNDA4NTY2MzQwODg1Mzg2NQ", "name": "ism-sync-handler-container-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["ism-sync-handler-service"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["5e8fb98938e663d1292f16c1a757697e797aba0a8643a63bccacb0925b775637"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/ism-sync/sync.handler.service:1865100"], "k8s.containerName": ["ism-sync-handler-container-service"], "k8s.deploymentName": ["ism-sync-handler-service-deployment"], "k8s.namespaceName": ["ism-sync"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["ism-sync-handler-service-deployment-646bcdc944-lgs8m"], "k8s.replicasetName": ["ism-sync-handler-service-deployment-646bcdc944"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["646bcdc944"], "service-pod-link": ["ism-sync-handler-service-selector"], "trustedAccountId": ["1093620"], "version": ["1865100"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMzUxNDA4NTY2MzQwODg1Mzg2NQ", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ0NjI0ODQ1MTI4MDM3MTk2OQ", "name": "kubelet", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app.kubernetes.io/component": ["kubelet"], "app.kubernetes.io/instance": ["newrelic-bundle"], "app.kubernetes.io/name": ["newrelic-infrastructure"], "container.state": ["Running"], "controller-revision-hash": ["69c459c7b6"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["c2af39f6ac4af86baaa47e0feae4561c902d0c6c71b64b104547731e4190cf67"], "k8s.containerImage": ["newrelic/nri-kubernetes:3.18.3"], "k8s.containerName": ["kubelet"], "k8s.daemonsetName": ["newrelic-bundle-nrk8s-kubelet"], "k8s.namespaceName": ["ns-core"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["newrelic-bundle-nrk8s-kubelet-rsxjq"], "k8s.status": ["Running"], "mode": ["privileged"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-generation": ["5"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ0NjI0ODQ1MTI4MDM3MTk2OQ", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwxODIyNDU0NDQ0MDQwMDM0Njg3", "name": "twistlock-defender", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["twistlock-defender"], "container.state": ["Running"], "controller-revision-hash": ["6cf65857d"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["0ea7c5ba1977bb38275bc7d8bd33bc4e28e2f446dc27ef4c8cd21a14a3f52aa5"], "k8s.containerImage": ["registry-auth.twistlock.com/tw_xte7xofo9nhi0kd6ahxzlg3dvgyfnszu/twistlock/defender:defender_32_04_112"], "k8s.containerName": ["twistlock-defender"], "k8s.daemonsetName": ["twistlock-defender-ds"], "k8s.namespaceName": ["twistlock"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["twistlock-defender-ds-qrg27"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-generation": ["5"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwxODIyNDU0NDQ0MDQwMDM0Njg3", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2", "name": "rollout-update-handler-container", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["rollout-update-handler-container"], "container.state": ["Waiting"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["e65b8b8c493699aa23fd2acacfc7dbed1c438d98939ec2e609461f3dfcbd1c98"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/memapps/memappsrolloutupdatermessagehandler:1898931"], "k8s.containerName": ["rollout-update-handler-container"], "k8s.deploymentName": ["rollout-update-handler-container"], "k8s.namespaceName": ["apps-for-mem"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["rollout-update-handler-container-5bb48f95fb-kkx46"], "k8s.replicasetName": ["rollout-update-handler-container-5bb48f95fb"], "k8s.status": ["Waiting"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["5bb48f95fb"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzNDk0NzU1MDk1NDkzOTcxMTMw", "name": "agent", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app.kubernetes.io/component": ["kubelet"], "app.kubernetes.io/instance": ["newrelic-bundle"], "app.kubernetes.io/name": ["newrelic-infrastructure"], "container.state": ["Running"], "controller-revision-hash": ["69c459c7b6"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["dee64886ff8aac700de74a48dfa94cf76654882513cadfd96df6984dd7a2055f"], "k8s.containerImage": ["newrelic/infrastructure-bundle:3.2.16"], "k8s.containerName": ["agent"], "k8s.daemonsetName": ["newrelic-bundle-nrk8s-kubelet"], "k8s.namespaceName": ["ns-core"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["newrelic-bundle-nrk8s-kubelet-rsxjq"], "k8s.status": ["Running"], "mode": ["privileged"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-generation": ["5"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzNDk0NzU1MDk1NDkzOTcxMTMw", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNjkwNzIzMzE4Mjg5NDgyNTcyNQ", "name": "inventorygateway-uploadserver", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["fa569f4e280a2d4bd097a2e7f4403d57cb389c4ebecae604c0ef3481c7eacf0c"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/inventorygateway-uploadserver/inventorygateway-uploadserver:1882212"], "k8s.containerName": ["inventorygateway-uploadserver"], "k8s.deploymentName": ["inventorygateway-uploadserver"], "k8s.namespaceName": ["data-services"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["inventorygateway-uploadserver-6d4f86ccf7-7crz5"], "k8s.replicasetName": ["inventorygateway-uploadserver-6d4f86ccf7"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["6d4f86ccf7"], "service-pod-link": ["inventorygateway-uploadserver-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNjkwNzIzMzE4Mjg5NDgyNTcyNQ", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTI2MDc3OTUyMTAwMDIyODkzMw", "name": "dataservices-dataimport-monitor-service-container-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["4f4cfaf43fc24600ea46041e13e854d8665dd2b761df6cd6aa5e776cfc181469"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/dataservices-dataimport/dataservices.dataimport.monitor.service:1905069"], "k8s.containerName": ["dataservices-dataimport-monitor-service-container-service"], "k8s.deploymentName": ["dataservices-dataimport-monitor-service-deployment"], "k8s.namespaceName": ["dataservices-dataimport"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["dataservices-dataimport-monitor-service-deployment-9c8fc85775nc"], "k8s.replicasetName": ["dataservices-dataimport-monitor-service-deployment-9c8fc859d"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["9c8fc859d"], "service-pod-link": ["dataservices-dataimport-monitor-service-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTI2MDc3OTUyMTAwMDIyODkzMw", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMTE0MjY0NTE0NDMyMzA5ODk3MQ", "name": "newrelic-logging", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["newrelic-logging"], "app.kubernetes.io/name": ["newrelic-logging"], "container.state": ["Running"], "controller-revision-hash": ["58685d46cd"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["cf028894d24514c4af8934bea425104f3cf96660c43df5a6ed4bf4c90c6cba40"], "k8s.containerImage": ["newrelic/newrelic-fluentbit-output:1.17.3"], "k8s.containerName": ["newrelic-logging"], "k8s.daemonsetName": ["newrelic-bundle-newrelic-logging"], "k8s.namespaceName": ["ns-core"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["newrelic-bundle-newrelic-logging-x7ln7"], "k8s.status": ["Running"], "kubernetes.io/os": ["linux"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-generation": ["6"], "release": ["newrelic-bundle"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMTE0MjY0NTE0NDMyMzA5ODk3MQ", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MDA5OTc2MDYyMzY5NTEzMDY0", "name": "login-app-login-service-non-agent", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["login-app-login-service"], "appWhenSplit": ["login-app-login-service-non-agent"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["f6aed795e5584bfe21884fa8336f7c727294656a4bfdd002dffa59ef19d430e4"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/login-app/login-app-login-service:1922913"], "k8s.containerName": ["login-app-login-service-non-agent"], "k8s.deploymentName": ["login-app-login-service-non-agent"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["login-app-login-service-non-agent-75bf9f9959-4h8z5"], "k8s.replicasetName": ["login-app-login-service-non-agent-75bf9f9959"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75bf9f9959"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2MDA5OTc2MDYyMzY5NTEzMDY0", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwxNjg2OTg0NjAzODczMjQ0MDk0", "name": "saas-processor-console-container-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["saas-processor-console"], "batch.kubernetes.io/controller-uid": ["be775b66-f657-4f3c-a335-f816152ac023"], "batch.kubernetes.io/job-name": ["saas-processor-console-app-********"], "container.state": ["Running"], "controller-uid": ["be775b66-f657-4f3c-a335-f816152ac023"], "job-name": ["saas-processor-console-app-********"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["1b22f9f81c79f75f34e8c9c6658c24fdacc4e2d19937fd0ea49eeafc5416f740"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/saas-inventory/processor.console:1902979"], "k8s.containerName": ["saas-processor-console-container-service"], "k8s.jobName": ["saas-processor-console-app-********"], "k8s.namespaceName": ["saas-inventory"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["saas-processor-console-app-********-mjn5w"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "trustedAccountId": ["1093620"], "version": ["1902979"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwxNjg2OTg0NjAzODczMjQ0MDk0", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw0MTQwMTczMzE1MjY0NTkwMjM4", "name": "smartadvisors-service-container-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["ef9b4df4fa93c1773ec2bc2a7a8fd64f99441be5b83c6ee5756146fd5838912d"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/smartadvisors/smartadvisors.service:1786408"], "k8s.containerName": ["smartadvisors-service-container-service"], "k8s.deploymentName": ["smartadvisors-service-deployment"], "k8s.namespaceName": ["smartadvisors"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["smartadvisors-service-deployment-b8b969b96-fgbhj"], "k8s.replicasetName": ["smartadvisors-service-deployment-b8b969b96"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["b8b969b96"], "service-pod-link": ["smartadvisors-service-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw0MTQwMTczMzE1MjY0NTkwMjM4", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw0NTc5NDMwNjAyMzA0NTkwNTk1", "name": "query-api", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["a84514f1a48aa20d428e34606c9cb04f208450252c8d55afed0355d5c911c6c4"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/data-services.queryapi/data-services.queryapi:1920399"], "k8s.containerName": ["query-api"], "k8s.deploymentName": ["data-services-query-api-deployment"], "k8s.namespaceName": ["data-services"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["data-services-query-api-deployment-5d744ff7d9-8t4tw"], "k8s.replicasetName": ["data-services-query-api-deployment-5d744ff7d9"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["5d744ff7d9"], "service-pod-link": ["data-services-query-api-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw0NTc5NDMwNjAyMzA0NTkwNTk1", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw0NDE5MzU5MTAzNjA3ODg5NTE3", "name": "patch-content-message-handlers", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app.kubernetes.io/instance": ["patch-content.deploy"], "app.kubernetes.io/name": ["patch-content-message-handlers"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["2eb7e232122c67baf58a1b7223db4c8f55a32f97d659a1c33731475c851a643d"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/patch-content/patchcontent.messagehandler.container:1919596"], "k8s.containerName": ["patch-content-message-handlers"], "k8s.deploymentName": ["patch-content-message-handlers-deploy"], "k8s.namespaceName": ["patch-content"], "k8s.nodeName": ["aks-default-********-vmss0007qj"], "k8s.podName": ["patch-content-message-handlers-deploy-7559d588fb-tkbjb"], "k8s.replicasetName": ["patch-content-message-handlers-deploy-7559d588fb"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["7559d588fb"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw0NDE5MzU5MTAzNjA3ODg5NTE3", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}], "links": [{"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwtNTA3MTMzMzQyNzkyOTE5OTkzNg", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTA3MTMzMzQyNzkyOTE5OTkzNg", "target": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwtODQ2Mzc0NTkyMzAxNzAzODg1Mg", "source": "MTA5MzYyMHxJTkZSQXxOQXwtODQ2Mzc0NTkyMzAxNzAzODg1Mg", "target": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwzNDk0NzU1MDk1NDkzOTcxMTMw", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXwzNDk0NzU1MDk1NDkzOTcxMTMw", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwtMTE0MjY0NTE0NDMyMzA5ODk3MQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXwtMTE0MjY0NTE0NDMyMzA5ODk3MQ", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwtNTQ0NjI0ODQ1MTI4MDM3MTk2OQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ0NjI0ODQ1MTI4MDM3MTk2OQ", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwxODIyNDU0NDQ0MDQwMDM0Njg3", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXwxODIyNDU0NDQ0MDQwMDM0Njg3", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwtNTI2MDc3OTUyMTAwMDIyODkzMw", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTI2MDc3OTUyMTAwMDIyODkzMw", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwyNzM0ODA2NjU0MDQ1ODExODQx", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXwyNzM0ODA2NjU0MDQ1ODExODQx", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXw3Nzc0MjAwMTQ0ODY5NDAxMzg3", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXw3Nzc0MjAwMTQ0ODY5NDAxMzg3", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXw2MDA5OTc2MDYyMzY5NTEzMDY0", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXw2MDA5OTc2MDYyMzY5NTEzMDY0", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwtMzUxNDA4NTY2MzQwODg1Mzg2NQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXwtMzUxNDA4NTY2MzQwODg1Mzg2NQ", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXw4MTQ0MzQ3NjM0MDM5MjA2NDc", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXw4MTQ0MzQ3NjM0MDM5MjA2NDc", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwzNzUzMjExNDUzMDczOTg4NzIz", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXwzNzUzMjExNDUzMDczOTg4NzIz", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwxNjg2OTg0NjAzODczMjQ0MDk0", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXwxNjg2OTg0NjAzODczMjQ0MDk0", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXwtNjkwNzIzMzE4Mjg5NDgyNTcyNQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNjkwNzIzMzE4Mjg5NDgyNTcyNQ", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXw0MzM5NjY4Njc5OTUzOTY4MDAx", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXw0MzM5NjY4Njc5OTUzOTY4MDAx", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXw0NDE5MzU5MTAzNjA3ODg5NTE3", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXw0NDE5MzU5MTAzNjA3ODg5NTE3", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXw0NTc5NDMwNjAyMzA0NTkwNTk1", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXw0NTc5NDMwNjAyMzA0NTkwNTk1", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXw0MTQwMTczMzE1MjY0NTkwMjM4", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0", "target": "MTA5MzYyMHxJTkZSQXxOQXw0MTQwMTczMzE1MjY0NTkwMjM4", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}]}