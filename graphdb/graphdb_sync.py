import json
import os
import requests
from dotenv import load_dotenv
from collections import deque
import logging
from typing import Dict, List, Tuple, Any

# Load environment variables
load_dotenv(override=True)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TopologyGenerator:
    def __init__(self, entity_id: str, depth: int = 2):
        if not entity_id:
            raise ValueError("Entity ID cannot be `None` or empty")
        self.entity_id = entity_id
        self.depth = depth
        self.endpoint = os.getenv("NEWRELIC_API_ENDPOINT", "https://api.newrelic.com/graphql")
        self.api_key = os.getenv("NEWRELIC_API_KEY")
        self.account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
        self.icons = {
            "default": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png",
            "redis": "https://redis.com/wp-content/themes/wpx/assets/images/logo-redis.svg",
            "database": "https://upload.wikimedia.org/wikipedia/commons/c/c5/201603_database.png",
        }
        self.topology = {"nodes": [], "links": []}
        self.processed_entities = set()
        self.entities_to_process = deque([(self.entity_id, 0)])  # Initialize the queue

    def fetch_entity_relationships(self, entity_id: str) -> Dict[str, List[Dict[str, Any]]]:
        headers = {"Content-Type": "application/json", "API-Key": self.api_key}
        query = self.build_graphql_query(entity_id)
        response = requests.post(self.endpoint, headers=headers, json=query)

        if response.status_code == 200:
            return self.parse_graphql_response(response.json())
        else:
            logger.error(f"Failed to fetch relationships for entity {entity_id}: {response.status_code}")
            return {}

    def build_graphql_query(self, entity_id: str) -> Dict[str, str]:
        query = {
            "query": f"""
            {{
            actor {{
                entity(guid: "{entity_id}") {{
                name
                relatedEntities {{
                    results {{
                    source {{
                        entity {{
                        guid
                        name
                        type
                        }}
                    }}
                    target {{
                        entity {{
                        guid
                        name
                        type
                        }}
                    }}
                    type
                    }}
                }}
                }}
            }}
            }}
            """
        }
        return query

    def parse_graphql_response(self, response_json: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        entity_relationships = {}
        try:
            results = response_json["data"]["actor"]["entity"]["relatedEntities"]["results"]
            for relationship in results:
                source_id = relationship["source"]["entity"]["guid"]
                if source_id not in entity_relationships:
                    entity_relationships[source_id] = []
                entity_relationships[source_id].append(relationship)
        except KeyError:
            logger.warning("Unexpected response structure")
        return entity_relationships

    def determine_icon(self, name: str) -> str:
        if "redis" in name.lower():
            return self.icons["redis"]
        elif "rds" in name.lower() or "database" in name.lower():
            return self.icons["database"]
        return self.icons["default"]

    def add_node(self, entity: Dict[str, Any]):
        if entity and not any(node["id"] == entity["guid"] for node in self.topology["nodes"]):
            self.topology["nodes"].append({
                "id": entity["guid"],
                "name": entity["name"],
                "group": 1,
                "icon": self.determine_icon(entity["name"]),
                "additionalInfo": "Entity in the network",
                "issue": entity["guid"] == self.entity_id,
            })

    def add_link(self, source_id: str, target_id: str):
        if not any(link["source"] == source_id and link["target"] == target_id for link in self.topology["links"]):
            self.topology["links"].append({
                "source": source_id,
                "target": target_id,
                "value": 1,
            })

    def process_entity_relationships(self, entity_id: str, current_depth: int):
        if current_depth > self.depth or entity_id in self.processed_entities:
            logger.info(f"Skipping entity: {entity_id}")
            return

        self.processed_entities.add(entity_id)

        # Fetch relationships if they are not already in entity_relationships
        entity_relationships = self.fetch_entity_relationships(entity_id)
        
        # Check if new relationships are fetched and add them to the processing queue
        if entity_id not in entity_relationships:
            entity_relationships = self.fetch_entity_relationships(entity_id)
            self.expand_entities_to_process(entity_relationships, current_depth + 1)
        
        if entity_id not in entity_relationships:
            return

        for relationship in entity_relationships.get(entity_id, []):
            source = relationship.get("source", {}).get("entity")
            target = relationship.get("target", {}).get("entity")

            if source and target:
                self.add_node(source)
                self.add_node(target)
                self.add_link(source["guid"], target["guid"])
                logger.info(f"Added link: {source['name']} -> {target['name']}")

                # Update the processing queue with the target entity's relationships
                self.expand_entities_to_process({target["guid"]: [relationship]}, current_depth + 1)

    def expand_entities_to_process(self, entity_relationships: Dict[str, List[Dict[str, Any]]], depth: int):
        """
        Adds new entities to the processing queue based on the fetched relationships.
        """
        for entity_id in entity_relationships.keys():
            if entity_id not in self.processed_entities:
                self.entities_to_process.append((entity_id, depth))

    def generate(self):
        while self.entities_to_process:
            entity_id, current_depth = self.entities_to_process.popleft()
            self.process_entity_relationships(entity_id, current_depth)

        data = {"nodes": self.topology["nodes"], "links": self.topology["links"]}
        return data

        # with open("topology.json", "w") as outfile:
        #     json.dump(data, outfile)
        #     logger.info("Topology data saved to topology.json")

        # response = requests.post("http://localhost:8005/data", json=data)
        # if response.status_code == 200:
        #     logger.info(f"Topology diagram successfully generated: {response.text}")
        #     return response.text
        # else:
        #     logger.error(f"Failed to generate topology diagram: {response.status_code}")
        #     return f"Error in generating topology diagram\n{response.text}"

# Example of how to use this class
if __name__ == "__main__":
    generator = TopologyGenerator(entity_id="MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAyNDk5MTQyMg")
    result = generator.generate()
    print(result)
