import json
from typing import Dict, List, Optional, Union
from enum import Enum
from datetime import datetime
import uuid
import re
from gremlin_python.driver import client, serializer
from gremlin_python.driver.protocol import GremlinServerError

class DataSource(Enum):
    STATIC = "static"
    NEW_RELIC = "new_relic"

class ComponentType(Enum):
    KUBERNETES_CLUSTER = "kubernetes_cluster"
    NAMESPACE = "namespace"
    POD = "pod"
    DATABASE = "database"
    CACHE = "cache"
    LOAD_BALANCER = "load_balancer"
    API_SERVER = "api_server"
    STORAGE = "storage"
    KAFKA = "kafka"
    SEARCH = "search"

class Component:
    def __init__(
        self,
        id: str,
        name: str,
        component_type: ComponentType,
        namespace: Optional[str] = None,
        properties: Dict[str, any] = None,
        alt_names: List[str] = None,
        cluster_properties: Dict[str, Dict[str, any]] = None,
        group: int = 1,
        icon: str = "",
        additional_info: str = "",
        issue: bool = False
    ):
        self.id = id
        self.name = name
        self.component_type = component_type
        self.namespace = namespace
        self.properties = properties or {}
        self.alt_names = alt_names or []
        self.cluster_properties = cluster_properties or {}
        self.group = group
        self.icon = icon
        self.additional_info = additional_info
        self.issue = issue
        self.data_source = DataSource.STATIC
        self.created_at = datetime.utcnow()
        self.updated_at = self.created_at

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "component_type": self.component_type.value,
            "namespace": self.namespace,
            "properties": self.properties,
            "alt_names": self.alt_names,
            "cluster_properties": self.cluster_properties,
            "group": self.group,
            "icon": self.icon,
            "additionalInfo": self.additional_info,
            "issue": self.issue,
            "data_source": self.data_source.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            name=data["name"],
            component_type=ComponentType(data["component_type"]),
            namespace=data.get("namespace"),
            properties=data.get("properties", {}),
            alt_names=data.get("alt_names", []),
            cluster_properties=data.get("cluster_properties", {}),
            group=data.get("group", 1),
            icon=data.get("icon", ""),
            additional_info=data.get("additionalInfo", ""),
            issue=data.get("issue", False)
        )

    def to_frontend_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "group": self.group,
            "icon": self.icon,
            "additionalInfo": self.additional_info,
            "issue": self.issue
        }

class Connection:
    def __init__(
        self,
        id: str,
        source_id: str,
        target_id: str,
        connection_type: str,
        properties: Dict[str, any] = None,
        value: int = 1
    ):
        self.id = id
        self.source_id = source_id
        self.target_id = target_id
        self.connection_type = connection_type
        self.properties = properties or {}
        self.value = value
        self.data_source = DataSource.STATIC
        self.created_at = datetime.utcnow()
        self.updated_at = self.created_at

    def to_dict(self):
        return {
            "id": self.id,
            "source_id": self.source_id,
            "target_id": self.target_id,
            "connection_type": self.connection_type,
            "properties": self.properties,
            "value": self.value,
            "data_source": self.data_source.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            source_id=data["source_id"],
            target_id=data["target_id"],
            connection_type=data["connection_type"],
            properties=data.get("properties", {}),
            value=data.get("value", 1)
        )

    def to_frontend_dict(self):
        return {
            "source": self.source_id,
            "target": self.target_id,
            "value": self.value
        }

class ServiceArchitecture:
    def __init__(self):
        self.components: Dict[str, Component] = {}
        self.connections: List[Connection] = []

    def add_component(self, component: Component):
        self.components[component.id] = component
        return component.id

    def add_connection(self, connection: Connection):
        self.connections.append(connection)

    def to_dict(self):
        return {
            "components": [component.to_dict() for component in self.components.values()],
            "connections": [connection.to_dict() for connection in self.connections]
        }

    @classmethod
    def from_dict(cls, data):
        architecture = cls()
        for component_data in data["components"]:
            component = Component.from_dict(component_data)
            architecture.add_component(component)
        for connection_data in data["connections"]:
            connection = Connection.from_dict(connection_data)
            architecture.add_connection(connection)
        return architecture

    def to_frontend_dict(self, start_component_id: Optional[str] = None):
        nodes = []
        links = []
        for component in self.components.values():
            node_dict = component.to_frontend_dict()
            if component.id == start_component_id:
                node_dict["issue"] = True
            nodes.append(node_dict)
        for connection in self.connections:
            links.append(connection.to_frontend_dict())
        return {"nodes": nodes, "links": links}

class CosmosDBGraphManager:
    def __init__(self, config):
        self.config = config
        self.client = client.Client(
            f'wss://{config["endpoint"]}:443/',
            'g',
            username=config['username'],
            password=config['password'],
            message_serializer=serializer.GraphSONSerializersV2d0()
        )

    async def create_vertex(self, label: str, properties: Dict[str, Any]):
        query = f"g.addV('{label}')"
        for key, value in properties.items():
            if isinstance(value, (str, int, bool)):
                query += f".property('{key}', {json.dumps(value)})"
            else:
                query += f".property('{key}', {json.dumps(json.dumps(value))})"
        return await self.execute_query(query)

    async def create_edge(self, label: str, from_id: str, to_id: str, properties: Dict[str, Any]):
        query = f"g.V('{from_id}').addE('{label}').to(g.V('{to_id}')"
        for key, value in properties.items():
            if isinstance(value, (str, int, bool)):
                query += f".property('{key}', {json.dumps(value)})"
            else:
                query += f".property('{key}', {json.dumps(json.dumps(value))})"
        query += ")"
        return await self.execute_query(query)

    async def get_topology(self, start_component_id: str, max_depth: int = 3):
        query = f"""
        g.V('{start_component_id}').repeat(bothE().otherV().simplePath()).times({max_depth}).path().
          by(project('id', 'name', 'group', 'icon', 'additionalInfo', 'issue').
             by(id).by('name').by('group').by('icon').by('additionalInfo').by('issue')).
          by(project('source', 'target', 'value').
             by(outV().id()).by(inV().id()).by('value'))
        """
        result = await self.execute_query(query)
        nodes = set()
        links = set()
        for path in result:
            for i, element in enumerate(path.objects):
                if i % 2 == 0:  # It's a vertex
                    node = element
                    node['issue'] = node['issue'] or (node['id'] == start_component_id)
                    nodes.add(json.dumps(node))
                else:  # It's an edge
                    links.add(json.dumps(element))
        return {
            "nodes": [json.loads(node) for node in nodes],
            "links": [json.loads(link) for link in links]
        }

    async def execute_query(self, query):
        print(f"Executing Gremlin query: {query}")
        try:
            return await self.client.submit(query)
        except GremlinServerError as e:
            print(f'Error: {e}')
            raise

    async def close(self):
        await self.client.close()

async def create_graph_from_architecture(architecture: ServiceArchitecture, graph_manager: CosmosDBGraphManager):
    for component in architecture.components.values():
        await graph_manager.create_vertex('Component', component.to_dict())
    
    for connection in architecture.connections:
        await graph_manager.create_edge('Connects', connection.source_id, connection.target_id, connection.to_dict())

# Configuration
config = {
    'endpoint': 'YOUR_COSMOS_DB_ENDPOINT',
    'username': '/dbs/YOUR_DATABASE/colls/YOUR_GRAPH_CONTAINER',
    'password': 'YOUR_COSMOS_DB_KEY',
    'graph_name': 'YOUR_GRAPH_NAME'
}

async def main():
    # Create initial architecture
    architecture = ServiceArchitecture()
    
    # Add components and connections from the provided JSON
    # (You'll need to add more components and connections based on your full data)
    aks_cluster = Component(
        id="1",
        name="MDM AKS Cluster",
        component_type=ComponentType.KUBERNETES_CLUSTER,
        alt_names=["AKS-*", "Kubernetes-Cluster-*"],
        cluster_properties={
            "cluster1": {"location": "East US", "node_count": 5},
            "cluster2": {"location": "West US", "node_count": 3},
            "cluster3": {"location": "Central US", "node_count": 4}
        }
    )
    architecture.add_component(aks_cluster)
    
    kong_namespaces = Component(
        id="2",
        name="Kong Namespaces",
        component_type=ComponentType.NAMESPACE,
        namespace="kong",
        alt_names=["kong-*"],
        cluster_properties={
            "cluster1": {"replicas": 3},
            "cluster2": {"replicas": 2},
            "cluster3": {"replicas": 2}
        }
    )
    architecture.add_component(kong_namespaces)
    
    connection = Connection(
        id="conn1",
        source_id="1",
        target_id="2",
        connection_type="contains"
    )
    architecture.add_connection(connection)

    # Initialize the CosmosDBGraphManager
    graph_manager = CosmosDBGraphManager(config)

    try:
        # Create the graph from the architecture
        await create_graph_from_architecture(architecture, graph_manager)
        print("Graph created successfully in Azure Cosmos DB")

        # Retrieve topology starting from a specific component
        start_component_id = "1"  # MDM AKS Cluster
        topology = await graph_manager.get_topology(start_component_id)
        print("Retrieved topology:")
        print(json.dumps(topology, indent=2))

    except Exception as e:
        print(f"An error occurred: {e}")

    finally:
        # Close the connection
        await graph_manager.close()

if __name__ == "__main__":
    import asyncio
    asyncio.get_event_loop().run_until_complete(main())