{"nodes": [{"id": "MTA5MzYyMHxJTkZSQXxOQXwtMjEyMDk1OTIzMjM3NDkxNzk1NQ", "name": "login-app-message-handler-service-75ddd95869-5wxdn", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-message-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-message-handler-service-75ddd95869"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pv"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-5wxdn"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMjEyMDk1OTIzMjM3NDkxNzk1NQ", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMjY3MTU4NDgzNDYyODYxNjM1", "name": "data-services-query-api-facade-deployment-54f85866b7-8jgd5", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["data-services-query-api-facade-deployment-54f85866b7"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["data-services-query-api-facade-deployment"], "k8s.namespaceName": ["data-services"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["data-services-query-api-facade-deployment-54f85866b7-8jgd5"], "k8s.replicasetName": ["data-services-query-api-facade-deployment-54f85866b7"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["54f85866b7"], "service-pod-link": ["data-services-query-api-facade-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMjY3MTU4NDgzNDYyODYxNjM1", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4OTA1NTE3MjI0MDI1MzQ4NDYx", "name": "users-kafka-consumer", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["users-kafka-consumer"], "k8s.namespaceName": ["nalp"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4OTA1NTE3MjI0MDI1MzQ4NDYx", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNzcxODU1MDMyNjE2MDI4NjIwMA", "name": "kubelet", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app.kubernetes.io/component": ["kubelet"], "app.kubernetes.io/instance": ["newrelic-bundle"], "app.kubernetes.io/name": ["newrelic-infrastructure"], "container.state": ["Running"], "controller-revision-hash": ["69c459c7b6"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["028cca05d695c8b60150c87028c87a2c5dfd46a03be115682180d2aab5d655cb"], "k8s.containerImage": ["newrelic/nri-kubernetes:3.18.3"], "k8s.containerName": ["kubelet"], "k8s.daemonsetName": ["newrelic-bundle-nrk8s-kubelet"], "k8s.namespaceName": ["ns-core"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["newrelic-bundle-nrk8s-kubelet-7tzvj"], "k8s.status": ["Running"], "mode": ["privileged"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-generation": ["5"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNzcxODU1MDMyNjE2MDI4NjIwMA", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNDkwOTQzNDU2OTk3ODY3OTA1NQ", "name": "dataservices-dataimport-monitor-service-container-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["3f26e602410c21670adae6eedf0ad20b6b8fcef6ee5f840dac75e1e4f01300b8"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/dataservices-dataimport/dataservices.dataimport.monitor.service:1905069"], "k8s.containerName": ["dataservices-dataimport-monitor-service-container-service"], "k8s.deploymentName": ["dataservices-dataimport-monitor-service-deployment"], "k8s.namespaceName": ["dataservices-dataimport"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["dataservices-dataimport-monitor-service-deployment-9c8fc85jhmh7"], "k8s.replicasetName": ["dataservices-dataimport-monitor-service-deployment-9c8fc859d"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["9c8fc859d"], "service-pod-link": ["dataservices-dataimport-monitor-service-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNDkwOTQzNDU2OTk3ODY3OTA1NQ", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwxNzM0MjcyNDgzOTIxNDAwMDA1", "name": "login-app-message-handler-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["login-app-message-handler-service"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["84a27777577f6c83203135f87218d978bdda9afe91ff1dffa10c7417511ac912"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/login-app/login-app-message-handler-service:1922913"], "k8s.containerName": ["login-app-message-handler-service"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-gp2w5"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwxNzM0MjcyNDgzOTIxNDAwMDA1", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODE5OTYwOTAzOTcxMzQyOTE3MQ", "name": "newrelic-bundle-newrelic-logging", "issue": false, "group": 1, "icon": "static/icons/svc.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DAEMONSET", "app": ["newrelic-logging"], "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "app_kubernetes_io_name": ["newrelic-logging"], "chart": ["newrelic-logging-1.10.11", "newrelic-logging-1.11.1"], "daemonset": ["newrelic-bundle-newrelic-logging"], "heritage": ["<PERSON><PERSON>"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.daemonsetName": ["newrelic-bundle-newrelic-logging"], "k8s.namespaceName": ["ns-core"], "namespace": ["ns-core"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.2.0", "v3.4.0", "v3.6.0"], "release": ["newrelic-bundle"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtODE5OTYwOTAzOTcxMzQyOTE3MQ", "type": "KUBERNETES_DAEMONSET", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNjI5NDY4MDcxMjQwOTg5NTE0OA", "name": "users-kafka-consumer-65bdc77-bw66t", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["users-kafka-consumer"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["users-kafka-consumer-65bdc77"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["users-kafka-consumer"], "k8s.namespaceName": ["nalp"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["users-kafka-consumer-65bdc77-bw66t"], "k8s.replicasetName": ["users-kafka-consumer-65bdc77"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["65bdc77"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNjI5NDY4MDcxMjQwOTg5NTE0OA", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNjM5MTg5NTgzNzQwMTU0MzYzMQ", "name": "connector-48f30bbc-15e2-478b-bfb9-03cf4ebbfed2-476d1231-ee4c-4fe8-a264-ac040c723abd", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "RecId": ["476d1231-ee4c-4fe8-a264-ac040c723abd"], "TenantId": ["48f30bbc-15e2-478b-bfb9-03cf4ebbfed2"], "environment-customerdata": ["no"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.namespaceName": ["connectors"], "k8s.nodeName": ["aks-default-********-vmss0007pt", "aks-default-********-vmss0007pv", "aks-default-********-vmss0007pf", "aks-default-********-vmss0007pg", "aks-default-********-vmss0007pm", "aks-default-********-vmss0006to", "aks-default-********-vmss0007jk"], "k8s.podName": ["connector-48f30bbc-15e2-478b-bfb9-03cf4ebbfed2-476d1231-ee4c-4fe8-a264-ac040c723abd"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "product-name": ["ivantineuronsdiscovery"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNjM5MTg5NTgzNzQwMTU0MzYzMQ", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzNTMzMTUwMzE2ODYxODI5MzU0", "name": "login-app-admin-service-8b4c5677b-xqhcd", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-admin-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-admin-service-8b4c5677b"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-admin-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["login-app-admin-service-8b4c5677b-xqhcd"], "k8s.replicasetName": ["login-app-admin-service-8b4c5677b"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["8b4c5677b"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzNTMzMTUwMzE2ODYxODI5MzU0", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTU0MTQ5NzUyNDUxNjU1OTA0", "name": "data-services-query-api-deployment-5d744ff7d9-hhnsz", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["data-services-query-api-deployment-5d744ff7d9"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["data-services-query-api-deployment"], "k8s.namespaceName": ["data-services"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["data-services-query-api-deployment-5d744ff7d9-hhnsz"], "k8s.replicasetName": ["data-services-query-api-deployment-5d744ff7d9"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["5d744ff7d9"], "service-pod-link": ["data-services-query-api-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTU0MTQ5NzUyNDUxNjU1OTA0", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2NjU4ODI0Njc2Njg4NTc5NzMw", "name": "newrelic-bundle-nrk8s-kubelet", "issue": false, "group": 1, "icon": "static/icons/svc.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DAEMONSET", "app_kubernetes_io_instance": ["newrelic-bundle"], "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "app_kubernetes_io_name": ["newrelic-infrastructure"], "app_kubernetes_io_version": ["3.2.0", "3.4.0"], "daemonset": ["newrelic-bundle-nrk8s-kubelet"], "helm_sh_chart": ["newrelic-infrastructure-3.7.0", "newrelic-infrastructure-3.5.1"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.daemonsetName": ["newrelic-bundle-nrk8s-kubelet"], "k8s.namespaceName": ["ns-core"], "mode": ["privileged"], "namespace": ["ns-core"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.2.0", "v3.4.0", "v3.6.0"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2NjU4ODI0Njc2Njg4NTc5NzMw", "type": "KUBERNETES_DAEMONSET", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMzY1NjI2MDc1MzE3Mjk1OTU5Mg", "name": "smartadvisors-service-container-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["0033bc6adb906258e5a73cc9394a867eb60742d8fd07c7140ca25fd2645f4104"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/smartadvisors/smartadvisors.service:1786408"], "k8s.containerName": ["smartadvisors-service-container-service"], "k8s.deploymentName": ["smartadvisors-service-deployment"], "k8s.namespaceName": ["smartadvisors"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["smartadvisors-service-deployment-b8b969b96-4h2ps"], "k8s.replicasetName": ["smartadvisors-service-deployment-b8b969b96"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["b8b969b96"], "service-pod-link": ["smartadvisors-service-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMzY1NjI2MDc1MzE3Mjk1OTU5Mg", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyMTkzNDc5NTYzMDI2MTc4MDE1", "name": "twistlock-defender", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["twistlock-defender"], "container.state": ["Running"], "controller-revision-hash": ["6cf65857d"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["5d8fd0fcfd6734614b6560e2f64d76f29cf06dff1c75db2226bdd0728f800a2f"], "k8s.containerImage": ["registry-auth.twistlock.com/tw_xte7xofo9nhi0kd6ahxzlg3dvgyfnszu/twistlock/defender:defender_32_04_112"], "k8s.containerName": ["twistlock-defender"], "k8s.daemonsetName": ["twistlock-defender-ds"], "k8s.namespaceName": ["twistlock"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["twistlock-defender-ds-lzjbk"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-generation": ["5"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwyMTkzNDc5NTYzMDI2MTc4MDE1", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTMyODUzMTI1MjY2NTc5MTQyNQ", "name": "login-app-message-handler-service-75ddd95869-lx2wk", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-message-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-message-handler-service-75ddd95869"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pg"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-lx2wk"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTMyODUzMTI1MjY2NTc5MTQyNQ", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2NDcxMTM5NzMxNjkzMzA2NjA", "name": "automation-management-webhost", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["automation-management-webhost"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["1647213f03a42a263bbc073f43b850abbf41f36acfa14585fca285553d9d77ca"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/automation-management/automation-management-webhost:1918689"], "k8s.containerName": ["automation-management-webhost"], "k8s.deploymentName": ["automation-management-webhost-deployment"], "k8s.namespaceName": ["automation-management"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["automation-management-webhost-deployment-fb889f59d-wp5lj"], "k8s.replicasetName": ["automation-management-webhost-deployment-fb889f59d"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["fb889f59d"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2NDcxMTM5NzMxNjkzMzA2NjA", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMTE4NDk5MDU2ODAyMzI0MjIyOQ", "name": "connectorapi", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "deployment": ["connectorapi"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["connectorapi"], "k8s.namespaceName": ["connector"], "kubernetes_io_metadata_name": ["connector"], "namespace": ["connector"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.4.0", "v3.6.0"], "service_pod_link": ["connectorapi-selector"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMTE4NDk5MDU2ODAyMzI0MjIyOQ", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4Nzg3MjI5NzA4MjczNDIwMzg4", "name": "login-app-message-handler-service-75ddd95869-tw6l6", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-message-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-message-handler-service-75ddd95869"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007qw"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-tw6l6"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4Nzg3MjI5NzA4MjczNDIwMzg4", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "************************************************", "name": "uwm-application-webapi-784b78fbd9-v8g28", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["uwm-application-webapi"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["uwm-application-webapi-784b78fbd9"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["uwm-application-webapi"], "k8s.namespaceName": ["uwm-application"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["uwm-application-webapi-784b78fbd9-v8g28"], "k8s.replicasetName": ["uwm-application-webapi-784b78fbd9"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["784b78fbd9"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "************************************************", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4MjQ2ODE5MTg3MjA2NDAzMzEw", "name": "collector-engine", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "RecId": ["382de0ba-5608-41ff-9400-0ee3f8ab2300"], "TenantId": ["747e91ba-39ad-464e-90fc-8c1b5439405f"], "container.state": ["Running"], "environment-customerdata": ["no"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["64df613600fdcd18cbd3761ece31d027dd9bf872d7c728adee553d8169b4a4b2"], "k8s.containerImage": ["teamphantomcr.azurecr.io/collector.engine2.legacy:latest"], "k8s.containerName": ["collector-engine"], "k8s.namespaceName": ["connectors"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["connector-747e91ba-39ad-464e-90fc-8c1b5439405f-382de0ba-5608-41ff-9400-0ee3f8ab2300"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "product-name": ["ivantineuronsdiscovery"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4MjQ2ODE5MTg3MjA2NDAzMzEw", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNDY3MDI4MzkxNjMwNTM3OTg3NA", "name": "smartadvisors-service-deployment-b8b969b96-4h2ps", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["smartadvisors-service-deployment-b8b969b96"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["smartadvisors-service-deployment"], "k8s.namespaceName": ["smartadvisors"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["smartadvisors-service-deployment-b8b969b96-4h2ps"], "k8s.replicasetName": ["smartadvisors-service-deployment-b8b969b96"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["b8b969b96"], "service-pod-link": ["smartadvisors-service-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNDY3MDI4MzkxNjMwNTM3OTg3NA", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMTc2NjExMzgwODY3NTA5Mzc3Ng", "name": "login-app-login-service-agent-only", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "app": ["login-app-login-service"], "appWhenSplit": ["login-app-login-service-agent-only"], "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "deployment": ["login-app-login-service-agent-only"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["login-app-login-service-agent-only"], "k8s.namespaceName": ["login-app"], "kubernetes_io_metadata_name": ["login-app"], "namespace": ["login-app"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.2.0", "v3.4.0", "v3.6.0"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMTc2NjExMzgwODY3NTA5Mzc3Ng", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4Njc5OTU0ODkwMjUzOTU2OTg1", "name": "device-discovery-engine-webapi-container-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["c8dba8187e6c67898e788689b27827d18c63f304bf657a359cda7a37e1790d64"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/device-discovery-engine-webapi/devicediscoveryenginewebapi:1917470"], "k8s.containerName": ["device-discovery-engine-webapi-container-service"], "k8s.deploymentName": ["device-discovery-engine-webapi-app"], "k8s.namespaceName": ["device-discovery-engine-webapi"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["device-discovery-engine-webapi-app-bdf5c7db7-x5fr6"], "k8s.replicasetName": ["device-discovery-engine-webapi-app-bdf5c7db7"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["bdf5c7db7"], "service-pod-link": ["device-discovery-engine-webapi-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4Njc5OTU0ODkwMjUzOTU2OTg1", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MjUzNjYwNzIwNjk0NzkwMjIy", "name": "newrelic-logging", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["newrelic-logging"], "app.kubernetes.io/name": ["newrelic-logging"], "container.state": ["Running"], "controller-revision-hash": ["58685d46cd"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["26d656cbb4425222fcb185897d6160cc2082ce1c5a5bb050fc06575e2aac7776"], "k8s.containerImage": ["newrelic/newrelic-fluentbit-output:1.17.3"], "k8s.containerName": ["newrelic-logging"], "k8s.daemonsetName": ["newrelic-bundle-newrelic-logging"], "k8s.namespaceName": ["ns-core"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["newrelic-bundle-newrelic-logging-k4bwh"], "k8s.status": ["Running"], "kubernetes.io/os": ["linux"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-generation": ["6"], "release": ["newrelic-bundle"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2MjUzNjYwNzIwNjk0NzkwMjIy", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyMTExNjU0OTk5NjUyMDk3Mzk4", "name": "dataservices-dataimport-monitor-service-deployment-9c8fc85jhmh7", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["dataservices-dataimport-monitor-service-deployment-9c8fc859d"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["dataservices-dataimport-monitor-service-deployment"], "k8s.namespaceName": ["dataservices-dataimport"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["dataservices-dataimport-monitor-service-deployment-9c8fc85jhmh7"], "k8s.replicasetName": ["dataservices-dataimport-monitor-service-deployment-9c8fc859d"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["9c8fc859d"], "service-pod-link": ["dataservices-dataimport-monitor-service-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwyMTExNjU0OTk5NjUyMDk3Mzk4", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw0NTcwMzYxNTE2MTk0ODU4MDM4", "name": "agent", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app.kubernetes.io/component": ["kubelet"], "app.kubernetes.io/instance": ["newrelic-bundle"], "app.kubernetes.io/name": ["newrelic-infrastructure"], "container.state": ["Running"], "controller-revision-hash": ["69c459c7b6"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["662ee09868992f9d9303b77debfe590015c5baa96c9c4c8019671aa0a659d9b0"], "k8s.containerImage": ["newrelic/infrastructure-bundle:3.2.16"], "k8s.containerName": ["agent"], "k8s.daemonsetName": ["newrelic-bundle-nrk8s-kubelet"], "k8s.namespaceName": ["ns-core"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["newrelic-bundle-nrk8s-kubelet-7tzvj"], "k8s.status": ["Running"], "mode": ["privileged"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-generation": ["5"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw0NTcwMzYxNTE2MTk0ODU4MDM4", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2NTY3MjQzNDk4NjkxODI5NTA0", "name": "ism-sync-handler-service-deployment-646bcdc944-4rv68", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["ism-sync-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["ism-sync-handler-service-deployment-646bcdc944"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["ism-sync-handler-service-deployment"], "k8s.namespaceName": ["ism-sync"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["ism-sync-handler-service-deployment-646bcdc944-4rv68"], "k8s.replicasetName": ["ism-sync-handler-service-deployment-646bcdc944"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["646bcdc944"], "service-pod-link": ["ism-sync-handler-service-selector"], "trustedAccountId": ["1093620"], "version": ["1865100"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2NTY3MjQzNDk4NjkxODI5NTA0", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzNTc1ODUxODk5ODY4OTk2MjA", "name": "login-app-message-handler-service-75ddd95869-77b7h", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-message-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-message-handler-service-75ddd95869"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-77b7h"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzNTc1ODUxODk5ODY4OTk2MjA", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw3NTc4MDc4Nzc0ODA0NTk2NDQ2", "name": "automation-management-webhost-deployment", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "app": ["automation-management-webhost-deploy"], "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "deployment": ["automation-management-webhost-deployment"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["automation-management-webhost-deployment"], "k8s.namespaceName": ["automation-management"], "kubernetes_io_metadata_name": ["automation-management"], "namespace": ["automation-management"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.4.0", "v3.6.0"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw3NTc4MDc4Nzc0ODA0NTk2NDQ2", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTI0MTI4MjY5OTY4NTM3ODI3Mg", "name": "uwm-application-webapi", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "deployment": ["uwm-application-webapi"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["uwm-application-webapi"], "k8s.namespaceName": ["uwm-application"], "namespace": ["uwm-application"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.6.0"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTI0MTI4MjY5OTY4NTM3ODI3Mg", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw0MjE4NTc2NjI0NjgyNDQ3NTA1", "name": "login-app-message-handler-service-75ddd95869-8nh7j", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-message-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-message-handler-service-75ddd95869"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007qw"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-8nh7j"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw0MjE4NTc2NjI0NjgyNDQ3NTA1", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyMzE3NDU4NzUyMTA3NzQwMzcx", "name": "query-api-facade", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["e78c6c16f64e5ffac2e01171fe4cd59c9bc23eeb2722a8d09e9dfe2a83457442"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/data-services.queryapifacade/data-services.queryapifacade:1866014"], "k8s.containerName": ["query-api-facade"], "k8s.deploymentName": ["data-services-query-api-facade-deployment"], "k8s.namespaceName": ["data-services"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["data-services-query-api-facade-deployment-54f85866b7-t2zk5"], "k8s.replicasetName": ["data-services-query-api-facade-deployment-54f85866b7"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["54f85866b7"], "service-pod-link": ["data-services-query-api-facade-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwyMzE3NDU4NzUyMTA3NzQwMzcx", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzNDIxNzI0ODc1NjYwNjEyNzg3", "name": "login-app-message-handler-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["login-app-message-handler-service"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["320ed524baea09e0fffcd1883938c1aa5818747b60c6edb69b8fb46c5a7fca60"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/login-app/login-app-message-handler-service:1922913"], "k8s.containerName": ["login-app-message-handler-service"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-bnjhw"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzNDIxNzI0ODc1NjYwNjEyNzg3", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4MjUxNjU2Mjk3MjU4MzAzMDIw", "name": "login-app-admin-service", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "app": ["login-app-admin-service"], "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "deployment": ["login-app-admin-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["login-app-admin-service"], "k8s.namespaceName": ["login-app"], "kubernetes_io_metadata_name": ["login-app"], "namespace": ["login-app"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.2.0", "v3.4.0", "v3.6.0"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4MjUxNjU2Mjk3MjU4MzAzMDIw", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "name": "login-app-message-handler-service", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "app": ["login-app-message-handler-service"], "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "deployment": ["login-app-message-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "kubernetes_io_metadata_name": ["login-app"], "namespace": ["login-app"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.2.0", "v3.4.0", "v3.6.0"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4Njg3Mzk1MDU4Nzg3MjI5Mzk3", "name": "device-discovery-engine-webapi-app-bdf5c7db7-x5fr6", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["device-discovery-engine-webapi-app-bdf5c7db7"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["device-discovery-engine-webapi-app"], "k8s.namespaceName": ["device-discovery-engine-webapi"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["device-discovery-engine-webapi-app-bdf5c7db7-x5fr6"], "k8s.replicasetName": ["device-discovery-engine-webapi-app-bdf5c7db7"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["bdf5c7db7"], "service-pod-link": ["device-discovery-engine-webapi-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4Njg3Mzk1MDU4Nzg3MjI5Mzk3", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNDUyNzA4NTU4MDE4MTAxMTE4Mw", "name": "login-app-message-handler-service-75ddd95869-bcnrz", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-message-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-message-handler-service-75ddd95869"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-bcnrz"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNDUyNzA4NTU4MDE4MTAxMTE4Mw", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4MzMxOTc5Nzk0Njg5MDQ4MDM5", "name": "spend-intelligence-webclient-5bfd5c5bc6-7spxx", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["spend-intelligence-webclient"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["spend-intelligence-webclient-5bfd5c5bc6"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["spend-intelligence-webclient"], "k8s.namespaceName": ["spend-intelligence-webclient"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["spend-intelligence-webclient-5bfd5c5bc6-7spxx"], "k8s.replicasetName": ["spend-intelligence-webclient-5bfd5c5bc6"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["5bfd5c5bc6"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4MzMxOTc5Nzk0Njg5MDQ4MDM5", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMTExNDQ4NDMwMTI1MzA2MzgwMw", "name": "patch-content-public-api-container", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "app": ["patch-content-public-api-container"], "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "deployment": ["patch-content-public-api-container"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["patch-content-public-api-container"], "k8s.namespaceName": ["patch-content"], "kubernetes_io_metadata_name": ["patch-content"], "namespace": ["patch-content"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.4.0", "v3.6.0"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMTExNDQ4NDMwMTI1MzA2MzgwMw", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2NjE5OTYzNDgxNTc4NDI2MDk4", "name": "collector-engine", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "RecId": ["476d1231-ee4c-4fe8-a264-ac040c723abd"], "TenantId": ["48f30bbc-15e2-478b-bfb9-03cf4ebbfed2"], "container.state": ["Running"], "environment-customerdata": ["no"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["c058553d825df78dc4085d8c6042a0bdfd67c592e776382a43c8eca151cb78c0"], "k8s.containerImage": ["teamphantomcr.azurecr.io/collector.engine2.legacy:latest"], "k8s.containerName": ["collector-engine"], "k8s.namespaceName": ["connectors"], "k8s.nodeName": ["aks-default-********-vmss0007pt", "aks-default-********-vmss0007pf", "aks-default-********-vmss0007pv", "aks-default-********-vmss0007pg", "aks-default-********-vmss0007pm", "aks-default-********-vmss0006to", "aks-default-********-vmss0007jk"], "k8s.podName": ["connector-48f30bbc-15e2-478b-bfb9-03cf4ebbfed2-476d1231-ee4c-4fe8-a264-ac040c723abd"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "product-name": ["ivantineuronsdiscovery"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2NjE5OTYzNDgxNTc4NDI2MDk4", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4OTk5NTg4ODgxODc5Mjk3NzI0", "name": "query-api-facade", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["d7af08c08060efd5daec9a7bd7e07defcc7337108dc09f0c6d9102985ea263be"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/data-services.queryapifacade/data-services.queryapifacade:1866014"], "k8s.containerName": ["query-api-facade"], "k8s.deploymentName": ["data-services-query-api-facade-deployment"], "k8s.namespaceName": ["data-services"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["data-services-query-api-facade-deployment-54f85866b7-8jgd5"], "k8s.replicasetName": ["data-services-query-api-facade-deployment-54f85866b7"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["54f85866b7"], "service-pod-link": ["data-services-query-api-facade-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4OTk5NTg4ODgxODc5Mjk3NzI0", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMTkxMDc0MzY5Nzk4MDc5ODIxMA", "name": "query-api", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["642755566ba044c8a507822829909c0488f1ff80904198b1af090165aba6c946"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/data-services.queryapi/data-services.queryapi:1920399"], "k8s.containerName": ["query-api"], "k8s.deploymentName": ["data-services-query-api-deployment"], "k8s.namespaceName": ["data-services"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["data-services-query-api-deployment-5d744ff7d9-sdlm7"], "k8s.replicasetName": ["data-services-query-api-deployment-5d744ff7d9"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["5d744ff7d9"], "service-pod-link": ["data-services-query-api-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMTkxMDc0MzY5Nzk4MDc5ODIxMA", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw3NDQ2ODI0NDk2NDQ1ODQzNTk0", "name": "smartadvisors-service-deployment", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "deployment": ["smartadvisors-service-deployment"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["smartadvisors-service-deployment"], "k8s.namespaceName": ["smartadvisors"], "namespace": ["smartadvisors"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.6.0"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw3NDQ2ODI0NDk2NDQ1ODQzNTk0", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMTEzOTk1OTkwMDA4MDY4MjIy", "name": "device-discovery-engine-webapi-app", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "app": ["device-discovery-engine-webapi-app"], "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "deployment": ["device-discovery-engine-webapi-app"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["device-discovery-engine-webapi-app"], "k8s.namespaceName": ["device-discovery-engine-webapi"], "kubernetes_io_metadata_name": ["device-discovery-engine-webapi"], "namespace": ["device-discovery-engine-webapi"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.4.0", "v3.6.0"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzMTEzOTk1OTkwMDA4MDY4MjIy", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTU5MTE0OTg4MTAyNjA5NzgzMQ", "name": "users-kafka-consumer", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["users-kafka-consumer"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["febe6ea9c7575565efa7d4781ce53d899ca02a8d28f714740cba3a10523f3fb4"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/nalp-users-kafka-consumer/nalp-users-kafka-consumer:1828483"], "k8s.containerName": ["users-kafka-consumer"], "k8s.deploymentName": ["users-kafka-consumer"], "k8s.namespaceName": ["nalp"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["users-kafka-consumer-65bdc77-bw66t"], "k8s.replicasetName": ["users-kafka-consumer-65bdc77"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["65bdc77"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTU5MTE0OTg4MTAyNjA5NzgzMQ", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNjk3MDIxNjE2MjAzMjU1NjYwNg", "name": "connector-747e91ba-39ad-464e-90fc-8c1b5439405f-382de0ba-5608-41ff-9400-0ee3f8ab2300", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "RecId": ["382de0ba-5608-41ff-9400-0ee3f8ab2300"], "TenantId": ["747e91ba-39ad-464e-90fc-8c1b5439405f"], "environment-customerdata": ["no"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.namespaceName": ["connectors"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["connector-747e91ba-39ad-464e-90fc-8c1b5439405f-382de0ba-5608-41ff-9400-0ee3f8ab2300"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "product-name": ["ivantineuronsdiscovery"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNjk3MDIxNjE2MjAzMjU1NjYwNg", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODg3ODYyMjA0MjkyMTI5MDg0NQ", "name": "automation-dispatcher-handlers", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["automation-dispatcher-handlers"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["210ad2e0740fa0bc80016a1a5ddf1ecdbfd25875cffb89d6fc1b19b57e1c8b02"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/automation-dispatcher/automation-dispatcher-handlers:1914709"], "k8s.containerName": ["automation-dispatcher-handlers"], "k8s.deploymentName": ["automation-dispatcher-handlers-deployment"], "k8s.namespaceName": ["automation-dispatcher"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["automation-dispatcher-handlers-deployment-5c6d7f8bb5-7j5jl"], "k8s.replicasetName": ["automation-dispatcher-handlers-deployment-5c6d7f8bb5"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["5c6d7f8bb5"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtODg3ODYyMjA0MjkyMTI5MDg0NQ", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwxMjAxMjMwODEyMzE3NTM1OTY0", "name": "dataservices-dataimport-monitor-service-deployment", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "deployment": ["dataservices-dataimport-monitor-service-deployment"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["dataservices-dataimport-monitor-service-deployment"], "k8s.namespaceName": ["dataservices-dataimport"], "namespace": ["dataservices-dataimport"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.6.0"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwxMjAxMjMwODEyMzE3NTM1OTY0", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNjcwMjEyNjc3MzY0ODU0ODYyNA", "name": "connectorapi", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["88697ca91b08eac0082c8307967bb90633d2d21fecfb5b151968b551689c6bf1"], "k8s.containerImage": ["apolloteam.azurecr.io/connector/connectorapi:********.1"], "k8s.containerName": ["connectorapi"], "k8s.deploymentName": ["connectorapi"], "k8s.namespaceName": ["connector"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["connectorapi-7f84bc95b5-lkrbd"], "k8s.replicasetName": ["connectorapi-7f84bc95b5"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["7f84bc95b5"], "service-pod-link": ["connectorapi-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNjcwMjEyNjc3MzY0ODU0ODYyNA", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "name": "aks-default-********-vmss0007pf", "issue": false, "group": 1, "icon": "static/icons/node.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "INFRASTRUCTURE_HOST_ENTITY", "type": "HOST", "agentName": ["Infrastructure"], "agentVersion": ["1.46.0"], "availabilityZone": ["3"], "clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "coreCount": ["4"], "fullHostname": ["aks-default-********-vmss0007pf"], "host.id": ["f3989479-b3b4-4fea-bba2-42161af7b510"], "hostStatus": ["running"], "hostname": ["aks-default-********-vmss0007pf"], "instanceType": ["Standard_D8s_v3"], "kernelVersion": ["5.15.0-1071-azure"], "linuxDistribution": ["Ubuntu 22.04.4 LTS"], "operatingSystem": ["linux"], "processorCount": ["8"], "regionName": ["eastus"], "subscriptionId": ["53f45c73-c7c2-4733-a772-6b3505573621"], "systemMemoryBytes": ["***********"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "type": "HOST", "entityType": "INFRASTRUCTURE_HOST_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw1NzQ5ODEyMDE2ODk3MzUwODg", "name": "data-services-query-api-deployment-5d744ff7d9-sdlm7", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["data-services-query-api-deployment-5d744ff7d9"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["data-services-query-api-deployment"], "k8s.namespaceName": ["data-services"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["data-services-query-api-deployment-5d744ff7d9-sdlm7"], "k8s.replicasetName": ["data-services-query-api-deployment-5d744ff7d9"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["5d744ff7d9"], "service-pod-link": ["data-services-query-api-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw1NzQ5ODEyMDE2ODk3MzUwODg", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzNTUxMzg4MTU1OTM2MDE5OTc1", "name": "patch-content-public-api-container", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["patch-content-public-api-container"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["8df7aa54b2b67a753418b07e3c4e59305069918c6da151cd67697878e1302690"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/patch-content/patchcontent.api.webhost.public.container:1919596"], "k8s.containerName": ["patch-content-public-api-container"], "k8s.deploymentName": ["patch-content-public-api-container"], "k8s.namespaceName": ["patch-content"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["patch-content-public-api-container-76569bbd9b-lxvzc"], "k8s.replicasetName": ["patch-content-public-api-container-76569bbd9b"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["76569bbd9b"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzNTUxMzg4MTU1OTM2MDE5OTc1", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTM3OTIyNjUyNzUwOTI5NTg4MA", "name": "automation-dispatcher-handlers-deployment-5c6d7f8bb5-7j5jl", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["automation-dispatcher-handlers"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["automation-dispatcher-handlers-deployment-5c6d7f8bb5"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["automation-dispatcher-handlers-deployment"], "k8s.namespaceName": ["automation-dispatcher"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["automation-dispatcher-handlers-deployment-5c6d7f8bb5-7j5jl"], "k8s.replicasetName": ["automation-dispatcher-handlers-deployment-5c6d7f8bb5"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["5c6d7f8bb5"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTM3OTIyNjUyNzUwOTI5NTg4MA", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODA5Mzc3ODk1NTQ3NDA1NTE0Mg", "name": "login-app-message-handler-service-75ddd95869-gp2w5", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-message-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-message-handler-service-75ddd95869"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-gp2w5"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtODA5Mzc3ODk1NTQ3NDA1NTE0Mg", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MzQ3NjM1NDI2NTU2NTg5MjE4", "name": "data-services-query-api-deployment", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "deployment": ["data-services-query-api-deployment"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["data-services-query-api-deployment"], "k8s.namespaceName": ["data-services"], "kubernetes_io_metadata_name": ["data-services"], "namespace": ["data-services"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.2.0", "v3.4.0", "v3.6.0", "2.6.1"], "service_pod_link": ["data-services-query-api-selector"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2MzQ3NjM1NDI2NTU2NTg5MjE4", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNzM5MjE4MzU5NTc4MDIxODM1Ng", "name": "twistlock-defender-ds", "issue": false, "group": 1, "icon": "static/icons/svc.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DAEMONSET", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.daemonsetName": ["twistlock-defender-ds"], "k8s.namespaceName": ["twistlock"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNzM5MjE4MzU5NTc4MDIxODM1Ng", "type": "KUBERNETES_DAEMONSET", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODI0MzQzNjg1MjEzMTAwMTI2", "name": "ism-sync-handler-container-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["ism-sync-handler-service"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["59369dc9d4a9ecabed59cd97bb99839b8404b5b57bdd6d825abf59309531e54a"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/ism-sync/sync.handler.service:1865100"], "k8s.containerName": ["ism-sync-handler-container-service"], "k8s.deploymentName": ["ism-sync-handler-service-deployment"], "k8s.namespaceName": ["ism-sync"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["ism-sync-handler-service-deployment-646bcdc944-4rv68"], "k8s.replicasetName": ["ism-sync-handler-service-deployment-646bcdc944"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["646bcdc944"], "service-pod-link": ["ism-sync-handler-service-selector"], "trustedAccountId": ["1093620"], "version": ["1865100"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtODI0MzQzNjg1MjEzMTAwMTI2", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw3NjA5ODA3NTQ1MzUzMzc4MzEx", "name": "automation-management-webhost-deployment-fb889f59d-wp5lj", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["automation-management-webhost"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["automation-management-webhost-deployment-fb889f59d"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["automation-management-webhost-deployment"], "k8s.namespaceName": ["automation-management"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["automation-management-webhost-deployment-fb889f59d-wp5lj"], "k8s.replicasetName": ["automation-management-webhost-deployment-fb889f59d"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["fb889f59d"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw3NjA5ODA3NTQ1MzUzMzc4MzEx", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwxMDIyNjI3NTM1MDQwODc3MDI0", "name": "data-services-query-api-facade-deployment-54f85866b7-t2zk5", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["data-services-query-api-facade-deployment-54f85866b7"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["data-services-query-api-facade-deployment"], "k8s.namespaceName": ["data-services"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["data-services-query-api-facade-deployment-54f85866b7-t2zk5"], "k8s.replicasetName": ["data-services-query-api-facade-deployment-54f85866b7"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["54f85866b7"], "service-pod-link": ["data-services-query-api-facade-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwxMDIyNjI3NTM1MDQwODc3MDI0", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyMjkyMTA0NDk0NDI3NTc3MjE", "name": "data-services-query-api-facade-deployment", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["data-services-query-api-facade-deployment"], "k8s.namespaceName": ["data-services"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwyMjkyMTA0NDk0NDI3NTc3MjE", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNzQ1MTcyMzMzMjk4MDE5NDI2Ng", "name": "twistlock-defender-ds-lzjbk", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["twistlock-defender"], "controller-revision-hash": ["6cf65857d"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["twistlock-defender-ds"], "k8s.createdKind": ["DaemonSet"], "k8s.daemonsetName": ["twistlock-defender-ds"], "k8s.namespaceName": ["twistlock"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["twistlock-defender-ds-lzjbk"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-generation": ["5"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNzQ1MTcyMzMzMjk4MDE5NDI2Ng", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwxOTMwNDYwMjk5MjQzNTg2NDQ0", "name": "login-app-message-handler-service-75ddd95869-bnjhw", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-message-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-message-handler-service-75ddd95869"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-bnjhw"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwxOTMwNDYwMjk5MjQzNTg2NDQ0", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTcyMTQ3ODQ1MDA2MjkzOTYwMw", "name": "login-app-message-handler-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["login-app-message-handler-service"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["991b56f5a2d867eeb74ab580288bab863fd68c14b108563370cd16061573a55b"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/login-app/login-app-message-handler-service:1922913"], "k8s.containerName": ["login-app-message-handler-service"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pg"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-lx2wk"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTcyMTQ3ODQ1MDA2MjkzOTYwMw", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4OTY0NTQzMDYxNzIxNjY5NDI", "name": "automation-dispatcher-handlers-deployment", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["automation-dispatcher-handlers-deployment"], "k8s.namespaceName": ["automation-dispatcher"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4OTY0NTQzMDYxNzIxNjY5NDI", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODEwMzczNzQ3Njc1NjM0MTY1Mw", "name": "uwm-application-webapi", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["uwm-application-webapi"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["5f46b86f4961ec16ea52128bdce995ce1dd4a8cb0473af4c212db8a8890b7899"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/uwm/uwm.webapi.service:1923769"], "k8s.containerName": ["uwm-application-webapi"], "k8s.deploymentName": ["uwm-application-webapi"], "k8s.namespaceName": ["uwm-application"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["uwm-application-webapi-784b78fbd9-v8g28"], "k8s.replicasetName": ["uwm-application-webapi-784b78fbd9"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["784b78fbd9"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtODEwMzczNzQ3Njc1NjM0MTY1Mw", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw1MjU4MDkyNDM3NjkzMDg0NTY0", "name": "login-app-login-service-agent-only", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["login-app-login-service"], "appWhenSplit": ["login-app-login-service-agent-only"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["48763f4871371052549187d143389b09ea40449514b8c388de485db0a82f138e"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/login-app/login-app-login-service:1922913"], "k8s.containerName": ["login-app-login-service-agent-only"], "k8s.deploymentName": ["login-app-login-service-agent-only"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["login-app-login-service-agent-only-6c787495-k6zqr"], "k8s.replicasetName": ["login-app-login-service-agent-only-6c787495"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["6c787495"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw1MjU4MDkyNDM3NjkzMDg0NTY0", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMjk1MDY2NDAwNzQ4NDQ5NzA1NQ", "name": "patch-content-public-api-container-76569bbd9b-lxvzc", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["patch-content-public-api-container"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["patch-content-public-api-container-76569bbd9b"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["patch-content-public-api-container"], "k8s.namespaceName": ["patch-content"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["patch-content-public-api-container-76569bbd9b-lxvzc"], "k8s.replicasetName": ["patch-content-public-api-container-76569bbd9b"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["76569bbd9b"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMjk1MDY2NDAwNzQ4NDQ5NzA1NQ", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyODUwMjg4ODYwMDExODY3MzUx", "name": "login-app-login-service-agent-only-6c787495-k6zqr", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-login-service"], "appWhenSplit": ["login-app-login-service-agent-only"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-login-service-agent-only-6c787495"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-login-service-agent-only"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["login-app-login-service-agent-only-6c787495-k6zqr"], "k8s.replicasetName": ["login-app-login-service-agent-only-6c787495"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["6c787495"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwyODUwMjg4ODYwMDExODY3MzUx", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODExMjc4Mjk3Mzg3NDQ5NDQyMg", "name": "newrelic-bundle-nrk8s-kubelet-7tzvj", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app.kubernetes.io/component": ["kubelet"], "app.kubernetes.io/instance": ["newrelic-bundle"], "app.kubernetes.io/name": ["newrelic-infrastructure"], "controller-revision-hash": ["69c459c7b6"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["newrelic-bundle-nrk8s-kubelet"], "k8s.createdKind": ["DaemonSet"], "k8s.daemonsetName": ["newrelic-bundle-nrk8s-kubelet"], "k8s.namespaceName": ["ns-core"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["newrelic-bundle-nrk8s-kubelet-7tzvj"], "k8s.status": ["Running"], "mode": ["privileged"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-generation": ["5"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtODExMjc4Mjk3Mzg3NDQ5NDQyMg", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNjY5OTUzNTg1NjY4MzM0NTc5NQ", "name": "query-api", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["bd5fad53a2256c2f0bca57bb5c89d15d6bfaf67533356b15b84ffda5704c9d1e"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/data-services.queryapi/data-services.queryapi:1920399"], "k8s.containerName": ["query-api"], "k8s.deploymentName": ["data-services-query-api-deployment"], "k8s.namespaceName": ["data-services"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["data-services-query-api-deployment-5d744ff7d9-hhnsz"], "k8s.replicasetName": ["data-services-query-api-deployment-5d744ff7d9"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["5d744ff7d9"], "service-pod-link": ["data-services-query-api-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNjY5OTUzNTg1NjY4MzM0NTc5NQ", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyNjU3NjQxMjEzOTA2MzM1MzQ3", "name": "spend-intelligence-webclient", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "app": ["spend-intelligence-webclient"], "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "deployment": ["spend-intelligence-webclient"], "istio_injection": ["disabled"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["spend-intelligence-webclient"], "k8s.namespaceName": ["spend-intelligence-webclient"], "kubernetes_io_metadata_name": ["spend-intelligence-webclient"], "namespace": ["spend-intelligence-webclient"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.4.0", "v3.6.0"], "trustedAccountId": ["3483511"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwyNjU3NjQxMjEzOTA2MzM1MzQ3", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw1MDk4MjY4Nzg0MjM3ODY4MDkz", "name": "login-app-message-handler-service-75ddd95869-75flb", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-message-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-message-handler-service-75ddd95869"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007qv"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-75flb"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw1MDk4MjY4Nzg0MjM3ODY4MDkz", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MDUzODM2OTczOTIyMzgwMjE", "name": "newrelic-bundle-newrelic-logging-k4bwh", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["newrelic-logging"], "app.kubernetes.io/name": ["newrelic-logging"], "controller-revision-hash": ["58685d46cd"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["newrelic-bundle-newrelic-logging"], "k8s.createdKind": ["DaemonSet"], "k8s.daemonsetName": ["newrelic-bundle-newrelic-logging"], "k8s.namespaceName": ["ns-core"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["newrelic-bundle-newrelic-logging-k4bwh"], "k8s.status": ["Running"], "kubernetes.io/os": ["linux"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-generation": ["6"], "release": ["newrelic-bundle"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2MDUzODM2OTczOTIyMzgwMjE", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MDcwNjUwMTUyNzI2Mzg0MDQw", "name": "login-app-admin-service", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["login-app-admin-service"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["e58e20347ffc26f345b9dd176b0858aa839158138a99a0a0798d7f4e0af6ba25"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/login-app/login-app-admin-service:1922913"], "k8s.containerName": ["login-app-admin-service"], "k8s.deploymentName": ["login-app-admin-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["login-app-admin-service-8b4c5677b-xqhcd"], "k8s.replicasetName": ["login-app-admin-service-8b4c5677b"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["8b4c5677b"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2MDcwNjUwMTUyNzI2Mzg0MDQw", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNzk0OTcxNTM1MDQ5Njk1NTMwOA", "name": "spend-intelligence-webclient", "issue": false, "group": 1, "icon": "static/icons/containerinstances.png", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "CONTAINER", "app": ["spend-intelligence-webclient"], "container.state": ["Running"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.containerId": ["e49617ece2e85663704661c400c395f43c4888643032e3adbb7bc9584f0b0303"], "k8s.containerImage": ["ivantiglobalregistry.azurecr.io/neurons/prod/spend-intelligence-webclient/spend-intelligence-webclient:1911716"], "k8s.containerName": ["spend-intelligence-webclient"], "k8s.deploymentName": ["spend-intelligence-webclient"], "k8s.namespaceName": ["spend-intelligence-webclient"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["spend-intelligence-webclient-5bfd5c5bc6-7spxx"], "k8s.replicasetName": ["spend-intelligence-webclient-5bfd5c5bc6"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["5bfd5c5bc6"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNzk0OTcxNTM1MDQ5Njk1NTMwOA", "type": "CONTAINER", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMTEyMzY2NTA5MjU3MDE1NTA2OA", "name": "login-app-message-handler-service-75ddd95869-wfm55", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "app": ["login-app-message-handler-service"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["login-app-message-handler-service-75ddd95869"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["login-app-message-handler-service"], "k8s.namespaceName": ["login-app"], "k8s.nodeName": ["aks-default-********-vmss0007bz"], "k8s.podName": ["login-app-message-handler-service-75ddd95869-wfm55"], "k8s.replicasetName": ["login-app-message-handler-service-75ddd95869"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["75ddd95869"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMTEyMzY2NTA5MjU3MDE1NTA2OA", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MDI5MjkxODg1NjE0MzYwNDIy", "name": "connectorapi-7f84bc95b5-lkrbd", "issue": false, "group": 1, "icon": "static/icons/pod.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_POD", "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.createdBy": ["connectorapi-7f84bc95b5"], "k8s.createdKind": ["ReplicaSet"], "k8s.deploymentName": ["connectorapi"], "k8s.namespaceName": ["connector"], "k8s.nodeName": ["aks-default-********-vmss0007pf"], "k8s.podName": ["connectorapi-7f84bc95b5-lkrbd"], "k8s.replicasetName": ["connectorapi-7f84bc95b5"], "k8s.status": ["Running"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3"], "pod-template-hash": ["7f84bc95b5"], "service-pod-link": ["connectorapi-selector"], "trustedAccountId": ["1093620"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2MDI5MjkxODg1NjE0MzYwNDIy", "type": "KUBERNETES_POD", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MzU4MTE2Mzg1NDEyMDEyMjI", "name": "ism-sync-handler-service-deployment", "issue": false, "group": 1, "icon": "static/icons/deploy.svg", "properties": {"account": ["IvantiCloud - US"], "accountId": ["1093620"], "domain": "INFRA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "type": "KUBERNETES_DEPLOYMENT", "app": ["ism-sync-handler-service"], "app_kubernetes_io_managed_by": ["<PERSON><PERSON>"], "deployment": ["ism-sync-handler-service-deployment"], "istio_injection": ["disabled"], "k8s.clusterName": ["https://aks-rg-uku-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443"], "k8s.deploymentName": ["ism-sync-handler-service-deployment"], "k8s.namespaceName": ["ism-sync"], "kubernetes_io_metadata_name": ["ism-sync"], "namespace": ["ism-sync"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.18.3", "v3.2.0", "v3.4.0", "v3.6.0"], "service_pod_link": ["ism-sync-handler-service-selector"], "trustedAccountId": ["3483511"], "version": ["1028307", "1028616", "1030897", "971519", "1012020"]}, "alt_names": [], "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2MzU4MTE2Mzg1NDEyMDEyMjI", "type": "KUBERNETES_DEPLOYMENT", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}], "links": [{"id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz->MTA5MzYyMHxJTkZSQXxOQXwtMjEyMDk1OTIzMjM3NDkxNzk1NQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwtMjEyMDk1OTIzMjM3NDkxNzk1NQ", "target": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyMjkyMTA0NDk0NDI3NTc3MjE->MTA5MzYyMHxJTkZSQXxOQXwtMjY3MTU4NDgzNDYyODYxNjM1", "source": "MTA5MzYyMHxJTkZSQXxOQXwtMjY3MTU4NDgzNDYyODYxNjM1", "target": "MTA5MzYyMHxJTkZSQXxOQXwyMjkyMTA0NDk0NDI3NTc3MjE", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMjY3MTU4NDgzNDYyODYxNjM1->MTA5MzYyMHxJTkZSQXxOQXw4OTk5NTg4ODgxODc5Mjk3NzI0", "source": "MTA5MzYyMHxJTkZSQXxOQXwtMjY3MTU4NDgzNDYyODYxNjM1", "target": "MTA5MzYyMHxJTkZSQXxOQXw4OTk5NTg4ODgxODc5Mjk3NzI0", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4OTA1NTE3MjI0MDI1MzQ4NDYx->MTA5MzYyMHxJTkZSQXxOQXwtNjI5NDY4MDcxMjQwOTg5NTE0OA", "source": "MTA5MzYyMHxJTkZSQXxOQXw4OTA1NTE3MjI0MDI1MzQ4NDYx", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNjI5NDY4MDcxMjQwOTg5NTE0OA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODExMjc4Mjk3Mzg3NDQ5NDQyMg->MTA5MzYyMHxJTkZSQXxOQXwtNzcxODU1MDMyNjE2MDI4NjIwMA", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNzcxODU1MDMyNjE2MDI4NjIwMA", "target": "MTA5MzYyMHxJTkZSQXxOQXwtODExMjc4Mjk3Mzg3NDQ5NDQyMg", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwtNzcxODU1MDMyNjE2MDI4NjIwMA", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNzcxODU1MDMyNjE2MDI4NjIwMA", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyMTExNjU0OTk5NjUyMDk3Mzk4->MTA5MzYyMHxJTkZSQXxOQXwtNDkwOTQzNDU2OTk3ODY3OTA1NQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNDkwOTQzNDU2OTk3ODY3OTA1NQ", "target": "MTA5MzYyMHxJTkZSQXxOQXwyMTExNjU0OTk5NjUyMDk3Mzk4", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwtNDkwOTQzNDU2OTk3ODY3OTA1NQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNDkwOTQzNDU2OTk3ODY3OTA1NQ", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODA5Mzc3ODk1NTQ3NDA1NTE0Mg->MTA5MzYyMHxJTkZSQXxOQXwxNzM0MjcyNDgzOTIxNDAwMDA1", "source": "MTA5MzYyMHxJTkZSQXxOQXwxNzM0MjcyNDgzOTIxNDAwMDA1", "target": "MTA5MzYyMHxJTkZSQXxOQXwtODA5Mzc3ODk1NTQ3NDA1NTE0Mg", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODE5OTYwOTAzOTcxMzQyOTE3MQ->MTA5MzYyMHxJTkZSQXxOQXw2MDUzODM2OTczOTIyMzgwMjE", "source": "MTA5MzYyMHxJTkZSQXxOQXwtODE5OTYwOTAzOTcxMzQyOTE3MQ", "target": "MTA5MzYyMHxJTkZSQXxOQXw2MDUzODM2OTczOTIyMzgwMjE", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNjI5NDY4MDcxMjQwOTg5NTE0OA->MTA5MzYyMHxJTkZSQXxOQXwtNTU5MTE0OTg4MTAyNjA5NzgzMQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNjI5NDY4MDcxMjQwOTg5NTE0OA", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTU5MTE0OTg4MTAyNjA5NzgzMQ", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNjM5MTg5NTgzNzQwMTU0MzYzMQ->MTA5MzYyMHxJTkZSQXxOQXw2NjE5OTYzNDgxNTc4NDI2MDk4", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNjM5MTg5NTgzNzQwMTU0MzYzMQ", "target": "MTA5MzYyMHxJTkZSQXxOQXw2NjE5OTYzNDgxNTc4NDI2MDk4", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4MjUxNjU2Mjk3MjU4MzAzMDIw->MTA5MzYyMHxJTkZSQXxOQXwzNTMzMTUwMzE2ODYxODI5MzU0", "source": "MTA5MzYyMHxJTkZSQXxOQXwzNTMzMTUwMzE2ODYxODI5MzU0", "target": "MTA5MzYyMHxJTkZSQXxOQXw4MjUxNjU2Mjk3MjU4MzAzMDIw", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzNTMzMTUwMzE2ODYxODI5MzU0->MTA5MzYyMHxJTkZSQXxOQXw2MDcwNjUwMTUyNzI2Mzg0MDQw", "source": "MTA5MzYyMHxJTkZSQXxOQXwzNTMzMTUwMzE2ODYxODI5MzU0", "target": "MTA5MzYyMHxJTkZSQXxOQXw2MDcwNjUwMTUyNzI2Mzg0MDQw", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MzQ3NjM1NDI2NTU2NTg5MjE4->MTA5MzYyMHxJTkZSQXxOQXwtNTU0MTQ5NzUyNDUxNjU1OTA0", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTU0MTQ5NzUyNDUxNjU1OTA0", "target": "MTA5MzYyMHxJTkZSQXxOQXw2MzQ3NjM1NDI2NTU2NTg5MjE4", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTU0MTQ5NzUyNDUxNjU1OTA0->MTA5MzYyMHxJTkZSQXxOQXwtNjY5OTUzNTg1NjY4MzM0NTc5NQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTU0MTQ5NzUyNDUxNjU1OTA0", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNjY5OTUzNTg1NjY4MzM0NTc5NQ", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2NjU4ODI0Njc2Njg4NTc5NzMw->MTA5MzYyMHxJTkZSQXxOQXwtODExMjc4Mjk3Mzg3NDQ5NDQyMg", "source": "MTA5MzYyMHxJTkZSQXxOQXw2NjU4ODI0Njc2Njg4NTc5NzMw", "target": "MTA5MzYyMHxJTkZSQXxOQXwtODExMjc4Mjk3Mzg3NDQ5NDQyMg", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNDY3MDI4MzkxNjMwNTM3OTg3NA->MTA5MzYyMHxJTkZSQXxOQXwtMzY1NjI2MDc1MzE3Mjk1OTU5Mg", "source": "MTA5MzYyMHxJTkZSQXxOQXwtMzY1NjI2MDc1MzE3Mjk1OTU5Mg", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNDY3MDI4MzkxNjMwNTM3OTg3NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwtMzY1NjI2MDc1MzE3Mjk1OTU5Mg", "source": "MTA5MzYyMHxJTkZSQXxOQXwtMzY1NjI2MDc1MzE3Mjk1OTU5Mg", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNzQ1MTcyMzMzMjk4MDE5NDI2Ng->MTA5MzYyMHxJTkZSQXxOQXwyMTkzNDc5NTYzMDI2MTc4MDE1", "source": "MTA5MzYyMHxJTkZSQXxOQXwyMTkzNDc5NTYzMDI2MTc4MDE1", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNzQ1MTcyMzMzMjk4MDE5NDI2Ng", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwyMTkzNDc5NTYzMDI2MTc4MDE1", "source": "MTA5MzYyMHxJTkZSQXxOQXwyMTkzNDc5NTYzMDI2MTc4MDE1", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz->MTA5MzYyMHxJTkZSQXxOQXwtNTMyODUzMTI1MjY2NTc5MTQyNQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTMyODUzMTI1MjY2NTc5MTQyNQ", "target": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTMyODUzMTI1MjY2NTc5MTQyNQ->MTA5MzYyMHxJTkZSQXxOQXwtNTcyMTQ3ODQ1MDA2MjkzOTYwMw", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTMyODUzMTI1MjY2NTc5MTQyNQ", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTcyMTQ3ODQ1MDA2MjkzOTYwMw", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw3NjA5ODA3NTQ1MzUzMzc4MzEx->MTA5MzYyMHxJTkZSQXxOQXw2NDcxMTM5NzMxNjkzMzA2NjA", "source": "MTA5MzYyMHxJTkZSQXxOQXw2NDcxMTM5NzMxNjkzMzA2NjA", "target": "MTA5MzYyMHxJTkZSQXxOQXw3NjA5ODA3NTQ1MzUzMzc4MzEx", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXw2NDcxMTM5NzMxNjkzMzA2NjA", "source": "MTA5MzYyMHxJTkZSQXxOQXw2NDcxMTM5NzMxNjkzMzA2NjA", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMTE4NDk5MDU2ODAyMzI0MjIyOQ->MTA5MzYyMHxJTkZSQXxOQXw2MDI5MjkxODg1NjE0MzYwNDIy", "source": "MTA5MzYyMHxJTkZSQXxOQXwtMTE4NDk5MDU2ODAyMzI0MjIyOQ", "target": "MTA5MzYyMHxJTkZSQXxOQXw2MDI5MjkxODg1NjE0MzYwNDIy", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz->MTA5MzYyMHxJTkZSQXxOQXw4Nzg3MjI5NzA4MjczNDIwMzg4", "source": "MTA5MzYyMHxJTkZSQXxOQXw4Nzg3MjI5NzA4MjczNDIwMzg4", "target": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTI0MTI4MjY5OTY4NTM3ODI3Mg->************************************************", "source": "************************************************", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTI0MTI4MjY5OTY4NTM3ODI3Mg", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "************************************************->MTA5MzYyMHxJTkZSQXxOQXwtODEwMzczNzQ3Njc1NjM0MTY1Mw", "source": "************************************************", "target": "MTA5MzYyMHxJTkZSQXxOQXwtODEwMzczNzQ3Njc1NjM0MTY1Mw", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNjk3MDIxNjE2MjAzMjU1NjYwNg->MTA5MzYyMHxJTkZSQXxOQXw4MjQ2ODE5MTg3MjA2NDAzMzEw", "source": "MTA5MzYyMHxJTkZSQXxOQXw4MjQ2ODE5MTg3MjA2NDAzMzEw", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNjk3MDIxNjE2MjAzMjU1NjYwNg", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXw4MjQ2ODE5MTg3MjA2NDAzMzEw", "source": "MTA5MzYyMHxJTkZSQXxOQXw4MjQ2ODE5MTg3MjA2NDAzMzEw", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw3NDQ2ODI0NDk2NDQ1ODQzNTk0->MTA5MzYyMHxJTkZSQXxOQXwtNDY3MDI4MzkxNjMwNTM3OTg3NA", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNDY3MDI4MzkxNjMwNTM3OTg3NA", "target": "MTA5MzYyMHxJTkZSQXxOQXw3NDQ2ODI0NDk2NDQ1ODQzNTk0", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMTc2NjExMzgwODY3NTA5Mzc3Ng->MTA5MzYyMHxJTkZSQXxOQXwyODUwMjg4ODYwMDExODY3MzUx", "source": "MTA5MzYyMHxJTkZSQXxOQXwtMTc2NjExMzgwODY3NTA5Mzc3Ng", "target": "MTA5MzYyMHxJTkZSQXxOQXwyODUwMjg4ODYwMDExODY3MzUx", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4Njg3Mzk1MDU4Nzg3MjI5Mzk3->MTA5MzYyMHxJTkZSQXxOQXw4Njc5OTU0ODkwMjUzOTU2OTg1", "source": "MTA5MzYyMHxJTkZSQXxOQXw4Njc5OTU0ODkwMjUzOTU2OTg1", "target": "MTA5MzYyMHxJTkZSQXxOQXw4Njg3Mzk1MDU4Nzg3MjI5Mzk3", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXw4Njc5OTU0ODkwMjUzOTU2OTg1", "source": "MTA5MzYyMHxJTkZSQXxOQXw4Njc5OTU0ODkwMjUzOTU2OTg1", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MDUzODM2OTczOTIyMzgwMjE->MTA5MzYyMHxJTkZSQXxOQXw2MjUzNjYwNzIwNjk0NzkwMjIy", "source": "MTA5MzYyMHxJTkZSQXxOQXw2MjUzNjYwNzIwNjk0NzkwMjIy", "target": "MTA5MzYyMHxJTkZSQXxOQXw2MDUzODM2OTczOTIyMzgwMjE", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXw2MjUzNjYwNzIwNjk0NzkwMjIy", "source": "MTA5MzYyMHxJTkZSQXxOQXw2MjUzNjYwNzIwNjk0NzkwMjIy", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwxMjAxMjMwODEyMzE3NTM1OTY0->MTA5MzYyMHxJTkZSQXxOQXwyMTExNjU0OTk5NjUyMDk3Mzk4", "source": "MTA5MzYyMHxJTkZSQXxOQXwyMTExNjU0OTk5NjUyMDk3Mzk4", "target": "MTA5MzYyMHxJTkZSQXxOQXwxMjAxMjMwODEyMzE3NTM1OTY0", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODExMjc4Mjk3Mzg3NDQ5NDQyMg->MTA5MzYyMHxJTkZSQXxOQXw0NTcwMzYxNTE2MTk0ODU4MDM4", "source": "MTA5MzYyMHxJTkZSQXxOQXw0NTcwMzYxNTE2MTk0ODU4MDM4", "target": "MTA5MzYyMHxJTkZSQXxOQXwtODExMjc4Mjk3Mzg3NDQ5NDQyMg", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXw0NTcwMzYxNTE2MTk0ODU4MDM4", "source": "MTA5MzYyMHxJTkZSQXxOQXw0NTcwMzYxNTE2MTk0ODU4MDM4", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MzU4MTE2Mzg1NDEyMDEyMjI->MTA5MzYyMHxJTkZSQXxOQXw2NTY3MjQzNDk4NjkxODI5NTA0", "source": "MTA5MzYyMHxJTkZSQXxOQXw2NTY3MjQzNDk4NjkxODI5NTA0", "target": "MTA5MzYyMHxJTkZSQXxOQXw2MzU4MTE2Mzg1NDEyMDEyMjI", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2NTY3MjQzNDk4NjkxODI5NTA0->MTA5MzYyMHxJTkZSQXxOQXwtODI0MzQzNjg1MjEzMTAwMTI2", "source": "MTA5MzYyMHxJTkZSQXxOQXw2NTY3MjQzNDk4NjkxODI5NTA0", "target": "MTA5MzYyMHxJTkZSQXxOQXwtODI0MzQzNjg1MjEzMTAwMTI2", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz->MTA5MzYyMHxJTkZSQXxOQXwzNTc1ODUxODk5ODY4OTk2MjA", "source": "MTA5MzYyMHxJTkZSQXxOQXwzNTc1ODUxODk5ODY4OTk2MjA", "target": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw3NTc4MDc4Nzc0ODA0NTk2NDQ2->MTA5MzYyMHxJTkZSQXxOQXw3NjA5ODA3NTQ1MzUzMzc4MzEx", "source": "MTA5MzYyMHxJTkZSQXxOQXw3NTc4MDc4Nzc0ODA0NTk2NDQ2", "target": "MTA5MzYyMHxJTkZSQXxOQXw3NjA5ODA3NTQ1MzUzMzc4MzEx", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz->MTA5MzYyMHxJTkZSQXxOQXw0MjE4NTc2NjI0NjgyNDQ3NTA1", "source": "MTA5MzYyMHxJTkZSQXxOQXw0MjE4NTc2NjI0NjgyNDQ3NTA1", "target": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwyMzE3NDU4NzUyMTA3NzQwMzcx", "source": "MTA5MzYyMHxJTkZSQXxOQXwyMzE3NDU4NzUyMTA3NzQwMzcx", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwxMDIyNjI3NTM1MDQwODc3MDI0->MTA5MzYyMHxJTkZSQXxOQXwyMzE3NDU4NzUyMTA3NzQwMzcx", "source": "MTA5MzYyMHxJTkZSQXxOQXwyMzE3NDU4NzUyMTA3NzQwMzcx", "target": "MTA5MzYyMHxJTkZSQXxOQXwxMDIyNjI3NTM1MDQwODc3MDI0", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwzNDIxNzI0ODc1NjYwNjEyNzg3", "source": "MTA5MzYyMHxJTkZSQXxOQXwzNDIxNzI0ODc1NjYwNjEyNzg3", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwxOTMwNDYwMjk5MjQzNTg2NDQ0->MTA5MzYyMHxJTkZSQXxOQXwzNDIxNzI0ODc1NjYwNjEyNzg3", "source": "MTA5MzYyMHxJTkZSQXxOQXwzNDIxNzI0ODc1NjYwNjEyNzg3", "target": "MTA5MzYyMHxJTkZSQXxOQXwxOTMwNDYwMjk5MjQzNTg2NDQ0", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz->MTA5MzYyMHxJTkZSQXxOQXwxOTMwNDYwMjk5MjQzNTg2NDQ0", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "target": "MTA5MzYyMHxJTkZSQXxOQXwxOTMwNDYwMjk5MjQzNTg2NDQ0", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz->MTA5MzYyMHxJTkZSQXxOQXwtODA5Mzc3ODk1NTQ3NDA1NTE0Mg", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "target": "MTA5MzYyMHxJTkZSQXxOQXwtODA5Mzc3ODk1NTQ3NDA1NTE0Mg", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz->MTA5MzYyMHxJTkZSQXxOQXwtMTEyMzY2NTA5MjU3MDE1NTA2OA", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "target": "MTA5MzYyMHxJTkZSQXxOQXwtMTEyMzY2NTA5MjU3MDE1NTA2OA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz->MTA5MzYyMHxJTkZSQXxOQXwtNDUyNzA4NTU4MDE4MTAxMTE4Mw", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNDUyNzA4NTU4MDE4MTAxMTE4Mw", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz->MTA5MzYyMHxJTkZSQXxOQXw1MDk4MjY4Nzg0MjM3ODY4MDkz", "source": "MTA5MzYyMHxJTkZSQXxOQXwzMjcwNDQ0OTExNzg2NjgyMTgz", "target": "MTA5MzYyMHxJTkZSQXxOQXw1MDk4MjY4Nzg0MjM3ODY4MDkz", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzMTEzOTk1OTkwMDA4MDY4MjIy->MTA5MzYyMHxJTkZSQXxOQXw4Njg3Mzk1MDU4Nzg3MjI5Mzk3", "source": "MTA5MzYyMHxJTkZSQXxOQXw4Njg3Mzk1MDU4Nzg3MjI5Mzk3", "target": "MTA5MzYyMHxJTkZSQXxOQXwzMTEzOTk1OTkwMDA4MDY4MjIy", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyNjU3NjQxMjEzOTA2MzM1MzQ3->MTA5MzYyMHxJTkZSQXxOQXw4MzMxOTc5Nzk0Njg5MDQ4MDM5", "source": "MTA5MzYyMHxJTkZSQXxOQXw4MzMxOTc5Nzk0Njg5MDQ4MDM5", "target": "MTA5MzYyMHxJTkZSQXxOQXwyNjU3NjQxMjEzOTA2MzM1MzQ3", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4MzMxOTc5Nzk0Njg5MDQ4MDM5->MTA5MzYyMHxJTkZSQXxOQXwtNzk0OTcxNTM1MDQ5Njk1NTMwOA", "source": "MTA5MzYyMHxJTkZSQXxOQXw4MzMxOTc5Nzk0Njg5MDQ4MDM5", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNzk0OTcxNTM1MDQ5Njk1NTMwOA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMTExNDQ4NDMwMTI1MzA2MzgwMw->MTA5MzYyMHxJTkZSQXxOQXwtMjk1MDY2NDAwNzQ4NDQ5NzA1NQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwtMTExNDQ4NDMwMTI1MzA2MzgwMw", "target": "MTA5MzYyMHxJTkZSQXxOQXwtMjk1MDY2NDAwNzQ4NDQ5NzA1NQ", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXw2NjE5OTYzNDgxNTc4NDI2MDk4", "source": "MTA5MzYyMHxJTkZSQXxOQXw2NjE5OTYzNDgxNTc4NDI2MDk4", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXw4OTk5NTg4ODgxODc5Mjk3NzI0", "source": "MTA5MzYyMHxJTkZSQXxOQXw4OTk5NTg4ODgxODc5Mjk3NzI0", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw1NzQ5ODEyMDE2ODk3MzUwODg->MTA5MzYyMHxJTkZSQXxOQXwtMTkxMDc0MzY5Nzk4MDc5ODIxMA", "source": "MTA5MzYyMHxJTkZSQXxOQXwtMTkxMDc0MzY5Nzk4MDc5ODIxMA", "target": "MTA5MzYyMHxJTkZSQXxOQXw1NzQ5ODEyMDE2ODk3MzUwODg", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwtMTkxMDc0MzY5Nzk4MDc5ODIxMA", "source": "MTA5MzYyMHxJTkZSQXxOQXwtMTkxMDc0MzY5Nzk4MDc5ODIxMA", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwtNTU5MTE0OTg4MTAyNjA5NzgzMQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTU5MTE0OTg4MTAyNjA5NzgzMQ", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTM3OTIyNjUyNzUwOTI5NTg4MA->MTA5MzYyMHxJTkZSQXxOQXwtODg3ODYyMjA0MjkyMTI5MDg0NQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwtODg3ODYyMjA0MjkyMTI5MDg0NQ", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTM3OTIyNjUyNzUwOTI5NTg4MA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwtODg3ODYyMjA0MjkyMTI5MDg0NQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwtODg3ODYyMjA0MjkyMTI5MDg0NQ", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwtNjcwMjEyNjc3MzY0ODU0ODYyNA", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNjcwMjEyNjc3MzY0ODU0ODYyNA", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MDI5MjkxODg1NjE0MzYwNDIy->MTA5MzYyMHxJTkZSQXxOQXwtNjcwMjEyNjc3MzY0ODU0ODYyNA", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNjcwMjEyNjc3MzY0ODU0ODYyNA", "target": "MTA5MzYyMHxJTkZSQXxOQXw2MDI5MjkxODg1NjE0MzYwNDIy", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwtODI0MzQzNjg1MjEzMTAwMTI2", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "target": "MTA5MzYyMHxJTkZSQXxOQXwtODI0MzQzNjg1MjEzMTAwMTI2", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwtNzk0OTcxNTM1MDQ5Njk1NTMwOA", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNzk0OTcxNTM1MDQ5Njk1NTMwOA", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwzNTUxMzg4MTU1OTM2MDE5OTc1", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "target": "MTA5MzYyMHxJTkZSQXxOQXwzNTUxMzg4MTU1OTM2MDE5OTc1", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwtNjY5OTUzNTg1NjY4MzM0NTc5NQ", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNjY5OTUzNTg1NjY4MzM0NTc5NQ", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXwtODEwMzczNzQ3Njc1NjM0MTY1Mw", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "target": "MTA5MzYyMHxJTkZSQXxOQXwtODEwMzczNzQ3Njc1NjM0MTY1Mw", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXw1MjU4MDkyNDM3NjkzMDg0NTY0", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "target": "MTA5MzYyMHxJTkZSQXxOQXw1MjU4MDkyNDM3NjkzMDg0NTY0", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA->MTA5MzYyMHxJTkZSQXxOQXw2MDcwNjUwMTUyNzI2Mzg0MDQw", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTQ2MjkxNTY0MzA3Nzc4NTk1NA", "target": "MTA5MzYyMHxJTkZSQXxOQXw2MDcwNjUwMTUyNzI2Mzg0MDQw", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2MzQ3NjM1NDI2NTU2NTg5MjE4->MTA5MzYyMHxJTkZSQXxOQXw1NzQ5ODEyMDE2ODk3MzUwODg", "source": "MTA5MzYyMHxJTkZSQXxOQXw1NzQ5ODEyMDE2ODk3MzUwODg", "target": "MTA5MzYyMHxJTkZSQXxOQXw2MzQ3NjM1NDI2NTU2NTg5MjE4", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMjk1MDY2NDAwNzQ4NDQ5NzA1NQ->MTA5MzYyMHxJTkZSQXxOQXwzNTUxMzg4MTU1OTM2MDE5OTc1", "source": "MTA5MzYyMHxJTkZSQXxOQXwzNTUxMzg4MTU1OTM2MDE5OTc1", "target": "MTA5MzYyMHxJTkZSQXxOQXwtMjk1MDY2NDAwNzQ4NDQ5NzA1NQ", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4OTY0NTQzMDYxNzIxNjY5NDI->MTA5MzYyMHxJTkZSQXxOQXwtNTM3OTIyNjUyNzUwOTI5NTg4MA", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTM3OTIyNjUyNzUwOTI5NTg4MA", "target": "MTA5MzYyMHxJTkZSQXxOQXw4OTY0NTQzMDYxNzIxNjY5NDI", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNzM5MjE4MzU5NTc4MDIxODM1Ng->MTA5MzYyMHxJTkZSQXxOQXwtNzQ1MTcyMzMzMjk4MDE5NDI2Ng", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNzM5MjE4MzU5NTc4MDIxODM1Ng", "target": "MTA5MzYyMHxJTkZSQXxOQXwtNzQ1MTcyMzMzMjk4MDE5NDI2Ng", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyMjkyMTA0NDk0NDI3NTc3MjE->MTA5MzYyMHxJTkZSQXxOQXwxMDIyNjI3NTM1MDQwODc3MDI0", "source": "MTA5MzYyMHxJTkZSQXxOQXwxMDIyNjI3NTM1MDQwODc3MDI0", "target": "MTA5MzYyMHxJTkZSQXxOQXwyMjkyMTA0NDk0NDI3NTc3MjE", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyODUwMjg4ODYwMDExODY3MzUx->MTA5MzYyMHxJTkZSQXxOQXw1MjU4MDkyNDM3NjkzMDg0NTY0", "source": "MTA5MzYyMHxJTkZSQXxOQXw1MjU4MDkyNDM3NjkzMDg0NTY0", "target": "MTA5MzYyMHxJTkZSQXxOQXwyODUwMjg4ODYwMDExODY3MzUx", "link_type": "connected_to", "properties": {}, "data_source": "live", "value": 1}]}