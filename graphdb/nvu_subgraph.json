{"nodes": [{"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw", "label": "KUBERNETESCLUSTER", "name": "https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443", "issue": "false", "group": "1", "icon": "static/icons/svc.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETESCLUSTER\", \"application\": \"IvantiNeurons\", \"displayName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"environment\": \"production\", \"instrumentation.provider\": \"newRelic\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw0NTU4NDkzMjc2Mjg1MjU1ODY", "label": "KUBERNETES_DEPLOYMENT", "name": "action-svc", "issue": "false", "group": "1", "icon": "static/icons/deploy.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_DEPLOYMENT\", \"app_kubernetes_io_managed_by\": \"Helm\", \"deployment\": \"action-svc\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.deploymentName\": \"action-svc\", \"k8s.namespaceName\": \"data-services\", \"kubernetes_io_metadata_name\": \"data-services\", \"namespace\": \"data-services\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"service_pod_link\": \"action-svc\", \"trustedAccountId\": \"3483511\"}", "alt_names": "[]", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw0NTU4NDkzMjc2Mjg1MjU1ODY"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyNjgxMTY2NjI1Mjc1ODY1NTI3", "label": "KUBERNETES_POD", "name": "action-svc-f5dd58786-kqhln", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"action-svc-f5dd58786\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"action-svc\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-default-********-vmss0007kb\", \"k8s.podName\": \"action-svc-f5dd58786-kqhln\", \"k8s.replicasetName\": \"action-svc-f5dd58786\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"f5dd58786\", \"service-pod-link\": \"action-svc\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwyNjgxMTY2NjI1Mjc1ODY1NTI3"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwyNTM5NTU0MDcyOTQzOTcyODEx", "label": "KUBERNETES_POD", "name": "action-svc-f5dd58786-7cll9", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"action-svc-f5dd58786\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"action-svc\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-default-********-vmss0007de\", \"k8s.podName\": \"action-svc-f5dd58786-7cll9\", \"k8s.replicasetName\": \"action-svc-f5dd58786\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"f5dd58786\", \"service-pod-link\": \"action-svc\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwyNTM5NTU0MDcyOTQzOTcyODEx"}], "edges": [{"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw->MTA5MzYyMHxJTkZSQXxOQXwyNTM5NTU0MDcyOTQzOTcyODEx", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw", "target": "MTA5MzYyMHxJTkZSQXxOQXwyNTM5NTU0MDcyOTQzOTcyODEx", "label": "contains", "properties": {"properties": "{}", "data_source": "live", "value": "1"}}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw->MTA5MzYyMHxJTkZSQXxOQXw0NTU4NDkzMjc2Mjg1MjU1ODY", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw", "target": "MTA5MzYyMHxJTkZSQXxOQXw0NTU4NDkzMjc2Mjg1MjU1ODY", "label": "contains", "properties": {"properties": "{}", "data_source": "live", "value": "1"}}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw->MTA5MzYyMHxJTkZSQXxOQXwyNjgxMTY2NjI1Mjc1ODY1NTI3", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw", "target": "MTA5MzYyMHxJTkZSQXxOQXwyNjgxMTY2NjI1Mjc1ODY1NTI3", "label": "contains", "properties": {"properties": "{}", "data_source": "live", "value": "1"}}]}