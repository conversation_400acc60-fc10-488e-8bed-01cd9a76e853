{"nodes": [{"id": "MTA5MzYyMHxJTkZSQXxOQXwtNjk5MTQ4MzUwNzg3MDIyMzYzOA", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-mwlxt", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wb\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-mwlxt\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNjk5MTQ4MzUwNzg3MDIyMzYzOA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMjI2NTA0NzE2NzcxODIwMTIzNQ", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-zm6m8", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w6\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-zm6m8\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMjI2NTA0NzE2NzcxODIwMTIzNQ", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4NTcwMTE0MjA0NDgwMjgyODE4", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-mjdkn", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wd\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-mjdkn\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4NTcwMTE0MjA0NDgwMjgyODE4", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw5MTYyNzU3NjE2NTU3MDMxNjMx", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-2kpk2", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wb\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-2kpk2\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw5MTYyNzU3NjE2NTU3MDMxNjMx", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw", "label": "KUBERNETESCLUSTER", "name": "https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443", "issue": "false", "group": "1", "icon": "static/icons/svc.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETESCLUSTER\", \"application\": \"IvantiNeurons\", \"displayName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"environment\": \"production\", \"instrumentation.provider\": \"newRelic\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtODI3OTE3MzI1NTk4ODYxODQ3", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-wwq87", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w7\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-wwq87\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtODI3OTE3MzI1NTk4ODYxODQ3", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMTU5MTYyMDU1MTEwOTcyNDUxNw", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-c2cms", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wd\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-c2cms\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMTU5MTYyMDU1MTEwOTcyNDUxNw", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMzAyMjE2NDIxODUxMzMwMzc1Nw", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-4jz4w", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w6\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-4jz4w\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMzAyMjE2NDIxODUxMzMwMzc1Nw", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNzgzNDUwNjE1Mjk5NDE3NjIzNw", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-mr59t", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wf\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-mr59t\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNzgzNDUwNjE1Mjk5NDE3NjIzNw", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMzY1NDMzODMwMjY2NDg1MDQwNg", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-gcp2n", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w3\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-gcp2n\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMzY1NDMzODMwMjY2NDg1MDQwNg", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNjQwNjAwNzM5NjQ5ODQzMDk5OQ", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-dkjj2", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wd\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-dkjj2\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNjQwNjAwNzM5NjQ5ODQzMDk5OQ", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw1MDAwMDIyNDAzMDY2NDcwMjk4", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-b9xtc", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wg\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-b9xtc\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw1MDAwMDIyNDAzMDY2NDcwMjk4", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2OTQ1NjM3MjQ5MDE1ODkxODg2", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-hk46s", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wg\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-hk46s\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2OTQ1NjM3MjQ5MDE1ODkxODg2", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw2NTE2NDY3MjA3MTAwNzcwNTAy", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-kkq5q", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w7\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-kkq5q\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw2NTE2NDY3MjA3MTAwNzcwNTAy", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMjA5MTAzMjQzMzAyNDAxNzkyOA", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-rgntg", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wb\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-rgntg\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMjA5MTAzMjQzMzAyNDAxNzkyOA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw1NTk3NDY3Njk4MDU1NTE5OTAx", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-mrs4x", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w6\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-mrs4x\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw1NTk3NDY3Njk4MDU1NTE5OTAx", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNzkzNTc5MjU2MzU4NTQxNzgw", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-j8cwv", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w7\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-j8cwv\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNzkzNTc5MjU2MzU4NTQxNzgw", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMTg2NjkyMTg0NTUxMDEzMTQ0Ng", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-4tnj7", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wd\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-4tnj7\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMTg2NjkyMTg0NTUxMDEzMTQ0Ng", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwxODMwNjYyNjk5NTcxNDM0MDc0", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-dlmd5", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wh\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-dlmd5\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwxODMwNjYyNjk5NTcxNDM0MDc0", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwxODczOTM0NDA2MTQxNjk3MDg", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-hz7ck", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w3\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-hz7ck\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwxODczOTM0NDA2MTQxNjk3MDg", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNTk5NzQ2NjYyMTE4MzE0NzEx", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-scjwd", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w3\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-scjwd\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTk5NzQ2NjYyMTE4MzE0NzEx", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtNDQ3NzAyNjcwMDAyOTU4NjA2Ng", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-gsrcv", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w6\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-gsrcv\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNDQ3NzAyNjcwMDAyOTU4NjA2Ng", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw1MzUyMjUxMDU0MTA3OTU2NDcz", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-djpsv", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000wf\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-djpsv\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Running\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw1MzUyMjUxMDU0MTA3OTU2NDcz", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtMjg2NTg5NDE4MDIyMTM3MzMwNQ", "label": "KUBERNETES_DEPLOYMENT", "name": "assetprocessor", "issue": "false", "group": "1", "icon": "static/icons/deploy.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_DEPLOYMENT\", \"app_kubernetes_io_managed_by\": \"Helm\", \"deployment\": \"assetprocessor\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"kubernetes_io_metadata_name\": \"data-services\", \"namespace\": \"data-services\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"service_pod_link\": \"assetprocessor-selector\", \"trustedAccountId\": \"3483511\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtMjg2NTg5NDE4MDIyMTM3MzMwNQ", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwtOTE1NzIxMTI5MDEwMjA0MzI0Mg", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-hpb6t", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w7\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-hpb6t\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtOTE1NzIxMTI5MDEwMjA0MzI0Mg", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw3MTMwMDYwMzU1ODczNjgzNTA", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-nck2c", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w6\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-nck2c\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw3MTMwMDYwMzU1ODczNjgzNTA", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw4NjEwMzg1ODYxNjk2ODc2MzAx", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-prwb5", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w6\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-prwb5\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw4NjEwMzg1ODYxNjk2ODc2MzAx", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXw1ODQxNDM4MjE1MzkzOTcwNjY1", "label": "KUBERNETES_POD", "name": "assetprocessor-5bd6b9f575-5v799", "issue": "false", "group": "1", "icon": "static/icons/pod.svg", "properties": "{\"account\": \"IvantiCloud - US\", \"accountId\": \"1093620\", \"domain\": \"INFRA\", \"entityType\": \"GENERIC_INFRASTRUCTURE_ENTITY\", \"type\": \"KUBERNETES_POD\", \"k8s.clusterName\": \"https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443\", \"k8s.createdBy\": \"assetprocessor-5bd6b9f575\", \"k8s.createdKind\": \"ReplicaSet\", \"k8s.deploymentName\": \"assetprocessor\", \"k8s.namespaceName\": \"data-services\", \"k8s.nodeName\": \"aks-dsap-********-vmss0000w3\", \"k8s.podName\": \"assetprocessor-5bd6b9f575-5v799\", \"k8s.replicasetName\": \"assetprocessor-5bd6b9f575\", \"k8s.status\": \"Failed\", \"newrelic.integrationName\": \"com.newrelic.kubernetes\", \"newrelic.integrationVersion\": \"3.18.3\", \"pod-template-hash\": \"5bd6b9f575\", \"service-pod-link\": \"assetprocessor-selector\", \"trustedAccountId\": \"1093620\"}", "alt_names": "[]", "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw1ODQxNDM4MjE1MzkzOTcwNjY1", "entityType": "GENERIC_INFRASTRUCTURE_ENTITY", "domain": "INFRA", "data_source": "live"}], "edges": [{"id": "b975b090-6add-4034-9355-97f3b8054ac4", "source": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw", "target": "MTA5MzYyMHxJTkZSQXxOQXwtMjg2NTg5NDE4MDIyMTM3MzMwNQ", "label": "connected_to", "properties": {"properties": "{}", "data_source": "live", "value": "1"}}]}