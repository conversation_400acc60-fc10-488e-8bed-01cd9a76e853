import json
import os
import requests
from dotenv import load_dotenv
from collections import deque
import logging
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass, field
from typing import List, Dict

# Load environment variables
load_dotenv(override=True)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class Tag:
    key: str
    values: List[str]

@dataclass
class Entity:
    guid: str
    name: str
    type: str
    entityType: str
    domain: str
    tags: List[Tag] = field(default_factory=list)

    def get_tag_value(self, key: str) -> List[str]:
        for tag in self.tags:
            if tag.key == key:
                return tag.values
        return []


class TopologyGenerator:
    def __init__(self, entity_id: str, depth: int = 2):
        if not entity_id:
            raise ValueError("Entity ID cannot be `None` or empty")
        self.entity_id = entity_id
        self.depth = depth
        self.endpoint = os.getenv("NEWRELIC_API_ENDPOINT", "https://api.newrelic.com/graphql")
        self.api_key = os.getenv("NEWRELIC_API_KEY")
        self.account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
        self.icons = {
            "default": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png",
            "redis": "https://redis.com/wp-content/themes/wpx/assets/images/logo-redis.svg",
            "database": "https://upload.wikimedia.org/wikipedia/commons/c/c5/201603_database.png",
        }
        self.topology = {"nodes": [], "links": []}
        self.processed_entities = set()
        
        # Initialize the queue with the root entity details
        root_entity = self.fetch_entity_details(self.entity_id)
        self.entities_to_process = deque([(root_entity, 0)])  # Initialize with Entity object
    
    def fetch_entity_relationships(self, entity_id: str) -> Dict[str, List[Dict[str, Any]]]:
        headers = {"Content-Type": "application/json", "API-Key": self.api_key}
        query = self.build_graphql_query(entity_id)
        response = requests.post(self.endpoint, headers=headers, json=query)

        if response.status_code == 200:
            return self.parse_graphql_response(response.json())
        else:
            logger.error(f"Failed to fetch relationships for entity {entity_id}: {response.status_code}")
            return {}

    def fetch_entity_details(self, entity_id: str) -> Entity:
        """Fetch the entity details for the root entity."""
        entity_relationships = self.fetch_entity_relationships(entity_id)
        # Get the root entity details from the relationships (source or target)
        for key in entity_relationships:
            for relationship in entity_relationships[key]:
                if relationship["source"].guid == entity_id:
                    return relationship["source"]
                if relationship["target"].guid == entity_id:
                    return relationship["target"]
        # Fallback in case entity not found in relationships
        return Entity(guid=entity_id, name="Unknown", type="", entityType="", domain="")


    def build_graphql_query(self, entity_id: str) -> Dict[str, str]:
        query = {
            "query": f"""
            {{
            actor {{
                entity(guid: "{entity_id}") {{
                name
                relatedEntities {{
                    results {{
                    source {{
                        entity {{
                        guid
                        name
                        type
                        entityType
                        domain
                        tags {{
                            key
                            values
                        }}
                        }}
                    }}
                    target {{
                        entity {{
                        guid
                        name
                        type
                        entityType
                        domain
                        tags {{
                            key
                            values
                        }}
                        }}
                    }}
                    type
                    }}
                }}
                }}
            }}
            }}
            """
        }
        return query

    def parse_graphql_response(self, response_json: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        entity_relationships = {}
        try:
            results = response_json["data"]["actor"]["entity"]["relatedEntities"]["results"]
            for relationship in results:
                source_data = relationship["source"]["entity"]
                target_data = relationship["target"]["entity"]

                source_entity = self.create_entity_from_data(source_data)
                target_entity = self.create_entity_from_data(target_data)

                if source_entity.guid not in entity_relationships:
                    entity_relationships[source_entity.guid] = []

                entity_relationships[source_entity.guid].append({
                    "source": source_entity,
                    "target": target_entity,
                })

        except KeyError:
            logger.warning("Unexpected response structure")
        return entity_relationships

    def create_entity_from_data(self, data: Dict[str, Any]) -> Entity:
        tags = [Tag(tag["key"], tag["values"]) for tag in data.get("tags", [])]
        return Entity(
            guid=data["guid"],
            name=data["name"],
            type=data["type"],
            entityType=data["entityType"],
            domain=data["domain"],
            tags=tags
        )

    def determine_icon(self, entity: Entity) -> str:
        # Modify this method to better use the entity's type, domain, etc.
        if "redis" in entity.name.lower():
            return self.icons["redis"]
        elif "database" in entity.entityType.lower():
            return self.icons["database"]
        else:
            return self.icons["default"]

    def add_node(self, entity: Entity):
        if entity and not any(node["id"] == entity.guid for node in self.topology["nodes"]):
            self.topology["nodes"].append({
                "id": entity.guid,
                "name": entity.name,
                "group": 1,
                "icon": self.determine_icon(entity),
                "additionalInfo": f"Entity in the network: {entity.entityType} - {entity.domain}",
                "issue": entity.guid == self.entity_id,
                "tags": {tag.key: tag.values for tag in entity.tags},
                "type": entity.type,
                "entityType": entity.entityType,
                "domain": entity.domain,
            })

    def add_link(self, source_id: str, target_id: str):
        if not any(link["source"] == source_id and link["target"] == target_id for link in self.topology["links"]):
            self.topology["links"].append({
                "source": source_id,
                "target": target_id,
                "value": 1,
            })

    def process_entity_relationships(self, entity: Entity, current_depth: int):
        if current_depth > self.depth or entity.guid in self.processed_entities:
            logger.info(f"Skipping entity: {entity.name} ({entity.guid})")
            return

        self.processed_entities.add(entity.guid)

        entity_relationships = self.fetch_entity_relationships(entity.guid)

        for relationship in entity_relationships.get(entity.guid, []):
            source_entity = relationship["source"]
            target_entity = relationship["target"]

            if source_entity and target_entity:
                self.add_node(source_entity)
                self.add_node(target_entity)
                self.add_link(source_entity.guid, target_entity.guid)
                logger.info(f"Added link: {source_entity.name} -> {target_entity.name}")

                if target_entity.guid not in self.processed_entities:
                    self.entities_to_process.append((target_entity, current_depth + 1))

    def generate(self):
        while self.entities_to_process:
            entity, current_depth = self.entities_to_process.popleft()
            self.process_entity_relationships(entity, current_depth)

        data = {"nodes": self.topology["nodes"], "links": self.topology["links"]}
        
        with open("topology-sync-v3.json", "w") as outfile:
            json.dump(data, outfile)
            logger.info("Topology data saved to topology.json")
        
        return data

        # response = requests.post("http://localhost:8005/data", json=data)
        # if response.status_code == 200:
        #     logger.info(f"Topology diagram successfully generated: {response.text}")
        #     return response.text
        # else:
        #     logger.error(f"Failed to generate topology diagram: {response.status_code}")
        #     return f"Error in generating topology diagram\n{response.text}"



# Example of how to use this class
if __name__ == "__main__":
    # generator = TopologyGenerator(entity_id="MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAxNjU5MzQ0NQ")
    generator = TopologyGenerator(entity_id="MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", depth=1)
    result = generator.generate()
    print(json.dumps(result, indent=2))
