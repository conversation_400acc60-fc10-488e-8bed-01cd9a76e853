# to test gen topology function from New Relic UI only query

from dotenv import load_dotenv

load_dotenv(override=True)

import os
import requests
import json
import browser_cookie3
import random

def generate_topology(entity_id):

    entity_guids = [entity_id]
    hops = 2
    hop_filters = [
        {
            "hop": 1,
            "filters": [
                {
                    "direction": "BOTH",
                    "toEntityDomainTypes": {
                        "exclude": [
                            {"domain": "AIOPS", "type": "CONDITION"},
                            {"domain": "AIOPS", "type": "ISSUE"},
                            {"domain": "REF", "type": "REPOSITORY"},
                            {"domain": "SYNTH", "type": "SECURE_CRED"},
                            {"domain": "SYNTH", "type": "PRIVATE_LOCATION"},
                            {"domain": "VIZ", "type": "DASHBOARD"},
                        ]
                    },
                    "fromEntityDomainTypes": {
                        "exclude": [
                            {"domain": "AIOPS", "type": "CONDITION"},
                            {"domain": "AIOPS", "type": "ISSUE"},
                            {"domain": "REF", "type": "REPOSITORY"},
                            {"domain": "SYNTH", "type": "SECURE_CRED"},
                            {"domain": "SYNTH", "type": "PRIVATE_LOCATION"},
                            {"domain": "VIZ", "type": "DASHBOARD"},
                        ]
                    },
                }
            ],
        },
        {
            "hop": 2,
            "filters": [
                {
                    "direction": "BOTH",
                    "toEntityDomainTypes": {
                        "exclude": [
                            {"domain": "AIOPS", "type": "CONDITION"},
                            {"domain": "AIOPS", "type": "ISSUE"},
                            {"domain": "REF", "type": "REPOSITORY"},
                            {"domain": "SYNTH", "type": "SECURE_CRED"},
                            {"domain": "SYNTH", "type": "PRIVATE_LOCATION"},
                            {"domain": "VIZ", "type": "DASHBOARD"},
                        ]
                    },
                    "fromEntityDomainTypes": {
                        "exclude": [
                            {"domain": "AIOPS", "type": "CONDITION"},
                            {"domain": "AIOPS", "type": "ISSUE"},
                            {"domain": "REF", "type": "REPOSITORY"},
                            {"domain": "SYNTH", "type": "SECURE_CRED"},
                            {"domain": "SYNTH", "type": "PRIVATE_LOCATION"},
                            {"domain": "VIZ", "type": "DASHBOARD"},
                        ]
                    },
                    "entityHealth": {
                        "alertingAtLeastOnceSince": 1712072032109,
                        "anomalousAtLeastOnceSince": 1712072032109,
                    },
                }
            ],
        },
    ]

    def get_entities_and_relationships(entity_guids, hops, hop_filters, cursor=None):
        url = "https://one.newrelic.com/graphql"

        query = """
        query GetEntitiesAndRelationshipsQuery($entityGuids: [EntityGuid!], $hops: Int, $hopFilters: [EntityRelationshipHopFilter!], $cursor: String) {
          actor {
            entities(guids: $entityGuids) {
              guid
              name
              alertSeverity
              accountId
              domain
              type
              entityType
              reporting
              account {
                id
                name
                __typename
              }
              relationshipTraversal(hops: $hops, hopFilters: { hopFilters: $hopFilters }, cursor: $cursor) {
                nextCursor
                results {
                  type
                  source {
                    guid
                    entity {
                      guid
                      name
                      alertSeverity
                      accountId
                      domain
                      type
                      entityType
                      reporting
                      account {
                        id
                        name
                        __typename
                      }
                      __typename
                    }
                    __typename
                  }
                  target {
                    guid
                    entity {
                      guid
                      name
                      alertSeverity
                      accountId
                      domain
                      type
                      entityType
                      reporting
                      account {
                        id
                        name
                        __typename
                      }
                      __typename
                    }
                    __typename
                  }
                  __typename
                }
                __typename
              }
              __typename
            }
            __typename
          }
        }
        """

        variables = {
            "entityGuids": entity_guids,
            "hops": hops,
            "hopFilters": hop_filters,
            "cursor": cursor,
        }
        headers = {
            "x-account-id": "1093620",
            "x-api-key": os.environ["NEWRELIC_USER_KEY"],
            "Origin": "https://one.newrelic.com",
            "authority": "generativeai.service.newrelic.com",
            "referer": "https://one.newrelic.com/",
            "Content-Type": "application/json",
        }
        cj = browser_cookie3.chrome()

        response = requests.post(
            url,
            json={"query": query, "variables": variables},
            cookies=cj,
            headers=headers,
        )

        if response.status_code == 200:
            data = response.json()
            return data
        else:
            print(f"Request failed with status code {response.status_code}")

    def determine_icon(entity_type):
        icon_map = {
            "APPLICATION": "static/App-Services.svg",
            "CONTAINER": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/pod-256.png",
            "SERVICE": "static/External-Service.svg",
            "DB_INSTANCE": "https://upload.wikimedia.org/wikipedia/commons/c/c5/201603_database.png",
            "HOST": "static/Virtual-Machine.svg",
        }
        return icon_map.get(
            entity_type,
            "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png",
        )

    def process_entity_data(entity_id, data):
        nodes = []
        links = []

        def process_entity(entity):
            # skip if entity is already in the list
            for node in nodes:
                if node["id"] == entity["guid"]:
                    return

            if entity["name"] is not None:
                if entity["name"] == ("Support - Workload"):
                    return
            additional_info = f"Alert Severity: {entity['alertSeverity']}\nDomain: {entity['domain']}\nType: {entity['type']}\n"
            for tag in entity.get("tags") or []:
                additional_info += f"{tag['key']}: {', '.join(tag['values'])}\n"

            issue = entity["alertSeverity"] == "CRITICAL"
            if entity_id == entity["guid"]:
                issue = True

            node = {
                "id": entity["guid"],
                "name": entity["name"],
                "group": random.randint(1, 4),
                "icon": determine_icon(entity["type"]),
                "additionalInfo": additional_info.strip(),
                "issue": issue,
            }

            nodes.append(node)

        def process_edge(edge):
            source_id = edge["source"]["guid"]
            target_id = edge["target"]["guid"]

            link = {
                "source": source_id,
                "target": target_id,
                "value": 1,
            }

            if edge["source"]["entity"]["name"] is not None:
                if edge["source"]["entity"]["name"] == ("Support - Workload"):
                    return
            if edge["target"]["entity"]["name"] is not None:
                if edge["target"]["entity"]["name"] == ("Support - Workload"):
                    return

            links.append(link)
            process_entity(edge["source"]["entity"])
            process_entity(edge["target"]["entity"])

        if data["data"]["actor"]["entities"][0]["relationshipTraversal"] is not None:
            results = data["data"]["actor"]["entities"][0]["relationshipTraversal"][
                "results"
            ]
        else:
            results = []

        for result in results:
            process_edge(result)

        return nodes, links

    rel_data = get_entities_and_relationships(entity_guids, hops, hop_filters)
    # print(json.dumps(rel_data, indent=4))
    # save rel_data to a file with random name
    # randon file name
    file_name = "rel_data" + str(random.randint(1, 1000)) + ".json"
    print(f"Saving rel_data to file: {file_name}")
    
    with open(file_name, "w") as file:
        json.dump(rel_data, file, indent=4, ensure_ascii=False)


    nodes, links = process_entity_data(entity_id, rel_data)

    data = {"nodes": nodes, "links": links}
    print(json.dumps(data, indent=2))

    # d3_server = os.environ["D3_SERVER"] + f"/{issue_id}"
    # response = requests.post(d3_server, json=data)

    # if response.status_code == 200:
    #     print(response.text)
    #     return f"Generated Topology Link: {os.environ["D3_SERVER_GLOBAL_URL"]}/{issue_id}"
    # else:
    #     print(response.status_code)
    #     return "Error in generating topology diagram\n" + response.text


args = {"entity_id":"MTA5MzYyMHxBUE18QVBQTElDQVRJT058MTAyNDk5MTQyMg"}

generate_topology(**args)