import json
import os
import sys
import time
import threading
import requests
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
from collections import deque, defaultdict
from typing import Any, Dict, List, Optional, Set, Tuple, Union
import re
import logging
from dataclasses import dataclass, field
from dotenv import load_dotenv
from azure.cosmos import CosmosClient

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.misc import light_gpt4_wrapper_autogen, light_gpt3_wrapper_autogen

# Load environment variables
load_dotenv(override=True)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Assuming Entity and Tag classes are defined elsewhere
# For the purpose of this code, I'll define simple versions
@dataclass
class Tag:
    key: str
    values: List[str]

@dataclass
class Entity:
    guid: str
    name: str
    type: str
    entityType: str
    domain: str
    tags: List[Tag] = field(default_factory=list)

    def get_tag_value(self, key: str) -> List[str]:
        for tag in self.tags:
            if tag.key == key:
                return tag.values
        return []

class TopologyGenerator:
    def __init__(self, entity_id: str, depth: int = 2, filters: Dict[str, Any] = None, max_workers: int = 10):
        if not entity_id:
            raise ValueError("Entity ID cannot be `None` or empty")
        self.entity_id = entity_id
        self.depth = depth
        self.filters = filters if filters is not None else {}
        self.endpoint = os.getenv("NEWRELIC_API_ENDPOINT", "https://api.newrelic.com/graphql")
        self.api_key = os.getenv("NEWRELIC_API_KEY")
        self.account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
        self.icons = {
            "default": "static/icons/svc.svg",
            "aws_redis": "static/icons/aws_redis.png",
            "aws_opensearch": "static/icons/aws_opensearch.png",
            "elasticsearch": "static/icons/elasticsearch.png",
            "redis": "https://redis.com/wp-content/themes/wpx/assets/images/logo-redis.svg",
            "database": "static/icons/database.png",
            "pod": "static/icons/pod.svg",
            "node": "static/icons/node.svg",
            "deploy": "static/icons/deploy.svg",
            "external": "static/icons/external-service.svg",
            "container": "static/icons/containerinstances.png",
        }
        self.topology = {"nodes": [], "links": []}
        self.processed_entities = set()
        
        # Initialize the queue with the root entity details
        root_entity = self.fetch_entity_details(self.entity_id)
        self.entities_to_process = deque([(root_entity, 0)])  # Initialize with Entity object
        
        self.max_workers = max_workers
        self.lock = threading.Lock()
    
    def fetch_entity_relationships(self, entity_id: str) -> Dict[str, List[Dict[str, Any]]]:
        headers = {"Content-Type": "application/json", "API-Key": self.api_key}
        query = self.build_graphql_query(entity_id)
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.post(self.endpoint, headers=headers, json=query)
                response.raise_for_status()  # Raise an error for bad responses
                return self.parse_graphql_response(response.json())
            except requests.exceptions.ConnectionError as e:
                logger.error(f"Connection error on attempt {attempt + 1} for entity {entity_id}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait before retrying
                else:
                    logger.error(f"Max retries exceeded for entity {entity_id}.")
                    return {}
            except requests.exceptions.HTTPError as e:
                logger.error(f"HTTP error for entity {entity_id}: {e}")
                return {}
            except Exception as e:
                logger.error(f"Unexpected error for entity {entity_id}: {e}")
                return {}

    def fetch_entity_details(self, entity_id: str) -> Entity:
        """Fetch the entity details for the root entity."""
        entity_relationships = self.fetch_entity_relationships(entity_id)
        # Get the root entity details from the relationships (source or target)
        for key in entity_relationships:
            for relationship in entity_relationships[key]:
                if relationship["source"].guid == entity_id:
                    return relationship["source"]
                if relationship["target"].guid == entity_id:
                    return relationship["target"]
        # Fallback in case entity not found in relationships
        return Entity(guid=entity_id, name="Unknown", type="", entityType="", domain="")

    def build_graphql_query(self, entity_id: str) -> Dict[str, str]:
        query = {
            "query": f"""
            {{
            actor {{
                entity(guid: "{entity_id}") {{
                name
                relatedEntities {{
                    results {{
                    source {{
                        entity {{
                        guid
                        name
                        type
                        entityType
                        domain
                        tags {{
                            key
                            values
                        }}
                        }}
                    }}
                    target {{
                        entity {{
                        guid
                        name
                        type
                        entityType
                        domain
                        tags {{
                            key
                            values
                        }}
                        }}
                    }}
                    type
                    }}
                }}
                }}
            }}
            }}
            """
        }
        return query

    def parse_graphql_response(self, response_json: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        entity_relationships = {}
        try:
            results = response_json["data"]["actor"]["entity"]["relatedEntities"]["results"]
            for relationship in results:
                source_data = relationship["source"]["entity"]
                target_data = relationship["target"]["entity"]

                source_entity = self.create_entity_from_data(source_data)
                target_entity = self.create_entity_from_data(target_data)

                # Skip relationships with invalid entities
                if source_entity is None or target_entity is None:
                    logger.info(f"Skipping relationship due to unavailable entity.")
                    continue

                for entity in [source_entity, target_entity]:
                    if entity.guid not in entity_relationships:
                        entity_relationships[entity.guid] = []

                # Add the relationship to both entities' lists
                entity_relationships[source_entity.guid].append({
                    "source": source_entity,
                    "target": target_entity,
                })
                entity_relationships[target_entity.guid].append({
                    "source": source_entity,
                    "target": target_entity,
                })

        except KeyError:
            logger.warning("Unexpected response structure")
        except Exception as e:
            logger.error(f"Error parsing GraphQL response: {e}")
        return entity_relationships

    def create_entity_from_data(self, data: Dict[str, Any]) -> Optional[Entity]:
        # Check if the entity is unavailable or invalid
        if data.get("entityType") == "UNAVAILABLE_ENTITY" or data.get("name") is None or data.get("type") in ["NA", None]:
            logger.info(f"Skipping unavailable or invalid entity: {data.get('guid')}")
            return None

        tags_data = data.get("tags", [])
        if tags_data is None:
            tags_data = []
        tags = [Tag(tag["key"], tag["values"]) for tag in tags_data]
        
        return Entity(
            guid=data.get("guid", ""),
            name=data.get("name", "Unknown"),
            type=data.get("type", "NA"),
            entityType=data.get("entityType", "NA"),
            domain=data.get("domain", "NA"),
            tags=tags
        )

    def filter_entity(self, entity: Entity) -> bool:
        if entity is None:
            return False

        # Exclude by entity type
        if "types" in self.filters and entity.type in self.filters["types"]:
            return False

        # Exclude by entity ID
        if "ids" in self.filters and entity.guid in self.filters["ids"]:
            return False

        # Exclude by name patterns (supports partial matches)
        if "name_patterns" in self.filters:
            if any(re.search(pattern, entity.name, re.IGNORECASE) for pattern in self.filters["name_patterns"]):
                return False
        
        # Exclude by tags
        if "tags" in self.filters:
            for key, excluded_values in self.filters["tags"].items():
                tag_values = entity.get_tag_value(key)
                logger.info(f"Checking tag '{key}' with values: {tag_values} against excluded values: {excluded_values}")
                if any(value in excluded_values for value in tag_values):
                    logger.info(f"Entity filtered out due to tag: {entity.name} ({entity.guid})")
                    return False

        return True

    def determine_icon(self, entity: Entity) -> str:
        type_icon_mapping = {
            "KUBERNETES_POD": self.icons["pod"],
            "KUBERNETES_DEPLOYMENT": self.icons["deploy"],
            "CONTAINER": self.icons["container"],
            "HOST": self.icons["node"],
            # Add more mappings as needed
        }

        # Check for specific name patterns to determine the icon
        if entity.name and entity.name.endswith("cache.amazonaws.com"):
            return self.icons["aws_redis"]
        elif entity.name and "es.amazonaws.com" in entity.name:
            return self.icons["aws_opensearch"]
        elif entity.name and "redis" in entity.name.lower():
            return self.icons["redis"]
        
        # If name match fails, check the entity type
        if entity.type in type_icon_mapping:
            return type_icon_mapping[entity.type]

        return self.icons["default"]  # Fallback to default icon

    def add_node(self, entity: Entity):
        if entity is None:
            return

        with self.lock:
            if not any(node["id"] == entity.guid for node in self.topology["nodes"]):
                if not self.filter_entity(entity):
                    logger.info(f"Entity filtered out: {entity.name} ({entity.guid})")
                    return
                
                self.topology["nodes"].append({
                    "id": entity.guid,
                    "name": entity.name or "Unknown",
                    "issue": False,
                    "group": 1,
                    "icon": self.determine_icon(entity),
                    "properties": {
                        "account": entity.get_tag_value("account"),
                        "accountId": entity.get_tag_value("accountId"),
                        "domain": entity.domain,
                        "entityType": entity.entityType,
                        "type": entity.type,
                        **{tag.key: tag.values for tag in entity.tags},
                    },
                    "alt_names": [],  # You can populate alternative names here if available
                    "entity_id": entity.guid,
                    "type": entity.type,
                    "entityType": entity.entityType,
                    "domain": entity.domain,
                    "data_source": "live",  # This can be adjusted as needed
                })
                logger.info(f"Added node: {entity.name} ({entity.guid})")

    def add_link(self, source_id: str, target_id: str):
        with self.lock:
            # Ensure that both source and target entities exist in the topology
            source_entity = next((node for node in self.topology["nodes"] if node["id"] == source_id), None)
            target_entity = next((node for node in self.topology["nodes"] if node["id"] == target_id), None)
            
            if source_entity and target_entity:
                # Check if the link already exists (in either direction for undirected graphs)
                if not any((link["source"] == source_id and link["target"] == target_id) or
                           (link["source"] == target_id and link["target"] == source_id)
                           for link in self.topology["links"]):
                    self.topology["links"].append({
                        "id": f"{source_id}->{target_id}",
                        "source": source_id,
                        "target": target_id,
                        "link_type": "connected_to",  # Adjust as needed
                        "properties": {},
                        "data_source": "live",
                        "value": 1,
                    })
                    logger.info(f"Added link: {source_id} -> {target_id}")
            else:
                logger.info(f"Link not added due to missing entities: {source_id} -> {target_id}")

    def process_entity_relationships(self, entity: Entity, current_depth: int):
        if entity is None:
            return []

        if current_depth > self.depth:
            logger.info(f"Max depth reached for entity: {entity.name} ({entity.guid})")
            return []
        
        with self.lock:
            if entity.guid in self.processed_entities:
                logger.info(f"Already processed entity: {entity.name} ({entity.guid})")
                return []
            self.processed_entities.add(entity.guid)
        
        entity_relationships = self.fetch_entity_relationships(entity.guid)
        new_entities = []

        for relationship in entity_relationships.get(entity.guid, []):
            source_entity = relationship["source"]
            target_entity = relationship["target"]

            # Skip if either entity is None
            if source_entity is None or target_entity is None:
                continue

            # Determine the other entity in the relationship
            if source_entity.guid == entity.guid:
                other_entity = target_entity
            else:
                other_entity = source_entity

            self.add_node(source_entity)
            self.add_node(target_entity)
            self.add_link(source_entity.guid, target_entity.guid)

            if other_entity.guid not in self.processed_entities:
                new_entities.append((other_entity, current_depth + 1))
                logger.info(f"Queued entity for processing: {other_entity.name} ({other_entity.guid})")

        return new_entities

    def generate(self):
        entity_count = 0
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {executor.submit(self.process_entity_relationships, entity, depth) for entity, depth in self.entities_to_process}
            
            while futures:
                done, _ = concurrent.futures.wait(futures, return_when=concurrent.futures.FIRST_COMPLETED)
                for future in done:
                    futures.remove(future)
                    try:
                        new_entities = future.result()
                        for new_entity, new_depth in new_entities:
                            if new_depth <= self.depth:
                                futures.add(executor.submit(self.process_entity_relationships, new_entity, new_depth))
                        entity_count += 1
                    except Exception as e:
                        logger.error(f"Error processing entity: {e}")

        logger.info(f"Processed {entity_count} entities")
        data = {"nodes": self.topology["nodes"], "links": self.topology["links"]}
        return data

icons = {
    "default": "static/icons/svc.svg",
    "aws_redis": "static/icons/aws_redis.png",
    "aws_opensearch": "static/icons/aws_opensearch.png",
    "elasticsearch": "static/icons/elasticsearch.png",
    "redis": "https://redis.com/wp-content/themes/wpx/assets/images/logo-redis.svg",
    "database": "static/icons/database.png",
    "pod": "static/icons/pod.svg",
    "node": "static/icons/node.svg",
    "deploy": "static/icons/deploy.svg",
    "external": "static/icons/external-service.svg",
    "container": "static/icons/containerinstances.png",
}

# Custom matching rules
CUSTOM_MATCH_RULES = [
    {
        "source_contains": "debezium",
        "target_contains": "es.amazonaws.com",
        "match_type": "partial"
    },
    {
        "source_contains": "db-postgres",
        "target_contains": "postgresql",
        "match_type": "partial"
    },
    {
        "source_matches": r"cache-redis-\d+",
        "target_matches": r"redis-\w+",
        "match_type": "regex"
    },
    # Add more custom matching rules as needed
]

def custom_match(source_value: str, target_value: str) -> bool:
    for rule in CUSTOM_MATCH_RULES:
        if rule["match_type"] == "partial":
            if (rule.get("source_contains", "") in source_value and 
                rule.get("target_contains", "") in target_value):
                logger.info(f"Custom match (partial): {source_value} -> {target_value}")
                return True
        elif rule["match_type"] == "regex":
            if (re.search(rule.get("source_matches", ""), source_value) and 
                re.search(rule.get("target_matches", ""), target_value)):
                logger.info(f"Custom match (regex): {source_value} -> {target_value}")
                return True
    return False

MATCHING_RULES = [
    {
        "source_data": "K8S",
        "target_data": "APM",
        "source_type": "CONTAINER",
        "target_type": "APPLICATION",
        "source_pattern": r"polaris-(.+)",
        "target_pattern": r"nmdm-\w+-\d+-polaris-(.+)",
        "match_field": "name"
    },
    {
        "source_data": "K8S",
        "target_data": "APM",
        "source_type": "CONTAINER",
        "target_type": "SERVICE",
        "source_pattern": r"(jasperreports)",
        "target_pattern": r"(jasperreports)",
        "match_field": "name"
    },
    {
        "source_data": "K8S",
        "target_data": "APM",
        "source_type": "CONTAINER",
        "target_type": "SERVICE",
        "source_pattern": r"(debezium)",
        "target_pattern": r"vpc.+(es\.amazonaws\.com)",
        "match_field": "name",
        "custom_match": custom_match
    },
    
]

def load_json(file_path: str) -> Dict:
    with open(file_path, 'r') as file:
        data = json.load(file)
    logger.info(f"Loaded JSON file: {file_path}")
    return data

def extract_pattern(value: str, pattern: str) -> Union[str, None]:
    match = re.match(pattern, value)
    return match.group(1) if match else None

def match_nodes(source_node: Dict, target_node: Dict, rule: Dict) -> bool:
    if (rule["source_type"] == "ANY" or source_node.get("type") == rule["source_type"]) and \
       (rule["target_type"] == "ANY" or target_node.get("type") == rule["target_type"]):
        
        source_value = source_node.get(rule['match_field'])
        target_value = target_node.get(rule['match_field'])
        
        if source_value and target_value:
            # Try pattern matching first
            source_extract = extract_pattern(source_value, rule['source_pattern'])
            target_extract = extract_pattern(target_value, rule['target_pattern'])
            
            if source_extract and target_extract:
                if source_extract.lower() == target_extract.lower():
                    logger.info(f"Pattern match: {source_value} -> {target_value}")
                    return True
            
            # If pattern matching fails, try custom matching if defined
            if 'custom_match' in rule and callable(rule['custom_match']):
                return rule['custom_match'](source_value, target_value)
    
    return False

def merge_topologies(topology_data: List[Tuple[str, Dict]]) -> Dict:
    merged_data = {"nodes": [], "links": []}
    node_map = {}  # Map to store nodes by their id
    source_nodes = defaultdict(list)  # Map to store nodes by their source

    # First pass: Collect all nodes and organize them by source
    for source, data in topology_data:
        for node in data.get("nodes", []):
            node_id = node["id"]
            if node_id not in node_map:
                merged_data["nodes"].append(node)
                node_map[node_id] = node
                source_nodes[source].append(node)
        logger.info(f"Collected {len(data.get('nodes', []))} nodes from source: {source}")

    # Second pass: Collect all existing links
    for _, data in topology_data:
        merged_data["links"].extend(data.get("links", []))
    logger.info(f"Collected {len(merged_data['links'])} existing links")

    # Third pass: Create new links based on matching rules
    new_links_count = 0
    for rule in MATCHING_RULES:
        logger.info(f"Applying matching rule: {rule['source_data']} -> {rule['target_data']}")
        for source_node in source_nodes[rule["source_data"]]:
            for target_node in source_nodes[rule["target_data"]]:
                if match_nodes(source_node, target_node, rule):
                    merged_data["links"].append({
                        "id": f"{source_node['id']}->{target_node['id']}",
                        "source": source_node["id"],
                        "target": target_node["id"],
                        "value": 1,
                        "link_type": "contains",
                        "properties": {},
                        "data_source": "rule_match",
                    })
                    new_links_count += 1
                    logger.info(f"New link created: {source_node['name']} -> {target_node['name']}")
    
    logger.info(f"Created {new_links_count} new links based on matching rules")
    logger.info(f"Final topology: {len(merged_data['nodes'])} nodes, {len(merged_data['links'])} links")

    return merged_data

def process_topology_data(input_data: Union[List[Tuple[str, str]], List[Tuple[str, Dict]]]) -> Dict:
    topology_data = []
    for item in input_data:
        if isinstance(item[1], str):
            # Input is a tuple of (source, file path)
            topology_data.append((item[0], load_json(item[1])))
        elif isinstance(item[1], dict):
            # Input is a tuple of (source, dictionary)
            topology_data.append(item)
            logger.info(f"Loaded data from dictionary for source: {item[0]}")
        else:
            error_msg = "Input must be either (source, file path) or (source, dictionary) tuples"
            logger.error(error_msg)
            raise ValueError(error_msg)

    return merge_topologies(topology_data)

# Generate Topology for cluster from different sources and merge them
def generate_topology_for_cluster(cluster_id: str, filters: Dict, sources: Dict) -> List[Tuple[str, Dict]]:

    topology_data = []

    for source, entity_info in sources.items():
        generator = TopologyGenerator(
            entity_id=entity_info["entity_id"],
            depth=entity_info["depth"],
            filters=filters
        )
        result = generator.generate()
        topology_data.append((source, result))

    return topology_data

# Additional source of topology data
def generate_topology_by_type(node_type: dict):

    logger.info(f"Generating topology for node type: {node_type}")

    domain = node_type["domain"]
    infrastructure_integration_type = node_type["infrastructure_integration_type"]
    icon = node_type["icon"]

    # get the entities from newrelic by entity type
    entities = get_entities_by_type(
        domain=domain,
        infrastructure_integration_type=infrastructure_integration_type
    )

    # create nodes from the entities
    nodes = []
    for entity in entities:
        nodes.append({
            "id": entity["guid"],
            "name": entity["name"],
            "type": entity["type"],
            "domain": entity["domain"],
            "entityType": entity["entityType"],
            "issue": False,
            "group": 1,
            "icon": icon,
            "data_source": "newrelic",
            "alt_names": [],
            "entity_id": entity["guid"],
            "properties": {
                "type": entity["type"],
                "domain": entity["domain"],
                "entityType": entity["entityType"],
                **{tag["key"]: tag["values"] for tag in entity["tags"]}
            }
        })

    # seperate topology by cluster for MDM
    mdm_clusters = {'na1', 'na2', 'ap1', 'ap2'}
    
    # use LLM to determine the cluster for each node
    system_message = """
    You are a helpful AI assistant that categorizes nodes based on their names and tags into specific cluster IDs: na1, na2, ap1 and ap2. 
    The categorization should prioritize the name first, then the tags. 
    one example if the name or tags contain "mig-primary-ap1-eks-rds" or "ap1migrdspolaris" or "ap1-mig-rds-polaris", it belongs to the ap1 cluster ID. 
    another example if the name or tags contain "mig-primary-na1-eks-rds" or "na1migrdspolaris" or "na1-mig-rds-polaris", it belongs to the na1 cluster ID.
    Additionally, identify the product the node belongs to from the options: polaris, aquila, mig, kong. Similiar to the cluster ID, the product should be prioritize from the name first, then the tags. 
    Return the result in JSON format with cluster IDs as keys and an object containing name, IDs, and product as values. Add other nodes to new section other key. Process all the nodes and return the result.
    """
    logger.info(f"Getting cluster nodes for: {domain} - {infrastructure_integration_type}")
    cluster_nodes = light_gpt4_wrapper_autogen(json.dumps(nodes), return_json=True, system_message=system_message)
    # cluster_nodes = json.loads(cluster_nodes_json)
    logger.info(f"Cluster nodes: {cluster_nodes}")


def get_entities_by_type(domain: str, infrastructure_integration_type: str) -> List[Dict]:

    logger.info(f"Getting entities by type: {domain} - {infrastructure_integration_type}")
    
    api_key = os.getenv("NEWRELIC_API_KEY")
    url = "https://api.newrelic.com/graphql"
    
    query = f"""
    {{
      actor {{
        entitySearch(
          queryBuilder: {{domain: {domain}, infrastructureIntegrationType: {infrastructure_integration_type}}}
        ) {{
          results {{
            entities {{
              guid
              name
              type
              domain
              entityType
              tags {{
                key
                values
              }}
            }}
          }}
        }}
      }}
    }}
    """

    headers = {
        "Content-Type": "application/json",
        "API-Key": api_key,
    }

    try:
        response = requests.post(url, json={"query": query}, headers=headers)
        response.raise_for_status()  # Raises an HTTPError for bad responses
        
        # get the entities from the response
        entities = response.json()["data"]["actor"]["entitySearch"]["results"]["entities"]
        logger.info(f"Found {len(entities)} entities")
        if not entities:
            logger.warning("No entities found in the response.")
        return entities

    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed: {e}")
    except json.JSONDecodeError:
        logger.error("Failed to parse the response as JSON")
    except ValueError as e:
        logger.error(f"Error: {e}")
    except KeyError as e:
        logger.error(f"Key error while accessing entities: {e}")
            
    return []



# Example of how to use this class
if __name__ == "__main__":
    filters = {
        "types": ["KUBERNETES_JOB", "KUBERNETES_PERSISTENTVOLUME", "KUBERNETES_PERSISTENTVOLUMECLAIM", "KUBERNETES_CRONJOB", "ISSUE", "WORKLOAD"],  # Example: exclude entities of types "APPLICATION" and "SERVICE"
        "ids": ["MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "MTA5MzYyMHxJTkZSQXxOQXw0MjY5NTY2NDczOTc0NTUwMTcx", "MTA5MzYyMHxJTkZSQXxOQXw1ODQ4OTM1MDE3ODE5NDg2MTg3", "MTA5MzYyMHxJTkZSQXxOQXwzMDY0MDYzODIwODkxNDE3OTY", "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw", "MTA5MzYyMHxJTkZSQXxOQXw3NjE4MzgwMzk4MDI2Mzk2MjQ2"],  # Example: exclude specific IDs
        "name_patterns": [],  # Example: exclude entities with these name patterns
        "tags": {"k8s.namespaceName": ["kube-system", "newrelic-metrics", "newrelic-logging", "newrelic", "nr-custom-metrics"]}  # Example: exclude entities with specific tags
    }

    # all mdm kubernetes cluster
    sources = {
        "nvu": {
            "K8S": {"entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNTUwNzA5MDQ5MTg2MTYzNjg2Nw", "depth": 2}
        },
        "mlu": {
            "K8S": {"entity_id": "MTA5MzYyMHxJTkZSQXxOQXw3NjE4MzgwMzk4MDI2Mzk2MjQ2", "depth": 2}
        },
        "uku": {
            "K8S": {"entity_id": "MTA5MzYyMHxJTkZSQXxOQXwxMzE0NDcwODE0NDUwMzQ2MjAy", "depth": 2}
        },
        "ttu": {
            "K8S": {"entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNzMyNzI1NjA5NTc5NDU3NjExOQ", "depth": 2}
        },
        "tku": {
            "K8S": {"entity_id": "MTA5MzYyMHxJTkZSQXxOQXwtNzE2ODIwMDc2NDIxMjc2MDY2MA", "depth": 2}
        }
    }

    for cluster_id, source in sources.items():
        topology_data = generate_topology_for_cluster(cluster_id=cluster_id, filters=filters, sources=source)
        filename = f"data/topology/neurons_{cluster_id}.json"
        with open(filename, "w") as outfile:
            json.dump(topology_data, outfile, indent=2)
            logger.info(f"Topology saved to {filename}")
        time.sleep(5)

    # temp
    # sources = {}

    # client = CosmosClient(os.getenv("COSMOS_ENDPOINT"), os.getenv("COSMOS_KEY"))
    # database_name = "service-map"

    # for cluster_id, entity_info in sources.items():
    #     topology_data = generate_topology_for_cluster(cluster_id=cluster_id, filters=filters, sources=entity_info)
    #     # print(json.dumps(topology_data, indent=2))

    #     merged_topology = process_topology_data(topology_data)
    #     # print(json.dumps(merged_topology, indent=2))

    #     # store it in file for reference
    #     filename = f"merged_topology_{cluster_id}_v2.json"
    #     with open(os.path.join(os.path.dirname(__file__), filename), "w") as outfile:
    #         json.dump(merged_topology, outfile, indent=2)
    #         logger.info(f"Merged topology saved to {filename}")

    #     # store the merged topology to cosmos db
    #     container_name = cluster_id
    #     database = client.get_database_client(database_name)
    #     container = database.get_container_client(container_name)

    #     # add additional properties to the merged topology
    #     merged_topology["id"] = cluster_id
    #     merged_topology["node"] = cluster_id
        
    #     container.upsert_item(merged_topology)

    #     # sleep for 2 seconds
    #     time.sleep(2)

    # # generate topology by type
    # node_types = [
    #     {"domain": "INFRA", "infrastructure_integration_type": "AWS_RDS_DB_INSTANCE", "icon": "database"},
    # ]

    # for node_type in node_types:
    #     generate_topology_by_type(node_type)


    