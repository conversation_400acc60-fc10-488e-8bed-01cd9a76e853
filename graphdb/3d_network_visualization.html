<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Network Visualization</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background-color: #000;
        }
        canvas {
            display: block;
        }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-family: Arial, sans-serif;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            max-width: 300px;
            pointer-events: none;
        }
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            font-family: Arial, sans-serif;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div id="loading">Loading network data...</div>
    <div id="info">
        <h3>Network Visualization</h3>
        <p>Nodes: <span id="nodeCount">0</span></p>
        <p>Connections: <span id="linkCount">0</span></p>
        <p>Hover over nodes to see details</p>
        <p>Use mouse to rotate, scroll to zoom</p>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/libs/stats.min.js"></script>
    <script>
        // Global variables
        let scene, camera, renderer, controls;
        let nodeObjects = new Map();
        let linkObjects = [];
        let raycaster, mouse;
        let hoveredNode = null;
        let stats;
        
        // Node and link data
        let networkData;
        
        // Color scheme based on node groups
        const colorScheme = [
            0x4285F4, // Google Blue
            0xEA4335, // Google Red
            0xFBBC05, // Google Yellow
            0x34A853, // Google Green
            0x9C27B0, // Purple
            0x00BCD4, // Teal
            0xFF9800, // Orange
            0x795548, // Brown
            0x607D8B, // Blue Grey
            0xE91E63  // Pink
        ];
        
        // Initialize the scene
        function init() {
            // Scene setup
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x050505);
            
            // Camera setup
            camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1, 10000);
            camera.position.z = 1500;
            
            // Renderer setup
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(window.devicePixelRatio);
            document.body.appendChild(renderer.domElement);
            
            // Controls setup
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.25;
            
            // Add ambient light
            const ambientLight = new THREE.AmbientLight(0xcccccc, 0.5);
            scene.add(ambientLight);
            
            // Add directional light
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(1, 1, 1).normalize();
            scene.add(directionalLight);
            
            // Setup raycaster for interaction
            raycaster = new THREE.Raycaster();
            mouse = new THREE.Vector2();
            
            // Stats for performance monitoring
            stats = new Stats();
            stats.dom.style.position = 'absolute';
            stats.dom.style.top = '0px';
            stats.dom.style.right = '0px';
            document.body.appendChild(stats.dom);
            
            // Event listeners
            window.addEventListener('resize', onWindowResize);
            document.addEventListener('mousemove', onMouseMove);
            
            // Load network data
            loadNetworkData();
        }
        
        // Load network data from JSON file
        async function loadNetworkData() {
            try {
                const response = await fetch('nx_subgraph_nvu_v3.json');
                networkData = await response.json();
                document.getElementById('nodeCount').textContent = networkData.nodes.length;
                document.getElementById('linkCount').textContent = networkData.links.length;
                
                // Create the network visualization
                createNetwork();
                
                // Hide loading indicator
                document.getElementById('loading').style.display = 'none';
            } catch (error) {
                console.error('Error loading network data:', error);
                document.getElementById('loading').textContent = 'Error loading network data. Please try again.';
            }
        }
        
        // Create the network visualization
        function createNetwork() {
            // Create nodes
            const nodePositions = calculateNodePositions();
            
            networkData.nodes.forEach(node => {
                const nodeGroup = node.group || 0;
                const nodeColor = colorScheme[nodeGroup % colorScheme.length];
                
                // Create node object
                const geometry = new THREE.SphereGeometry(
                    node.issue ? 8 : 4, // Issues are larger
                    16, 16
                );
                const material = new THREE.MeshPhongMaterial({
                    color: nodeColor,
                    emissive: new THREE.Color(nodeColor).multiplyScalar(0.2),
                    specular: 0xffffff,
                    shininess: 50,
                    transparent: true,
                    opacity: 0.9
                });
                
                const nodeMesh = new THREE.Mesh(geometry, material);
                
                // Position node
                const position = nodePositions.get(node.id);
                nodeMesh.position.set(position.x, position.y, position.z);
                
                // Add custom properties
                nodeMesh.userData = {
                    id: node.id,
                    name: node.name,
                    group: nodeGroup,
                    isIssue: node.issue,
                    properties: node.properties,
                    originalColor: nodeColor
                };
                
                // Add to scene and store reference
                scene.add(nodeMesh);
                nodeObjects.set(node.id, nodeMesh);
                
                // Add glow effect for issues
                if (node.issue) {
                    const glowGeometry = new THREE.SphereGeometry(12, 16, 16);
                    const glowMaterial = new THREE.MeshBasicMaterial({
                        color: nodeColor,
                        transparent: true,
                        opacity: 0.2
                    });
                    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
                    nodeMesh.add(glowMesh);
                }
            });
            
            // Create links
            networkData.links.forEach((link, index) => {
                const sourceNode = nodeObjects.get(link.source);
                const targetNode = nodeObjects.get(link.target);
                
                if (!sourceNode || !targetNode) {
                    console.warn(`Missing node for link: ${link.source} -> ${link.target}`);
                    return;
                }
                
                // Get positions
                const sourcePos = sourceNode.position;
                const targetPos = targetNode.position;
                
                // Create link geometry
                const points = [
                    new THREE.Vector3(sourcePos.x, sourcePos.y, sourcePos.z),
                    new THREE.Vector3(targetPos.x, targetPos.y, targetPos.z)
                ];
                
                const linkGeometry = new THREE.BufferGeometry().setFromPoints(points);
                
                // Create link material
                const linkColor = new THREE.Color(0xaaaaaa);
                const linkMaterial = new THREE.LineBasicMaterial({
                    color: linkColor,
                    transparent: true,
                    opacity: 0.3
                });
                
                // Create line
                const line = new THREE.Line(linkGeometry, linkMaterial);
                
                // Add custom properties
                line.userData = {
                    source: link.source,
                    target: link.target,
                    value: link.value || 1,
                    type: link.type || 'connection'
                };
                
                // Add to scene and store reference
                scene.add(line);
                linkObjects.push({
                    line: line,
                    source: sourceNode,
                    target: targetNode,
                    particles: []
                });
                
                // Create particles for flow animation (for a subset of links to avoid performance issues)
                if (index % 3 === 0) {
                    createFlowParticles(line, sourcePos, targetPos);
                }
            });
            
            // Start animation loop
            animate();
        }
        
        // Calculate positions for nodes using a force-directed algorithm
        function calculateNodePositions() {
            const positions = new Map();
            const scale = 1000;
            
            // First, assign random positions
            networkData.nodes.forEach(node => {
                positions.set(node.id, {
                    x: (Math.random() - 0.5) * scale,
                    y: (Math.random() - 0.5) * scale,
                    z: (Math.random() - 0.5) * scale
                });
            });
            
            // Simple implementation to avoid crowding
            // In a real implementation, you might want to use a more sophisticated algorithm
            networkData.nodes.forEach(node => {
                const group = node.group || 0;
                const angle = (group / 10) * Math.PI * 2;
                const radius = scale * 0.4;
                
                const pos = positions.get(node.id);
                pos.x += Math.cos(angle) * radius * 0.3;
                pos.y += Math.sin(angle) * radius * 0.3;
                pos.z += (group % 3 - 1) * radius * 0.2;
            });
            
            return positions;
        }
        
        // Create particles for flow animation
        function createFlowParticles(line, sourcePos, targetPos) {
            const numParticles = 5;
            const particles = [];
            
            for (let i = 0; i < numParticles; i++) {
                const particleGeometry = new THREE.SphereGeometry(1.5, 8, 8);
                const particleMaterial = new THREE.MeshBasicMaterial({
                    color: 0x88ccff,
                    transparent: true,
                    opacity: 0.7
                });
                
                const particle = new THREE.Mesh(particleGeometry, particleMaterial);
                
                // Random starting position along the line
                const t = Math.random();
                particle.position.copy(sourcePos).lerp(targetPos, t);
                
                particle.userData = {
                    speed: 0.005 + Math.random() * 0.01,
                    t: t
                };
                
                scene.add(particle);
                particles.push(particle);
            }
            
            // Find the link object and add the particles
            const linkObject = linkObjects.find(obj => obj.line === line);
            if (linkObject) {
                linkObject.particles = particles;
            }
        }
        
        // Update flow animation
        function updateFlowAnimation() {
            linkObjects.forEach(link => {
                const sourcePos = link.source.position;
                const targetPos = link.target.position;
                
                // Update particles
                link.particles.forEach(particle => {
                    // Update position along the path
                    particle.userData.t += particle.userData.speed;
                    
                    // Reset when reaching the end
                    if (particle.userData.t > 1.0) {
                        particle.userData.t = 0.0;
                    }
                    
                    // Update particle position
                    particle.position.copy(sourcePos).lerp(targetPos, particle.userData.t);
                    
                    // Pulse effect
                    const pulse = Math.sin(Date.now() * 0.005 + particle.userData.t * 10) * 0.5 + 0.5;
                    particle.material.opacity = 0.3 + pulse * 0.4;
                    particle.scale.set(0.7 + pulse * 0.6, 0.7 + pulse * 0.6, 0.7 + pulse * 0.6);
                });
                
                // Update line geometry to match node positions
                const points = [
                    new THREE.Vector3(sourcePos.x, sourcePos.y, sourcePos.z),
                    new THREE.Vector3(targetPos.x, targetPos.y, targetPos.z)
                ];
                
                link.line.geometry.setFromPoints(points);
                link.line.geometry.verticesNeedUpdate = true;
            });
        }
        
        // Handle mouse move for node interaction
        function onMouseMove(event) {
            // Calculate mouse position in normalized device coordinates
            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            
            // Raycast to find intersections
            raycaster.setFromCamera(mouse, camera);
            
            // Get objects intersecting the ray
            const nodeArray = Array.from(nodeObjects.values());
            const intersects = raycaster.intersectObjects(nodeArray);
            
            // Reset previously hovered node
            if (hoveredNode) {
                hoveredNode.material.emissive.setHex(
                    hoveredNode.userData.originalColor * 0.2
                );
                hoveredNode.material.opacity = 0.9;
            }
            
            // Set new hovered node
            if (intersects.length > 0) {
                hoveredNode = intersects[0].object;
                hoveredNode.material.emissive.setHex(0xffffff);
                hoveredNode.material.opacity = 1.0;
                
                // Show node info
                document.getElementById('info').innerHTML = `
                    <h3>${hoveredNode.userData.name}</h3>
                    <p>ID: ${hoveredNode.userData.id}</p>
                    <p>Group: ${hoveredNode.userData.group}</p>
                    <p>Type: ${hoveredNode.userData.isIssue ? 'Issue' : 'Entity'}</p>
                    ${hoveredNode.userData.properties ? `
                        <p>Account: ${hoveredNode.userData.properties.account || 'N/A'}</p>
                        <p>Type: ${hoveredNode.userData.properties.type || 'N/A'}</p>
                    ` : ''}
                `;
            } else {
                hoveredNode = null;
                document.getElementById('info').innerHTML = `
                    <h3>Network Visualization</h3>
                    <p>Nodes: <span id="nodeCount">${networkData ? networkData.nodes.length : 0}</span></p>
                    <p>Connections: <span id="linkCount">${networkData ? networkData.links.length : 0}</span></p>
                    <p>Hover over nodes to see details</p>
                    <p>Use mouse to rotate, scroll to zoom</p>
                `;
            }
        }
        
        // Handle window resize
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            // Update controls
            controls.update();
            
            // Update flow animation
            if (networkData) {
                updateFlowAnimation();
            }
            
            // Render scene
            renderer.render(scene, camera);
            
            // Update stats
            stats.update();
        }
        
        // Start the visualization
        init();
    </script>
</body>
</html>
