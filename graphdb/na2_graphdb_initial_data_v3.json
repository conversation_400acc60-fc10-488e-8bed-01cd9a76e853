{"nodes": [{"id": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "node_type": "KUBERNETESCLUSTER", "namespace": null, "properties": {"alert_severity": "NOT_CONFIGURED", "domain": "INFRA", "additionalInfo": "Entity in the network: GENERIC_INFRASTRUCTURE_ENTITY - INFRA", "issue": true, "account": ["IvantiCloud - US"], "accountId": ["1093620"], "displayName": ["primary-na1-eks"], "instrumentation.provider": ["newRelic"], "trustedAccountId": ["1093620"]}, "alt_names": [], "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "name": "primary-na2-eks", "entity_id": "primary-na2-eks", "group": 4, "data_source": "static"}, {"id": "MTA5MzYyMHxJTkZSQXxOQXwzOTQwNzk3NDU5NTkyMzU2Nzg2", "node_type": "KUBERNETES_JOB", "namespace": null, "properties": {"alert_severity": "NOT_ALERTING", "domain": "INFRA", "additionalInfo": "Entity in the network: GENERIC_INFRASTRUCTURE_ENTITY - INFRA", "issue": false, "account": ["IvantiCloud - US"], "accountId": ["1093620"], "k8s.clusterName": ["primary-na1-eks"], "k8s.jobName": ["kong-provisioning"], "k8s.namespaceName": ["na1-kong-100111-kong-00002"], "newrelic.integrationName": ["com.newrelic.kubernetes"], "newrelic.integrationVersion": ["3.24.1"], "trustedAccountId": ["1093620"]}, "alt_names": [], "icon": "https://raw.githubusercontent.com/kubernetes/community/master/icons/png/resources/unlabeled/svc-256.png", "name": "kong-provisioning", "entity_id": "kong-provisioning-entity", "group": 1, "data_source": "static"}], "links": [{"id": "conn1", "source_id": "MTA5MzYyMHxJTkZSQXxOQXwyNzcyMjk0MTA0MDU5MjA5NDQy", "target_id": "MTA5MzYyMHxJTkZSQXxOQXwzOTQwNzk3NDU5NTkyMzU2Nzg2", "link_type": "contains", "properties": {}, "data_source": "static", "value": 1}]}