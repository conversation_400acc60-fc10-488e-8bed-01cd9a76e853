import json
import rustworkx as rx

def create_subgraph_json(input_file, start_node_id, output_file):
    # Load JSON data
    with open(input_file, 'r') as f:
        data = json.load(f)

    data = data[0][1]

    # Create a graph
    G = rx.PyGraph()

    # Add nodes
    node_map = {}
    reverse_node_map = {}
    for i, node in enumerate(data['nodes']):
        node_map[node['id']] = i
        reverse_node_map[i] = node['id']
        G.add_node(node)

    # Add edges
    for link in data['links']:
        source = node_map[link['source']]
        target = node_map[link['target']]
        G.add_edge(source, target, link)

    # Get nodes within 2 hops
    start_node = node_map[start_node_id]
    distances = rx.dijkstra_shortest_path_lengths(G, start_node)
    subgraph_nodes = [node for node, distance in distances.items() if distance <= 2]

    # Create subgraph
    subgraph = G.subgraph(subgraph_nodes)

    # Prepare subgraph data in original format
    subgraph_data = {
        'nodes': [],
        'links': []
    }

    # Add nodes to subgraph data
    for node in subgraph.node_indices():
        subgraph_data['nodes'].append(G[node])

    # Add links to subgraph data
    for edge in subgraph.edge_list():
        source_id = reverse_node_map[edge[0]]
        target_id = reverse_node_map[edge[1]]
        edge_data = G.get_edge_data(edge[0], edge[1])
        edge_data['source'] = source_id
        edge_data['target'] = target_id
        subgraph_data['links'].append(edge_data)

    # Write subgraph data to JSON file
    with open(output_file, 'w') as f:
        json.dump(subgraph_data, f, indent=2)

    print(f"Subgraph data written to {output_file}")

# Example usage
input_file = 'topology_nvu_v3.json'
start_node_id = "MTA5MzYyMHxJTkZSQXxOQXw0NTU4NDkzMjc2Mjg1MjU1ODY"
output_file = 'nx_subgraph_nvu_v3.json'

create_subgraph_json(input_file, start_node_id, output_file)