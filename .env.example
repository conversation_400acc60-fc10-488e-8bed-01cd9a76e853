# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=incident_manager
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password

# New Relic API
NEWRELIC_API_KEY=your_api_key
NEWRELIC_ACCOUNT_ID=your_account_id

# OpenAI API
OPENAI_API_KEY=your_api_key

# Azure Service Bus (optional, for alert ingestion)
AZURE_SERVICE_BUS_CONNECTION_STRING=your_connection_string
AZURE_SERVICE_BUS_QUEUE_NAME=your_queue_name

# VictorOps API
VICTOROPS_API_ID=your_victorops_api_id
VICTOROPS_API_KEY=your_victorops_api_key

# Application Settings
APP_HOST=0.0.0.0
APP_PORT=8000 