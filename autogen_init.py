from fastapi import FastAPI
import logging
import os
import json
import threading
from tempfile import TemporaryDirectory

from tenacity import retry, stop_after_attempt, wait_random_exponential, retry_if_exception_type
from openai import OpenAIError

import agentops

from autogen_mods.modified_group_chat import ModifiedG<PERSON>C<PERSON>, ModifiedGroupChatManager
from autogen import (
    config_list_from_json,
    runtime_logging,
    gather_usage_summary,
    Cache,
    GroupChat,
    GroupChatManager,
)

from azure.cosmos import CosmosClient
from azure.cosmos.exceptions import CosmosHttpResponseError

from azure.servicebus import ServiceBusClient, AutoLockRenewer

from utils import azure_devops
from utils.html_formatter import nr_issue_to_html2

from agents.agents import (
    user_proxy,
    code_reviewer,
    agent_awareness_expert,
    python_expert,
    function_calling_agent,
    creative_solution_agent,
    first_principles_thinker_agent,
    project_manager_agent,
    task_history_review_agent,
    task_comprehension_agent,
    rca_agent,
    web_surfer,
)

from dotenv import load_dotenv

load_dotenv(override=True)

logging.basicConfig(level=logging.INFO)

# Initialize the FastAPI app with a lifespan function
def lifespan(app: FastAPI):
    # Startup code
    message_thread = threading.Thread(target=receive_messages, daemon=True)
    message_thread.start()
    yield
    # Shutdown code
    # If needed, handle cleanup here

app = FastAPI(lifespan=lifespan)

SERVICE_BUS_CONNECTION_STR = os.getenv("SERVICE_BUS_CONNECTION_STR")
SERVICE_BUS_QUEUE_NAME = "nr-alert"

# Load configuration for the LLM
config_list4 = config_list_from_json(
    "OAI_CONFIG_LIST", filter_dict={"model": os.environ.get("AZURE_GPT4_MODEL_NAME", "obv-gpt4t0125")}
)

llm_config4 = {
    "config_list": config_list4,
    "temperature": 0.1,
}

AGENT_TEAM = [
    user_proxy,
    code_reviewer,
    agent_awareness_expert,
    python_expert,
    function_calling_agent,
    creative_solution_agent,
    first_principles_thinker_agent,
    project_manager_agent,
    task_history_review_agent,
    task_comprehension_agent,
    rca_agent,
    web_surfer,
]

# Initialize Cosmos DB client
cosmos_client = CosmosClient(os.getenv("COSMOS_ENDPOINT"), os.getenv("COSMOS_KEY"))
database_name = "alert"
container_name = "alert"
database = cosmos_client.get_database_client(database_name)
container = database.get_container_client(container_name)

@retry(
    stop=stop_after_attempt(3),
    wait=wait_random_exponential(multiplier=1, max=60),
    retry=retry_if_exception_type(OpenAIError),
    reraise=True
)
def process_message(message_body: str):
    try:
        message_json = json.loads(message_body)
        message_id = message_json.get("issueId")
        if message_id:
            # agentops.init(api_key=os.environ["AGENTOPS_API_KEY"], tags=[message_id])

            print(f"Processing message with id: {message_id}")

            token = os.environ["ADO_PERSONAL_ACCESS_TOKEN"]
            organization = os.environ["ADO_ORG_NAME"]
            project = os.environ["ADO_PROJECT_NAME"]

            ado = azure_devops.AzureDevops(token, organization, project, api_version=os.environ["ADO_API_VERSION"])

            if message_json.get("state") != "ACTIVATED":
                print(f"Skipping processing for non-activated issue: {message_id}")
                return

            existing_work_item_id = None
            processed_message = False
            try:
                existing_item = container.read_item(item=message_id, partition_key=message_id)
                existing_work_item_id = existing_item.get('work_item_id')
                processed_message = existing_item.get('status') == "completed"
            except CosmosHttpResponseError as e:
                if e.status_code == 404:
                    print(f"No existing item found for issue ID: {message_id}")
                else:
                    raise

            if processed_message:
                print(f"Message already processed: {message_id}")
                return

            if existing_work_item_id:
                print(f"Work item already exists with ID: {existing_work_item_id}")
                work_item = {"id": existing_work_item_id}
            else:
                issue_in_html = nr_issue_to_html2(message_json)
                title = issue_in_html["title"]
                description_in_html = issue_in_html["description"]
                area_path = os.environ["ADO_AREA_PATH"]
                work_item = ado.create_workitem("task", title, description_in_html, area_path)

            message = f"""Process the following Issue Detail {message_json} from NewRelic. Perform the following in order:
1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
2) Search the web for detailed information on the error logs for root cause, remedy, and preventive action.
3) Fetch additional metrics from New Relic, Kubernetes, and Azure (if applicable) using the new functions: fetch_newrelic_metrics, fetch_kubernetes_metrics, and fetch_azure_metrics. Analyze these metrics to provide more context to the root cause analysis.
4) Root Cause Analysis should be in the following JSON format:
{{
    "customer_affected": "<customer_affected>",
    "date_incident_began": "<date_incident_began>",
    "date_incident_resolved": "<date_incident_resolved>",
    "teams_involved": "<teams_involved>",
    "customer_impact": "<customer_impact>",
    "root_cause": "<root_cause>",
    "remedy": "<remedy>",
    "preventive_action": "<preventive_action>",
    "additional_metrics": "<summary_of_additional_metrics>"
}}
Also, issue detail in the following JSON format:
issue_details = {{
    "issue_id": "<issue_id>",
    "issue_url": "<issue_url>",
    "title": "<title>",
    "priority": "<priority>",
    "impacted_entities": "<impacted_entities>",
    "total_incidents": "<total_incidents>",
    "state": "<state>",
    "trigger": "<trigger>",
    "is_correlated": "<is_correlated>",
    "created_at": "<created_at>",
    "updated_at": "<updated_at>",
    "sources": "<sources>",
    "alert_policy_names": "<alert_policy_names>",
    "alert_condition_names": "<alert_condition_names>",
    "workflow_name": "<workflow_name>",
    "chart_url": "<chart_url>"
}}.
5) Based on the RCA, generate comprehensive suggested actions. Each action should include:
   - Specific, actionable description
   - Priority level (HIGH/MEDIUM/LOW)
   - Timeline for implementation
   - Team/Role responsible
   - Success criteria
   - Type (immediate fix or preventive measure)
   The suggested actions will be automatically generated and stored when you use the generate_topology_report function.
6) Send the affected entity detail, Issue Detail, and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id {work_item["id"]} using the "generate_topology_report" function with help of functioncallingagent. This will also generate and store the suggested actions.
7) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search the internet/web for detailed information on the root cause, remedy, and preventive action.
IMPORTANT: The suggested actions should be specific and actionable, with clear ownership and measurable success criteria.
"""

            # Update or create the issue details in Cosmos DB for tracking
            container.upsert_item({
                "id": message_id,
                "work_item_id": work_item["id"],
                "nodes": [],
                "links": [],
                "rca": "",
                "issue_info": message_json,
                "autogen_message": message,
                "status": "processing"
            })

            groupchat = GroupChat(
                agents=AGENT_TEAM,
                messages=[],
                max_round=100,
            )
            manager = GroupChatManager(groupchat=groupchat, llm_config=llm_config4)

            with TemporaryDirectory() as cache_path_root:
                with Cache.disk(cache_path_root=cache_path_root) as cache:
                    print(
                        f"Initiating chat with agent using message '{message_id}'",
                        flush=True,
                    )

                    user_proxy.initiate_chat(
                        manager,
                        clear_history=False,
                        message=message,
                        cache=cache,
                    )

            usage_summary = gather_usage_summary(AGENT_TEAM)
            print("Total Usage Summary: ", usage_summary["usage_including_cached_inference"])
            print("Actual Usage Summary: ", usage_summary["usage_excluding_cached_inference"])

            # Close your AgentOps session to indicate that it completed.
            # agentops.end_session("Success")

            container.patch_item(
                item=message_id,
                partition_key=message_id,
                patch_operations=[
                    {"op": "replace", "path": "/status", "value": "completed"}
                ]
            )
        else:
            print("ID field not found in the message.")
    except json.JSONDecodeError as e:
        logging.error(f"Error decoding JSON message: {e}")
    except OpenAIError as e:
        logging.error(f"OpenAI API error: {e}")
    except AttributeError as e:
        logging.error(f"AttributeError: {e}")
        if "'str' object has no attribute 'choices'" in str(e):
            logging.error("Received unexpected string response from OpenAI API")
    except Exception as e:
        logging.error(f"Error processing message: {e}")

def receive_messages():
    try:
        with ServiceBusClient.from_connection_string(conn_str=SERVICE_BUS_CONNECTION_STR, logging_enable=True) as servicebus_client:
            receiver = servicebus_client.get_queue_receiver(queue_name=SERVICE_BUS_QUEUE_NAME)
            with receiver:
                auto_lock_renewer = AutoLockRenewer()
                for msg in receiver:
                    print(f"Received message: {msg}")
                    try:
                        # Register the message with AutoLockRenewer
                        auto_lock_renewer.register(receiver, msg, max_lock_renewal_duration=300)

                        message_body = str(msg)
                        process_message(message_body)

                        receiver.complete_message(msg)
                    except Exception as e:
                        logging.error(f"Error processing message: {e}")
                        try:
                            receiver.abandon_message(msg)
                        except Exception as abandon_error:
                            logging.error(f"Error abandoning message: {abandon_error}")
    except Exception as e:
        logging.error(f"Error in receive_messages: {e}")

@app.get("/")
def read_root():
    return {"message": "Service is running"}
