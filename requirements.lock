# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
aiohappyeyeballs==2.4.6
    # via aiohttp
aiohttp==3.11.13
    # via instructor
    # via langchain
aiosignal==1.3.2
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anthropic==0.49.0
    # via pydantic-ai-slim
anyio==4.8.0
    # via anthropic
    # via groq
    # via httpx
    # via openai
    # via sse-starlette
    # via starlette
    # via watchfiles
asttokens==2.4.1
    # via devtools
attrs==25.1.0
    # via aiohttp
azure-core==1.32.0
    # via azure-cosmos
    # via azure-servicebus
azure-cosmos==4.9.0
    # via obv-ai-incident-management
azure-servicebus==7.14.0
    # via obv-ai-incident-management
blinker==1.9.0
    # via flask
cachetools==5.5.2
    # via google-auth
certifi==2024.8.30
    # via httpcore
    # via httpx
    # via requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.3.2
    # via requests
click==8.1.8
    # via flask
    # via langgraph-cli
    # via typer
    # via uvicorn
cohere==5.14.0
    # via pydantic-ai-slim
colorama==0.4.6
    # via griffe
cryptography==43.0.3
    # via langgraph-api
deprecated==1.2.18
    # via opentelemetry-api
    # via opentelemetry-exporter-otlp-proto-http
    # via opentelemetry-semantic-conventions
devtools==0.12.2
    # via obv-ai-incident-management
distro==1.9.0
    # via anthropic
    # via groq
    # via openai
dnspython==2.7.0
    # via pymongo
docstring-parser==0.16
    # via instructor
et-xmlfile==2.0.0
    # via openpyxl
eval-type-backport==0.2.2
    # via mistralai
    # via pydantic-ai-slim
executing==2.2.0
    # via devtools
    # via logfire
fastapi==0.115.11
    # via obv-ai-incident-management
fastavro==1.10.0
    # via cohere
filelock==3.17.0
    # via huggingface-hub
flask==3.1.0
    # via obv-ai-incident-management
frozenlist==1.5.0
    # via aiohttp
    # via aiosignal
fsspec==2025.2.0
    # via huggingface-hub
fuzzywuzzy==0.18.0
    # via obv-ai-incident-management
google-auth==2.38.0
    # via pydantic-ai-slim
googleapis-common-protos==1.69.0
    # via opentelemetry-exporter-otlp-proto-http
griffe==1.6.0
    # via pydantic-ai-slim
groq==0.18.0
    # via pydantic-ai-slim
h11==0.14.0
    # via httpcore
    # via uvicorn
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via anthropic
    # via cohere
    # via groq
    # via langgraph-api
    # via langgraph-sdk
    # via langsmith
    # via mistralai
    # via openai
    # via pydantic-ai-slim
    # via pydantic-graph
httpx-sse==0.4.0
    # via cohere
huggingface-hub==0.29.1
    # via tokenizers
idna==3.7
    # via anyio
    # via httpx
    # via requests
    # via yarl
importlib-metadata==8.5.0
    # via opentelemetry-api
instructor==1.7.3
    # via obv-ai-incident-management
isodate==0.7.2
    # via azure-servicebus
itsdangerous==2.2.0
    # via flask
jinja2==3.1.6
    # via flask
    # via instructor
jiter==0.8.2
    # via anthropic
    # via instructor
    # via openai
jsonpatch==1.33
    # via langchain-core
jsonpath-python==1.0.6
    # via mistralai
jsonpointer==3.0.0
    # via jsonpatch
jsonschema-rs==0.20.0
    # via langgraph-api
langchain==0.3.19
    # via obv-ai-incident-management
langchain-core==0.3.40
    # via langchain
    # via langchain-openai
    # via langchain-text-splitters
    # via langgraph
    # via langgraph-api
    # via langgraph-checkpoint
    # via langgraph-prebuilt
langchain-openai==0.3.7
    # via obv-ai-incident-management
langchain-text-splitters==0.3.6
    # via langchain
langgraph==0.3.16
    # via langgraph-api
    # via obv-ai-incident-management
langgraph-api==0.0.27
    # via langgraph-cli
langgraph-checkpoint==2.0.16
    # via langgraph
    # via langgraph-api
    # via langgraph-prebuilt
langgraph-cli==0.1.74
    # via obv-ai-incident-management
langgraph-prebuilt==0.1.1
    # via langgraph
langgraph-sdk==0.1.53
    # via langgraph
    # via langgraph-api
langsmith==0.3.11
    # via langchain
    # via langchain-core
    # via langgraph-api
levenshtein==0.27.1
    # via python-levenshtein
logfire==3.9.0
    # via obv-ai-incident-management
logfire-api==3.6.4
    # via pydantic-ai-slim
    # via pydantic-graph
loguru==0.7.3
    # via obv-ai-incident-management
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
    # via werkzeug
mdurl==0.1.2
    # via markdown-it-py
mistralai==1.5.0
    # via pydantic-ai-slim
motor==3.7.0
    # via obv-ai-incident-management
msgpack==1.1.0
    # via langgraph-checkpoint
multidict==6.1.0
    # via aiohttp
    # via yarl
mypy-extensions==1.0.0
    # via typing-inspect
networkx==3.4.2
    # via obv-ai-incident-management
newrelic==10.8.1
    # via obv-ai-incident-management
numpy==2.2.3
    # via langchain
    # via pandas
openai==1.77.0
    # via instructor
    # via langchain-openai
    # via obv-ai-incident-management
    # via pydantic-ai-slim
openpyxl==3.1.5
    # via obv-ai-incident-management
opentelemetry-api==1.30.0
    # via opentelemetry-exporter-otlp-proto-http
    # via opentelemetry-instrumentation
    # via opentelemetry-sdk
    # via opentelemetry-semantic-conventions
opentelemetry-exporter-otlp-proto-common==1.30.0
    # via opentelemetry-exporter-otlp-proto-http
opentelemetry-exporter-otlp-proto-http==1.30.0
    # via logfire
opentelemetry-instrumentation==0.51b0
    # via logfire
opentelemetry-proto==1.30.0
    # via opentelemetry-exporter-otlp-proto-common
    # via opentelemetry-exporter-otlp-proto-http
opentelemetry-sdk==1.30.0
    # via logfire
    # via opentelemetry-exporter-otlp-proto-http
opentelemetry-semantic-conventions==0.51b0
    # via opentelemetry-instrumentation
    # via opentelemetry-sdk
orjson==3.10.15
    # via langgraph-api
    # via langgraph-sdk
    # via langsmith
packaging==24.2
    # via huggingface-hub
    # via langchain-core
    # via langsmith
    # via opentelemetry-instrumentation
pandas==2.2.3
    # via obv-ai-incident-management
propcache==0.3.0
    # via aiohttp
    # via yarl
protobuf==5.29.3
    # via googleapis-common-protos
    # via logfire
    # via opentelemetry-proto
psycopg==3.2.6
    # via obv-ai-incident-management
psycopg2-binary==2.9.10
    # via obv-ai-incident-management
pyasn1==0.6.1
    # via pyasn1-modules
    # via rsa
pyasn1-modules==0.4.1
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.10.6
    # via anthropic
    # via cohere
    # via fastapi
    # via groq
    # via instructor
    # via langchain
    # via langchain-core
    # via langsmith
    # via mistralai
    # via obv-ai-incident-management
    # via openai
    # via pydantic-ai-slim
    # via pydantic-graph
pydantic-ai==0.0.30
    # via obv-ai-incident-management
pydantic-ai-slim==0.0.30
    # via pydantic-ai
pydantic-core==2.27.2
    # via cohere
    # via instructor
    # via pydantic
pydantic-graph==0.0.30
    # via pydantic-ai-slim
pygments==2.19.1
    # via devtools
    # via rich
pyjwt==2.10.1
    # via langgraph-api
pymongo==4.11.3
    # via motor
python-dateutil==2.9.0.post0
    # via mistralai
    # via pandas
python-dotenv==1.0.1
    # via langgraph-cli
    # via obv-ai-incident-management
python-levenshtein==0.27.1
    # via obv-ai-incident-management
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via huggingface-hub
    # via langchain
    # via langchain-core
    # via obv-ai-incident-management
rapidfuzz==3.12.2
    # via levenshtein
regex==2024.11.6
    # via tiktoken
requests==2.32.3
    # via azure-core
    # via cohere
    # via huggingface-hub
    # via instructor
    # via langchain
    # via langsmith
    # via obv-ai-incident-management
    # via opentelemetry-exporter-otlp-proto-http
    # via pydantic-ai-slim
    # via requests-toolbelt
    # via tiktoken
requests-toolbelt==1.0.0
    # via langsmith
rich==13.9.4
    # via instructor
    # via logfire
    # via obv-ai-incident-management
    # via typer
rsa==4.9
    # via google-auth
shellingham==1.5.4
    # via typer
six==1.17.0
    # via asttokens
    # via azure-core
    # via python-dateutil
sniffio==1.3.1
    # via anthropic
    # via anyio
    # via groq
    # via openai
sqlalchemy==2.0.38
    # via langchain
sse-starlette==2.1.3
    # via langgraph-api
starlette==0.46.0
    # via fastapi
    # via langgraph-api
    # via sse-starlette
structlog==23.3.0
    # via langgraph-api
tenacity==9.0.0
    # via instructor
    # via langchain
    # via langchain-core
    # via langgraph-api
    # via obv-ai-incident-management
tiktoken==0.9.0
    # via langchain-openai
tokenizers==0.21.0
    # via cohere
tqdm==4.67.1
    # via huggingface-hub
    # via openai
typer==0.15.2
    # via instructor
types-requests==2.32.0.20250301
    # via cohere
typing-extensions==4.12.2
    # via anthropic
    # via anyio
    # via azure-core
    # via azure-cosmos
    # via azure-servicebus
    # via cohere
    # via fastapi
    # via groq
    # via huggingface-hub
    # via langchain-core
    # via logfire
    # via openai
    # via opentelemetry-sdk
    # via psycopg
    # via pydantic
    # via pydantic-core
    # via sqlalchemy
    # via typer
    # via typing-inspect
typing-inspect==0.9.0
    # via mistralai
tzdata==2025.2
    # via pandas
urllib3==2.3.0
    # via requests
    # via types-requests
uvicorn==0.34.0
    # via langgraph-api
    # via obv-ai-incident-management
    # via sse-starlette
watchfiles==1.0.4
    # via langgraph-api
werkzeug==3.1.3
    # via flask
wrapt==1.17.2
    # via deprecated
    # via opentelemetry-instrumentation
yarl==1.18.3
    # via aiohttp
zipp==3.21.0
    # via importlib-metadata
zstandard==0.23.0
    # via langsmith
