[project]
name = "obv-ai-incident-management"
version = "0.1.0"
description = "AI-powered incident management system integrating with New Relic and Azure services"
authors = [
    {name = "<PERSON><PERSON><PERSON>"}
]
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn>=0.24.0",
    "langchain>=0.0.335",
    "langgraph>=0.3.16",
    "langchain-openai>=0.0.1",
    "azure-servicebus>=7.11.4",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.4.2",
    "python-dotenv>=1.0.1",
    "pydantic-ai==0.0.30",
    "pyyaml>=6.0.2",
    "rich>=13.9.4",
    "langgraph-cli[inmem]>=0.1.74",
    "logfire>=3.9.0",
    "devtools>=0.12.2",
    "tenacity>=9.0.0",
    "instructor>=1.7.3",
    "networkx>=3.4.2",
    "fuzzywuzzy>=0.18.0",
    "requests>=2.32.3",
    "azure-cosmos>=4.9.0",
    "python-levenshtein>=0.27.1",
    "loguru>=0.7.3",
    "motor>=3.7.0",
    "newrelic>=10.8.1",
    "flask>=3.1.0",
    "psycopg>=3.2.6",
    "pandas>=2.2.3",
    "openpyxl>=3.1.5",
    "openai>=1.77.0",
]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = [
    "pytest>=7.4.3",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"

[tool.black]
line-length = 100
target-version = ["py310"]

[tool.isort]
profile = "black"
line_length = 100

[tool.hatch.build.targets.wheel]
packages = ["ai_incident_manager"]
