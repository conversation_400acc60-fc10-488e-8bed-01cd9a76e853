name: BlackDuck RAPID Scan

# Controls when the workflow will run
on:
  # Triggers the workflow on pull request event but only for the default branch
  pull_request:
    branches: [master]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      # Runs the action from the template repo
      - name: Run a BlackDuck Rapid Scan
        uses: ivantiinc/Composite-GitHub-Actions@blackduck_rapid_scan_07_2022
        with:
          # pass in token secret, can't retrieve it from the action itself
          SYNOPSYS_BLACKDUCK_TOKEN: ${{ secrets.SYNOPSYS_BLACKDUCK_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
