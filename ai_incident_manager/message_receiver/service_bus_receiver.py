"""
Azure Service Bus message receiver for New Relic alerts.
"""

import json
import os
import logging
import asyncio
from typing import Dict, Any, List, Callable, Optional, Union, Awaitable

from azure.servicebus.aio import ServiceBusClient
from azure.servicebus import ServiceBusMessage
import dotenv

from ai_incident_manager.parsers.alert_parser import extract_incident_info

dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ServiceBusReceiver:
    """Azure Service Bus receiver for processing alert messages."""
    
    def __init__(
        self,
        connection_string: Optional[str] = None,
        queue_name: Optional[str] = None,
        max_wait_time: int = 30,
        max_message_count: int = 10
    ):
        """
        Initialize the Service Bus receiver.
        
        Args:
            connection_string: Azure Service Bus connection string
            queue_name: Azure Service Bus queue name
            max_wait_time: Maximum wait time in seconds for receiving messages
            max_message_count: Maximum number of messages to receive in one batch
        """
        self.connection_string = connection_string or os.environ.get("AZURE_SERVICE_BUS_CONNECTION_STRING")
        self.queue_name = queue_name or os.environ.get("AZURE_SERVICE_BUS_QUEUE_NAME")
        self.max_wait_time = max_wait_time
        self.max_message_count = max_message_count
        
        if not self.connection_string:
            raise ValueError("Service Bus connection string not provided and not found in environment variables")
        
        if not self.queue_name:
            raise ValueError("Service Bus queue name not provided and not found in environment variables")
        
        self._client = None
        self._receiver = None
        
    async def initialize(self):
        """Initialize Service Bus client and receiver."""
        self._client = ServiceBusClient.from_connection_string(self.connection_string)
        
    async def close(self):
        """Close Service Bus client and receiver."""
        if self._client:
            await self._client.close()
            
    async def receive_messages(
        self,
        callback: Callable[[Dict[str, Any]], Union[Awaitable[None], None]],
        max_messages: Optional[int] = None,
        timeout: Optional[int] = None
    ):
        """
        Receive and process messages from the Service Bus queue.
        
        Args:
            callback: Callback function to process each message
            max_messages: Maximum number of messages to receive (None for unlimited)
            timeout: Timeout in seconds for receiving messages (None for unlimited)
        """
        if not self._client:
            await self.initialize()
            
        async with self._client.get_queue_receiver(self.queue_name) as receiver:
            self._receiver = receiver
            messages_processed = 0
            
            logger.info(f"Listening for messages on queue: {self.queue_name}")
            
            while True:
                if max_messages is not None and messages_processed >= max_messages:
                    logger.info(f"Reached maximum message count: {max_messages}")
                    break
                
                batch_receiver = await receiver.receive_messages(
                    max_message_count=self.max_message_count,
                    max_wait_time=timeout or self.max_wait_time
                )
                
                if not batch_receiver:
                    if timeout is not None:
                        logger.info("No messages received within timeout period")
                        break
                    continue
                
                logger.info(f"Received {len(batch_receiver)} messages")
                
                for message in batch_receiver:
                    try:
                        # Process the message
                        message_body = message.body.decode('utf-8')
                        message_data = json.loads(message_body)
                        
                        # Process via callback
                        result = callback(message_data)
                        if asyncio.iscoroutine(result):
                            await result
                        
                        # Complete the message to remove it from the queue
                        await receiver.complete_message(message)
                        messages_processed += 1
                        
                    except Exception as e:
                        logger.error(f"Error processing message: {str(e)}")
                        # Abandon the message to make it available again
                        await receiver.abandon_message(message)
                        
            self._receiver = None
            
    async def process_single_message(
        self,
        callback: Callable[[Dict[str, Any]], Union[Awaitable[None], None]]
    ) -> bool:
        """
        Process a single message from the Service Bus queue.
        
        Args:
            callback: Callback function to process the message
            
        Returns:
            True if a message was processed, False otherwise
        """
        if not self._client:
            await self.initialize()
            
        async with self._client.get_queue_receiver(self.queue_name) as receiver:
            messages = await receiver.receive_messages(max_message_count=1, max_wait_time=5)
            
            if not messages:
                return False
                
            message = messages[0]
            
            try:
                # Process the message
                message_body = message.body.decode('utf-8')
                message_data = json.loads(message_body)
                
                # Process via callback
                result = callback(message_data)
                if asyncio.iscoroutine(result):
                    await result
                
                # Complete the message to remove it from the queue
                await receiver.complete_message(message)
                return True
                
            except Exception as e:
                logger.error(f"Error processing message: {str(e)}")
                # Abandon the message to make it available again
                await receiver.abandon_message(message)
                return False
                
    @staticmethod
    def extract_incident_from_message(message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract incident information from an alert message.
        
        Args:
            message: Alert message from New Relic
            
        Returns:
            Incident information
        """
        return extract_incident_info(message)


async def process_alert_message(message: Dict[str, Any]) -> None:
    """
    Process a New Relic alert message.
    
    Args:
        message: Alert message data
    """
    logger.info(f"Processing alert: {message.get('title', 'Unknown Alert')}")
    
    # Extract incident information
    incident = ServiceBusReceiver.extract_incident_from_message(message)
    
    # Log the incident
    logger.info(
        f"Extracted incident: {incident['id']} - {incident['title']} "
        f"(Severity: {incident['severity']}, Entity: {incident['entities'][0]['type']})"
    )
    
    # Here you would typically:
    # 1. Save the incident to the database
    # 2. Enrich it with additional information
    # 3. Start the analysis workflow
    
    # For now, we just log it
    logger.info(f"Incident {incident['id']} processed successfully")
    
    return incident


async def start_receiver(
    max_messages: Optional[int] = None,
    timeout: Optional[int] = None
) -> None:
    """
    Start the Service Bus receiver to process alert messages.
    
    Args:
        max_messages: Maximum number of messages to receive (None for unlimited)
        timeout: Timeout in seconds for receiving messages (None for unlimited)
    """
    receiver = ServiceBusReceiver()
    
    try:
        await receiver.initialize()
        await receiver.receive_messages(
            callback=process_alert_message,
            max_messages=max_messages,
            timeout=timeout
        )
    finally:
        await receiver.close()


def create_test_alert() -> Dict[str, Any]:
    """
    Create a test alert message for development and testing.
    
    Returns:
        Test alert message
    """
    return {
        "id": "test-alert-1",
        "incident_id": "test-incident-1",
        "account_id": "12345",
        "account_name": "Test Account",
        "title": "High CPU usage on Kubernetes Pod",
        "description": "CPU usage above threshold: 90%. Current: 95% for pod: api-service-pod",
        "severity": "CRITICAL",
        "condition_name": "High CPU Usage",
        "policy_name": "Kubernetes Monitoring",
        "url": "https://onenr.io/test-alert",
        "created_at": "2023-10-15T12:30:45Z",
        "targets": [
            {
                "id": "test-pod-guid",
                "name": "api-service-pod",
                "type": "KUBERNETES_POD",
                "labels": ["service:api", "environment:production"]
            }
        ],
        "details": {
            "nrql_query": "SELECT average(cpuUtilization) FROM K8sPodSample WHERE podName = 'api-service-pod'",
            "violation_chart_url": "https://onenr.io/test-chart"
        }
    }


async def run_test_receiver():
    """Run a test of the receiver with a simulated message."""
    logger.info("Running test receiver with simulated message")
    
    # Create a test alert
    test_alert = create_test_alert()
    
    # Process it directly
    incident = await process_alert_message(test_alert)
    
    logger.info(f"Test completed. Incident ID: {incident['id']}")
    return incident


if __name__ == "__main__":
    """Run the receiver when script is executed directly."""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        # Run in test mode with a simulated message
        asyncio.run(run_test_receiver())
    else:
        # Run in normal mode, listening to Service Bus
        asyncio.run(start_receiver()) 