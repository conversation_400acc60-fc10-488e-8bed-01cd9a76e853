"""
Graph algorithms utility module for advanced entity relationship analysis.

This module provides specialized algorithms for analyzing entity relationships,
detecting cascading failures, and performing sophisticated graph operations
for incident management.
"""

import networkx as nx
import numpy as np
from typing import Dict, List, Tuple, Set, Optional, Any, Union
from collections import defaultdict, deque
from dataclasses import dataclass
from loguru import logger
import math
import random

from ai_incident_manager.models.graph_models import (
    NodeTypeEnum, RelationshipTypeEnum, GraphNodeModel, GraphEdgeModel,
    FailurePathModel, CriticalPathModel
)


@dataclass
class FailurePropagationResult:
    """Result of failure propagation simulation"""
    entity_id: str
    failure_probability: float
    propagation_time: float
    source_path: List[str]
    contributing_factors: List[str]


@dataclass
class CriticalityAnalysisResult:
    """Result of criticality analysis"""
    entity_id: str
    criticality_score: float
    centrality_scores: Dict[str, float]
    dependency_count: int
    dependent_count: int
    failure_impact_score: float


@dataclass
class ClusterAnalysisResult:
    """Result of graph clustering analysis"""
    clusters: Dict[int, List[str]]
    cluster_centralities: Dict[int, float]
    inter_cluster_edges: List[Tuple[str, str]]
    modularity: float
    silhouette_score: float


class GraphAlgorithms:
    """
    Advanced graph algorithms for entity relationship analysis.
    
    This class provides sophisticated algorithms for:
    1. Failure propagation simulation
    2. Criticality analysis
    3. Dependency impact assessment
    4. Graph clustering and community detection
    5. Resilience analysis
    6. Path optimization
    """
    
    def __init__(self):
        self.logger = logger.bind(name="graph_algorithms")
    
    def simulate_failure_propagation(
        self,
        graph: nx.DiGraph,
        initial_failures: List[str],
        propagation_steps: int = 10,
        threshold: float = 0.1,
        time_decay: float = 0.9,
        uncertainty_factor: float = 0.1
    ) -> Dict[str, FailurePropagationResult]:
        """
        Simulate failure propagation through the graph with temporal dynamics.
        
        Args:
            graph: Entity relationship graph
            initial_failures: List of initially failing entities
            propagation_steps: Number of propagation steps to simulate
            threshold: Minimum probability to consider a failure
            time_decay: Decay factor for propagation over time
            uncertainty_factor: Random factor for simulation uncertainty
            
        Returns:
            Dictionary mapping entity IDs to failure propagation results
        """
        # Initialize failure probabilities
        failure_probs = {node: 0.0 for node in graph.nodes()}
        propagation_times = {node: float('inf') for node in graph.nodes()}
        source_paths = {node: [] for node in graph.nodes()}
        
        # Set initial failures
        for entity in initial_failures:
            if entity in graph.nodes():
                failure_probs[entity] = 1.0
                propagation_times[entity] = 0.0
                source_paths[entity] = [entity]
        
        # Simulate propagation steps
        for step in range(propagation_steps):
            current_time = step + 1
            new_failures = {}
            
            # Process each node
            for node in graph.nodes():
                if failure_probs[node] >= threshold:
                    # Node has already failed or is failing
                    continue
                
                # Calculate incoming failure pressure
                incoming_pressure = 0.0
                best_path = []
                earliest_time = float('inf')
                
                for predecessor in graph.predecessors(node):
                    if failure_probs[predecessor] >= threshold:
                        # Get edge properties
                        edge_data = graph.get_edge_data(predecessor, node)
                        propagation_prob = edge_data.get('failure_propagation_probability', 0.5)
                        edge_weight = edge_data.get('weight', 1.0)
                        
                        # Calculate time-decayed pressure
                        time_factor = time_decay ** (current_time - propagation_times[predecessor])
                        pressure = failure_probs[predecessor] * propagation_prob * edge_weight * time_factor
                        
                        # Add uncertainty
                        pressure *= (1 + random.uniform(-uncertainty_factor, uncertainty_factor))
                        
                        incoming_pressure += pressure
                        
                        # Track best propagation path
                        if propagation_times[predecessor] < earliest_time:
                            earliest_time = propagation_times[predecessor]
                            best_path = source_paths[predecessor] + [node]
                
                # Update failure probability
                if incoming_pressure > 0:
                    # Get node's inherent resistance
                    node_resistance = 1.0 - graph.nodes[node].get('failure_probability', 0.1)
                    
                    # Calculate new failure probability
                    new_prob = min(1.0, incoming_pressure / node_resistance)
                    
                    if new_prob >= threshold and new_prob > failure_probs[node]:
                        new_failures[node] = new_prob
                        propagation_times[node] = current_time
                        source_paths[node] = best_path
            
            # Apply new failures
            for node, prob in new_failures.items():
                failure_probs[node] = prob
        
        # Generate results
        results = {}
        for node in graph.nodes():
            if failure_probs[node] >= threshold:
                # Identify contributing factors
                contributing_factors = []
                for predecessor in graph.predecessors(node):
                    if failure_probs[predecessor] >= threshold:
                        edge_data = graph.get_edge_data(predecessor, node)
                        rel_type = edge_data.get('relationship_type', 'UNKNOWN')
                        contributing_factors.append(f"{rel_type} dependency on {predecessor}")
                
                results[node] = FailurePropagationResult(
                    entity_id=node,
                    failure_probability=failure_probs[node],
                    propagation_time=propagation_times[node],
                    source_path=source_paths[node],
                    contributing_factors=contributing_factors
                )
        
        return results
    
    def analyze_criticality(
        self,
        graph: nx.DiGraph,
        weight_attribute: str = 'weight',
        include_centrality: bool = True
    ) -> Dict[str, CriticalityAnalysisResult]:
        """
        Analyze the criticality of each entity in the graph.
        
        Args:
            graph: Entity relationship graph
            weight_attribute: Edge attribute to use for weights
            include_centrality: Whether to include centrality calculations
            
        Returns:
            Dictionary mapping entity IDs to criticality analysis results
        """
        results = {}
        
        # Calculate various centrality measures
        centrality_scores = {}
        if include_centrality:
            try:
                centrality_scores['degree'] = dict(graph.degree())
                centrality_scores['in_degree'] = dict(graph.in_degree())
                centrality_scores['out_degree'] = dict(graph.out_degree())
                centrality_scores['betweenness'] = nx.betweenness_centrality(graph, weight=weight_attribute)
                centrality_scores['closeness'] = nx.closeness_centrality(graph, distance=weight_attribute)
                centrality_scores['eigenvector'] = nx.eigenvector_centrality(graph, weight=weight_attribute, max_iter=1000)
                centrality_scores['pagerank'] = nx.pagerank(graph, weight=weight_attribute)
            except:
                self.logger.warning("Some centrality measures failed to compute")
                centrality_scores = {
                    'degree': dict(graph.degree()),
                    'in_degree': dict(graph.in_degree()),
                    'out_degree': dict(graph.out_degree())
                }
        
        # Calculate criticality for each node
        for node in graph.nodes():
            node_data = graph.nodes[node]
            
            # Base criticality from node properties
            base_criticality = node_data.get('criticality_score', 0.5)
            
            # Dependency-based criticality
            dependency_count = graph.in_degree(node)
            dependent_count = graph.out_degree(node)
            
            # Centrality-based criticality
            centrality_contribution = 0.0
            node_centralities = {}
            
            if include_centrality:
                for measure, scores in centrality_scores.items():
                    score = scores.get(node, 0.0)
                    node_centralities[measure] = score
                    centrality_contribution += score
                
                centrality_contribution /= len(centrality_scores)
            
            # Failure impact analysis
            failure_impact = self._calculate_failure_impact(graph, node)
            
            # Combined criticality score
            criticality_score = (
                base_criticality * 0.3 +
                min(1.0, dependency_count / 10.0) * 0.2 +
                min(1.0, dependent_count / 10.0) * 0.3 +
                centrality_contribution * 0.1 +
                failure_impact * 0.1
            )
            
            results[node] = CriticalityAnalysisResult(
                entity_id=node,
                criticality_score=criticality_score,
                centrality_scores=node_centralities,
                dependency_count=dependency_count,
                dependent_count=dependent_count,
                failure_impact_score=failure_impact
            )
        
        return results
    
    def find_critical_paths_advanced(
        self,
        graph: nx.DiGraph,
        source: str,
        target: str,
        max_paths: int = 10,
        criticality_threshold: float = 0.5
    ) -> List[CriticalPathModel]:
        """
        Find critical paths between entities with advanced scoring.
        
        Args:
            graph: Entity relationship graph
            source: Source entity ID
            target: Target entity ID
            max_paths: Maximum number of paths to return
            criticality_threshold: Minimum criticality for path inclusion
            
        Returns:
            List of critical path models
        """
        critical_paths = []
        
        try:
            # Find all simple paths (with reasonable cutoff)
            all_paths = list(nx.all_simple_paths(graph, source, target, cutoff=8))
            
            # Score each path
            path_scores = []
            for path in all_paths:
                score = self._calculate_path_criticality_advanced(graph, path)
                if score >= criticality_threshold:
                    path_scores.append((path, score))
            
            # Sort by score (descending)
            path_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Convert to models
            for path, score in path_scores[:max_paths]:
                # Calculate path statistics
                total_weight = self._calculate_path_weight(graph, path)
                criticality_scores = {
                    entity: graph.nodes[entity].get('criticality_score', 0.0)
                    for entity in path
                }
                
                # Determine risk level
                risk_level = "low"
                if score >= 0.8:
                    risk_level = "critical"
                elif score >= 0.6:
                    risk_level = "high"
                elif score >= 0.4:
                    risk_level = "medium"
                
                critical_paths.append(CriticalPathModel(
                    source=source,
                    target=target,
                    paths=[path],
                    shortest_path=path,
                    criticality_scores=criticality_scores,
                    total_weight=total_weight,
                    risk_level=risk_level
                ))
            
        except nx.NetworkXNoPath:
            self.logger.warning(f"No path found between {source} and {target}")
        
        return critical_paths
    
    def detect_graph_clusters(
        self,
        graph: nx.DiGraph,
        method: str = 'spectral',
        num_clusters: Optional[int] = None
    ) -> ClusterAnalysisResult:
        """
        Detect clusters/communities in the graph.
        
        Args:
            graph: Entity relationship graph
            method: Clustering method ('spectral', 'modularity', 'leiden')
            num_clusters: Number of clusters (if None, auto-detect)
            
        Returns:
            Cluster analysis result
        """
        if graph.number_of_nodes() == 0:
            return ClusterAnalysisResult(
                clusters={},
                cluster_centralities={},
                inter_cluster_edges=[],
                modularity=0.0,
                silhouette_score=0.0
            )
        
        # Convert to undirected for clustering
        undirected = graph.to_undirected()
        
        if method == 'spectral':
            clusters = self._spectral_clustering(undirected, num_clusters)
        elif method == 'modularity':
            clusters = self._modularity_clustering(undirected)
        elif method == 'leiden':
            clusters = self._leiden_clustering(undirected)
        else:
            raise ValueError(f"Unknown clustering method: {method}")
        
        # Calculate cluster centralities
        cluster_centralities = {}
        for cluster_id, nodes in clusters.items():
            subgraph = undirected.subgraph(nodes)
            centrality = nx.density(subgraph) if subgraph.number_of_nodes() > 0 else 0.0
            cluster_centralities[cluster_id] = centrality
        
        # Find inter-cluster edges
        inter_cluster_edges = []
        node_to_cluster = {}
        for cluster_id, nodes in clusters.items():
            for node in nodes:
                node_to_cluster[node] = cluster_id
        
        for edge in undirected.edges():
            if node_to_cluster.get(edge[0]) != node_to_cluster.get(edge[1]):
                inter_cluster_edges.append(edge)
        
        # Calculate modularity
        modularity = self._calculate_modularity(undirected, clusters)
        
        # Calculate silhouette score (simplified)
        silhouette_score = self._calculate_silhouette_score(undirected, clusters)
        
        return ClusterAnalysisResult(
            clusters=clusters,
            cluster_centralities=cluster_centralities,
            inter_cluster_edges=inter_cluster_edges,
            modularity=modularity,
            silhouette_score=silhouette_score
        )
    
    def analyze_graph_resilience(
        self,
        graph: nx.DiGraph,
        removal_strategy: str = 'random',
        num_removals: int = 5
    ) -> Dict[str, Any]:
        """
        Analyze graph resilience to node/edge removal.
        
        Args:
            graph: Entity relationship graph
            removal_strategy: Strategy for node removal ('random', 'targeted', 'degree')
            num_removals: Number of nodes to remove
            
        Returns:
            Dictionary with resilience analysis results
        """
        original_graph = graph.copy()
        resilience_data = {
            'original_components': nx.number_weakly_connected_components(graph),
            'original_largest_component': len(max(nx.weakly_connected_components(graph), key=len)),
            'removal_impact': []
        }
        
        # Select nodes to remove
        if removal_strategy == 'random':
            nodes_to_remove = random.sample(list(graph.nodes()), min(num_removals, graph.number_of_nodes()))
        elif removal_strategy == 'targeted':
            # Remove highest criticality nodes
            criticality_scores = {
                node: graph.nodes[node].get('criticality_score', 0.0)
                for node in graph.nodes()
            }
            nodes_to_remove = sorted(criticality_scores.keys(), key=lambda x: criticality_scores[x], reverse=True)[:num_removals]
        elif removal_strategy == 'degree':
            # Remove highest degree nodes
            degree_scores = dict(graph.degree())
            nodes_to_remove = sorted(degree_scores.keys(), key=lambda x: degree_scores[x], reverse=True)[:num_removals]
        else:
            raise ValueError(f"Unknown removal strategy: {removal_strategy}")
        
        # Simulate removals
        for i, node in enumerate(nodes_to_remove):
            if node in graph:
                graph.remove_node(node)
                
                # Analyze impact
                components = nx.number_weakly_connected_components(graph)
                largest_component_size = len(max(nx.weakly_connected_components(graph), key=len)) if components > 0 else 0
                
                resilience_data['removal_impact'].append({
                    'removed_node': node,
                    'removal_step': i + 1,
                    'components_after': components,
                    'largest_component_size': largest_component_size,
                    'fragmentation_index': components / resilience_data['original_components']
                })
        
        # Calculate overall resilience score
        final_components = resilience_data['removal_impact'][-1]['components_after'] if resilience_data['removal_impact'] else 1
        final_largest = resilience_data['removal_impact'][-1]['largest_component_size'] if resilience_data['removal_impact'] else 0
        
        resilience_score = (
            (1.0 - (final_components - 1) / max(1, original_graph.number_of_nodes())) * 0.5 +
            (final_largest / max(1, resilience_data['original_largest_component'])) * 0.5
        )
        
        resilience_data['resilience_score'] = resilience_score
        
        return resilience_data
    
    def find_bottlenecks(
        self,
        graph: nx.DiGraph,
        flow_threshold: float = 0.8
    ) -> List[Dict[str, Any]]:
        """
        Identify bottleneck nodes in the graph.
        
        Args:
            graph: Entity relationship graph
            flow_threshold: Threshold for considering a node a bottleneck
            
        Returns:
            List of bottleneck information
        """
        bottlenecks = []
        
        # Calculate betweenness centrality
        betweenness = nx.betweenness_centrality(graph, weight='weight')
        
        # Calculate flow centrality (custom metric)
        flow_centrality = {}
        for node in graph.nodes():
            in_flow = sum(graph.get_edge_data(pred, node).get('weight', 1.0) for pred in graph.predecessors(node))
            out_flow = sum(graph.get_edge_data(node, succ).get('weight', 1.0) for succ in graph.successors(node))
            flow_centrality[node] = min(in_flow, out_flow)
        
        # Normalize flow centrality
        max_flow = max(flow_centrality.values()) if flow_centrality else 1.0
        flow_centrality = {node: flow / max_flow for node, flow in flow_centrality.items()}
        
        # Identify bottlenecks
        for node in graph.nodes():
            betweenness_score = betweenness.get(node, 0.0)
            flow_score = flow_centrality.get(node, 0.0)
            
            # Combined bottleneck score
            bottleneck_score = (betweenness_score * 0.6 + flow_score * 0.4)
            
            if bottleneck_score >= flow_threshold:
                bottlenecks.append({
                    'node': node,
                    'bottleneck_score': bottleneck_score,
                    'betweenness_centrality': betweenness_score,
                    'flow_centrality': flow_score,
                    'in_degree': graph.in_degree(node),
                    'out_degree': graph.out_degree(node),
                    'node_type': graph.nodes[node].get('node_type', 'unknown')
                })
        
        # Sort by bottleneck score
        bottlenecks.sort(key=lambda x: x['bottleneck_score'], reverse=True)
        
        return bottlenecks
    
    def _calculate_failure_impact(self, graph: nx.DiGraph, node: str) -> float:
        """Calculate the potential impact of a node failure."""
        if node not in graph:
            return 0.0
        
        # Count affected downstream nodes
        try:
            descendants = len(nx.descendants(graph, node))
            total_nodes = graph.number_of_nodes()
            return min(1.0, descendants / max(1, total_nodes))
        except:
            return 0.0
    
    def _calculate_path_criticality_advanced(self, graph: nx.DiGraph, path: List[str]) -> float:
        """Calculate advanced criticality score for a path."""
        if not path:
            return 0.0
        
        # Node criticality contribution
        node_criticality = sum(
            graph.nodes[node].get('criticality_score', 0.0) for node in path
        ) / len(path)
        
        # Edge weight contribution
        edge_weight = 0.0
        for i in range(len(path) - 1):
            edge_data = graph.get_edge_data(path[i], path[i + 1])
            if edge_data:
                edge_weight += edge_data.get('weight', 1.0)
        
        if len(path) > 1:
            edge_weight /= (len(path) - 1)
        
        # Path length penalty (shorter paths are more critical)
        length_penalty = 1.0 / (1.0 + len(path) * 0.1)
        
        # Relationship type bonus
        relationship_bonus = 0.0
        for i in range(len(path) - 1):
            edge_data = graph.get_edge_data(path[i], path[i + 1])
            if edge_data:
                rel_type = edge_data.get('relationship_type', 'RELATED_TO')
                if rel_type in ['DEPENDS_ON', 'RUNS_ON']:
                    relationship_bonus += 0.2
                elif rel_type in ['MANAGES', 'CONTAINS']:
                    relationship_bonus += 0.1
        
        # Combined score
        return min(1.0, node_criticality * 0.4 + edge_weight * 0.3 + length_penalty * 0.2 + relationship_bonus * 0.1)
    
    def _calculate_path_weight(self, graph: nx.DiGraph, path: List[str]) -> float:
        """Calculate total weight of a path."""
        total_weight = 0.0
        for i in range(len(path) - 1):
            edge_data = graph.get_edge_data(path[i], path[i + 1])
            if edge_data:
                total_weight += edge_data.get('weight', 1.0)
        return total_weight
    
    def _spectral_clustering(self, graph: nx.Graph, num_clusters: Optional[int] = None) -> Dict[int, List[str]]:
        """Perform spectral clustering on the graph using NetworkX-based approach."""
        if graph.number_of_nodes() < 2:
            return {0: list(graph.nodes())}
        
        # Use NetworkX community detection as a fallback
        try:
            import networkx.algorithms.community as nx_comm
            communities = nx_comm.greedy_modularity_communities(graph)
            
            clusters = {}
            for i, community in enumerate(communities):
                clusters[i] = list(community)
            
            # If too many communities, merge smaller ones
            if num_clusters and len(clusters) > num_clusters:
                # Sort communities by size
                sorted_communities = sorted(clusters.items(), key=lambda x: len(x[1]), reverse=True)
                
                # Keep top num_clusters communities
                final_clusters = {}
                for i in range(min(num_clusters, len(sorted_communities))):
                    final_clusters[i] = sorted_communities[i][1]
                
                # Merge remaining communities into the last cluster
                if len(sorted_communities) > num_clusters:
                    for i in range(num_clusters, len(sorted_communities)):
                        final_clusters[num_clusters - 1].extend(sorted_communities[i][1])
                
                return final_clusters
            
            return clusters
            
        except Exception as e:
            self.logger.warning(f"Community detection failed: {e}, using simple clustering")
            # Fallback to simple clustering based on connectivity
            return self._simple_connectivity_clustering(graph, num_clusters)
    
    def _simple_connectivity_clustering(self, graph: nx.Graph, num_clusters: Optional[int] = None) -> Dict[int, List[str]]:
        """Simple clustering based on connected components and node degrees."""
        if graph.number_of_nodes() < 2:
            return {0: list(graph.nodes())}
        
        # Start with connected components
        components = list(nx.connected_components(graph))
        
        if num_clusters is None:
            num_clusters = min(len(components), max(2, int(np.sqrt(graph.number_of_nodes()))))
        
        # If we have fewer components than desired clusters, split larger components
        if len(components) < num_clusters:
            clusters = {}
            cluster_id = 0
            
            for component in components:
                if len(component) > 2 and cluster_id < num_clusters - 1:
                    # Split this component by degree
                    nodes_by_degree = sorted(component, key=lambda x: graph.degree(x), reverse=True)
                    mid_point = len(nodes_by_degree) // 2
                    
                    clusters[cluster_id] = nodes_by_degree[:mid_point]
                    cluster_id += 1
                    clusters[cluster_id] = nodes_by_degree[mid_point:]
                    cluster_id += 1
                else:
                    clusters[cluster_id] = list(component)
                    cluster_id += 1
            
            return clusters
        else:
            # We have enough or too many components, group them
            clusters = {}
            for i, component in enumerate(components[:num_clusters]):
                clusters[i] = list(component)
            
            # Merge remaining components into the last cluster
            if len(components) > num_clusters:
                for component in components[num_clusters:]:
                    clusters[num_clusters - 1].extend(component)
            
            return clusters
    
    def _modularity_clustering(self, graph: nx.Graph) -> Dict[int, List[str]]:
        """Perform modularity-based clustering."""
        try:
            # Use NetworkX's built-in community detection
            import networkx.algorithms.community as nx_comm
            communities = nx_comm.greedy_modularity_communities(graph)
            
            clusters = {}
            for i, community in enumerate(communities):
                clusters[i] = list(community)
            
            return clusters
        except:
            # Fallback to simple clustering
            return {0: list(graph.nodes())}
    
    def _leiden_clustering(self, graph: nx.Graph) -> Dict[int, List[str]]:
        """Perform Leiden clustering (simplified version)."""
        # This is a simplified version - in practice, you'd use a proper Leiden implementation
        return self._modularity_clustering(graph)
    
    def _calculate_modularity(self, graph: nx.Graph, clusters: Dict[int, List[str]]) -> float:
        """Calculate modularity of the clustering."""
        try:
            # Convert clusters to partition format
            partition = {}
            for cluster_id, nodes in clusters.items():
                for node in nodes:
                    partition[node] = cluster_id
            
            # Calculate modularity
            import networkx.algorithms.community as nx_comm
            return nx_comm.modularity(graph, clusters.values())
        except:
            return 0.0
    
    def _calculate_silhouette_score(self, graph: nx.Graph, clusters: Dict[int, List[str]]) -> float:
        """Calculate silhouette score for clustering."""
        # Simplified silhouette calculation
        if len(clusters) <= 1:
            return 0.0
        
        try:
            # Calculate average intra-cluster distance vs inter-cluster distance
            total_score = 0.0
            total_nodes = 0
            
            for cluster_id, nodes in clusters.items():
                if len(nodes) <= 1:
                    continue
                
                subgraph = graph.subgraph(nodes)
                intra_distance = nx.density(subgraph)
                
                # Calculate inter-cluster distance (simplified)
                inter_distance = 0.0
                inter_count = 0
                
                for node in nodes:
                    for other_cluster_id, other_nodes in clusters.items():
                        if other_cluster_id != cluster_id:
                            for other_node in other_nodes:
                                if graph.has_edge(node, other_node):
                                    inter_distance += 1
                                    inter_count += 1
                
                if inter_count > 0:
                    inter_distance /= inter_count
                
                # Silhouette score for this cluster
                if intra_distance > 0 or inter_distance > 0:
                    silhouette = (inter_distance - intra_distance) / max(intra_distance, inter_distance)
                    total_score += silhouette * len(nodes)
                    total_nodes += len(nodes)
            
            return total_score / max(1, total_nodes)
        except:
            return 0.0


# Utility functions
def calculate_graph_similarity(graph1: nx.DiGraph, graph2: nx.DiGraph) -> float:
    """
    Calculate similarity between two graphs.
    
    Args:
        graph1: First graph
        graph2: Second graph
        
    Returns:
        Similarity score between 0 and 1
    """
    # Node overlap
    nodes1 = set(graph1.nodes())
    nodes2 = set(graph2.nodes())
    node_overlap = len(nodes1.intersection(nodes2)) / len(nodes1.union(nodes2)) if nodes1 or nodes2 else 0.0
    
    # Edge overlap
    edges1 = set(graph1.edges())
    edges2 = set(graph2.edges())
    edge_overlap = len(edges1.intersection(edges2)) / len(edges1.union(edges2)) if edges1 or edges2 else 0.0
    
    # Structural similarity (simplified)
    structural_similarity = 0.0
    if graph1.number_of_nodes() > 0 and graph2.number_of_nodes() > 0:
        density1 = nx.density(graph1)
        density2 = nx.density(graph2)
        structural_similarity = 1.0 - abs(density1 - density2)
    
    # Combined similarity
    return (node_overlap * 0.4 + edge_overlap * 0.4 + structural_similarity * 0.2)


def extract_subgraph_by_distance(
    graph: nx.DiGraph,
    center_node: str,
    max_distance: int = 2,
    direction: str = 'both'
) -> nx.DiGraph:
    """
    Extract a subgraph within a certain distance from a center node.
    
    Args:
        graph: Source graph
        center_node: Center node for extraction
        max_distance: Maximum distance from center
        direction: Direction to traverse ('in', 'out', 'both')
        
    Returns:
        Subgraph containing nodes within the specified distance
    """
    if center_node not in graph:
        return nx.DiGraph()
    
    # Find nodes within distance
    nodes_to_include = {center_node}
    
    if direction in ['out', 'both']:
        # Outgoing edges (descendants)
        for distance in range(1, max_distance + 1):
            current_nodes = list(nodes_to_include)
            for node in current_nodes:
                for successor in graph.successors(node):
                    if nx.shortest_path_length(graph, center_node, successor) <= max_distance:
                        nodes_to_include.add(successor)
    
    if direction in ['in', 'both']:
        # Incoming edges (ancestors)
        for distance in range(1, max_distance + 1):
            current_nodes = list(nodes_to_include)
            for node in current_nodes:
                for predecessor in graph.predecessors(node):
                    if nx.shortest_path_length(graph.reverse(), center_node, predecessor) <= max_distance:
                        nodes_to_include.add(predecessor)
    
    # Create subgraph
    return graph.subgraph(nodes_to_include).copy()


def identify_critical_nodes_by_removal(
    graph: nx.DiGraph,
    metric: str = 'connectivity',
    top_k: int = 5
) -> List[Tuple[str, float]]:
    """
    Identify critical nodes by measuring impact of their removal.
    
    Args:
        graph: Input graph
        metric: Metric to use ('connectivity', 'efficiency', 'clustering')
        top_k: Number of top critical nodes to return
        
    Returns:
        List of (node_id, criticality_score) tuples
    """
    original_graph = graph.copy()
    node_criticality = []
    
    # Calculate baseline metric
    if metric == 'connectivity':
        baseline = nx.number_weakly_connected_components(original_graph)
    elif metric == 'efficiency':
        baseline = nx.global_efficiency(original_graph)
    elif metric == 'clustering':
        baseline = nx.average_clustering(original_graph.to_undirected())
    else:
        raise ValueError(f"Unknown metric: {metric}")
    
    # Test removal of each node
    for node in original_graph.nodes():
        test_graph = original_graph.copy()
        test_graph.remove_node(node)
        
        # Calculate metric after removal
        if metric == 'connectivity':
            after_removal = nx.number_weakly_connected_components(test_graph)
            impact = (after_removal - baseline) / max(1, baseline)
        elif metric == 'efficiency':
            after_removal = nx.global_efficiency(test_graph)
            impact = (baseline - after_removal) / max(0.001, baseline)
        elif metric == 'clustering':
            after_removal = nx.average_clustering(test_graph.to_undirected())
            impact = (baseline - after_removal) / max(0.001, baseline)
        
        node_criticality.append((node, impact))
    
    # Sort by criticality and return top k
    node_criticality.sort(key=lambda x: x[1], reverse=True)
    return node_criticality[:top_k]


# Singleton instance
_graph_algorithms_instance = None


def get_graph_algorithms() -> GraphAlgorithms:
    """Get the singleton instance of the graph algorithms."""
    global _graph_algorithms_instance
    if _graph_algorithms_instance is None:
        _graph_algorithms_instance = GraphAlgorithms()
    return _graph_algorithms_instance