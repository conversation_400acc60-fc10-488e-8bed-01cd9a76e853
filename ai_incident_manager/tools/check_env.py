"""
Environment Variable Checker

This script checks if required environment variables are properly set.
"""

import os
import sys
from loguru import logger
import dotenv

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def check_mongodb_env_vars():
    """Check if MongoDB environment variables are set."""
    # Load environment variables
    dotenv.load_dotenv()
    
    # Required environment variables for MongoDB
    mongodb_vars = {
        "MONGODB_CONNECTION_STRING": os.getenv("MONGODB_CONNECTION_STRING"),
        "MONGODB_DATABASE_NAME": os.getenv("MONGODB_DATABASE_NAME", "incident_management")
    }
    
    # Check if MongoDB connection string is set
    if not mongodb_vars["MONGODB_CONNECTION_STRING"]:
        logger.error("MONGODB_CONNECTION_STRING environment variable is not set!")
        logger.info("Set this in your .env file with a valid MongoDB connection string")
        logger.info("Example: MONGODB_CONNECTION_STRING=mongodb://localhost:27017")
    else:
        # Mask the connection string for security
        masked_conn_string = mongodb_vars["MONGODB_CONNECTION_STRING"]
        if "@" in masked_conn_string:
            # Format typically: ************************************:port/
            protocol, rest = masked_conn_string.split("://", 1)
            credentials, host_part = rest.split("@", 1)
            
            if ":" in credentials:
                username, _ = credentials.split(":", 1)
                masked_conn_string = f"{protocol}://{username}:****@{host_part}"
            
        logger.info(f"MONGODB_CONNECTION_STRING is set: {masked_conn_string}")
    
    # Check database name
    logger.info(f"MONGODB_DATABASE_NAME is set to: {mongodb_vars['MONGODB_DATABASE_NAME']}")
    
    return mongodb_vars["MONGODB_CONNECTION_STRING"] is not None

def check_newrelic_env_vars():
    """Check if New Relic environment variables are set."""
    # Required environment variables for New Relic
    newrelic_vars = {
        "NEWRELIC_API_KEY": os.getenv("NEWRELIC_API_KEY"),
        "NEWRELIC_ACCOUNT_ID": os.getenv("NEWRELIC_ACCOUNT_ID")
    }
    
    # Check New Relic API key
    if not newrelic_vars["NEWRELIC_API_KEY"]:
        logger.error("NEWRELIC_API_KEY environment variable is not set!")
    else:
        logger.info("NEWRELIC_API_KEY is set (value masked for security)")
    
    # Check New Relic account ID
    if not newrelic_vars["NEWRELIC_ACCOUNT_ID"]:
        logger.error("NEWRELIC_ACCOUNT_ID environment variable is not set!")
    else:
        logger.info(f"NEWRELIC_ACCOUNT_ID is set to: {newrelic_vars['NEWRELIC_ACCOUNT_ID']}")
    
    return all(newrelic_vars.values())

def check_all_env_vars():
    """Check all required environment variables."""
    logger.info("Checking environment variables...")
    
    # Check MongoDB environment variables
    mongodb_ok = check_mongodb_env_vars()
    
    # Check New Relic environment variables
    newrelic_ok = check_newrelic_env_vars()
    
    # Overall status
    if mongodb_ok and newrelic_ok:
        logger.info("All required environment variables are set!")
    else:
        logger.warning("Some required environment variables are missing!")
        
        if not mongodb_ok:
            logger.warning("MongoDB configuration is incomplete - this may affect data storage!")
            
        if not newrelic_ok:
            logger.warning("New Relic configuration is incomplete - this may affect data fetching!")
    
    return mongodb_ok and newrelic_ok

if __name__ == "__main__":
    # Configure logger
    logger.remove()
    logger.add(sys.stderr, format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")
    
    # Run the environment variable check
    check_all_env_vars() 