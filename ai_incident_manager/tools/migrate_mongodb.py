"""
MongoDB Data Migration Script

This script migrates existing MongoDB data from separate collections to the new consolidated
incident document structure.
"""

import asyncio
import sys
import os
from loguru import logger
import json

# Add the parent directory to the Python path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from ai_incident_manager.services.mongodb_service import get_mongodb_service

async def migrate_data():
    """Migrate data from separate collections to consolidated incident documents."""
    logger.info("Starting MongoDB data migration...")
    
    # Get MongoDB service
    mongodb_service = get_mongodb_service()
    
    # Check connection first
    connection_info = await mongodb_service.test_connection()
    if not connection_info["connection_success"]:
        logger.error("Cannot proceed with migration due to MongoDB connection failure")
        return
    
    # Print current collection counts
    logger.info("Current collection counts before migration:")
    if "incidents_count" in connection_info:
        logger.info(f"  Incidents: {connection_info['incidents_count']}")
    if "tool_results_count" in connection_info:
        logger.info(f"  Tool Results: {connection_info['tool_results_count']}")
    if "metrics_count" in connection_info:
        logger.info(f"  Metrics: {connection_info['metrics_count']}")
    if "logs_count" in connection_info:
        logger.info(f"  Logs: {connection_info['logs_count']}")
    if "events_count" in connection_info:
        logger.info(f"  Events: {connection_info['events_count']}")
    
    # Run the migration
    logger.info("Performing migration...")
    migration_result = await mongodb_service.migrate_to_consolidated_structure()
    
    # Print results
    logger.info("Migration results:")
    logger.info(f"  Incidents processed: {migration_result['incidents_processed']}")
    logger.info(f"  Metrics migrated: {migration_result['metrics_migrated']}")
    logger.info(f"  Logs migrated: {migration_result['logs_migrated']}")
    logger.info(f"  Events migrated: {migration_result['events_migrated']}")
    logger.info(f"  Tool results migrated: {migration_result['tool_results_migrated']}")
    
    # Print errors if any
    if migration_result["errors"]:
        logger.warning(f"Migration completed with {len(migration_result['errors'])} errors:")
        for error in migration_result["errors"]:
            logger.warning(f"  - {error}")
    else:
        logger.info("Migration completed successfully with no errors")
    
    # Check current state after migration
    connection_info_after = await mongodb_service.test_connection()
    logger.info("Collection counts after migration:")
    if "incidents_count" in connection_info_after:
        logger.info(f"  Incidents: {connection_info_after['incidents_count']}")
    if "tool_results_count" in connection_info_after:
        logger.info(f"  Tool Results: {connection_info_after['tool_results_count']}")
    if "metrics_count" in connection_info_after:
        logger.info(f"  Metrics: {connection_info_after['metrics_count']}")
    if "logs_count" in connection_info_after:
        logger.info(f"  Logs: {connection_info_after['logs_count']}")
    if "events_count" in connection_info_after:
        logger.info(f"  Events: {connection_info_after['events_count']}")
    
    # Check a few incidents to confirm they have the arrays
    try:
        # Get a few incidents
        incidents_collection = mongodb_service._incidents_collection
        cursor = incidents_collection.find().sort("timestamp", -1).limit(3)
        incidents = await cursor.to_list(length=3)
        
        logger.info("Sample incidents after migration:")
        for incident in incidents:
            incident_id = incident.get("incident_id", "Unknown")
            metrics_count = len(incident.get("metrics", []))
            logs_count = len(incident.get("logs", []))
            events_count = len(incident.get("events", []))
            tool_results_count = len(incident.get("tool_results", []))
            
            logger.info(f"  Incident {incident_id}:")
            logger.info(f"    - Metrics: {metrics_count}")
            logger.info(f"    - Logs: {logs_count}")
            logger.info(f"    - Events: {events_count}")
            logger.info(f"    - Tool Results: {tool_results_count}")
    except Exception as e:
        logger.error(f"Error checking incidents after migration: {str(e)}")
    
    logger.info("MongoDB data migration completed")

if __name__ == "__main__":
    # Configure logger
    logger.remove()
    logger.add(sys.stderr, format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")
    
    # Run the migration
    asyncio.run(migrate_data()) 