"""
MongoDB Debugging Tool

This script tests MongoDB connectivity and data storage/retrieval to help diagnose issues.
"""

import asyncio
import json
from datetime import datetime, timezone
import sys
import os
from loguru import logger
from typing import Dict, Any, List

# Add the parent directory to the Python path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from ai_incident_manager.services.mongodb_service import get_mongodb_service

async def test_mongodb_connection():
    """Test the MongoDB connection and print diagnostics."""
    logger.info("Testing MongoDB connection...")
    mongodb_service = get_mongodb_service()
    
    # Test connection using the new debug method
    connection_info = await mongodb_service.test_connection()
    
    # Pretty print the connection info
    logger.info(f"MongoDB Connection Status: {json.dumps(connection_info, indent=2)}")
    
    return connection_info["connection_success"]

async def test_data_storage():
    """Test storing and retrieving data in MongoDB."""
    logger.info("Testing data storage and retrieval...")
    mongodb_service = get_mongodb_service()
    
    # Create a test incident ID
    test_incident_id = f"test-incident-{int(datetime.now().timestamp())}"
    
    # Test storing an incident
    incident_data = {
        "incident_id": test_incident_id,
        "title": "Test Incident",
        "description": "This is a test incident for debugging MongoDB",
        "severity": "low",
        "timestamp": datetime.now(timezone.utc),
        "status": "open"
    }
    
    try:
        # Store the incident
        logger.info(f"Storing test incident: {test_incident_id}")
        doc_id = await mongodb_service.store_incident(incident_data)
        logger.info(f"Stored incident with document ID: {doc_id}")
        
        # Retrieve the incident
        logger.info(f"Retrieving test incident: {test_incident_id}")
        retrieved_incident = await mongodb_service.get_incident_by_id(test_incident_id)
        
        if retrieved_incident:
            logger.info(f"Successfully retrieved incident: {retrieved_incident.get('title')}")
        else:
            logger.error(f"Failed to retrieve incident: {test_incident_id}")
        
        # Test storing metrics
        logger.info("Storing test metrics")
        metric_data = {
            "values": [1, 2, 3, 4, 5],
            "average": 3.0,
            "max": 5,
            "min": 1
        }
        
        metric_id = await mongodb_service.store_metrics(
            incident_id=test_incident_id,
            entity_guid="test-entity-guid",
            metric_name="test-metric",
            metric_data=metric_data
        )
        logger.info(f"Stored metric with document ID: {metric_id}")
        
        # Retrieve metrics
        logger.info(f"Retrieving metrics for incident: {test_incident_id}")
        metrics = await mongodb_service.get_metrics_by_incident_id(test_incident_id)
        logger.info(f"Retrieved {len(metrics)} metrics")
        
        # Test storing logs
        logger.info("Storing test logs")
        log_data = {
            "messages": ["This is a test log message", "Another test log message"],
            "level": "info",
            "service": "test-service"
        }
        
        log_id = await mongodb_service.store_logs(
            incident_id=test_incident_id,
            entity_guid="test-entity-guid",
            log_data=log_data
        )
        logger.info(f"Stored logs with document ID: {log_id}")
        
        # Retrieve logs
        logger.info(f"Retrieving logs for incident: {test_incident_id}")
        logs = await mongodb_service.get_logs_by_incident_id(test_incident_id)
        logger.info(f"Retrieved {len(logs)} logs")
        
        return True
    except Exception as e:
        logger.error(f"Error during data storage test: {str(e)}")
        return False

async def check_existing_data():
    """Check for existing data in MongoDB collections."""
    logger.info("Checking existing data in MongoDB...")
    mongodb_service = get_mongodb_service()
    
    # Get connection info which includes document counts
    connection_info = await mongodb_service.test_connection()
    
    if not connection_info["connection_success"]:
        logger.error("Cannot check existing data due to connection failure")
        return
    
    # Print collection counts
    logger.info("Collection counts:")
    if "incidents_count" in connection_info:
        logger.info(f"  Incidents: {connection_info['incidents_count']}")
    if "tool_results_count" in connection_info:
        logger.info(f"  Tool Results: {connection_info['tool_results_count']}")
    if "metrics_count" in connection_info:
        logger.info(f"  Metrics: {connection_info['metrics_count']}")
    if "logs_count" in connection_info:
        logger.info(f"  Logs: {connection_info['logs_count']}")
    if "events_count" in connection_info:
        logger.info(f"  Events: {connection_info['events_count']}")
    
    # If there are incidents, list a few of them
    if connection_info.get("incidents_count", 0) > 0:
        # This is a bit of a hack since we don't have a method to list all incidents,
        # but we can try to access the collection directly for debugging
        if mongodb_service._incidents_collection:
            logger.info("Fetching the 5 most recent incidents:")
            try:
                cursor = mongodb_service._incidents_collection.find().sort("timestamp", -1).limit(5)
                incidents = await cursor.to_list(length=5)
                for incident in incidents:
                    incident_id = incident.get("incident_id", "Unknown")
                    timestamp = incident.get("timestamp", "Unknown")
                    logger.info(f"  Incident ID: {incident_id}, Timestamp: {timestamp}")
                    
                    # Check if this incident has metrics
                    metrics = await mongodb_service.get_metrics_by_incident_id(incident_id)
                    logger.info(f"  - Has {len(metrics)} metrics")
                    
                    # Check if this incident has logs
                    logs = await mongodb_service.get_logs_by_incident_id(incident_id)
                    logger.info(f"  - Has {len(logs)} logs")
                    
                    # Check if this incident has tool results
                    tool_results = await mongodb_service.get_tool_results_by_incident_id(incident_id)
                    logger.info(f"  - Has {len(tool_results)} tool results")
            except Exception as e:
                logger.error(f"Error fetching recent incidents: {str(e)}")

async def main():
    """Main function to run all tests."""
    logger.info("Starting MongoDB debugging")
    
    # Configure logger to show debug messages
    logger.remove()
    logger.add(sys.stderr, level="DEBUG")
    
    # Test connection
    connection_success = await test_mongodb_connection()
    
    if connection_success:
        # Check existing data
        await check_existing_data()
        
        # Test data storage
        await test_data_storage()
    else:
        logger.error("Cannot proceed with tests due to connection failure")
    
    logger.info("MongoDB debugging complete")

if __name__ == "__main__":
    asyncio.run(main()) 