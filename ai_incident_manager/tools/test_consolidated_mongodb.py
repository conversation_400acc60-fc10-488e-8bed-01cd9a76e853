"""
Test MongoDB Consolidated Structure

This script tests the new consolidated MongoDB structure by creating a test incident
and then adding metrics, logs, events, and tool results to it.
"""

import asyncio
import sys
import os
from loguru import logger
import json
from datetime import datetime, timezone
import uuid

# Add the parent directory to the Python path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from ai_incident_manager.services.mongodb_service import get_mongodb_service

async def test_consolidated_structure():
    """
    Test the consolidated MongoDB structure by creating a test incident and
    adding metrics, logs, events, and tool results to it.
    """
    logger.info("Testing consolidated MongoDB structure...")
    
    # Get MongoDB service
    mongodb_service = get_mongodb_service()
    
    # Create a unique test incident ID
    incident_id = f"test-consolidated-{uuid.uuid4()}"
    
    # Create a test incident
    incident_data = {
        "incident_id": incident_id,
        "title": "Test Consolidated Incident",
        "description": "This is a test incident for testing consolidated MongoDB structure",
        "severity": "medium",
        "status": "open",
        "timestamp": datetime.now(timezone.utc)
    }
    
    logger.info(f"Creating test incident: {incident_id}")
    doc_id = await mongodb_service.store_incident(incident_data)
    logger.info(f"Created incident with document ID: {doc_id}")
    
    # Add metrics to the incident
    logger.info("Adding metrics to the incident")
    metrics = [
        {
            "metric_name": "cpu_usage",
            "entity_guid": "test-entity-1",
            "data": {
                "values": [10, 20, 30, 40, 50],
                "average": 30.0,
                "max": 50,
                "unit": "percent"
            }
        },
        {
            "metric_name": "memory_usage",
            "entity_guid": "test-entity-1",
            "data": {
                "values": [100, 200, 300, 400, 500],
                "average": 300.0,
                "max": 500,
                "unit": "MB"
            }
        }
    ]
    
    for metric in metrics:
        metric_id = await mongodb_service.store_metrics(
            incident_id=incident_id,
            entity_guid=metric["entity_guid"],
            metric_name=metric["metric_name"],
            metric_data=metric["data"]
        )
        logger.info(f"Added metric {metric['metric_name']} with ID: {metric_id}")
    
    # Add logs to the incident
    logger.info("Adding logs to the incident")
    logs = [
        {
            "entity_guid": "test-entity-1",
            "data": {
                "messages": ["Log message 1", "Log message 2"],
                "level": "info",
                "service": "test-service-1"
            }
        },
        {
            "entity_guid": "test-entity-2",
            "data": {
                "messages": ["Error: Connection refused", "Error: Timeout"],
                "level": "error",
                "service": "test-service-2"
            }
        }
    ]
    
    for log in logs:
        log_id = await mongodb_service.store_logs(
            incident_id=incident_id,
            entity_guid=log["entity_guid"],
            log_data=log["data"]
        )
        logger.info(f"Added log for entity {log['entity_guid']} with ID: {log_id}")
    
    # Add events to the incident
    logger.info("Adding events to the incident")
    events = [
        {
            "entity_guid": "test-entity-1",
            "data": {
                "type": "pod_restart",
                "message": "Pod restarted due to OOM",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        },
        {
            "entity_guid": "test-entity-2",
            "data": {
                "type": "deployment_scale",
                "message": "Deployment scaled up from 2 to 4 replicas",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }
    ]
    
    for event in events:
        event_id = await mongodb_service.store_events(
            incident_id=incident_id,
            entity_guid=event["entity_guid"],
            event_data=event["data"]
        )
        logger.info(f"Added event for entity {event['entity_guid']} with ID: {event_id}")
    
    # Add tool results to the incident
    logger.info("Adding tool results to the incident")
    tools = [
        {
            "entity_guid": "test-entity-1",
            "tool_name": "get_pod_logs",
            "tool_parameters": {
                "pod_name": "test-pod-1",
                "namespace": "default",
                "lines": 100
            },
            "result_data": {
                "logs": ["Line 1", "Line 2", "Line 3"],
                "pod_name": "test-pod-1",
                "namespace": "default"
            }
        },
        {
            "entity_guid": "test-entity-2",
            "tool_name": "get_pod_metrics",
            "tool_parameters": {
                "pod_name": "test-pod-2",
                "namespace": "default",
                "metric_types": ["cpu", "memory"]
            },
            "result_data": {
                "cpu": {"usage": 50, "limit": 100},
                "memory": {"usage": 200, "limit": 500},
                "pod_name": "test-pod-2",
                "namespace": "default"
            }
        }
    ]
    
    for tool in tools:
        tool_id = await mongodb_service.store_tool_result(
            incident_id=incident_id,
            tool_name=tool["tool_name"],
            tool_parameters=tool["tool_parameters"],
            result_data=tool["result_data"],
            entity_guid=tool["entity_guid"]
        )
        logger.info(f"Added tool result for {tool['tool_name']} with ID: {tool_id}")
    
    # Retrieve the incident and verify it contains all the data
    logger.info(f"Retrieving incident {incident_id} to verify consolidated structure")
    incident = await mongodb_service.get_incident_by_id(incident_id)
    
    if not incident:
        logger.error(f"Failed to retrieve incident {incident_id}")
        return
    
    # Verify metrics
    metrics_count = len(incident.get("metrics", []))
    logger.info(f"Found {metrics_count} metrics in the incident document (expected: {len(metrics)})")
    
    # Verify logs
    logs_count = len(incident.get("logs", []))
    logger.info(f"Found {logs_count} logs in the incident document (expected: {len(logs)})")
    
    # Verify events
    events_count = len(incident.get("events", []))
    logger.info(f"Found {events_count} events in the incident document (expected: {len(events)})")
    
    # Verify tool results
    tool_results_count = len(incident.get("tool_results", []))
    logger.info(f"Found {tool_results_count} tool results in the incident document (expected: {len(tools)})")
    
    # Verify using the individual get methods
    retrieved_metrics = await mongodb_service.get_metrics_by_incident_id(incident_id)
    logger.info(f"Retrieved {len(retrieved_metrics)} metrics using get_metrics_by_incident_id")
    
    retrieved_logs = await mongodb_service.get_logs_by_incident_id(incident_id)
    logger.info(f"Retrieved {len(retrieved_logs)} logs using get_logs_by_incident_id")
    
    retrieved_events = await mongodb_service.get_events_by_incident_id(incident_id)
    logger.info(f"Retrieved {len(retrieved_events)} events using get_events_by_incident_id")
    
    retrieved_tool_results = await mongodb_service.get_tool_results_by_incident_id(incident_id)
    logger.info(f"Retrieved {len(retrieved_tool_results)} tool results using get_tool_results_by_incident_id")
    
    # Overall verification
    result = (
        metrics_count == len(metrics) and
        logs_count == len(logs) and
        events_count == len(events) and
        tool_results_count == len(tools) and
        len(retrieved_metrics) == len(metrics) and
        len(retrieved_logs) == len(logs) and
        len(retrieved_events) == len(events) and
        len(retrieved_tool_results) == len(tools)
    )
    
    if result:
        logger.info("✅ Consolidated MongoDB structure is working correctly!")
    else:
        logger.error("❌ Consolidated MongoDB structure test failed!")
    
    logger.info("Test completed.")
    
    # Return the incident ID so it can be deleted if needed
    return incident_id

if __name__ == "__main__":
    # Configure logger
    logger.remove()
    logger.add(sys.stderr, format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")
    
    # Run the test
    asyncio.run(test_consolidated_structure()) 