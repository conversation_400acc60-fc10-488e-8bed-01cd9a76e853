"""
Graph-specific data models for entity relationship and cascading failure analysis.

This module provides Pydantic models for representing graph structures,
analysis results, and configuration used in the graph service.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Literal, Union
from pydantic import BaseModel, Field
from enum import Enum


class NodeTypeEnum(str, Enum):
    """Types of nodes in the graph"""
    KUBERNETES_POD = "KUBERNETES_POD"
    KUBERNETES_NODE = "KUBERNETES_NODE"
    KUBERNETES_DEPLOYMENT = "KUBERNETES_DEPLOYMENT"
    KUBERNETES_NAMESPACE = "KUBERNETES_NAMESPACE"
    KUBERNETES_CLUSTER = "KUBERNETES_CLUSTER"
    KUBERNETES_CONTAINER = "KUBERNETES_CONTAINER"
    CONTAINER = "CONTAINER"
    APPLICATION = "APPLICATION"
    HOST = "HOST"
    DATABASE = "DATABASE"
    KAFKA = "KAFKA"
    DEBEZIUM = "DEBEZIUM"
    UNKNOWN = "UNKNOWN"


class RelationshipTypeEnum(str, Enum):
    """Types of relationships between entities"""
    RUNS_ON = "RUNS_ON"
    CONTAINS = "CONTAINS"
    DEPENDS_ON = "DEPENDS_ON"
    COMMUNICATES_WITH = "COMMUNICATES_WITH"
    MANAGES = "MANAGES"
    BELONGS_TO = "BELONGS_TO"
    WRITES_TO = "WRITES_TO"
    READS_FROM = "READS_FROM"
    RELATED_TO = "RELATED_TO"
    CONTAINED_BY = "CONTAINED_BY"
    MANAGED_BY = "MANAGED_BY"


class HealthStatusEnum(str, Enum):
    """Health status of entities"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    ISSUE = "issue"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class GraphNodeModel(BaseModel):
    """Pydantic model for graph nodes"""
    id: str
    name: str
    node_type: NodeTypeEnum
    entity_guid: Optional[str] = None
    properties: Dict[str, Any] = Field(default_factory=dict)
    metrics: Dict[str, Any] = Field(default_factory=dict)
    health_status: HealthStatusEnum = HealthStatusEnum.UNKNOWN
    criticality_score: float = Field(default=0.0, ge=0.0, le=1.0)
    failure_probability: float = Field(default=0.0, ge=0.0, le=1.0)
    degree_centrality: float = Field(default=0.0, ge=0.0, le=1.0)
    betweenness_centrality: float = Field(default=0.0, ge=0.0, le=1.0)
    cluster_name: Optional[str] = None
    namespace: Optional[str] = None
    created_at: Optional[datetime] = None
    last_updated: Optional[datetime] = None


class GraphEdgeModel(BaseModel):
    """Pydantic model for graph edges"""
    source: str
    target: str
    relationship_type: RelationshipTypeEnum
    weight: float = Field(default=1.0, ge=0.0)
    strength: float = Field(default=1.0, ge=0.0, le=1.0)
    failure_propagation_probability: float = Field(default=0.5, ge=0.0, le=1.0)
    properties: Dict[str, Any] = Field(default_factory=dict)
    metrics_to_collect: List[str] = Field(default_factory=list)
    importance: Literal["low", "medium", "high"] = "medium"


class GraphModel(BaseModel):
    """Pydantic model for complete graph structure"""
    nodes: List[GraphNodeModel]
    edges: List[GraphEdgeModel]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.now)
    incident_id: Optional[str] = None
    primary_entity_guid: Optional[str] = None
    alert_category: Optional[str] = None
    max_depth: int = Field(default=3, ge=1, le=10)


class FailurePathModel(BaseModel):
    """Model for failure propagation paths"""
    path: List[str]
    probability: float = Field(ge=0.0, le=1.0)
    total_weight: float = Field(ge=0.0)
    criticality_score: float = Field(ge=0.0, le=1.0)
    entities: List[GraphNodeModel] = Field(default_factory=list)


class CascadingFailureAnalysisModel(BaseModel):
    """Pydantic model for cascading failure analysis results"""
    primary_entity: str
    affected_entities: List[str]
    failure_paths: List[FailurePathModel]
    risk_scores: Dict[str, float]
    critical_dependencies: List[str]
    potential_blast_radius: int = Field(ge=0)
    estimated_impact_score: float = Field(ge=0.0)
    recommended_actions: List[str]
    analysis_timestamp: datetime = Field(default_factory=datetime.now)
    failure_threshold: float = Field(default=0.1, ge=0.0, le=1.0)
    propagation_decay: float = Field(default=0.8, ge=0.0, le=1.0)
    max_hops: int = Field(default=5, ge=1, le=20)


class GraphMetricsModel(BaseModel):
    """Pydantic model for graph metrics"""
    node_count: int = Field(ge=0)
    edge_count: int = Field(ge=0)
    density: float = Field(ge=0.0, le=1.0)
    average_clustering: float = Field(ge=0.0, le=1.0)
    average_path_length: float = Field(ge=0.0)
    diameter: int = Field(ge=0)
    largest_component_size: int = Field(ge=0)
    centrality_metrics: Dict[str, Dict[str, float]] = Field(default_factory=dict)
    calculated_at: datetime = Field(default_factory=datetime.now)


class CriticalPathModel(BaseModel):
    """Model for critical paths between entities"""
    source: str
    target: str
    paths: List[List[str]]
    shortest_path: List[str]
    criticality_scores: Dict[str, float]
    total_weight: float = Field(ge=0.0)
    risk_level: Literal["low", "medium", "high", "critical"] = "medium"


class EntitySearchCriteria(BaseModel):
    """Model for entity search criteria"""
    node_type: Optional[NodeTypeEnum] = None
    min_criticality: float = Field(default=0.0, ge=0.0, le=1.0)
    max_distance_from_issue: int = Field(default=999, ge=0)
    health_status: Optional[HealthStatusEnum] = None
    cluster_name: Optional[str] = None
    namespace: Optional[str] = None
    has_metrics: bool = False
    min_centrality: float = Field(default=0.0, ge=0.0, le=1.0)


class EntitySearchResult(BaseModel):
    """Model for entity search results"""
    matching_entities: List[str]
    total_matches: int = Field(ge=0)
    criteria: EntitySearchCriteria
    search_timestamp: datetime = Field(default_factory=datetime.now)


class GraphAnalysisConfig(BaseModel):
    """Configuration for graph analysis"""
    max_depth: int = Field(default=3, ge=1, le=10)
    failure_threshold: float = Field(default=0.1, ge=0.0, le=1.0)
    propagation_decay: float = Field(default=0.8, ge=0.0, le=1.0)
    max_hops: int = Field(default=5, ge=1, le=20)
    critical_path_threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    default_failure_probability: float = Field(default=0.1, ge=0.0, le=1.0)
    default_propagation_probability: float = Field(default=0.5, ge=0.0, le=1.0)
    cache_timeout_seconds: int = Field(default=300, ge=0)


class GraphDifferenceModel(BaseModel):
    """Model for comparing two graph snapshots"""
    added_nodes: List[str]
    removed_nodes: List[str]
    modified_nodes: List[str]
    added_edges: List[GraphEdgeModel]
    removed_edges: List[GraphEdgeModel]
    modified_edges: List[GraphEdgeModel]
    comparison_timestamp: datetime = Field(default_factory=datetime.now)
    summary: str = ""


class GraphVisualizationModel(BaseModel):
    """Model for graph visualization data"""
    nodes: List[Dict[str, Any]]
    links: List[Dict[str, Any]]
    layout: Literal["force", "hierarchical", "circular", "grid"] = "force"
    visualization_config: Dict[str, Any] = Field(default_factory=dict)
    highlighted_entities: List[str] = Field(default_factory=list)
    issue_entity: Optional[str] = None
    cluster_info: Dict[str, Any] = Field(default_factory=dict)


class GraphPersistenceModel(BaseModel):
    """Model for graph persistence metadata"""
    document_id: str
    incident_id: str
    primary_entity_guid: str
    storage_timestamp: datetime = Field(default_factory=datetime.now)
    version: int = Field(default=1, ge=1)
    compression_used: bool = False
    size_bytes: int = Field(default=0, ge=0)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class GraphRestoreModel(BaseModel):
    """Model for graph restoration from storage"""
    document_id: str
    incident_id: str
    restored_graph: GraphModel
    restoration_timestamp: datetime = Field(default_factory=datetime.now)
    version: int = Field(ge=1)
    restoration_successful: bool = True
    error_message: Optional[str] = None


class GraphAnalysisRequest(BaseModel):
    """Request model for graph analysis"""
    primary_entity_guid: str
    entity_type: str
    entity_name: str
    alert_category: str
    cluster_name: Optional[str] = None
    since_time_ms: Optional[int] = None
    until_time_ms: Optional[int] = None
    analysis_config: GraphAnalysisConfig = Field(default_factory=GraphAnalysisConfig)
    requested_analyses: List[Literal["cascading_failure", "critical_paths", "graph_metrics", "entity_search"]] = Field(
        default_factory=lambda: ["cascading_failure"]
    )


class GraphAnalysisResponse(BaseModel):
    """Response model for graph analysis"""
    request_id: str
    analysis_timestamp: datetime = Field(default_factory=datetime.now)
    graph_summary: GraphMetricsModel
    cascading_failure_analysis: Optional[CascadingFailureAnalysisModel] = None
    critical_paths: List[CriticalPathModel] = Field(default_factory=list)
    entity_search_results: List[EntitySearchResult] = Field(default_factory=list)
    visualization_data: Optional[GraphVisualizationModel] = None
    analysis_duration_ms: int = Field(default=0, ge=0)
    success: bool = True
    error_message: Optional[str] = None


class GraphAgentResponse(BaseModel):
    """Response model for graph analysis agent"""
    analysis_summary: str
    cascading_failure_analysis: CascadingFailureAnalysisModel
    critical_insights: List[str]
    risk_assessment: str
    recommended_actions: List[str]
    entities_to_monitor: List[str]
    estimated_resolution_time: str
    confidence_score: float = Field(ge=0.0, le=1.0)
    analysis_methodology: str
    limitations: List[str] = Field(default_factory=list)


class GraphAlertEnrichment(BaseModel):
    """Model for enriching alerts with graph analysis"""
    original_alert: Dict[str, Any]
    enriched_context: Dict[str, Any]
    related_entities: List[str]
    dependency_chain: List[str]
    impact_assessment: str
    urgency_score: float = Field(ge=0.0, le=1.0)
    recommended_escalation: bool = False
    enrichment_timestamp: datetime = Field(default_factory=datetime.now)


class GraphHealthCheck(BaseModel):
    """Model for graph service health checks"""
    service_status: Literal["healthy", "degraded", "unhealthy"]
    cache_status: Dict[str, Any] = Field(default_factory=dict)
    graph_count: int = Field(default=0, ge=0)
    last_analysis_time: Optional[datetime] = None
    performance_metrics: Dict[str, float] = Field(default_factory=dict)
    error_count: int = Field(default=0, ge=0)
    health_check_timestamp: datetime = Field(default_factory=datetime.now)


# Configuration models for entity relationships
class NodeTypeConfig(BaseModel):
    """Configuration for node types"""
    aliases: List[str] = Field(default_factory=list)
    metadata: List[str] = Field(default_factory=list)
    default_criticality: float = Field(default=0.5, ge=0.0, le=1.0)
    default_failure_probability: float = Field(default=0.1, ge=0.0, le=1.0)
    icon: Optional[str] = None
    color: Optional[str] = None


class RelationshipConfig(BaseModel):
    """Configuration for relationships"""
    target: str
    relation: str
    importance: Literal["low", "medium", "high"] = "medium"
    metrics: List[str] = Field(default_factory=list)
    query: Optional[str] = None
    weight: float = Field(default=1.0, ge=0.0)
    propagation_probability: float = Field(default=0.5, ge=0.0, le=1.0)


class AlertCategoryConfig(BaseModel):
    """Configuration for alert categories"""
    primary_entity_type: str
    traverse_relationships: List[Dict[str, Any]] = Field(default_factory=list)
    fallback_queries: List[Dict[str, Any]] = Field(default_factory=list)
    entity_discovery: Dict[str, Any] = Field(default_factory=dict)
    failure_characteristics: Dict[str, Any] = Field(default_factory=dict)


class GraphConfiguration(BaseModel):
    """Complete graph configuration"""
    entity_types: Dict[str, NodeTypeConfig] = Field(default_factory=dict)
    relationships: Dict[str, List[RelationshipConfig]] = Field(default_factory=dict)
    alert_categories: Dict[str, AlertCategoryConfig] = Field(default_factory=dict)
    analysis_config: GraphAnalysisConfig = Field(default_factory=GraphAnalysisConfig)
    aliases: Dict[str, str] = Field(default_factory=dict)
    last_updated: datetime = Field(default_factory=datetime.now)
    version: str = "1.0"