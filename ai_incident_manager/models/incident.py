"""
Pydantic models for the incident management system.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Literal, Union
from pydantic import BaseModel, Field


class MetricDataPoint(BaseModel):
    """Single data point for a metric"""
    timestamp: str  # ISO format
    value: float


class Metric(BaseModel):
    """Metric with time series data"""
    name: str
    data: List[MetricDataPoint]


class SystemCheckMetric(BaseModel):
    """Metric information for system check"""
    name: str
    value: str
    trend: Optional[str] = None


class SystemCheck(BaseModel):
    """System check result for the incident"""
    category: str
    status: Literal["ISSUES_FOUND", "ALL_GOOD"]
    description: str
    details: List[str] = Field(default_factory=list)
    metrics: List[SystemCheckMetric] = Field(default_factory=list)


class ActionStep(BaseModel):
    """Single step for a remediation action"""
    command: str
    explanation: str


class Action(BaseModel):
    """Remediation action for the incident"""
    title: str
    description: str
    steps: List[ActionStep] = Field(default_factory=list)


class LogEntry(BaseModel):
    """Log entry related to the incident"""
    timestamp: str  # ISO format
    level: str
    service: str
    message: str


class NRQLQuery(BaseModel):
    """NRQL query execution details"""
    name: str
    query: str
    observations: str


class InvestigationDetails(BaseModel):
    """Details from an investigation step"""
    nrqlQueries: List[NRQLQuery] = Field(default_factory=list)
    logEntries: List[Dict[str, Any]] = Field(default_factory=list)
    analysis: Optional[str] = None


class InvestigationStep(BaseModel):
    """Step in the incident investigation process"""
    step: int
    timestamp: str  # ISO format
    title: str
    description: str
    investigationDetails: InvestigationDetails = Field(default_factory=InvestigationDetails)


class EventTag(BaseModel):
    """Tag for timeline event"""
    label: str
    variant: str  # For UI rendering: "destructive", "secondary", etc.


class TimelineEvent(BaseModel):
    """Event in the incident timeline"""
    id: str
    incidentId: str
    timestamp: str  # ISO format
    title: str
    description: str
    type: Literal["alert", "investigation", "analysis", "action"]
    source: str
    tags: List[EventTag] = Field(default_factory=list)
    metric: Optional[Metric] = None


class Incident(BaseModel):
    """Main incident model"""
    id: str
    title: str
    status: Literal["Active", "Investigating", "Mitigated", "Resolved"]
    severity: Literal["Critical", "High", "Medium", "Low"]
    description: str
    customer: str
    startTime: str  # ISO format
    endTime: Optional[str] = None  # ISO format
    impact: str
    teams: List[str] = Field(default_factory=list)
    sources: List[str] = Field(default_factory=list)
    metrics: List[Metric] = Field(default_factory=list)
    systemChecks: List[SystemCheck] = Field(default_factory=list)
    actions: List[Action] = Field(default_factory=list)
    investigationSteps: List[InvestigationStep] = Field(default_factory=list)
    rootCause: Optional[str] = None
    preventiveActions: Optional[str] = None
    logs: List[LogEntry] = Field(default_factory=list)
    timelineEvents: List[TimelineEvent] = Field(default_factory=list)

    class Config:
        """Pydantic model configuration"""
        schema_extra = {
            "example": {
                "id": "763222ee-0a9e-4dce-a14e-8b24b3a11020",
                "title": "Login App Message Handler Pod Eviction Alert",
                "status": "Active",
                "severity": "Critical",
                "description": "Multiple login-app-message-handler-service pods have been evicted",
                "customer": "Neurons",
                "startTime": "2025-01-06T20:00:00.000Z",
                "impact": "Message handling service disruption affecting login functionality",
                "teams": ["Platform", "Login App"],
                "sources": ["New Relic"]
            }
        } 