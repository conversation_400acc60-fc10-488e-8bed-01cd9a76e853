"""
State schema for the incident analysis workflow.
"""

from typing import Dict, List, Any, Optional, Literal, Annotated, Union
from datetime import datetime, timezone
from operator import add
from pydantic import BaseModel, Field, field_validator


class Entity(BaseModel):
    """Unified entity model for all entity types"""
    entity_guid: str = Field(index=True)  # primary key for database
    entity_name: str
    entity_type: str = Field(index=True)  # useful for filtering
    cluster_name: Optional[str] = None
    namespace: Optional[str] = None
    product: Optional[Literal["neurons", "mdm"]]
    region: Optional[Literal["us", "eu"]]
    landscape: Optional[Literal["nvu", "uku", "mlu", "ttu", "tku", "na1", "na2", "ap1", "ap2", "eu1"]]
    is_primary: bool = False
    entity_missing: bool = False
    importance: Literal["low", "medium", "high"] = "medium"
    metadata: Dict[str, Any] = Field(default_factory=dict)
    importance_score: int = Field(ge=1, le=10)
    metrics_to_collect: List[str] = Field(default_factory=list)
    logs_to_collect: List[str] = Field(default_factory=list)
    
    # For database relationships
    incident_id: Optional[str] = Field(index=True)
    parent_entity_guid: Optional[str] = None

# Models from alert_parser_agent.py
class AlertParserAgentResponse(BaseModel):
    """Response model for alert parser agent."""
    alert_category: str
    alert_runbook: str
    entities: List[Entity]
    alert_title: str
    condition_name: str
    condition_id: str
    policy_name: str
    policy_id: str
    cluster_name: str
    product: Literal["neurons", "mdm"]
    nr_region: Literal["us", "eu"]
    landscape: Literal["nvu", "uku", "mlu", "ttu", "tku", "na1", "na2", "ap1", "ap2", "eu1"]
    region: str  # region of the product
    since_time: str  # ISO formatted time string for the start of analysis window
    until_time: str  # ISO formatted time string for the end of analysis window
    since_time_ms: int  # Epoch milliseconds for the start of analysis window
    until_time_ms: int  # Epoch milliseconds for the end of analysis window
    threshold_duration: int  # Alert condition threshold duration in seconds
    aggregation_window: int  # Alert condition aggregation window in seconds
    alert_created_at: int  # Original alert creation time in epoch milliseconds


class EntityRelationship(BaseModel):
    """A relationship between two entities."""
    source_guid: str
    target_guid: str
    relationship_type: str
    importance: str = "medium"


class EntityRelationshipAgentResponse(BaseModel):
    """Response model for entity relationship agent."""
    entities: List[Entity]
    relationships: List[EntityRelationship]
    entity_missing_strategy: Optional[str] = None
    fallback_entities_found: bool = False
    alert_based_discovery: bool = False
    total_entity_count: int


# Models from entity_analyzer_agent.py
# class MetricInfo(BaseModel):
#     """Information about a metric to collect for an entity."""
#     name: str
#     description: str
#     importance: int = Field(ge=1, le=10)


# class LogInfo(BaseModel):
#     """Information about logs to collect for an entity."""
#     type: str
#     importance: int = Field(ge=1, le=10)


class EntityAnalyzerAgentResponse(BaseModel):
    """Response model for entity analyzer agent."""
    entity_details: Entity
    significant_metrics: List[str]
    significant_logs: List[str]
    related_entities_to_check: List[Dict[str, Any]]
    entity_health_status: str
    potential_impact: str

class CollectedInformation(BaseModel):
    """Information collected during runbook execution."""
    id: str = Field(index=True)
    title: str
    description: str = ""
    content_type: Literal["metric", "log", "information"] = "information"  # One of: metric, log, information
    content_summary: str 
    entity_guid: str
    entity_name: Optional[str] = None
    entity_type: Optional[str] = None
    nrql_query: Optional[str] = None
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

# Models from runbook_agent.py
class RunbookStepResult(BaseModel):
    """Result of executing a runbook step."""
    step_title: str
    step_description: str 
    tool: str
    parameters: Dict[str, Any]
    summary: str 
    issues_found: bool 
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    collected_information: List[CollectedInformation] = Field(default_factory=list)


# class EntityRelationship(BaseModel):
#     """Relationship between entities."""
#     source_guid: str
#     source_name: str
#     source_type: str
#     target_guid: str
#     target_name: str
#     target_type: str
#     relationship_type: str
#     is_primary: bool = False


class RunbookExecutionAgentResponse(BaseModel):
    """Result of executing a runbook."""
    runbook_id: str
    runbook_name: str
    runbook_description: str
    execution_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    step_results: List[RunbookStepResult] = Field(default_factory=list)
    # collected_information: List[CollectedInformation] = Field(default_factory=list)
    issues_found: bool 
    summary: str 
    # findings: List[str] = Field(default_factory=list)
    # recommendations: List[str] = Field(default_factory=list)

class RemediationAction(BaseModel):
    """Remediation action for the incident"""
    title: str
    description: str
    # steps: List[Dict[str, str]] = Field(default_factory=list)  # List of {command, explanation} for reference
    steps: List[str] = Field(default_factory=list) # List of steps to perform the remediation

class RCAPrioritizedFactor(BaseModel):
    """Prioritized factor contributing to the incident"""
    factor: str
    importance: int = Field(ge=1, le=10)  # Scale of 1-10
    confidence: int = Field(ge=1, le=10)  # Scale of 1-10
    evidence: List[str] = Field(default_factory=list)
    related_entities: List[str] = Field(default_factory=list)

class RCAAgentResponse(BaseModel):
    """Response model for root cause analysis agent."""
    alert_summary: str
    primary_root_cause: str
    secondary_factors: List[str] = Field(default_factory=list)
    confidence_level: int = Field(ge=1, le=10)  # Scale of 1-10
    prioritized_factors: List[RCAPrioritizedFactor] = Field(default_factory=list)
    evidence_summary: str
    timeline_reconstruction: str
    affected_components: List[Dict[str, str]] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)
    uncertainty_factors: List[str] = Field(default_factory=list)
    remediation_actions: List[RemediationAction] = Field(default_factory=list)

# Model for database context agent
class DatabaseContextItem(BaseModel):
    """Individual context item retrieved from the database."""
    query_name: str
    query_type: str  # 'general', 'product_specific', 'rca'
    summary: str
    relevance_score: int = Field(ge=1, le=10)  # Scale of 1-10
    data_count: int  # Number of records retrieved
    key_findings: List[str] = Field(default_factory=list)

class DatabaseContextResponse(BaseModel):
    """Response model for database context agent."""
    context_summary: str  # Overall summary of the database context
    priority_findings: List[str] = Field(default_factory=list)  # Most important findings
    context_items: List[DatabaseContextItem] = Field(default_factory=list)  # Individual context items
    entities_examined: List[str] = Field(default_factory=list)  # List of entities that were examined
    additional_data_recommendations: List[str] = Field(default_factory=list)  # Recommendations for additional data
    confidence_level: int = Field(ge=1, le=10)  # Overall confidence in the relevance of the context


class MetricData(BaseModel):
    """Metric data for the incident"""
    name: str
    data: List[Dict[str, Any]] = Field(default_factory=list)  # List of {timestamp, value}


class SystemCheckResult(BaseModel):
    """Result of a system check"""
    category: str
    status: str  # "ISSUES_FOUND" or "ALL_GOOD"
    description: str
    details: List[str] = Field(default_factory=list)
    metrics: List[Dict[str, Any]] = Field(default_factory=list)  # List of {name, value, trend}

class InvestigationNote(BaseModel):
    """Note from the investigation process"""
    timestamp: str
    agent: str
    note: str
    data: Optional[Dict[str, Any]] = None


class EventTag(BaseModel):
    """Tag for timeline event"""
    label: str
    variant: str  # For UI rendering: "destructive", "secondary", etc.


class TimelineEvent(BaseModel):
    """Event in the incident timeline"""
    id: str
    incidentId: str
    timestamp: str  # ISO format
    title: str
    description: str
    type: Literal["alert", "investigation", "analysis", "action"]
    source: str
    tags: List[EventTag] = Field(default_factory=list)


class IncidentState(BaseModel):
    """State for the incident analysis workflow"""
    # Raw alert data
    raw_alert: Dict[str, Any] = Field(default_factory=dict)
    run_id: str

    # ado details
    ado_ticket_id: Optional[str] = None
    ado_ticket_url: Optional[str] = None
    dashboard_url: Optional[str] = None

    # front-end dashboard details
    dashboard_url: Optional[str] = None
    issue_url: Optional[str] = None
    
    # Extracted information
    incident_id: str
    title: str
    description: str
    severity: str
    start_time: str
    alert_category: str
    alert_runbook: str
    entities: List[Entity] = Field(default_factory=list)
    alert_title: str
    condition_name: str
    condition_id: Optional[str] = None
    policy_name: Optional[str] = None
    policy_id: Optional[str] = None

    entity_relationships: List[EntityRelationship] = Field(default_factory=list)
    
    # Fields from AlertParserResponse
    cluster_name: Optional[str] = None
    product: Optional[str] = None
    nr_region: Optional[str] = None
    landscape: Optional[str] = None
    region: Optional[str] = None  # region of the product
    
    # Time window information for metrics and logs analysis
    since_time: Optional[str] = None  # ISO formatted time string for the start of analysis window
    until_time: Optional[str] = None  # ISO formatted time string for the end of analysis window
    since_time_ms: Optional[int] = None  # Epoch milliseconds for the start of analysis window
    until_time_ms: Optional[int] = None  # Epoch milliseconds for the end of analysis window
    threshold_duration: Optional[int] = None  # Alert condition threshold duration in seconds
    aggregation_window: Optional[int] = None  # Alert condition aggregation window in seconds
    
    # Investigation state
    current_phase: str  # "initialize", "entity_analysis", "metrics_collection", "logs_analysis", "rca", "remediation"
    investigation_notes: Annotated[List[InvestigationNote], add] = Field(default_factory=list)
    timeline: Annotated[List[TimelineEvent], add] = Field(default_factory=list)
    
    # Analysis results
    metrics: List[Dict[str, Any]] = Field(default_factory=list)
    logs: List[Dict[str, Any]] = Field(default_factory=list)
    events: List[Dict[str, Any]] = Field(default_factory=list)
    system_checks: List[SystemCheckResult] = Field(default_factory=list)
    root_cause: Optional[str] = None
    rca_details: Dict[str, Any] = Field(default_factory=dict)  # Comprehensive RCA results in a single dictionary
    remediation_actions: List[RemediationAction] = Field(default_factory=list)
    analysis_summary: Optional[str] = None
    alert_summary: Optional[str] = None
    
    # Runbook results
    runbook_results: List[RunbookStepResult] = Field(default_factory=list)
    
    # Output
    # output_incident: Optional[Dict[str, Any]] = None