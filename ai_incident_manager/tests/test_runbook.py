#!/usr/bin/env python3
"""
Test script for runbook execution with the MetricsCollector service.

This script tests the execution of runbooks using the MetricsCollector service
instead of directly interacting with New Relic clients.
"""

import os
import json
import asyncio
import logging
from datetime import datetime, timedelta

import dotenv
from openai import AsyncAzureOpenAI
from devtools import pprint

from ai_incident_manager.services.runbook_service import get_runbook_service
from ai_incident_manager.services.metrics_collector import MetricsCollector
from ai_incident_manager.agents.runbook_agent import (
    runbook_agent, RunbookAgentDeps, RunbookExecutionResult
)

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_runbook_execution():
    """Test the execution of a runbook using the MetricsCollector service."""
    logger.info("Testing runbook execution with MetricsCollector...")
    
    # Initialize the MetricsCollector
    metrics_collector = MetricsCollector()
    
    # Initialize OpenAI client
    azure_openai_enabled = "AZURE_OPENAI_ENDPOINT" in os.environ and "AZURE_OPENAI_API_KEY" in os.environ
    
    if not azure_openai_enabled:
        raise ValueError("Azure OpenAI is required for this test")
        
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    
    # Set up dependencies for the runbook agent
    deps = RunbookAgentDeps(
        openai_client=openai_client,
        metrics_collector=metrics_collector
    )
    
    # Get a test entity GUID
    # NOTE: Replace with a real entity GUID for testing
    test_entity_guid = "TEST_ENTITY_GUID"  # This should be replaced with a real GUID
    
    # Test getting a runbook
    runbook_service = get_runbook_service()
    available_runbooks = runbook_service.get_all_runbooks()
    
    if not available_runbooks:
        logger.error("No runbooks found. Make sure runbooks.yaml is properly configured.")
        return
        
    test_runbook = available_runbooks[0]
    logger.info(f"Testing with runbook: {test_runbook['name']} (ID: {test_runbook['id']})")
    
    # Test the runbook agent with a recommendation request
    logger.info("Testing runbook agent with recommendations request...")
    recommendation_result = await runbook_agent.run(
        "What runbooks should I use for kubernetes pods with crashloopbackoff issues?", 
        deps=deps
    )
    logger.info(f"Recommendation result: {recommendation_result.data}")
    
    # Only run the entity-specific tests if a real entity GUID is provided
    if test_entity_guid != "TEST_ENTITY_GUID":
        try:
            # Test executing a runbook
            logger.info(f"Testing runbook execution with a real entity...")
            prompt = f"Execute runbook {test_runbook['id']} for entity {test_entity_guid}"
            execution_result = await runbook_agent.run(prompt, deps=deps)
            
            logger.info(f"Execution result summary: {execution_result.data.summary if hasattr(execution_result.data, 'summary') else ''}")
            
            if hasattr(execution_result.data, 'recommendations') and execution_result.data.recommendations:
                logger.info("Recommendations:")
                for rec in execution_result.data.recommendations:
                    logger.info(f"  - {rec}")
                    
        except Exception as e:
            logger.error(f"Error executing runbook: {str(e)}")
    else:
        logger.warning("No real entity GUID provided. Skipping entity-specific tests.")
        
        # Test with mock entity
        logger.info("Testing with mock entity (will likely fail with entity not found)...")
        mock_prompt = f"Execute runbook {test_runbook['id']} for entity TEST_ENTITY_GUID"
        try:
            mock_result = await runbook_agent.run(mock_prompt, deps=deps)
            logger.info(f"Mock execution result: {mock_result.data}")
        except Exception as e:
            logger.error(f"Expected error with mock entity: {str(e)}")
    
    logger.info("Tests completed!")


if __name__ == "__main__":
    asyncio.run(test_runbook_execution()) 