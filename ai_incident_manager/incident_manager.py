"""
Incident Manager module.

This module provides the IncidentManager class, which is responsible for coordinating
the incident management lifecycle, from creation to resolution.
"""

import json
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Union

from ai_incident_manager.database.postgres import PostgresDB
from ai_incident_manager.models.workflow_state import IncidentState
from ai_incident_manager.config.entity_relationships import RelationshipConfig
from ai_incident_manager.services.alert_category_service import get_alert_category_service

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class IncidentManager:
    """
    Incident Manager for coordinating incident lifecycle.
    
    This class is responsible for:
    - Creating and storing incidents
    - Updating incident status
    - Managing incident-related entities and metrics
    - Coordinating the analysis workflow
    """
    
    def __init__(self):
        """Initialize the Incident Manager."""
        self.db = PostgresDB()
        self.relationship_config = RelationshipConfig()
        self.category_service = get_alert_category_service()
        
        # Ensure the database tables exist
        self.db.create_tables()
        
    def create_incident(self, incident_data: Dict[str, Any]) -> str:
        """
        Create a new incident.
        
        Args:
            incident_data: Incident data
            
        Returns:
            Incident ID
        """
        # Generate an ID if not provided
        if 'id' not in incident_data:
            incident_data['id'] = str(uuid.uuid4())
            
        # Add creation timestamp if not present
        if 'startTime' not in incident_data:
            incident_data['startTime'] = datetime.utcnow().isoformat()
            
        # Set initial status if not specified
        if 'status' not in incident_data:
            incident_data['status'] = 'OPEN'
        
        # Add alert category and runbook if not present
        if 'alert_category' not in incident_data and 'title' in incident_data:
            condition_name = ""
            condition_id = None
            
            # Try to get condition ID and name from raw alert
            if 'raw_alert' in incident_data:
                raw_alert = incident_data['raw_alert']
                
                # Check for condition ID
                if 'alertConditionIds' in raw_alert and raw_alert['alertConditionIds']:
                    condition_id = raw_alert['alertConditionIds'][0]
                
                # Check for condition name
                if 'alertConditionNames' in raw_alert and raw_alert['alertConditionNames']:
                    condition_name = raw_alert['alertConditionNames'][0]
            
            # Get category from service
            category = self.category_service.get_category(
                alert_title=incident_data['title'],
                condition_name=condition_name,
                condition_id=condition_id
            )
            
            # Add category data to incident
            incident_data['alert_category'] = category['category']
            incident_data['runbook'] = category.get('runbook', '')
            incident_data['likely_causes'] = category.get('likely_causes', [])
            incident_data['metrics_to_check'] = category.get('metrics_to_check', [])
            
            # Add metrics to check from category if needed
            if 'metrics_to_check' in category and not incident_data.get('metrics'):
                incident_data['metrics'] = []
                for metric_name in category.get('metrics_to_check', []):
                    incident_data['metrics'].append({
                        'name': metric_name,
                        'data': []
                    })
        
        # Store in database
        incident_id = self.db.create_incident(incident_data)
        
        logger.info(f"Created incident: {incident_id}")
        return incident_id
    
    def get_incident(self, incident_id: str) -> Dict[str, Any]:
        """
        Get an incident by ID.
        
        Args:
            incident_id: Incident ID
            
        Returns:
            Incident data
        """
        return self.db.get_incident(incident_id)
    
    def update_incident_status(self, incident_id: str, status: str):
        """
        Update an incident's status.
        
        Args:
            incident_id: Incident ID
            status: New status
        """
        self.db.update_incident_status(incident_id, status)
        logger.info(f"Updated incident {incident_id} status to {status}")
    
    def add_investigation_step(self, incident_id: str, step_number: int, title: str, content: str, timestamp: Optional[str] = None):
        """
        Add an investigation step to an incident.
        
        Args:
            incident_id: Incident ID
            step_number: Step number
            title: Step title
            content: Step content
            timestamp: Optional timestamp (defaults to current time)
        """
        step_data = {
            'step_number': step_number,
            'title': title,
            'content': content,
            'timestamp': timestamp or datetime.utcnow().isoformat()
        }
        self.db.add_investigation_step(incident_id, step_data)
        logger.info(f"Added investigation step {step_number} to incident {incident_id}: {title}")
    
    def add_timeline_event(self, incident_id: str, event_data: Dict[str, Any]):
        """
        Add a timeline event to an incident.
        
        Args:
            incident_id: Incident ID
            event_data: Timeline event data
        """
        self.db.add_timeline_event(incident_id, event_data)
        logger.info(f"Added timeline event to incident {incident_id}: {event_data.get('title')}")
    
    def process_alert(self, alert_data: Dict[str, Any]) -> str:
        """
        Process an alert and create or update an incident.
        
        Args:
            alert_data: Alert data from the message receiver
            
        Returns:
            Incident ID
        """
        # Extract incident ID from the alert if available
        incident_id = str(alert_data.get('incident_id', ''))
        
        # Check if this incident already exists
        existing_incident = None
        if incident_id:
            existing_incident = self.get_incident(incident_id)
        
        if existing_incident:
            logger.info(f"Updating existing incident: {incident_id}")
            # TODO: Update the existing incident with new information
            return incident_id
        else:
            # Create a new incident
            logger.info("Creating new incident from alert")
            return self.create_incident(alert_data)
    
    def find_related_entities(self, incident_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Find related entities based on the incident data.
        
        Args:
            incident_data: Incident data
            
        Returns:
            List of related entities
        """
        # Get source entity type from incident data
        source_entity_type = None
        if 'alert' in incident_data:
            source_entity_type = incident_data['alert'].get('type')
        
        if not source_entity_type and 'entities' in incident_data and incident_data['entities']:
            # Use the first entity type as source
            source_entity_type = incident_data['entities'][0].get('type')
        
        if not source_entity_type:
            logger.warning("Could not determine source entity type for finding relationships")
            return []
        
        # Get alert title/description for condition matching
        alert_condition = incident_data.get('title', '')
        if 'description' in incident_data:
            alert_condition += " " + incident_data['description']
        
        # Find related entities using the relationship config
        return self.relationship_config.find_related_entities(
            source_type=source_entity_type,
            alert_condition=alert_condition
        )
    
    def prepare_workflow_state(self, incident_data: Dict[str, Any]) -> IncidentState:
        """
        Prepare the workflow state for an incident.
        
        Args:
            incident_data: Incident data
            
        Returns:
            Workflow state
        """
        # Create workflow state from incident data
        state: IncidentState = {
            "incident_id": incident_data.get('id', ''),
            "raw_alert": incident_data.get('raw_alert', {}),
            "entities": [],
            "metrics": [],
            "logs": [],
            "system_checks": [],
            "investigation_state": {
                "current_step": 0,
                "next_steps": [],
                "completed_steps": [],
                "notes": []
            },
            "remediation": {
                "actions": [],
                "verified": False,
                "verification_notes": ""
            },
            "output": {
                "title": incident_data.get('title', ''),
                "summary": incident_data.get('description', ''),
                "severity": incident_data.get('severity', 'INFO'),
                "status": incident_data.get('status', 'OPEN'),
                "root_cause": "",
                "resolution": "",
                "timeline": incident_data.get('timelineEvents', []),
                "recommendations": []
            }
        }
        
        # Add alert category and runbook information
        if 'alert_category' in incident_data:
            state["alert_category"] = incident_data['alert_category']
            state["runbook"] = incident_data.get('runbook', '')
            state["likely_causes"] = incident_data.get('likely_causes', [])
            
            # Add to output
            state["output"]["alert_category"] = incident_data['alert_category']
            if incident_data.get('runbook'):
                state["output"]["runbook"] = incident_data['runbook']
            
            # Add likely causes to summary
            if incident_data.get('likely_causes'):
                causes_text = "\n\nPotential causes:\n"
                for cause in incident_data['likely_causes']:
                    causes_text += f"- {cause}\n"
                state["output"]["summary"] += causes_text
        
        # Add entities from the incident data
        if 'entities' in incident_data and incident_data['entities']:
            for entity in incident_data['entities']:
                state['entities'].append({
                    "id": entity.get('id', ''),
                    "name": entity.get('name', ''),
                    "type": entity.get('type', ''),
                    "metrics": [],
                    "logs": [],
                    "related_entities": [],
                    "metadata": entity.get('metadata', {})
                })
        
        # Add metrics from the incident data
        if 'metrics' in incident_data and incident_data['metrics']:
            for metric in incident_data['metrics']:
                state['metrics'].append({
                    "name": metric.get('name', ''),
                    "entity_id": state['entities'][0]['id'] if state['entities'] else None,
                    "data": metric.get('data', []),
                    "unit": metric.get('unit', ''),
                    "threshold": metric.get('threshold', None)
                })
        
        return state
    
    def find_similar_incidents(self, title: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Find similar incidents based on title.
        
        Args:
            title: Incident title to match against
            limit: Maximum number of results
            
        Returns:
            List of similar incidents
        """
        return self.db.find_similar_incidents(title, limit)
    
    def get_similar_root_causes(self, entity_type: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get similar root causes for the same entity type.
        
        Args:
            entity_type: Entity type to match
            limit: Maximum number of results
            
        Returns:
            List of incidents with related root causes
        """
        return self.db.get_similar_root_causes(entity_type, limit) 