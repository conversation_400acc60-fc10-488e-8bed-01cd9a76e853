"""
Alert categories configuration.

This module provides backward compatibility for the AlertCategoryManager class.
It's recommended to use the AlertCategoryService from ai_incident_manager.services.alert_category_service instead.
"""

import os
import re
import yaml
import logging
import warnings
from typing import Dict, List, Any, Optional

import dotenv
from ai_incident_manager.services.alert_category_service import get_alert_category_service

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AlertCategoryManager:
    """
    DEPRECATED: Manager for alert categories and runbooks.
    
    This class is provided for backward compatibility.
    Please use AlertCategoryService from ai_incident_manager.services.alert_category_service instead.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the alert category manager.
        
        Args:
            config_path: Path to the YAML configuration file
        """
        warnings.warn(
            "AlertCategoryManager is deprecated. "
            "Please use AlertCategoryService from ai_incident_manager.services.alert_category_service instead.",
            DeprecationWarning, 
            stacklevel=2
        )
        self._service = get_alert_category_service()
        self.categories = self._service.get_all_categories()
    
    def load_config(self, config_path: str) -> None:
        """
        DEPRECATED: Load alert categories from a YAML config file.
        
        Args:
            config_path: Path to the YAML configuration file
        """
        warnings.warn(
            "load_config is deprecated. "
            "Please use AlertCategoryService which automatically loads config.",
            DeprecationWarning, 
            stacklevel=2
        )
        # No-op since the service already loads the config
        pass
    
    def get_category(self, alert_title: str, condition_name: str) -> Dict[str, Any]:
        """
        Find the matching category for an alert based on title and condition.
        
        Args:
            alert_title: Alert title to match
            condition_name: Alert condition name to match
            
        Returns:
            Dictionary with category information
        """
        return self._service.get_category(alert_title, condition_name)
    
    def get_all_categories(self) -> List[Dict[str, Any]]:
        """Get all defined alert categories."""
        return self._service.get_all_categories()
    
    def add_category(self, category: Dict[str, Any]) -> None:
        """
        DEPRECATED: Add a new alert category.
        
        Args:
            category: Dictionary with category information
        """
        warnings.warn(
            "add_category is deprecated and no longer effective. "
            "Please update the YAML configuration file directly.",
            DeprecationWarning, 
            stacklevel=2
        )
        # No-op as this functionality is removed
        pass 