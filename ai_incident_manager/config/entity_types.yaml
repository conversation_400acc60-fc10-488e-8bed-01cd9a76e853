# Entity Types Configuration
#
# This file defines entity types, their relationships, and metrics to collect.
# Each entity type includes:
# - type: Unique identifier for the entity type
# - metrics: List of key metrics to collect for this entity type
# - logs: Types of logs to collect for this entity type
# - related_entities: Types of entities that may be related
# - importance: How critical this entity type is to incident analysis (1-10)

entity_types:
  # Kubernetes Pod
  - type: KUBERNETES_POD
    description: "Kubernetes pod running containers"
    metrics:
      - name: cpu_usage
        description: "CPU usage percentage"
        importance: 9
      - name: memory_usage
        description: "Memory usage in bytes"
        importance: 9
      - name: restarts_count
        description: "Number of container restarts"
        importance: 10
      - name: network_receive_bytes
        description: "Network bytes received"
        importance: 7
      - name: network_transmit_bytes
        description: "Network bytes transmitted"
        importance: 7
    logs:
      - type: container_logs
        importance: 10
      - type: pod_events
        importance: 9
    related_entities:
      - type: KUBERNETES_NODE
        relationship: "runs_on"
        importance: 8
      - type: KUBERNETES_DEPLOYMENT
        relationship: "managed_by"
        importance: 7
      - type: KUBERNETES_SERVICE
        relationship: "exposed_by"
        importance: 6
    importance: 9

  # Kubernetes Node
  - type: KUBERNETES_NODE
    description: "Kubernetes worker node hosting pods"
    metrics:
      - name: cpu_usage
        description: "CPU usage percentage"
        importance: 9
      - name: memory_usage
        description: "Memory usage in bytes"
        importance: 9
      - name: disk_usage
        description: "Disk usage percentage"
        importance: 8
      - name: pod_count
        description: "Number of pods running on the node"
        importance: 7
      - name: network_receive_bytes
        description: "Network bytes received"
        importance: 6
      - name: network_transmit_bytes
        description: "Network bytes transmitted"
        importance: 6
    logs:
      - type: node_events
        importance: 8
      - type: kubelet_logs
        importance: 7
    related_entities:
      - type: KUBERNETES_POD
        relationship: "hosts"
        importance: 9
      - type: KUBERNETES_CLUSTER
        relationship: "part_of"
        importance: 7
    importance: 8

  # Kubernetes Deployment
  - type: KUBERNETES_DEPLOYMENT
    description: "Kubernetes deployment managing replica sets and pods"
    metrics:
      - name: desired_replicas
        description: "Desired number of replicas"
        importance: 9
      - name: available_replicas
        description: "Number of available replicas"
        importance: 9
      - name: unavailable_replicas
        description: "Number of unavailable replicas"
        importance: 9
      - name: updated_replicas
        description: "Number of updated replicas"
        importance: 8
    logs:
      - type: deployment_events
        importance: 9
    related_entities:
      - type: KUBERNETES_POD
        relationship: "manages"
        importance: 9
      - type: KUBERNETES_SERVICE
        relationship: "exposed_by"
        importance: 7
    importance: 8

  # Database
  - type: DATABASE
    description: "Database instance"
    metrics:
      - name: queries_per_second
        description: "Number of queries per second"
        importance: 9
      - name: connection_count
        description: "Number of active connections"
        importance: 8
      - name: query_execution_time
        description: "Average query execution time"
        importance: 9
      - name: cpu_usage
        description: "CPU usage percentage"
        importance: 8
      - name: memory_usage
        description: "Memory usage in bytes"
        importance: 8
      - name: disk_usage
        description: "Disk usage percentage"
        importance: 8
    logs:
      - type: db_logs
        importance: 9
      - type: slow_query_logs
        importance: 10
    related_entities:
      - type: APPLICATION
        relationship: "used_by"
        importance: 8
      - type: KUBERNETES_POD
        relationship: "runs_in"
        importance: 7
    importance: 9

  # Application Service
  - type: APPLICATION
    description: "Application service"
    metrics:
      - name: response_time
        description: "Average response time"
        importance: 10
      - name: error_rate
        description: "Error rate percentage"
        importance: 10
      - name: throughput
        description: "Requests per minute"
        importance: 9
      - name: apdex_score
        description: "Application performance index score"
        importance: 8
      - name: cpu_usage
        description: "CPU usage percentage"
        importance: 7
      - name: memory_usage
        description: "Memory usage in bytes"
        importance: 7
    logs:
      - type: application_logs
        importance: 10
      - type: error_logs
        importance: 10
    related_entities:
      - type: DATABASE
        relationship: "uses"
        importance: 8
      - type: EXTERNAL_SERVICE
        relationship: "calls"
        importance: 7
      - type: KUBERNETES_POD
        relationship: "runs_in"
        importance: 8
    importance: 10

  # External Service (API, Cloud Service)
  - type: EXTERNAL_SERVICE
    description: "External service or API"
    metrics:
      - name: response_time
        description: "Average response time"
        importance: 9
      - name: error_rate
        description: "Error rate percentage"
        importance: 9
      - name: availability
        description: "Service availability percentage"
        importance: 10
      - name: throughput
        description: "Requests per minute"
        importance: 8
    logs:
      - type: api_logs
        importance: 8
    related_entities:
      - type: APPLICATION
        relationship: "called_by"
        importance: 8
    importance: 7

  # Message Queue
  - type: MESSAGE_QUEUE
    description: "Message queue (Kafka, RabbitMQ, etc.)"
    metrics:
      - name: message_rate
        description: "Messages per second"
        importance: 9
      - name: queue_depth
        description: "Number of messages in queue"
        importance: 9
      - name: consumer_lag
        description: "Consumer lag"
        importance: 10
      - name: producer_throughput
        description: "Producer throughput"
        importance: 8
      - name: consumer_throughput
        description: "Consumer throughput"
        importance: 8
      - name: cpu_usage
        description: "CPU usage percentage"
        importance: 7
      - name: memory_usage
        description: "Memory usage in bytes"
        importance: 7
    logs:
      - type: broker_logs
        importance: 9
    related_entities:
      - type: APPLICATION
        relationship: "used_by"
        importance: 8
      - type: KUBERNETES_POD
        relationship: "runs_in"
        importance: 7
    importance: 8

  # Container
  - type: CONTAINER
    description: "Container running within a Kubernetes pod"
    metrics:
      - name: cpu_usage
        description: "CPU usage percentage"
        importance: 9
      - name: memory_usage
        description: "Memory usage in bytes"
        importance: 9
      - name: restart_count
        description: "Number of container restarts"
        importance: 10
      - name: state
        description: "Container state (running, waiting, terminated)"
        importance: 10
      - name: termination_reason
        description: "Reason for container termination"
        importance: 10
      - name: oom_killed
        description: "Whether container was killed due to OOM"
        importance: 10
    logs:
      - type: container_logs
        importance: 10
      - type: container_events
        importance: 9
    related_entities:
      - type: KUBERNETES_POD
        relationship: "contained_by"
        importance: 10
      - type: KUBERNETES_DEPLOYMENT
        relationship: "managed_by"
        importance: 7
      - type: APPLICATION
        relationship: "runs"
        importance: 8
    importance: 10
