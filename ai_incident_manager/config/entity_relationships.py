"""
Configuration for entity relationships and alert routing.
"""

import os
import yaml
from typing import Dict, List, Optional, Any, TypedDict
import re


class SourceEntity(TypedDict):
    """Source entity configuration for relationship mapping"""
    type: str  # Alert type or entity type
    condition: str  # Condition for matching (regex pattern)
    metrics: List[str]  # Default metrics to collect


class TargetEntity(TypedDict):
    """Target entity configuration for relationship mapping"""
    type: str  # Entity type to find
    pattern: str  # Pattern to match entity name
    metrics: List[str]  # Metrics to collect


class EntityRelationship(TypedDict):
    """Relationship between source and target entities"""
    source: SourceEntity
    target_entities: List[TargetEntity]


class RelationshipConfig:
    """Configuration manager for entity relationships"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the relationship configuration manager
        
        Args:
            config_path: Path to the relationships configuration YAML file.
                         If None, uses the default config/entity_relationships.yaml.
        """
        if config_path is None:
            # Default location relative to the package
            module_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(module_dir, '..', '..', 'config', 'entity_relationships.yaml')
            
        self.config = self._load_config(config_path)
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load the relationship configuration from YAML file
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dictionary containing the configuration
        """
        if not os.path.exists(config_path):
            print(f"Warning: Relationship configuration file not found at {config_path}")
            return {"relationships": []}
            
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"Error loading relationship configuration: {str(e)}")
            return {"relationships": []}
    
    def find_related_entities(self, alert_data: Dict[str, Any]) -> List[TargetEntity]:
        """
        Find related entities based on alert data
        
        Args:
            alert_data: Alert data from New Relic
            
        Returns:
            List of target entity configurations for the alert
        """
        alert_title = alert_data.get('title', '')
        alert_type = "NRQL_ALERT"  # Default type for New Relic alerts
        
        related_entities = []
        
        # Check each relationship in the configuration
        for relationship in self.config.get('relationships', []):
            source = relationship.get('source', {})
            source_type = source.get('type', '')
            source_condition = source.get('condition', '')
            
            # Check if this relationship applies to the alert
            if source_type == alert_type:
                # Try to match the condition as a regex pattern
                try:
                    if re.search(source_condition, alert_title, re.IGNORECASE):
                        # This relationship matches the alert
                        related_entities.extend(relationship.get('target_entities', []))
                except re.error:
                    # Invalid regex pattern, skip this relationship
                    pass
        
        return related_entities
        
    def get_all_relationships(self) -> List[EntityRelationship]:
        """Get all defined entity relationships"""
        return self.config.get('relationships', [])


# Default relationship configuration
DEFAULT_RELATIONSHIPS = {
    "relationships": [
        {
            "source": {
                "type": "NRQL_ALERT",
                "condition": ".*debezium.*lag.*",
                "metrics": []
            },
            "target_entities": [
                {
                    "type": "KUBERNETES_POD",
                    "pattern": "^debezium-.*",
                    "metrics": ["cpu", "memory", "restarts", "network_received", "network_transmitted"]
                },
                {
                    "type": "APPLICATION",
                    "pattern": "kafka",
                    "metrics": ["throughput", "consumer_lag", "broker_count", "topic_partition_count"]
                }
            ]
        },
        {
            "source": {
                "type": "NRQL_ALERT",
                "condition": ".*evicted.*pod.*",
                "metrics": []
            },
            "target_entities": [
                {
                    "type": "KUBERNETES_NODE",
                    "pattern": ".*",
                    "metrics": ["cpu", "memory", "disk", "network_received", "network_transmitted"]
                },
                {
                    "type": "KUBERNETES_POD",
                    "pattern": ".*",
                    "metrics": ["status", "restarts", "cpu", "memory"]
                }
            ]
        }
    ]
} 