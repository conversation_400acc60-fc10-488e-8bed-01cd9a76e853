# Alert Categories Configuration
#
# This file defines alert categories, patterns, and associated runbooks.
# Each category includes:
# - category: Unique identifier for the category
# - condition_ids: List of specific New Relic condition IDs that belong to this category
# - title_pattern: Regular expression to match alert titles (fallback if no condition_id match)
# - condition_pattern: Regular expression to match alert condition names (fallback if no condition_id match)
# - description: Description of what this alert type represents
# - likely_causes: List of likely causes for this type of alert
# - metrics: List of metrics that should be examined
# - entity_relationships: Related entities to investigate
# - entity_relationship_mapping: Structured mapping of relationships to traverse for incident analysis
# - runbook: Step-by-step guide for resolving this type of alert, with tools to execute

condition_categories:
  # CrashLoopBackOff Conditions
  - category: kubernetes_crashloopbackoff
    condition_ids:
      - "25541032" # Pod with CrashLoopBackOff
      - "31089733" # Container restarts > 10 in 15 minutes
    title_pattern: "(?i).*crashloopbackoff.*pod.*"
    condition_pattern: "(?i).*pod.*crashloopbackoff.*"
    description: "Pod is in CrashLoopBackOff state, indicating it's repeatedly crashing after restart"
    likely_causes:
      - "Application error or bug causing crash"
      - "Container resource limits too restrictive"
      - "Configuration issue with the container"
      - "Dependency not available"
      - "Container args or command issue"
    metrics:
      - name: container_restarts
      - name: pod_status
      - name: memory_usage
      - name: cpu_usage
    entity_relationships:
      - target_type: KUBERNETES_POD
        metrics: ["pod_status", "container_status", "restart_count"]
      - target_type: KUBERNETES_NODE
        metrics: ["node_cpu", "node_memory"]
      - target_type: KUBERNETES_DEPLOYMENT
        metrics: ["deployment_status"]
    # Enhanced entity relationship mapping for automated traversal with architectural augmentation
    entity_relationship_mapping:
      primary_entity: "CONTAINER"
      
      # Enable architectural relationship discovery for service dependencies
      architectural_augmentation: true
      architecture_scope: "mi_production_architecture"
      
      traverse_relationships:
        # Standard telemetry-based relationships
        - source_type: "CONTAINER"
          target_type: "KUBERNETES_POD"
          relationship: "CONTAINED_BY"
          importance: "high"
          discovery_method: "telemetry"
          metrics_to_collect:
            ["cpu_usage", "memory_usage", "restart_count", "container_state"]
          continue_traversal_if:
            - "restart_count > 10"
            - "cpu_usage > 0.9"
            - "memory_usage > 0.85"
            - "container_state != 'running'"
            
        - source_type: "KUBERNETES_POD"
          target_type: "KUBERNETES_NODE"
          relationship: "RUNS_ON"
          importance: "medium"
          discovery_method: "telemetry"
          metrics_to_collect:
            ["cpu_pressure", "memory_pressure", "disk_pressure", "pod_count"]
          continue_traversal_if:
            - "cpu_pressure == true"
            - "memory_pressure == true"
            - "disk_pressure == true"
            
        - source_type: "KUBERNETES_POD"
          target_type: "KUBERNETES_DEPLOYMENT"
          relationship: "MANAGED_BY"
          importance: "medium"
          discovery_method: "telemetry"
          metrics_to_collect:
            ["available_replicas", "desired_replicas", "updated_replicas"]
          continue_traversal_if:
            - "available_replicas < desired_replicas"
            - "updated_replicas < desired_replicas"
            
        # Architectural relationships for service dependencies that could cause crashes
        - source_type: "APPLICATION"
          target_type: "APPLICATION"
          relationship: "DEPENDS_ON"
          importance: "critical"
          discovery_method: "architectural_config"
          validation_required: true
          metrics_to_collect: ["error_rate", "response_time", "availability"]
          continue_traversal_if:
            - "error_rate > 0.10"  # Higher threshold for crash scenarios
            - "response_time > 1000"
            - "availability < 0.90"
            
        - source_type: "APPLICATION"
          target_type: "DATABASE"
          relationship: "STORES_DATA"
          importance: "critical"
          discovery_method: "architectural_config"
          validation_required: true
          metrics_to_collect: ["db_connection_error_rate", "connection_pool_usage"]
          continue_traversal_if:
            - "db_connection_error_rate > 0.05"
            - "connection_pool_usage > 0.95"
    runbook: |
      1. Fetch pod description and details:
         - Tool: get_pod_description(pod_name)
         
      2. Check Kubernetes events for all related entities:
         - Tool: get_pod_events(pod_name)
         - Tool: get_node_events(node_name)
         
      3. Check resource usage across entities:
         - Tool: get_pod_metrics(pod_name, ["cpu", "memory"])
      
      4. Check container logs:
         - Tool: get_pod_logs(pod_name)
  
  - category: kubernetes_evicted_pod
    condition_ids:
      - "31365356" # Evicted Pods
    title_pattern: "(?i).*evicted.*pod.*"
    condition_pattern: "(?i).*pod.*evict.*"
    description: "Pod has been evicted from a node, usually due to resource pressure"
    likely_causes:
      - "Node memory pressure"
      - "Node disk pressure"
      - "High resource usage from other pods"
      - "Node maintenance or drain operation"
    metrics:
      - name: node_memory_pressure
      - name: node_disk_pressure
      - name: pod_memory_usage
      - name: pod_cpu_usage
    entity_relationships:
      - target_type: KUBERNETES_NODE
        metrics: ["node_memory", "node_disk", "node_pressure_conditions"]
    # Enhanced entity relationship mapping for automated traversal with architectural augmentation
    entity_relationship_mapping:
      primary_entity_types: ["KUBERNETES_POD", "K8S_POD"]
      
      # Enable architectural relationship discovery for resource pressure analysis
      architectural_augmentation: true
      architecture_scope: "mi_production_architecture"
      
      traverse_relationships:
        # Standard telemetry-based relationships
        - source_type: "KUBERNETES_POD"
          target_type: "KUBERNETES_NODE"
          relationship: "RUNS_ON"
          importance: "high"
          discovery_method: "telemetry"
          metrics_to_collect:
            ["cpu_pressure", "memory_pressure", "disk_pressure", "pod_count"]
          continue_traversal_if:
            - "cpu_pressure == true"
            - "memory_pressure == true"
            - "disk_pressure == true"
            - "pod_count > 80"  # High pod density
            
        - source_type: "KUBERNETES_POD"
          target_type: "KUBERNETES_DEPLOYMENT"
          relationship: "MANAGED_BY"
          importance: "medium"
          discovery_method: "telemetry"
          metrics_to_collect:
            ["available_replicas", "desired_replicas", "updated_replicas"]
          continue_traversal_if:
            - "available_replicas < desired_replicas"
            - "updated_replicas < desired_replicas"
            
        # Architectural relationships for services that could cause resource pressure
        - source_type: "APPLICATION"
          target_type: "APPLICATION"
          relationship: "DEPENDS_ON"
          importance: "medium"
          discovery_method: "architectural_config"
          validation_required: true
          metrics_to_collect: ["memory_usage", "cpu_usage", "connection_count"]
          continue_traversal_if:
            - "memory_usage > 0.8"
            - "cpu_usage > 0.8"
            - "connection_count > 1000"
            
        - source_type: "APPLICATION"
          target_type: "CACHE"
          relationship: "CACHES_TO"
          importance: "medium"
          discovery_method: "architectural_config"
          validation_required: true
          metrics_to_collect: ["memory_usage", "eviction_rate"]
          continue_traversal_if:
            - "memory_usage > 0.9"
            - "eviction_rate > 0.1"
    runbook: |
      1. Check node resource pressure:
         - Tool: get_node_conditions(node_name)
      2. Check memory consumption:
         - Tool: get_node_metrics(node_name, ["memory"])
      3. Check disk usage:
         - Tool: get_node_metrics(node_name, ["disk"])
      4. Review pod resource requests:
         - Tool: get_pod_spec(pod_name)
      5. Consider scaling up resources:
         - Tool: suggest_resource_adjustments(pod_name)

  # FailedScheduling Conditions
  - category: kubernetes_scheduling_failure
    condition_ids:
      - "25541421" # FailedScheduling event for pods
      - "48131767" # FailedScheduling event for Edge MongoDB pods
    title_pattern: "(?i).*failedscheduling.*"
    condition_pattern: "(?i).*failed.*schedul.*"
    description: "Kubernetes scheduler is unable to assign the pod to a node"
    likely_causes:
      - "Insufficient node resources"
      - "Node selector constraints not met"
      - "Taint/toleration mismatches"
      - "Pod anti-affinity rules"
      - "PersistentVolume not available"
    metrics:
      - name: node_cpu_allocatable
      - name: node_memory_allocatable
      - name: node_pod_allocatable
    entity_relationships:
      - target_type: KUBERNETES_NODE
        metrics: ["node_cpu", "node_memory", "node_pods"]
      - target_type: KUBERNETES_CLUSTER
        metrics: ["cluster_allocatable"]
    runbook: |
      1. Check node capacity:
         - Tool: get_nodes_capacity()
      2. Check pod resource requests:
         - Tool: get_pod_spec(pod_name)
      3. Verify node selectors and taints:
         - Tool: get_node_taints(cluster_name)
      4. Examine scheduling events:
         - Tool: get_pod_events(pod_name)
      5. Consider scaling the cluster:
         - Tool: suggest_cluster_scaling(cluster_name)

  # Debezium Lag
  - category: debezium_lag
    condition_ids: [] # Add specific condition IDs if available
    title_pattern: "(?i).*debezium.*lag.*"
    condition_pattern: "(?i).*debezium.*lag.*"
    description: "Debezium connector is experiencing lag, causing delay in CDC data replication"
    likely_causes:
      - "High write load on source database"
      - "Network issues between source and Kafka"
      - "Kafka consumer group issues"
      - "Connector configuration issues"
      - "Resource constraints on Debezium container"
    metrics:
      - name: debezium_lag_records
      - name: debezium_events_per_second
      - name: kafka_topic_partition_lag
      - name: source_database_load
      - name: network_latency
    entity_relationships:
      - target_type: KUBERNETES_POD
        metrics: ["cpu_usage", "memory_usage"]
      - target_type: DATABASE
        metrics: ["connection_count", "query_rate"]
    runbook: |
      1. Check Debezium logs:
         - Tool: get_pod_logs(pod_name, container="debezium")
      2. Verify Kafka health:
         - Tool: check_kafka_topics(topic_name)
      3. Check source database load:
         - Tool: get_database_metrics(db_name)
      4. Review connector config:
         - Tool: get_config_map(namespace, "debezium-config")
      5. Consider scaling resources:
         - Tool: suggest_resource_adjustment(deployment_name)

  # Query Result Threshold
  - category: query_result_threshold
    condition_ids:
      - "32549584" # Config Proxy Service Request takes > 20000 ms
    title_pattern: "(?i).*query result.*>.*"
    condition_pattern: "(?i).*query result.*"
    description: "NRQL query result has exceeded a predefined threshold"
    likely_causes:
      - "Application performance degradation"
      - "Increased error rates"
      - "Resource constraints"
      - "Unexpected traffic patterns"
    metrics:
      - name: response_time
      - name: error_rate
      - name: throughput
      - name: cpu_usage
      - name: memory_usage
    entity_relationships:
      - target_type: APPLICATION
        metrics: ["apdex", "error_rate", "throughput"]
      - target_type: DATABASE
        metrics: ["query_time", "connection_count"]
    runbook: |
      1. Review the NRQL query:
         - Tool: get_condition_details(condition_id)
      2. Check application metrics:
         - Tool: get_application_metrics(app_name)
      3. Check associated infrastructure:
         - Tool: get_entity_metrics(entity_guid)
      4. Look for recent changes:
         - Tool: get_deployment_history(service_name)
      5. Analyze logs for errors:
         - Tool: search_logs(entity_guid, "error")

  # RDS Swap Usage High
  - category: rds_swap_usage_high
    condition_ids:
      - "36691411" # Swap Usage High on RDS Instance
    title_pattern: "(?i).*swap usage.*>.*"
    condition_pattern: "(?i).*swap usage.*"
    description: "RDS instance is experiencing high swap usage, indicating potential memory pressure or inefficient workload management. Elevated swap usage can degrade database performance and may be a symptom of resource constraints or suboptimal query execution."
    likely_causes:
      - "Insufficient allocated memory for the instance workload"
      - "Heavy or inefficient queries leading to increased memory swapping"
      - "Misconfigured database parameters or instance sizing"
      - "Underlying hardware performance issues or resource contention"
    metrics:
      - name: rds_swap_usage_percent
      - name: rds_memory_usage
      - name: rds_disk_io
    entity_relationships:
      - target_type: DATABASE
        metrics: ["swap_usage", "memory", "disk_io"]
    runbook: |
      1. Verify RDS instance metrics to confirm high swap usage:
         - Tool: get_rds_metrics(instance_id, ["swap_usage_percent", "memory_usage"])
      2. Review query performance and workload patterns:
         - Tool: analyze_query_performance(instance_id)
      3. Evaluate instance size and configuration settings:
         - Tool: get_instance_configuration(instance_id)
      4. Consider optimizing queries or scaling the instance memory:
         - Tool: suggest_instance_scaling(instance_id)
      5. Monitor swap usage trends after adjustments:
         - Tool: monitor_rds_swap_trends(instance_id)

  # Kubernetes Deployment Unavailable Pods
  - category: kubernetes_deployment_unavailable_pods
    condition_ids:
      - "44945494" # Percentage of Unavailable pods > 25% for cluster
    title_pattern: "(?i).*unavailable pods.*>.*25%.*"
    condition_pattern: "(?i).*percentage of unavailable pods.*>.*25%.*"
    description: "Kubernetes deployment has a significant percentage of unavailable pods, indicating potential issues with pod scheduling, resource constraints, or application errors"
    likely_causes:
      - "Pod scheduling failures due to resource constraints"
      - "Application crashes or errors within pods"
      - "Node failures or resource exhaustion"
      - "Configuration issues with deployments or pods"
      - "Network connectivity problems between nodes"
    metrics:
      - name: deployment_available_replicas
      - name: deployment_unavailable_replicas
      - name: pod_status
      - name: node_resource_utilization
      - name: container_restarts
    entity_relationships:
      - target_type: KUBERNETES_POD
        metrics: ["pod_status", "container_status", "restart_count"]
      - target_type: KUBERNETES_NODE
        metrics: ["cpu_usage", "memory_usage", "node_conditions"]
      - target_type: KUBERNETES_DEPLOYMENT
        metrics:
          ["available_replicas", "unavailable_replicas", "desired_replicas"]
    # Enhanced entity relationship mapping for automated traversal with architectural augmentation
    entity_relationship_mapping:
      primary_entity_types: ["KUBERNETES_DEPLOYMENT", "K8S_DEPLOYMENT"]
      
      # Enable architectural relationship discovery for business logic dependencies
      architectural_augmentation: true
      architecture_scope: "mi_production_architecture"
      
      traverse_relationships:
        # Standard telemetry-based relationships
        - source_type: "KUBERNETES_DEPLOYMENT"
          target_type: "KUBERNETES_POD"
          relationship: "MANAGES"
          importance: "high"
          discovery_method: "telemetry"
          metrics_to_collect:
            ["pod_status", "container_status", "restart_count", "cpu_usage", "memory_usage"]
          continue_traversal_if:
            - "restart_count > 5"
            - "cpu_usage > 0.8"
            - "memory_usage > 0.8"
            - "pod_status != 'Running'"
            
        - source_type: "KUBERNETES_POD"
          target_type: "KUBERNETES_NODE"
          relationship: "RUNS_ON"
          importance: "medium"
          discovery_method: "telemetry"
          metrics_to_collect:
            ["cpu_pressure", "memory_pressure", "disk_pressure", "pod_count"]
          continue_traversal_if:
            - "cpu_pressure == true"
            - "memory_pressure == true"
            - "disk_pressure == true"
            
        - source_type: "KUBERNETES_POD"
          target_type: "CONTAINER"
          relationship: "CONTAINS"
          importance: "high"
          discovery_method: "telemetry"
          metrics_to_collect:
            ["container_status", "restart_count", "cpu_usage", "memory_usage"]
          continue_traversal_if:
            - "restart_count > 3"
            - "container_status != 'running'"
            
        # Architectural relationships for business logic dependencies
        - source_type: "APPLICATION"
          target_type: "APPLICATION"
          relationship: "DEPENDS_ON"
          importance: "critical"
          discovery_method: "architectural_config"
          validation_required: true
          metrics_to_collect: ["error_rate", "response_time", "availability"]
          continue_traversal_if:
            - "error_rate > 0.05"
            - "response_time > 500"
            - "availability < 0.95"
            
        - source_type: "APPLICATION"
          target_type: "DATABASE"
          relationship: "STORES_DATA"
          importance: "critical"
          discovery_method: "architectural_config"
          validation_required: true
          metrics_to_collect: ["db_connection_error_rate", "avg_query_time", "connection_pool_usage"]
          continue_traversal_if:
            - "db_connection_error_rate > 0.02"
            - "avg_query_time > 1000"
            - "connection_pool_usage > 0.9"
            
        - source_type: "APPLICATION"
          target_type: "CACHE"
          relationship: "CACHES_TO"
          importance: "medium"
          discovery_method: "architectural_config"
          validation_required: true
          metrics_to_collect: ["cache_miss_rate", "connection_error_rate"]
          continue_traversal_if:
            - "cache_miss_rate > 0.2"
            - "connection_error_rate > 0.02"
    runbook: |
      1. Identify affected pods and check their status:
         - Tool: get_deployment_pods(deployment_name, namespace)
         - Tool: get_pod_status(pod_name, namespace)
      2. Check pod logs for error messages:
         - Tool: get_pod_logs(pod_name, namespace)
      3. Examine Kubernetes events for scheduling issues:
         - Tool: get_kubernetes_events(namespace, "deployment/{deployment_name}")
         - Tool: get_pod_events(pod_name, namespace)
      4. Verify node status and resource availability:
         - Tool: get_node_status(node_name)
         - Tool: get_node_metrics(node_name, ["cpu", "memory"])
      5. Check deployment configuration and resource requests:
         - Tool: get_deployment_spec(deployment_name, namespace)
      6. Review recent changes to the deployment:
         - Tool: get_deployment_history(deployment_name, namespace)
      7. Consider scaling resources or adjusting configuration:
         - Tool: suggest_resource_adjustments(deployment_name, namespace)
