'2405324':
- description: 'Elasticsearch node: {{entity.name}}

    The elasticsearch process is not running.'
  enabled: true
  id: '44333180'
  name: Neurons UEM - Elasticsearch Process is not running
  nrql:
    query: 'SELECT count(entityName) FROM ProcessSample WHERE entityName like ''%PRDES%''
      where processDisplayName = ''elasticsearch'' FACET entityName '
  policyId: '2405324'
  runbookUrl: null
  signal:
    aggregationDelay: 120
    aggregationMethod: EVENT_FLOW
    aggregationTimer: null
    aggregationWindow: 60
  terms:
  - operator: BELOW
    priority: CRITICAL
    threshold: 1.0
    thresholdDuration: 300
    thresholdOccurrences: ALL
  type: STATIC
  violationTimeLimitSeconds: 259200
- description: 'Request duration > 20000 ms for cluster: {{ tags.cluster_name }}'
  enabled: true
  id: '32549584'
  name: Config Proxy Service Request takes > 20000 ms
  nrql:
    query: FROM Log SELECT count(*) WHERE cluster_name LIKE '%aks-rg-%-prd-neurons%'
      and upstream_cluster like 'outbound|80||%.dev-ops.svc.cluster.local' and numeric(duration)
      > 20000 Facet cluster_name
  policyId: '2405324'
  runbookUrl: null
  signal:
    aggregationDelay: 120
    aggregationMethod: EVENT_FLOW
    aggregationTimer: null
    aggregationWindow: 60
  terms:
  - operator: ABOVE
    priority: CRITICAL
    threshold: 1.0
    thresholdDuration: 300
    thresholdOccurrences: AT_LEAST_ONCE
  type: STATIC
  violationTimeLimitSeconds: 259200
- description: null
  enabled: true
  id: '31517371'
  name: Edge intelligence ingress-nginx-controller restartcount above 5 in 10 minutes
  nrql:
    query: from K8sContainerSample SELECT average(restartCount) WHERE clusterName
      LIKE '%aks-edge-rg-%-prd%' AND deploymentName = 'ingress-nginx-controller' AND
      isReady != 1 AND status = 'Running'
  policyId: '2405324'
  runbookUrl: null
  signal:
    aggregationDelay: 120
    aggregationMethod: EVENT_FLOW
    aggregationTimer: null
    aggregationWindow: 300
  terms:
  - operator: ABOVE
    priority: CRITICAL
    threshold: 5.0
    thresholdDuration: 600
    thresholdOccurrences: ALL
  type: STATIC
  violationTimeLimitSeconds: 259200
- description: null
  enabled: true
  id: '31365356'
  name: Evicted Pods
  nrql:
    query: 'SELECT count(*) FROM K8sPodSample

      WHERE reason = ''Evicted'' AND status = ''Failed'' AND podName NOT LIKE ''%assetprocessor%''
      AND clusterName like ''%prd%'' AND clusterName NOT LIKE ''%fru-prd%'' FACET
      podName, clusterName'
  policyId: '2405324'
  runbookUrl: null
  signal:
    aggregationDelay: 120
    aggregationMethod: EVENT_FLOW
    aggregationTimer: null
    aggregationWindow: 600
  terms:
  - operator: ABOVE
    priority: CRITICAL
    threshold: 2.0
    thresholdDuration: 1800
    thresholdOccurrences: ALL
  type: STATIC
  violationTimeLimitSeconds: 10800
- description: null
  enabled: true
  id: '31223298'
  name: Long running Cronjobs
  nrql:
    query: FROM K8sPodSample SELECT  latest((timestamp/1000 - startTime)/60/60) as
      'hours' where createdKind = 'Job' AND clusterName like '%prd%' and status =
      'Running' and (podName LIKE 'patchcontent-macexporter%' or podName LIKE 'patchcontent-windowsexporter%')
      facet podName, clusterName
  policyId: '2405324'
  runbookUrl: null
  signal:
    aggregationDelay: 120
    aggregationMethod: EVENT_FLOW
    aggregationTimer: null
    aggregationWindow: 1800
  terms:
  - operator: ABOVE
    priority: CRITICAL
    threshold: 6.0
    thresholdDuration: 1800
    thresholdOccurrences: AT_LEAST_ONCE
  type: STATIC
  violationTimeLimitSeconds: 10800
- description: 'Container restarts greater than 10 in 15 minutes for the container:
    {{tags.containerName}}, cluster: {{tags.clusterName}}'
  enabled: true
  id: '31089733'
  name: Container restarts > 10 in 15 minutes
  nrql:
    query: from K8sContainerSample select sum(restartCountDelta) WHERE clusterName
      LIKE '%aks-rg-%-prd%' FACET clusterName, containerName
  policyId: '2405324'
  runbookUrl: null
  signal:
    aggregationDelay: 120
    aggregationMethod: EVENT_FLOW
    aggregationTimer: null
    aggregationWindow: 60
  terms:
  - operator: ABOVE
    priority: CRITICAL
    threshold: 10.0
    thresholdDuration: 900
    thresholdOccurrences: ALL
  type: STATIC
  violationTimeLimitSeconds: 86400
- description: null
  enabled: true
  id: '31022537'
  name: Remote Control Service Pods not available
  nrql:
    query: select latest(podsDesired)-latest(podsAvailable) from K8sDeploymentSample  WHERE
      clusterName LIKE '%prd-neurons%' AND clusterName NOT LIKE '%fru-prd%' AND deploymentName
      in  ('remotecontrol-rcagentlessapi-deployment', 'remotecontrol-rcfunctions-deployment',
      'rc-viewer-webclient', 'configservice', 'capabilitybuilder') FACET clusterName,
      deploymentName
  policyId: '2405324'
  runbookUrl: null
  signal:
    aggregationDelay: 120
    aggregationMethod: EVENT_FLOW
    aggregationTimer: null
    aggregationWindow: 60
  terms:
  - operator: ABOVE
    priority: CRITICAL
    threshold: 1.0
    thresholdDuration: 900
    thresholdOccurrences: ALL
  type: STATIC
  violationTimeLimitSeconds: 259200
- description: '''FailedScheduling'' event for pods because {{ tags.event.message
    }} in cluster {{ tags.clusterName }} for {{ tags.event.involvedObject.name }}'
  enabled: true
  id: '25541421'
  name: '''FailedScheduling'' event for pods'
  nrql:
    query: 'FROM InfrastructureEvent SELECT count(*)

      Facet clusterName,event.involvedObject.name,event.reason,event.message

      WHERE clusterName Like ''%prd-neurons%'' AND clusterName NOT LIKE ''%fru-prd%''
      where event.reason Like ''%FailedScheduling%'''
  policyId: '2405324'
  runbookUrl: null
  signal:
    aggregationDelay: null
    aggregationMethod: EVENT_TIMER
    aggregationTimer: 120
    aggregationWindow: 1800
  terms:
  - operator: ABOVE
    priority: CRITICAL
    threshold: 3.0
    thresholdDuration: 3600
    thresholdOccurrences: ALL
  type: STATIC
  violationTimeLimitSeconds: 259200
- description: Pod  is in CrashLoopBackOff.
  enabled: true
  id: '25541032'
  name: 'Pod with CrashLoopBackOff -- '
  nrql:
    query: select count(*) from K8sContainerSample where status='Waiting' and reason='CrashLoopBackOff'
      and restartCount > 20 WHERE clusterName LIKE '%prd-neurons%'  and clusterName
      not like '%fru-prd%' and clusterName not like '%fru-prd%' and clusterName not
      like '%ttu-prd%' and clusterName not like '%tku-prd%' and containerName not
      in ('keda-operator', 'help') FACET clusterName, deploymentName
  policyId: '2405324'
  runbookUrl: null
  signal:
    aggregationDelay: 120
    aggregationMethod: EVENT_FLOW
    aggregationTimer: null
    aggregationWindow: 900
  terms:
  - operator: ABOVE
    priority: CRITICAL
    threshold: 10.0
    thresholdDuration: 2700
    thresholdOccurrences: AT_LEAST_ONCE
  type: STATIC
  violationTimeLimitSeconds: 21600
'5916989':
- description: '''FailedScheduling'' event for pods because {{ tags.event.message
    }} in cluster {{ tags.clusterName }} for {{ tags.event.involvedObject.name }}'
  enabled: true
  id: '48131767'
  name: '''FailedScheduling'' event for pods - Edge Mongodb pods'
  nrql:
    query: 'FROM InfrastructureEvent SELECT filter(count(*), where event.reason Like
      ''%FailedScheduling%'')

      Facet clusterName,event.involvedObject.name,event.reason,event.message

      WHERE clusterName Like ''%edge%prd-neurons%'' AND clusterName NOT LIKE ''%fru-prd%''
      AND event.involvedObject.name LIKE ''%mongo-deployment%'''
  policyId: '5916989'
  runbookUrl: null
  signal:
    aggregationDelay: null
    aggregationMethod: EVENT_TIMER
    aggregationTimer: 120
    aggregationWindow: 900
  terms:
  - operator: ABOVE_OR_EQUALS
    priority: CRITICAL
    threshold: 1.0
    thresholdDuration: 900
    thresholdOccurrences: ALL
  type: STATIC
  violationTimeLimitSeconds: 259200
- description: null
  enabled: true
  id: '47601339'
  name: AKS Nodepool Scale Out Percantage above 99%
  nrql:
    query: 'SELECT average(ScaleOutPercentage) FROM CM_AKSNodePoolCount where ClusterName
      not like ''%TTU%'' and ClusterName not like ''%TKU%'' and ClusterName not like
      ''%MLU%'' and ClusterName not like ''%FRU%'' FACET Name '
  policyId: '5916989'
  runbookUrl: null
  signal:
    aggregationDelay: 120
    aggregationMethod: EVENT_FLOW
    aggregationTimer: null
    aggregationWindow: 600
  terms:
  - operator: ABOVE
    priority: CRITICAL
    threshold: 99.0
    thresholdDuration: 1800
    thresholdOccurrences: ALL
  type: STATIC
  violationTimeLimitSeconds: 259200
