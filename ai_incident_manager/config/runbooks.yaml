# Runbooks Configuration
#
# This file defines runbooks that can be executed to investigate and remediate incidents.
# Each runbook includes:
# - id: Unique identifier for the runbook
# - name: Human-readable name of the runbook
# - description: Description of what this runbook does
# - applicable_categories: Alert categories this runbook is applicable to
# - applicable_entities: Entity types this runbook is applicable to
# - steps: Ordered list of steps to execute
#   - Each step has a title, description, tool, and parameters
#     - Parameters can include dynamic values:
#       - {alert_time}: The time when the alert was triggered
#       - {aggregation_window}: The aggregation window from the alert condition
#       - {threshold_duration}: The threshold duration from the alert condition
#       - {violation_time_limit}: The violation time limit from the alert condition
# - priority: Priority of this runbook (1-10, with 1 being highest)
# - prerequisites: List of other runbooks that should be executed before this one
# - tags: List of tags for categorizing runbooks

runbooks:
  # Kubernetes Pod Investigation Runbook
  - id: kubernetes_pod_investigation
    name: "Kubernetes Pod Investigation"
    description: "Investigate a Kubernetes pod for common issues including crashes, resource constraints, and configuration problems"
    applicable_categories:
      - kubernetes_crashloopbackoff
      - kubernetes_evicted_pod
    applicable_entities:
      - KUBERNETES_POD
      - K8S_POD
    priority: 1
    steps:
      - title: "Get Pod Logs"
        description: "Retrieve recent logs from the pod to identify errors or issues"
        tool: "get_pod_logs"
        parameters:
          # Use either the aggregation window or at least 30 minutes, whichever is larger
          time_window_minutes: "{max(aggregation_window/60, 30)}"
          limit: 100
      - title: "Check Pod Status"
        description: "Get the current status of the pod and any events"
        tool: "get_pod_status"
        parameters:
          include_events: true
          # Use the alert time for the query
          since_time: "{alert_time - threshold_duration}"
          until_time: "{alert_time}"
      - title: "Analyze Resource Usage"
        description: "Check CPU and memory usage of the pod"
        tool: "get_pod_metrics"
        parameters:
          metrics: ["cpu_usage", "memory_usage", "restart_count"]
          # Use either the threshold duration or 60 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 60)}"
      - title: "Analyze Network Usage"
        description: "Check network I/O and errors for the pod"
        tool: "get_pod_network_metrics"
        parameters:
          # Use the threshold duration or 30 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 30)}"
      - title: "Check Volume Usage"
        description: "Analyze persistent volume usage for the pod"
        tool: "get_pod_volume_metrics"
        parameters:
          # Use the threshold duration or 30 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 30)}"
      - title: "Check Resource Limits"
        description: "Verify the pod's resource requests and limits"
        tool: "get_pod_spec"
        parameters:
          include_resource_spec: true
    prerequisites: []
    tags: ["kubernetes", "pod", "investigation"]

  # Node Investigation Runbook
  - id: kubernetes_node_investigation
    name: "Kubernetes Node Investigation"
    description: "Investigate a Kubernetes node for resource pressure and capacity issues"
    applicable_categories:
      - kubernetes_evicted_pod
      - kubernetes_scheduling_failure
    applicable_entities:
      - KUBERNETES_NODE
      - K8S_NODE
      - HOST
    priority: 2
    steps:
      - title: "Check Node Conditions"
        description: "Check node conditions including memory pressure, disk pressure, and PID pressure"
        tool: "get_node_conditions"
        parameters:
          since_time: "{alert_time - threshold_duration}"
          until_time: "{alert_time}"
      - title: "Analyze Node Resources"
        description: "Check CPU, memory, and disk usage of the node"
        tool: "get_node_metrics"
        parameters:
          metrics: ["cpu_usage", "memory_usage", "pod_count", "condition"]
          # Use either the threshold duration or 60 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 60)}"
      - title: "Analyze Disk Usage"
        description: "Check detailed disk usage of the node"
        tool: "get_node_disk_metrics"
        parameters:
          # Use either the threshold duration or 30 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 30)}"
      - title: "Analyze Network I/O"
        description: "Check network traffic and errors on the node"
        tool: "get_node_network_metrics"
        parameters:
          # Use either the threshold duration or 30 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 30)}"
      - title: "Check Allocatable Resources"
        description: "Analyze allocatable vs used resources on the node"
        tool: "get_node_allocatable_resources"
        parameters:
          # Use the threshold duration or 30 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 30)}"
      - title: "List Pods on Node"
        description: "List all pods running on the node"
        tool: "get_node_pods"
        parameters:
          include_resource_usage: true
          sort_by: "memory_usage"
          limit: 20
          since_time: "{alert_time - 1800}"  # 30 minutes before alert
          until_time: "{alert_time}"
      - title: "Check Node Events"
        description: "Get recent events related to the node"
        tool: "get_node_events"
        parameters:
          # Use 2x the threshold duration or 120 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration*2/60, 120)}"
    prerequisites: []
    tags: ["kubernetes", "node", "investigation"]

  # Database Performance Runbook
  - id: database_performance_investigation
    name: "Database Performance Investigation"
    description: "Investigate database performance issues including high CPU, memory usage, and slow queries"
    applicable_categories:
      - query_result_threshold
      - rds_swap_usage_high
    applicable_entities:
      - DATABASE
      - RDS
    priority: 1
    steps:
      - title: "Check Database Metrics"
        description: "Retrieve key performance metrics for the database"
        tool: "get_database_metrics"
        parameters:
          metrics: ["cpu_usage", "memory_usage", "connections", "read_iops", "write_iops"]
          # Use either the threshold duration or 60 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 60)}"
      - title: "Analyze Slow Queries"
        description: "Find and analyze slow queries"
        tool: "get_slow_queries"
        parameters:
          # Use twice the threshold duration time for a wider view
          time_window_minutes: "{max(threshold_duration*2/60, 120)}"
          limit: 20
          min_execution_time_ms: 1000
      - title: "Check Connection Count"
        description: "Analyze connection counts by application"
        tool: "get_database_connections"
        parameters:
          group_by: "application"
          since_time: "{alert_time - threshold_duration}"
          until_time: "{alert_time}"
      - title: "Review Database Configuration"
        description: "Review important database configuration parameters"
        tool: "get_database_config"
        parameters:
          include_parameters: ["shared_buffers", "work_mem", "effective_cache_size", "max_connections"]
    prerequisites: []
    tags: ["database", "performance", "investigation"]

  # Debezium Lag Investigation Runbook
  - id: debezium_lag_investigation
    name: "Debezium Lag Investigation"
    description: "Investigate Debezium CDC lag issues"
    applicable_categories:
      - debezium_lag
    applicable_entities:
      - APPLICATION
      - KUBERNETES_POD
    priority: 2
    steps:
      - title: "Check Connector Status"
        description: "Get the status of the Debezium connector"
        tool: "get_debezium_connector_status"
        parameters:
          connector_name: "auto"
          since_time: "{alert_time - 1800}"  # 30 minutes before alert
          until_time: "{alert_time}"
      - title: "Analyze Lag Metrics"
        description: "Get detailed lag metrics over time"
        tool: "get_debezium_lag_metrics"
        parameters:
          # Use twice the threshold duration time for a wider view
          time_window_minutes: "{max(threshold_duration*2/60, 120)}"
      - title: "Check Source Database Load"
        description: "Check the load on the source database"
        tool: "get_database_metrics"
        parameters:
          database_type: "source"
          metrics: ["cpu_usage", "memory_usage", "connections", "transaction_rate"]
          # Use the threshold duration or 60 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 60)}"
      - title: "Check Kafka Topics"
        description: "Check Kafka topic details including partition count and consumer lag"
        tool: "get_kafka_topic_info"
        parameters:
          include_consumer_groups: true
          since_time: "{alert_time - threshold_duration}"
          until_time: "{alert_time}"
    prerequisites: []
    tags: ["debezium", "kafka", "cdc", "lag", "investigation"]

  # Memory-focused Kubernetes Pod Investigation
  - id: pod_memory_investigation
    name: "Pod Memory Usage Investigation"
    description: "Deep dive into pod memory usage and container memory limits"
    applicable_categories:
      - kubernetes_crashloopbackoff
      - kubernetes_evicted_pod
    applicable_entities:
      - KUBERNETES_POD
      - K8S_POD
    priority: 2
    steps:
      - title: "Get Memory Usage Trend"
        description: "Get detailed memory usage trends"
        tool: "get_pod_memory_metrics"
        parameters:
          include_containers: true
          # Use twice the threshold duration time for a wider view
          time_window_minutes: "{max(threshold_duration*2/60, 120)}"
          resolution_minutes: 5
      - title: "Check Memory Limits"
        description: "Compare memory usage against limits"
        tool: "analyze_pod_memory_limits"
        parameters:
          include_recommendation: true
          since_time: "{alert_time - threshold_duration}"
          until_time: "{alert_time}"
      - title: "Check for Memory Leaks"
        description: "Analyze memory usage patterns to identify potential leaks"
        tool: "detect_memory_leaks"
        parameters:
          # Use a longer time window for leak detection
          time_window_hours: "{max(threshold_duration/3600, 24)}"
      - title: "Check OOMKilled Events"
        description: "Look for OOMKilled events in container history"
        tool: "get_oomkilled_events"
        parameters:
          # Use a longer time window for historical context
          time_window_days: 7
    prerequisites: ["kubernetes_pod_investigation"]
    tags: ["kubernetes", "pod", "memory", "investigation"]
    
  # Kubernetes Cluster Health Investigation
  - id: kubernetes_cluster_investigation
    name: "Kubernetes Cluster Health Investigation"
    description: "Investigate overall Kubernetes cluster health, resource usage, and pod status"
    applicable_categories:
      - kubernetes_cluster_health
      - kubernetes_scheduling_failure
      - kubernetes_resource_pressure
    applicable_entities:
      - KUBERNETES_CLUSTER
      - K8S_CLUSTER
    priority: 1
    steps:
      - title: "Check Cluster Resource Usage"
        description: "Analyze overall CPU and memory usage across the cluster"
        tool: "get_cluster_metrics"
        parameters:
          metrics: ["resource_usage"]
          # Use either the threshold duration or 60 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 60)}"
      - title: "Check Pod Status Distribution"
        description: "Get counts of pods by status (Running, Pending, Failed, etc.)"
        tool: "get_cluster_metrics"
        parameters:
          metrics: ["pod_counts"]
          # Use the threshold duration or 30 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 30)}"
      - title: "Check Node Status"
        description: "Get status and capacity information for all nodes"
        tool: "get_cluster_metrics"
        parameters:
          metrics: ["nodes"]
          # Use the threshold duration or 30 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 30)}"
      - title: "Check Deployment Status"
        description: "Get status of all deployments in the cluster"
        tool: "get_cluster_metrics"
        parameters:
          metrics: ["deployments"]
          # Use the threshold duration or 30 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 30)}"
      - title: "Check Cluster Events"
        description: "Get recent Kubernetes events for the entire cluster"
        tool: "get_cluster_metrics"
        parameters:
          metrics: ["events"]
          # Use 2x the threshold duration or 120 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration*2/60, 120)}"
          limit: 100
    prerequisites: []
    tags: ["kubernetes", "cluster", "investigation"]

  # Kubernetes Deployment Unavailable Pods Investigation
  - id: kubernetes_deployment_unavailable_pods_investigation
    name: "Kubernetes Deployment Unavailable Pods Investigation"
    description: "Investigate Kubernetes deployments with a high percentage of unavailable pods to determine root causes and remediation steps"
    applicable_categories:
      - kubernetes_deployment_unavailable_pods
    applicable_entities:
      - KUBERNETES_DEPLOYMENT
      - K8S_DEPLOYMENT
    priority: 1
    steps:
      - title: "Get Deployment Status"
        description: "Get the current status of the deployment including replica counts and conditions"
        tool: "get_deployment_status"
        parameters:
          namespace: "auto"
          include_conditions: true
          since_time: "{alert_time - 1800}"  # 30 minutes before alert
          until_time: "{alert_time}"
      - title: "List Pod Status"
        description: "List all pods associated with the deployment and their status"
        tool: "get_deployment_pods"
        parameters:
          namespace: "auto"
          include_status: true
          include_conditions: true
          since_time: "{alert_time - 1800}"  # 30 minutes before alert
          until_time: "{alert_time}"
      - title: "Check Pod Logs"
        description: "Get logs from the affected pods to identify any application errors"
        tool: "get_pod_logs"
        parameters:
          pod_selector: "deployment-name"
          limit_pods: 3
          limit_lines: 100
          include_previous: true
          # Use the aggregation window or 30 minutes, whichever is larger
          time_window_minutes: "{max(aggregation_window/60, 30)}"
      - title: "Check Kubernetes Events"
        description: "Get recent events related to the deployment and its pods"
        tool: "get_kubernetes_events"
        parameters:
          resource_type: "Deployment"
          # Use the threshold duration or 60 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 60)}"
      - title: "Check Node Status"
        description: "Check status of nodes running the deployment's pods"
        tool: "check_nodes_status"
        parameters:
          nodes_selector: "pods-from-deployment"
          include_metrics: ["cpu", "memory", "pods"]
          since_time: "{alert_time - 1800}"  # 30 minutes before alert
          until_time: "{alert_time}"
      - title: "Analyze Resource Usage"
        description: "Analyze CPU and memory usage of the deployment"
        tool: "analyze_deployment_resources"
        parameters:
          include_pods: true
          # Use the threshold duration or 60 minutes, whichever is larger
          time_window_minutes: "{max(threshold_duration/60, 60)}"
          include_recommendations: true
      - title: "Check Recent Changes"
        description: "Check for recent changes to the deployment configuration"
        tool: "get_deployment_history"
        parameters:
          revisions_to_show: 5
          include_diff: true
          # Go back a week to catch deployment changes
          since_time: "{alert_time - 604800}"  # 7 days before alert
          until_time: "{alert_time}"
    prerequisites: []
    tags: ["kubernetes", "deployment", "pods", "investigation"] 