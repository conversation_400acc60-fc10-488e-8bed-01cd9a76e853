"""
Pydantic AI agents for incident analysis.

This module contains Pydantic AI agents that perform various tasks in the incident
analysis workflow, such as alert analysis, metrics analysis, and root cause determination.
"""

import os
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, List, Any, Optional, TypedDict

from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel
from openai import AsyncOpenAI, AsyncAzureOpenAI
from langsmith import traceable
from langsmith.wrappers import wrap_openai
import dotenv

from ai_incident_manager.services.metrics_collector import MetricsCollector
from ai_incident_manager.models.workflow_state import (
    IncidentState, EntityDetails, MetricData, SystemCheckResult, 
    RemediationAction, InvestigationNote
)

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize OpenAI client
azure_openai_enabled = "AZURE_OPENAI_ENDPOINT" in os.environ and "AZURE_OPENAI_API_KEY" in os.environ

if azure_openai_enabled:
    openai_client = wrap_openai(AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    ))
    model = OpenAIModel("gpt-4", openai_client=openai_client)
else:
    openai_client = AsyncOpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
    model = OpenAIModel("gpt-4", openai_client=openai_client)


@dataclass
class AlertAnalyzerDeps:
    """Dependencies for the alert analyzer agent."""
    metrics_collector: MetricsCollector
    incident_state: IncidentState


@dataclass
class MetricsAnalyzerDeps:
    """Dependencies for the metrics analyzer agent."""
    metrics_collector: MetricsCollector
    incident_state: IncidentState


@dataclass
class LogsAnalyzerDeps:
    """Dependencies for the logs analyzer agent."""
    metrics_collector: MetricsCollector
    incident_state: IncidentState


@dataclass
class SystemChecksDeps:
    """Dependencies for the system checks agent."""
    metrics_collector: MetricsCollector
    incident_state: IncidentState


@dataclass
class RootCauseAnalyzerDeps:
    """Dependencies for the root cause analyzer agent."""
    metrics_collector: MetricsCollector
    incident_state: IncidentState


@dataclass
class RemediationAdvisorDeps:
    """Dependencies for the remediation advisor agent."""
    metrics_collector: MetricsCollector
    incident_state: IncidentState


# Alert Analyzer Agent
alert_analyzer = Agent(
    model,
    system_prompt="""You are an AI incident analyst specializing in cloud infrastructure and application monitoring.
Your task is to analyze New Relic alerts to understand the incident context and determine:
1. The likely scope and impact of the issue
2. What entities are involved
3. What metrics should be investigated
4. Initial hypotheses about the cause

Provide a clear explanation of the alert and identify the next steps to investigate.
Be detailed but concise, focusing on actionable insights.
""",
    deps_type=AlertAnalyzerDeps,
    retries=2
)


@traceable(run_type="tool")
@alert_analyzer.tool
def get_entity_details(ctx: RunContext[AlertAnalyzerDeps], entity_guid: str) -> Dict[str, Any]:
    """
    Get detailed information about an entity from New Relic.
    
    Args:
        ctx: The run context with dependencies
        entity_guid: The New Relic entity GUID
        
    Returns:
        Entity details dictionary
    """
    entity_details = ctx.deps.metrics_collector.get_entity_details(entity_guid)
    return entity_details or {"error": "Entity not found"}


# Metrics Analyzer Agent
metrics_analyzer = Agent(
    model,
    system_prompt="""You are an AI incident analyst specializing in metrics analysis.
Your task is to analyze collected metrics to identify patterns, anomalies, and potential causes of the incident.

Look for:
1. Metrics that exceed their thresholds
2. Unusual patterns or trends
3. Correlations between different metrics
4. Temporal patterns (when did the anomaly start)

Based on the metrics, provide:
1. A clear explanation of what metrics show
2. Potential causes of the observed anomalies
3. Recommendations for further investigation

Be detailed, technical, and provide specific evidence from the metrics to support your analysis.
""",
    deps_type=MetricsAnalyzerDeps,
    retries=2
)


@traceable(run_type="tool")
@metrics_analyzer.tool
def collect_entity_metrics(
    ctx: RunContext[MetricsAnalyzerDeps],
    entity_guid: str,
    entity_type: str,
    metrics_list: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Collect metrics for a specific entity from New Relic.
    
    Args:
        ctx: The run context with dependencies
        entity_guid: The New Relic entity GUID
        entity_type: Entity type (KUBERNETES_POD, etc.)
        metrics_list: List of specific metrics to collect (optional)
        
    Returns:
        Dictionary of metrics data
    """
    # Calculate time window from the incident state
    since_time = None
    if "start_time" in ctx.deps.incident_state:
        try:
            # Parse the start time and go back 15 minutes
            start_time = datetime.fromisoformat(ctx.deps.incident_state["start_time"].replace('Z', '+00:00'))
            since_time = start_time - timedelta(minutes=15)
        except (ValueError, TypeError):
            since_time = None
    
    # Collect metrics
    return ctx.deps.metrics_collector.collect_entity_metrics(
        entity_guid=entity_guid,
        entity_type=entity_type,
        since_time=since_time,
        metrics_list=metrics_list
    )


# Logs Analyzer Agent
logs_analyzer = Agent(
    model,
    system_prompt="""You are an AI incident analyst specializing in log analysis.
Your task is to analyze logs to identify errors, warnings, and issues that might be related to the incident.

Look for:
1. Error messages and exceptions
2. Warning patterns
3. Timing of errors relative to the incident
4. Evidence of system failures

Based on the logs, provide:
1. A clear explanation of what the logs indicate
2. Potential root causes
3. Timeline of events leading to the incident

Be specific, technical, and provide direct evidence from the logs to support your analysis.
""",
    deps_type=LogsAnalyzerDeps,
    retries=2
)


@traceable(run_type="tool")
@logs_analyzer.tool
def collect_entity_logs(
    ctx: RunContext[LogsAnalyzerDeps],
    entity_guid: str,
    entity_name: str,
    entity_type: str,
    limit: int = 100
) -> List[Dict[str, Any]]:
    """
    Collect logs for a specific entity from New Relic.
    
    Args:
        ctx: The run context with dependencies
        entity_guid: The New Relic entity GUID
        entity_name: Entity name for query filtering
        entity_type: Entity type for query customization
        limit: Maximum number of log entries to retrieve
        
    Returns:
        List of log entries
    """
    # Calculate time window from the incident state
    since_time = None
    if "start_time" in ctx.deps.incident_state:
        try:
            # Parse the start time and go back 15 minutes
            start_time = datetime.fromisoformat(ctx.deps.incident_state["start_time"].replace('Z', '+00:00'))
            since_time = start_time - timedelta(minutes=15)
        except (ValueError, TypeError):
            since_time = None
    
    # Collect logs
    return ctx.deps.metrics_collector.collect_entity_logs(
        entity_guid=entity_guid,
        entity_name=entity_name,
        entity_type=entity_type,
        since_time=since_time,
        limit=limit
    )


@traceable(run_type="tool")
@logs_analyzer.tool
def collect_kubernetes_events(
    ctx: RunContext[LogsAnalyzerDeps],
    entity_name: str,
    entity_type: str,
    cluster_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Collect Kubernetes events for a specific entity from New Relic.
    
    Args:
        ctx: The run context with dependencies
        entity_name: Name of the entity (pod name, node name, etc.)
        entity_type: Type of entity (KUBERNETES_POD, KUBERNETES_NODE, etc.)
        cluster_id: Kubernetes cluster ID (optional)
        
    Returns:
        List of Kubernetes events
    """
    # Calculate time window from the incident state
    since_time = None
    if "start_time" in ctx.deps.incident_state:
        try:
            # Parse the start time and go back 15 minutes
            start_time = datetime.fromisoformat(ctx.deps.incident_state["start_time"].replace('Z', '+00:00'))
            since_time = start_time - timedelta(minutes=15)
        except (ValueError, TypeError):
            since_time = None
    
    # Collect Kubernetes events
    return ctx.deps.metrics_collector.collect_kubernetes_events(
        entity_name=entity_name,
        entity_type=entity_type,
        cluster_id=cluster_id,
        since_time=since_time
    )


# System Checks Agent
system_checks_agent = Agent(
    model,
    system_prompt="""You are an AI incident analyst specializing in system health diagnosis.
Your task is to determine appropriate system checks based on the incident details, metrics, and logs.

Consider checks related to:
1. Resource usage (CPU, memory, disk, network)
2. Service health
3. Dependency status
4. Configuration issues
5. Recent changes or deployments

For each check:
1. Provide a descriptive name
2. Explain why it's relevant to this incident
3. Specify what should be checked
4. Define criteria for success/failure

Be comprehensive, systematic, and prioritize checks that are most likely to identify the root cause.
""",
    deps_type=SystemChecksDeps,
    retries=2
)


@traceable(run_type="tool")
@system_checks_agent.tool
def analyze_entity(
    ctx: RunContext[SystemChecksDeps],
    entity_guid: str,
    product: Optional[str] = None,
    region: Optional[str] = None
) -> Dict[str, Any]:
    """
    Perform a comprehensive analysis of an entity using New Relic analyzer.
    
    Args:
        ctx: The run context with dependencies
        entity_guid: New Relic entity GUID
        product: Optional product name for context
        region: Optional region name for context
        
    Returns:
        Comprehensive analysis of the entity
    """
    # Calculate time window from the incident state
    since_time = None
    if "start_time" in ctx.deps.incident_state:
        try:
            # Parse the start time and go back 15 minutes
            start_time = datetime.fromisoformat(ctx.deps.incident_state["start_time"].replace('Z', '+00:00'))
            since_time = start_time - timedelta(minutes=15)
        except (ValueError, TypeError):
            since_time = None
    
    # Perform entity analysis
    return ctx.deps.metrics_collector.analyze_entity(
        entity_guid=entity_guid,
        since_time=since_time,
        product=product,
        region=region
    )


# Root Cause Analyzer Agent
root_cause_analyzer = Agent(
    model,
    system_prompt="""You are an AI incident analyst specializing in root cause analysis.
Your task is to determine the most likely root cause of the incident based on all the collected information.

Consider:
1. Metrics and their patterns
2. Log messages and errors
3. System check results
4. Timeline of events

Provide a clear, concise explanation of:
1. The primary root cause
2. Any contributing factors
3. The impact of the incident
4. How the issue propagated through the system

Your analysis should be thorough, specific, and actionable.
""",
    deps_type=RootCauseAnalyzerDeps,
    retries=2
)


# Remediation Advisor Agent
remediation_advisor = Agent(
    model,
    system_prompt="""You are an AI incident analyst specializing in remediation planning.
Your task is to recommend specific remediation actions based on the incident analysis and root cause.

Your recommendations should:
1. Address the immediate issue to resolve the incident
2. Suggest preventive measures for the future
3. Include specific, actionable steps
4. Consider both short-term fixes and long-term improvements

Format your response as a prioritized list of actions, with clear steps for each action.
Be specific, practical, and consider the operational context of cloud infrastructure and applications.
""",
    deps_type=RemediationAdvisorDeps,
    retries=2
) 