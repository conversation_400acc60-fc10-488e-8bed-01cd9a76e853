"""
Runbook Agent - Pydantic AI Agent for executing runbooks.

This agent is responsible for executing runbooks to investigate and remediate incidents.
The agent is responsible for:
1. Selecting appropriate runbooks based on alert category and entity types
2. Executing runbook steps using specialized tools
3. Collecting and analyzing results from each step
4. Providing recommendations and insights based on the runbook execution
"""

import logging
import os
import re
import json
from loguru import logger
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass, field
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel

import dotenv
from openai import AsyncOpenAI, AsyncAzureOpenAI

from ai_incident_manager.services.runbook_service import get_runbook_service
from ai_incident_manager.services.metrics_collector import MetricsCollector
from lib.new_relic.analyzer import EntityAnalyzer
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.logs import NewRelicLogsClient
from lib.new_relic.base import Region
from ai_incident_manager.models.workflow_state import (
    RunbookStepResult, CollectedInformation, EntityRelationship, RunbookExecutionAgentResponse
)

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logger = logger.bind(name="runbook_agent")

# Initialize OpenAI client
required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
azure_openai_enabled = all(var in os.environ for var in required_azure_vars)

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    model = OpenAIModel(os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"), openai_client=openai_client)
else:
    logger.error("Azure OpenAI credentials not found. This application requires Azure OpenAI.")
    logger.error(f"Missing environment variables: {[var for var in required_azure_vars if var not in os.environ]}")
    raise EnvironmentError("Azure OpenAI credentials are required but not found in environment variables.")


def get_runbook_service():
    """
    Get the singleton instance of RunbookService.
    
    Returns:
        RunbookService: The singleton instance
    """
    from ai_incident_manager.services.runbook_service import RunbookService
    return RunbookService()


@dataclass
class RunbookAgentDeps:
    """Dependencies for the runbook agent."""
    openai_client: AsyncAzureOpenAI
    metrics_collector: MetricsCollector
    logs_client: NewRelicLogsClient
    query_client: NewRelicQueryClient
    state: Any = None  # Can be Dict[str, Any] or Pydantic model (IncidentState)
    runbook: str = None
    alert_category: Dict[str, Any] = None
    primary_entity: Dict[str, Any] = None  # Primary entity affected by the alert
    related_entities: List[Dict[str, Any]] = field(default_factory=list)  # Related entities
    
    def safe_get_state(self, key: str, default: Any = None) -> Any:
        """
        Safely get a value from state whether it's a dictionary or a Pydantic model.
        
        Args:
            key: The key or attribute name to access
            default: Default value to return if key not found
            
        Returns:
            The value from state or the default
        """
        if not self.state:
            return default
            
        # If state is a dictionary, use get method
        if isinstance(self.state, dict):
            return self.state.get(key, default)
            
        # If state is a Pydantic model or other object, use getattr
        if hasattr(self.state, key):
            return getattr(self.state, key)
            
        return default
    
    def get_time_window(self) -> Dict[str, Any]:
        """
        Get time window information from the state.
        
        Returns:
            Dict containing time window information with both datetime 
            objects and millisecond timestamps
        """
        if not self.state:
            return {}
            
        time_window = {}
        
        # Use safe_get_state to handle both dict and Pydantic model
        # Check for since_time and until_time directly in state
        since_time = self.safe_get_state("since_time")
        until_time = self.safe_get_state("until_time")
        
        if since_time is not None and until_time is not None:
            # Ensure datetime objects for since_time and until_time
            if isinstance(since_time, str):
                try:
                    time_window["since_time"] = datetime.fromisoformat(since_time.replace('Z', '+00:00'))
                except ValueError:
                    # Handle other string formats if needed
                    logger.warning(f"Could not parse since_time: {since_time}")
                    time_window["since_time"] = since_time
            else:
                time_window["since_time"] = since_time
            
            if isinstance(until_time, str):
                try:
                    time_window["until_time"] = datetime.fromisoformat(until_time.replace('Z', '+00:00'))
                except ValueError:
                    # Handle other string formats if needed
                    logger.warning(f"Could not parse until_time: {until_time}")
                    time_window["until_time"] = until_time
            else:
                time_window["until_time"] = until_time
            
            # Add millisecond formats if not present
            since_time_ms = self.safe_get_state("since_time_ms")
            if since_time_ms is None and isinstance(time_window["since_time"], datetime):
                time_window["since_time_ms"] = int(time_window["since_time"].timestamp() * 1000)
            else:
                time_window["since_time_ms"] = since_time_ms
            
            until_time_ms = self.safe_get_state("until_time_ms")
            if until_time_ms is None and isinstance(time_window["until_time"], datetime):
                time_window["until_time_ms"] = int(time_window["until_time"].timestamp() * 1000)
            else:
                time_window["until_time_ms"] = until_time_ms
        
        # Check for epoch millisecond versions
        else:
            since_time_ms = self.safe_get_state("since_time_ms")
            until_time_ms = self.safe_get_state("until_time_ms")
            
            if since_time_ms is not None and until_time_ms is not None:
                time_window["since_time_ms"] = since_time_ms
                time_window["until_time_ms"] = until_time_ms
                
                # Convert to datetime objects
                time_window["since_time"] = datetime.fromtimestamp(
                    since_time_ms / 1000, tz=timezone.utc
                )
                    
                time_window["until_time"] = datetime.fromtimestamp(
                    until_time_ms / 1000, tz=timezone.utc
                )
        
        return time_window

    def get_entity_by_type(self, entity_type: str) -> Optional[Dict[str, Any]]:
        """Get an entity by its type."""
        if self.primary_entity and self.primary_entity.get("type") == entity_type:
            return self.primary_entity
            
        for entity in self.related_entities:
            if entity.get("type") == entity_type:
                return entity
                
        return None
        
    def get_entity_by_name(self, entity_name: str) -> Optional[Dict[str, Any]]:
        """Get an entity by its name."""
        if self.primary_entity and self.primary_entity.get("name") == entity_name:
            return self.primary_entity
            
        for entity in self.related_entities:
            if entity.get("name") == entity_name:
                return entity
                
        return None
        
    def get_entity_by_guid(self, entity_guid: str) -> Optional[Dict[str, Any]]:
        """Get an entity by its GUID."""
        if self.primary_entity and self.primary_entity.get("guid") == entity_guid:
            return self.primary_entity
            
        for entity in self.related_entities:
            if entity.get("guid") == entity_guid:
                return entity
                
        return None


# Runbook Agent with result_type and system message
runbook_agent = Agent(
    model,
    deps_type=RunbookAgentDeps,
    result_type=RunbookExecutionAgentResponse,
    retries=3,
    system_prompt="""
You are an expert runbook executor specialized in incident investigation for cloud and Kubernetes environments.
Your primary task is to execute runbook steps meticulously and record the results accurately.

IMPORTANT: Your role is NOT to perform root cause analysis or complex remediation planning.
Instead, focus on carefully executing each step in the runbook and documenting the results.

When executing a runbook:
1. Follow each step in the runbook exactly as specified
2. Call the appropriate tools for data collection with the correct parameters
3. Document each step execution with proper structure
4. Categorize collected information properly (metric, log, information)
5. Create concise summaries for each step and collected information
6. Note whether issues were found in each step but don't attempt deep analysis

For each runbook step:
- Record the step title and description
- Document the tool used and parameters passed
- Create a summary of what was done and what was found
- Flag whether issues were identified (true/false)
- For all collected information, include entity details (guid, name, type)
- Include NRQL queries when available

Time Windows:
- ALWAYS use the exact time window from the alert for all time-based queries
- Use epoch millisecond timestamps (since_time_ms, until_time_ms) for all NRQL queries
- When writing NRQL queries, use 'SINCE timestamp UNTIL timestamp' format with epoch milliseconds

Response Structure:
Your response must follow the RunbookExecutionAgentResponse model with:
1. runbook_id, runbook_name, and runbook_description
2. A list of step_results, each following the RunbookStepResult model
3. For each step, a list of collected_information items following the CollectedInformation model
4. A simple issues_found flag (true if any issues were detected)
5. A concise summary of the runbook execution

REMEMBER: Actual metrics, logs, and information are stored in the database automatically.
You only need to provide summaries and proper categorization.
"""
)

@runbook_agent.system_prompt  
async def add_runbook_context(ctx: RunContext) -> str:
    """
    Add runbook context to the system prompt dynamically.
    This includes:
    1. The runbook from deps if available
    2. Alert category details if available
    3. Entity relationship information
    4. Time window information with emphasis on epoch millisecond timestamps
    """
    result = []
    
    # Add runbook from deps if available
    # if ctx.deps.runbook:
    #     result.append(f"RUNBOOK DETAILS:\n{ctx.deps.runbook}")
    
    # Add alert category details if available
    if ctx.deps.alert_category:
        category = ctx.deps.alert_category.get("category", "Unknown Category")
        description = ctx.deps.alert_category.get("description", "")
        likely_causes = ctx.deps.alert_category.get("likely_causes", [])
        metrics = ctx.deps.alert_category.get("metrics", [])
        runbook_text = ctx.deps.alert_category.get("runbook", "")
        
        result.append(f"""
ALERT CATEGORY DETAILS:
Category: {category}
Description: {description}
Likely Causes: {', '.join(likely_causes)}
Key Metrics: {', '.join([m.get('name', '') for m in metrics])}

RUNBOOK STEPS:
{runbook_text}
""")
    
    # Add entity information if available
    if ctx.deps.primary_entity:
        entity_context = f"\nPRIMARY ENTITY:\n{ctx.deps.primary_entity.get('name')} (GUID: {ctx.deps.primary_entity.get('guid')}, Type: {ctx.deps.primary_entity.get('type')})\n"
        
        if ctx.deps.related_entities:
            entity_context += "\nRELATED ENTITIES:\n"
            for entity in ctx.deps.related_entities:
                entity_context += f"- {entity.get('name')} (GUID: {entity.get('guid')}, Type: {entity.get('type')}, Relationship: {entity.get('relationship', 'unknown')})\n"
        
        result.append(entity_context)
    
    # Add time window if available - enhanced with more emphasis on epoch milliseconds
    time_window = ctx.deps.get_time_window()
    if time_window:
        time_window_info = []
        
        # Add human-readable time window
        if "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            time_window_info.append(f"Human-readable: {since_time.isoformat()} to {until_time.isoformat()}")
        
        # Add epoch millisecond time window with emphasis
        if "since_time_ms" in time_window and "until_time_ms" in time_window:
            since_time_ms = time_window["since_time_ms"]
            until_time_ms = time_window["until_time_ms"]
            time_window_info.append(f"Epoch milliseconds: {since_time_ms} to {until_time_ms}")
            
            # Add example NRQL usage
            time_window_info.append(f"\nFor NRQL queries, use:\nSINCE {since_time_ms} UNTIL {until_time_ms}")
        
        result.append("\nTIME WINDOW INFORMATION:")
        result.append("\n".join(time_window_info))
        result.append("\nALWAYS USE EPOCH MILLISECONDS FOR NRQL QUERIES AND TIME-BASED API CALLS")
    
    return "\n".join(result)


@runbook_agent.tool
async def get_pod_logs(
    ctx: RunContext,
    entity_guid: str,
    pod_name: str, # not optional
    since_time_ms: int,  # Mandatory epoch milliseconds
    until_time_ms: int,  # Mandatory epoch milliseconds
    limit: int = 100
) -> Dict[str, Any]:
    """
    Get the logs from a Kubernetes pod.
    
    Args:
        entity_guid: New Relic entity GUID for the pod
        pod_name: Pod name
        since_time_ms: Start time in epoch milliseconds
        until_time_ms: End time in epoch milliseconds
        limit: Maximum number of log lines to return
        
    Returns:
        Pod logs and metadata including exact time window used
    """
    try:
        if not pod_name:
            # Get entity details
            metrics_collector = ctx.deps.metrics_collector
            entity_details = await metrics_collector.get_entity_details(entity_guid)
            
            if not entity_details:
                logger.error(f"Entity not found: {entity_guid}")
                return {
                    "success": False,
                    "error": f"Entity not found: {entity_guid}",
                    "logs": []
                }
                
            # Find pod name from entity details or entity list
            pod_name = entity_details.get("name")
        
        # If entity not found in main entity, check related entities
        if not pod_name and entity_guid:
            # Try to get entity from dependencies
            entity = ctx.deps.get_entity_by_guid(entity_guid)
            if entity:
                pod_name = entity.get("name")
        
        if not pod_name:
            logger.error(f"Could not determine pod name for entity: {entity_guid}")
            return {
                "success": False,
                "error": f"Could not determine pod name for entity: {entity_guid}",
                "logs": []
            }
            
        # Convert milliseconds to datetime for the logs client
        since_time = datetime.fromtimestamp(since_time_ms / 1000, tz=timezone.utc)
        until_time = datetime.fromtimestamp(until_time_ms / 1000, tz=timezone.utc)
        
        # Determine partition name from state if available
        partition = "all"  # Default partition
        if ctx.deps.state:
            # Use safe_get_state to handle both dict and Pydantic model
            product = ctx.deps.safe_get_state("product", "").lower()
            landscape = ctx.deps.safe_get_state("landscape", "").lower()
            
            if product and landscape:
                # Construct partition name pattern: product_landscape
                partition_key = f"{product}_{landscape}"
                # Check if this matches any known partition key pattern
                if partition_key in NewRelicLogsClient.KNOWN_PARTITIONS:
                    partition = partition_key
                # For neurons partitions that follow the pattern "neurons_XXX"
                elif product == "neurons" and landscape in ["nvu", "mlu", "uku", "ttu", "tku"]:
                    partition = f"neurons_{landscape}"
                # For MDM partitions that follow the pattern "mdm_XXX"
                elif product == "mdm" and landscape in ["na1", "na2", "ap1", "ap2"]:
                    partition = f"mdm_{landscape}"
        
        # Get logs directly from the logs client in dependencies
        logs_client = ctx.deps.logs_client
        
        logger.info(f"Querying logs for pod: {pod_name}")
        logger.info(f"Using partition: {partition}")
        logger.info(f"Time window (epoch ms): {since_time_ms} to {until_time_ms}")
        
        # Get logs directly from the logs client
        pod_logs = logs_client.get_pod_logs(
            pod_name=pod_name,
            cluster_partition=partition,
            since=since_time,
            until=until_time,
            limit=limit
        )
        
        # If no logs found using the partition, try with default partition
        if not pod_logs and partition != "default":
            logger.info(f"No logs found in partition {partition}, trying default partition")
            pod_logs = logs_client.get_pod_logs(
                pod_name=pod_name,
                cluster_partition="default",
                since=since_time,
                until=until_time,
                limit=limit
            )
            
        # If still no logs, try with neurons_nvu partition (common for pod logs)
        if not pod_logs and partition != "neurons_nvu":
            logger.info("No logs found in default partition, trying neurons_nvu partition")
            pod_logs = logs_client.get_pod_logs(
                pod_name=pod_name,
                cluster_partition="neurons_nvu",
                since=since_time,
                until=until_time,
                limit=limit
            )
        
        result = {
            "success": True,
            "pod_name": pod_name,
            "entity_guid": entity_guid,
            "logs_count": len(pod_logs),
            "partition": partition,
            "since_time": since_time.isoformat(),
            "until_time": until_time.isoformat(),
            "since_time_ms": since_time_ms,
            "until_time_ms": until_time_ms,
            "logs": pod_logs
        }
        
        # Store tool result in MongoDB if incident_id is available
        incident_id = ctx.deps.safe_get_state("incident_id")
        if incident_id:
            # Get run_id if available
            run_id = ctx.deps.safe_get_state("run_id")
            
            # First try to get the MongoDB service safely
            mongodb_service = None
            try:
                # Import here to avoid circular imports
                from ai_incident_manager.services.mongodb_service import get_mongodb_service
                mongodb_service = get_mongodb_service()
            except Exception as mongo_init_error:
                logger.error(f"Could not initialize MongoDB service: {str(mongo_init_error)}")
                result["db_error"] = f"MongoDB initialization failed: {str(mongo_init_error)}"
                
            # Only proceed if we have a valid MongoDB service
            if mongodb_service:
                try:
                    # Create parameters for storing
                    tool_parameters = {
                        "entity_guid": entity_guid,
                        "pod_name": pod_name,
                        "since_time_ms": since_time_ms,
                        "until_time_ms": until_time_ms,
                        "limit": limit
                    }
                    
                    # Create a copy of the result for MongoDB storage
                    mongo_result = result.copy()
                    
                    # Store the tool result
                    tool_result_id = await mongodb_service.store_tool_result(
                        incident_id=incident_id,
                        tool_name="get_pod_logs",
                        tool_parameters=tool_parameters,
                        result_data=mongo_result,
                        entity_guid=entity_guid,
                        run_id=run_id
                    )
                    
                    # Also store in logs collection
                    log_id = await mongodb_service.store_logs(
                        incident_id=incident_id,
                        entity_guid=entity_guid,
                        log_data={
                            "pod_name": pod_name,
                            "logs": pod_logs,
                            "since_time_ms": since_time_ms,
                            "until_time_ms": until_time_ms,
                            "partition": partition
                        },
                        run_id=run_id
                    )
                    
                    # Add storage IDs to the result
                    result["tool_result_id"] = tool_result_id
                    result["log_id"] = log_id
                    
                    logger.info(f"Stored pod logs in MongoDB. Tool result ID: {tool_result_id}, Log ID: {log_id}")
                    
                except Exception as db_error:
                    logger.error(f"Error storing pod logs in MongoDB: {str(db_error)}")
                    result["db_error"] = str(db_error)
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting pod logs: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": str(e),
            "logs": []
        }


@runbook_agent.tool
async def get_kubernetes_description(
    ctx: RunContext,
    entity_guid: Optional[str] = None,
    entity_type: str = "Pod",
    entity_name: Optional[str] = None,
    cluster_name: Optional[str] = None,
    since_time_ms: int = None,
    until_time_ms: int = None
) -> Dict[str, Any]:
    """
    Get detailed description of a Kubernetes entity similar to 'kubectl describe'.
    
    This tool retrieves a detailed description of Kubernetes entities including:
    - Pods
    - Nodes
    - Deployments
    - Services
    - DaemonSets
    - CronJobs
    - Jobs
    - and other Kubernetes resources
    
    Args:
        entity_guid: Optional New Relic entity GUID for the Kubernetes resource
        entity_type: Type of Kubernetes entity (e.g., Pod, Node, Deployment)
        entity_name: Optional name of the entity to query
        cluster_name: Optional cluster name to filter results
        since_time_ms: Start time in epoch milliseconds
        until_time_ms: End time in epoch milliseconds
        
    Returns:
        Detailed entity description and metadata
    """
    try:
        # Get metrics collector from context
        metrics_collector = ctx.deps.metrics_collector
        
        # If times not provided, get from state
        if since_time_ms is None or until_time_ms is None:
            time_window = ctx.deps.get_time_window()
            
            if "since_time_ms" in time_window and "until_time_ms" in time_window:
                if since_time_ms is None:
                    since_time_ms = time_window["since_time_ms"]
                if until_time_ms is None:
                    until_time_ms = time_window["until_time_ms"]
            else:
                # Default to last 30 minutes if nothing provided
                until_time_ms = int(datetime.now(timezone.utc).timestamp() * 1000)
                since_time_ms = until_time_ms - (30 * 60 * 1000)  # 30 minutes
        
        # Convert to datetime for the API call
        since_time = datetime.fromtimestamp(since_time_ms / 1000, tz=timezone.utc)
        until_time = datetime.fromtimestamp(until_time_ms / 1000, tz=timezone.utc)
        
        logger.info(f"Fetching Kubernetes description for {entity_type}")
        logger.info(f"Time window (epoch ms): {since_time_ms} to {until_time_ms}")
        
        # Call the metrics collector to get the Kubernetes description
        result = metrics_collector.get_kubernetes_entity_description(
            entity_guid=entity_guid,
            entity_type=entity_type,
            entity_name=entity_name,
            cluster_name=cluster_name,
            since_time=since_time,
            until_time=until_time
        )
        
        # Add time window information to the result
        result["time_window"] = {
            "since_time": since_time.isoformat(),
            "until_time": until_time.isoformat(),
            "since_time_ms": since_time_ms,
            "until_time_ms": until_time_ms
        }
        
        # Process the result for better readability
        if "description" in result and result["description"]:
            # Extract key information for summary
            entity_type = result.get("entity_type", "Unknown")
            entity_name = result.get("entity_name", "Unknown")
            
            # Add a summary based on the entity type
            if entity_type == "Pod":
                # Extract status and container info for pods
                status = "Unknown"
                containers_status = []
                
                lines = result["description"].split('\n')
                for i, line in enumerate(lines):
                    if line.startswith("Status:"):
                        status = line.split(':', 1)[1].strip()
                    
                    # Look for container statuses
                    if "State:" in line and i > 0 and "Container ID:" in lines[i-1]:
                        container_name = lines[i-2].strip().rstrip(':')
                        state = line.split(':', 1)[1].strip()
                        containers_status.append(f"{container_name}: {state}")
                
                result["summary"] = {
                    "entity_type": "Pod",
                    "name": entity_name,
                    "status": status,
                    "containers": containers_status
                }
            
            elif entity_type == "Node":
                # Extract node conditions and resource info
                conditions = []
                resources = {}
                
                lines = result["description"].split('\n')
                in_conditions_section = False
                
                for line in lines:
                    if line.startswith("Conditions:"):
                        in_conditions_section = True
                        continue
                    
                    if in_conditions_section and line.strip() and not line.startswith(" "):
                        in_conditions_section = False
                    
                    if in_conditions_section and ":" in line:
                        conditions.append(line.strip())
                    
                    # Extract resource info
                    if "cpu:" in line or "memory:" in line:
                        parts = line.split(':', 1)
                        if len(parts) == 2:
                            key = parts[0].strip()
                            value = parts[1].strip()
                            resources[key] = value
                
                result["summary"] = {
                    "entity_type": "Node",
                    "name": entity_name,
                    "conditions": conditions,
                    "resources": resources
                }
            
            elif entity_type in ["Deployment", "DaemonSet", "ReplicaSet"]:
                # Extract deployment status
                replicas = "Unknown"
                strategy = "Unknown"
                
                lines = result["description"].split('\n')
                for line in lines:
                    if "Replicas:" in line:
                        replicas = line.split(':', 1)[1].strip()
                    if "Strategy:" in line:
                        strategy = line.split(':', 1)[1].strip()
                
                result["summary"] = {
                    "entity_type": entity_type,
                    "name": entity_name,
                    "replicas": replicas,
                    "strategy": strategy
                }
        
        # Store tool result in MongoDB if incident_id is available
        incident_id = ctx.deps.safe_get_state("incident_id")
        if incident_id:
            # Get run_id if available
            run_id = ctx.deps.safe_get_state("run_id")
            
            # First try to get the MongoDB service safely
            mongodb_service = None
            try:
                # Import here to avoid circular imports
                from ai_incident_manager.services.mongodb_service import get_mongodb_service
                mongodb_service = get_mongodb_service()
            except Exception as mongo_init_error:
                logger.error(f"Could not initialize MongoDB service: {str(mongo_init_error)}")
                result["db_error"] = f"MongoDB initialization failed: {str(mongo_init_error)}"
                
            # Only proceed if we have a valid MongoDB service
            if mongodb_service:
                try:
                    # Create parameters for storing
                    tool_parameters = {
                        "entity_guid": entity_guid,
                        "entity_type": entity_type,
                        "entity_name": entity_name,
                        "cluster_name": cluster_name,
                        "since_time_ms": since_time_ms,
                        "until_time_ms": until_time_ms
                    }
                    
                    # Create a copy of the result for MongoDB storage
                    mongo_result = result.copy()
                    
                    # Store the tool result
                    tool_result_id = await mongodb_service.store_tool_result(
                        incident_id=incident_id,
                        tool_name="get_kubernetes_description",
                        tool_parameters=tool_parameters,
                        result_data=mongo_result,
                        entity_guid=entity_guid,
                        run_id=run_id
                    )
                    
                    # Store in events collection
                    event_id = await mongodb_service.store_events(
                        incident_id=incident_id,
                        entity_guid=entity_guid or "",
                        event_data={
                            "type": "kubernetes_description",
                            "entity_type": entity_type,
                            "entity_name": entity_name,
                            "description": result.get("description", ""),
                            "summary": result.get("summary", {})
                        },
                        run_id=run_id
                    )
                    
                    # Add storage IDs to the result
                    result["tool_result_id"] = tool_result_id
                    result["event_id"] = event_id
                    
                    logger.info(f"Stored Kubernetes description in MongoDB. Tool result ID: {tool_result_id}, Event ID: {event_id}")
                    
                except Exception as db_error:
                    logger.error(f"Error storing Kubernetes description in MongoDB: {str(db_error)}")
                    result["db_error"] = str(db_error)
        
        return result
            
    except Exception as e:
        entity_identifier = entity_guid or f"{entity_type}/{entity_name}" if entity_name else entity_type
        logger.error(f"Error getting Kubernetes description for {entity_identifier}: {str(e)}")
        return {
            "error": f"Failed to get Kubernetes description: {str(e)}",
            "entity_guid": entity_guid,
            "entity_type": entity_type,
            "entity_name": entity_name,
            "cluster_name": cluster_name
        }


@runbook_agent.tool
async def get_pod_events(
    ctx: RunContext,
    pod_name: str,
    since_time_ms: int,
    until_time_ms: int,
    cluster_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get Kubernetes events for a specific pod.
    
    This tool retrieves events related to a pod, such as scheduling, creation, deletion,
    failures, and other lifecycle events.
    
    Args:
        pod_name: Name of the pod
        since_time_ms: Start time in epoch milliseconds
        until_time_ms: End time in epoch milliseconds
        cluster_name: Optional cluster name to filter events (if known)
        
    Returns:
        Pod events and metadata with both epoch millisecond and human-readable timestamps
    """
    try:
        # Get query client from context
        query_client = ctx.deps.query_client
        
        logger.info(f"Fetching Kubernetes events for pod: {pod_name}")
        logger.info(f"Time window (epoch ms): {since_time_ms} to {until_time_ms}")
        if cluster_name:
            logger.info(f"Filtering by cluster: {cluster_name}")
        
        # Execute the query using epoch millisecond timestamps
        pod_events = query_client.get_kubernetes_events(
            object_name=pod_name,
            object_kind="Pod",
            cluster_name=cluster_name,
            since=since_time_ms,
            until=until_time_ms
        )
        
        # Convert epoch milliseconds to human-readable time for the response
        since_iso = datetime.fromtimestamp(since_time_ms / 1000, tz=timezone.utc).isoformat()
        until_iso = datetime.fromtimestamp(until_time_ms / 1000, tz=timezone.utc).isoformat()
        
        # Format the response with both epoch ms and human-readable timestamps
        result = {
            "success": True,
            "pod_name": pod_name,
            "cluster_name": cluster_name,
            "since_time_ms": since_time_ms,
            "until_time_ms": until_time_ms,
            "since_time": since_iso,
            "until_time": until_iso,
            "event_count": len(pod_events),
            "events": pod_events
        }
        
        # Store tool result in MongoDB if incident_id is available
        incident_id = ctx.deps.safe_get_state("incident_id")
        if incident_id:
            # Get run_id if available
            run_id = ctx.deps.safe_get_state("run_id")
            
            # First try to get the MongoDB service safely
            mongodb_service = None
            try:
                # Import here to avoid circular imports
                from ai_incident_manager.services.mongodb_service import get_mongodb_service
                mongodb_service = get_mongodb_service()
            except Exception as mongo_init_error:
                logger.error(f"Could not initialize MongoDB service: {str(mongo_init_error)}")
                result["db_error"] = f"MongoDB initialization failed: {str(mongo_init_error)}"
                
            # Only proceed if we have a valid MongoDB service
            if mongodb_service:
                try:
                    # Create parameters for storing
                    tool_parameters = {
                        "pod_name": pod_name,
                        "since_time_ms": since_time_ms,
                        "until_time_ms": until_time_ms,
                        "cluster_name": cluster_name
                    }
                    
                    # Create a copy of the result for MongoDB storage
                    mongo_result = result.copy()
                    
                    # Get entity GUID if available
                    entity_guid = None
                    if pod_name:
                        # Try to find entity with this pod name
                        for entity in ctx.deps.state.entities if hasattr(ctx.deps.state, "entities") else []:
                            if entity.entity_name == pod_name:
                                entity_guid = entity.entity_guid
                                break
                    
                    # Store the tool result
                    tool_result_id = await mongodb_service.store_tool_result(
                        incident_id=incident_id,
                        tool_name="get_pod_events",
                        tool_parameters=tool_parameters,
                        result_data=mongo_result,
                        entity_guid=entity_guid,
                        run_id=run_id
                    )
                    
                    # Also store in events collection
                    event_id = await mongodb_service.store_events(
                        incident_id=incident_id,
                        entity_guid=entity_guid or "",
                        event_data={
                            "pod_name": pod_name,
                            "events": pod_events,
                            "since_time_ms": since_time_ms,
                            "until_time_ms": until_time_ms,
                            "cluster_name": cluster_name,
                            "type": "kubernetes_pod_events"
                        },
                        run_id=run_id
                    )
                    
                    # Add storage IDs to the result
                    result["tool_result_id"] = tool_result_id
                    result["event_id"] = event_id
                    
                    logger.info(f"Stored pod events in MongoDB. Tool result ID: {tool_result_id}, Event ID: {event_id}")
                    
                except Exception as db_error:
                    logger.error(f"Error storing pod events in MongoDB: {str(db_error)}")
                    result["db_error"] = str(db_error)
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting pod events: {str(e)}", exc_info=True)
        return {
            "success": False,
            "pod_name": pod_name,
            "error": str(e),
            "events": []
        }

@runbook_agent.tool
async def get_node_events(
    ctx: RunContext,
    node_name: str,
    since_time_ms: int,
    until_time_ms: int,
    cluster_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get Kubernetes events for a specific node.
    
    This tool retrieves events related to a node, such as resource pressure,
    kubelet status, node conditions, and other node-level events.
    
    Args:
        node_name: Name of the node
        since_time_ms: Start time in epoch milliseconds
        until_time_ms: End time in epoch milliseconds
        cluster_name: Optional cluster name to filter events (if known)
        
    Returns:
        Node events and metadata with both human-readable and epoch millisecond timestamps
    """
    try:
        # Get query client from context
        query_client = ctx.deps.query_client
        
        logger.info(f"Fetching Kubernetes events for node: {node_name}")
        logger.info(f"Time window (epoch ms): {since_time_ms} to {until_time_ms}")
        if cluster_name:
            logger.info(f"Filtering by cluster: {cluster_name}")
        
        # Execute the query using epoch millisecond timestamps
        node_events = query_client.get_kubernetes_events(
            object_name=node_name,
            object_kind="Node",
            cluster_name=cluster_name,
            since=since_time_ms,
            until=until_time_ms
        )
        
        # Convert epoch milliseconds to human-readable time for the response
        since_iso = datetime.fromtimestamp(since_time_ms / 1000, tz=timezone.utc).isoformat()
        until_iso = datetime.fromtimestamp(until_time_ms / 1000, tz=timezone.utc).isoformat()
        
        # Format the response with both epoch ms and human-readable timestamps
        result = {
            "success": True,
            "node_name": node_name,
            "cluster_name": cluster_name,
            "since_time_ms": since_time_ms,
            "until_time_ms": until_time_ms,
            "since_time": since_iso,
            "until_time": until_iso,
            "event_count": len(node_events),
            "events": node_events
        }
        
        # Store tool result in MongoDB if incident_id is available
        incident_id = ctx.deps.safe_get_state("incident_id")
        if incident_id:
            # Get run_id if available
            run_id = ctx.deps.safe_get_state("run_id")
            
            # First try to get the MongoDB service safely
            mongodb_service = None
            try:
                # Import here to avoid circular imports
                from ai_incident_manager.services.mongodb_service import get_mongodb_service
                mongodb_service = get_mongodb_service()
            except Exception as mongo_init_error:
                logger.error(f"Could not initialize MongoDB service: {str(mongo_init_error)}")
                result["db_error"] = f"MongoDB initialization failed: {str(mongo_init_error)}"
                
            # Only proceed if we have a valid MongoDB service
            if mongodb_service:
                try:
                    # Create parameters for storing
                    tool_parameters = {
                        "node_name": node_name,
                        "since_time_ms": since_time_ms,
                        "until_time_ms": until_time_ms,
                        "cluster_name": cluster_name
                    }
                    
                    # Create a copy of the result for MongoDB storage
                    mongo_result = result.copy()
                    
                    # Get entity GUID if available
                    entity_guid = None
                    if node_name:
                        # Try to find entity with this node name
                        for entity in ctx.deps.state.entities if hasattr(ctx.deps.state, "entities") else []:
                            if entity.entity_name == node_name:
                                entity_guid = entity.entity_guid
                                break
                    
                    # Store the tool result
                    tool_result_id = await mongodb_service.store_tool_result(
                        incident_id=incident_id,
                        tool_name="get_node_events",
                        tool_parameters=tool_parameters,
                        result_data=mongo_result,
                        entity_guid=entity_guid,
                        run_id=run_id
                    )
                    
                    # Also store in events collection
                    event_id = await mongodb_service.store_events(
                        incident_id=incident_id,
                        entity_guid=entity_guid or "",
                        event_data={
                            "node_name": node_name,
                            "events": node_events,
                            "since_time_ms": since_time_ms,
                            "until_time_ms": until_time_ms,
                            "cluster_name": cluster_name,
                            "type": "kubernetes_node_events"
                        },
                        run_id=run_id
                    )
                    
                    # Add storage IDs to the result
                    result["tool_result_id"] = tool_result_id
                    result["event_id"] = event_id
                    
                    logger.info(f"Stored node events in MongoDB. Tool result ID: {tool_result_id}, Event ID: {event_id}")
                    
                except Exception as db_error:
                    logger.error(f"Error storing node events in MongoDB: {str(db_error)}")
                    result["db_error"] = str(db_error)
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting node events: {str(e)}", exc_info=True)
        return {
            "success": False,
            "node_name": node_name,
            "error": str(e),
            "events": []
        }

@runbook_agent.tool
async def get_pod_metrics(
    ctx: RunContext,
    entity_guid: str,
    pod_name: str,
    cluster_name: str,
    since_time_ms: int,
    until_time_ms: int,
    metrics: List[str] = None
) -> Dict[str, Any]:
    """
    Get metrics for a Kubernetes pod.
    
    This tool collects various metrics for a pod including CPU usage, memory usage,
    restart count, and container status.
    
    Args:
        entity_guid: GUID of the pod entity
        pod_name: Name of the pod
        cluster_name: Name of the cluster
        since_time_ms: Start time in epoch milliseconds
        until_time_ms: End time in epoch milliseconds
        metrics: List of metrics to collect (cpu_usage, memory_usage, restart_count, container_status)
        
    Returns:
        Metrics for the pod over the specified time window
    """
    try:
        # Convert millisecond timestamps to datetime objects
        since_time = datetime.fromtimestamp(since_time_ms / 1000, tz=timezone.utc)
        until_time = datetime.fromtimestamp(until_time_ms / 1000, tz=timezone.utc)
        
        logger.info(f"Fetching pod metrics for entity: {entity_guid}")
        logger.info(f"Time window (epoch ms): {since_time_ms} to {until_time_ms}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get pod metrics
        metrics_result = await metrics_collector.get_pod_metrics(
            entity_guid=entity_guid,
            pod_name=pod_name,
            cluster_name=cluster_name,
            since_time=since_time,
            until_time=until_time,
            metrics=metrics
        )
        
        # Add time window information to the result
        if isinstance(metrics_result, dict):
            metrics_result["time_window"] = {
                "since_time": since_time.isoformat(),
                "until_time": until_time.isoformat(),
                "since_time_ms": since_time_ms,
                "until_time_ms": until_time_ms
            }
        
        # Store tool result in MongoDB if incident_id is available
        incident_id = ctx.deps.safe_get_state("incident_id")
        if incident_id:
            try:
                # Get run_id if available
                run_id = ctx.deps.safe_get_state("run_id")
                
                # Import here to avoid circular imports
                from ai_incident_manager.services.mongodb_service import get_mongodb_service
                mongodb_service = get_mongodb_service()
                
                # Create parameters for storing
                tool_parameters = {
                    "entity_guid": entity_guid,
                    "pod_name": pod_name,
                    "cluster_name": cluster_name,
                    "since_time_ms": since_time_ms,
                    "until_time_ms": until_time_ms,
                    "metrics": metrics
                }
                
                # Create a copy of the result for MongoDB storage
                mongo_result = metrics_result.copy() if isinstance(metrics_result, dict) else metrics_result
                
                # Store the tool result
                tool_result_id = await mongodb_service.store_tool_result(
                    incident_id=incident_id,
                    tool_name="get_pod_metrics",
                    tool_parameters=tool_parameters,
                    result_data=mongo_result,
                    entity_guid=entity_guid,
                    run_id=run_id
                )
                
                # Also store in metrics collection
                # Process the metrics data
                for metric_name, metric_data in metrics_result.get("metrics", {}).items():
                    metric_id = await mongodb_service.store_metrics(
                        incident_id=incident_id,
                        entity_guid=entity_guid,
                        metric_name=metric_name,
                        metric_data=metric_data,
                        run_id=run_id
                    )
                
                # Add storage ID to the result
                metrics_result["tool_result_id"] = tool_result_id
                
                logger.info(f"Stored pod metrics in MongoDB. Tool result ID: {tool_result_id}")
                
            except Exception as db_error:
                logger.error(f"Error storing pod metrics in MongoDB: {str(db_error)}")
                if isinstance(metrics_result, dict):
                    metrics_result["db_error"] = str(db_error)
        
        return metrics_result
        
    except Exception as e:
        logger.error(f"Error getting pod metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get pod metrics",
            "entity_guid": entity_guid,
            "since_time_ms": since_time_ms,
            "until_time_ms": until_time_ms
        }

async def get_alert_category_config(category: str) -> Dict[str, Any]:
    """
    Get the configuration for a specific alert category.
    
    Args:
        category: The category of the alert
        
    Returns:
        Dict containing the alert category configuration
    """
    import yaml
    import os
    
    try:
        # Load alert categories config
        config_path = os.path.join("ai_incident_manager", "config", "alert_categories.yaml")
        
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        
        # Find the category
        for cat in config.get("condition_categories", []):
            if cat.get("category") == category:
                return cat
                
        return None
    except Exception as e:
        logger.error(f"Error loading alert category config: {str(e)}")
        return None

async def main():
    """
    Test function for the runbook agent.
    """
    # Initialize dependencies
    logger.info("Initializing dependencies")
    
    # Load environment variables
    dotenv.load_dotenv()
    
    # Initialize the metrics collector
    metrics_collector = MetricsCollector()
    
    # Initialize OpenAI client
    azure_openai_enabled = "AZURE_OPENAI_ENDPOINT" in os.environ and "AZURE_OPENAI_API_KEY" in os.environ
    
    if azure_openai_enabled:
        openai_client = AsyncAzureOpenAI(
            azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
            azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
            api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
            api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
        )
    else:
        openai_client = AsyncOpenAI(
            api_key=os.environ.get("OPENAI_API_KEY")
        )
    
    # Initialize New Relic GraphQL client
    api_key = os.environ.get("NEWRELIC_API_KEY")
    account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        logger.error("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set in environment variables")
        return
    
    # Create the New Relic GraphQL client
    graphql_client = NewRelicGraphQLClient(
        api_key=api_key,
        account_id=account_id,
        region=Region.US,
        debug=True
    )
    
    # Create the logs client using the GraphQL client
    logs_client = NewRelicLogsClient(client=graphql_client, debug=True)
    
    # Create sample alert info with condition details
    sample_alert_info = {
        "issueId": "a271ddf7-a9a8-4dc1-beea-39cca4fa1987", 
        "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/a271ddf7-a9a8-4dc1-beea-39cca4fa1987?notifier=WEBHOOK", 
        "title": "agent-management-background-container-service query result is > 10.0 on 'Pod with CrashLoopBackOff -- '", 
        "priority": "CRITICAL", 
        "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"], 
        "impactedEntities": ["agent-management-background-container-service"], 
        "totalIncidents": "1", 
        "state": "ACTIVATED", 
        "trigger": "STATE_CHANGE", 
        "createdAt": *************, 
        "updatedAt": *************, 
        "sources": ["newrelic"], 
        "alertPolicyNames": ["Neurons k8s Infra - Critical"], 
        "alertConditionNames": ["Pod with CrashLoopBackOff -- "], 
        "workflowName": "obv-ai-processing-neurons", 
        "chartLink": "https://gorgon.nr-assets.net/image/7cd23d09-c8aa-4e97-928a-d82a5bcfc2c9?config.legend.enabled=false&width=400&height=210", 
        "product": "neurons", 
        "nr_region": "us",
        
        # Additional fields needed for processing
        "alert_category": "castai_workload_autoscaler",
        "condition_id": "25541032",
        "triggered_at": datetime.fromtimestamp(************* / 1000).isoformat(),
        
        # Include time window information (from epoch milliseconds)
        "since_time_ms": ************* - (2700 * 1000),  # 2700 seconds before triggered_at
        "until_time_ms": *************,
        "threshold_duration": 2700,
        "aggregation_window": 900,
        
        # Adding human-readable time for reference
        "since_time": datetime.fromtimestamp((************* - (2700 * 1000)) / 1000).isoformat(),
        "until_time": datetime.fromtimestamp(************* / 1000).isoformat(),
        
        # Additional condition details
        "condition_details": {
            "description": "Pod is in CrashLoopBackOff.",
            "enabled": True,
            "id": "25541032",
            "name": "Pod with CrashLoopBackOff -- ",
            "policyId": "2405324",
            "runbookUrl": None,
            "signal": {
                "aggregationDelay": 120,
                "aggregationMethod": "EVENT_FLOW",
                "aggregationTimer": None,
                "aggregationWindow": 900
            },
            "terms": [
                {
                    "operator": "ABOVE",
                    "priority": "CRITICAL",
                    "threshold": 10,
                    "thresholdDuration": 2700,
                    "thresholdOccurrences": "AT_LEAST_ONCE"
                }
            ],
            "type": "STATIC",
            "violationTimeLimitSeconds": 21600,
            "policyName": "Neurons k8s Infra - Critical",
            "formatted_threshold": "ABOVE 10.0 for 2700 seconds",
            "query": "select count(*) from K8sContainerSample where status='Waiting' and reason='CrashLoopBackOff'"
        }
    }
    
    # Set up dependencies for the agent
    deps = RunbookAgentDeps(
        openai_client=openai_client,
        metrics_collector=metrics_collector,
        logs_client=logs_client,  # Pass the properly initialized logs client
        state=sample_alert_info  # Pass the sample alert info as state
    )
    
    # Get runbook service
    runbook_service = get_runbook_service()
    
    # Test the runbook service directly
    logger.info("Testing RunbookService capabilities with dynamic parameters")
    
    # Get available runbooks
    all_runbooks = runbook_service.get_all_runbooks()
    logger.info(f"Found {len(all_runbooks)} runbooks")
    
    # Get recommended runbooks for pod issues
    pod_runbooks = runbook_service.get_recommended_runbooks(
        alert_category="kubernetes_crashloopbackoff",
        entity_types=["KUBERNETES_POD"],
        max_results=2
    )
    logger.info(f"Recommended pod runbooks: {[rb['id'] for rb in pod_runbooks]}")
    
    # Test get_pod_logs function with a proper logs client
    entity_guid = "MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"  # Sample entity GUID from alert info
    logger.info(f"Testing get_pod_logs with entity GUID: {entity_guid}")
    
    
    # Test dynamically evaluating time windows
    # Example from runbook: time_window_minutes: "{max(aggregation_window/60, 30)}"
    logger.info("Testing dynamic parameter evaluation:")
    
    # Test the agent with a recommendation query
    test_prompt = "What runbooks should I use for a pod with crashloopbackoff issues?"
    logger.info(f"Testing runbook agent with prompt: {test_prompt}")
    
    # Run the agent
    result = await runbook_agent.run(test_prompt, deps=deps)
    logger.info(f"Agent recommendation response: {result.data}")
    
    # Test entity existence check with a sample entity
    # Note: Replace with a real entity GUID for testing
    test_entity_guid = "MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"
    
    logger.info(f"Testing entity existence check with entity: {test_entity_guid}")
    
    # Skip the check_entity_exists call since it requires RunContext
    # Instead, we'll simulate the entity check result
    entity_check = {
        "exists": False,
        "entity_guid": test_entity_guid,
        "entity_name": "test-pod",
        "entity_type": "KUBERNETES_POD",
        "cluster": "test-cluster",
        "namespace": "test-namespace"
    }
    
    if entity_check["exists"]:
        # Demonstrate the improved tool execution flow using RunbookService directly
        logger.info("Demonstrating improved tool execution using RunbookService with state")
        
        # Extract entity info
        entity_info = {
            "entity_guid": entity_check["entity_guid"],
            "entity_name": entity_check["entity_name"],
            "entity_type": entity_check["entity_type"],
            "cluster_name": entity_check["cluster"],
            "namespace": entity_check["namespace"]
        }
        
        # Execute a tool using RunbookService with state
        tool_result = await runbook_service.execute_tool(
            metrics_collector=metrics_collector,
            tool_name="get_pod_metrics",
            entity_info=entity_info,
            parameters={
                "metrics": ["cpu_usage", "memory_usage", "restart_count"],
                "time_window_minutes": "{max(threshold_duration/60, 30)}"  # Dynamic parameter
            },
            state=sample_alert_info
        )
        
        # Generate a summary
        summary = runbook_service.generate_step_summary("Analyze Pod Metrics", tool_result)
        
        logger.info(f"Tool execution summary: {summary}")
        logger.info(f"Tool result: {tool_result}")
    else:
        logger.info(f"Test entity does not exist: {entity_check}")
    
    # Demo with execute_tool directly
    # logger.info("Testing execute_tool with direct tool executor")
    # tool_result = await tool_executor(
    #     metrics_collector=metrics_collector,
    #     tool_name="get_pod_metrics",
    #     entity_guid=test_entity_guid,
    #     parameters={
    #         "metrics": ["cpu_usage", "memory_usage", "restart_count"],
    #         "time_window_minutes": 30  # Fixed parameter instead of dynamic
    #     },
    #     state=sample_alert_info
    # )
    # logger.info(f"Tool executor result: {tool_result}")
    
    # Test runbook execution with state
    if pod_runbooks:
        logger.info("Testing runbook execution with state")
        runbook_id = pod_runbooks[0]["id"]
        
        # Create a sample call to execute_runbook_steps with manually created context
        logger.info(f"Would execute runbook {runbook_id} with entity {test_entity_guid}")
        logger.info("Skipping actual execution due to RunContext initialization requirements")


if __name__ == "__main__":
    # Run the main function as an async coroutine
    import asyncio
    
    # Set up logging for direct execution
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("Starting runbook agent test...")
    
    # Run the main function
    asyncio.run(main())
    
    logger.info("Runbook agent test completed.") 