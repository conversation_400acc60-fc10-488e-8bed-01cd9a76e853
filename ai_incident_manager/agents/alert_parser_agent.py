"""
Alert Parser Agent - Pydantic AI Agent for parsing alerts.

This agent analyzes New Relic alerts to extract detailed information and context.
It uses tools to retrieve additional information from New Relic API to enhance
alert understanding.
"""

import os
import re
import json
from loguru import logger
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple, cast
from dataclasses import dataclass
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel
from pydantic import BaseModel
from openai import AsyncOpenAI, AsyncAzureOpenAI
import dotenv
import asyncio
from devtools import pprint
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.base import NewRelicGraphQLError
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.logs import NewRelicLogsClient
from lib.new_relic.analyzer import EntityAnalyzer

from ai_incident_manager.models.workflow_state import IncidentState, Entity, AlertParserAgentResponse

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logger = logger.bind(name="alert_parser_agent")

# Initialize OpenAI client
required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
azure_openai_enabled = all(var in os.environ for var in required_azure_vars)

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    model = OpenAIModel(os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"), openai_client=openai_client)
else:
    logger.error("Azure OpenAI credentials not found. This application requires Azure OpenAI.")
    logger.error(f"Missing environment variables: {[var for var in required_azure_vars if var not in os.environ]}")
    raise EnvironmentError("Azure OpenAI credentials are required but not found in environment variables.")

# Import alert category service
from ai_incident_manager.services.alert_category_service import get_alert_category_service

@dataclass
class AlertParserDeps:
    """Dependencies for the alert parser agent."""
    openai_client: AsyncAzureOpenAI
    nr_query_client: NewRelicQueryClient
    entity_analyzer: EntityAnalyzer

# Alert Parser Agent with updated result_type and system message
alert_parser_agent = Agent(
    model,
    deps_type=AlertParserDeps,
    result_type=AlertParserAgentResponse,
    system_prompt="""You are an AI incident response analyst specializing in New Relic alerts.
Your task is to parse and analyze alert data to extract useful information and context.

Given a New Relic alert, you will:
1. Extract key information from the alert data
2. Use available tools to fetch additional context from New Relic
3. Categorize the alert and identify the most appropriate runbook
4. Extract entity information and other details
5. Determine appropriate time windows for analyzing metrics and logs

Key information looking for:
- Alert category
- Alert runbook
- affected entities guids
- alert title
- alert condition name
- alert condition id
- alert policy name
- alert policy id
- cluster name
- product
- nr_region
- landscape
- region (region of the product, should be available in the entity details metadata)
- since_time and until_time for metrics and logs analysis
- threshold_duration from the alert condition
- aggregation_window from the alert condition

Time Window Determination Rules:
- The until_time should be the alert creation time from the 'createdAt' field in milliseconds since epoch
- If 'createdAt' is not available, fall back to the current time
- The since_time should be calculated as follows:
  * If threshold_duration is available from the alert condition, use (alert_time - threshold_duration - buffer_time)
  * If aggregation_window is also available, include it in the calculation: (alert_time - threshold_duration - aggregation_window - buffer_time)
  * A buffer_time of 1 hour (3600 seconds) should be used to capture the complete alert window
  * The minimum look-back period should be 45 minutes even if the threshold duration is shorter
  * Store both times in both ISO 8601 format (since_time, until_time) and epoch milliseconds (since_time_ms, until_time_ms)

Landscape Determination Rules:
Landscape is determined based on the product and the cluster name or metadata.
For MDM product:
- The landscape will be one of the following values: 'na1', 'na2', 'ap1', 'ap2', 'eu1'
- Scan the entire issue details for any of these patterns:
  * For 'na1': "na1", "primary-na1", "na1-eks", "north-america-1"
  * For 'na2': "na2", "primary-na2", "na2-eks", "north-america-2"
  * For 'ap1': "ap1", "primary-ap1", "ap1-eks", "asia-pacific-1"
  * For 'ap2': "ap2", "primary-ap2", "ap2-eks", "asia-pacific-2"
  * For 'eu1': "eu1", "primary-eu1", "eu1-eks", "europe-1"


For Neurons product:
- The landscape will be one of the following values: 'nvu', 'uku', 'mlu', 'ttu', 'tku'
- Scan the entire issue details for any of these patterns:
  * For 'nvu': "aks-edge-rg-nvu-prd-neurons", "aks-rg-nvu-prd-neurons"
  * For 'uku': "aks-rg-uku-prd-neurons", "aks-edge-rg-uku-prd-neurons"
  * For 'mlu': "aks-edge-rg-mlu-prd-neurons", "aks-rg-mlu-prd-neurons"
  * For 'ttu': "aks-edge-rg-ttu-prd-neurons", "aks-rg-ttu-prd-neurons"
  * For 'tku': "aks-edge-rg-tku-prd-neurons", "aks-rg-tku-prd-neurons"

- If multiple patterns are found, prioritize the first occurrence
- If no landscape pattern is found, set landscape to "unknown"

Important for missing entity:
if get_entity_details tool does not return any entity details, update the entity_guid from issue details entity_id or user provided entity_guid. Also set entity missing to true.

Notes:
- Use the available tools to enhance the alert with additional data, and provide a complete picture
of the incident for further analysis.
- condition_id is the id of the alert condition, we can get it from get_issue_details tool
- The time window (since_time and until_time) is critical for metric and log analysis. Calculate this based on 
  threshold_duration and aggregation_window from the alert condition. The system needs to analyze data from
  before the alert triggered to understand what led to the problem.
"""
)

@alert_parser_agent.tool
async def get_issue_details(
    ctx: RunContext,
    issue_id: str,
    account_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Fetch detailed information about an alert or issue from New Relic. Every alert in New Relic is an issue, which might contain more than one incident.

    Args:
        issue_id: The ID of the issue
        account_id: Optional account ID, defaults to NEWRELIC_ACCOUNT_ID from env

    Returns:
        Detailed information about the issue
    """
    try:
        client = ctx.deps.nr_query_client

        if not account_id:
            account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")

        issue_details = client.get_issue_details(issue_id=issue_id, account_id=account_id)
        return issue_details
    except Exception as e:
        logger.error(f"Error fetching issue details: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to retrieve issue details from New Relic"
        }

@alert_parser_agent.tool
async def get_entity_details(
    ctx: RunContext,
    entity_guid: str
) -> Dict[str, Any]:
    """
    Fetch comprehensive details about an entity from New Relic using EntityAnalyzer. Metadata collected from entity might help to understand the alert better.

    Args:
        entity_guid: The GUID of the entity

    Returns:
        Detailed information about the entity including metrics, logs, and relationships
    """
    try:
        query_client = ctx.deps.nr_query_client

        # Use the current time to define a time window
        current_time = datetime.now()
        since_time = current_time - timedelta(minutes=30)
        until_time = current_time

        # Get product and region information from alert if available
        product = None
        region = None

        # Use the comprehensive analyze_entity method to get all entity details at once
        # entity_data = await analyzer.analyze_entity(
        #     entity_guid=entity_guid,
        #     since_time=since_time,
        #     until_time=until_time,
        #     product=product,
        #     region=region
        # )

        entity_data = query_client.get_entity_details(entity_guid=entity_guid)

        if not entity_data:
            return {
                "error": "Entity not found",
                "message": f"Could not find entity with GUID: {entity_guid}"
            }

        # Return the comprehensive entity data
        return entity_data

    except Exception as e:
        logger.error(f"Error fetching entity details: {str(e)}")
        return {
            "error": str(e),
            "message": f"Failed to retrieve entity details for {entity_guid}"
        }

@alert_parser_agent.tool
async def get_alert_condition(
    ctx: RunContext,
    condition_id: str,
    account_id: Optional[str] = None,
    alert_created_at: Optional[int] = None
) -> Dict[str, Any]:
    """
    Fetch alert condition details from New Relic. It helps to understand more about the alert and the condition that triggered it.
    Extract the threshold duration and aggregation window to determine the appropriate time window for metrics and logs analysis.

    Args:
        condition_id: The ID (String) of the alert condition, we can get it from get_issue_details tool
        account_id: Optional account ID, defaults to NEWRELIC_ACCOUNT_ID from env
        alert_created_at: Optional timestamp in milliseconds since epoch when the alert was created,
                          used to calculate the appropriate time window for analysis

    Returns:
        Alert condition details, including formatted threshold, threshold duration, and aggregation window
    """
    try:
        client = ctx.deps.nr_query_client

        if not account_id:
            account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
            
        # Use the new comprehensive method to get condition details
        logger.info(f"Fetching alert condition details for {condition_id}")
        logger.info(f"running get_alert_condition_details with condition_id: {condition_id} and account_id: {account_id}")
        condition = client.get_alert_condition_details(condition_id=condition_id, account_id=account_id)

        # Add some additional formatted information for easier analysis
        if condition:
            # Extract threshold duration and format it
            threshold_duration = None
            if "terms" in condition and condition["terms"]:
                term = condition["terms"][0]  # Use the first term
                threshold_duration = term.get("thresholdDuration")
                condition["threshold_duration"] = threshold_duration
                condition["formatted_threshold"] = f"{term.get('operator', '')} {term.get('threshold', '')} for {threshold_duration} seconds"
            
            # Extract aggregation window
            aggregation_window = None
            if "signal" in condition and "aggregationWindow" in condition["signal"]:
                aggregation_window = condition["signal"]["aggregationWindow"]
                condition["aggregation_window"] = aggregation_window
            
            # Get alert creation time from the context if available
            # The creation time should be provided by the caller in milliseconds since epoch
            alert_created_at_ms = alert_created_at
            
            # Default to current time if creation time is not available
            if alert_created_at_ms:
                # Convert from milliseconds to seconds and create datetime
                alert_time = datetime.fromtimestamp(alert_created_at_ms / 1000, timezone.utc)
                logger.info(f"Using alert creation time: {alert_time.isoformat()} from timestamp {alert_created_at_ms}")
            else:
                # Fall back to current time if no creation time provided
                alert_time = datetime.now(timezone.utc)
                logger.warning("No alert creation time provided, using current time instead")
            
            # Set buffer time to 1 hour as requested
            buffer_time = 3600  # 1 hour in seconds
            min_lookback = 2700  # 45 minutes in seconds
            
            # Determine the lookback period based on threshold duration and aggregation window
            lookback_period = min_lookback
            if threshold_duration:
                lookback_period = max(min_lookback, threshold_duration + buffer_time)
                if aggregation_window:
                    lookback_period = max(min_lookback, threshold_duration + aggregation_window + buffer_time)
            
            # Calculate since_time based on alert time
            since_time = alert_time - timedelta(seconds=lookback_period)
            
            # Add time window information to condition details
            condition["time_window"] = {
                "since_time": since_time.isoformat(),
                "until_time": alert_time.isoformat(),
                "since_time_ms": int(since_time.timestamp() * 1000),  # Convert to epoch milliseconds
                "until_time_ms": int(alert_time.timestamp() * 1000),  # Convert to epoch milliseconds
                "lookback_period_seconds": lookback_period,
                "alert_created_at": alert_created_at_ms  # Include the original alert creation time
            }
            
            # Extract basic NRQL query
            if "nrql" in condition and "query" in condition["nrql"]:
                condition["query"] = condition["nrql"]["query"]

        return condition
    except Exception as e:
        logger.error(f"Error fetching alert condition: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to retrieve alert condition from New Relic"
        }

@alert_parser_agent.tool
async def get_alert_category(
    ctx: RunContext,
    alert_title: str,
    condition_name: str,
    condition_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Determine the category of the alert based on condition ID, title, or condition name.

    Args:
        alert_title: The title of the alert
        condition_name: The name of the condition
        condition_id: Optional condition ID for exact matching

    Returns:
        Dictionary with category information and runbook
    """
    try:
        # Get the service instance
        category_service = get_alert_category_service()

        # Get category using all available information
        category_info = category_service.get_category(
            alert_title=alert_title,
            condition_name=condition_name,
            condition_id=condition_id
        )

        return category_info
    except Exception as e:
        logger.error(f"Error determining alert category: {str(e)}")
        return {
            "category": "unknown",
            "error": str(e),
            "message": "Failed to determine alert category",
            "source": "error"
        }


async def main():
    """
    Test function for the alert parser agent.
    """
    # Initialize the New Relic client
    api_key = os.environ.get("NEWRELIC_API_KEY")
    account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")

    if not api_key or not account_id:
        raise ValueError("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set in environment variables")

    graphql_client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
    query_client = NewRelicQueryClient(graphql_client)
    logs_client = NewRelicLogsClient(graphql_client)

    # Initialize the entity analyzer
    entity_analyzer = EntityAnalyzer(graphql_client, debug=True)

    # Get alert categories from the service
    category_service = get_alert_category_service()

    # Set up dependencies for the agent
    deps = AlertParserDeps(
        openai_client=openai_client,
        nr_query_client=query_client,
        entity_analyzer=entity_analyzer
    )

    # Test alert (you can use the example alert provided)
    test_alert = {"issueId": "a271ddf7-a9a8-4dc1-beea-39cca4fa1987", "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/a271ddf7-a9a8-4dc1-beea-39cca4fa1987?notifier=WEBHOOK", "title": "agent-management-background-container-service query result is > 10.0 on 'Pod with CrashLoopBackOff -- '", "priority": "CRITICAL", "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"], "impactedEntities": ["agent-management-background-container-service"], "totalIncidents": "1", "state": "ACTIVATED", "trigger": "STATE_CHANGE", "isCorrelated": "false", "createdAt": *************, "updatedAt": *************, "sources": ["newrelic"], "alertPolicyNames": ["Neurons k8s Infra - Critical"], "alertConditionNames": ["Pod with CrashLoopBackOff -- "], "workflowName": "obv-ai-processing-neurons", "chartLink": "https://gorgon.nr-assets.net/image/7cd23d09-c8aa-4e97-928a-d82a5bcfc2c9?config.legend.enabled=false&width=400&height=210", "product": "neurons", "nr_region": "us"}

    # Prepare the user prompt
    user_prompt = f"""
    ```json
    {json.dumps(test_alert, indent=2)}
    ```
    """

    # Run the agent
    result = await alert_parser_agent.run(user_prompt, deps=deps)
    
    # Print the results
    pprint(result.data)
    
    # Print time window information specifically
    print("\nTime Window Information:")
    print(f"Since Time: {result.data.since_time}")
    print(f"Until Time: {result.data.until_time}")
    print(f"Threshold Duration: {result.data.threshold_duration} seconds")
    print(f"Aggregation Window: {result.data.aggregation_window} seconds")


if __name__ == "__main__":
    asyncio.run(main())
