"""
Entity Relationship Agent - Pydantic AI Agent for analyzing entity relationships.

This agent is responsible for discovering related entities, handling missing entities,
and providing a comprehensive view of the entity relationships for incident investigation.
"""

import os
import re
import json
from loguru import logger
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple, cast
from dataclasses import dataclass
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel
from pydantic import BaseModel, Field
from openai import AsyncOpenAI, AsyncAzureOpenAI
import dotenv
import asyncio

from ai_incident_manager.models.workflow_state import (
    IncidentState, EntityRelationshipAgentResponse
)
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.client import NewRelicGraphQLClient

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logger = logger.bind(name="entity_relationship_agent")

# Initialize OpenAI client
required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
azure_openai_enabled = all(var in os.environ for var in required_azure_vars)

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    model = OpenAIModel(os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"), openai_client=openai_client)
else:
    logger.error("Azure OpenAI credentials not found. This application requires Azure OpenAI.")
    logger.error(f"Missing environment variables: {[var for var in required_azure_vars if var not in os.environ]}")
    raise EnvironmentError("Azure OpenAI credentials are required but not found in environment variables.")

@dataclass
class EntityRelationshipDeps:
    """Dependencies for the entity relationship agent."""
    openai_client: AsyncAzureOpenAI
    nr_query_client: NewRelicQueryClient
    state: IncidentState

# Entity Relationship Agent
entity_relationship_agent = Agent(
    model,
    deps_type=EntityRelationshipDeps,
    result_type=EntityRelationshipAgentResponse,
    system_prompt="""You are an AI incident response analyst specializing in entity relationship analysis.
Your task is to discover and map relationships between different entities in an IT infrastructure.

Given information about a primary entity and alert data, you will:
1. Identify the primary entity and its details
2. Discover related entities based on the alert category and entity relationships 
3. Handle cases where the primary entity might be missing
4. Map relationships between entities to build a comprehensive view of the affected system

You need to handle different scenarios:
1. Primary entity provided + entity present: Find standard entity relationships
2. Primary entity provided + entity missing: Find fallback relationships using historical data
3. No primary entity provided: Use alert patterns to find potential related entities

Note: 
- Use missing entity relationships to find fallback entities.
- Use get_entity_relationships to find standard entity relationships.

When analyzing relationships:
- Consider the alert category which gives context about what kinds of relationships to look for
- Use entity_relationship_mapping from the alert category when available
- Look for entities that might be part of the same system or infrastructure
- Consider hierarchical relationships (e.g., container → pod → node → cluster)
- Consider service dependencies (e.g., application → database)

When handling missing entities:
- Look for the most recent information about the entity
- Find entities that were related to it before it went missing
- Consider which related entities might provide context about what happened

Your goal is to build a complete entity relationship graph to assist with incident investigation.
"""
)

@entity_relationship_agent.tool
async def get_entity_relationships(
    ctx: RunContext,
    entity_guid: str,
    entity_type: str,
    entity_name: str,
    alert_category: str,
    cluster_name: Optional[str] = None,
    since_time_ms: Optional[int] = None,
    until_time_ms: Optional[int] = None
) -> Dict[str, Any]:
    """
    Get relationships for an entity based on standard relationship traversal.
    
    Args:
        entity_guid: The GUID of the entity
        entity_type: The type of the entity (e.g., KUBERNETES_POD, KUBERNETES_NODE)
        entity_name: The name of the entity
        alert_category: The category of the alert for context
        cluster_name: Optional cluster name for Kubernetes entities
        since_time_ms: Start time in epoch milliseconds
        until_time_ms: End time in epoch milliseconds
        
    Returns:
        A dictionary containing primary entity and related entities information
    """
    try:
        # Get the entity relationship service
        from ai_incident_manager.services.entity_relationship_service import get_entity_relationship_service
        relationship_service = get_entity_relationship_service()
        
        # Set the query client for the relationship service
        query_client = ctx.deps.nr_query_client
        relationship_service.set_query_client(query_client)
        
        # Use the traverse_relationships method to get related entities
        related_info = relationship_service.traverse_relationships(
            entity_guid=entity_guid,
            entity_type=entity_type,
            entity_name=entity_name,
            alert_category=alert_category,
            cluster_name=cluster_name,
            since_time_ms=since_time_ms,
            until_time_ms=until_time_ms
        )
        
        return related_info
        
    except Exception as e:
        logger.error(f"Error getting entity relationships: {str(e)}")
        return {
            "error": str(e),
            "primary_entity": {
                "guid": entity_guid,
                "type": entity_type,
                "name": entity_name
            },
            "related_entities": []
        }

@entity_relationship_agent.tool
async def get_missing_entity_relationships(
    ctx: RunContext,
    entity_guid: str,
    alert_category: str,
    since_time: Optional[str] = None,
    until_time: Optional[str] = None
) -> Dict[str, Any]:
    """
    Find related entities when the primary entity (e.g., container) is missing.
    
    Args:
        entity_guid: The entity GUID that might be missing
        alert_category: The alert category to determine relationship mapping
        since_time: ISO-formatted start time for looking back in history
        until_time: ISO-formatted end time for looking back in history
        
    Returns:
        A dictionary with primary and related entities information
    """
    try:
        # Get the entity relationship service
        from ai_incident_manager.services.entity_relationship_service import get_entity_relationship_service
        relationship_service = get_entity_relationship_service()
        
        # Set the query client for the relationship service
        query_client = ctx.deps.nr_query_client
        relationship_service.set_query_client(query_client)
        
        # Convert string time to datetime if provided
        since_dt = None
        until_dt = None
        
        if since_time:
            since_dt = datetime.fromisoformat(since_time.replace('Z', '+00:00'))
        
        if until_time:
            until_dt = datetime.fromisoformat(until_time.replace('Z', '+00:00'))
        
        # Use the find_related_entities_from_missing_entity method
        related_info = relationship_service.find_related_entities_from_missing_entity(
            entity_guid=entity_guid,
            alert_category=alert_category,
            since_time=since_dt,
            until_time=until_dt
        )
        
        return related_info
        
    except Exception as e:
        logger.error(f"Error getting missing entity relationships: {str(e)}")
        return {
            "error": str(e),
            "primary_entity": None,
            "related_entities": []
        }

@entity_relationship_agent.tool
async def map_alert_to_entities(
    ctx: RunContext,
    alert_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Map an alert to potentially affected entities when no direct entity GUID is available.
    
    Args:
        alert_data: The alert data including title, condition, etc.
        
    Returns:
        A list of potentially affected entities
    """
    try:
        # Get the entity relationship service
        from ai_incident_manager.services.entity_relationship_service import get_entity_relationship_service
        relationship_service = get_entity_relationship_service()
        
        # Set the query client for the relationship service
        query_client = ctx.deps.nr_query_client
        relationship_service.set_query_client(query_client)
        
        # Use the map_alert_to_entity method
        mapped_entities = relationship_service.map_alert_to_entity(alert_data)
        
        return {
            "mapped_entities": mapped_entities,
            "count": len(mapped_entities)
        }
        
    except Exception as e:
        logger.error(f"Error mapping alert to entities: {str(e)}")
        return {
            "error": str(e),
            "mapped_entities": [],
            "count": 0
        }

# @entity_relationship_agent.tool
# async def get_alert_category_relationship_mapping(
#     ctx: RunContext,
#     alert_category: str
# ) -> Dict[str, Any]:
#     """
#     Get the relationship mapping configuration for a specific alert category.
    
#     Args:
#         alert_category: The alert category to get the mapping for
        
#     Returns:
#         Relationship mapping dictionary or empty dictionary if not found
#     """
#     try:
#         # Get the entity relationship service
#         from ai_incident_manager.services.entity_relationship_service import get_entity_relationship_service
#         relationship_service = get_entity_relationship_service()
        
#         # Call the internal method to get the alert category relationship mapping
#         mapping = relationship_service._get_alert_category_relationship_mapping(alert_category)
        
#         return mapping
        
#     except Exception as e:
#         logger.error(f"Error getting alert category relationship mapping: {str(e)}")
#         return {
#             "error": str(e),
#             "mapping": {}
#         }

@entity_relationship_agent.tool
async def get_entity_details(
    ctx: RunContext,
    entity_guid: str
) -> Dict[str, Any]:
    """
    Get details about an entity from New Relic.
    
    Args:
        entity_guid: The GUID of the entity
        
    Returns:
        Entity details or error information
    """
    try:
        # Use the query client to get entity details
        query_client = ctx.deps.nr_query_client
        
        # Get entity details
        entity_details = query_client.get_entity_details(entity_guid=entity_guid)
        
        if not entity_details:
            return {
                "entity_guid": entity_guid,
                "entity_exists": False,
                "error": "Entity not found"
            }
        
        return {
            "entity_guid": entity_guid,
            "entity_exists": True,
            "entity_details": entity_details
        }
        
    except Exception as e:
        logger.error(f"Error getting entity details: {str(e)}")
        return {
            "entity_guid": entity_guid,
            "entity_exists": False,
            "error": str(e)
        }

async def main():
    """
    Test function for the entity relationship agent.
    """
    # Initialize the New Relic client
    api_key = os.environ.get("NEWRELIC_API_KEY")
    account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")

    if not api_key or not account_id:
        raise ValueError("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set in environment variables")

    graphql_client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
    query_client = NewRelicQueryClient(graphql_client)

    # Sample alert data for testing
    test_alert = {
        "issueId": "a271ddf7-a9a8-4dc1-beea-39cca4fa1987",
        "title": "agent-management-background-container-service query result is > 10.0 on 'Pod with CrashLoopBackOff -- '",
        "priority": "CRITICAL",
        "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtOTAzODY4MDkwNTY4NDkwMDMxMg"],
        "impactedEntities": ["agent-management-background-container-service"],
        "alertPolicyNames": ["Neurons k8s Infra - Critical"],
        "alertConditionNames": ["Pod with CrashLoopBackOff -- "],
        "product": "neurons",
        "nr_region": "us"
    }

    # Create a complete test state with all required fields
    test_state = IncidentState(
        incident_id="test-incident-001",
        raw_alert=test_alert,
        title=test_alert.get("title"),
        description=f"Alert from {test_alert.get('alertPolicyNames', ['Unknown'])[0]}",
        severity=test_alert.get("priority", "CRITICAL"),
        start_time=datetime.now(timezone.utc).isoformat(),  # Required field
        alert_category="kubernetes_crashloopbackoff",
        alert_runbook="Test runbook",  # Required field
        alert_title=test_alert.get("title"),  # Required field
        condition_name=test_alert.get("alertConditionNames", ["Unknown"])[0],  # Required field
        current_phase="entity_relationships",  # Required field
        entities=[{
            "entity_guid": "MTA5MzYyMHxJTkZSQXxOQXwtOTAzODY4MDkwNTY4NDkwMDMxMg",
            "entity_name": "agent-management-background-container-servicer",  # Required field
            "entity_type": "CONTAINER",  # Required field
            "cluster_name": "https://aks-rg-uku-prd-neurons-a398f44f.hcp.uksouth.azmk8s.io:443"  # Add cluster_name information
        }],
        since_time="2023-08-01T00:00:00Z",
        until_time="2023-08-01T01:00:00Z"
    )

    # Set up dependencies for the agent
    deps = EntityRelationshipDeps(
        openai_client=openai_client,
        nr_query_client=query_client,
        state=test_state
    )

    # Test Case 1: Normal entity relationships
    normal_prompt = f"""
    Please analyze the relationships for this entity:
    
    Entity GUID: MTA5MzYyMHxJTkZSQXxOQXwtOTAzODY4MDkwNTY4NDkwMDMxMg
    Entity Type: CONTAINER
    Entity Name: agent-management-background-container-service
    Alert Category: kubernetes_crashloopbackoff
    Cluster Name: https://aks-rg-uku-prd-neurons-a398f44f.hcp.uksouth.azmk8s.io:443
    
    I need to know:
    1. What are the direct relationships to this entity?
    2. What related entities provide important context?
    3. What metrics should we collect from each entity?
    """
    
    # Test Case 2: Missing entity scenario
    missing_prompt = f"""
    Please analyze the relationships for this entity that might be missing:
    
    Entity GUID: MTA5MzYyMHxJTkZSQXxOQXwtOTAzODY4MDkwNTY4NDkwMDMxMg
    Entity Type: CONTAINER
    Entity Name: agent-management-background-container-service
    Alert Category: kubernetes_crashloopbackoff
    Cluster Name: https://aks-rg-uku-prd-neurons-a398f44f.hcp.uksouth.azmk8s.io:443
    
    The entity might be missing because the container has been terminated.
    I need to know:
    1. What fallback entities can we find?
    2. What related entities provide important context?
    3. What metrics should we collect from each entity?
    """
    
    # Choose which test to run
    prompt = normal_prompt  # or missing_prompt to test fallback queries
    
    print(f"Running entity relationship test with prompt:\n{prompt}\n")
    print("-" * 80)
    
    try:
        # Run the agent
        result = await entity_relationship_agent.run(prompt, deps=deps)
        
        # Print the results
        print("\nEntity Relationship Analysis Results:")
        print(f"Primary Entities: {len(result.data.primary_entities)}")
        print(f"Related Entities: {len(result.data.related_entities)}")
        print(f"Total Relationships: {len(result.data.relationships)}")
        print(f"Fallback Strategy: {result.data.entity_missing_strategy or 'None'}")
        print(f"Alert Based Discovery: {result.data.alert_based_discovery}")
        print(f"Total Entity Count: {result.data.total_entity_count}")
        
        # Print primary entities
        print("\nPrimary Entities:")
        for entity in result.data.primary_entities:
            print(f"- {entity.entity_type}: {entity.entity_name} (Missing: {entity.entity_missing})")
            
        # Print related entities
        print("\nKey Related Entities:")
        for i, entity in enumerate(result.data.related_entities):
            print(f"- {entity.entity_type}: {entity.entity_name}")
            # Show only first 5 to avoid too much output
            if i >= 4 and len(result.data.related_entities) > 5:
                remaining = len(result.data.related_entities) - 5
                print(f"... and {remaining} more related entities")
                break
            
        # Print relationships
        print("\nKey Relationships:")
        for i, rel in enumerate(result.data.relationships):
            # Extract entity type and name from the target_guid if it contains ::
            if "::" in rel.target_guid:
                target_type, target_name = rel.target_guid.split("::", 1)
                print(f"- {rel.source_guid} -> {rel.relationship_type} -> {target_type}::{target_name}")
            else:
                print(f"- {rel.source_guid} -> {rel.relationship_type} -> {rel.target_guid}")
            # Show only first 5 to avoid too much output
            if i >= 4 and len(result.data.relationships) > 5:
                remaining = len(result.data.relationships) - 5
                print(f"... and {remaining} more relationships")
                break
        
        # Print raw output
        print("\nRaw Output:")
        print(result.data.model_dump_json(indent=2))
            
    except Exception as e:
        print(f"Error running entity relationship agent: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())