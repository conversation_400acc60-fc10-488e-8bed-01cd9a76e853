"""
Root Cause Analysis Agent - Pydantic AI Agent for determining incident root causes.

This agent analyzes all collected information from an incident to determine
the most likely root cause, contributing factors, and recommendations.
"""

import os
import re
import json
from loguru import logger
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass, field
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel

import dotenv
from openai import AsyncOpenAI, AsyncAzureOpenAI

from ai_incident_manager.models.workflow_state import (
    RCAAgentResponse, RCAPrioritizedFactor, IncidentState
)

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logger = logger.bind(name="rca_agent")

# Initialize OpenAI client
required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
azure_openai_enabled = all(var in os.environ for var in required_azure_vars)

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment='o1',
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    model = OpenAIModel('o1', openai_client=openai_client)
else:
    logger.error("Azure OpenAI credentials not found. This application requires Azure OpenAI.")
    logger.error(f"Missing environment variables: {[var for var in required_azure_vars if var not in os.environ]}")
    raise EnvironmentError("Azure OpenAI credentials are required but not found in environment variables.")

@dataclass
class RCAAgentDeps:
    """Dependencies for the root cause analysis agent."""
    openai_client: AsyncAzureOpenAI
    state: Any = None  # Complete IncidentState to access all collected data
    nr_query_client: Any = None
    logs_client: Any = None
    metrics_collector: Any = None

    def safe_get_state(self, key: str, default: Any = None) -> Any:
        """
        Safely get a value from state whether it's a dictionary or a Pydantic model.
        
        Args:
            key: The key or attribute name to access
            default: Default value to return if key not found
            
        Returns:
            The value from state or the default
        """
        if not self.state:
            return default
            
        # If state is a dictionary, use get method
        if isinstance(self.state, dict):
            return self.state.get(key, default)
            
        # If state is a Pydantic model or other object, use getattr
        if hasattr(self.state, key):
            return getattr(self.state, key)
            
        return default

# Root Cause Analysis Agent with system prompt
rca_agent = Agent(
    model,
    deps_type=RCAAgentDeps,
    result_type=RCAAgentResponse,
    retries=3,
    system_prompt="""
You are an expert Root Cause Analysis specialist for cloud and Kubernetes environments.
Your task is to analyze all collected information about an incident and determine the most likely root cause.

Guidelines for Root Cause Analysis:
1. Analyze all available data including alert details, entity information, metrics, logs, and runbook results
2. Look for patterns and correlations across different data sources
3. Identify the primary root cause and any contributing factors
4. Assign confidence levels based on the quality and quantity of evidence
5. Reconstruct a timeline of events leading to the incident
6. Consider both technical factors (infrastructure, code) and operational factors (processes, configuration)
7. Identify affected components and potential impact scope
8. Be transparent about uncertainty when evidence is limited or contradictory

When analyzing the evidence:
- Prioritize strong temporal correlations (events that occurred just before the incident)
- Look for anomalies in metrics that indicate resource constraints or performance issues
- Examine log patterns for error messages, warnings, or unusual behaviors
- Consider infrastructure changes, deployments, or configuration updates that might have triggered the issue
- Analyze entity relationships to identify potential cascade effects
- Extract insights from runbook execution results
- Consider common failure patterns in Kubernetes environments (pod crashes, scheduling issues, resource contention)

KUBERNETES-SPECIFIC ANALYSIS PATTERNS:
1. Pod Crashing or Restarting Issues:
   - Look for OOMKilled events (Out of Memory)
   - Check for container init failures
   - Examine liveness/readiness probe failures
   - Analyze application error logs right before crashes
   - Check for dependent service connectivity issues

2. Deployment/Scaling Issues:
   - Examine resource constraints on nodes
   - Check for scheduling issues (node selectors, taints, affinity rules)
   - Look for PersistentVolume/PVC issues
   - Analyze horizontal pod autoscaler events

3. Performance Issues:
   - Check for CPU/Memory pressure
   - Look for network latency or throughput problems
   - Analyze IO bottlenecks
   - Check for too many pods on a node (resource contention)
   - Examine service mesh/network policy issues

4. Cluster-Level Issues:
   - Check control plane component health
   - Look for API server bottlenecks
   - Analyze etcd performance
   - Examine DNS resolution problems
   - Check node conditions (Ready, MemoryPressure, DiskPressure)

When creating your analysis:
- Be specific about which evidence supports your conclusions
- Assign confidence levels for each identified factor
- Link affected components to specific entities in the incident
- Provide clear, actionable recommendations
- Identify areas of uncertainty that require further investigation
- Please provide a list of remediation actions that can be taken to resolve the incident.
"""
)

@rca_agent.tool
async def get_entity_details(
    ctx: RunContext,
    entity_guid: str
) -> Dict[str, Any]:
    """
    Get detailed information about an entity.
    
    Args:
        entity_guid: The GUID of the entity to retrieve
        
    Returns:
        Dictionary containing entity details
    """
    try:
        # Find the entity in the state
        state = ctx.deps.state
        entities = ctx.deps.safe_get_state("entities", [])
        
        # Try to find the entity in the list
        for entity in entities:
            if isinstance(entity, dict) and entity.get("entity_guid") == entity_guid:
                return entity
            elif hasattr(entity, "entity_guid") and entity.entity_guid == entity_guid:
                # If entity is a Pydantic model, convert to dict
                if hasattr(entity, "model_dump"):
                    return entity.model_dump()
                else:
                    # Convert to dict manually
                    return {k: v for k, v in entity.__dict__.items() if not k.startswith("_")}
                
        # If not found, return an error
        return {
            "error": f"Entity with GUID {entity_guid} not found in state",
            "entity_guid": entity_guid
        }
        
    except Exception as e:
        logger.error(f"Error getting entity details: {str(e)}")
        return {
            "error": str(e),
            "entity_guid": entity_guid
        }

@rca_agent.tool
async def get_metrics_by_incident_id(ctx: RunContext) -> Dict[str, Any]:
    """
    Retrieve all metrics collected for this incident from MongoDB.
    
    Returns:
        Dictionary containing all metrics data
    """
    try:
        # Import MongoDB service
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Get incident ID from state
        incident_id = ctx.deps.safe_get_state("incident_id")
        
        if not incident_id:
            return {
                "error": "No incident ID found in state",
                "metrics": []
            }
            
        # Get all metrics for this incident
        metrics = await mongodb_service.get_metrics_by_incident_id(incident_id)
        
        return {
            "metrics_count": len(metrics),
            "metrics": metrics
        }
        
    except Exception as e:
        logger.error(f"Error retrieving metrics: {str(e)}")
        return {
            "error": str(e),
            "metrics": []
        }

@rca_agent.tool
async def get_logs_by_incident_id(ctx: RunContext) -> Dict[str, Any]:
    """
    Retrieve all logs collected for this incident from MongoDB.
    
    Returns:
        Dictionary containing all logs data
    """
    try:
        # Import MongoDB service
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Get incident ID from state
        incident_id = ctx.deps.safe_get_state("incident_id")
        
        if not incident_id:
            return {
                "error": "No incident ID found in state",
                "logs": []
            }
            
        # Get all logs for this incident
        logs = await mongodb_service.get_logs_by_incident_id(incident_id)
        
        return {
            "logs_count": len(logs),
            "logs": logs
        }
        
    except Exception as e:
        logger.error(f"Error retrieving logs: {str(e)}")
        return {
            "error": str(e),
            "logs": []
        }

@rca_agent.tool
async def get_events_by_incident_id(ctx: RunContext) -> Dict[str, Any]:
    """
    Retrieve all events collected for this incident from MongoDB.
    
    Returns:
        Dictionary containing all events data
    """
    try:
        # Import MongoDB service
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Get incident ID from state
        incident_id = ctx.deps.safe_get_state("incident_id")
        
        if not incident_id:
            return {
                "error": "No incident ID found in state",
                "events": []
            }
            
        # Get all events for this incident
        events = await mongodb_service.get_events_by_incident_id(incident_id)
        
        return {
            "events_count": len(events),
            "events": events
        }
        
    except Exception as e:
        logger.error(f"Error retrieving events: {str(e)}")
        return {
            "error": str(e),
            "events": []
        }

@rca_agent.tool
async def get_tool_results_by_incident_id(ctx: RunContext) -> Dict[str, Any]:
    """
    Retrieve all tool execution results for this incident from MongoDB.
    
    Returns:
        Dictionary containing all tool results
    """
    try:
        # Import MongoDB service
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Get incident ID from state
        incident_id = ctx.deps.safe_get_state("incident_id")
        
        if not incident_id:
            return {
                "error": "No incident ID found in state",
                "tool_results": []
            }
            
        # Get all tool results for this incident
        tool_results = await mongodb_service.get_tool_results_by_incident_id(incident_id)
        
        # Categorize tool results by type for easier analysis
        categorized_results = {}
        for result in tool_results:
            tool_name = result.get("tool_name", "unknown")
            if tool_name not in categorized_results:
                categorized_results[tool_name] = []
            categorized_results[tool_name].append(result)
        
        return {
            "tool_results_count": len(tool_results),
            "categorized_results": categorized_results,
            "tool_results": tool_results
        }
        
    except Exception as e:
        logger.error(f"Error retrieving tool results: {str(e)}")
        return {
            "error": str(e),
            "tool_results": []
        }

@rca_agent.tool
async def get_runbook_steps(ctx: RunContext) -> Dict[str, Any]:
    """
    Extract runbook steps and their results from the state.
    
    Returns:
        Dictionary containing runbook execution details
    """
    try:
        # Get runbook results from state
        runbook_results = ctx.deps.safe_get_state("runbook_results", [])
        
        # Check if we have results in the right format (list of objects)
        formatted_results = []
        
        for step in runbook_results:
            # Handle both Pydantic models and dictionaries
            if hasattr(step, "model_dump"):
                formatted_step = step.model_dump()
            elif isinstance(step, dict):
                formatted_step = step
            else:
                # Try to convert to dict directly
                formatted_step = {k: v for k, v in step.__dict__.items() if not k.startswith("_")}
                
            formatted_results.append(formatted_step)
        
        # Get other runbook details
        runbook_id = ctx.deps.safe_get_state("runbook_id", "unknown")
        runbook_name = ctx.deps.safe_get_state("runbook_name", "unknown")
        runbook_summary = ctx.deps.safe_get_state("runbook_summary", "")
        
        return {
            "runbook_id": runbook_id,
            "runbook_name": runbook_name,
            "runbook_summary": runbook_summary,
            "steps_count": len(formatted_results),
            "steps": formatted_results
        }
        
    except Exception as e:
        logger.error(f"Error extracting runbook steps: {str(e)}")
        return {
            "error": str(e),
            "steps": []
        }

@rca_agent.tool
async def analyze_entity_relationships(ctx: RunContext) -> Dict[str, Any]:
    """
    Analyze entity relationships to identify potential cascade effects and dependencies.
    
    Returns:
        Dictionary containing relationship analysis
    """
    try:
        # Get entity relationships from state
        relationships = ctx.deps.safe_get_state("entity_relationships", [])
        entities = ctx.deps.safe_get_state("entities", [])
        
        # Format relationships for analysis
        formatted_relationships = []
        
        for rel in relationships:
            # Handle both Pydantic models and dictionaries
            if hasattr(rel, "model_dump"):
                formatted_rel = rel.model_dump()
            elif isinstance(rel, dict):
                formatted_rel = rel
            else:
                # Try to convert to dict directly
                formatted_rel = {k: v for k, v in rel.__dict__.items() if not k.startswith("_")}
                
            formatted_relationships.append(formatted_rel)
        
        # Create a mapping of entity GUIDs to names for easier reference
        entity_map = {}
        for entity in entities:
            if hasattr(entity, "entity_guid") and hasattr(entity, "entity_name"):
                entity_map[entity.entity_guid] = entity.entity_name
            elif isinstance(entity, dict) and "entity_guid" in entity and "entity_name" in entity:
                entity_map[entity["entity_guid"]] = entity["entity_name"]
        
        # Enhance relationships with entity names
        for rel in formatted_relationships:
            source_guid = rel.get("source_guid")
            target_guid = rel.get("target_guid")
            
            if source_guid in entity_map:
                rel["source_name"] = entity_map[source_guid]
            
            if target_guid in entity_map:
                rel["target_name"] = entity_map[target_guid]
        
        # Return enhanced relationships
        return {
            "relationships_count": len(formatted_relationships),
            "relationships": formatted_relationships,
            "entity_map": entity_map
        }
        
    except Exception as e:
        logger.error(f"Error analyzing entity relationships: {str(e)}")
        return {
            "error": str(e),
            "relationships": []
        }

async def main():
    """
    Test function for the RCA agent.
    """
    # Initialize dependencies
    logger.info("Initializing dependencies")
    
    # Load environment variables
    dotenv.load_dotenv()
    
    # Initialize OpenAI client
    azure_openai_enabled = "AZURE_OPENAI_ENDPOINT" in os.environ and "AZURE_OPENAI_API_KEY" in os.environ
    
    if azure_openai_enabled:
        openai_client = AsyncAzureOpenAI(
            azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
            azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
            api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
            api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
        )
    else:
        logger.error("Azure OpenAI credentials not found")
        return
    
    # Sample incident state with dummy data
    sample_state = {
        "incident_id": "test-incident-001",
        "title": "Pod with CrashLoopBackOff",
        "alert_category": "kubernetes_crashloopbackoff",
        "entities": [
            {
                "entity_guid": "test-entity-001",
                "entity_name": "test-pod",
                "entity_type": "KUBERNETES_POD",
                "is_primary": True
            }
        ],
        "runbook_results": [
            {
                "step_title": "Check Pod Logs",
                "summary": "Found Java OutOfMemoryError in container logs",
                "issues_found": True
            }
        ]
    }
    
    # Set up dependencies for the agent
    deps = RCAAgentDeps(
        openai_client=openai_client,
        state=sample_state
    )
    
    # Test prompt
    test_prompt = """
    Please analyze the following incident:
    
    Incident ID: test-incident-001
    Title: Pod with CrashLoopBackOff
    
    The primary entity is a Kubernetes pod that is in CrashLoopBackOff state.
    The logs show Java OutOfMemoryError exceptions.
    Memory metrics show increasing usage before the crash.
    
    Determine the root cause of this incident.
    """
    
    # Run the agent
    try:
        logger.info("Running RCA agent")
        result = await rca_agent.run(test_prompt, deps=deps)
        logger.info(f"RCA agent result: {result.data}")
    except Exception as e:
        logger.error(f"Error running RCA agent: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main()) 