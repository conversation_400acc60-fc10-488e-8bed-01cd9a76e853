"""
Runbook Agent - Pydantic AI Agent for executing runbooks.

This agent is responsible for executing runbooks to investigate and remediate incidents.
The agent is responsible for:
1. Selecting appropriate runbooks based on alert category and entity types
2. Executing runbook steps using specialized tools
3. Collecting and analyzing results from each step
4. Providing recommendations and insights based on the runbook execution
"""

import os
import re
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel

import dotenv
from openai import AsyncOpenAI, AsyncAzureOpenAI

from ai_incident_manager.services.runbook_service import get_runbook_service
from ai_incident_manager.services.metrics_collector import MetricsCollector
from lib.new_relic.analyzer import EntityAnalyzer
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.logs import NewRelicLogsClient

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize OpenAI client
required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
azure_openai_enabled = all(var in os.environ for var in required_azure_vars)

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    model = OpenAIModel(os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"), openai_client=openai_client)
else:
    logger.error("Azure OpenAI credentials not found. This application requires Azure OpenAI.")
    logger.error(f"Missing environment variables: {[var for var in required_azure_vars if var not in os.environ]}")
    raise EnvironmentError("Azure OpenAI credentials are required but not found in environment variables.")


def get_runbook_service():
    """
    Get the singleton instance of RunbookService.
    
    Returns:
        RunbookService: The singleton instance
    """
    from ai_incident_manager.services.runbook_service import RunbookService
    return RunbookService()


@dataclass
class RunbookAgentDeps:
    """Dependencies for the runbook agent."""
    openai_client: AsyncAzureOpenAI
    metrics_collector: MetricsCollector
    state: Dict[str, Any] = None
    
    def get_time_window(self) -> Dict[str, Any]:
        """
        Get time window information from the state.
        
        Returns:
            Dict containing time window information with both datetime 
            objects and millisecond timestamps
        """
        if not self.state:
            return {}
            
        time_window = {}
        
        # Check for since_time and until_time directly in state
        if "since_time" in self.state and "until_time" in self.state:
            # Ensure datetime objects for since_time and until_time
            if isinstance(self.state["since_time"], str):
                try:
                    time_window["since_time"] = datetime.fromisoformat(self.state["since_time"].replace('Z', '+00:00'))
                except ValueError:
                    # Handle other string formats if needed
                    logger.warning(f"Could not parse since_time: {self.state['since_time']}")
                    time_window["since_time"] = self.state["since_time"]
            else:
                time_window["since_time"] = self.state["since_time"]
                
            if isinstance(self.state["until_time"], str):
                try:
                    time_window["until_time"] = datetime.fromisoformat(self.state["until_time"].replace('Z', '+00:00'))
                except ValueError:
                    # Handle other string formats if needed
                    logger.warning(f"Could not parse until_time: {self.state['until_time']}")
                    time_window["until_time"] = self.state["until_time"]
            else:
                time_window["until_time"] = self.state["until_time"]
            
            # Add millisecond formats if not present
            if "since_time_ms" not in self.state and isinstance(time_window["since_time"], datetime):
                time_window["since_time_ms"] = int(time_window["since_time"].timestamp() * 1000)
                
            if "until_time_ms" not in self.state and isinstance(time_window["until_time"], datetime):
                time_window["until_time_ms"] = int(time_window["until_time"].timestamp() * 1000)
            
        # Check for epoch millisecond versions
        elif "since_time_ms" in self.state and "until_time_ms" in self.state:
            time_window["since_time_ms"] = self.state["since_time_ms"]
            time_window["until_time_ms"] = self.state["until_time_ms"]
            
            # Convert to datetime objects
            time_window["since_time"] = datetime.fromtimestamp(
                self.state["since_time_ms"] / 1000, tz=timezone.utc
            )
                
            time_window["until_time"] = datetime.fromtimestamp(
                self.state["until_time_ms"] / 1000, tz=timezone.utc
            )
        
        return time_window


class RunbookStepResult(BaseModel):
    """Result of executing a runbook step."""
    step_title: str
    step_description: str
    tool: str
    parameters: Dict[str, Any]
    result: Dict[str, Any]
    summary: str
    issues_found: bool = False
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class RunbookExecutionResult(BaseModel):
    """Result of executing a runbook."""
    runbook_id: str
    runbook_name: str
    runbook_description: str
    entity_guid: Optional[str] = None
    entity_name: Optional[str] = None
    entity_type: Optional[str] = None
    cluster_id: Optional[str] = None
    execution_time: datetime = Field(default_factory=datetime.utcnow)
    step_results: List[RunbookStepResult] = Field(default_factory=list)
    issues_found: bool = False
    summary: str = ""
    recommendations: List[str] = Field(default_factory=list)


# Runbook Agent with result_type and system message
runbook_agent = Agent(
    model,
    deps_type=RunbookAgentDeps,
    result_type=RunbookExecutionResult,
    system_prompt="""
You are an expert runbook executor specialized in investigating and remediating cloud and Kubernetes incidents.
Your task is to execute runbooks for various incident types, collect relevant data, and provide insights.

When executing runbooks:
1. Follow each step methodically and interpret the results
2. Look for patterns and anomalies in metrics, logs, and other data
3. Summarize findings clearly and concisely
4. Highlight any issues found and recommend next steps
5. When appropriate, provide specific remediation suggestions

You have access to a variety of tools that can collect metrics, logs, and other information.

The user will either:
1. Specify a runbook ID to execute (e.g., "Execute runbook kubernetes_pod_investigation for entity X")
2. Ask for recommendations based on an alert category and entity type (e.g., "What runbooks should I use for pod crashes?")

When executing a runbook, you'll need to:
- Get the runbook details using get_runbook_details
- Execute each step in the runbook in order
- Analyze the results and provide recommendations

When recommending runbooks, you'll need to:
- Use get_recommended_runbooks to find appropriate runbooks
- Summarize the recommendations

Remember to always try to extract the entity ID and runbook ID from the user's request.
"""
)


@runbook_agent.tool
async def get_runbook_details(
    ctx: RunContext,
    runbook_id: str
) -> Dict[str, Any]:
    """
    Get details about a specific runbook.
    
    Args:
        runbook_id: ID of the runbook to retrieve
        
    Returns:
        Runbook details including steps and prerequisites
    """
    runbook_service = get_runbook_service()
    runbook = runbook_service.get_runbook(runbook_id)
    
    if not runbook:
        return {
            "error": f"Runbook not found: {runbook_id}",
            "available_runbooks": [rb["id"] for rb in runbook_service.get_all_runbooks()]
        }
    
    return runbook


@runbook_agent.tool
async def get_recommended_runbooks(
    ctx: RunContext,
    alert_category: str,
    entity_types: List[str],
    max_results: int = 3
) -> Dict[str, Any]:
    """
    Get recommended runbooks based on alert category and entity types.
    
    Args:
        alert_category: Alert category (e.g., kubernetes_crashloopbackoff, kubernetes_evicted_pod)
        entity_types: List of entity types (e.g., KUBERNETES_POD, K8S_NODE)
        max_results: Maximum number of results to return
        
    Returns:
        List of recommended runbooks
    """
    runbook_service = get_runbook_service()
    recommended_runbooks = runbook_service.get_recommended_runbooks(
        alert_category=alert_category,
        entity_types=entity_types,
        max_results=max_results
    )
    
    return {
        "recommended_runbooks": recommended_runbooks,
        "count": len(recommended_runbooks)
    }


@runbook_agent.tool
async def get_pod_logs(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 30,
    limit: int = 100
) -> Dict[str, Any]:
    """
    Get the logs from a Kubernetes pod.
    
    Args:
        entity_guid: New Relic entity GUID for the pod
        time_window_minutes: Number of minutes of logs to retrieve
        limit: Maximum number of log lines to return
        
    Returns:
        Pod logs and metadata
    """
    try:
        # Get entity details
        metrics_collector = ctx.deps.metrics_collector
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            logger.error(f"Entity not found: {entity_guid}")
            return {
                "error": f"Entity not found: {entity_guid}",
                "logs": []
            }
        
        # Extract pod name and cluster
        pod_name = entity_details.get("name")
        
        if not pod_name:
            logger.error(f"Pod name not found in entity details for {entity_guid}")
            return {
                "error": f"Pod name not found in entity details",
                "logs": []
            }
            
        cluster_name = None
        tags = entity_details.get("tags", [])
        
        if isinstance(tags, list):
            for tag in tags:
                if isinstance(tag, dict) and tag.get("key") == "clusterName":
                    values = tag.get("values", [])
                    if isinstance(values, list) and values:
                        cluster_name = values[0]
        
        if not cluster_name:
            logger.warning(f"Cluster name not found for pod {pod_name} ({entity_guid})")
        
        # Get pod namespace
        namespace = None
        for tag in entity_details.get("tags", []):
            if isinstance(tag, dict) and tag.get("key") == "namespace":
                values = tag.get("values", [])
                if isinstance(values, list) and values:
                    namespace = values[0]
                    break
        
        if not namespace:
            logger.warning(f"Namespace not found for pod {pod_name}, using 'default'")
            namespace = "default"
            
        # Calculate time window
        current_time = datetime.utcnow()
        since_time = current_time - timedelta(minutes=time_window_minutes)
        
        # Fetch logs from the metrics collector
        try:
            logs_data = await metrics_collector.get_entity_logs(
                entity_guid=entity_guid,
                since_time=since_time,
                until_time=current_time,
                limit=limit
            )
            
            logs = logs_data.get("logs", [])
            
            # Format the logs
            log_lines = []
            for log in logs:
                timestamp = log.get("timestamp", "")
                message = log.get("message", "")
                level = log.get("level", "INFO")
                
                log_lines.append({
                    "timestamp": timestamp,
                    "level": level,
                    "message": message
                })
                
            return {
                "pod_name": pod_name,
                "namespace": namespace,
                "cluster": cluster_name,
                "time_window_minutes": time_window_minutes,
                "log_count": len(log_lines),
                "logs": log_lines
            }
            
        except Exception as e:
            logger.error(f"Error retrieving logs for pod {pod_name}: {str(e)}")
            
            # Try to get logs using the Kubernetes API as a fallback
            try:
                logger.info(f"Attempting to get logs via Kubernetes API for {pod_name}")
                k8s_client = get_kubernetes_client(cluster_name)
                
                if not k8s_client:
                    logger.error(f"Could not get Kubernetes client for cluster: {cluster_name}")
                    return {
                        "error": f"Failed to get logs: {str(e)}, and Kubernetes client not available for cluster: {cluster_name or 'unknown'}",
                        "pod_name": pod_name,
                        "namespace": namespace,
                        "cluster": cluster_name,
                        "logs": []
                    }
                
                # Get pod details to check for containers
                pod = await k8s_client.read_namespaced_pod(pod_name, namespace)
                
                if not pod:
                    logger.error(f"Pod {pod_name} not found in namespace {namespace}")
                    return {
                        "error": f"Pod {pod_name} not found in namespace {namespace}",
                        "logs": []
                    }
                
                # Get logs from the first container (or specified container)
                container_name = pod.spec.containers[0].name if pod.spec.containers else None
                
                if not container_name:
                    logger.error(f"No containers found in pod {pod_name}")
                    return {
                        "error": f"No containers found in pod {pod_name}",
                        "logs": []
                    }
                
                # Get logs
                logs = await k8s_client.read_namespaced_pod_log(
                    name=pod_name,
                    namespace=namespace,
                    container=container_name,
                    tail_lines=limit
                )
                
                # Split into lines
                log_lines = []
                for line in logs.split("\n"):
                    if line.strip():
                        log_lines.append({
                            "timestamp": "",  # Timestamp may not be available
                            "level": "INFO",  # Level may not be available
                            "message": line.strip()
                        })
                
                return {
                    "pod_name": pod_name,
                    "namespace": namespace,
                    "cluster": cluster_name,
                    "container": container_name,
                    "fallback_method": "kubernetes_api",
                    "log_count": len(log_lines),
                    "logs": log_lines
                }
                
            except Exception as k8s_error:
                logger.error(f"Fallback to Kubernetes API also failed: {str(k8s_error)}")
                return {
                    "error": f"Failed to get logs: {str(e)}. Fallback to Kubernetes API also failed: {str(k8s_error)}",
                    "pod_name": pod_name,
                    "namespace": namespace,
                    "cluster": cluster_name,
                    "logs": []
                }
        
    except Exception as e:
        logger.error(f"Error getting pod logs: {str(e)}")
        logger.exception("Exception details:")
        return {
            "error": f"Error getting pod logs: {str(e)}",
            "entity_guid": entity_guid,
            "logs": []
        }


@runbook_agent.tool
async def get_kubernetes_description(
    ctx: RunContext,
    entity_guid: str
) -> Dict[str, Any]:
    """
    Get detailed description of a Kubernetes entity similar to 'kubectl describe'.
    
    This tool retrieves a detailed description of Kubernetes entities including:
    - Pods
    - Nodes
    - Deployments
    - Services
    - DaemonSets
    - CronJobs
    - Jobs
    - and other Kubernetes resources
    
    Args:
        entity_guid: New Relic entity GUID for the Kubernetes resource
        
    Returns:
        Detailed entity description and metadata
    """
    try:
        # Get metrics collector from context
        metrics_collector = ctx.deps.metrics_collector
        
        # Prepare time window
        time_window = ctx.deps.get_time_window()
        
        # Use timestamps from state if available
        since_time = None
        until_time = None
        
        if "since_time" in time_window:
            try:
                since_time = datetime.fromisoformat(time_window["since_time"].replace("Z", "+00:00"))
            except Exception as e:
                logger.warning(f"Failed to parse since_time: {e}")
        
        if "until_time" in time_window:
            try:
                until_time = datetime.fromisoformat(time_window["until_time"].replace("Z", "+00:00"))
            except Exception as e:
                logger.warning(f"Failed to parse until_time: {e}")
        
        # Verify we have both since_time and until_time
        if not since_time or not until_time:
            logger.error("Missing required time window information in state")
            return {
                "error": "Missing required time window information. Alert time window must be provided in state.",
                "entity_guid": entity_guid
            }
        
        # Call the metrics collector to get the Kubernetes description
        result = metrics_collector.get_kubernetes_entity_description(
            entity_guid=entity_guid,
            since_time=since_time,
            until_time=until_time
        )
        
        # Process the result for better readability
        if "description" in result and result["description"]:
            # Extract key information for summary
            entity_type = result.get("entity_type", "Unknown")
            entity_name = result.get("entity_name", "Unknown")
            
            # Add a summary based on the entity type
            if entity_type == "Pod":
                # Extract status and container info for pods
                status = "Unknown"
                containers_status = []
                
                lines = result["description"].split('\n')
                for i, line in enumerate(lines):
                    if line.startswith("Status:"):
                        status = line.split(':', 1)[1].strip()
                    
                    # Look for container statuses
                    if "State:" in line and i > 0 and "Container ID:" in lines[i-1]:
                        container_name = lines[i-2].strip().rstrip(':')
                        state = line.split(':', 1)[1].strip()
                        containers_status.append(f"{container_name}: {state}")
                
                result["summary"] = {
                    "entity_type": "Pod",
                    "name": entity_name,
                    "status": status,
                    "containers": containers_status
                }
            
            elif entity_type == "Node":
                # Extract node conditions and resource info
                conditions = []
                resources = {}
                
                lines = result["description"].split('\n')
                in_conditions_section = False
                
                for line in lines:
                    if line.startswith("Conditions:"):
                        in_conditions_section = True
                        continue
                    
                    if in_conditions_section and line.strip() and not line.startswith(" "):
                        in_conditions_section = False
                    
                    if in_conditions_section and ":" in line:
                        conditions.append(line.strip())
                    
                    # Extract resource info
                    if "cpu:" in line or "memory:" in line:
                        parts = line.split(':', 1)
                        if len(parts) == 2:
                            key = parts[0].strip()
                            value = parts[1].strip()
                            resources[key] = value
                
                result["summary"] = {
                    "entity_type": "Node",
                    "name": entity_name,
                    "conditions": conditions,
                    "resources": resources
                }
            
            elif entity_type in ["Deployment", "DaemonSet", "ReplicaSet"]:
                # Extract deployment status
                replicas = "Unknown"
                strategy = "Unknown"
                
                lines = result["description"].split('\n')
                for line in lines:
                    if "Replicas:" in line:
                        replicas = line.split(':', 1)[1].strip()
                    if "Strategy:" in line:
                        strategy = line.split(':', 1)[1].strip()
                
                result["summary"] = {
                    "entity_type": entity_type,
                    "name": entity_name,
                    "replicas": replicas,
                    "strategy": strategy
                }
        
        return result
            
    except Exception as e:
        logger.error(f"Error getting Kubernetes description for {entity_guid}: {str(e)}")
        return {
            "error": f"Failed to get Kubernetes description: {str(e)}",
            "entity_guid": entity_guid
        }


@runbook_agent.tool
async def get_pod_status(
    ctx: RunContext,
    entity_guid: str,
    include_events: bool = True
) -> Dict[str, Any]:
    """
    Get the current status of a Kubernetes pod.
    
    Args:
        entity_guid: New Relic entity GUID for the pod
        include_events: Whether to include pod events
        
    Returns:
        Pod status information
    """
    try:
        # Get entity details
        metrics_collector = ctx.deps.metrics_collector
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            logger.error(f"Entity not found: {entity_guid}")
            return {
                "error": f"Entity not found: {entity_guid}",
                "status": "unknown"
            }
        
        # Extract pod name and cluster
        pod_name = entity_details.get("name")
        
        if not pod_name:
            logger.error(f"Pod name not found in entity details for {entity_guid}")
            return {
                "error": f"Pod name not found in entity details",
                "status": "unknown"
            }
            
        cluster_name = None
        tags = entity_details.get("tags", [])
        
        if isinstance(tags, list):
            for tag in tags:
                if isinstance(tag, dict) and tag.get("key") == "clusterName":
                    values = tag.get("values", [])
                    if isinstance(values, list) and values:
                        cluster_name = values[0]
                elif isinstance(tag, dict) and tag.get("key") == "k8s.clusterName":
                    values = tag.get("values", [])
                    if isinstance(values, list) and values:
                        cluster_name = values[0]
                elif isinstance(tag, dict) and tag.get("key") == "namespace":
                    values = tag.get("values", [])
                    if isinstance(values, list) and values:
                        namespace = values[0]
                elif isinstance(tag, dict) and tag.get("key") == "k8s.namespace.name":
                    values = tag.get("values", [])
                    if isinstance(values, list) and values:
                        namespace = values[0]
                    
        if not cluster_name:
            logger.warning(f"Cluster name not found for pod {pod_name} ({entity_guid})")
        
        # Get pod status from the Kubernetes API
        k8s_client = get_kubernetes_client(cluster_name)
        
        if not k8s_client:
            logger.error(f"Could not get Kubernetes client for cluster: {cluster_name}")
            return {
                "error": f"Kubernetes client not available for cluster: {cluster_name or 'unknown'}",
                "pod_name": pod_name,
                "cluster": cluster_name,
                "status": "unknown"
            }
        
        # Get pod details
        pod = await k8s_client.read_namespaced_pod(pod_name, namespace)
        
        if not pod:
            logger.error(f"Pod {pod_name} not found in namespace {namespace}")
            return {
                "error": f"Pod {pod_name} not found in namespace {namespace}",
                "pod_name": pod_name,
                "namespace": namespace,
                "cluster": cluster_name,
                "status": "unknown"
            }
            
        # Extract status
        status = {
            "pod_name": pod_name,
            "namespace": namespace,
            "cluster": cluster_name,
            "phase": pod.status.phase,
            "conditions": [],
            "container_statuses": []
        }
        
        # Add conditions
        if pod.status.conditions:
            for condition in pod.status.conditions:
                status["conditions"].append({
                    "type": condition.type,
                    "status": condition.status,
                    "reason": condition.reason,
                    "message": condition.message,
                    "last_transition_time": condition.last_transition_time.isoformat() if condition.last_transition_time else None
                })
                
        # Add container statuses
        if pod.status.container_statuses:
            for container_status in pod.status.container_statuses:
                container_info = {
                    "name": container_status.name,
                    "ready": container_status.ready,
                    "restart_count": container_status.restart_count,
                    "image": container_status.image
                }
                
                # Add state info
                state = {}
                
                if container_status.state.running:
                    state["state"] = "running"
                    state["started_at"] = container_status.state.running.started_at.isoformat() if container_status.state.running.started_at else None
                elif container_status.state.waiting:
                    state["state"] = "waiting"
                    state["reason"] = container_status.state.waiting.reason
                    state["message"] = container_status.state.waiting.message
                elif container_status.state.terminated:
                    state["state"] = "terminated"
                    state["reason"] = container_status.state.terminated.reason
                    state["message"] = container_status.state.terminated.message
                    state["exit_code"] = container_status.state.terminated.exit_code
                    state["started_at"] = container_status.state.terminated.started_at.isoformat() if container_status.state.terminated.started_at else None
                    state["finished_at"] = container_status.state.terminated.finished_at.isoformat() if container_status.state.terminated.finished_at else None
                    
                container_info["state"] = state
                status["container_statuses"].append(container_info)
                
        # Add events if requested
        if include_events:
            try:
                field_selector = f"involvedObject.name={pod_name}"
                events = await k8s_client.list_namespaced_event(namespace, field_selector=field_selector)
                
                status["events"] = []
                
                for event in events.items:
                    status["events"].append({
                        "type": event.type,
                        "reason": event.reason,
                        "message": event.message,
                        "count": event.count,
                        "first_timestamp": event.first_timestamp.isoformat() if event.first_timestamp else None,
                        "last_timestamp": event.last_timestamp.isoformat() if event.last_timestamp else None
                    })
            except Exception as e:
                logger.warning(f"Error retrieving events for pod {pod_name}: {str(e)}")
                status["events_error"] = str(e)
                
        return status
        
    except Exception as e:
        logger.error(f"Error getting pod status: {str(e)}")
        logger.exception("Exception details:")
        return {
            "error": f"Error getting pod status: {str(e)}",
            "entity_guid": entity_guid,
            "status": "error"
        }


@runbook_agent.tool
async def get_pod_metrics(
    ctx: RunContext,
    entity_guid: str,
    metrics: List[str] = None,
    time_window_minutes: int = 60
) -> Dict[str, Any]:
    """
    Get metrics for a Kubernetes pod.
    
    This tool collects various metrics for a pod including CPU usage, memory usage,
    restart count, and container status.
    
    Args:
        entity_guid: GUID of the pod entity
        metrics: List of metrics to collect (cpu_usage, memory_usage, restart_count, container_status)
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Metrics for the pod over the specified time window
    """
    try:
        # First check if we have time window in state
        since_time = None
        until_time = None
        
        # Get time window from state if available
        time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            logger.info(f"Using time window from state: {since_time} to {until_time}")
        else:
            # Fall back to calculating from time_window_minutes
            current_time = datetime.now(timezone.utc)
            since_time = current_time - timedelta(minutes=time_window_minutes)
            until_time = current_time
            logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get pod metrics
        metrics_result = await metrics_collector.get_pod_metrics(
            entity_guid=entity_guid,
            since_time=since_time,
            until_time=until_time,
            metrics=metrics
        )
        
        return metrics_result
        
    except Exception as e:
        logger.error(f"Error getting pod metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get pod metrics",
            "entity_guid": entity_guid
        }
        
@runbook_agent.tool
async def get_node_metrics(
    ctx: RunContext,
    entity_guid: str,
    metrics: List[str] = None,
    time_window_minutes: int = 60
) -> Dict[str, Any]:
    """
    Get metrics for a Kubernetes node.
    
    This tool collects various metrics for a node including CPU usage, memory usage,
    pod count, conditions, disk usage, and network I/O.
    
    Args:
        entity_guid: GUID of the node entity
        metrics: List of metrics to collect (cpu_usage, memory_usage, pod_count, condition, disk_usage, network_io)
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Metrics for the node over the specified time window
    """
    try:
        # First check if we have time window in state
        since_time = None
        until_time = None
        
        # Get time window from state if available
        time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            logger.info(f"Using time window from state: {since_time} to {until_time}")
        else:
            # Fall back to calculating from time_window_minutes
            current_time = datetime.now(timezone.utc)
            since_time = current_time - timedelta(minutes=time_window_minutes)
            until_time = current_time
            logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get node metrics
        metrics_result = await metrics_collector.get_node_metrics(
            entity_guid=entity_guid,
            since_time=since_time,
            until_time=until_time,
            metrics=metrics
        )
        
        return metrics_result
        
    except Exception as e:
        logger.error(f"Error getting node metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get node metrics",
            "entity_guid": entity_guid
        }
        
@runbook_agent.tool
async def get_application_metrics(
    ctx: RunContext,
    entity_guid: str,
    metrics: List[str] = None,
    time_window_minutes: int = 60
) -> Dict[str, Any]:
    """
    Get metrics for an application.
    
    This tool collects various metrics for an application including response time,
    throughput, error rate, and Apdex score.
    
    Args:
        entity_guid: GUID of the application entity
        metrics: List of metrics to collect (response_time, throughput, error_rate, apdex)
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Metrics for the application over the specified time window
    """
    try:
        # First check if we have time window in state
        since_time = None
        until_time = None
        
        # Get time window from state if available
        time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            logger.info(f"Using time window from state: {since_time} to {until_time}")
        else:
            # Fall back to calculating from time_window_minutes
            current_time = datetime.now(timezone.utc)
            since_time = current_time - timedelta(minutes=time_window_minutes)
            until_time = current_time
            logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get application metrics
        metrics_result = await metrics_collector.get_application_metrics(
            entity_guid=entity_guid,
            since_time=since_time,
            until_time=until_time,
            metrics=metrics
        )
        
        return metrics_result
        
    except Exception as e:
        logger.error(f"Error getting application metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get application metrics",
            "entity_guid": entity_guid
        }
        
@runbook_agent.tool
async def get_host_metrics(
    ctx: RunContext,
    entity_guid: str,
    metrics: List[str] = None,
    time_window_minutes: int = 60
) -> Dict[str, Any]:
    """
    Get metrics for a host.
    
    This tool collects various metrics for a host including CPU usage, memory usage,
    disk usage, and network I/O.
    
    Args:
        entity_guid: GUID of the host entity
        metrics: List of metrics to collect (cpu_usage, memory_usage, disk_usage, network_io)
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Metrics for the host over the specified time window
    """
    try:
        # First check if we have time window in state
        since_time = None
        until_time = None
        
        # Get time window from state if available
        time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            logger.info(f"Using time window from state: {since_time} to {until_time}")
        else:
            # Fall back to calculating from time_window_minutes
            current_time = datetime.now(timezone.utc)
            since_time = current_time - timedelta(minutes=time_window_minutes)
            until_time = current_time
            logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get host metrics
        metrics_result = await metrics_collector.get_host_metrics(
            entity_guid=entity_guid,
            since_time=since_time,
            until_time=until_time,
            metrics=metrics
        )
        
        return metrics_result
        
    except Exception as e:
        logger.error(f"Error getting host metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get host metrics",
            "entity_guid": entity_guid
        }


@runbook_agent.tool
async def get_pod_spec(
    ctx: RunContext,
    entity_guid: str,
    include_resource_spec: bool = True
) -> Dict[str, Any]:
    """
    Get the specification of a Kubernetes pod.
    
    Args:
        entity_guid: GUID of the pod entity
        include_resource_spec: Whether to include resource requests and limits
        
    Returns:
        Pod specification
    """
    try:
        # Get the pod details using the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            return {
                "error": "Entity not found",
                "message": f"Could not find entity with GUID: {entity_guid}"
            }
            
        # Extract pod name
        pod_name = entity_details.get("name", "unknown").split("/")[-1]
        
        # Get namespace from tags
        namespace = None
        for tag in entity_details.get("tags", []):
            if tag.get("key") == "k8s.namespace.name":
                namespace = tag.get("values", [])[0] if tag.get("values") else None
                break
        
        # Get details from entity_analyzer
        current_time = datetime.now()
        since_time = current_time - timedelta(minutes=60)
        
        entity_data = await metrics_collector.analyze_entity(
            entity_guid=entity_guid,
            since_time=since_time,
            until_time=current_time
        )
        
        # Extract data
        status = entity_data.get("status", "unknown")
        phase = entity_data.get("phase", "unknown")
        node = entity_data.get("node", "unknown")
        
        # Extract resource specs if requested
        resource_specs = {}
        if include_resource_spec and "containers" in entity_data:
            for container_name, container_data in entity_data.get("containers", {}).items():
                if "resources" in container_data:
                    resources = container_data["resources"]
                    resource_specs[container_name] = {
                        "cpu_request": resources.get("cpu_request"),
                        "cpu_limit": resources.get("cpu_limit"),
                        "memory_request_mb": resources.get("memory_request_mb"),
                        "memory_limit_mb": resources.get("memory_limit_mb")
                    }
        
        return {
            "entity_name": entity_details.get("name", "unknown"),
            "entity_type": entity_details.get("type", "unknown"),
            "pod_name": pod_name,
            "namespace": namespace,
            "status": status,
            "phase": phase,
            "node": node,
            "containers": resource_specs if include_resource_spec else {}
        }
        
    except Exception as e:
        logger.error(f"Error getting pod spec: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get pod spec"
        }


@runbook_agent.tool
async def execute_runbook_steps(
    ctx: RunContext,
    runbook_id: str,
    entity_guid: str,
    state: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Execute all steps in a runbook for a specific entity.
    
    Args:
        runbook_id: ID of the runbook to execute
        entity_guid: GUID of the entity to run the runbook against
        state: Optional state information with condition details
        
    Returns:
        Results of executing the runbook steps
    """
    try:
        # Get the runbook details
        runbook_service = get_runbook_service()
        runbook = runbook_service.get_runbook(runbook_id)
        
        if not runbook:
            return {
                "error": f"Runbook not found: {runbook_id}",
                "available_runbooks": [rb["id"] for rb in runbook_service.get_all_runbooks()]
            }
            
        # Prepare result with basic runbook info
        result = {
            "runbook_id": runbook_id,
            "runbook_name": runbook.get("name", "Unknown Runbook"),
            "runbook_description": runbook.get("description", ""),
            "entity_guid": entity_guid,
            "entity_name": None,
            "entity_type": None,
            "cluster_id": None,
            "execution_time": datetime.utcnow().isoformat(),
            "step_results": [],
            "issues_found": False
        }
            
        # Get entity details
        metrics_collector = ctx.deps.metrics_collector
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            logger.error(f"Entity not found: {entity_guid}")
            return {
                **result,
                "error": "Entity not found",
                "message": f"Could not find entity with GUID: {entity_guid}",
                "issues_found": True,
                "summary": "Execution failed - the specified entity could not be found. Entity GUID is not associated with an active deployment.",
                "recommendations": [
                    "Verify the entity GUID provided in the request.",
                    "Check whether the entity (deployment) is still active in your Kubernetes cluster.",
                    "Consult your New Relic agent and configuration to ensure the deployment is being correctly monitored.",
                    "Retry execution with the corrected entity GUID or choose a different active deployment."
                ]
            }
            
        # Extract entity details using RunbookService
        entity_info = runbook_service.extract_entity_details(entity_details)
        
        # Update result with entity details
        result["entity_name"] = entity_info.get("entity_name")
        result["entity_type"] = entity_info.get("entity_type")
        result["cluster_id"] = entity_info.get("cluster_name")
        result["namespace"] = entity_info.get("namespace")
        
        # Execute each step
        steps = runbook.get("steps", [])
        
        for step in steps:
            step_title = step.get("title", "Unnamed Step")
            step_description = step.get("description", "")
            tool_name = step.get("tool", "")
            tool_params = step.get("parameters", {}) or {}
            
            logger.info(f"Executing step: {step_title} using tool {tool_name}")
            
            # Execute the tool using RunbookService's execute_tool method
            try:
                tool_result = await runbook_service.execute_tool(
                    metrics_collector=metrics_collector,
                    tool_name=tool_name,
                    entity_info=entity_info,
                    parameters=tool_params,
                    state=state
                )
                
                # Check for errors
                issues_found = "error" in tool_result
                
                # Generate a summary using RunbookService
                summary = runbook_service.generate_step_summary(step_title, tool_result)
                
                step_result = {
                    "step_title": step_title,
                    "step_description": step_description,
                    "tool": tool_name,
                    "parameters": tool_params,
                    "result": tool_result,
                    "summary": summary,
                    "issues_found": issues_found,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                if issues_found:
                    result["issues_found"] = True
                    
            except Exception as e:
                step_result = {
                    "step_title": step_title,
                    "step_description": step_description,
                    "tool": tool_name,
                    "parameters": tool_params,
                    "result": {"error": str(e)},
                    "summary": f"Step failed with error: {str(e)}",
                    "issues_found": True,
                    "timestamp": datetime.utcnow().isoformat()
                }
                result["issues_found"] = True
                
            result["step_results"].append(step_result)
            
        # Generate overall summary if not already set
        if "summary" not in result:
            if result["issues_found"]:
                result["summary"] = f"Completed with issues - {len(result['step_results'])} steps executed"
            else:
                result["summary"] = f"Successfully executed {len(result['step_results'])} steps"
                
        return result
        
    except Exception as e:
        logger.error(f"Error executing runbook steps: {str(e)}")
        logger.exception("Exception details:")
        return {
            "error": str(e),
            "runbook_id": runbook_id,
            "entity_guid": entity_guid,
            "issues_found": True,
            "summary": f"Execution failed with error: {str(e)}",
            "step_results": []
        }


@runbook_agent.tool
async def execute_tool(
    ctx: RunContext,
    tool_name: str,
    entity_guid: str = None,
    entity_type: Optional[str] = None,
    entity_name: Optional[str] = None,
    cluster_name: Optional[str] = None,
    namespace: Optional[str] = None,
    parameters: Dict[str, Any] = None,
    state: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Generic tool executor that can execute any tool defined in runbooks.
    
    Args:
        tool_name: Name of the tool to execute
        entity_guid: Optional GUID of the entity to operate on
        entity_type: Optional type of entity (e.g., KUBERNETES_POD, KUBERNETES_NODE)
        entity_name: Optional name of the entity (for cases where GUID is not available)
        cluster_name: Optional name of the Kubernetes cluster
        namespace: Optional Kubernetes namespace
        parameters: Tool-specific parameters
        state: Optional state information with condition details
        
    Returns:
        Results of the tool execution
    """
    return await tool_executor(
        metrics_collector=ctx.deps.metrics_collector,
        tool_name=tool_name,
        entity_guid=entity_guid,
        parameters=parameters,
        state=state
    )


async def tool_executor(
    metrics_collector: MetricsCollector,
    tool_name: str,
    entity_guid: str = None,
    parameters: Dict[str, Any] = None,
    state: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Execute a specific tool with the provided parameters.
    
    Args:
        metrics_collector: MetricsCollector instance for data retrieval
        tool_name: Name of the tool to execute
        entity_guid: Optional GUID of the entity to run against
        parameters: Tool-specific parameters
        state: State information containing all context
        
    Returns:
        Dictionary containing the result of tool execution
    """
    try:
        # Initialize parameters if None
        if parameters is None:
            parameters = {}
            
        # Get the runbook service
        runbook_service = get_runbook_service()
        
        # Initialize entity info
        entity_info = {
            "entity_guid": entity_guid,
            "entity_name": None,
            "entity_type": None,
            "cluster_name": None,
            "namespace": None
        }
        
        # Get entity details if GUID is provided
        if entity_guid:
            try:
                # Get entity details
                entity_details = await metrics_collector.get_entity_details(entity_guid)
                
                # Use RunbookService to extract entity details
                entity_info = runbook_service.extract_entity_details(entity_details)
            except Exception as e:
                logger.warning(f"Error getting entity details: {str(e)}")
        
        # Special handling for entity metrics tools
        if tool_name in ["get_entity_metrics", "get_pod_metrics", "get_node_metrics", 
                         "get_application_metrics", "get_host_metrics"]:
            # Extract time window information from state if available
            if state:
                time_window_minutes = parameters.get("time_window_minutes", 30)
                metrics = parameters.get("metrics", None)
                
                # Calculate time window
                current_time = datetime.now(timezone.utc)
                since_time = None
                until_time = None
                
                # Try to get time window from state
                if "since_time_ms" in state and "until_time_ms" in state:
                    since_time = datetime.fromtimestamp(state["since_time_ms"] / 1000, timezone.utc)
                    until_time = datetime.fromtimestamp(state["until_time_ms"] / 1000, timezone.utc)
                elif "since_time" in state and "until_time" in state:
                    try:
                        since_time = datetime.fromisoformat(state["since_time"].replace("Z", "+00:00"))
                        until_time = datetime.fromisoformat(state["until_time"].replace("Z", "+00:00"))
                    except Exception:
                        pass
                
                # Fall back to time_window_minutes if needed
                if not since_time or not until_time:
                    since_time = current_time - timedelta(minutes=time_window_minutes)
                    until_time = current_time
                
                # Choose the appropriate entity-specific method based on tool name
                if tool_name == "get_pod_metrics":
                    return await metrics_collector.get_pod_metrics(
                        entity_guid=entity_guid,
                        since_time=since_time,
                        until_time=until_time,
                        metrics=metrics
                    )
                elif tool_name == "get_node_metrics":
                    return await metrics_collector.get_node_metrics(
                        entity_guid=entity_guid,
                        since_time=since_time,
                        until_time=until_time,
                        metrics=metrics
                    )
                elif tool_name == "get_application_metrics":
                    return await metrics_collector.get_application_metrics(
                        entity_guid=entity_guid,
                        since_time=since_time,
                        until_time=until_time,
                        metrics=metrics
                    )
                elif tool_name == "get_host_metrics":
                    return await metrics_collector.get_host_metrics(
                        entity_guid=entity_guid,
                        since_time=since_time,
                        until_time=until_time,
                        metrics=metrics
                    )
                elif tool_name == "get_entity_metrics":
                    return await metrics_collector.get_entity_metrics(
                        entity_guid=entity_guid,
                        metrics=metrics,
                        since_time=since_time,
                        until_time=until_time
                    )
        
        # For entity logs, use the new get_entity_logs method
        if tool_name == "get_pod_logs" and entity_guid:
            limit = parameters.get("limit", 100)
            
            # First check if we have time parameters in state
            since_time = None
            until_time = None
            
            if state and "since_time" in state and "until_time" in state:
                since_time = state["since_time"]
                until_time = state["until_time"]
            elif state and "since_time_ms" in state and "until_time_ms" in state:
                # Convert ms to datetime objects
                since_time = datetime.fromtimestamp(state["since_time_ms"] / 1000, tz=timezone.utc)
                until_time = datetime.fromtimestamp(state["until_time_ms"] / 1000, tz=timezone.utc)
            else:
                # Only calculate from time_window if we don't have explicit time parameters
                time_window_minutes = parameters.get("time_window_minutes", 30)
                current_time = datetime.now(timezone.utc)
                since_time = current_time - timedelta(minutes=time_window_minutes)
                until_time = current_time
            
            return await metrics_collector.get_entity_logs(
                entity_guid=entity_guid,
                since_time=since_time,
                until_time=until_time,
                limit=limit
            )
                
        # Execute the tool using the RunbookService's execute_tool method for other tools
        result = await runbook_service.execute_tool(
            metrics_collector=metrics_collector,
            tool_name=tool_name,
            entity_info=entity_info,
            parameters=parameters,
            state=state
        )
        
        return result
            
    except Exception as e:
        logger.error(f"Error executing tool {tool_name}: {str(e)}")
        logger.exception("Exception details:")
        return {
            "error": f"Error executing tool {tool_name}: {str(e)}"
        }


@runbook_agent.tool
async def check_entity_exists(
    ctx: RunContext,
    entity_guid: str
) -> Dict[str, Any]:
    """
    Check if an entity exists and get its basic information.
    
    Args:
        entity_guid: GUID of the entity to check
        
    Returns:
        Dictionary with entity information or error
    """
    try:
        metrics_collector = ctx.deps.metrics_collector
        runbook_service = get_runbook_service()
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            return {
                "exists": False,
                "entity_guid": entity_guid,
                "error": "Entity not found"
            }
            
        # Use RunbookService to extract entity details
        entity_info = runbook_service.extract_entity_details(entity_details)
        
        return {
            "exists": True,
            "entity_guid": entity_guid,
            "entity_name": entity_info.get("entity_name"),
            "entity_type": entity_info.get("entity_type"),
            "cluster": entity_info.get("cluster_name"),
            "namespace": entity_info.get("namespace")
        }
        
    except Exception as e:
        logger.error(f"Error checking entity existence: {str(e)}")
        return {
            "exists": False,
            "entity_guid": entity_guid,
            "error": str(e)
        }


@runbook_agent.tool
async def get_pod_network_metrics(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 30
) -> Dict[str, Any]:
    """
    Get network metrics for a Kubernetes pod.
    
    Args:
        entity_guid: GUID of the pod entity
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Network metrics for the pod over the specified time window
    """
    try:
        # First check if we have time window in state
        since_time = None
        until_time = None
        
        # Get time window from state if available
        time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            logger.info(f"Using time window from state: {since_time} to {until_time}")
        else:
            # Fall back to calculating from time_window_minutes
            current_time = datetime.now(timezone.utc)
            since_time = current_time - timedelta(minutes=time_window_minutes)
            until_time = current_time
            logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get pod network metrics
        metrics_result = await metrics_collector.get_pod_network_metrics(
            entity_guid=entity_guid,
            since_time=since_time,
            until_time=until_time
        )
        
        return metrics_result
        
    except Exception as e:
        logger.error(f"Error getting pod network metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get pod network metrics",
            "entity_guid": entity_guid
        }

@runbook_agent.tool
async def get_pod_volume_metrics(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 30
) -> Dict[str, Any]:
    """
    Get volume metrics for a Kubernetes pod.
    
    Args:
        entity_guid: GUID of the pod entity
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Volume metrics for the pod over the specified time window
    """
    try:
        # First check if we have time window in state
        since_time = None
        until_time = None
        
        # Get time window from state if available
        time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            logger.info(f"Using time window from state: {since_time} to {until_time}")
        else:
            # Fall back to calculating from time_window_minutes
            current_time = datetime.now(timezone.utc)
            since_time = current_time - timedelta(minutes=time_window_minutes)
            until_time = current_time
            logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get pod volume metrics
        metrics_result = await metrics_collector.get_pod_volume_metrics(
            entity_guid=entity_guid,
            since_time=since_time,
            until_time=until_time
        )
        
        return metrics_result
        
    except Exception as e:
        logger.error(f"Error getting pod volume metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get pod volume metrics",
            "entity_guid": entity_guid
        }

@runbook_agent.tool
async def get_node_disk_metrics(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 30
) -> Dict[str, Any]:
    """
    Get disk usage metrics for a Kubernetes node.
    
    Args:
        entity_guid: GUID of the node entity
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Disk usage metrics for the node over the specified time window
    """
    try:
        # First check if we have time window in state
        since_time = None
        until_time = None
        
        # Get time window from state if available
        time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            logger.info(f"Using time window from state: {since_time} to {until_time}")
        else:
            # Fall back to calculating from time_window_minutes
            current_time = datetime.now(timezone.utc)
            since_time = current_time - timedelta(minutes=time_window_minutes)
            until_time = current_time
            logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get entity details first
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            return {"error": f"Node entity {entity_guid} not found"}
        
        # Verify it's a node
        entity_type = entity_details.get("type", "")
        if not entity_type.startswith("KUBERNETES_NODE"):
            return {"error": f"Entity {entity_guid} is not a Kubernetes node"}
        
        # Extract node name and cluster from entity details
        node_name = entity_details.get("name", "unknown")
        cluster_name = None
        
        # Extract cluster from tags
        tags = entity_details.get("tags", [])
        for tag in tags:
            if tag.get("key") == "clusterName":
                values = tag.get("values", [])
                if values:
                    cluster_name = values[0]
                    break
        
        if not cluster_name:
            logger.warning(f"Cluster name not found for node {node_name}")
            cluster_name = "unknown"
        
        # Convert to milliseconds for queries
        since_time_ms = int(since_time.timestamp() * 1000)
        until_time_ms = int(until_time.timestamp() * 1000)
        
        # Get disk metrics using NodeEntity
        disk_metrics = ctx.deps.metrics_collector.node_entity.get_disk_usage(
            node_name=node_name,
            cluster_name=cluster_name,
            since_time_ms=since_time_ms,
            until_time_ms=until_time_ms
        )
        
        return {
            "entity_guid": entity_guid,
            "node_name": node_name,
            "cluster_name": cluster_name,
            "disk_metrics": disk_metrics,
            "time_window": {
                "since": since_time.isoformat(),
                "until": until_time.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting node disk metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get node disk metrics",
            "entity_guid": entity_guid
        }

@runbook_agent.tool
async def get_node_network_metrics(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 30
) -> Dict[str, Any]:
    """
    Get network metrics for a Kubernetes node.
    
    Args:
        entity_guid: GUID of the node entity
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Network metrics for the node over the specified time window
    """
    try:
        # First check if we have time window in state
        since_time = None
        until_time = None
        
        # Get time window from state if available
        time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            logger.info(f"Using time window from state: {since_time} to {until_time}")
        else:
            # Fall back to calculating from time_window_minutes
            current_time = datetime.now(timezone.utc)
            since_time = current_time - timedelta(minutes=time_window_minutes)
            until_time = current_time
            logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get entity details first
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            return {"error": f"Node entity {entity_guid} not found"}
        
        # Verify it's a node
        entity_type = entity_details.get("type", "")
        if not entity_type.startswith("KUBERNETES_NODE"):
            return {"error": f"Entity {entity_guid} is not a Kubernetes node"}
        
        # Extract node name and cluster from entity details
        node_name = entity_details.get("name", "unknown")
        cluster_name = None
        
        # Extract cluster from tags
        tags = entity_details.get("tags", [])
        for tag in tags:
            if tag.get("key") == "clusterName":
                values = tag.get("values", [])
                if values:
                    cluster_name = values[0]
                    break
        
        if not cluster_name:
            logger.warning(f"Cluster name not found for node {node_name}")
            cluster_name = "unknown"
        
        # Convert to milliseconds for queries
        since_time_ms = int(since_time.timestamp() * 1000)
        until_time_ms = int(until_time.timestamp() * 1000)
        
        # Get network metrics using NodeEntity
        network_metrics = ctx.deps.metrics_collector.node_entity.get_network_io(
            node_name=node_name,
            cluster_name=cluster_name,
            since_time_ms=since_time_ms,
            until_time_ms=until_time_ms
        )
        
        return {
            "entity_guid": entity_guid,
            "node_name": node_name,
            "cluster_name": cluster_name,
            "network_metrics": network_metrics,
            "time_window": {
                "since": since_time.isoformat(),
                "until": until_time.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting node network metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get node network metrics",
            "entity_guid": entity_guid
        }

@runbook_agent.tool
async def get_node_allocatable_resources(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 30
) -> Dict[str, Any]:
    """
    Get allocatable resources for a Kubernetes node.
    
    Args:
        entity_guid: GUID of the node entity
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Allocatable resources for the node over the specified time window
    """
    try:
        # Calculate time window
        current_time = datetime.now(timezone.utc)
        since_time = current_time - timedelta(minutes=time_window_minutes)
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get entity details first
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            return {"error": f"Node entity {entity_guid} not found"}
        
        # Verify it's a node
        entity_type = entity_details.get("type", "")
        if not entity_type.startswith("KUBERNETES_NODE"):
            return {"error": f"Entity {entity_guid} is not a Kubernetes node"}
        
        # Extract node name and cluster from entity details
        node_name = entity_details.get("name", "unknown")
        cluster_name = None
        
        # Extract cluster from tags
        tags = entity_details.get("tags", [])
        for tag in tags:
            if tag.get("key") == "clusterName":
                values = tag.get("values", [])
                if values:
                    cluster_name = values[0]
                    break
        
        if not cluster_name:
            logger.warning(f"Cluster name not found for node {node_name}")
            cluster_name = "unknown"
        
        # Convert to milliseconds for queries
        since_time_ms = int(since_time.timestamp() * 1000)
        until_time_ms = int(until_time.timestamp() * 1000)
        
        # Get allocatable resources using NodeEntity
        allocatable_resources = ctx.deps.metrics_collector.node_entity.get_allocatable_resources(
            node_name=node_name,
            cluster_name=cluster_name,
            since_time_ms=since_time_ms,
            until_time_ms=until_time_ms
        )
        
        return {
            "entity_guid": entity_guid,
            "node_name": node_name,
            "cluster_name": cluster_name,
            "allocatable_resources": allocatable_resources,
            "time_window": {
                "since": since_time.isoformat(),
                "until": until_time.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting node allocatable resources: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get node allocatable resources",
            "entity_guid": entity_guid
        }

@runbook_agent.tool
async def get_cluster_metrics(
    ctx: RunContext,
    cluster_name: str,
    metrics: List[str] = None,
    time_window_minutes: int = 60,
    limit: int = 100
) -> Dict[str, Any]:
    """
    Get metrics for a Kubernetes cluster.
    
    Args:
        cluster_name: Name of the Kubernetes cluster
        metrics: List of metrics to retrieve
        time_window_minutes: Time window in minutes to look back
        limit: Maximum number of results to return
        
    Returns:
        Dictionary with cluster metrics
    """
    try:
        # Get metrics collector from context
        metrics_collector = ctx.deps.metrics_collector
        
        # Get time window from context if available
        time_window = ctx.deps.get_time_window()
        since_time = time_window.get("since_time")
        until_time = time_window.get("until_time")
        
        # If we don't have a time window from context, calculate it from time_window_minutes
        if not since_time or not until_time:
            until_time = datetime.now(timezone.utc)
            since_time = until_time - timedelta(minutes=time_window_minutes)
        
        logger.info(f"Getting cluster metrics for {cluster_name} from {since_time} to {until_time}")
        
        # Get default metrics if none specified
        if not metrics:
            metrics = [
                "node_count", 
                "pod_count", 
                "deployment_count",
                "namespace_count",
                "cluster_cpu_utilization",
                "cluster_memory_utilization"
            ]
        
        # Get cluster metrics
        metrics_result = await metrics_collector.get_cluster_metrics(
            cluster_name=cluster_name,
            since_time=since_time,
            until_time=until_time,
            metrics=metrics,
            limit=limit
        )
        
        return metrics_result
        
    except Exception as e:
        logger.error(f"Error getting cluster metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get cluster metrics",
            "cluster_name": cluster_name
        }


async def main():
    """
    Test function for the runbook agent.
    """
    # Initialize dependencies
    logger.info("Initializing dependencies")
    
    # Load environment variables
    dotenv.load_dotenv()
    
    # Initialize the metrics collector
    metrics_collector = MetricsCollector()
    
    # Initialize OpenAI client
    azure_openai_enabled = "AZURE_OPENAI_ENDPOINT" in os.environ and "AZURE_OPENAI_API_KEY" in os.environ
    
    if azure_openai_enabled:
        openai_client = AsyncAzureOpenAI(
            azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
            azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
            api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
            api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
        )
    else:
        openai_client = AsyncOpenAI(
            api_key=os.environ.get("OPENAI_API_KEY")
        )
    
    # Create sample alert info with condition details
    sample_alert_info = {
        "issueId": "a271ddf7-a9a8-4dc1-beea-39cca4fa1987", 
        "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/a271ddf7-a9a8-4dc1-beea-39cca4fa1987?notifier=WEBHOOK", 
        "title": "agent-management-background-container-service query result is > 10.0 on 'Pod with CrashLoopBackOff -- '", 
        "priority": "CRITICAL", 
        "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"], 
        "impactedEntities": ["agent-management-background-container-service"], 
        "totalIncidents": "1", 
        "state": "ACTIVATED", 
        "trigger": "STATE_CHANGE", 
        "createdAt": *************, 
        "updatedAt": *************, 
        "sources": ["newrelic"], 
        "alertPolicyNames": ["Neurons k8s Infra - Critical"], 
        "alertConditionNames": ["Pod with CrashLoopBackOff -- "], 
        "workflowName": "obv-ai-processing-neurons", 
        "chartLink": "https://gorgon.nr-assets.net/image/7cd23d09-c8aa-4e97-928a-d82a5bcfc2c9?config.legend.enabled=false&width=400&height=210", 
        "product": "neurons", 
        "nr_region": "us",
        
        # Additional fields needed for processing
        "alert_category": "castai_workload_autoscaler",
        "condition_id": "25541032",
        "triggered_at": datetime.fromtimestamp(************* / 1000).isoformat(),
        
        # Include time window information (from epoch milliseconds)
        "since_time_ms": ************* - (2700 * 1000),  # 2700 seconds before triggered_at
        "until_time_ms": *************,
        "threshold_duration": 2700,
        "aggregation_window": 900,
        
        # Adding human-readable time for reference
        "since_time": datetime.fromtimestamp((************* - (2700 * 1000)) / 1000).isoformat(),
        "until_time": datetime.fromtimestamp(************* / 1000).isoformat(),
        
        # Additional condition details
        "condition_details": {
            "description": "Pod is in CrashLoopBackOff.",
            "enabled": True,
            "id": "25541032",
            "name": "Pod with CrashLoopBackOff -- ",
            "policyId": "2405324",
            "runbookUrl": None,
            "signal": {
                "aggregationDelay": 120,
                "aggregationMethod": "EVENT_FLOW",
                "aggregationTimer": None,
                "aggregationWindow": 900
            },
            "terms": [
                {
                    "operator": "ABOVE",
                    "priority": "CRITICAL",
                    "threshold": 10,
                    "thresholdDuration": 2700,
                    "thresholdOccurrences": "AT_LEAST_ONCE"
                }
            ],
            "type": "STATIC",
            "violationTimeLimitSeconds": 21600,
            "policyName": "Neurons k8s Infra - Critical",
            "formatted_threshold": "ABOVE 10.0 for 2700 seconds",
            "query": "select count(*) from K8sContainerSample where status='Waiting' and reason='CrashLoopBackOff'"
        }
    }
    
    # Set up dependencies for the agent
    deps = RunbookAgentDeps(
        openai_client=openai_client,
        metrics_collector=metrics_collector,
        state=sample_alert_info  # Pass the sample alert info as state
    )
    
    # Get runbook service
    runbook_service = get_runbook_service()
    
    # Test the runbook service directly
    logger.info("Testing RunbookService capabilities with dynamic parameters")
    
    # Get available runbooks
    all_runbooks = runbook_service.get_all_runbooks()
    logger.info(f"Found {len(all_runbooks)} runbooks")
    
    # Get recommended runbooks for pod issues
    pod_runbooks = runbook_service.get_recommended_runbooks(
        alert_category="kubernetes_crashloopbackoff",
        entity_types=["KUBERNETES_POD"],
        max_results=2
    )
    logger.info(f"Recommended pod runbooks: {[rb['id'] for rb in pod_runbooks]}")
    
    # Test dynamically evaluating time windows
    # Example from runbook: time_window_minutes: "{max(aggregation_window/60, 30)}"
    logger.info("Testing dynamic parameter evaluation:")
    
    # Get a sample runbook step
    if pod_runbooks and "steps" in pod_runbooks[0] and pod_runbooks[0]["steps"]:
        sample_step = pod_runbooks[0]["steps"][0]
        sample_params = sample_step.get("parameters", {})
        
        logger.info(f"Sample step: {sample_step['title']}")
        logger.info(f"Raw parameters: {sample_params}")
        
        # Test parameter processing with alert info
        processed_params = runbook_service.get_tool_execution_params(
            tool_name=sample_step.get("tool"),
            entity_info={"entity_name": "test-pod", "entity_type": "KUBERNETES_POD"},
            parameters=sample_params,
            state=sample_alert_info
        )
        
        logger.info(f"Processed parameters with state: {processed_params}")
    
    # Test the agent with a recommendation query
    test_prompt = "What runbooks should I use for a pod with crashloopbackoff issues?"
    logger.info(f"Testing runbook agent with prompt: {test_prompt}")
    
    # Run the agent
    result = await runbook_agent.run(test_prompt, deps=deps)
    logger.info(f"Agent recommendation response: {result.data}")
    
    # Test entity existence check with a sample entity
    # Note: Replace with a real entity GUID for testing
    test_entity_guid = "MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"
    
    logger.info(f"Testing entity existence check with entity: {test_entity_guid}")
    
    # Skip the check_entity_exists call since it requires RunContext
    # Instead, we'll simulate the entity check result
    entity_check = {
        "exists": False,
        "entity_guid": test_entity_guid,
        "entity_name": "test-pod",
        "entity_type": "KUBERNETES_POD",
        "cluster": "test-cluster",
        "namespace": "test-namespace"
    }
    
    if entity_check["exists"]:
        # Demonstrate the improved tool execution flow using RunbookService directly
        logger.info("Demonstrating improved tool execution using RunbookService with state")
        
        # Extract entity info
        entity_info = {
            "entity_guid": entity_check["entity_guid"],
            "entity_name": entity_check["entity_name"],
            "entity_type": entity_check["entity_type"],
            "cluster_name": entity_check["cluster"],
            "namespace": entity_check["namespace"]
        }
        
        # Execute a tool using RunbookService with state
        tool_result = await runbook_service.execute_tool(
            metrics_collector=metrics_collector,
            tool_name="get_pod_metrics",
            entity_info=entity_info,
            parameters={
                "metrics": ["cpu_usage", "memory_usage", "restart_count"],
                "time_window_minutes": "{max(threshold_duration/60, 30)}"  # Dynamic parameter
            },
            state=sample_alert_info
        )
        
        # Generate a summary
        summary = runbook_service.generate_step_summary("Analyze Pod Metrics", tool_result)
        
        logger.info(f"Tool execution summary: {summary}")
        logger.info(f"Tool result: {tool_result}")
    else:
        logger.info(f"Test entity does not exist: {entity_check}")
    
    # Demo with execute_tool directly
    logger.info("Testing execute_tool with direct tool executor")
    tool_result = await tool_executor(
        metrics_collector=metrics_collector,
        tool_name="get_pod_metrics",
        entity_guid=test_entity_guid,
        parameters={
            "metrics": ["cpu_usage", "memory_usage", "restart_count"],
            "time_window_minutes": 30  # Fixed parameter instead of dynamic
        },
        state=sample_alert_info
    )
    logger.info(f"Tool executor result: {tool_result}")
    
    # Test runbook execution with state
    if pod_runbooks:
        logger.info("Testing runbook execution with state")
        runbook_id = pod_runbooks[0]["id"]
        
        # Create a sample call to execute_runbook_steps with manually created context
        logger.info(f"Would execute runbook {runbook_id} with entity {test_entity_guid}")
        logger.info("Skipping actual execution due to RunContext initialization requirements")


if __name__ == "__main__":
    asyncio.run(main()) 