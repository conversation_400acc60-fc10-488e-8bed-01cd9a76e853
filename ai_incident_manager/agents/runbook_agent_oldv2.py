"""
Runbook Agent - Pydantic AI Agent for executing runbooks.

This agent is responsible for executing runbooks to investigate and remediate incidents.
The agent is responsible for:
1. Selecting appropriate runbooks based on alert category and entity types
2. Executing runbook steps using specialized tools
3. Collecting and analyzing results from each step
4. Providing recommendations and insights based on the runbook execution
"""

import os
import re
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass, field
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel

import dotenv
from openai import AsyncOpenAI, AsyncAzureOpenAI

from ai_incident_manager.services.runbook_service import get_runbook_service
from ai_incident_manager.services.metrics_collector import MetricsCollector
from lib.new_relic.analyzer import EntityAnalyzer
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.logs import NewRelicLogsClient
from lib.new_relic.base import Region

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize OpenAI client
required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
azure_openai_enabled = all(var in os.environ for var in required_azure_vars)

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    model = OpenAIModel(os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"), openai_client=openai_client)
else:
    logger.error("Azure OpenAI credentials not found. This application requires Azure OpenAI.")
    logger.error(f"Missing environment variables: {[var for var in required_azure_vars if var not in os.environ]}")
    raise EnvironmentError("Azure OpenAI credentials are required but not found in environment variables.")


def get_runbook_service():
    """
    Get the singleton instance of RunbookService.
    
    Returns:
        RunbookService: The singleton instance
    """
    from ai_incident_manager.services.runbook_service import RunbookService
    return RunbookService()


@dataclass
class RunbookAgentDeps:
    """Dependencies for the runbook agent."""
    openai_client: AsyncAzureOpenAI
    metrics_collector: MetricsCollector
    logs_client: NewRelicLogsClient
    state: Dict[str, Any] = None
    runbook: str = None
    alert_category: Dict[str, Any] = None
    primary_entity: Dict[str, Any] = None  # Primary entity affected by the alert
    related_entities: List[Dict[str, Any]] = field(default_factory=list)  # Related entities
    
    def get_time_window(self) -> Dict[str, Any]:
        """
        Get time window information from the state.
        
        Returns:
            Dict containing time window information with both datetime 
            objects and millisecond timestamps
        """
        if not self.state:
            return {}
            
        time_window = {}
        
        # Check for since_time and until_time directly in state
        if "since_time" in self.state and "until_time" in self.state:
            # Ensure datetime objects for since_time and until_time
            if isinstance(self.state["since_time"], str):
                try:
                    time_window["since_time"] = datetime.fromisoformat(self.state["since_time"].replace('Z', '+00:00'))
                except ValueError:
                    # Handle other string formats if needed
                    logger.warning(f"Could not parse since_time: {self.state['since_time']}")
                    time_window["since_time"] = self.state["since_time"]
            else:
                time_window["since_time"] = self.state["since_time"]
                
            if isinstance(self.state["until_time"], str):
                try:
                    time_window["until_time"] = datetime.fromisoformat(self.state["until_time"].replace('Z', '+00:00'))
                except ValueError:
                    # Handle other string formats if needed
                    logger.warning(f"Could not parse until_time: {self.state['until_time']}")
                    time_window["until_time"] = self.state["until_time"]
            else:
                time_window["until_time"] = self.state["until_time"]
            
            # Add millisecond formats if not present
            if "since_time_ms" not in self.state and isinstance(time_window["since_time"], datetime):
                time_window["since_time_ms"] = int(time_window["since_time"].timestamp() * 1000)
                
            if "until_time_ms" not in self.state and isinstance(time_window["until_time"], datetime):
                time_window["until_time_ms"] = int(time_window["until_time"].timestamp() * 1000)
            
        # Check for epoch millisecond versions
        elif "since_time_ms" in self.state and "until_time_ms" in self.state:
            time_window["since_time_ms"] = self.state["since_time_ms"]
            time_window["until_time_ms"] = self.state["until_time_ms"]
            
            # Convert to datetime objects
            time_window["since_time"] = datetime.fromtimestamp(
                self.state["since_time_ms"] / 1000, tz=timezone.utc
            )
                
            time_window["until_time"] = datetime.fromtimestamp(
                self.state["until_time_ms"] / 1000, tz=timezone.utc
            )
        
        return time_window

    def get_entity_by_type(self, entity_type: str) -> Optional[Dict[str, Any]]:
        """Get an entity by its type."""
        if self.primary_entity and self.primary_entity.get("type") == entity_type:
            return self.primary_entity
            
        for entity in self.related_entities:
            if entity.get("type") == entity_type:
                return entity
                
        return None
        
    def get_entity_by_name(self, entity_name: str) -> Optional[Dict[str, Any]]:
        """Get an entity by its name."""
        if self.primary_entity and self.primary_entity.get("name") == entity_name:
            return self.primary_entity
            
        for entity in self.related_entities:
            if entity.get("name") == entity_name:
                return entity
                
        return None
        
    def get_entity_by_guid(self, entity_guid: str) -> Optional[Dict[str, Any]]:
        """Get an entity by its GUID."""
        if self.primary_entity and self.primary_entity.get("guid") == entity_guid:
            return self.primary_entity
            
        for entity in self.related_entities:
            if entity.get("guid") == entity_guid:
                return entity
                
        return None


class RunbookStepResult(BaseModel):
    """Result of executing a runbook step."""
    step_title: str
    step_description: str = ""
    tool: str
    parameters: Dict[str, Any]
    result: Dict[str, Any]
    summary: str
    data_type: str = "information"  # One of: metric, log, information
    nrql_query: Optional[str] = None  # NRQL query if applicable
    entity_guid: Optional[str] = None  # Entity GUID this step was executed against
    entity_name: Optional[str] = None  # Entity name this step was executed against
    entity_type: Optional[str] = None  # Entity type this step was executed against
    issues_found: bool = False
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class CollectedInformation(BaseModel):
    """Information collected during runbook execution."""
    title: str
    description: str = ""
    data_type: str  # metric, log, information
    content: Any
    entity_guid: Optional[str] = None
    entity_name: Optional[str] = None
    entity_type: Optional[str] = None
    nrql_query: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class EntityRelationship(BaseModel):
    """Relationship between entities."""
    source_guid: str
    source_name: str
    source_type: str
    target_guid: str
    target_name: str
    target_type: str
    relationship_type: str
    is_primary: bool = False


class RunbookExecutionResult(BaseModel):
    """Result of executing a runbook."""
    runbook_id: str
    runbook_name: str
    runbook_description: str
    entity_guid: Optional[str] = None  # Primary entity GUID
    entity_name: Optional[str] = None  # Primary entity name
    entity_type: Optional[str] = None  # Primary entity type
    cluster_id: Optional[str] = None
    execution_time: datetime = Field(default_factory=datetime.utcnow)
    step_results: List[RunbookStepResult] = Field(default_factory=list)
    entities: List[Dict[str, Any]] = Field(default_factory=list)  # All related entities with their details
    entity_relationships: List[EntityRelationship] = Field(default_factory=list)
    collected_information: List[CollectedInformation] = Field(default_factory=list)
    metrics: List[CollectedInformation] = Field(default_factory=list)
    logs: List[CollectedInformation] = Field(default_factory=list)
    information: List[CollectedInformation] = Field(default_factory=list)
    issues_found: bool = False
    summary: str = ""
    findings: List[str] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)


# Runbook Agent with result_type and system message
runbook_agent = Agent(
    model,
    deps_type=RunbookAgentDeps,
    result_type=RunbookExecutionResult,
    system_prompt="""
You are an expert runbook executor specialized in investigating and remediating cloud and Kubernetes incidents.
Your task is to analyze alerts, identify related entities, and execute runbook steps to investigate and resolve issues.

When investigating an incident:
1. Understand the alert category and the primary entity affected
2. Identify all related entities based on entity relationships
3. Follow each step in the runbook and call the appropriate tools
4. Collect and analyze metrics, logs, and other information
5. Summarize findings and highlight any issues found
6. Recommend specific remediation actions

Entity Relationship Management:
- The primary entity is the one directly affected by the alert
- Related entities are those with relationships to the primary entity (e.g., Pod → Node, Pod → Container)
- Always specify the correct entity GUID when making tool calls
- Document all entities with their types, GUIDs, and relationship to primary entity

Information Collection:
- For each tool call, categorize the information collected as one of: "metric", "log", or "information"
- Include the entity GUID and name that the information relates to
- Include NRQL queries when available
- Organize collected information by type for easy reference

Response Format:
- Organize your response in a structured format with clearly labeled sections
- Include a section for entity relationships
- Include a section for all collected information, organized by type
- Include a findings section that highlights issues discovered
- Include a recommendations section with specific actions to resolve issues

Always use the exact since_time and until_time from the alert window for all time-based queries, 
rather than using relative time windows where possible.
"""
)

@runbook_agent.system_prompt  
async def add_runbook_context(ctx: RunContext) -> str:
    """
    Add runbook context to the system prompt dynamically.
    This includes:
    1. The runbook from deps if available
    2. Alert category details if available
    3. Entity relationship information
    """
    result = []
    
    # Add runbook from deps if available
    if ctx.deps.runbook:
        result.append(f"RUNBOOK DETAILS:\n{ctx.deps.runbook}")
    
    # Add alert category details if available
    if ctx.deps.alert_category:
        category = ctx.deps.alert_category.get("category", "Unknown Category")
        description = ctx.deps.alert_category.get("description", "")
        likely_causes = ctx.deps.alert_category.get("likely_causes", [])
        metrics = ctx.deps.alert_category.get("metrics", [])
        runbook_text = ctx.deps.alert_category.get("runbook", "")
        
        result.append(f"""
ALERT CATEGORY DETAILS:
Category: {category}
Description: {description}
Likely Causes: {', '.join(likely_causes)}
Key Metrics: {', '.join([m.get('name', '') for m in metrics])}

RUNBOOK STEPS:
{runbook_text}
""")
    
    # Add entity information if available
    if ctx.deps.primary_entity:
        entity_context = f"\nPRIMARY ENTITY:\n{ctx.deps.primary_entity.get('name')} (GUID: {ctx.deps.primary_entity.get('guid')}, Type: {ctx.deps.primary_entity.get('type')})\n"
        
        if ctx.deps.related_entities:
            entity_context += "\nRELATED ENTITIES:\n"
            for entity in ctx.deps.related_entities:
                entity_context += f"- {entity.get('name')} (GUID: {entity.get('guid')}, Type: {entity.get('type')}, Relationship: {entity.get('relationship', 'unknown')})\n"
        
        result.append(entity_context)
    
    # Add time window if available
    time_window = ctx.deps.get_time_window()
    if time_window and "since_time" in time_window and "until_time" in time_window:
        since_time = time_window["since_time"]
        until_time = time_window["until_time"]
        result.append(f"\nTIME WINDOW:\nFrom {since_time.isoformat()} to {until_time.isoformat()}")
        # time window in epoch milliseconds
        result.append(f"\nTIME WINDOW (EPOCH MILLISECONDS):\nFrom {int(since_time.timestamp() * 1000)} to {int(until_time.timestamp() * 1000)}")
    
    return "\n".join(result)


# @runbook_agent.tool
# async def get_runbook_details(
#     ctx: RunContext,
#     runbook_id: str
# ) -> Dict[str, Any]:
#     """
#     Get details about a specific runbook.
    
#     Args:
#         runbook_id: ID of the runbook to retrieve
        
#     Returns:
#         Runbook details including steps and prerequisites
#     """
#     runbook_service = get_runbook_service()
#     runbook = runbook_service.get_runbook(runbook_id)
    
#     if not runbook:
#         return {
#             "error": f"Runbook not found: {runbook_id}",
#             "available_runbooks": [rb["id"] for rb in runbook_service.get_all_runbooks()]
#         }
    
#     return runbook


# @runbook_agent.tool
# async def get_recommended_runbooks(
#     ctx: RunContext,
#     alert_category: str,
#     entity_types: List[str],
#     max_results: int = 3
# ) -> Dict[str, Any]:
#     """
#     Get recommended runbooks based on alert category and entity types.
    
#     Args:
#         alert_category: Alert category (e.g., kubernetes_crashloopbackoff, kubernetes_evicted_pod)
#         entity_types: List of entity types (e.g., KUBERNETES_POD, K8S_NODE)
#         max_results: Maximum number of results to return
        
#     Returns:
#         List of recommended runbooks
#     """
#     runbook_service = get_runbook_service()
#     recommended_runbooks = runbook_service.get_recommended_runbooks(
#         alert_category=alert_category,
#         entity_types=entity_types,
#         max_results=max_results
#     )
    
#     return {
#         "recommended_runbooks": recommended_runbooks,
#         "count": len(recommended_runbooks)
#     }


@runbook_agent.tool
async def get_pod_logs(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 30,
    limit: int = 100
) -> Dict[str, Any]:
    """
    Get the logs from a Kubernetes pod.
    
    Args:
        entity_guid: New Relic entity GUID for the pod
        time_window_minutes: Number of minutes of logs to retrieve
        limit: Maximum number of log lines to return
        
    Returns:
        Pod logs and metadata
    """
    try:
        # Get entity details
        metrics_collector = ctx.deps.metrics_collector
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            logger.error(f"Entity not found: {entity_guid}")
            return {
                "success": False,
                "error": f"Entity not found: {entity_guid}",
                "logs": []
            }
            
        # Find pod name from entity details or entity list
        pod_name = entity_details.get("name")
        
        # If entity not found in main entity, check related entities
        if not pod_name and entity_guid:
            # Try to get entity from dependencies
            entity = ctx.deps.get_entity_by_guid(entity_guid)
            if entity:
                pod_name = entity.get("name")
        
        if not pod_name:
            logger.error(f"Could not determine pod name for entity: {entity_guid}")
            return {
                "success": False,
                "error": f"Could not determine pod name for entity: {entity_guid}",
                "logs": []
            }
            
        # Calculate time window
        until_time = datetime.now(timezone.utc)
        since_time = until_time - timedelta(minutes=time_window_minutes)
        
        # If state has time window, use that instead
        time_window = ctx.deps.get_time_window()
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            
        # Determine partition name from state if available
        partition = "default"  # Default partition
        if ctx.deps.state:
            product = ctx.deps.state.get("product", "").lower()
            landscape = ctx.deps.state.get("landscape", "").lower()
            
            if product and landscape:
                # Construct partition name pattern: product_landscape
                partition_key = f"{product}_{landscape}"
                # Check if this matches any known partition key pattern
                if partition_key in NewRelicLogsClient.KNOWN_PARTITIONS:
                    partition = partition_key
                # For neurons partitions that follow the pattern "neurons_XXX"
                elif product == "neurons" and landscape in ["nvu", "mlu", "uku", "ttu", "tku"]:
                    partition = f"neurons_{landscape}"
                # For MDM partitions that follow the pattern "mdm_XXX"
                elif product == "mdm" and landscape in ["na1", "na2", "ap1", "ap2"]:
                    partition = f"mdm_{landscape}"
        
        # Get logs directly from the logs client in dependencies
        logs_client = ctx.deps.logs_client
        
        logger.info(f"Querying logs for pod: {pod_name}")
        logger.info(f"Using partition: {partition}")
        logger.info(f"Time window: {since_time.isoformat()} to {until_time.isoformat()}")
        
        # Get logs directly from the logs client
        pod_logs = logs_client.get_pod_logs(
            pod_name=pod_name,
            cluster_partition=partition,
            since=since_time,
            until=until_time,
            limit=limit
        )
        
        # If no logs found using the partition, try with default partition
        if not pod_logs and partition != "default":
            logger.info(f"No logs found in partition {partition}, trying default partition")
            pod_logs = logs_client.get_pod_logs(
                pod_name=pod_name,
                cluster_partition="default",
                since=since_time,
                until=until_time,
                limit=limit
            )
            
        # If still no logs, try with neurons_nvu partition (common for pod logs)
        if not pod_logs and partition != "neurons_nvu":
            logger.info("No logs found in default partition, trying neurons_nvu partition")
            pod_logs = logs_client.get_pod_logs(
                pod_name=pod_name,
                cluster_partition="neurons_nvu",
                since=since_time,
                until=until_time,
                limit=limit
            )
        
        return {
            "success": True,
            "pod_name": pod_name,
            "entity_guid": entity_guid,
            "logs_count": len(pod_logs),
            "partition": partition,
            "since_time": since_time.isoformat(),
            "until_time": until_time.isoformat(),
            "logs": pod_logs
        }
        
    except Exception as e:
        logger.error(f"Error getting pod logs: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": str(e),
            "logs": []
        }


@runbook_agent.tool
async def get_kubernetes_description(
    ctx: RunContext,
    entity_guid: str
) -> Dict[str, Any]:
    """
    Get detailed description of a Kubernetes entity similar to 'kubectl describe'.
    
    This tool retrieves a detailed description of Kubernetes entities including:
    - Pods
    - Nodes
    - Deployments
    - Services
    - DaemonSets
    - CronJobs
    - Jobs
    - and other Kubernetes resources
    
    Args:
        entity_guid: New Relic entity GUID for the Kubernetes resource
        
    Returns:
        Detailed entity description and metadata
    """
    try:
        # Get metrics collector from context
        metrics_collector = ctx.deps.metrics_collector
        
        # Prepare time window
        time_window = ctx.deps.get_time_window()
        
        # Use timestamps from state if available
        since_time = None
        until_time = None
        
        if "since_time" in time_window:
            try:
                since_time = datetime.fromisoformat(time_window["since_time"].replace("Z", "+00:00"))
            except Exception as e:
                logger.warning(f"Failed to parse since_time: {e}")
        
        if "until_time" in time_window:
            try:
                until_time = datetime.fromisoformat(time_window["until_time"].replace("Z", "+00:00"))
            except Exception as e:
                logger.warning(f"Failed to parse until_time: {e}")
        
        # Verify we have both since_time and until_time
        if not since_time or not until_time:
            logger.error("Missing required time window information in state")
            return {
                "error": "Missing required time window information. Alert time window must be provided in state.",
                "entity_guid": entity_guid
            }
        
        # Call the metrics collector to get the Kubernetes description
        result = metrics_collector.get_kubernetes_entity_description(
            entity_guid=entity_guid,
            since_time=since_time,
            until_time=until_time
        )
        
        # Process the result for better readability
        if "description" in result and result["description"]:
            # Extract key information for summary
            entity_type = result.get("entity_type", "Unknown")
            entity_name = result.get("entity_name", "Unknown")
            
            # Add a summary based on the entity type
            if entity_type == "Pod":
                # Extract status and container info for pods
                status = "Unknown"
                containers_status = []
                
                lines = result["description"].split('\n')
                for i, line in enumerate(lines):
                    if line.startswith("Status:"):
                        status = line.split(':', 1)[1].strip()
                    
                    # Look for container statuses
                    if "State:" in line and i > 0 and "Container ID:" in lines[i-1]:
                        container_name = lines[i-2].strip().rstrip(':')
                        state = line.split(':', 1)[1].strip()
                        containers_status.append(f"{container_name}: {state}")
                
                result["summary"] = {
                    "entity_type": "Pod",
                    "name": entity_name,
                    "status": status,
                    "containers": containers_status
                }
            
            elif entity_type == "Node":
                # Extract node conditions and resource info
                conditions = []
                resources = {}
                
                lines = result["description"].split('\n')
                in_conditions_section = False
                
                for line in lines:
                    if line.startswith("Conditions:"):
                        in_conditions_section = True
                        continue
                    
                    if in_conditions_section and line.strip() and not line.startswith(" "):
                        in_conditions_section = False
                    
                    if in_conditions_section and ":" in line:
                        conditions.append(line.strip())
                    
                    # Extract resource info
                    if "cpu:" in line or "memory:" in line:
                        parts = line.split(':', 1)
                        if len(parts) == 2:
                            key = parts[0].strip()
                            value = parts[1].strip()
                            resources[key] = value
                
                result["summary"] = {
                    "entity_type": "Node",
                    "name": entity_name,
                    "conditions": conditions,
                    "resources": resources
                }
            
            elif entity_type in ["Deployment", "DaemonSet", "ReplicaSet"]:
                # Extract deployment status
                replicas = "Unknown"
                strategy = "Unknown"
                
                lines = result["description"].split('\n')
                for line in lines:
                    if "Replicas:" in line:
                        replicas = line.split(':', 1)[1].strip()
                    if "Strategy:" in line:
                        strategy = line.split(':', 1)[1].strip()
                
                result["summary"] = {
                    "entity_type": entity_type,
                    "name": entity_name,
                    "replicas": replicas,
                    "strategy": strategy
                }
        
        return result
            
    except Exception as e:
        logger.error(f"Error getting Kubernetes description for {entity_guid}: {str(e)}")
        return {
            "error": f"Failed to get Kubernetes description: {str(e)}",
            "entity_guid": entity_guid
        }


# @runbook_agent.tool
# async def get_pod_status(
#     ctx: RunContext,
#     entity_guid: str,
#     include_events: bool = True
# ) -> Dict[str, Any]:
#     """
#     Get the current status of a Kubernetes pod.
    
#     Args:
#         entity_guid: New Relic entity GUID for the pod
#         include_events: Whether to include pod events
        
#     Returns:
#         Pod status information
#     """
#     try:
#         # Get entity details
#         metrics_collector = ctx.deps.metrics_collector
#         entity_details = await metrics_collector.get_entity_details(entity_guid)
        
#         if not entity_details:
#             logger.error(f"Entity not found: {entity_guid}")
#             return {
#                 "error": f"Entity not found: {entity_guid}",
#                 "status": "unknown"
#             }
        
#         # Extract pod name and cluster
#         pod_name = entity_details.get("name")
        
#         if not pod_name:
#             logger.error(f"Pod name not found in entity details for {entity_guid}")
#             return {
#                 "error": f"Pod name not found in entity details",
#                 "status": "unknown"
#             }
            
#         cluster_name = None
#         tags = entity_details.get("tags", [])
        
#         if isinstance(tags, list):
#             for tag in tags:
#                 if isinstance(tag, dict) and tag.get("key") == "clusterName":
#                     values = tag.get("values", [])
#                     if isinstance(values, list) and values:
#                         cluster_name = values[0]
#                 elif isinstance(tag, dict) and tag.get("key") == "k8s.clusterName":
#                     values = tag.get("values", [])
#                     if isinstance(values, list) and values:
#                         cluster_name = values[0]
#                 elif isinstance(tag, dict) and tag.get("key") == "namespace":
#                     values = tag.get("values", [])
#                     if isinstance(values, list) and values:
#                         namespace = values[0]
#                 elif isinstance(tag, dict) and tag.get("key") == "k8s.namespace.name":
#                     values = tag.get("values", [])
#                     if isinstance(values, list) and values:
#                         namespace = values[0]
                    
#         if not cluster_name:
#             logger.warning(f"Cluster name not found for pod {pod_name} ({entity_guid})")
        
#         # Get pod status from the Kubernetes API
#         k8s_client = get_kubernetes_client(cluster_name)
        
#         if not k8s_client:
#             logger.error(f"Could not get Kubernetes client for cluster: {cluster_name}")
#             return {
#                 "error": f"Kubernetes client not available for cluster: {cluster_name or 'unknown'}",
#                 "pod_name": pod_name,
#                 "cluster": cluster_name,
#                 "status": "unknown"
#             }
        
#         # Get pod details
#         pod = await k8s_client.read_namespaced_pod(pod_name, namespace)
        
#         if not pod:
#             logger.error(f"Pod {pod_name} not found in namespace {namespace}")
#             return {
#                 "error": f"Pod {pod_name} not found in namespace {namespace}",
#                 "pod_name": pod_name,
#                 "namespace": namespace,
#                 "cluster": cluster_name,
#                 "status": "unknown"
#             }
            
#         # Extract status
#         status = {
#             "pod_name": pod_name,
#             "namespace": namespace,
#             "cluster": cluster_name,
#             "phase": pod.status.phase,
#             "conditions": [],
#             "container_statuses": []
#         }
        
#         # Add conditions
#         if pod.status.conditions:
#             for condition in pod.status.conditions:
#                 status["conditions"].append({
#                     "type": condition.type,
#                     "status": condition.status,
#                     "reason": condition.reason,
#                     "message": condition.message,
#                     "last_transition_time": condition.last_transition_time.isoformat() if condition.last_transition_time else None
#                 })
                
#         # Add container statuses
#         if pod.status.container_statuses:
#             for container_status in pod.status.container_statuses:
#                 container_info = {
#                     "name": container_status.name,
#                     "ready": container_status.ready,
#                     "restart_count": container_status.restart_count,
#                     "image": container_status.image
#                 }
                
#                 # Add state info
#                 state = {}
                
#                 if container_status.state.running:
#                     state["state"] = "running"
#                     state["started_at"] = container_status.state.running.started_at.isoformat() if container_status.state.running.started_at else None
#                 elif container_status.state.waiting:
#                     state["state"] = "waiting"
#                     state["reason"] = container_status.state.waiting.reason
#                     state["message"] = container_status.state.waiting.message
#                 elif container_status.state.terminated:
#                     state["state"] = "terminated"
#                     state["reason"] = container_status.state.terminated.reason
#                     state["message"] = container_status.state.terminated.message
#                     state["exit_code"] = container_status.state.terminated.exit_code
#                     state["started_at"] = container_status.state.terminated.started_at.isoformat() if container_status.state.terminated.started_at else None
#                     state["finished_at"] = container_status.state.terminated.finished_at.isoformat() if container_status.state.terminated.finished_at else None
                    
#                 container_info["state"] = state
#                 status["container_statuses"].append(container_info)
                
#         # Add events if requested
#         if include_events:
#             try:
#                 field_selector = f"involvedObject.name={pod_name}"
#                 events = await k8s_client.list_namespaced_event(namespace, field_selector=field_selector)
                
#                 status["events"] = []
                
#                 for event in events.items:
#                     status["events"].append({
#                         "type": event.type,
#                         "reason": event.reason,
#                         "message": event.message,
#                         "count": event.count,
#                         "first_timestamp": event.first_timestamp.isoformat() if event.first_timestamp else None,
#                         "last_timestamp": event.last_timestamp.isoformat() if event.last_timestamp else None
#                     })
#             except Exception as e:
#                 logger.warning(f"Error retrieving events for pod {pod_name}: {str(e)}")
#                 status["events_error"] = str(e)
                
#         return status
        
#     except Exception as e:
#         logger.error(f"Error getting pod status: {str(e)}")
#         logger.exception("Exception details:")
#         return {
#             "error": f"Error getting pod status: {str(e)}",
#             "entity_guid": entity_guid,
#             "status": "error"
#         }


# @runbook_agent.tool
# async def get_pod_metrics(
#     ctx: RunContext,
#     entity_guid: str,
#     metrics: List[str] = None,
#     time_window_minutes: int = 60
# ) -> Dict[str, Any]:
#     """
#     Get metrics for a Kubernetes pod.
    
#     This tool collects various metrics for a pod including CPU usage, memory usage,
#     restart count, and container status.
    
#     Args:
#         entity_guid: GUID of the pod entity
#         metrics: List of metrics to collect (cpu_usage, memory_usage, restart_count, container_status)
#         time_window_minutes: Time window in minutes to look back
        
#     Returns:
#         Metrics for the pod over the specified time window
#     """
#     try:
#         # First check if we have time window in state
#         since_time = None
#         until_time = None
        
#         # Get time window from state if available
#         time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
#         if time_window and "since_time" in time_window and "until_time" in time_window:
#             since_time = time_window["since_time"]
#             until_time = time_window["until_time"]
#             logger.info(f"Using time window from state: {since_time} to {until_time}")
#         else:
#             # Fall back to calculating from time_window_minutes
#             current_time = datetime.now(timezone.utc)
#             since_time = current_time - timedelta(minutes=time_window_minutes)
#             until_time = current_time
#             logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
#         # Get the metrics collector
#         metrics_collector = ctx.deps.metrics_collector
        
#         # Get pod metrics
#         metrics_result = await metrics_collector.get_pod_metrics(
#             entity_guid=entity_guid,
#             since_time=since_time,
#             until_time=until_time,
#             metrics=metrics
#         )
        
#         return metrics_result
        
#     except Exception as e:
#         logger.error(f"Error getting pod metrics: {str(e)}")
#         return {
#             "error": str(e),
#             "message": "Failed to get pod metrics",
#             "entity_guid": entity_guid
#         }
        
# @runbook_agent.tool
# async def get_node_metrics(
#     ctx: RunContext,
#     entity_guid: str,
#     metrics: List[str] = None,
#     time_window_minutes: int = 60
# ) -> Dict[str, Any]:
#     """
#     Get metrics for a Kubernetes node.
    
#     This tool collects various metrics for a node including CPU usage, memory usage,
#     pod count, conditions, disk usage, and network I/O.
    
#     Args:
#         entity_guid: GUID of the node entity
#         metrics: List of metrics to collect (cpu_usage, memory_usage, pod_count, condition, disk_usage, network_io)
#         time_window_minutes: Time window in minutes to look back
        
#     Returns:
#         Metrics for the node over the specified time window
#     """
#     try:
#         # First check if we have time window in state
#         since_time = None
#         until_time = None
        
#         # Get time window from state if available
#         time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
#         if time_window and "since_time" in time_window and "until_time" in time_window:
#             since_time = time_window["since_time"]
#             until_time = time_window["until_time"]
#             logger.info(f"Using time window from state: {since_time} to {until_time}")
#         else:
#             # Fall back to calculating from time_window_minutes
#             current_time = datetime.now(timezone.utc)
#             since_time = current_time - timedelta(minutes=time_window_minutes)
#             until_time = current_time
#             logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
#         # Get the metrics collector
#         metrics_collector = ctx.deps.metrics_collector
        
#         # Get node metrics
#         metrics_result = await metrics_collector.get_node_metrics(
#             entity_guid=entity_guid,
#             since_time=since_time,
#             until_time=until_time,
#             metrics=metrics
#         )
        
#         return metrics_result
        
#     except Exception as e:
#         logger.error(f"Error getting node metrics: {str(e)}")
#         return {
#             "error": str(e),
#             "message": "Failed to get node metrics",
#             "entity_guid": entity_guid
#         }
        
# @runbook_agent.tool
# async def get_application_metrics(
#     ctx: RunContext,
#     entity_guid: str,
#     metrics: List[str] = None,
#     time_window_minutes: int = 60
# ) -> Dict[str, Any]:
#     """
#     Get metrics for an application.
    
#     This tool collects various metrics for an application including response time,
#     throughput, error rate, and Apdex score.
    
#     Args:
#         entity_guid: GUID of the application entity
#         metrics: List of metrics to collect (response_time, throughput, error_rate, apdex)
#         time_window_minutes: Time window in minutes to look back
        
#     Returns:
#         Metrics for the application over the specified time window
#     """
#     try:
#         # First check if we have time window in state
#         since_time = None
#         until_time = None
        
#         # Get time window from state if available
#         time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
#         if time_window and "since_time" in time_window and "until_time" in time_window:
#             since_time = time_window["since_time"]
#             until_time = time_window["until_time"]
#             logger.info(f"Using time window from state: {since_time} to {until_time}")
#         else:
#             # Fall back to calculating from time_window_minutes
#             current_time = datetime.now(timezone.utc)
#             since_time = current_time - timedelta(minutes=time_window_minutes)
#             until_time = current_time
#             logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
#         # Get the metrics collector
#         metrics_collector = ctx.deps.metrics_collector
        
#         # Get application metrics
#         metrics_result = await metrics_collector.get_application_metrics(
#             entity_guid=entity_guid,
#             since_time=since_time,
#             until_time=until_time,
#             metrics=metrics
#         )
        
#         return metrics_result
        
#     except Exception as e:
#         logger.error(f"Error getting application metrics: {str(e)}")
#         return {
#             "error": str(e),
#             "message": "Failed to get application metrics",
#             "entity_guid": entity_guid
#         }
        
# @runbook_agent.tool
# async def get_host_metrics(
#     ctx: RunContext,
#     entity_guid: str,
#     metrics: List[str] = None,
#     time_window_minutes: int = 60
# ) -> Dict[str, Any]:
#     """
#     Get metrics for a host.
    
#     This tool collects various metrics for a host including CPU usage, memory usage,
#     disk usage, and network I/O.
    
#     Args:
#         entity_guid: GUID of the host entity
#         metrics: List of metrics to collect (cpu_usage, memory_usage, disk_usage, network_io)
#         time_window_minutes: Time window in minutes to look back
        
#     Returns:
#         Metrics for the host over the specified time window
#     """
#     try:
#         # First check if we have time window in state
#         since_time = None
#         until_time = None
        
#         # Get time window from state if available
#         time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
#         if time_window and "since_time" in time_window and "until_time" in time_window:
#             since_time = time_window["since_time"]
#             until_time = time_window["until_time"]
#             logger.info(f"Using time window from state: {since_time} to {until_time}")
#         else:
#             # Fall back to calculating from time_window_minutes
#             current_time = datetime.now(timezone.utc)
#             since_time = current_time - timedelta(minutes=time_window_minutes)
#             until_time = current_time
#             logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
#         # Get the metrics collector
#         metrics_collector = ctx.deps.metrics_collector
        
#         # Get host metrics
#         metrics_result = await metrics_collector.get_host_metrics(
#             entity_guid=entity_guid,
#             since_time=since_time,
#             until_time=until_time,
#             metrics=metrics
#         )
        
#         return metrics_result
        
#     except Exception as e:
#         logger.error(f"Error getting host metrics: {str(e)}")
#         return {
#             "error": str(e),
#             "message": "Failed to get host metrics",
#             "entity_guid": entity_guid
#         }


# @runbook_agent.tool
# async def check_entity_exists(
#     ctx: RunContext,
#     entity_guid: str
# ) -> Dict[str, Any]:
#     """
#     Check if an entity exists and get its basic information.
    
#     Args:
#         entity_guid: GUID of the entity to check
        
#     Returns:
#         Dictionary with entity information or error
#     """
#     try:
#         metrics_collector = ctx.deps.metrics_collector
#         runbook_service = get_runbook_service()
#         entity_details = await metrics_collector.get_entity_details(entity_guid)
        
#         if not entity_details:
#             return {
#                 "exists": False,
#                 "entity_guid": entity_guid,
#                 "error": "Entity not found"
#             }
            
#         # Use RunbookService to extract entity details
#         entity_info = runbook_service.extract_entity_details(entity_details)
        
#         return {
#             "exists": True,
#             "entity_guid": entity_guid,
#             "entity_name": entity_info.get("entity_name"),
#             "entity_type": entity_info.get("entity_type"),
#             "cluster": entity_info.get("cluster_name"),
#             "namespace": entity_info.get("namespace")
#         }
        
#     except Exception as e:
#         logger.error(f"Error checking entity existence: {str(e)}")
#         return {
#             "exists": False,
#             "entity_guid": entity_guid,
#             "error": str(e)
#         }


# @runbook_agent.tool
# async def get_pod_network_metrics(
#     ctx: RunContext,
#     entity_guid: str,
#     time_window_minutes: int = 30
# ) -> Dict[str, Any]:
#     """
#     Get network metrics for a Kubernetes pod.
    
#     Args:
#         entity_guid: GUID of the pod entity
#         time_window_minutes: Time window in minutes to look back
        
#     Returns:
#         Network metrics for the pod over the specified time window
#     """
#     try:
#         # First check if we have time window in state
#         since_time = None
#         until_time = None
        
#         # Get time window from state if available
#         time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
#         if time_window and "since_time" in time_window and "until_time" in time_window:
#             since_time = time_window["since_time"]
#             until_time = time_window["until_time"]
#             logger.info(f"Using time window from state: {since_time} to {until_time}")
#         else:
#             # Fall back to calculating from time_window_minutes
#             current_time = datetime.now(timezone.utc)
#             since_time = current_time - timedelta(minutes=time_window_minutes)
#             until_time = current_time
#             logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
#         # Get the metrics collector
#         metrics_collector = ctx.deps.metrics_collector
        
#         # Get pod network metrics
#         metrics_result = await metrics_collector.get_pod_network_metrics(
#             entity_guid=entity_guid,
#             since_time=since_time,
#             until_time=until_time
#         )
        
#         return metrics_result
        
#     except Exception as e:
#         logger.error(f"Error getting pod network metrics: {str(e)}")
#         return {
#             "error": str(e),
#             "message": "Failed to get pod network metrics",
#             "entity_guid": entity_guid
#         }

@runbook_agent.tool
async def get_pod_volume_metrics(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 30
) -> Dict[str, Any]:
    """
    Get volume metrics for a Kubernetes pod.
    
    Args:
        entity_guid: GUID of the pod entity
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Volume metrics for the pod over the specified time window
    """
    try:
        # First check if we have time window in state
        since_time = None
        until_time = None
        
        # Get time window from state if available
        time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            logger.info(f"Using time window from state: {since_time} to {until_time}")
        else:
            # Fall back to calculating from time_window_minutes
            current_time = datetime.now(timezone.utc)
            since_time = current_time - timedelta(minutes=time_window_minutes)
            until_time = current_time
            logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get pod volume metrics
        metrics_result = await metrics_collector.get_pod_volume_metrics(
            entity_guid=entity_guid,
            since_time=since_time,
            until_time=until_time
        )
        
        return metrics_result
        
    except Exception as e:
        logger.error(f"Error getting pod volume metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get pod volume metrics",
            "entity_guid": entity_guid
        }

@runbook_agent.tool
async def get_node_disk_metrics(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 30
) -> Dict[str, Any]:
    """
    Get disk usage metrics for a Kubernetes node.
    
    Args:
        entity_guid: GUID of the node entity
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Disk usage metrics for the node over the specified time window
    """
    try:
        # First check if we have time window in state
        since_time = None
        until_time = None
        
        # Get time window from state if available
        time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            logger.info(f"Using time window from state: {since_time} to {until_time}")
        else:
            # Fall back to calculating from time_window_minutes
            current_time = datetime.now(timezone.utc)
            since_time = current_time - timedelta(minutes=time_window_minutes)
            until_time = current_time
            logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get entity details first
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            return {"error": f"Node entity {entity_guid} not found"}
        
        # Verify it's a node
        entity_type = entity_details.get("type", "")
        if not entity_type.startswith("KUBERNETES_NODE"):
            return {"error": f"Entity {entity_guid} is not a Kubernetes node"}
        
        # Extract node name and cluster from entity details
        node_name = entity_details.get("name", "unknown")
        cluster_name = None
        
        # Extract cluster from tags
        tags = entity_details.get("tags", [])
        for tag in tags:
            if tag.get("key") == "clusterName":
                values = tag.get("values", [])
                if values:
                    cluster_name = values[0]
                    break
        
        if not cluster_name:
            logger.warning(f"Cluster name not found for node {node_name}")
            cluster_name = "unknown"
        
        # Convert to milliseconds for queries
        since_time_ms = int(since_time.timestamp() * 1000)
        until_time_ms = int(until_time.timestamp() * 1000)
        
        # Get disk metrics using NodeEntity
        disk_metrics = ctx.deps.metrics_collector.node_entity.get_disk_usage(
            node_name=node_name,
            cluster_name=cluster_name,
            since_time_ms=since_time_ms,
            until_time_ms=until_time_ms
        )
        
        return {
            "entity_guid": entity_guid,
            "node_name": node_name,
            "cluster_name": cluster_name,
            "disk_metrics": disk_metrics,
            "time_window": {
                "since": since_time.isoformat(),
                "until": until_time.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting node disk metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get node disk metrics",
            "entity_guid": entity_guid
        }

@runbook_agent.tool
async def get_node_network_metrics(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 30
) -> Dict[str, Any]:
    """
    Get network metrics for a Kubernetes node.
    
    Args:
        entity_guid: GUID of the node entity
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Network metrics for the node over the specified time window
    """
    try:
        # First check if we have time window in state
        since_time = None
        until_time = None
        
        # Get time window from state if available
        time_window = ctx.deps.get_time_window() if hasattr(ctx.deps, 'get_time_window') else {}
        
        if time_window and "since_time" in time_window and "until_time" in time_window:
            since_time = time_window["since_time"]
            until_time = time_window["until_time"]
            logger.info(f"Using time window from state: {since_time} to {until_time}")
        else:
            # Fall back to calculating from time_window_minutes
            current_time = datetime.now(timezone.utc)
            since_time = current_time - timedelta(minutes=time_window_minutes)
            until_time = current_time
            logger.info(f"Using calculated time window: {since_time} to {until_time}")
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get entity details first
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            return {"error": f"Node entity {entity_guid} not found"}
        
        # Verify it's a node
        entity_type = entity_details.get("type", "")
        if not entity_type.startswith("KUBERNETES_NODE"):
            return {"error": f"Entity {entity_guid} is not a Kubernetes node"}
        
        # Extract node name and cluster from entity details
        node_name = entity_details.get("name", "unknown")
        cluster_name = None
        
        # Extract cluster from tags
        tags = entity_details.get("tags", [])
        for tag in tags:
            if tag.get("key") == "clusterName":
                values = tag.get("values", [])
                if values:
                    cluster_name = values[0]
                    break
        
        if not cluster_name:
            logger.warning(f"Cluster name not found for node {node_name}")
            cluster_name = "unknown"
        
        # Convert to milliseconds for queries
        since_time_ms = int(since_time.timestamp() * 1000)
        until_time_ms = int(until_time.timestamp() * 1000)
        
        # Get network metrics using NodeEntity
        network_metrics = ctx.deps.metrics_collector.node_entity.get_network_io(
            node_name=node_name,
            cluster_name=cluster_name,
            since_time_ms=since_time_ms,
            until_time_ms=until_time_ms
        )
        
        return {
            "entity_guid": entity_guid,
            "node_name": node_name,
            "cluster_name": cluster_name,
            "network_metrics": network_metrics,
            "time_window": {
                "since": since_time.isoformat(),
                "until": until_time.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting node network metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get node network metrics",
            "entity_guid": entity_guid
        }

@runbook_agent.tool
async def get_node_allocatable_resources(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 30
) -> Dict[str, Any]:
    """
    Get allocatable resources for a Kubernetes node.
    
    Args:
        entity_guid: GUID of the node entity
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Allocatable resources for the node over the specified time window
    """
    try:
        # Calculate time window
        current_time = datetime.now(timezone.utc)
        since_time = current_time - timedelta(minutes=time_window_minutes)
        
        # Get the metrics collector
        metrics_collector = ctx.deps.metrics_collector
        
        # Get entity details first
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if not entity_details:
            return {"error": f"Node entity {entity_guid} not found"}
        
        # Verify it's a node
        entity_type = entity_details.get("type", "")
        if not entity_type.startswith("KUBERNETES_NODE"):
            return {"error": f"Entity {entity_guid} is not a Kubernetes node"}
        
        # Extract node name and cluster from entity details
        node_name = entity_details.get("name", "unknown")
        cluster_name = None
        
        # Extract cluster from tags
        tags = entity_details.get("tags", [])
        for tag in tags:
            if tag.get("key") == "clusterName":
                values = tag.get("values", [])
                if values:
                    cluster_name = values[0]
                    break
        
        if not cluster_name:
            logger.warning(f"Cluster name not found for node {node_name}")
            cluster_name = "unknown"
        
        # Convert to milliseconds for queries
        since_time_ms = int(since_time.timestamp() * 1000)
        until_time_ms = int(until_time.timestamp() * 1000)
        
        # Get allocatable resources using NodeEntity
        allocatable_resources = ctx.deps.metrics_collector.node_entity.get_allocatable_resources(
            node_name=node_name,
            cluster_name=cluster_name,
            since_time_ms=since_time_ms,
            until_time_ms=until_time_ms
        )
        
        return {
            "entity_guid": entity_guid,
            "node_name": node_name,
            "cluster_name": cluster_name,
            "allocatable_resources": allocatable_resources,
            "time_window": {
                "since": since_time.isoformat(),
                "until": until_time.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting node allocatable resources: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get node allocatable resources",
            "entity_guid": entity_guid
        }

@runbook_agent.tool
async def get_cluster_metrics(
    ctx: RunContext,
    cluster_name: str,
    metrics: List[str] = None,
    time_window_minutes: int = 60,
    limit: int = 100
) -> Dict[str, Any]:
    """
    Get metrics for a Kubernetes cluster.
    
    Args:
        cluster_name: Name of the Kubernetes cluster
        metrics: List of metrics to retrieve
        time_window_minutes: Time window in minutes to look back
        limit: Maximum number of results to return
        
    Returns:
        Dictionary with cluster metrics
    """
    try:
        # Get metrics collector from context
        metrics_collector = ctx.deps.metrics_collector
        
        # Get time window from context if available
        time_window = ctx.deps.get_time_window()
        since_time = time_window.get("since_time")
        until_time = time_window.get("until_time")
        
        # If we don't have a time window from context, calculate it from time_window_minutes
        if not since_time or not until_time:
            until_time = datetime.now(timezone.utc)
            since_time = until_time - timedelta(minutes=time_window_minutes)
        
        logger.info(f"Getting cluster metrics for {cluster_name} from {since_time} to {until_time}")
        
        # Get default metrics if none specified
        if not metrics:
            metrics = [
                "node_count", 
                "pod_count", 
                "deployment_count",
                "namespace_count",
                "cluster_cpu_utilization",
                "cluster_memory_utilization"
            ]
        
        # Get cluster metrics
        metrics_result = await metrics_collector.get_cluster_metrics(
            cluster_name=cluster_name,
            since_time=since_time,
            until_time=until_time,
            metrics=metrics,
            limit=limit
        )
        
        return metrics_result
        
    except Exception as e:
        logger.error(f"Error getting cluster metrics: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to get cluster metrics",
            "cluster_name": cluster_name
        }


async def main():
    """
    Test function for the runbook agent.
    """
    # Initialize dependencies
    logger.info("Initializing dependencies")
    
    # Load environment variables
    dotenv.load_dotenv()
    
    # Initialize the metrics collector
    metrics_collector = MetricsCollector()
    
    # Initialize OpenAI client
    azure_openai_enabled = "AZURE_OPENAI_ENDPOINT" in os.environ and "AZURE_OPENAI_API_KEY" in os.environ
    
    if azure_openai_enabled:
        openai_client = AsyncAzureOpenAI(
            azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
            azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
            api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
            api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
        )
    else:
        openai_client = AsyncOpenAI(
            api_key=os.environ.get("OPENAI_API_KEY")
        )
    
    # Initialize New Relic GraphQL client
    api_key = os.environ.get("NEWRELIC_API_KEY")
    account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        logger.error("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set in environment variables")
        return
    
    # Create the New Relic GraphQL client
    graphql_client = NewRelicGraphQLClient(
        api_key=api_key,
        account_id=account_id,
        region=Region.US,
        debug=True
    )
    
    # Create the logs client using the GraphQL client
    logs_client = NewRelicLogsClient(client=graphql_client, debug=True)
    
    # Create sample alert info with condition details
    sample_alert_info = {
        "issueId": "a271ddf7-a9a8-4dc1-beea-39cca4fa1987", 
        "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/a271ddf7-a9a8-4dc1-beea-39cca4fa1987?notifier=WEBHOOK", 
        "title": "agent-management-background-container-service query result is > 10.0 on 'Pod with CrashLoopBackOff -- '", 
        "priority": "CRITICAL", 
        "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"], 
        "impactedEntities": ["agent-management-background-container-service"], 
        "totalIncidents": "1", 
        "state": "ACTIVATED", 
        "trigger": "STATE_CHANGE", 
        "createdAt": *************, 
        "updatedAt": *************, 
        "sources": ["newrelic"], 
        "alertPolicyNames": ["Neurons k8s Infra - Critical"], 
        "alertConditionNames": ["Pod with CrashLoopBackOff -- "], 
        "workflowName": "obv-ai-processing-neurons", 
        "chartLink": "https://gorgon.nr-assets.net/image/7cd23d09-c8aa-4e97-928a-d82a5bcfc2c9?config.legend.enabled=false&width=400&height=210", 
        "product": "neurons", 
        "nr_region": "us",
        
        # Additional fields needed for processing
        "alert_category": "castai_workload_autoscaler",
        "condition_id": "25541032",
        "triggered_at": datetime.fromtimestamp(************* / 1000).isoformat(),
        
        # Include time window information (from epoch milliseconds)
        "since_time_ms": ************* - (2700 * 1000),  # 2700 seconds before triggered_at
        "until_time_ms": *************,
        "threshold_duration": 2700,
        "aggregation_window": 900,
        
        # Adding human-readable time for reference
        "since_time": datetime.fromtimestamp((************* - (2700 * 1000)) / 1000).isoformat(),
        "until_time": datetime.fromtimestamp(************* / 1000).isoformat(),
        
        # Additional condition details
        "condition_details": {
            "description": "Pod is in CrashLoopBackOff.",
            "enabled": True,
            "id": "25541032",
            "name": "Pod with CrashLoopBackOff -- ",
            "policyId": "2405324",
            "runbookUrl": None,
            "signal": {
                "aggregationDelay": 120,
                "aggregationMethod": "EVENT_FLOW",
                "aggregationTimer": None,
                "aggregationWindow": 900
            },
            "terms": [
                {
                    "operator": "ABOVE",
                    "priority": "CRITICAL",
                    "threshold": 10,
                    "thresholdDuration": 2700,
                    "thresholdOccurrences": "AT_LEAST_ONCE"
                }
            ],
            "type": "STATIC",
            "violationTimeLimitSeconds": 21600,
            "policyName": "Neurons k8s Infra - Critical",
            "formatted_threshold": "ABOVE 10.0 for 2700 seconds",
            "query": "select count(*) from K8sContainerSample where status='Waiting' and reason='CrashLoopBackOff'"
        }
    }
    
    # Set up dependencies for the agent
    deps = RunbookAgentDeps(
        openai_client=openai_client,
        metrics_collector=metrics_collector,
        logs_client=logs_client,  # Pass the properly initialized logs client
        state=sample_alert_info  # Pass the sample alert info as state
    )
    
    # Get runbook service
    runbook_service = get_runbook_service()
    
    # Test the runbook service directly
    logger.info("Testing RunbookService capabilities with dynamic parameters")
    
    # Get available runbooks
    all_runbooks = runbook_service.get_all_runbooks()
    logger.info(f"Found {len(all_runbooks)} runbooks")
    
    # Get recommended runbooks for pod issues
    pod_runbooks = runbook_service.get_recommended_runbooks(
        alert_category="kubernetes_crashloopbackoff",
        entity_types=["KUBERNETES_POD"],
        max_results=2
    )
    logger.info(f"Recommended pod runbooks: {[rb['id'] for rb in pod_runbooks]}")
    
    # Test get_pod_logs function with a proper logs client
    entity_guid = "MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"  # Sample entity GUID from alert info
    logger.info(f"Testing get_pod_logs with entity GUID: {entity_guid}")
    
    
    # Test dynamically evaluating time windows
    # Example from runbook: time_window_minutes: "{max(aggregation_window/60, 30)}"
    logger.info("Testing dynamic parameter evaluation:")
    
    # Test the agent with a recommendation query
    test_prompt = "What runbooks should I use for a pod with crashloopbackoff issues?"
    logger.info(f"Testing runbook agent with prompt: {test_prompt}")
    
    # Run the agent
    result = await runbook_agent.run(test_prompt, deps=deps)
    logger.info(f"Agent recommendation response: {result.data}")
    
    # Test entity existence check with a sample entity
    # Note: Replace with a real entity GUID for testing
    test_entity_guid = "MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"
    
    logger.info(f"Testing entity existence check with entity: {test_entity_guid}")
    
    # Skip the check_entity_exists call since it requires RunContext
    # Instead, we'll simulate the entity check result
    entity_check = {
        "exists": False,
        "entity_guid": test_entity_guid,
        "entity_name": "test-pod",
        "entity_type": "KUBERNETES_POD",
        "cluster": "test-cluster",
        "namespace": "test-namespace"
    }
    
    if entity_check["exists"]:
        # Demonstrate the improved tool execution flow using RunbookService directly
        logger.info("Demonstrating improved tool execution using RunbookService with state")
        
        # Extract entity info
        entity_info = {
            "entity_guid": entity_check["entity_guid"],
            "entity_name": entity_check["entity_name"],
            "entity_type": entity_check["entity_type"],
            "cluster_name": entity_check["cluster"],
            "namespace": entity_check["namespace"]
        }
        
        # Execute a tool using RunbookService with state
        tool_result = await runbook_service.execute_tool(
            metrics_collector=metrics_collector,
            tool_name="get_pod_metrics",
            entity_info=entity_info,
            parameters={
                "metrics": ["cpu_usage", "memory_usage", "restart_count"],
                "time_window_minutes": "{max(threshold_duration/60, 30)}"  # Dynamic parameter
            },
            state=sample_alert_info
        )
        
        # Generate a summary
        summary = runbook_service.generate_step_summary("Analyze Pod Metrics", tool_result)
        
        logger.info(f"Tool execution summary: {summary}")
        logger.info(f"Tool result: {tool_result}")
    else:
        logger.info(f"Test entity does not exist: {entity_check}")
    
    # Demo with execute_tool directly
    logger.info("Testing execute_tool with direct tool executor")
    tool_result = await tool_executor(
        metrics_collector=metrics_collector,
        tool_name="get_pod_metrics",
        entity_guid=test_entity_guid,
        parameters={
            "metrics": ["cpu_usage", "memory_usage", "restart_count"],
            "time_window_minutes": 30  # Fixed parameter instead of dynamic
        },
        state=sample_alert_info
    )
    logger.info(f"Tool executor result: {tool_result}")
    
    # Test runbook execution with state
    if pod_runbooks:
        logger.info("Testing runbook execution with state")
        runbook_id = pod_runbooks[0]["id"]
        
        # Create a sample call to execute_runbook_steps with manually created context
        logger.info(f"Would execute runbook {runbook_id} with entity {test_entity_guid}")
        logger.info("Skipping actual execution due to RunContext initialization requirements")


@runbook_agent.tool
async def map_entity_relationships(
    ctx: RunContext,
    entity_guid: str
) -> Dict[str, Any]:
    """
    Map relationships between the specified entity and other entities.
    
    Args:
        entity_guid: The GUID of the entity to map relationships for
        
    Returns:
        Dict containing the entity and its relationships
    """
    deps: RunbookAgentDeps = ctx.deps
    
    # Get entity details from New Relic
    try:
        entity_details = await deps.metrics_collector.get_entity_by_guid(entity_guid)
        if not entity_details:
            return {
                "success": False,
                "message": f"Entity with GUID {entity_guid} not found",
                "relationships": []
            }
            
        # Get entity relationships from New Relic
        relationships = await deps.metrics_collector.get_entity_relationships(entity_guid)
        
        # Map relationships to a more usable format
        mapped_relationships = []
        for rel in relationships:
            source = rel.get("source", {})
            target = rel.get("target", {})
            
            mapped_relationships.append({
                "source_entity": {
                    "guid": source.get("guid"),
                    "name": source.get("name"),
                    "type": source.get("entity_type")
                },
                "target_entity": {
                    "guid": target.get("guid"),
                    "name": target.get("name"),
                    "type": target.get("entity_type")
                },
                "relationship_type": rel.get("type")
            })
            
        return {
            "success": True,
            "entity": {
                "guid": entity_details.get("guid"),
                "name": entity_details.get("name"),
                "type": entity_details.get("type")
            },
            "relationships": mapped_relationships
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Error mapping entity relationships: {str(e)}",
            "relationships": []
        }


@runbook_agent.tool
async def get_related_entities(
    ctx: RunContext,
    entity_guid: str,
    entity_types: List[str] = None
) -> Dict[str, Any]:
    """
    Get related entities of specific types for the given entity.
    
    Args:
        entity_guid: The GUID of the entity to get related entities for
        entity_types: Optional list of entity types to filter by
        
    Returns:
        Dict containing the related entities
    """
    deps: RunbookAgentDeps = ctx.deps
    
    try:
        # Get entity relationships from New Relic
        relationships = await deps.metrics_collector.get_entity_relationships(entity_guid)
        
        # Filter and extract related entities
        related_entities = []
        for rel in relationships:
            source = rel.get("source", {})
            target = rel.get("target", {})
            
            # Add source entity if it's not the original entity and matches type filter
            if source.get("guid") != entity_guid:
                source_type = source.get("entity_type")
                if not entity_types or source_type in entity_types:
                    related_entities.append({
                        "guid": source.get("guid"),
                        "name": source.get("name"),
                        "type": source_type,
                        "relationship": rel.get("type"),
                        "direction": "source"
                    })
            
            # Add target entity if it's not the original entity and matches type filter
            if target.get("guid") != entity_guid:
                target_type = target.get("entity_type")
                if not entity_types or target_type in entity_types:
                    related_entities.append({
                        "guid": target.get("guid"),
                        "name": target.get("name"),
                        "type": target_type,
                        "relationship": rel.get("type"),
                        "direction": "target"
                    })
        
        return {
            "success": True,
            "related_entities": related_entities
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Error getting related entities: {str(e)}",
            "related_entities": []
        }


@runbook_agent.tool
async def get_container_exit_codes(
    ctx: RunContext,
    entity_guid: str,
    time_window_minutes: int = 60
) -> Dict[str, Any]:
    """
    Get container exit codes for a container entity.
    
    Args:
        entity_guid: The GUID of the container entity
        time_window_minutes: Time window in minutes to look back
        
    Returns:
        Dict containing container exit codes and their counts
    """
    deps: RunbookAgentDeps = ctx.deps
    time_window = deps.get_time_window()
    
    try:
        # Get time window parameters
        since_time = time_window.get("since_time")
        until_time = time_window.get("until_time")
        
        if not since_time or not until_time:
            # Use relative time window if exact times not available
            until_time = datetime.utcnow()
            since_time = until_time - timedelta(minutes=time_window_minutes)
            
        # Format times for NRQL
        since_time_ms = int(since_time.timestamp() * 1000)
        until_time_ms = int(until_time.timestamp() * 1000)
        
        # NRQL query to get container exit codes
        nrql = f"""
        SELECT uniqueCount(containerExitCode), containerExitCode, latest(containerState)
        FROM K8sContainerSample
        WHERE entityGuid = '{entity_guid}'
        SINCE {since_time_ms} UNTIL {until_time_ms}
        FACET containerExitCode
        LIMIT 10
        """
        
        # Execute NRQL query
        result = await deps.metrics_collector.execute_nrql_query(nrql)
        
        # Parse results
        exit_codes = []
        for item in result.get("results", []):
            exit_codes.append({
                "exit_code": item.get("containerExitCode"),
                "count": item.get("uniqueCount.containerExitCode"),
                "container_state": item.get("latest.containerState")
            })
        
        return {
            "success": True,
            "exit_codes": exit_codes,
            "nrql_query": nrql
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Error getting container exit codes: {str(e)}",
            "exit_codes": []
        }


# @runbook_agent.tool
# async def get_container_logs(
#     ctx: RunContext,
#     entity_guid: str,
#     tail: int = 100,
#     time_window_minutes: int = 60
# ) -> Dict[str, Any]:
#     """
#     Get container logs for a container entity.
    
#     Args:
#         entity_guid: The GUID of the container entity
#         tail: Number of log lines to retrieve
#         time_window_minutes: Time window in minutes to look back
        
#     Returns:
#         Dict containing container logs
#     """
#     deps: RunbookAgentDeps = ctx.deps
#     time_window = deps.get_time_window()
    
#     try:
#         # Get time window parameters
#         since_time = time_window.get("since_time")
#         until_time = time_window.get("until_time")
        
#         if not since_time or not until_time:
#             # Use relative time window if exact times not available
#             until_time = datetime.utcnow()
#             since_time = until_time - timedelta(minutes=time_window_minutes)
            
#         # Format times for NRQL
#         since_time_ms = int(since_time.timestamp() * 1000)
#         until_time_ms = int(until_time.timestamp() * 1000)
        
#         # NRQL query to get container logs
#         nrql = f"""
#         SELECT timestamp, message 
#         FROM Log 
#         WHERE entityGuid = '{entity_guid}'
#         SINCE {since_time_ms} UNTIL {until_time_ms}
#         ORDER BY timestamp DESC 
#         LIMIT {tail}
#         """
        
#         # Execute NRQL query
#         result = await deps.metrics_collector.execute_nrql_query(nrql)
        
#         # Parse results
#         logs = []
#         for item in result.get("results", []):
#             logs.append({
#                 "timestamp": item.get("timestamp"),
#                 "message": item.get("message")
#             })
        
#         return {
#             "success": True,
#             "logs": logs,
#             "nrql_query": nrql
#         }
#     except Exception as e:
#         return {
#             "success": False,
#             "message": f"Error getting container logs: {str(e)}",
#             "logs": []
#         }


# @runbook_agent.tool
# async def compare_resource_usage_vs_limits(
#     ctx: RunContext,
#     entity_guid: str,
#     time_window_minutes: int = 60
# ) -> Dict[str, Any]:
#     """
#     Compare resource usage vs limits for a pod or container.
    
#     Args:
#         entity_guid: The GUID of the pod or container entity
#         time_window_minutes: Time window in minutes to look back
        
#     Returns:
#         Dict containing resource usage vs limits comparison
#     """
#     deps: RunbookAgentDeps = ctx.deps
#     time_window = deps.get_time_window()
    
#     try:
#         # Get entity details to determine if it's a pod or container
#         entity_details = await deps.metrics_collector.get_entity_by_guid(entity_guid)
#         if not entity_details:
#             return {
#                 "success": False,
#                 "message": f"Entity with GUID {entity_guid} not found"
#             }
            
#         entity_type = entity_details.get("type")
        
#         # Get time window parameters
#         since_time = time_window.get("since_time")
#         until_time = time_window.get("until_time")
        
#         if not since_time or not until_time:
#             # Use relative time window if exact times not available
#             until_time = datetime.utcnow()
#             since_time = until_time - timedelta(minutes=time_window_minutes)
            
#         # Format times for NRQL
#         since_time_ms = int(since_time.timestamp() * 1000)
#         until_time_ms = int(until_time.timestamp() * 1000)
        
#         # NRQL query to get resource usage vs limits
#         if entity_type in ["KUBERNETES_CONTAINER", "K8S_CONTAINER"]:
#             nrql = f"""
#             SELECT 
#                 max(cpuUsedCores) as max_cpu_usage,
#                 max(cpuLimitCores) as cpu_limit,
#                 max(cpuUsedCores)/max(cpuLimitCores)*100 as cpu_usage_percent,
#                 max(memoryUsedBytes)/1024/1024 as max_memory_usage_mb,
#                 max(memoryLimitBytes)/1024/1024 as memory_limit_mb,
#                 max(memoryUsedBytes)/max(memoryLimitBytes)*100 as memory_usage_percent
#             FROM K8sContainerSample
#             WHERE entityGuid = '{entity_guid}'
#             SINCE {since_time_ms} UNTIL {until_time_ms}
#             """
#         elif entity_type in ["KUBERNETES_POD", "K8S_POD"]:
#             nrql = f"""
#             SELECT 
#                 max(cpuUsedCores) as max_cpu_usage,
#                 max(cpuLimitCores) as cpu_limit,
#                 max(cpuUsedCores)/max(cpuLimitCores)*100 as cpu_usage_percent,
#                 max(memoryUsedBytes)/1024/1024 as max_memory_usage_mb,
#                 max(memoryLimitBytes)/1024/1024 as memory_limit_mb,
#                 max(memoryUsedBytes)/max(memoryLimitBytes)*100 as memory_usage_percent
#             FROM K8sPodSample
#             WHERE entityGuid = '{entity_guid}'
#             SINCE {since_time_ms} UNTIL {until_time_ms}
#             """
#         else:
#             return {
#                 "success": False,
#                 "message": f"Entity type {entity_type} not supported for resource comparison"
#             }
        
#         # Execute NRQL query
#         result = await deps.metrics_collector.execute_nrql_query(nrql)
        
#         # Parse results
#         if not result.get("results"):
#             return {
#                 "success": False,
#                 "message": "No resource usage data found"
#             }
            
#         data = result.get("results")[0]
        
#         # Analyze resource usage
#         cpu_usage_percent = data.get("cpu_usage_percent", 0)
#         memory_usage_percent = data.get("memory_usage_percent", 0)
        
#         analysis = {
#             "cpu": {
#                 "usage": data.get("max_cpu_usage"),
#                 "limit": data.get("cpu_limit"),
#                 "usage_percent": cpu_usage_percent,
#                 "status": "HIGH" if cpu_usage_percent > 80 else "NORMAL"
#             },
#             "memory": {
#                 "usage_mb": data.get("max_memory_usage_mb"),
#                 "limit_mb": data.get("memory_limit_mb"),
#                 "usage_percent": memory_usage_percent,
#                 "status": "HIGH" if memory_usage_percent > 80 else "NORMAL"
#             }
#         }
        
#         return {
#             "success": True,
#             "resource_analysis": analysis,
#             "nrql_query": nrql
#         }
#     except Exception as e:
#         return {
#             "success": False,
#             "message": f"Error comparing resource usage vs limits: {str(e)}"
#         }


# @runbook_agent.tool
# async def analyze_crash_patterns(
#     ctx: RunContext,
#     entity_guid: str,
#     time_window_minutes: int = 1440  # 24 hours
# ) -> Dict[str, Any]:
#     """
#     Analyze crash patterns for a container.
    
#     Args:
#         entity_guid: The GUID of the container entity
#         time_window_minutes: Time window in minutes to look back
        
#     Returns:
#         Dict containing crash pattern analysis
#     """
#     deps: RunbookAgentDeps = ctx.deps
#     time_window = deps.get_time_window()
    
#     try:
#         # Get time window parameters
#         since_time = time_window.get("since_time")
#         until_time = time_window.get("until_time")
        
#         if not since_time or not until_time:
#             # Use relative time window if exact times not available
#             until_time = datetime.utcnow()
#             since_time = until_time - timedelta(minutes=time_window_minutes)
            
#         # Format times for NRQL
#         since_time_ms = int(since_time.timestamp() * 1000)
#         until_time_ms = int(until_time.timestamp() * 1000)
        
#         # NRQL query to get container restart timeline
#         nrql = f"""
#         SELECT timestamp, containerState, containerExitCode
#         FROM K8sContainerSample
#         WHERE entityGuid = '{entity_guid}'
#         AND containerRestarts IS NOT NULL
#         SINCE {since_time_ms} UNTIL {until_time_ms}
#         ORDER BY timestamp ASC
#         LIMIT 1000
#         """
        
#         # Execute NRQL query
#         result = await deps.metrics_collector.execute_nrql_query(nrql)
        
#         # Parse results to identify crash patterns
#         crashes = []
#         last_state = None
        
#         for item in result.get("results", []):
#             current_state = item.get("containerState")
            
#             # Detect state transitions indicating a crash
#             if last_state == "running" and current_state in ["waiting", "terminated"]:
#                 crashes.append({
#                     "timestamp": item.get("timestamp"),
#                     "exit_code": item.get("containerExitCode")
#                 })
                
#             last_state = current_state
        
#         # Analyze time between crashes
#         crash_intervals = []
#         for i in range(1, len(crashes)):
#             try:
#                 current = datetime.fromisoformat(crashes[i]["timestamp"].replace('Z', '+00:00'))
#                 previous = datetime.fromisoformat(crashes[i-1]["timestamp"].replace('Z', '+00:00'))
#                 interval_seconds = (current - previous).total_seconds()
#                 crash_intervals.append(interval_seconds)
#             except Exception:
#                 continue
        
#         # Calculate average time between crashes
#         avg_interval = sum(crash_intervals) / len(crash_intervals) if crash_intervals else 0
        
#         # Group crashes by exit code
#         exit_code_counts = {}
#         for crash in crashes:
#             code = crash.get("exit_code")
#             if code in exit_code_counts:
#                 exit_code_counts[code] += 1
#             else:
#                 exit_code_counts[code] = 1
        
#         # Determine if crashes are getting more frequent
#         crash_frequency_increasing = False
#         if len(crash_intervals) >= 3:
#             first_half = crash_intervals[:len(crash_intervals)//2]
#             second_half = crash_intervals[len(crash_intervals)//2:]
#             avg_first_half = sum(first_half) / len(first_half) if first_half else 0
#             avg_second_half = sum(second_half) / len(second_half) if second_half else 0
#             crash_frequency_increasing = avg_second_half < avg_first_half
        
#         return {
#             "success": True,
#             "crash_count": len(crashes),
#             "crash_exit_codes": exit_code_counts,
#             "avg_time_between_crashes_seconds": avg_interval,
#             "crash_frequency_increasing": crash_frequency_increasing,
#             "nrql_query": nrql
#         }
#     except Exception as e:
#         return {
#             "success": False,
#             "message": f"Error analyzing crash patterns: {str(e)}"
#         }


# @runbook_agent.tool
# async def execute_alert_category_runbook(
#     ctx: RunContext,
#     alert_category: str,
#     primary_entity_guid: str
# ) -> Dict[str, Any]:
#     """
#     Set up the context for executing a runbook for a specific alert category.
#     This tool should be called at the beginning of runbook execution to establish
#     the alert category and primary entity context.
    
#     Args:
#         alert_category: The category of the alert (e.g., kubernetes_crashloopbackoff)
#         primary_entity_guid: The GUID of the primary entity affected by the alert
        
#     Returns:
#         Dict containing setup information and confirmation
#     """
#     deps: RunbookAgentDeps = ctx.deps
    
#     try:
#         # Get alert category config
#         alert_config = await get_alert_category_config(alert_category)
#         if not alert_config:
#             return {
#                 "success": False,
#                 "message": f"Alert category '{alert_category}' not found"
#             }
        
#         # Get primary entity details
#         primary_entity = await deps.metrics_collector.get_entity_by_guid(primary_entity_guid)
#         if not primary_entity:
#             return {
#                 "success": False,
#                 "message": f"Primary entity with GUID '{primary_entity_guid}' not found"
#             }
        
#         # Set up runbook execution context
#         deps.alert_category = alert_config
#         deps.primary_entity = {
#             "guid": primary_entity.get("guid"),
#             "name": primary_entity.get("name"),
#             "type": primary_entity.get("type"),
#             "is_primary": True
#         }
        
#         # Map entity relationships
#         try:
#             # Get entity relationships
#             relationships_result = await map_entity_relationships(ctx, primary_entity_guid)
            
#             if relationships_result.get("success", False):
#                 # Extract and add related entities
#                 for relationship in relationships_result.get("relationships", []):
#                     source = relationship.get("source_entity", {})
#                     target = relationship.get("target_entity", {})
                    
#                     # Add source if not primary
#                     if source.get("guid") != primary_entity_guid:
#                         if not any(e.get("guid") == source.get("guid") for e in deps.related_entities):
#                             deps.related_entities.append({
#                                 "guid": source.get("guid"),
#                                 "name": source.get("name"),
#                                 "type": source.get("type"),
#                                 "is_primary": False,
#                                 "relationship": relationship.get("relationship_type", "unknown")
#                             })
                    
#                     # Add target if not primary
#                     if target.get("guid") != primary_entity_guid:
#                         if not any(e.get("guid") == target.get("guid") for e in deps.related_entities):
#                             deps.related_entities.append({
#                                 "guid": target.get("guid"),
#                                 "name": target.get("name"),
#                                 "type": target.get("type"),
#                                 "is_primary": False,
#                                 "relationship": relationship.get("relationship_type", "unknown")
#                             })
#         except Exception as e:
#             logger.warning(f"Error mapping entity relationships: {str(e)}")
        
#         # Return confirmation and context summary
#         return {
#             "success": True,
#             "message": f"Successfully set up context for {alert_category} runbook execution",
#             "alert_category": alert_category,
#             "primary_entity": {
#                 "guid": primary_entity_guid,
#                 "name": primary_entity.get("name"),
#                 "type": primary_entity.get("type")
#             },
#             "related_entity_count": len(deps.related_entities),
#             "runbook_steps": len(alert_config.get("runbook", "").strip().split("\n")),
#             "context_ready": True
#         }
#     except Exception as e:
#         logger.error(f"Error setting up alert category runbook context: {str(e)}")
#         return {
#             "success": False,
#             "message": f"Error setting up runbook context: {str(e)}"
#         }


async def get_alert_category_config(category: str) -> Dict[str, Any]:
    """
    Get the configuration for a specific alert category.
    
    Args:
        category: The category of the alert
        
    Returns:
        Dict containing the alert category configuration
    """
    import yaml
    import os
    
    try:
        # Load alert categories config
        config_path = os.path.join("ai_incident_manager", "config", "alert_categories.yaml")
        
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        
        # Find the category
        for cat in config.get("condition_categories", []):
            if cat.get("category") == category:
                return cat
                
        return None
    except Exception as e:
        logger.error(f"Error loading alert category config: {str(e)}")
        return None


def parse_runbook_steps(runbook_text: str) -> List[Dict[str, Any]]:
    """
    Parse runbook steps from the runbook text.
    
    Args:
        runbook_text: The text of the runbook
        
    Returns:
        List of parsed runbook steps
    """
    steps = []
    current_step = None
    
    # Split runbook text into lines
    lines = runbook_text.strip().split("\n")
    
    for line in lines:
        line = line.strip()
        
        # Skip empty lines
        if not line:
            continue
        
        # Check if line starts a new step
        if line.startswith(tuple([f"{i}." for i in range(1, 10)])):
            # Save previous step if exists
            if current_step:
                steps.append(current_step)
            
            # Extract step number and title
            parts = line.split(".", 1)
            if len(parts) > 1:
                title = parts[1].strip()
            else:
                title = line
            
            current_step = {
                "title": title,
                "tools": []
            }
        # Check if line is a tool
        elif line.startswith("-") and "Tool:" in line:
            if current_step:
                # Extract tool name and parameters
                tool_line = line.split("Tool:", 1)[1].strip()
                
                # Parse tool name and parameters
                if "(" in tool_line and ")" in tool_line:
                    tool_name = tool_line.split("(", 1)[0].strip()
                    params_str = tool_line.split("(", 1)[1].rsplit(")", 1)[0].strip()
                    
                    # Parse parameters
                    params = {}
                    if params_str:
                        param_parts = params_str.split(",")
                        for part in param_parts:
                            part = part.strip()
                            if "=" in part:
                                # Key-value parameter
                                key, value = part.split("=", 1)
                                params[key.strip()] = eval_param_value(value.strip())
                            else:
                                # Positional parameter - use position as key
                                position = len(params)
                                params[f"param_{position}"] = eval_param_value(part)
                    
                    current_step["tools"].append({
                        "name": tool_name,
                        "parameters": params
                    })
    
    # Add the last step if exists
    if current_step:
        steps.append(current_step)
    
    return steps


def eval_param_value(value_str: str) -> Any:
    """
    Evaluate a parameter value string to convert it to the appropriate Python type.
    
    Args:
        value_str: The parameter value as a string
        
    Returns:
        The parameter value converted to the appropriate type
    """
    # Try to evaluate the value as a Python literal
    try:
        # Handle lists
        if value_str.startswith("[") and value_str.endswith("]"):
            # Split by comma and handle string literals
            items = []
            for item in value_str[1:-1].split(","):
                item = item.strip()
                if item.startswith('"') and item.endswith('"'):
                    items.append(item[1:-1])
                elif item.startswith("'") and item.endswith("'"):
                    items.append(item[1:-1])
                else:
                    items.append(item)
            return items
            
        # Handle string literals
        if (value_str.startswith('"') and value_str.endswith('"')) or (value_str.startswith("'") and value_str.endswith("'")):
            return value_str[1:-1]
            
        # Handle numbers
        if value_str.isdigit():
            return int(value_str)
        
        try:
            return float(value_str)
        except ValueError:
            pass
        
        # Handle booleans
        if value_str.lower() == "true":
            return True
        if value_str.lower() == "false":
            return False
            
        # If all else fails, return the string as is
        return value_str
    except Exception:
        # If evaluation fails, return the string as is
        return value_str


async def execute_runbook_step(ctx: RunContext, step: Dict[str, Any]) -> RunbookStepResult:
    """
    Execute a single runbook step.
    
    Args:
        ctx: The run context
        step: The step to execute
        
    Returns:
        RunbookStepResult containing the results of executing the step
    """
    deps: RunbookAgentDeps = ctx.deps
    
    # Initialize step result
    step_result = RunbookStepResult(
        step_title=step.get("title", "Unknown Step"),
        step_description="",
        tool="",
        parameters={},
        result={},
        summary=""
    )
    
    try:
        # Check if step has tools
        if not step.get("tools"):
            step_result.summary = f"No tools defined for step: {step.get('title')}"
            return step_result
        
        # Get the first tool (for now, we'll just execute the first tool in each step)
        tool = step["tools"][0]
        tool_name = tool.get("name")
        parameters = tool.get("parameters", {})
        
        # Set tool and parameters in result
        step_result.tool = tool_name
        step_result.parameters = parameters
        
        # Check if tool function exists
        if not hasattr(ctx.agent.tools, tool_name):
            step_result.summary = f"Tool '{tool_name}' is not available"
            return step_result
        
        # Update parameters with entity information if needed
        for key, value in parameters.items():
            # If parameter value is a placeholder, try to substitute it
            if isinstance(value, str):
                # Entity placeholder
                if value in ["pod_name", "container_name", "node_name", "deployment_name"]:
                    entity_type_map = {
                        "pod_name": ["KUBERNETES_POD", "K8S_POD"],
                        "container_name": ["KUBERNETES_CONTAINER", "K8S_CONTAINER"],
                        "node_name": ["KUBERNETES_NODE", "K8S_NODE"],
                        "deployment_name": ["KUBERNETES_DEPLOYMENT", "K8S_DEPLOYMENT"]
                    }
                    
                    entity_types = entity_type_map.get(value, [])
                    for entity_type in entity_types:
                        entity = deps.get_entity_by_type(entity_type)
                        if entity:
                            parameters[key] = entity.get("guid")
                            # Update step result with entity info
                            step_result.entity_guid = entity.get("guid")
                            step_result.entity_name = entity.get("name")
                            step_result.entity_type = entity.get("type")
                            break
        
        # Get the tool function
        tool_fn = getattr(ctx.agent.tools, tool_name)
        
        # Call the tool function with parameters
        result = await tool_fn(**parameters)
        
        # Set result in step result
        step_result.result = result
        
        # Check if result has NRQL query
        if isinstance(result, dict) and "nrql_query" in result:
            step_result.nrql_query = result["nrql_query"]
            
            # Determine data type based on NRQL query
            if "Log" in result["nrql_query"]:
                step_result.data_type = "log"
            elif any(metric_table in result["nrql_query"] for metric_table in 
                   ["K8sContainerSample", "K8sPodSample", "K8sNodeSample", "SystemSample"]):
                step_result.data_type = "metric"
            else:
                step_result.data_type = "information"
        
        # Generate summary
        if isinstance(result, dict):
            if "success" in result and not result["success"]:
                step_result.summary = result.get("message", "Step execution failed")
                step_result.issues_found = True
            elif "message" in result:
                step_result.summary = result["message"]
            else:
                step_result.summary = f"Executed {tool_name} successfully"
        else:
            step_result.summary = f"Executed {tool_name} successfully"
        
        return step_result
    except Exception as e:
        step_result.summary = f"Error executing step: {str(e)}"
        step_result.issues_found = True
        return step_result


if __name__ == "__main__":
    # Run the main function as an async coroutine
    import asyncio
    
    # Set up logging for direct execution
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("Starting runbook agent test...")
    
    # Run the main function
    asyncio.run(main())
    
    logger.info("Runbook agent test completed.") 