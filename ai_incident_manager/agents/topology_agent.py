"""
Topology Agent - Pydantic AI Agent for analyzing and generating topology data.

This agent provides tools for retrieving and analyzing topology data for incidents,
helping with visualization and understanding of the relationships between entities.
"""

import os
import json
import logging
from dataclasses import dataclass
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIModel
from openai import AsyncAzureOpenAI
import asyncio
from datetime import datetime

from ai_incident_manager.services.topology_service import get_topology_service
from lib.new_relic.query import NewRelicQueryClient
from ai_incident_manager.services.mongodb_service import get_mongodb_service

import dotenv

# Load environment variables
dotenv.load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize OpenAI client
required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
azure_openai_enabled = all(var in os.environ for var in required_azure_vars)

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    model = OpenAIModel(os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"), openai_client=openai_client)
else:
    logger.error("Azure OpenAI credentials not found. This application requires Azure OpenAI.")
    logger.error(f"Missing environment variables: {[var for var in required_azure_vars if var not in os.environ]}")
    raise EnvironmentError("Azure OpenAI credentials are required but not found in environment variables.")

@dataclass
class TopologyAgentDeps:
    """Dependencies for the topology agent."""
    openai_client: AsyncAzureOpenAI
    nr_query_client: NewRelicQueryClient


class NodeData(BaseModel):
    """Model for a node in the topology graph."""
    id: str
    name: str
    issue: bool = False
    group: int = 1
    icon: Optional[str] = None
    properties: Dict[str, Any] = Field(default_factory=dict)
    entity_guid: Optional[str] = None
    type: Optional[str] = None
    entityType: Optional[str] = None
    domain: Optional[str] = None
    alt_names: List[str] = Field(default_factory=list)
    data_source: str = "live"


class LinkData(BaseModel):
    """Model for a link in the topology graph."""
    id: str
    source: str
    target: str
    link_type: str = "connected_to"
    properties: Dict[str, Any] = Field(default_factory=dict)
    data_source: str = "live"
    value: int = 1


class TopologyData(BaseModel):
    """Model for the complete topology data including nodes and links."""
    nodes: List[NodeData]
    links: List[LinkData]
    entity_tags: Dict[str, Any] = Field(default_factory=dict)
    cluster_id: Optional[str] = None
    primary_entity_guid: str


class TopologyResponse(BaseModel):
    """Response model for topology agent."""
    topology: TopologyData
    analysis: str
    impacted_services: List[str] = Field(default_factory=list)
    adjacent_services: List[str] = Field(default_factory=list)
    potential_cascading_impacts: str
    topology_visualization_notes: str


# Create the Topology Agent
topology_agent = Agent(
    model,
    deps_type=TopologyAgentDeps,
    result_type=TopologyResponse,
    system_prompt="""You are an AI topology analyst specializing in service and infrastructure relationships.
Your task is to analyze topology data and provide insights for incident investigation.

Given entity information and related topology data, you will:
1. Analyze the structure of the services and their relationships
2. Identify critical paths and dependencies
3. Determine potential cascading impacts of the issue
4. Suggest areas to investigate based on the topology
5. Provide notes that will help with visualization of the topology

Focus on understanding:
- How services are connected
- Which services might be affected by the issue
- Critical dependencies and potential bottlenecks
- Service clusters and their importance
- Unusual patterns in the topology that might indicate problems

Be precise and analytical in your assessment. Your analysis will be used to enhance
the visualization and understanding of the incident's impact on the system.
"""
)


@topology_agent.tool
async def get_topology(
    ctx: RunContext,
    entity_guid: str,
    product: str,
    issue_id: str,
    cluster_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Generate topology data for visualization.
    
    This tool retrieves topology data for a specific entity and product,
    using the topology service to handle the complex logic.
    
    Args:
        entity_guid: The GUID of the primary entity
        product: The product (MDM or Neurons)
        issue_id: The ID of the issue
        cluster_id: Optional cluster ID
        
    Returns:
        Topology data with nodes, links, and metadata
    """
    try:
        # Get the topology service
        topology_service = get_topology_service()
        
        # If no cluster ID is provided, try to determine it from issue details
        if not cluster_id:
            # Get issue details from New Relic
            nr_query_client = ctx.deps.nr_query_client
            issue_details = nr_query_client.get_issue_details(issue_id=issue_id)
            
            # Determine cluster ID from issue details
            cluster_id = topology_service.determine_cluster_id(issue_details, product)
            logger.info(f"Determined cluster ID: {cluster_id}")
        
        # Get topology data using the async method
        logger.info(f"Getting topology data for entity {entity_guid} on product {product} (cluster {cluster_id})")
        topology_data = await topology_service.get_topology_data_async(entity_guid, product, cluster_id)
        
        # Get entity tags
        logger.info(f"Getting entity tags for {entity_guid}")
        entity_tags = topology_service.get_entity_tags(entity_guid)
        
        # Prepare the response
        result = {
            "nodes": topology_data.get("nodes", []),
            "links": topology_data.get("links", []),
            "entity_tags": entity_tags,
            "cluster_id": cluster_id,
            "primary_entity_guid": entity_guid
        }
        
        # Log the result size
        logger.info(f"Retrieved topology with {len(result['nodes'])} nodes and {len(result['links'])} links")
        
        if not result["nodes"]:
            logger.warning(f"No nodes found in topology for {entity_guid}")
            # Return a minimal topology with just the entity
            entity_name = entity_tags.get("k8s.podName", entity_guid)
            if isinstance(entity_name, list):
                entity_name = entity_name[0]
                
            result["nodes"] = [{
                "id": entity_guid,
                "name": entity_name,
                "issue": True,
                "entity_guid": entity_guid,
                "group": 1,
                "data_source": "fallback"
            }]
        
        # Store the result in MongoDB if it was successful
        try:
            mongodb_service = get_mongodb_service()
            
            # Store as a tool result
            await mongodb_service.store_tool_result(
                incident_id=issue_id,
                tool_name="get_topology",
                tool_parameters={
                    "entity_guid": entity_guid,
                    "product": product,
                    "cluster_id": cluster_id
                },
                result_data=result,
                entity_guid=entity_guid,
                run_id=f"topology_{datetime.utcnow().isoformat()}"
            )
            logger.info(f"Stored topology result in MongoDB for {product} {cluster_id or 'unknown'}")
        except Exception as e:
            logger.error(f"Error storing topology result in MongoDB: {str(e)}")
        
        return result
        
    except Exception as e:
        logger.error(f"Error in get_topology: {str(e)}")
        return {
            "error": str(e),
            "message": f"Failed to generate topology data for {entity_guid}"
        }


@topology_agent.tool
async def get_entity_relationships(
    ctx: RunContext,
    entity_guid: str
) -> Dict[str, Any]:
    """
    Get relationships for a specific entity from New Relic.
    
    This tool retrieves the relationships for an entity to provide
    additional context for topology analysis.
    
    Args:
        entity_guid: The GUID of the entity
        
    Returns:
        Dictionary with entity relationships
    """
    try:
        # Get the topology service
        topology_service = get_topology_service()
        
        # Use New Relic API to get entity relationships
        url = "https://api.newrelic.com/graphql"
        api_key = os.environ.get("NEWRELIC_API_KEY")
        
        headers = {
            "Content-Type": "application/json",
            "API-Key": api_key
        }
        
        query = {
            "query": """
            {
              actor {
                entity(guid: "%s") {
                  name
                  entityType
                  relationships {
                    source {
                      entity {
                        guid
                        name
                        entityType
                      }
                    }
                    target {
                      entity {
                        guid
                        name
                        entityType
                      }
                    }
                  }
                }
              }
            }
            """ % entity_guid
        }
        
        import requests
        response = requests.post(url, headers=headers, json=query)
        
        if response.status_code == 200:
            logger.info(f"Retrieved relationships for entity {entity_guid}")
            return response.json()
        else:
            logger.warning(f"Error getting entity relationships: {response.status_code} {response.text}")
            return {
                "error": f"API Error: {response.status_code}",
                "message": "Failed to get entity relationships"
            }
            
    except Exception as e:
        logger.error(f"Error in get_entity_relationships: {str(e)}")
        return {
            "error": str(e),
            "message": f"Failed to get relationships for {entity_guid}"
        }


@topology_agent.tool
async def analyze_topology_impact(
    ctx: RunContext,
    topology_data: Dict[str, Any],
    entity_guid: str
) -> Dict[str, Any]:
    """
    Analyze the impact of an issue on the service topology.
    
    This tool analyzes the provided topology data to determine the potential
    impact of an issue, including identifying critical services and dependencies.
    
    Args:
        topology_data: The topology data (nodes and links)
        entity_guid: The GUID of the entity with the issue
        
    Returns:
        Analysis of the topology impact
    """
    try:
        # Extract nodes and links
        nodes = topology_data.get("nodes", [])
        links = topology_data.get("links", [])
        
        # Find the issue node
        issue_node = next((node for node in nodes if node.get("id") == entity_guid), None)
        if not issue_node:
            logger.warning(f"Issue node {entity_guid} not found in topology")
            return {
                "message": f"Issue node not found in topology",
                "impacted_services": [],
                "connected_services": []
            }
        
        # Build a graph to analyze connections
        import networkx as nx
        G = nx.Graph()
        
        # Add all nodes
        for node in nodes:
            node_id = node.get("id")
            node_name = node.get("name", "")
            node_type = node.get("type", "")
            G.add_node(node_id, name=node_name, type=node_type)
        
        # Add all edges
        for link in links:
            source = link.get("source")
            target = link.get("target")
            if source and target:
                G.add_edge(source, target)
        
        # Find directly connected nodes (1-hop neighbors)
        if entity_guid in G:
            direct_neighbors = list(G.neighbors(entity_guid))
            direct_neighbor_nodes = [
                {"id": neighbor, "name": G.nodes[neighbor].get("name", ""), "type": G.nodes[neighbor].get("type", "")}
                for neighbor in direct_neighbors
            ]
            
            # Find nodes within 2 hops (potential cascading impact)
            two_hop_neighbors = set()
            for neighbor in direct_neighbors:
                two_hop_neighbors.update(G.neighbors(neighbor))
            two_hop_neighbors.discard(entity_guid)  # Remove the original issue node
            two_hop_neighbors -= set(direct_neighbors)  # Remove direct neighbors (already counted)
            
            two_hop_neighbor_nodes = [
                {"id": neighbor, "name": G.nodes[neighbor].get("name", ""), "type": G.nodes[neighbor].get("type", "")}
                for neighbor in two_hop_neighbors
            ]
            
            # Get service names from nodes
            impacted_services = set()
            for node in direct_neighbor_nodes:
                name = node.get("name", "")
                if name:
                    # Extract service name (usually first part before dash)
                    service = name.split("-")[0] if "-" in name else name
                    impacted_services.add(service)
            
            adjacent_services = set()
            for node in two_hop_neighbor_nodes:
                name = node.get("name", "")
                if name:
                    # Extract service name (usually first part before dash)
                    service = name.split("-")[0] if "-" in name else name
                    adjacent_services.add(service)
            
            return {
                "message": "Successfully analyzed topology impact",
                "issue_node": issue_node,
                "direct_neighbors": direct_neighbor_nodes,
                "two_hop_neighbors": two_hop_neighbor_nodes,
                "impacted_services": list(impacted_services),
                "adjacent_services": list(adjacent_services)
            }
        else:
            logger.warning(f"Entity {entity_guid} not found in graph")
            return {
                "message": "Entity not found in graph",
                "impacted_services": [],
                "connected_services": []
            }
    
    except Exception as e:
        logger.error(f"Error analyzing topology impact: {str(e)}")
        return {
            "error": str(e),
            "message": "Failed to analyze topology impact"
        }


async def main():
    """Test function for the topology agent."""
    import asyncio
    from lib.new_relic.query import NewRelicQueryClient
    from lib.new_relic.client import NewRelicGraphQLClient
    
    # Initialize the New Relic client
    api_key = os.environ.get("NEWRELIC_API_KEY")
    account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        raise ValueError("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set")
        
    graphql_client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
    query_client = NewRelicQueryClient(graphql_client)
    
    # Set up dependencies for the agent
    deps = TopologyAgentDeps(
        openai_client=openai_client,
        nr_query_client=query_client
    )
    
    # Test entity
    test_entity = {
        "entity_guid": "MTA5MzYyMHxJTkZSQXxOQXwtMjEyMDk1OTIzMjM3NDkxNzk1NQ",
        "product": "neurons",
        "issue_id": "test-issue-id",
        "cluster_id": "uku"
    }
    
    # First, try to get the topology data directly
    topology_service = get_topology_service()
    try:
        logger.info(f"Testing direct topology service with {test_entity['product']} {test_entity['cluster_id']}")
        topology_data = await topology_service.get_topology_data_async(
            test_entity['entity_guid'],
            test_entity['product'],
            test_entity['cluster_id']
        )
        logger.info(f"Retrieved topology with {len(topology_data.get('nodes', []))} nodes and {len(topology_data.get('links', []))} links")
    except Exception as e:
        logger.error(f"Error testing topology service: {str(e)}")
    
    # Prepare the user prompt
    user_prompt = f"""
    Please generate and analyze topology data for the following entity:
    
    Entity GUID: {test_entity['entity_guid']}
    Product: {test_entity['product']}
    Issue ID: {test_entity['issue_id']}
    Cluster ID: {test_entity['cluster_id']}
    
    I need a detailed analysis of the topology, including:
    1. Key services and their relationships
    2. Potential impact of this issue on the system
    3. Recommendations for further investigation based on the topology
    """
    
    # Run the agent
    try:
        result = await topology_agent.run(user_prompt, deps=deps)
        
        # Print the result
        print("Topology Analysis Complete")
        print(f"Nodes: {len(result.data.topology.nodes)}")
        print(f"Links: {len(result.data.topology.links)}")
        print(f"Analysis: {result.data.analysis}")
        print(f"Impacted Services: {result.data.impacted_services}")
        print(f"Adjacent Services: {result.data.adjacent_services}")
        print(f"Potential Cascading Impacts: {result.data.potential_cascading_impacts}")
        print(f"Visualization Notes: {result.data.topology_visualization_notes}")
        
    except Exception as e:
        logger.error(f"Error running topology agent: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main()) 