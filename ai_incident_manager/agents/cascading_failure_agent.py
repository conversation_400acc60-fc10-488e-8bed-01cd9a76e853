"""
Cascading Failure Detection Agent - Pydantic AI Agent for analyzing cascading failures.

This agent specializes in detecting and analyzing cascading failures in entity relationship
graphs, providing detailed insights into failure propagation patterns, risk assessment,
and recommendations for mitigation.
"""

import os
import json
import asyncio
from loguru import logger
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple, cast
from dataclasses import dataclass
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel
from pydantic import BaseModel, Field
from openai import AsyncAzureOpenAI
import dotenv

from ai_incident_manager.models.workflow_state import IncidentState
from ai_incident_manager.models.graph_models import (
    GraphAgentResponse, CascadingFailureAnalysisModel, GraphAnalysisRequest,
    GraphAnalysisResponse, NodeTypeEnum, RelationshipTypeEnum
)
from ai_incident_manager.services.graph_service import get_graph_service
from ai_incident_manager.utils.graph_algorithms import get_graph_algorithms
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.client import NewRelicGraphQLClient

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logger = logger.bind(name="cascading_failure_agent")

# Initialize OpenAI client
required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
azure_openai_enabled = all(var in os.environ for var in required_azure_vars)

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    model = OpenAIModel(os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"), openai_client=openai_client)
else:
    logger.error("Azure OpenAI credentials not found. This application requires Azure OpenAI.")
    logger.error(f"Missing environment variables: {[var for var in required_azure_vars if var not in os.environ]}")
    raise EnvironmentError("Azure OpenAI credentials are required but not found in environment variables.")


@dataclass
class CascadingFailureAgentDeps:
    """Dependencies for the cascading failure agent."""
    openai_client: AsyncAzureOpenAI
    nr_query_client: NewRelicQueryClient
    state: IncidentState


# Cascading Failure Detection Agent
cascading_failure_agent = Agent(
    model,
    deps_type=CascadingFailureAgentDeps,
    result_type=GraphAgentResponse,
    system_prompt="""You are an AI incident response specialist with expertise in cascading failure analysis and graph theory.
Your role is to analyze entity relationship graphs to detect, predict, and provide insights about cascading failures in complex systems.

When analyzing entity relationships and potential cascading failures, you should:

1. **Graph Analysis**: Examine the structure of entity relationships, identifying critical paths, bottlenecks, and vulnerable points
2. **Failure Propagation**: Analyze how failures propagate through the system, considering relationship types, weights, and probabilities
3. **Risk Assessment**: Evaluate the risk level of cascading failures based on entity criticality, dependency patterns, and historical data
4. **Impact Analysis**: Determine the potential blast radius and impact of cascading failures on system components
5. **Mitigation Strategies**: Provide actionable recommendations to prevent or minimize cascading failures

Key areas to focus on:
- **Critical Dependencies**: Identify entities that, if they fail, could trigger widespread cascading failures
- **Failure Paths**: Map out the most likely paths for failure propagation
- **Risk Prioritization**: Rank entities and dependencies by their potential impact
- **Temporal Analysis**: Consider time-based factors in failure propagation
- **Resilience Assessment**: Evaluate system resilience and suggest improvements

Your analysis should be comprehensive yet practical, providing clear insights that help incident responders understand:
- What entities are most at risk of cascading failures
- How failures are likely to propagate through the system
- What actions should be taken to prevent or mitigate cascading effects
- Which monitoring and alerting should be enhanced

Always provide specific, actionable recommendations based on the graph analysis results.
"""
)


@cascading_failure_agent.tool
async def build_entity_graph(
    ctx: RunContext,
    primary_entity_guid: str,
    entity_type: str,
    entity_name: str,
    alert_category: str,
    cluster_name: Optional[str] = None,
    max_depth: int = 3,
    since_time_ms: Optional[int] = None,
    until_time_ms: Optional[int] = None
) -> Dict[str, Any]:
    """
    Build a comprehensive entity relationship graph for analysis.
    
    Args:
        primary_entity_guid: GUID of the primary entity
        entity_type: Type of the primary entity
        entity_name: Name of the primary entity
        alert_category: Alert category for context
        cluster_name: Optional cluster name
        max_depth: Maximum depth for relationship traversal
        since_time_ms: Start time for data collection
        until_time_ms: End time for data collection
        
    Returns:
        Graph structure with nodes and edges
    """
    try:
        # Get graph service
        graph_service = get_graph_service()
        
        # Build entity graph
        graph = await graph_service.build_entity_graph(
            primary_entity_guid=primary_entity_guid,
            entity_type=entity_type,
            entity_name=entity_name,
            alert_category=alert_category,
            cluster_name=cluster_name,
            max_depth=max_depth,
            since_time_ms=since_time_ms,
            until_time_ms=until_time_ms
        )
        
        # Convert graph to serializable format
        graph_data = {
            'nodes': [
                {
                    'id': node,
                    'name': graph.nodes[node].get('name', ''),
                    'type': graph.nodes[node].get('node_type', ''),
                    'criticality_score': graph.nodes[node].get('criticality_score', 0.0),
                    'failure_probability': graph.nodes[node].get('failure_probability', 0.0),
                    'health_status': graph.nodes[node].get('health_status', 'unknown'),
                    'properties': graph.nodes[node].get('properties', {})
                }
                for node in graph.nodes()
            ],
            'edges': [
                {
                    'source': edge[0],
                    'target': edge[1],
                    'relationship_type': graph.get_edge_data(edge[0], edge[1]).get('relationship_type', 'RELATED_TO'),
                    'weight': graph.get_edge_data(edge[0], edge[1]).get('weight', 1.0),
                    'failure_propagation_probability': graph.get_edge_data(edge[0], edge[1]).get('failure_propagation_probability', 0.5)
                }
                for edge in graph.edges()
            ],
            'metadata': {
                'node_count': graph.number_of_nodes(),
                'edge_count': graph.number_of_edges(),
                'primary_entity': primary_entity_guid,
                'max_depth': max_depth,
                'alert_category': alert_category
            }
        }
        
        logger.info(f"Built entity graph with {graph.number_of_nodes()} nodes and {graph.number_of_edges()} edges")
        return graph_data
        
    except Exception as e:
        logger.error(f"Error building entity graph: {str(e)}")
        return {
            'error': str(e),
            'nodes': [],
            'edges': [],
            'metadata': {}
        }


@cascading_failure_agent.tool
async def analyze_cascading_failures(
    ctx: RunContext,
    graph_data: Dict[str, Any],
    primary_entity_guid: str,
    failure_threshold: float = 0.1,
    propagation_decay: float = 0.8,
    max_hops: int = 5
) -> Dict[str, Any]:
    """
    Analyze potential cascading failures in the entity graph.
    
    Args:
        graph_data: Graph structure with nodes and edges
        primary_entity_guid: GUID of the failing entity
        failure_threshold: Minimum probability to consider a failure
        propagation_decay: Decay factor for failure propagation
        max_hops: Maximum hops to consider for cascading
        
    Returns:
        Cascading failure analysis results
    """
    try:
        # Get graph service
        graph_service = get_graph_service()
        
        # Reconstruct NetworkX graph from data
        import networkx as nx
        graph = nx.DiGraph()
        
        # Add nodes
        for node_data in graph_data.get('nodes', []):
            graph.add_node(node_data['id'], **node_data)
        
        # Add edges
        for edge_data in graph_data.get('edges', []):
            graph.add_edge(edge_data['source'], edge_data['target'], **edge_data)
        
        # Perform cascading failure analysis
        analysis = graph_service.analyze_cascading_failures(
            graph=graph,
            primary_entity_guid=primary_entity_guid,
            failure_threshold=failure_threshold,
            propagation_decay=propagation_decay,
            max_hops=max_hops
        )
        
        # Convert to serializable format
        return {
            'primary_entity': analysis.primary_entity,
            'affected_entities': analysis.affected_entities,
            'failure_paths': [
                {
                    'path': path,
                    'length': len(path),
                    'entities': [
                        {
                            'id': entity,
                            'name': graph.nodes[entity].get('name', ''),
                            'type': graph.nodes[entity].get('node_type', ''),
                            'criticality': graph.nodes[entity].get('criticality_score', 0.0)
                        }
                        for entity in path
                    ]
                }
                for path in analysis.failure_paths
            ],
            'risk_scores': analysis.risk_scores,
            'critical_dependencies': analysis.critical_dependencies,
            'potential_blast_radius': analysis.potential_blast_radius,
            'estimated_impact_score': analysis.estimated_impact_score,
            'recommended_actions': analysis.recommended_actions
        }
        
    except Exception as e:
        logger.error(f"Error analyzing cascading failures: {str(e)}")
        return {
            'error': str(e),
            'primary_entity': primary_entity_guid,
            'affected_entities': [],
            'failure_paths': [],
            'risk_scores': {},
            'critical_dependencies': [],
            'potential_blast_radius': 0,
            'estimated_impact_score': 0.0,
            'recommended_actions': []
        }


@cascading_failure_agent.tool
async def find_critical_paths(
    ctx: RunContext,
    graph_data: Dict[str, Any],
    source_entity: str,
    target_entity: Optional[str] = None,
    max_paths: int = 10
) -> Dict[str, Any]:
    """
    Find critical paths in the entity graph.
    
    Args:
        graph_data: Graph structure with nodes and edges
        source_entity: Source entity GUID
        target_entity: Target entity GUID (if None, find paths to all critical entities)
        max_paths: Maximum number of paths to return
        
    Returns:
        Critical paths analysis
    """
    try:
        # Get graph algorithms
        graph_algorithms = get_graph_algorithms()
        
        # Reconstruct NetworkX graph from data
        import networkx as nx
        graph = nx.DiGraph()
        
        # Add nodes
        for node_data in graph_data.get('nodes', []):
            graph.add_node(node_data['id'], **node_data)
        
        # Add edges
        for edge_data in graph_data.get('edges', []):
            graph.add_edge(edge_data['source'], edge_data['target'], **edge_data)
        
        critical_paths = []
        
        if target_entity:
            # Find paths to specific target
            paths = graph_algorithms.find_critical_paths_advanced(
                graph=graph,
                source=source_entity,
                target=target_entity,
                max_paths=max_paths
            )
            critical_paths.extend(paths)
        else:
            # Find paths to all high-criticality entities
            high_criticality_nodes = [
                node for node in graph.nodes()
                if graph.nodes[node].get('criticality_score', 0.0) > 0.7
                and node != source_entity
            ]
            
            for target in high_criticality_nodes[:5]:  # Limit to top 5 targets
                paths = graph_algorithms.find_critical_paths_advanced(
                    graph=graph,
                    source=source_entity,
                    target=target,
                    max_paths=2
                )
                critical_paths.extend(paths)
        
        # Convert to serializable format
        return {
            'source_entity': source_entity,
            'target_entity': target_entity,
            'critical_paths': [
                {
                    'source': path.source,
                    'target': path.target,
                    'path': path.shortest_path,
                    'risk_level': path.risk_level,
                    'total_weight': path.total_weight,
                    'criticality_scores': path.criticality_scores
                }
                for path in critical_paths
            ],
            'path_count': len(critical_paths)
        }
        
    except Exception as e:
        logger.error(f"Error finding critical paths: {str(e)}")
        return {
            'error': str(e),
            'source_entity': source_entity,
            'target_entity': target_entity,
            'critical_paths': [],
            'path_count': 0
        }


@cascading_failure_agent.tool
async def analyze_graph_resilience(
    ctx: RunContext,
    graph_data: Dict[str, Any],
    removal_strategy: str = 'targeted',
    num_removals: int = 5
) -> Dict[str, Any]:
    """
    Analyze graph resilience to node failures.
    
    Args:
        graph_data: Graph structure with nodes and edges
        removal_strategy: Strategy for node removal ('random', 'targeted', 'degree')
        num_removals: Number of nodes to remove
        
    Returns:
        Graph resilience analysis
    """
    try:
        # Get graph algorithms
        graph_algorithms = get_graph_algorithms()
        
        # Reconstruct NetworkX graph from data
        import networkx as nx
        graph = nx.DiGraph()
        
        # Add nodes
        for node_data in graph_data.get('nodes', []):
            graph.add_node(node_data['id'], **node_data)
        
        # Add edges
        for edge_data in graph_data.get('edges', []):
            graph.add_edge(edge_data['source'], edge_data['target'], **edge_data)
        
        # Analyze resilience
        resilience_data = graph_algorithms.analyze_graph_resilience(
            graph=graph,
            removal_strategy=removal_strategy,
            num_removals=num_removals
        )
        
        return resilience_data
        
    except Exception as e:
        logger.error(f"Error analyzing graph resilience: {str(e)}")
        return {
            'error': str(e),
            'resilience_score': 0.0,
            'removal_impact': []
        }


@cascading_failure_agent.tool
async def identify_bottlenecks(
    ctx: RunContext,
    graph_data: Dict[str, Any],
    flow_threshold: float = 0.6
) -> Dict[str, Any]:
    """
    Identify bottleneck entities in the graph.
    
    Args:
        graph_data: Graph structure with nodes and edges
        flow_threshold: Threshold for considering a node a bottleneck
        
    Returns:
        Bottleneck analysis results
    """
    try:
        # Get graph algorithms
        graph_algorithms = get_graph_algorithms()
        
        # Reconstruct NetworkX graph from data
        import networkx as nx
        graph = nx.DiGraph()
        
        # Add nodes
        for node_data in graph_data.get('nodes', []):
            graph.add_node(node_data['id'], **node_data)
        
        # Add edges
        for edge_data in graph_data.get('edges', []):
            graph.add_edge(edge_data['source'], edge_data['target'], **edge_data)
        
        # Find bottlenecks
        bottlenecks = graph_algorithms.find_bottlenecks(
            graph=graph,
            flow_threshold=flow_threshold
        )
        
        return {
            'bottlenecks': bottlenecks,
            'bottleneck_count': len(bottlenecks),
            'flow_threshold': flow_threshold
        }
        
    except Exception as e:
        logger.error(f"Error identifying bottlenecks: {str(e)}")
        return {
            'error': str(e),
            'bottlenecks': [],
            'bottleneck_count': 0
        }


@cascading_failure_agent.tool
async def simulate_failure_propagation(
    ctx: RunContext,
    graph_data: Dict[str, Any],
    initial_failures: List[str],
    propagation_steps: int = 10,
    threshold: float = 0.1
) -> Dict[str, Any]:
    """
    Simulate failure propagation through the graph.
    
    Args:
        graph_data: Graph structure with nodes and edges
        initial_failures: List of initially failing entities
        propagation_steps: Number of propagation steps to simulate
        threshold: Minimum probability to consider a failure
        
    Returns:
        Failure propagation simulation results
    """
    try:
        # Get graph algorithms
        graph_algorithms = get_graph_algorithms()
        
        # Reconstruct NetworkX graph from data
        import networkx as nx
        graph = nx.DiGraph()
        
        # Add nodes
        for node_data in graph_data.get('nodes', []):
            graph.add_node(node_data['id'], **node_data)
        
        # Add edges
        for edge_data in graph_data.get('edges', []):
            graph.add_edge(edge_data['source'], edge_data['target'], **edge_data)
        
        # Simulate failure propagation
        propagation_results = graph_algorithms.simulate_failure_propagation(
            graph=graph,
            initial_failures=initial_failures,
            propagation_steps=propagation_steps,
            threshold=threshold
        )
        
        # Convert to serializable format
        return {
            'initial_failures': initial_failures,
            'propagation_results': {
                entity_id: {
                    'entity_id': result.entity_id,
                    'failure_probability': result.failure_probability,
                    'propagation_time': result.propagation_time,
                    'source_path': result.source_path,
                    'contributing_factors': result.contributing_factors
                }
                for entity_id, result in propagation_results.items()
            },
            'total_affected': len(propagation_results),
            'propagation_steps': propagation_steps,
            'threshold': threshold
        }
        
    except Exception as e:
        logger.error(f"Error simulating failure propagation: {str(e)}")
        return {
            'error': str(e),
            'initial_failures': initial_failures,
            'propagation_results': {},
            'total_affected': 0
        }


@cascading_failure_agent.tool
async def get_entity_metrics(
    ctx: RunContext,
    entity_guid: str,
    metric_types: List[str] = None,
    since_time_ms: Optional[int] = None,
    until_time_ms: Optional[int] = None
) -> Dict[str, Any]:
    """
    Get metrics for a specific entity to support analysis.
    
    Args:
        entity_guid: GUID of the entity
        metric_types: List of metric types to collect
        since_time_ms: Start time for metrics
        until_time_ms: End time for metrics
        
    Returns:
        Entity metrics data
    """
    try:
        # Get New Relic query client
        query_client = ctx.deps.nr_query_client
        
        # Default metrics if not specified
        if metric_types is None:
            metric_types = ["cpu_usage", "memory_usage", "error_rate", "throughput"]
        
        # Get entity details
        entity_details = query_client.get_entity_details(entity_guid=entity_guid)
        
        if not entity_details:
            return {
                'entity_guid': entity_guid,
                'metrics': {},
                'error': 'Entity not found'
            }
        
        # Collect metrics based on entity type
        metrics = {}
        entity_type = entity_details.get('entityType', 'UNKNOWN')
        
        # Build time constraints
        time_constraint = ""
        if since_time_ms and until_time_ms:
            time_constraint = f"SINCE {since_time_ms} UNTIL {until_time_ms}"
        elif since_time_ms:
            time_constraint = f"SINCE {since_time_ms}"
        else:
            time_constraint = "SINCE 1 hour ago"
        
        # Collect metrics based on type
        if entity_type in ['KUBERNETES_POD', 'CONTAINER']:
            for metric_type in metric_types:
                if metric_type == 'cpu_usage':
                    query = f"""
                        SELECT average(cpuCoresUtilization) as cpu_avg, 
                               max(cpuCoresUtilization) as cpu_max
                        FROM K8sContainerSample 
                        WHERE entityGuid = '{entity_guid}' 
                        {time_constraint}
                    """
                elif metric_type == 'memory_usage':
                    query = f"""
                        SELECT average(memoryUtilization) as memory_avg,
                               max(memoryUtilization) as memory_max
                        FROM K8sContainerSample 
                        WHERE entityGuid = '{entity_guid}' 
                        {time_constraint}
                    """
                elif metric_type == 'restart_count':
                    query = f"""
                        SELECT latest(restartCount) as restart_count
                        FROM K8sContainerSample 
                        WHERE entityGuid = '{entity_guid}' 
                        {time_constraint}
                    """
                else:
                    continue
                
                try:
                    result = query_client.execute_nrql(query)
                    if result:
                        metrics[metric_type] = result[0]
                except Exception as e:
                    logger.warning(f"Failed to get {metric_type} for {entity_guid}: {str(e)}")
        
        return {
            'entity_guid': entity_guid,
            'entity_type': entity_type,
            'metrics': metrics,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting entity metrics: {str(e)}")
        return {
            'entity_guid': entity_guid,
            'metrics': {},
            'error': str(e)
        }


async def main():
    """
    Test function for the cascading failure agent.
    """
    # Initialize the New Relic client
    api_key = os.environ.get("NEWRELIC_API_KEY")
    account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")

    if not api_key or not account_id:
        raise ValueError("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set in environment variables")

    graphql_client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
    query_client = NewRelicQueryClient(graphql_client)

    # Sample test data
    test_entity = {
        "entity_guid": "MTA5MzYyMHxJTkZSQXxOQXwtOTAzODY4MDkwNTY4NDkwMDMxMg",
        "entity_type": "CONTAINER",
        "entity_name": "agent-management-background-container-service",
        "alert_category": "kubernetes_crashloopbackoff",
        "cluster_name": "https://aks-rg-uku-prd-neurons-a398f44f.hcp.uksouth.azmk8s.io:443"
    }

    # Create a complete test state
    test_state = IncidentState(
        incident_id="test-incident-001",
        raw_alert={
            "issueId": "test-issue-001",
            "title": "Container crashloop detected",
            "priority": "CRITICAL",
            "EntityId": [test_entity["entity_guid"]],
            "product": "neurons",
            "nr_region": "us"
        },
        title="Container crashloop detected",
        description="Container experiencing crashloop backoff",
        severity="CRITICAL",
        start_time=datetime.now(timezone.utc).isoformat(),
        alert_category=test_entity["alert_category"],
        alert_runbook="Test runbook",
        alert_title="Container crashloop detected",
        condition_name="CrashLoopBackOff",
        current_phase="cascading_failure_analysis",
        entities=[{
            "entity_guid": test_entity["entity_guid"],
            "entity_name": test_entity["entity_name"],
            "entity_type": test_entity["entity_type"],
            "cluster_name": test_entity["cluster_name"]
        }],
        since_time="2023-08-01T00:00:00Z",
        until_time="2023-08-01T01:00:00Z"
    )

    # Set up dependencies
    deps = CascadingFailureAgentDeps(
        openai_client=openai_client,
        nr_query_client=query_client,
        state=test_state
    )

    # Create analysis prompt
    analysis_prompt = f"""
    Please perform a comprehensive cascading failure analysis for the following incident:
    
    Primary Entity: {test_entity['entity_name']} (GUID: {test_entity['entity_guid']})
    Entity Type: {test_entity['entity_type']}
    Alert Category: {test_entity['alert_category']}
    Cluster: {test_entity['cluster_name']}
    
    I need a detailed analysis including:
    1. Build the entity relationship graph
    2. Analyze potential cascading failures
    3. Identify critical paths and bottlenecks
    4. Assess system resilience
    5. Provide actionable recommendations
    
    Focus on:
    - Entities most likely to be affected by cascading failures
    - Critical dependencies that need immediate attention
    - Recommended actions to prevent failure propagation
    - Monitoring and alerting enhancements
    """

    print(f"Running cascading failure analysis with prompt:\n{analysis_prompt}\n")
    print("-" * 80)

    try:
        # Run the agent
        result = await cascading_failure_agent.run(analysis_prompt, deps=deps)

        # Print the results
        print("\nCascading Failure Analysis Results:")
        print(f"Analysis Summary: {result.data.analysis_summary}")
        print(f"Risk Assessment: {result.data.risk_assessment}")
        print(f"Confidence Score: {result.data.confidence_score}")
        print(f"Estimated Resolution Time: {result.data.estimated_resolution_time}")
        
        print("\nCritical Insights:")
        for i, insight in enumerate(result.data.critical_insights, 1):
            print(f"{i}. {insight}")
        
        print("\nRecommended Actions:")
        for i, action in enumerate(result.data.recommended_actions, 1):
            print(f"{i}. {action}")
        
        print("\nEntities to Monitor:")
        for i, entity in enumerate(result.data.entities_to_monitor, 1):
            print(f"{i}. {entity}")
        
        print(f"\nAnalysis Methodology: {result.data.analysis_methodology}")
        
        if result.data.limitations:
            print("\nLimitations:")
            for i, limitation in enumerate(result.data.limitations, 1):
                print(f"{i}. {limitation}")
        
        # Print cascading failure details
        cascading_analysis = result.data.cascading_failure_analysis
        print(f"\nCascading Failure Details:")
        print(f"- Affected Entities: {len(cascading_analysis.affected_entities)}")
        print(f"- Critical Dependencies: {len(cascading_analysis.critical_dependencies)}")
        print(f"- Potential Blast Radius: {cascading_analysis.potential_blast_radius}")
        print(f"- Estimated Impact Score: {cascading_analysis.estimated_impact_score:.2f}")
        print(f"- Failure Paths: {len(cascading_analysis.failure_paths)}")

    except Exception as e:
        print(f"Error running cascading failure agent: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())