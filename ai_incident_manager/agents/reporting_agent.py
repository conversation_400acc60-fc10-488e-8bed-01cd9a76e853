"""
Reporting Agent for incident management.

This agent analyzes incident data and generates structured reporting content 
for tickets and notifications.
"""

import os
import json
from typing import Dict, List, Any, Optional, Tuple, cast
from datetime import datetime
from dataclasses import dataclass
from pydantic import BaseModel, Field
from loguru import logger
import dotenv
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel
from openai import AsyncOpenAI, AsyncAzureOpenAI

# Import the services
from ai_incident_manager.services.ado_service import get_ado_service, ADOService
from ai_incident_manager.services.teams_service import get_teams_service, TeamsService

# Load environment variables
dotenv.load_dotenv()

# Configure logging
logger = logger.bind(name="reporting_agent")

# Initialize OpenAI client
required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
azure_openai_enabled = all(var in os.environ for var in required_azure_vars)

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    model = OpenAIModel(os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"), openai_client=openai_client)
else:
    logger.error("Azure OpenAI credentials not found. This application requires Azure OpenAI.")
    logger.error(f"Missing environment variables: {[var for var in required_azure_vars if var not in os.environ]}")
    raise EnvironmentError("Azure OpenAI credentials are required but not found in environment variables.")

class ReportingResult(BaseModel):
    """Legacy model for reporting operation results."""
    ado_ticket_id: Optional[int] = None
    ado_ticket_url: Optional[str] = None
    teams_notification_sent: bool = False
    success: bool = False
    error: Optional[str] = None
    current_phase: str = "results_reported"

class ReportingAgentResponse(BaseModel):
    """Structured response from the reporting agent with content for reporting."""
    
    # Ticket content
    ticket_title: str = Field(..., description="Title for the Azure DevOps ticket")
    ticket_description: str = Field(..., description="Description for the Azure DevOps ticket")
    ticket_tags: List[str] = Field(default_factory=list, description="Tags to apply to the ticket")
    
    # Teams notification content
    notification_title: str = Field(..., description="Title for the Teams notification")
    notification_summary: str = Field(..., description="Brief summary for the Teams notification")
    notification_details: str = Field(..., description="Detailed content for the Teams notification")
    
    # Analysis highlights
    root_cause_summary: str = Field(..., description="Summary of the root cause analysis")
    confidence_level: int = Field(default=0, description="Confidence level in the analysis (1-10)")
    key_findings: List[str] = Field(default_factory=list, description="Key findings from the analysis")
    recommendations: List[str] = Field(default_factory=list, description="Recommended actions based on the analysis")
    
    # Additional information
    entities_impacted: List[str] = Field(default_factory=list, description="List of impacted entities")
    runbook_results_summary: str = Field("", description="Summary of runbook execution results")
    
    # Formatting options
    include_full_analysis: bool = Field(True, description="Whether to include the full analysis details in the ticket")
    highlight_critical_items: bool = Field(True, description="Whether to highlight critical items in the reporting")

@dataclass
class ReportingAgentDeps:
    """Dependencies for the reporting agent."""
    openai_client: AsyncAzureOpenAI
    
    # These services are no longer needed by the agent directly
    # but are kept for backward compatibility
    ado_service: Optional[ADOService] = None
    teams_service: Optional[TeamsService] = None

# Define the reporting agent
reporting_agent = Agent(
    model,
    deps_type=ReportingAgentDeps,
    result_type=ReportingAgentResponse,
    system_prompt="""
You are a reporting agent for an incident management system. Your role is to generate structured reporting content based on incident analysis results.

Your task is to:
1. Analyze the incident details and root cause analysis (RCA) results
2. Generate a concise, well-formatted title and description for an Azure DevOps ticket
3. Create content for a Microsoft Teams notification that highlights the most important information
4. Extract and summarize key findings, recommendations, and impacted components

Focus on creating high-quality content that communicates the incident details clearly and professionally.
Do not try to create or update tickets directly, just provide the formatted content.

You should structure your response to include:
- Formatted title and description for the ticket (HTML format for description)
- Notification content for Teams (title, summary, and details)
- A summary of the root cause analysis
- Key findings and recommendations
- A list of suggested tags for the ticket

Keep your content clear, concise, and focused on the key information from the incident.
"""
)

@reporting_agent.tool
async def analyze_incident_data(
    ctx: RunContext,
    incident_id: str,
    title: str,
    severity: str,
    product: str,
    region: str,
    alert_category: str,
    root_cause: Optional[str] = None,
    rca_details: Optional[Dict[str, Any]] = None,
    entities: Optional[List[Dict[str, Any]]] = None,
    runbook_results: Optional[List[Dict[str, Any]]] = None,
    analysis_summary: Optional[str] = None,
    existing_ticket_id: Optional[int] = None,
    existing_ticket_url: Optional[str] = None
) -> Dict[str, Any]:
    """
    Analyze incident data and prepare content for reporting.
    
    Args:
        incident_id: ID of the incident
        title: Title of the incident
        severity: Severity level of the incident
        product: Product affected by the incident
        region: Region where the incident occurred
        alert_category: Category of the alert
        root_cause: Primary root cause of the incident (if available)
        rca_details: Detailed RCA information (if available)
        entities: List of entities involved in the incident
        runbook_results: Results of runbook execution
        analysis_summary: Summary of the incident analysis
        existing_ticket_id: ID of existing ADO ticket (if any)
        existing_ticket_url: URL of existing ADO ticket (if any)
        
    Returns:
        Dictionary with analysis results for reporting
    """
    # Determine if this is a new incident or an update to an existing one
    is_update = existing_ticket_id is not None
    
    # Extract relevant information from RCA details if available
    confidence_level = 0
    recommendations = []
    secondary_factors = []
    prioritized_factors = []
    evidence_summary = ""
    
    if rca_details:
        confidence_level = rca_details.get("confidence_level", 0)
        recommendations = rca_details.get("recommendations", [])
        secondary_factors = rca_details.get("secondary_factors", [])
        prioritized_factors = rca_details.get("prioritized_factors", [])
        evidence_summary = rca_details.get("evidence_summary", "")
    
    # Extract entity information
    entity_names = []
    if entities:
        for entity in entities:
            name = entity.get("entity_name", "Unknown")
            entity_type = entity.get("entity_type", "Unknown")
            entity_names.append(f"{name} ({entity_type})")
    
    # Create runbook summary
    runbook_summary = ""
    if runbook_results:
        for i, step in enumerate(runbook_results[:5]):  # Limit to first 5 steps
            step_title = step.get("step_title", f"Step {i+1}")
            issues_found = step.get("issues_found", False)
            status = "❌" if issues_found else "✅"
            runbook_summary += f"{status} {step_title}\n"
    
    # Determine if RCA is complete
    has_rca = root_cause is not None and rca_details is not None
    
    # Return the analysis results
    return {
        "is_update": is_update,
        "has_rca": has_rca,
        "incident_id": incident_id,
        "title": title,
        "severity": severity,
        "product": product,
        "region": region,
        "alert_category": alert_category,
        "root_cause": root_cause,
        "confidence_level": confidence_level,
        "entity_names": entity_names,
        "recommendations": recommendations,
        "secondary_factors": secondary_factors,
        "prioritized_factors": prioritized_factors,
        "evidence_summary": evidence_summary,
        "runbook_summary": runbook_summary,
        "analysis_summary": analysis_summary,
        "existing_ticket_id": existing_ticket_id,
        "existing_ticket_url": existing_ticket_url
    }

# Get singleton instance
_reporting_agent = None

def get_reporting_agent() -> Agent:
    """Get the singleton instance of the reporting agent."""
    global _reporting_agent
    if _reporting_agent is None:
        _reporting_agent = reporting_agent
    return _reporting_agent 