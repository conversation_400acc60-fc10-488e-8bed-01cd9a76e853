"""
Database Agent - Pydantic AI Agent for retrieving context from PostgreSQL database.

This agent queries the PostgreSQL database to retrieve additional context data
related to incidents, helping to provide deeper insights for RCA and incident resolution.
"""

import os
import json
import yaml
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple, Union, cast
from dataclasses import dataclass
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel
from pydantic import BaseModel, Field
from openai import AsyncOpenAI, AsyncAzureOpenAI
from devtools import pprint
import dotenv
import asyncio
from loguru import logger

from lib.database_client import DatabaseClient, get_database_client
from ai_incident_manager.models.workflow_state import IncidentState, DatabaseContextResponse

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logger = logger.bind(name="database_agent")

# Initialize OpenAI client (same as other agents)
required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
azure_openai_enabled = all(var in os.environ for var in required_azure_vars)

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    model = OpenAIModel(os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"), openai_client=openai_client)
else:
    logger.error("Azure OpenAI credentials not found. This application requires Azure OpenAI.")
    logger.error(f"Missing environment variables: {[var for var in required_azure_vars if var not in os.environ]}")
    raise EnvironmentError("Azure OpenAI credentials are required but not found in environment variables.")


@dataclass
class DatabaseAgentDeps:
    """Dependencies for the database agent."""
    openai_client: AsyncAzureOpenAI
    database_client: DatabaseClient
    state: IncidentState


# Database Agent with system message and result type
database_agent = Agent(
    model,
    deps_type=DatabaseAgentDeps,
    result_type=DatabaseContextResponse,
    system_prompt="""You are an AI database analyst specializing in retrieving context data for incident analysis.
Your task is to query a PostgreSQL database to retrieve additional context about incidents
that will help with root cause analysis (RCA) and incident resolution.

Given an incident with product information, entities, and time window:
1. Determine which database queries are relevant for the incident context
2. Format query parameters correctly based on incident information
3. Execute relevant queries against the PostgreSQL database
4. Analyze the results and summarize key findings
5. Return structured information that can be used in incident analysis

Key responsibilities:
- Choose appropriate queries based on the product (mdm, neurons) and context needed
- Recognize entity identifiers like application names, cluster names, etc. from incident data
- Format timestamps correctly for database queries
- Extract the most relevant context from query results
- Prioritize information based on relevance to the incident

When analyzing query results:
- Look for patterns that might indicate causes of the incident
- Identify recent changes or deployments that could have contributed to the issue
- Note any historical incidents with similar symptoms
- Focus on the time window around when the incident occurred
- Correlate results across different queries to find potential relationships

Your response should include:
- A comprehensive summary of the context found
- Individual summaries for each query executed
- A priority list of the most relevant context findings
- Specific entity identifiers that were examined
- Recommendations for additional data that might be useful

Note: If a query returns no results, indicate this clearly rather than making assumptions.
If you encounter errors, report them accurately but try alternative approaches when possible.
"""
)


@database_agent.tool
async def get_database_product_context(
    ctx: RunContext,
    product: str,
    query_params: Dict[str, Any]
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get product-specific context data from the database.
    
    Args:
        product: Product name (e.g., 'mdm', 'neurons')
        query_params: Parameters to substitute into the query templates
        
    Returns:
        Dictionary of query names and their results
    """
    try:
        database_client = ctx.deps.database_client
        results = await database_client.get_product_context_async(product, query_params)
        
        # Log summary of results
        query_count = len(results)
        total_rows = sum(len(rows) for rows in results.values())
        logger.info(f"Retrieved product context: {query_count} queries with {total_rows} total rows")
        
        return results
    except Exception as e:
        logger.error(f"Error retrieving product context: {str(e)}")
        return {"error": str(e)}


@database_agent.tool
async def get_database_rca_context(
    ctx: RunContext,
    product: str,
    query_params: Dict[str, Any]
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get RCA-specific context data from the database.
    
    Args:
        product: Product name (e.g., 'mdm', 'neurons')
        query_params: Parameters to substitute into the query templates
        
    Returns:
        Dictionary of query names and their results
    """
    try:
        database_client = ctx.deps.database_client
        results = await database_client.get_rca_context_async(product, query_params)
        
        # Log summary of results
        query_count = len(results)
        total_rows = sum(len(rows) for rows in results.values())
        logger.info(f"Retrieved RCA context: {query_count} queries with {total_rows} total rows")
        
        return results
    except Exception as e:
        logger.error(f"Error retrieving RCA context: {str(e)}")
        return {"error": str(e)}


@database_agent.tool
async def get_database_general_context(
    ctx: RunContext,
    product: str,
    query_params: Dict[str, Any]
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get general context data (applicable to all products) from the database.
    
    Args:
        product: Product name to include in query parameters
        query_params: Parameters to substitute into the query templates
        
    Returns:
        Dictionary of query names and their results
    """
    try:
        database_client = ctx.deps.database_client
        results = await database_client.get_general_context_async(product, query_params)
        
        # Log summary of results
        query_count = len(results)
        total_rows = sum(len(rows) for rows in results.values())
        logger.info(f"Retrieved general context: {query_count} queries with {total_rows} total rows")
        
        return results
    except Exception as e:
        logger.error(f"Error retrieving general context: {str(e)}")
        return {"error": str(e)}


@database_agent.tool
async def get_available_queries(
    ctx: RunContext,
    product: Optional[str] = None,
    context_type: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get information about available queries in the configuration.
    
    Args:
        product: Optional product name to filter queries (e.g., 'mdm', 'neurons', 'general')
        context_type: Optional context type to filter queries (e.g., 'general_context', 'rca')
        
    Returns:
        Dictionary with information about available queries
    """
    try:
        database_client = ctx.deps.database_client
        queries = database_client.queries
        
        # Filter by product if specified
        if product and product in queries:
            filtered_queries = {product: queries[product]}
        elif product:
            return {"error": f"No queries found for product: {product}"}
        else:
            filtered_queries = queries
        
        # Filter by context type if specified
        if context_type:
            for prod in list(filtered_queries.keys()):
                if context_type in filtered_queries[prod]:
                    filtered_queries[prod] = {context_type: filtered_queries[prod][context_type]}
                else:
                    filtered_queries.pop(prod)
        
        # Format response to show available queries without the full SQL
        result = {}
        for prod, contexts in filtered_queries.items():
            result[prod] = {}
            for ctx_type, q_dict in contexts.items():
                result[prod][ctx_type] = list(q_dict.keys())
        
        return {"available_queries": result}
    
    except Exception as e:
        logger.error(f"Error retrieving available queries: {str(e)}")
        return {"error": str(e)}


@database_agent.tool
async def get_query_parameters_from_state(
    ctx: RunContext
) -> Dict[str, Any]:
    """
    Extract relevant query parameters from the current state for database queries.
    
    This tool analyzes the current incident state and extracts parameters
    that can be used in database queries, such as time window, product,
    entity names, etc.
    
    Returns:
        Dictionary of parameter names and values
    """
    try:
        state = ctx.deps.state
        
        # Initialize parameters dictionary
        params = {}
        
        # Extract time window
        params['since_time'] = state.since_time
        params['until_time'] = state.until_time
        
        # Extract product information
        params['product'] = state.product
        params['landscape'] = state.landscape
        params['region'] = state.region
        params['cluster_name'] = state.cluster_name
        
        # Extract alert information
        params['alert_title'] = state.alert_title
        params['alert_category'] = state.alert_category
        params['condition_name'] = state.condition_name
        
        # Extract keywords from alert title for search
        if state.alert_title:
            # Simple keyword extraction (could be improved with NLP)
            keywords = [word.lower() for word in state.alert_title.split() 
                       if len(word) > 3 and word.lower() not in ['with', 'from', 'this', 'that', 'than']]
            if keywords:
                params['alert_keyword'] = keywords[0]  # Use first keyword
        
        # Extract entity-specific parameters
        if state.entities:
            primary_entity = next((e for e in state.entities if e.is_primary), state.entities[0])
            
            # Common parameters from primary entity
            params['entity_guid'] = primary_entity.entity_guid
            params['entity_name'] = primary_entity.entity_name
            params['entity_type'] = primary_entity.entity_type
            
            # Type-specific parameters
            if primary_entity.entity_type == 'APPLICATION':
                params['application_name'] = primary_entity.entity_name
            
            elif primary_entity.entity_type in ['KUBERNETES_POD', 'KUBERNETES_DEPLOYMENT', 'KUBERNETES_SERVICE']:
                # Parse namespace and pod name from entity name if available
                if '/' in primary_entity.entity_name:
                    namespace, name = primary_entity.entity_name.split('/', 1)
                    params['namespace'] = namespace
                    
                    if primary_entity.entity_type == 'KUBERNETES_POD':
                        params['pod_name'] = name
                        params['pod_name_pattern'] = name.split('-')[0] if '-' in name else name
                    elif primary_entity.entity_type == 'KUBERNETES_DEPLOYMENT':
                        params['deployment_name'] = name
                    elif primary_entity.entity_type == 'KUBERNETES_SERVICE':
                        params['service_name'] = name
                else:
                    # If no namespace in entity name, use entity name directly
                    if primary_entity.entity_type == 'KUBERNETES_POD':
                        params['pod_name'] = primary_entity.entity_name
                        params['pod_name_pattern'] = primary_entity.entity_name.split('-')[0] if '-' in primary_entity.entity_name else primary_entity.entity_name
            
            # Add namespace from entity if available
            if primary_entity.namespace:
                params['namespace'] = primary_entity.namespace
        
        return params
    
    except Exception as e:
        logger.error(f"Error extracting query parameters from state: {str(e)}")
        return {"error": str(e)}


@database_agent.tool
async def get_comprehensive_context(
    ctx: RunContext,
    query_params: Dict[str, Any]
) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
    """
    Get comprehensive context data including general, product-specific, and RCA contexts.
    
    Args:
        query_params: Parameters to substitute into the query templates
        
    Returns:
        Nested dictionary with context types, query names, and results
    """
    try:
        state = ctx.deps.state
        database_client = ctx.deps.database_client
        
        # Extract product from params or state
        product = query_params.get('product', state.product)
        if not product:
            return {"error": "Product information is required"}
        
        # Get comprehensive context
        results = await database_client.get_comprehensive_context_async(product, query_params)
        
        # Log summary of results
        total_queries = sum(len(context) for context in results.values())
        total_rows = sum(sum(len(rows) for rows in context.values()) for context in results.values())
        logger.info(f"Retrieved comprehensive context: {total_queries} queries with {total_rows} total rows")
        
        return results
    
    except Exception as e:
        logger.error(f"Error retrieving comprehensive context: {str(e)}")
        return {"error": str(e)}


async def main():
    """Test function for the database agent."""
    # Set up test environment
    from lib.database_client import get_database_client
    
    # Create dummy IncidentState for testing
    from ai_incident_manager.models.workflow_state import IncidentState, Entity
    
    test_state = IncidentState(
        incident_id="test-incident-001",
        run_id="test-run-001",
        raw_alert={},
        title="High CPU Usage in Application",
        description="Application CPU usage exceeds 80% threshold",
        severity="critical",
        start_time=datetime.now(timezone.utc).isoformat(),
        alert_category="performance",
        alert_runbook="cpu_performance",
        alert_title="High CPU Usage in Application",
        condition_name="CPU Usage > 80%",
        product="mdm",
        nr_region="us",
        landscape="na1",
        region="us-east-1",
        cluster_name="mdm-prod-na1",
        since_time=(datetime.now(timezone.utc) - timedelta(hours=1)).isoformat(),
        until_time=datetime.now(timezone.utc).isoformat(),
        current_phase="entity_analysis_completed",
        entities=[
            Entity(
                entity_guid="entity-guid-001",
                entity_name="mdm-core-api",
                entity_type="APPLICATION",
                is_primary=True,
                product="mdm",
                landscape="na1",
                region="us-east-1",
                importance_score=8
            )
        ]
    )
    
    # Initialize dependencies
    deps = DatabaseAgentDeps(
        openai_client=openai_client,
        database_client=get_database_client(),
        state=test_state
    )
    
    # Create a test prompt
    prompt = """
    Please retrieve relevant database context for this incident:
    
    Incident ID: test-incident-001
    Product: MDM
    Primary Entity: mdm-core-api (APPLICATION)
    Alert: High CPU Usage in Application
    Time Window: Last 1 hour
    
    I need both general context and RCA-specific information that might help
    understand what's happening with this application.
    """
    
    # Run the agent
    try:
        result = await database_agent.run(prompt, deps=deps)
        print("Agent Result:")
        pprint(result.data)
    except Exception as e:
        print(f"Error running database agent: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main()) 