"""
Entity Analyzer Agent - Pydantic AI Agent for analyzing entities.

This agent analyzes entities involved in incidents to extract detailed information and context.
It uses tools to retrieve additional information from New Relic API and other sources to build
a comprehensive understanding of the entity and its relationships.
"""

import os
import re
import json
import logging
import yaml
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple, cast
from dataclasses import dataclass
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel
from pydantic import BaseModel, Field
from openai import AsyncOpenAI, AsyncAzureOpenAI
import dotenv
import asyncio
from devtools import pprint
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.base import NewRelicGraphQLError
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.logs import NewRelicLogsClient
from lib.new_relic.analyzer import EntityAnalyzer

from ai_incident_manager.services.entity_type_service import get_entity_type_service
from ai_incident_manager.models.workflow_state import (
    Entity, IncidentState, EntityAnalyzerAgentResponse
)

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize OpenAI client
required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
azure_openai_enabled = all(var in os.environ for var in required_azure_vars)

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    model = OpenAIModel(os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"), openai_client=openai_client)
else:
    logger.error("Azure OpenAI credentials not found. This application requires Azure OpenAI.")
    logger.error(f"Missing environment variables: {[var for var in required_azure_vars if var not in os.environ]}")
    raise EnvironmentError("Azure OpenAI credentials are required but not found in environment variables.")

@dataclass
class EntityAnalyzerDeps:
    """Dependencies for the entity analyzer agent."""
    openai_client: AsyncAzureOpenAI
    nr_query_client: NewRelicQueryClient
    nr_logs_client: NewRelicLogsClient
    entity_analyzer: EntityAnalyzer
    entity_types: List[Dict[str, Any]]
    state: IncidentState

# Entity Analyzer Agent
entity_analyzer_agent = Agent(
    model,
    retries=3,
    deps_type=EntityAnalyzerDeps,
    result_type=EntityAnalyzerAgentResponse,
    system_prompt="""You are an AI incident response analyst specializing in entity analysis.
Your task is to analyze entities involved in an incident to extract useful information and context.

Given an entity GUID and metadata, you will:
1. Determine the entity type and its role in the system
2. Identify the most important metrics and logs to collect for this entity
3. Map the entity's relationships to other entities in the topology
4. Assess the potential impact of issues with this entity

Your goal is to build a comprehensive understanding of the entity to assist with incident investigation.

Key information to determine:
- Entity type (KUBERNETES_POD, KUBERNETES_NODE, APPLICATION, DATABASE, etc.)
- Entity name and identifying details
- Cluster ID (if applicable)
- Product association
- Region and landscape
- Important metrics to monitor
- Types of logs to collect
- Related entities that might be affected
- Position in the topology (e.g., frontend, backend, database, etc.)
- Tags for the entity

"""
)

@entity_analyzer_agent.tool()
async def get_entity_details(
    ctx: RunContext,
    entity_guid: str
) -> Dict[str, Any]:
    """
    Fetch comprehensive details about an entity from New Relic using EntityAnalyzer.
    
    Args:
        entity_guid: The GUID of the entity
        
    Returns:
        Detailed information about the entity including metadata
    """
    deps = cast(EntityAnalyzerDeps, ctx.deps)
    entity_analyzer = deps.entity_analyzer
    
    try:
        # Get entity details from New Relic
        entity_details = await entity_analyzer.get_entity_details(entity_guid)
        
        if not entity_details:
            return {
                "error": f"Entity with GUID {entity_guid} not found",
                "status": "ERROR"
            }
        
        return {
            "status": "SUCCESS",
            "entity": entity_details
        }
    except NewRelicGraphQLError as e:
        return {
            "error": str(e),
            "status": "ERROR"
        }
    except Exception as e:
        return {
            "error": f"Unexpected error while fetching entity details: {str(e)}",
            "status": "ERROR"
        }

async def main():
    """Test function for the entity analyzer agent."""
    # Load test data
    test_entity_guid = "MTA5MzYyMHxJTkZSQXxOQXwyOTE5NzU3MDk1NDg0NTA3Nzcz"  # Replace with a real entity GUID
    
    # Initialize New Relic clients
    api_key = os.environ.get("NEWRELIC_API_KEY")
    account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        raise ValueError("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set in environment variables")
        
    graphql_client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
    query_client = NewRelicQueryClient(graphql_client)
    logs_client = NewRelicLogsClient(graphql_client)
    
    # Initialize the entity analyzer
    entity_analyzer = EntityAnalyzer(graphql_client, debug=True)
    
    # Get entity types from service
    entity_type_service = get_entity_type_service()
    entity_types = entity_type_service.get_all_entity_types()
    
    # Set up dependencies for the agent
    deps = EntityAnalyzerDeps(
        openai_client=openai_client,
        nr_query_client=query_client,
        nr_logs_client=logs_client,
        entity_analyzer=entity_analyzer,
        entity_types=entity_types
    )
    
    # Create a sample incident ID and test timeline
    test_incident_id = "test-incident-001"
    timeline = []
    
    # Create a timeline event for starting entity analysis
    start_event = {
        "id": f"evt-start-entity-analysis-{datetime.now().timestamp():.0f}",
        "incidentId": test_incident_id,
        "timestamp": datetime.now().isoformat(),
        "title": "Starting Entity Analysis",
        "description": f"Analyzing entity {test_entity_guid} for detailed information",
        "type": "investigation",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Analysis", "variant": "secondary"}
        ]
    }
    timeline.append(start_event)
    
    # Get entity details
    try:
        # Create a timeline event for entity retrieval
        retrieval_event = {
            "id": f"evt-entity-retrieval-{test_entity_guid[:8]}-{datetime.now().timestamp():.0f}",
            "incidentId": test_incident_id,
            "timestamp": datetime.now().isoformat(),
            "title": "Retrieved Entity Details",
            "description": f"Retrieved detailed information for entity {test_entity_guid}",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Entity", "variant": "secondary"}
            ]
        }
        timeline.append(retrieval_event)
        
        entity_details = await entity_analyzer.get_entity_details(test_entity_guid)
        
        # Format entity details for the prompt
        entity_details_json = json.dumps(entity_details, indent=2)
        
        # Prepare the user prompt
        user_prompt = f"""
        Please analyze the following entity from New Relic:
        
        Entity GUID: {test_entity_guid}
        
        ```json
        {entity_details_json}
        ```
        
        I need a detailed analysis of this entity, including:
        1. What type of entity is this and what is its role in the system?
        2. What are the most important metrics to monitor for this entity?
        3. What logs should be collected for this entity?
        4. What other entities are related to this one?
        5. What is the potential impact if this entity has issues?
        """
        
        # Run the agent
        result = await entity_analyzer_agent.run(user_prompt, deps=deps)
        entity_analysis = result.data
        
        # Create a timeline event for completed entity analysis
        analysis_event = {
            "id": f"evt-entity-analysis-{test_entity_guid[:8]}-{datetime.now().timestamp():.0f}",
            "incidentId": test_incident_id,
            "timestamp": datetime.now().isoformat(),
            "title": f"Analyzed Entity: {entity_analysis.entity_details.entity_name}",
            "description": f"Entity type: {entity_analysis.entity_details.entity_type}, Health status: {entity_analysis.entity_health_status}",
            "type": "analysis",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Entity", "variant": "secondary"},
                {"label": entity_analysis.entity_details.entity_type, "variant": "outline"}
            ]
        }
        timeline.append(analysis_event)
        
        # Print the results
        pprint(entity_analysis.model_dump())
        
        # Print timeline events
        print("\nTimeline Events:")
        for event in timeline:
            tags_str = ", ".join([f"{tag['label']}:{tag['variant']}" for tag in event["tags"]])
            print(f"- [{event['timestamp']}] {event['title']}")
            print(f"  Type: {event['type']}, Source: {event['source']}, Tags: {tags_str}")
            print(f"  {event['description']}")
            print()
        
    except Exception as e:
        # Create a timeline event for entity analysis error
        error_event = {
            "id": f"evt-entity-error-{test_entity_guid[:8]}-{datetime.now().timestamp():.0f}",
            "incidentId": test_incident_id,
            "timestamp": datetime.now().isoformat(),
            "title": "Entity Analysis Error",
            "description": f"Error analyzing entity {test_entity_guid}: {str(e)}",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        timeline.append(error_event)
        
        logger.error(f"Error running entity analyzer agent: {str(e)}")
        
        # Print timeline events with error
        print("\nTimeline Events:")
        for event in timeline:
            tags_str = ", ".join([f"{tag['label']}:{tag['variant']}" for tag in event["tags"]])
            print(f"- [{event['timestamp']}] {event['title']}")
            print(f"  Type: {event['type']}, Source: {event['source']}, Tags: {tags_str}")
            print(f"  {event['description']}")
            print()

if __name__ == "__main__":
    asyncio.run(main())
