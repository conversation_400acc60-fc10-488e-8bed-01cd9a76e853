"""
Main application module for the AI Incident Manager.

This module ties together all the components of the system and provides the entry point
for the application.
"""

import os
import json
import logging
import asyncio
from typing import Dict, Any, Optional, Union
from datetime import datetime
import uuid

import dotenv
from fastapi import FastAPI, BackgroundTasks, Request, Response, HTTPException, Depends
from pydantic import BaseModel

from ai_incident_manager.incident_manager import IncidentManager
from ai_incident_manager.message_receiver.service_bus_receiver import ServiceBusReceiver, process_alert_message
from ai_incident_manager.parsers.alert_parser import extract_incident_info
from ai_incident_manager.workflow.flow import run_analysis_workflow

# Load environment variables
dotenv.load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="AI Incident Manager",
    description="AI-powered incident management system",
    version="0.1.0"
)

# Initialize incident manager
incident_manager = IncidentManager()


# Request models
class AlertData(BaseModel):
    """Model for alert data received via API."""
    id: str
    title: str
    description: Optional[str] = None
    severity: Optional[str] = None
    # Additional fields can be added as needed
    details: Optional[Dict[str, Any]] = None


class IncidentStatusRequest(BaseModel):
    """Model for incident status update requests."""
    status: str


# Background tasks
async def process_incident_in_background(incident_data: Dict[str, Any]):
    """
    Process an incident in the background.
    
    Args:
        incident_data: Incident data
    """
    try:
        # Create incident
        incident_id = incident_manager.create_incident(incident_data)
        logger.info(f"Created incident {incident_id}")
        
        # Prepare workflow state
        state = incident_manager.prepare_workflow_state(incident_data)
        
        # Run analysis workflow
        result_state = await run_analysis_workflow(state)
        
        # Update incident with workflow results
        if result_state and result_state["output"]:
            # Update the incident status
            if result_state["output"]["status"]:
                incident_manager.update_incident_status(incident_id, result_state["output"]["status"])
            
            # Add investigation steps
            for note in result_state["investigation_state"]["notes"]:
                step_data = {
                    "step": note["step"],
                    "timestamp": note["timestamp"] or datetime.utcnow().isoformat(),
                    "title": note["title"],
                    "description": note["content"],
                    "investigationDetails": {}
                }
                incident_manager.add_investigation_step(incident_id, step_data)
            
            # Add timeline events for major steps
            for i, note in enumerate(result_state["investigation_state"]["notes"]):
                # Only add timeline events for major steps
                if note["title"] in ["Alert Analysis", "Metrics Analysis", "Logs Analysis", 
                                    "Root Cause Analysis", "Remediation Recommendations"]:
                    event_data = {
                        "id": f"step-{i}-{uuid.uuid4().hex[:8]}",
                        "timestamp": note["timestamp"] or datetime.utcnow().isoformat(),
                        "title": note["title"],
                        "description": note["content"][:200] + "..." if len(note["content"]) > 200 else note["content"],
                        "type": "investigation",
                        "source": "AI Incident Manager",
                        "tags": ["investigation", note["title"].lower().replace(" ", "_")]
                    }
                    incident_manager.add_timeline_event(incident_id, event_data)
        
        logger.info(f"Completed processing for incident {incident_id}")
        
    except Exception as e:
        logger.error(f"Error processing incident: {str(e)}", exc_info=True)


async def listen_for_alerts_in_background():
    """Listen for alerts from the Service Bus queue in the background."""
    try:
        logger.info("Starting Service Bus receiver for alerts")
        await ServiceBusReceiver().receive_messages(
            callback=process_alert,
            max_messages=None,  # Run indefinitely
            timeout=None
        )
    except Exception as e:
        logger.error(f"Error in Service Bus receiver: {str(e)}", exc_info=True)


async def process_alert(alert_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process an alert from Service Bus.
    
    Args:
        alert_data: Alert data from Service Bus
        
    Returns:
        Processed incident data
    """
    logger.info(f"Processing alert: {alert_data.get('title', 'Unknown Alert')}")
    
    # Extract incident information
    incident_data = extract_incident_info(alert_data)
    
    # Process the incident in the background
    asyncio.create_task(process_incident_in_background(incident_data))
    
    return incident_data


# API routes
@app.get("/")
async def root():
    """Root endpoint for the API."""
    return {"message": "AI Incident Management API"}


@app.post("/alerts", status_code=202)
async def receive_alert(
    alert: AlertData,
    background_tasks: BackgroundTasks
):
    """
    Receive an alert via the API.
    
    Args:
        alert: Alert data
        background_tasks: Background tasks
        
    Returns:
        Acceptance message
    """
    logger.info(f"Received alert via API: {alert.title}")
    
    # Convert to dictionary format expected by the system
    alert_dict = alert.dict()
    
    # Add some fields that might be missing in API format
    alert_dict["severity"] = alert_dict.get("severity", "INFO").upper()
    
    # Process the alert to extract incident information
    incident_data = extract_incident_info(alert_dict)
    
    # Process incident in background
    background_tasks.add_task(
        asyncio.create_task,
        process_incident_in_background(incident_data)
    )
    
    return {
        "message": "Alert received and processing started",
        "incident_id": incident_data.get("id")
    }


@app.get("/incidents/{incident_id}")
async def get_incident(incident_id: str):
    """
    Get an incident by ID.
    
    Args:
        incident_id: Incident ID
        
    Returns:
        Incident data
    """
    incident = incident_manager.get_incident(incident_id)
    
    if not incident:
        raise HTTPException(status_code=404, detail="Incident not found")
    
    return incident


@app.patch("/incidents/{incident_id}/status")
async def update_incident_status(
    incident_id: str,
    status_request: IncidentStatusRequest
):
    """
    Update an incident's status.
    
    Args:
        incident_id: Incident ID
        status_request: Status update request
        
    Returns:
        Success message
    """
    incident = incident_manager.get_incident(incident_id)
    
    if not incident:
        raise HTTPException(status_code=404, detail="Incident not found")
    
    incident_manager.update_incident_status(incident_id, status_request.status.upper())
    
    return {
        "message": f"Incident status updated to {status_request.status.upper()}",
        "incident_id": incident_id
    }


@app.get("/incidents")
async def list_incidents(
    limit: int = 10,
    status: Optional[str] = None
):
    """
    List incidents with optional filtering.
    
    Args:
        limit: Maximum number of incidents to return
        status: Filter by status
        
    Returns:
        List of incidents
    """
    # TODO: Implement this endpoint by adding a list_incidents method to IncidentManager
    return {
        "message": "Not implemented yet",
        "incidents": []
    }


# Event handlers
@app.on_event("startup")
async def startup_event():
    """Handler for application startup."""
    logger.info("Starting AI Incident Manager application")
    
    # Start Service Bus receiver if configured
    if os.environ.get("AZURE_SERVICE_BUS_CONNECTION_STRING") and os.environ.get("AZURE_SERVICE_BUS_QUEUE_NAME"):
        asyncio.create_task(listen_for_alerts_in_background())
        logger.info("Service Bus receiver started in background")


# Main entry point
def main():
    """Main entry point for the application."""
    import uvicorn
    uvicorn.run(
        "ai_incident_manager.main:app",
        host=os.environ.get("APP_HOST", "0.0.0.0"),
        port=int(os.environ.get("APP_PORT", "8000")),
        reload=True
    )


if __name__ == "__main__":
    main() 