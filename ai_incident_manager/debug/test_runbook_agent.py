#!/usr/bin/env python
"""
Test script for debugging the runbook agent with specific entity data
"""
import asyncio
import logging
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

from dotenv import load_dotenv
from openai import AsyncAzureOpenAI

from ai_incident_manager.agents.runbook_agent import (
    runbook_agent, RunbookAgentDeps, 
    get_pod_logs, get_pod_status, get_pod_metrics, get_pod_spec
)
from ai_incident_manager.services.runbook_service import get_runbook_service
from ai_incident_manager.services.metrics_collector import MetricsCollector

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

async def test_execute_runbook_steps(
    runbook_id: str, 
    entity_guid: str,
    verbose: bool = True
) -> Dict[str, Any]:
    """
    Test function to execute runbook steps for a specific entity.
    This is a standalone version of the execute_runbook_steps function.
    
    Args:
        runbook_id: ID of the runbook to execute
        entity_guid: GUID of the entity to run the runbook against
        verbose: Whether to print verbose debugging information
        
    Returns:
        Results of executing the runbook steps
    """
    try:
        # Initialize the metrics collector
        metrics_collector = MetricsCollector()
        
        # Initialize OpenAI client for the runbook agent
        azure_openai_enabled = "AZURE_OPENAI_ENDPOINT" in os.environ and "AZURE_OPENAI_API_KEY" in os.environ
        
        if azure_openai_enabled:
            openai_client = AsyncAzureOpenAI(
                azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
                azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
                api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
                api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
            )
        else:
            raise ValueError("Azure OpenAI is required for this script")
        
        # Set up dependencies for the runbook agent
        deps = RunbookAgentDeps(
            openai_client=openai_client,
            metrics_collector=metrics_collector
        )
        
        # Get the runbook details
        runbook_service = get_runbook_service()
        runbook = runbook_service.get_runbook(runbook_id)
        
        if not runbook:
            logger.error(f"Runbook not found: {runbook_id}")
            available_runbooks = [rb["id"] for rb in runbook_service.get_all_runbooks()]
            logger.info(f"Available runbooks: {available_runbooks}")
            return {
                "error": f"Runbook not found: {runbook_id}",
                "available_runbooks": available_runbooks
            }
            
        logger.info(f"Retrieved runbook: {runbook_id} - {runbook.get('name')}")
        
        # Prepare result with basic runbook info
        result = {
            "runbook_id": runbook_id,
            "runbook_name": runbook.get("name", "Unknown Runbook"),
            "runbook_description": runbook.get("description", ""),
            "entity_guid": entity_guid,
            "entity_name": None,
            "entity_type": None,
            "cluster_id": None,
            "execution_time": datetime.utcnow().isoformat(),
            "step_results": [],
            "issues_found": False
        }
        
        # Get entity details
        if verbose:
            logger.info(f"Getting entity details for: {entity_guid}")
            
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        
        if verbose:
            logger.info(f"Entity details: {entity_details}")
            
        if not entity_details:
            logger.error(f"Entity not found: {entity_guid}")
            return {
                **result,
                "error": "Entity not found",
                "message": f"Could not find entity with GUID: {entity_guid}",
                "issues_found": True,
                "summary": "Execution failed - the specified entity could not be found. Entity GUID is not associated with an active deployment.",
                "recommendations": [
                    "Verify the entity GUID provided in the request.",
                    "Check whether the entity (deployment) is still active in your Kubernetes cluster.",
                    "Consult your New Relic agent and configuration to ensure the deployment is being correctly monitored.",
                    "Retry execution with the corrected entity GUID or choose a different active deployment."
                ]
            }
            
        # Update result with entity details
        result["entity_name"] = entity_details.get("name", "unknown")
        result["entity_type"] = entity_details.get("type", "unknown")
        
        # Extract cluster ID if available
        cluster_id = None
        tags = entity_details.get("tags", [])
        
        if verbose:
            logger.info(f"Entity tags: {tags}")
            
        if isinstance(tags, list):
            for tag in tags:
                if isinstance(tag, dict) and tag.get("key") == "clusterName":
                    values = tag.get("values", [])
                    if isinstance(values, list) and values:
                        cluster_id = values[0]
                        break
                # Also check for k8s.clusterName
                elif isinstance(tag, dict) and tag.get("key") == "k8s.clusterName":
                    values = tag.get("values", [])
                    if isinstance(values, list) and values:
                        cluster_id = values[0]
                        break
                        
        result["cluster_id"] = cluster_id
        logger.info(f"Cluster ID: {cluster_id}")
        
        # Execute each step
        steps = runbook.get("steps", [])
        logger.info(f"Found {len(steps)} steps to execute")
        
        for i, step in enumerate(steps, 1):
            step_title = step.get("title", "Unnamed Step")
            step_description = step.get("description", "")
            tool_name = step.get("tool", "")
            tool_params = step.get("parameters", {}).copy()  # Make a copy to avoid modifying original
            
            # Add entity_guid to parameters if needed
            if tool_name in ["get_pod_logs", "get_pod_status", "get_pod_metrics", "get_pod_spec"]:
                tool_params["entity_guid"] = entity_guid
                
            logger.info(f"Step {i}/{len(steps)}: {step_title} using tool {tool_name}")
            if verbose:
                logger.info(f"  Parameters: {tool_params}")
            
            # Find the corresponding tool
            tool_method = None
            if tool_name == "get_pod_logs":
                tool_method = get_pod_logs
            elif tool_name == "get_pod_status":
                tool_method = get_pod_status
            elif tool_name == "get_pod_metrics":
                tool_method = get_pod_metrics
            elif tool_name == "get_pod_spec":
                tool_method = get_pod_spec
                
            if not tool_method:
                error_msg = f"Tool not found: {tool_name}"
                logger.error(error_msg)
                step_result = {
                    "step_title": step_title,
                    "step_description": step_description,
                    "tool": tool_name,
                    "parameters": tool_params,
                    "result": {"error": error_msg},
                    "summary": f"Step failed: {error_msg}",
                    "issues_found": True,
                    "timestamp": datetime.utcnow().isoformat()
                }
                result["step_results"].append(step_result)
                continue
                
            # Execute the tool
            try:
                # We need to create a run context for the tool
                class MockRunContext:
                    def __init__(self, deps):
                        self.deps = deps
                
                ctx = MockRunContext(deps)
                
                # Execute the tool with verbose logging
                if verbose:
                    logger.info(f"  Executing {tool_name}...")
                    
                tool_result = await tool_method(ctx, **tool_params)
                
                if verbose:
                    logger.info(f"  Tool result: {tool_result}")
                
                # Check for errors
                issues_found = "error" in tool_result
                
                if issues_found and verbose:
                    logger.warning(f"  Issues found in step {i}: {tool_result.get('error')}")
                
                # Generate a summary (simplified version)
                if issues_found:
                    summary = f"Issues found: {tool_result.get('error', 'Unknown error')}"
                else:
                    summary = f"Step completed successfully"
                
                step_result = {
                    "step_title": step_title,
                    "step_description": step_description,
                    "tool": tool_name,
                    "parameters": tool_params,
                    "result": tool_result,
                    "summary": summary,
                    "issues_found": issues_found,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                if issues_found:
                    result["issues_found"] = True
                    
            except Exception as e:
                error_msg = f"Step failed with error: {str(e)}"
                logger.error(error_msg)
                if verbose:
                    logger.exception("Exception details:")
                    
                step_result = {
                    "step_title": step_title,
                    "step_description": step_description,
                    "tool": tool_name,
                    "parameters": tool_params,
                    "result": {"error": str(e)},
                    "summary": error_msg,
                    "issues_found": True,
                    "timestamp": datetime.utcnow().isoformat()
                }
                result["issues_found"] = True
                
            result["step_results"].append(step_result)
        
        # Generate overall summary if not already set
        if "summary" not in result:
            if result["issues_found"]:
                result["summary"] = f"Completed with issues - {len(result['step_results'])} steps executed"
            else:
                result["summary"] = f"Successfully executed {len(result['step_results'])} steps"
                
        return result
        
    except Exception as e:
        logger.error(f"Error executing runbook steps: {str(e)}")
        if verbose:
            logger.exception("Exception details:")
        return {"error": str(e)}

async def test_direct_tools(entity_guid: str, verbose: bool = True):
    """
    Test pod-related tools directly for a given entity
    """
    # Initialize the metrics collector
    metrics_collector = MetricsCollector()
    
    # Initialize OpenAI client for the runbook agent
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    
    # Set up dependencies for the runbook agent
    deps = RunbookAgentDeps(
        openai_client=openai_client,
        metrics_collector=metrics_collector
    )
    
    class MockRunContext:
        def __init__(self, deps):
            self.deps = deps
    
    ctx = MockRunContext(deps)
    
    # Test each tool directly
    logger.info(f"Testing get_pod_status for entity: {entity_guid}")
    try:
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        if verbose:
            logger.info(f"Entity details: {entity_details}")
        
        result = await get_pod_status(ctx, entity_guid=entity_guid)
        logger.info(f"get_pod_status result: {result}")
    except Exception as e:
        logger.error(f"Error in get_pod_status: {str(e)}")
        if verbose:
            logger.exception("Exception details:")
    
    logger.info(f"Testing get_pod_logs for entity: {entity_guid}")
    try:
        result = await get_pod_logs(ctx, entity_guid=entity_guid, time_window_minutes=30)
        logger.info(f"get_pod_logs result: {result}")
    except Exception as e:
        logger.error(f"Error in get_pod_logs: {str(e)}")
        if verbose:
            logger.exception("Exception details:")
    
    logger.info(f"Testing get_pod_metrics for entity: {entity_guid}")
    try:
        result = await get_pod_metrics(ctx, entity_guid=entity_guid)
        logger.info(f"get_pod_metrics result: {result}")
    except Exception as e:
        logger.error(f"Error in get_pod_metrics: {str(e)}")
        if verbose:
            logger.exception("Exception details:")
    
    logger.info(f"Testing get_pod_spec for entity: {entity_guid}")
    try:
        result = await get_pod_spec(ctx, entity_guid=entity_guid)
        logger.info(f"get_pod_spec result: {result}")
    except Exception as e:
        logger.error(f"Error in get_pod_spec: {str(e)}")
        if verbose:
            logger.exception("Exception details:")

async def test_runbook_agent_run(runbook_id: str, entity_guid: str):
    """
    Test the runbook agent run method to execute a runbook for an entity
    """
    # Initialize the metrics collector
    metrics_collector = MetricsCollector()
    
    # Initialize OpenAI client for the runbook agent
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    
    # Set up dependencies for the runbook agent
    deps = RunbookAgentDeps(
        openai_client=openai_client,
        metrics_collector=metrics_collector
    )
    
    # Execute the runbook using the runbook agent
    prompt = f"Execute runbook {runbook_id} for entity {entity_guid}"
    logger.info(f"Calling runbook agent with prompt: {prompt}")
    
    try:
        result = await runbook_agent.run(prompt, deps=deps)
        logger.info(f"Runbook agent result: {result}")
        return result
    except Exception as e:
        logger.error(f"Error in runbook agent run: {str(e)}")
        logger.exception("Exception details:")
        return {"error": str(e)}

async def main():
    # The entity GUID from the error logs
    entity_guid = "MTA5MzYyMHxJTkZSQXxOQXwtNzcyOTU1MzAxNDQ5ODQ0MDQ1MA"
    runbook_id = "kubernetes_deployment_unavailable_pods_investigation"
    
    logger.info("=" * 60)
    logger.info("TESTING ENTITY DETAILS RETRIEVAL")
    logger.info("=" * 60)
    
    metrics_collector = MetricsCollector()
    try:
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        logger.info(f"Entity details: {entity_details}")
    except Exception as e:
        logger.error(f"Error getting entity details: {str(e)}")
        logger.exception("Exception details:")
    
    logger.info("\n" + "=" * 60)
    logger.info("TESTING DIRECT TOOLS")
    logger.info("=" * 60)
    
    await test_direct_tools(entity_guid)
    
    logger.info("\n" + "=" * 60)
    logger.info("TESTING EXECUTE RUNBOOK STEPS")
    logger.info("=" * 60)
    
    result = await test_execute_runbook_steps(runbook_id, entity_guid)
    logger.info(f"Runbook execution result: {result}")
    
    logger.info("\n" + "=" * 60)
    logger.info("TESTING RUNBOOK AGENT RUN")
    logger.info("=" * 60)
    
    result = await test_runbook_agent_run(runbook_id, entity_guid)
    logger.info(f"Runbook agent run result: {result}")

if __name__ == "__main__":
    asyncio.run(main()) 