#!/usr/bin/env python
"""
Focused debugging script for entity details and tags handling.
Specifically targets the 'str' object has no attribute 'value' error.
"""
import asyncio
import logging
import os
from pprint import pformat
from typing import Any, Dict, List, Optional

from dotenv import load_dotenv

from ai_incident_manager.services.metrics_collector import MetricsCollector
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

async def debug_entity_tags(entity_guid: str):
    """
    Focused debugging function for entity tags handling
    """
    logger.info(f"Debugging entity tags for: {entity_guid}")
    
    # First, try to get entity details directly using the New Relic client
    logger.info("Attempting to get entity details directly from New Relic client...")
    
    # Create New Relic clients
    nr_api_key = os.environ.get("NEWRELIC_API_KEY")
    nr_account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
    
    if not nr_api_key or not nr_account_id:
        logger.error("Missing New Relic API key or account ID environment variables")
        return
        
    # Initialize GraphQL client
    graphql_client = NewRelicGraphQLClient(
        api_key=nr_api_key,
        account_id=nr_account_id
    )
    
    # Initialize Query client
    query_client = NewRelicQueryClient(graphql_client)
    
    try:
        # Get entity details directly
        nr_details = query_client.get_entity_details(entity_guid)
        logger.info(f"Raw New Relic entity details: {pformat(nr_details)}")
        
        if nr_details is None:
            logger.error("Entity details returned None")
            return
            
        # Inspect the tags specifically
        tags = nr_details.get("tags", [])
        logger.info(f"Raw tags: {pformat(tags)}")
        
        # Check the type and structure of tags
        logger.info(f"Tags type: {type(tags)}")
        if isinstance(tags, list):
            for i, tag in enumerate(tags):
                logger.info(f"Tag {i} type: {type(tag)}")
                logger.info(f"Tag {i} content: {pformat(tag)}")
                
                # If this is a dict, check for the clusterName key
                if isinstance(tag, dict):
                    if tag.get("key") == "clusterName":
                        values = tag.get("values", [])
                        logger.info(f"ClusterName values: {values}, type: {type(values)}")
                        
                        if values and isinstance(values, list):
                            cluster_id = values[0]
                            logger.info(f"Extracted cluster_id: {cluster_id}")
                        else:
                            logger.info("No valid values found for clusterName tag")
                # Check if it's a string, which might be causing the error
                elif isinstance(tag, str):
                    logger.warning(f"Tag is a string, not a dict! This may cause the error: {tag}")
                    # Try to parse if it looks like JSON
                    if tag.startswith("{") and tag.endswith("}"):
                        logger.info("Tag appears to be a JSON string, might need parsing")
                
        else:
            logger.warning(f"Tags is not a list! Type: {type(tags)}")
    except Exception as e:
        logger.error(f"Error getting raw entity details: {str(e)}")
        logger.exception("Exception details:")
    
    # Now try through the MetricsCollector for comparison
    logger.info("\nNow trying through MetricsCollector...")
    metrics_collector = MetricsCollector()
    try:
        entity_details = await metrics_collector.get_entity_details(entity_guid)
        logger.info(f"MetricsCollector entity details: {pformat(entity_details)}")
        
        # Look at the tags from the metrics collector
        tags = entity_details.get("tags", []) if entity_details else []
        logger.info(f"MetricsCollector tags: {pformat(tags)}")
    except Exception as e:
        logger.error(f"Error in MetricsCollector.get_entity_details: {str(e)}")
        logger.exception("Exception details:")

    return

def main():
    # Test with the problematic entity GUID from the logs
    entity_guid = "MTA5MzYyMHxJTkZSQXxOQXwtNzcyOTU1MzAxNDQ5ODQ0MDQ1MA"
    
    logger.info("=" * 60)
    logger.info("DEBUGGING ENTITY TAGS")
    logger.info("=" * 60)
    
    # Run the async function using asyncio.run
    asyncio.run(debug_entity_tags(entity_guid))

if __name__ == "__main__":
    main() 