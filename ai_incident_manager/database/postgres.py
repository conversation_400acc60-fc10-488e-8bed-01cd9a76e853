"""
PostgreSQL database module for the incident management system.

This module handles all database operations for storing and retrieving incidents,
investigation steps, timeline events, and related data.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

import psycopg2
from psycopg2.extras import J<PERSON>, DictCursor
import dotenv

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PostgresDB:
    """PostgreSQL database handler for the incident management system."""
    
    def __init__(self):
        """Initialize the database connection."""
        self.conn_params = {
            "dbname": os.environ.get("POSTGRES_DB", "incident_manager"),
            "user": os.environ.get("POSTGRES_USER", "postgres"),
            "password": os.environ.get("POSTGRES_PASSWORD", "postgres"),
            "host": os.environ.get("POSTGRES_HOST", "localhost"),
            "port": os.environ.get("POSTGRES_PORT", "5432")
        }
    
    def get_connection(self):
        """Get a database connection."""
        return psycopg2.connect(**self.conn_params)
    
    def create_tables(self):
        """Create the necessary database tables if they don't exist."""
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                # Create incidents table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS incidents (
                    id UUID PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT,
                    severity VARCHAR(50),
                    status VARCHAR(50),
                    alert_category VARCHAR(100),
                    runbook TEXT,
                    created_at TIMESTAMP WITH TIME ZONE,
                    updated_at TIMESTAMP WITH TIME ZONE,
                    raw_alert JSONB,
                    entities JSONB,
                    metrics JSONB,
                    likely_causes JSONB,
                    output JSONB
                );
                """)
                
                # Create investigation steps table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS investigation_steps (
                    id UUID PRIMARY KEY,
                    incident_id UUID REFERENCES incidents(id) ON DELETE CASCADE,
                    step_number INTEGER,
                    title TEXT,
                    content TEXT,
                    timestamp TIMESTAMP WITH TIME ZONE,
                    metadata JSONB,
                    UNIQUE(incident_id, step_number)
                );
                """)
                
                # Create timeline events table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS timeline_events (
                    id VARCHAR(255) PRIMARY KEY,
                    incident_id UUID REFERENCES incidents(id) ON DELETE CASCADE,
                    event_type VARCHAR(100),
                    title TEXT,
                    description TEXT,
                    timestamp TIMESTAMP WITH TIME ZONE,
                    source VARCHAR(100),
                    metadata JSONB
                );
                """)
                
                # Create entity metrics table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_metrics (
                    id UUID PRIMARY KEY,
                    incident_id UUID REFERENCES incidents(id) ON DELETE CASCADE,
                    entity_guid VARCHAR(255),
                    entity_name VARCHAR(255),
                    metric_name VARCHAR(255),
                    metric_data JSONB,
                    collected_at TIMESTAMP WITH TIME ZONE
                );
                """)
                
                # Create entity logs table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_logs (
                    id UUID PRIMARY KEY,
                    incident_id UUID REFERENCES incidents(id) ON DELETE CASCADE,
                    entity_guid VARCHAR(255),
                    entity_name VARCHAR(255),
                    log_data JSONB,
                    timestamp TIMESTAMP WITH TIME ZONE
                );
                """)
                
                # Create tags table for incident categorization
                cur.execute("""
                CREATE TABLE IF NOT EXISTS incident_tags (
                    id UUID PRIMARY KEY,
                    incident_id UUID REFERENCES incidents(id) ON DELETE CASCADE,
                    tag_type VARCHAR(100),
                    tag_value VARCHAR(255),
                    UNIQUE(incident_id, tag_type, tag_value)
                );
                """)
                
                # Create similar incidents table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS similar_incidents (
                    id UUID PRIMARY KEY,
                    source_incident_id UUID REFERENCES incidents(id) ON DELETE CASCADE,
                    similar_incident_id UUID REFERENCES incidents(id) ON DELETE CASCADE,
                    similarity_score FLOAT,
                    similarity_reason TEXT,
                    UNIQUE(source_incident_id, similar_incident_id)
                );
                """)
                
                # Entity tables for storing entity information
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_types (
                    id UUID PRIMARY KEY,
                    type_name VARCHAR(100) UNIQUE,
                    description TEXT,
                    importance INTEGER DEFAULT 5,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                """)
                
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_type_metrics (
                    id UUID PRIMARY KEY,
                    entity_type_id UUID REFERENCES entity_types(id) ON DELETE CASCADE,
                    metric_name VARCHAR(100),
                    description TEXT,
                    importance INTEGER DEFAULT 5,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(entity_type_id, metric_name)
                );
                """)
                
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_type_logs (
                    id UUID PRIMARY KEY,
                    entity_type_id UUID REFERENCES entity_types(id) ON DELETE CASCADE,
                    log_type VARCHAR(100),
                    importance INTEGER DEFAULT 5,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(entity_type_id, log_type)
                );
                """)
                
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_type_relationships (
                    id UUID PRIMARY KEY,
                    entity_type_id UUID REFERENCES entity_types(id) ON DELETE CASCADE,
                    related_type VARCHAR(100),
                    relationship VARCHAR(100),
                    importance INTEGER DEFAULT 5,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(entity_type_id, related_type, relationship)
                );
                """)
                
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_instances (
                    id UUID PRIMARY KEY,
                    entity_guid VARCHAR(255) UNIQUE,
                    entity_name VARCHAR(255),
                    entity_type_id UUID REFERENCES entity_types(id),
                    cluster_id VARCHAR(100),
                    product VARCHAR(100),
                    region VARCHAR(50),
                    landscape VARCHAR(50),
                    metadata JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                """)
                
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_instance_metrics (
                    id UUID PRIMARY KEY,
                    entity_instance_id UUID REFERENCES entity_instances(id) ON DELETE CASCADE,
                    metric_name VARCHAR(100),
                    metric_data JSONB,
                    collected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                """)
                
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_instance_logs (
                    id UUID PRIMARY KEY,
                    entity_instance_id UUID REFERENCES entity_instances(id) ON DELETE CASCADE,
                    log_type VARCHAR(100),
                    log_data JSONB,
                    collected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                """)
                
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_instance_relationships (
                    id UUID PRIMARY KEY,
                    source_entity_id UUID REFERENCES entity_instances(id) ON DELETE CASCADE,
                    target_entity_id UUID REFERENCES entity_instances(id) ON DELETE CASCADE,
                    relationship VARCHAR(100),
                    metadata JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(source_entity_id, target_entity_id, relationship)
                );
                """)
                
                cur.execute("""
                CREATE TABLE IF NOT EXISTS incident_entities (
                    id UUID PRIMARY KEY,
                    incident_id UUID REFERENCES incidents(id) ON DELETE CASCADE,
                    entity_instance_id UUID REFERENCES entity_instances(id) ON DELETE CASCADE,
                    is_primary BOOLEAN DEFAULT FALSE,
                    analysis_data JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(incident_id, entity_instance_id)
                );
                """)
                
                # New tables for alert condition categorization
                
                # Alert categories table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS alert_categories (
                    id SERIAL PRIMARY KEY,
                    category_name VARCHAR(100) UNIQUE,
                    title_pattern TEXT,
                    description TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                """)
                
                # Alert conditions table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS alert_conditions (
                    condition_id VARCHAR(100) PRIMARY KEY,
                    policy_id VARCHAR(100),
                    name TEXT,
                    description TEXT,
                    nrql_query TEXT,
                    enabled BOOLEAN,
                    threshold_operator VARCHAR(50),
                    threshold_value FLOAT,
                    threshold_duration INTEGER,
                    threshold_occurrences VARCHAR(50),
                    priority VARCHAR(50),
                    runbook_url TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    condition_data JSONB  -- Full condition data from New Relic
                );
                """)
                
                # Mapping between conditions and categories
                cur.execute("""
                CREATE TABLE IF NOT EXISTS condition_category_mapping (
                    id SERIAL PRIMARY KEY,
                    condition_id VARCHAR(100) REFERENCES alert_conditions(condition_id),
                    category_id INTEGER REFERENCES alert_categories(id),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(condition_id, category_id)
                );
                """)
                
                # Runbook steps for categories
                cur.execute("""
                CREATE TABLE IF NOT EXISTS category_runbooks (
                    id SERIAL PRIMARY KEY,
                    category_id INTEGER REFERENCES alert_categories(id),
                    step_number INTEGER,
                    title TEXT,
                    description TEXT,
                    tool_name VARCHAR(100),
                    tool_parameters JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(category_id, step_number)
                );
                """)
                
                # Default metrics to collect for each category
                cur.execute("""
                CREATE TABLE IF NOT EXISTS category_metrics (
                    id SERIAL PRIMARY KEY,
                    category_id INTEGER REFERENCES alert_categories(id),
                    metric_name VARCHAR(100),
                    description TEXT,
                    importance INTEGER,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(category_id, metric_name)
                );
                """)
                
                # Entity relationships for categories
                cur.execute("""
                CREATE TABLE IF NOT EXISTS category_entity_relationships (
                    id SERIAL PRIMARY KEY,
                    category_id INTEGER REFERENCES alert_categories(id),
                    source_type VARCHAR(100),
                    target_type VARCHAR(100),
                    relationship_type VARCHAR(100),
                    metrics_to_collect JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(category_id, source_type, target_type)
                );
                """)
                
                # Likely causes for each category
                cur.execute("""
                CREATE TABLE IF NOT EXISTS category_likely_causes (
                    id SERIAL PRIMARY KEY,
                    category_id INTEGER REFERENCES alert_categories(id),
                    cause_description TEXT,
                    probability INTEGER,  -- 1-100 rating
                    symptoms TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(category_id, cause_description)
                );
                """)
                
                conn.commit()
                logger.info("Database tables created successfully")
    
    def create_incident(self, incident_data: Dict[str, Any]) -> str:
        """
        Create a new incident in the database.
        
        Args:
            incident_data: Dictionary containing incident information
            
        Returns:
            Incident ID
        """
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                incident_id = incident_data.get('id')
                now = datetime.utcnow()
                
                # Insert into incidents table
                cur.execute("""
                INSERT INTO incidents (
                    id, title, description, severity, status, alert_category,
                    runbook, created_at, updated_at, raw_alert, entities,
                    metrics, likely_causes, output
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
                """, (
                    incident_id,
                    incident_data.get('title'),
                    incident_data.get('description'),
                    incident_data.get('severity'),
                    incident_data.get('status', 'OPEN'),
                    incident_data.get('alert_category'),
                    incident_data.get('runbook'),
                    incident_data.get('created_at', now),
                    now,
                    Json(incident_data.get('raw_alert', {})),
                    Json(incident_data.get('entities', [])),
                    Json(incident_data.get('metrics', [])),
                    Json(incident_data.get('likely_causes', [])),
                    Json(incident_data.get('output', {}))
                ))
                
                # Add tags if present
                if 'tags' in incident_data:
                    for tag_type, tag_value in incident_data['tags'].items():
                        cur.execute("""
                        INSERT INTO incident_tags (incident_id, tag_type, tag_value)
                        VALUES (%s, %s, %s)
                        ON CONFLICT (incident_id, tag_type, tag_value) DO NOTHING
                        """, (incident_id, tag_type, tag_value))
                
                conn.commit()
                return incident_id
    
    def get_incident(self, incident_id: str) -> Optional[Dict[str, Any]]:
        """
        Get an incident by ID.
        
        Args:
            incident_id: ID of the incident
            
        Returns:
            Incident data or None if not found
        """
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=DictCursor) as cur:
                # Get incident data
                cur.execute("""
                SELECT * FROM incidents WHERE id = %s
                """, (incident_id,))
                
                incident = cur.fetchone()
                if not incident:
                    return None
                
                incident_dict = dict(incident)
                
                # Get investigation steps
                cur.execute("""
                SELECT * FROM investigation_steps 
                WHERE incident_id = %s 
                ORDER BY step_number
                """, (incident_id,))
                incident_dict['investigationSteps'] = [dict(row) for row in cur.fetchall()]
                
                # Get timeline events
                cur.execute("""
                SELECT * FROM timeline_events 
                WHERE incident_id = %s 
                ORDER BY timestamp
                """, (incident_id,))
                incident_dict['timelineEvents'] = [dict(row) for row in cur.fetchall()]
                
                # Get tags
                cur.execute("""
                SELECT tag_type, tag_value FROM incident_tags 
                WHERE incident_id = %s
                """, (incident_id,))
                incident_dict['tags'] = {row['tag_type']: row['tag_value'] for row in cur.fetchall()}
                
                return incident_dict
    
    def update_incident_status(self, incident_id: str, status: str):
        """
        Update an incident's status.
        
        Args:
            incident_id: ID of the incident
            status: New status
        """
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                UPDATE incidents 
                SET status = %s, updated_at = %s 
                WHERE id = %s
                """, (status, datetime.utcnow(), incident_id))
                conn.commit()
    
    def add_investigation_step(self, incident_id: str, step_data: Dict[str, Any]):
        """
        Add an investigation step to an incident.
        
        Args:
            incident_id: ID of the incident
            step_data: Investigation step data
        """
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                INSERT INTO investigation_steps (
                    incident_id, step_number, title, content, timestamp, metadata
                ) VALUES (
                    %s, %s, %s, %s, %s, %s
                ) ON CONFLICT (incident_id, step_number) DO UPDATE 
                SET title = EXCLUDED.title,
                    content = EXCLUDED.content,
                    timestamp = EXCLUDED.timestamp,
                    metadata = EXCLUDED.metadata
                """, (
                    incident_id,
                    step_data.get('step_number'),
                    step_data.get('title'),
                    step_data.get('content'),
                    step_data.get('timestamp', datetime.utcnow()),
                    Json(step_data.get('metadata', {}))
                ))
                conn.commit()
    
    def add_timeline_event(self, incident_id: str, event_data: Dict[str, Any]):
        """
        Add a timeline event to an incident.
        
        Args:
            incident_id: ID of the incident
            event_data: Timeline event data
        """
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                INSERT INTO timeline_events (
                    incident_id, event_type, title, description,
                    timestamp, source, metadata
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s
                )
                """, (
                    incident_id,
                    event_data.get('event_type'),
                    event_data.get('title'),
                    event_data.get('description'),
                    event_data.get('timestamp', datetime.utcnow()),
                    event_data.get('source'),
                    Json(event_data.get('metadata', {}))
                ))
                conn.commit()
    
    def find_similar_incidents(self, title: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Find similar incidents based on title.
        
        Args:
            title: Title to match against
            limit: Maximum number of results
            
        Returns:
            List of similar incidents
        """
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=DictCursor) as cur:
                # Use trigram similarity for fuzzy matching
                cur.execute("""
                SELECT id, title, status, severity, alert_category, created_at,
                       similarity(title, %s) as score
                FROM incidents
                WHERE similarity(title, %s) > 0.3
                ORDER BY score DESC
                LIMIT %s
                """, (title, title, limit))
                
                return [dict(row) for row in cur.fetchall()]
    
    def get_similar_root_causes(self, entity_type: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get similar root causes for the same entity type.
        
        Args:
            entity_type: Entity type to match
            limit: Maximum number of results
            
        Returns:
            List of incidents with related root causes
        """
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=DictCursor) as cur:
                cur.execute("""
                SELECT i.id, i.title, i.alert_category, i.output->>'root_cause' as root_cause,
                       i.created_at, i.status
                FROM incidents i
                WHERE i.entities @> '[{"type": %s}]'
                  AND i.output->>'root_cause' IS NOT NULL
                ORDER BY i.created_at DESC
                LIMIT %s
                """, (entity_type, limit))
                
                return [dict(row) for row in cur.fetchall()] 