"""
Topology Service for fetching and processing topology data.

This service handles all the complex logic for obtaining topology data from different sources
(MongoDB, Cosmos DB for MDM or JSON files for Neurons) and processing it for visualization.
"""
import os
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import requests
import networkx as nx
from fuzzywuzzy import process as fuzz_process
from azure.cosmos import CosmosClient

from ai_incident_manager.services.mongodb_service import get_mongodb_service

import dotenv

# Load environment variables
dotenv.load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)


class TopologyService:
    """
    Service for retrieving and processing topology data from different sources.
    
    This service provides methods to:
    1. Fetch topology data from MongoDB (primary source)
    2. Fetch topology data from Cosmos DB (fallback for MDM)
    3. Fetch topology data from JSON files (fallback for Neurons)
    4. Process and format topology data for visualization
    5. Mark issue entities in the topology
    """
    
    def __init__(self, cache_timeout_seconds: int = 300):
        """
        Initialize the topology service with caching capabilities.
        
        Args:
            cache_timeout_seconds: How long to cache topology data in seconds
        """
        self.cache_timeout = cache_timeout_seconds
        self.topology_cache = {}
        self.cache_timestamps = {}
        
    def get_topology_data(self, entity_guid: str, product: str, cluster_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get topology data for a specific entity and product.
        
        Args:
            entity_guid: The entity GUID to get topology for
            product: The product (MDM or Neurons)
            cluster_id: Optional cluster ID (required for MDM)
            
        Returns:
            Dictionary with nodes and links for visualization
        """
        # Check if we have fresh cached data
        cache_key = f"{product}_{cluster_id or 'default'}"
        current_time = datetime.now()
        
        if (cache_key in self.topology_cache and 
            cache_key in self.cache_timestamps and
            (current_time - self.cache_timestamps[cache_key]).total_seconds() < self.cache_timeout):
            logger.info(f"Using cached topology data for {cache_key}")
            topology_data = self.topology_cache[cache_key]
        else:
            # Fetch fresh topology data from MongoDB first
            logger.info(f"Fetching fresh topology data for {product} {cluster_id or 'unknown'}")
            
            # Try MongoDB first and fall back to original sources
            # Instead of trying to run async code in sync method, we'll go directly to sources
            if product.lower() == "mdm":
                topology_data = self._get_mdm_topology(cluster_id)
            elif product.lower() == "neurons":
                topology_data = self._get_neurons_topology(cluster_id)
            else:
                logger.warning(f"Unknown product: {product}")
                return {"nodes": [], "links": []}
            
            # Cache the result
            self.topology_cache[cache_key] = topology_data
            self.cache_timestamps[cache_key] = current_time
        
        # Mark the issue entity in the topology
        marked_topology = self._mark_issue_entity(topology_data, entity_guid)
        
        # For Neurons, create a subgraph if the topology is too large
        if product.lower() == "neurons" and len(marked_topology.get("nodes", [])) > 100:
            logger.info(f"Creating subgraph for Neurons entity {entity_guid}")
            return self._create_subgraph(marked_topology, entity_guid, num_hops=6)
        
        return marked_topology
    
    async def get_topology_data_async(self, entity_guid: str, product: str, cluster_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Async version of get_topology_data to work with MongoDB properly.
        
        Args:
            entity_guid: The entity GUID to get topology for
            product: The product (MDM or Neurons)  
            cluster_id: Optional cluster ID (required for MDM)
            
        Returns:
            Dictionary with nodes and links for visualization
        """
        # Check if we have fresh cached data
        cache_key = f"{product}_{cluster_id or 'default'}"
        current_time = datetime.now()
        
        if (cache_key in self.topology_cache and 
            cache_key in self.cache_timestamps and
            (current_time - self.cache_timestamps[cache_key]).total_seconds() < self.cache_timeout):
            logger.info(f"Using cached topology data for {cache_key}")
            topology_data = self.topology_cache[cache_key]
        else:
            # Fetch fresh topology data from MongoDB first
            logger.info(f"Fetching fresh topology data for {product} {cluster_id or 'unknown'}")
            
            # Try to get from MongoDB first
            topology_data = await self._get_mongodb_topology(product, cluster_id)
            
            # If not found in MongoDB, fall back to original sources
            if not topology_data or not topology_data.get("nodes"):
                logger.info(f"Topology data not found in MongoDB, falling back to original sources")
                
                if product.lower() == "mdm":
                    topology_data = self._get_mdm_topology(cluster_id)
                elif product.lower() == "neurons":
                    topology_data = self._get_neurons_topology(cluster_id)
                else:
                    logger.warning(f"Unknown product: {product}")
                    return {"nodes": [], "links": []}
                
                # Store the fetched data in MongoDB for future use
                if topology_data and topology_data.get("nodes"):
                    try:
                        await self._store_topology_in_mongodb(
                            product, 
                            cluster_id, 
                            topology_data,
                            source="fallback" # Indicate this came from fallback sources
                        )
                        logger.info(f"Stored topology data in MongoDB for {product} {cluster_id or 'unknown'}")
                    except Exception as e:
                        logger.error(f"Error storing topology in MongoDB: {str(e)}")
            
            # Cache the result
            self.topology_cache[cache_key] = topology_data
            self.cache_timestamps[cache_key] = current_time
        
        # Mark the issue entity in the topology
        marked_topology = self._mark_issue_entity(topology_data, entity_guid)
        
        # For Neurons, create a subgraph if the topology is too large
        if product.lower() == "neurons" and len(marked_topology.get("nodes", [])) > 100:
            logger.info(f"Creating subgraph for Neurons entity {entity_guid}")
            return self._create_subgraph(marked_topology, entity_guid, num_hops=6)
        
        return marked_topology
    
    async def _get_mongodb_topology(self, product: str, cluster_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get topology data from MongoDB.
        
        Args:
            product: The product (MDM or Neurons)
            cluster_id: Optional cluster ID
            
        Returns:
            Dictionary with nodes and links
        """
        try:
            # Get MongoDB service
            mongodb_service = get_mongodb_service()
            
            # Use the dedicated topology method
            topology_data = await mongodb_service.get_topology(product, cluster_id)
            
            if topology_data and topology_data.get("nodes"):
                logger.info(f"Retrieved topology for {product} {cluster_id or 'unknown'} from MongoDB")
                return topology_data
            
            # If no data found through direct method, fall back to tool_results
            if hasattr(mongodb_service, "_tool_results_collection"):
                # Try to find a matching topology in tool_results
                query = {
                    "tool_name": "get_topology",
                    "tool_parameters.product": product.lower()
                }
                
                if cluster_id and cluster_id != "unknown":
                    query["tool_parameters.cluster_id"] = cluster_id
                
                tool_results = await mongodb_service._tool_results_collection.find(query).sort("timestamp", -1).limit(1).to_list(length=1)
                
                if tool_results:
                    topology_result = tool_results[0].get("result", {})
                    logger.info(f"Retrieved topology for {product} {cluster_id or 'unknown'} from tool_results in MongoDB")
                    return {
                        "nodes": topology_result.get("nodes", []),
                        "links": topology_result.get("links", [])
                    }
            
            logger.warning(f"No topology data found in MongoDB for {product} {cluster_id or 'unknown'}")
            return {"nodes": [], "links": []}
            
        except Exception as e:
            logger.error(f"Error fetching topology from MongoDB: {str(e)}")
            return {"nodes": [], "links": []}
    
    def _get_mdm_topology(self, cluster_id: str) -> Dict[str, Any]:
        """
        Get topology data for MDM from Cosmos DB.
        
        Args:
            cluster_id: The cluster ID
            
        Returns:
            Dictionary with nodes and links
        """
        if not cluster_id or cluster_id == "unknown":
            logger.error("Valid cluster ID is required for MDM topology")
            return {"nodes": [], "links": []}
        
        try:
            # Connect to Cosmos DB
            client = CosmosClient(os.environ.get("COSMOS_ENDPOINT"), os.environ.get("COSMOS_KEY"))
            database_name = "service-map"
            container_name = cluster_id
            
            # Get database and container
            database = client.get_database_client(database_name)
            container = database.get_container_client(container_name)
            
            # Get the merged topology data
            merged_topology = container.read_item(item=cluster_id, partition_key=cluster_id)
            logger.info(f"Successfully retrieved MDM topology for cluster {cluster_id}")
            
            return {
                "nodes": merged_topology.get("nodes", []),
                "links": merged_topology.get("links", [])
            }
        except Exception as e:
            logger.error(f"Error fetching MDM topology from Cosmos DB: {str(e)}")
            return {"nodes": [], "links": []}
    
    def _get_neurons_topology(self, cluster_id: str) -> Dict[str, Any]:
        """
        Get topology data for Neurons from JSON files.
        
        Args:
            cluster_id: The cluster ID
            
        Returns:
            Dictionary with nodes and links
        """
        try:
            # Construct the file path
            if not cluster_id or cluster_id == "unknown":
                # Try to find a matching file
                import glob
                topology_files = glob.glob("data/topology/neurons_*.json")
                if not topology_files:
                    logger.error("No Neurons topology files found")
                    return {"nodes": [], "links": []}
                
                # Use the first file found
                input_file = topology_files[0]
                logger.info(f"Using topology file {input_file}")
            else:
                input_file = f"data/topology/neurons_{cluster_id}.json"
            
            # Check if file exists
            if not os.path.exists(input_file):
                logger.error(f"Topology file not found: {input_file}")
                return {"nodes": [], "links": []}
            
            # Load the topology data
            with open(input_file, 'r') as f:
                topology_data = json.load(f)
            
            # The data might be in a nested structure
            if isinstance(topology_data, list) and len(topology_data) > 0 and len(topology_data[0]) > 1:
                topology_data = topology_data[0][1]
            
            logger.info(f"Successfully loaded Neurons topology with {len(topology_data.get('nodes', []))} nodes")
            
            return topology_data
        except Exception as e:
            logger.error(f"Error loading Neurons topology: {str(e)}")
            return {"nodes": [], "links": []}
    
    def _mark_issue_entity(self, topology: Dict[str, Any], entity_guid: str) -> Dict[str, Any]:
        """
        Mark the node corresponding to the issue entity.
        
        Args:
            topology: Dictionary with nodes and links
            entity_guid: The GUID of the entity with the issue
            
        Returns:
            Updated topology with issue node marked
        """
        # Make a deep copy to avoid modifying the cached data
        import copy
        topology = copy.deepcopy(topology)
        
        # Try to find the node by ID
        issue_node = next((node for node in topology.get("nodes", []) if node.get("id") == entity_guid), None)
        
        if issue_node:
            # Mark the node as the issue
            issue_node["issue"] = True
            logger.info(f"Marked node {issue_node.get('name', entity_guid)} as the issue node")
        else:
            # Try fuzzy matching by name if we can get the entity name
            entity_name = self._get_entity_name(entity_guid)
            
            if entity_name:
                # Get all node names
                node_names = [node.get("name", "") for node in topology.get("nodes", [])]
                
                # Find the closest match
                if node_names:
                    closest_match = fuzz_process.extractOne(entity_name, node_names, score_cutoff=80)
                    
                    if closest_match:
                        matched_name = closest_match[0]
                        # Find the node with this name
                        matched_node = next((node for node in topology.get("nodes", []) if node.get("name") == matched_name), None)
                        
                        if matched_node:
                            matched_node["issue"] = True
                            logger.info(f"Marked node {matched_name} as the issue node (fuzzy match)")
                        else:
                            logger.warning(f"Could not find node with name {matched_name} in topology")
                    else:
                        logger.warning(f"No match found for entity name {entity_name} in topology")
                else:
                    logger.warning("No node names available for fuzzy matching")
            else:
                logger.warning(f"Could not find entity {entity_guid} in topology and no name available for fuzzy matching")
        
        return topology
    
    def _get_entity_name(self, entity_guid: str) -> Optional[str]:
        """
        Get the name of an entity from New Relic.
        
        Args:
            entity_guid: The GUID of the entity
            
        Returns:
            Entity name or None if not found
        """
        try:
            # Use New Relic GraphQL API to get entity name
            url = "https://api.newrelic.com/graphql"
            api_key = os.environ.get("NEWRELIC_API_KEY")
            
            headers = {
                "Content-Type": "application/json",
                "API-Key": api_key
            }
            
            query = {
                "query": """
                {
                  actor {
                    entity(guid: "%s") {
                      name
                    }
                  }
                }
                """ % entity_guid
            }
            
            response = requests.post(url, headers=headers, json=query)
            
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "actor" in data["data"] and data["data"]["actor"]["entity"]:
                    return data["data"]["actor"]["entity"]["name"]
            
            logger.warning(f"Could not get name for entity {entity_guid}")
            return None
        except Exception as e:
            logger.error(f"Error getting entity name: {str(e)}")
            return None
    
    def _create_subgraph(self, topology: Dict[str, Any], center_entity_guid: str, num_hops: int) -> Dict[str, Any]:
        """
        Create a subgraph centered around a specific entity.
        
        Args:
            topology: The full topology
            center_entity_guid: The GUID of the entity to center on
            num_hops: Number of hops from the center entity to include
            
        Returns:
            Subgraph as a dictionary with nodes and links
        """
        try:
            # Create a NetworkX graph from the topology
            G = nx.Graph()
            
            # Add all nodes
            for node in topology.get("nodes", []):
                G.add_node(node.get("id"), **node)
            
            # Add all edges
            for link in topology.get("links", []):
                G.add_edge(link.get("source"), link.get("target"), **link)
            
            # Get the subgraph
            if center_entity_guid in G:
                # Get nodes within num_hops of the center entity
                subgraph_nodes = {center_entity_guid}
                current_nodes = {center_entity_guid}
                
                for _ in range(num_hops):
                    next_nodes = set()
                    for node in current_nodes:
                        neighbors = list(G.neighbors(node))
                        next_nodes.update(neighbors)
                    
                    subgraph_nodes.update(next_nodes)
                    current_nodes = next_nodes
                
                # Create subgraph
                subgraph = G.subgraph(subgraph_nodes)
                
                # Convert back to dictionary format
                nodes = []
                for node_id in subgraph.nodes:
                    nodes.append(subgraph.nodes[node_id])
                
                links = []
                for edge in subgraph.edges:
                    source, target = edge
                    edge_data = subgraph.get_edge_data(source, target)
                    # Ensure the link has the correct format
                    link_data = {
                        "id": f"{source}->{target}",
                        "source": source,
                        "target": target,
                        "link_type": edge_data.get("link_type", "connected_to"),
                        "properties": edge_data.get("properties", {}),
                        "data_source": edge_data.get("data_source", "live"),
                        "value": edge_data.get("value", 1)
                    }
                    links.append(link_data)
                
                logger.info(f"Created subgraph with {len(nodes)} nodes and {len(links)} links")
                return {"nodes": nodes, "links": links}
            else:
                logger.warning(f"Center entity {center_entity_guid} not found in graph")
                return topology
        except Exception as e:
            logger.error(f"Error creating subgraph: {str(e)}")
            return topology
    
    def determine_cluster_id(self, issue_details: Dict[str, Any], product: str) -> str:
        """
        Determine the cluster ID from issue details.
        
        Args:
            issue_details: Issue details from New Relic
            product: The product (MDM or Neurons)
            
        Returns:
            Cluster ID or 'unknown'
        """
        try:
            # Extract cluster information from issue details
            if "data" not in issue_details or "actor" not in issue_details["data"]:
                logger.warning("Invalid issue details format")
                return "unknown"
            
            results = issue_details["data"]["actor"]["account"]["nrql"]["results"]
            if not results:
                logger.warning("No results in issue details")
                return "unknown"
            
            # Try different fields depending on the product
            if product.lower() == "mdm":
                # For MDM, try to find cluster in tags
                for result in results:
                    # Look for cluster name
                    cluster_name = result.get("cluster.name") or result.get("clusterName")
                    if cluster_name:
                        parts = cluster_name.split("-")
                        if len(parts) >= 3:
                            # Return something like na1, na2, ap1, etc.
                            return parts[1].lower()
                    
                    # Look in tags if no direct field
                    if "tags" in result:
                        tags = result["tags"]
                        if isinstance(tags, dict):
                            for tag_key, tag_value in tags.items():
                                if "cluster" in tag_key.lower():
                                    parts = str(tag_value).split("-")
                                    if len(parts) >= 3:
                                        return parts[1].lower()
            
            elif product.lower() == "neurons":
                # For Neurons, extract from cluster name
                for result in results:
                    cluster_name = result.get("k8s.clusterName") or result.get("clusterName")
                    if cluster_name:
                        if "uku" in cluster_name.lower():
                            return "uku"
                        elif "nvu" in cluster_name.lower():
                            return "nvu"
                        elif "mlu" in cluster_name.lower():
                            return "mlu"
                        elif "ttu" in cluster_name.lower():
                            return "ttu"
                        elif "tku" in cluster_name.lower():
                            return "tku"
            
            logger.warning(f"Could not determine cluster ID for {product}")
            return "unknown"
            
        except Exception as e:
            logger.error(f"Error determining cluster ID: {str(e)}")
            return "unknown"
    
    def get_entity_tags(self, entity_guid: str) -> Dict[str, Any]:
        """
        Get tags for an entity from New Relic.
        
        Args:
            entity_guid: The GUID of the entity
            
        Returns:
            Dictionary of entity tags
        """
        try:
            # Use New Relic GraphQL API to get entity tags
            url = "https://api.newrelic.com/graphql"
            api_key = os.environ.get("NEWRELIC_API_KEY")
            
            headers = {
                "Content-Type": "application/json",
                "API-Key": api_key
            }
            
            query = {
                "query": """
                {
                  actor {
                    entity(guid: "%s") {
                      tags {
                        key
                        values
                      }
                    }
                  }
                }
                """ % entity_guid
            }
            
            response = requests.post(url, headers=headers, json=query)
            
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "actor" in data["data"] and data["data"]["actor"]["entity"]:
                    tags = data["data"]["actor"]["entity"]["tags"]
                    
                    # Convert to dictionary format
                    tags_dict = {}
                    for tag in tags:
                        key = tag["key"]
                        values = tag["values"]
                        
                        # If value is list, convert to string
                        if isinstance(values, list):
                            tags_dict[key] = ", ".join(values)
                        else:
                            tags_dict[key] = values
                    
                    # Add service and product detail
                    if "k8s.deploymentName" in tags_dict:
                        tags_dict["Service"] = tags_dict["k8s.deploymentName"].split("-")[0]
                    elif "product" in tags_dict:
                        tags_dict["Service"] = tags_dict["product"]
                    
                    return tags_dict
            
            logger.warning(f"Could not get tags for entity {entity_guid}")
            return {}
        except Exception as e:
            logger.error(f"Error getting entity tags: {str(e)}")
            return {}

    async def _store_topology_in_mongodb(self, product: str, cluster_id: Optional[str], topology_data: Dict[str, Any], source: str = "direct") -> str:
        """
        Store topology data in MongoDB.
        
        Args:
            product: The product (MDM or Neurons)
            cluster_id: Optional cluster ID
            topology_data: Topology data to store
            source: Source of the data (direct, fallback, etc.)
            
        Returns:
            ID of the stored document
        """
        try:
            # Get MongoDB service
            mongodb_service = get_mongodb_service()
            
            # Use the dedicated method to store topology
            result = await mongodb_service.store_topology(product, topology_data, cluster_id, source)
            return result
                
        except Exception as e:
            logger.error(f"Error storing topology in MongoDB: {str(e)}")
            return None


# Singleton instance
_topology_service_instance = None

def get_topology_service() -> TopologyService:
    """Get the singleton instance of the topology service."""
    global _topology_service_instance
    if _topology_service_instance is None:
        _topology_service_instance = TopologyService()
    return _topology_service_instance 