"""
Metrics collection service using New Relic analyzer.

This service interfaces with the New Relic analyzer to collect real metrics and logs
for entities involved in incidents.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta, timezone

import dotenv
from lib.new_relic.analyzer import <PERSON><PERSON><PERSON><PERSON>nal<PERSON><PERSON>, EntityDetails
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.logs import NewRelicLogsClient
from lib.new_relic.base import UTC
from lib.new_relic.nrql_manager import NRQLManager
from lib.new_relic.entities import PodEntity, NodeEntity, HostEntity, ApplicationEntity
from lib.new_relic.entities.cluster_entity import ClusterEntity

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MetricsCollector:
    """Service for collecting metrics and logs from New Relic."""
    
    def __init__(self):
        """Initialize the metrics collector with New Relic clients."""
        api_key = os.environ.get("NEWRELIC_API_KEY")
        account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
        
        if not api_key or not account_id:
            raise ValueError("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set in environment variables")
        
        # Initialize New Relic clients
        self.graphql_client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
        self.query_client = NewRelicQueryClient(self.graphql_client)
        self.logs_client = NewRelicLogsClient(self.graphql_client)
        
        # Initialize the analyzer
        self.analyzer = EntityAnalyzer(
            self.graphql_client,
            debug=True
        )
        
        # Initialize NRQL Manager
        self.nrql_manager = NRQLManager()
        
        # Initialize entity-specific clients
        self.pod_entity = PodEntity(self.query_client, self.nrql_manager)
        self.node_entity = NodeEntity(self.query_client, self.nrql_manager)
        self.host_entity = HostEntity(self.query_client, self.nrql_manager)
        self.app_entity = ApplicationEntity(self.query_client, self.nrql_manager)
        self.cluster_entity = ClusterEntity(self.query_client, self.nrql_manager)
        
        # Map entity types to their handlers
        self.entity_handlers = {
            "KUBERNETES_POD": self.pod_entity,
            "KUBERNETES_CONTAINER": self.pod_entity,  # Container metrics often come from pod entity
            "KUBERNETES_NODE": self.node_entity,
            "KUBERNETES_CLUSTER": self.cluster_entity,
            "HOST": self.host_entity,
            "APPLICATION": self.app_entity
        }
    
    def collect_entity_metrics(
        self,
        entity_guid: str,
        entity_type: str,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None,
        metrics_list: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Collect metrics for a specific entity.
        
        Args:
            entity_guid: The New Relic entity GUID
            entity_type: Entity type (KUBERNETES_POD, etc.)
            since_time: Start time for metrics collection (defaults to 30 minutes ago)
            until_time: End time for metrics collection (defaults to now)
            metrics_list: List of specific metrics to collect (optional)
            
        Returns:
            Dictionary of metrics data
        """
        logger.info(f"Collecting metrics for entity {entity_guid} of type {entity_type}")
        
        # Set default times if not provided
        if not since_time:
            since_time = datetime.now(UTC) - timedelta(minutes=30)
        if not until_time:
            until_time = datetime.now(UTC)
        
        try:
            # Use the entity analyzer to get metrics
            metrics = self.analyzer._get_entity_metrics(
                entity_guid=entity_guid,
                entity_type=entity_type,
                since_time=since_time,
                until_time=until_time
            )
            
            # Filter metrics if a list is provided
            if metrics_list and metrics:
                filtered_metrics = {}
                for metric_name in metrics_list:
                    if metric_name in metrics:
                        filtered_metrics[metric_name] = metrics[metric_name]
                return filtered_metrics
            
            return metrics or {}
            
        except Exception as e:
            logger.error(f"Error collecting metrics for entity {entity_guid}: {str(e)}")
            return {}
    
    def collect_entity_logs(
        self,
        entity_guid: str,
        entity_name: str,
        entity_type: str,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Collect logs for a specific entity.
        
        Args:
            entity_guid: The New Relic entity GUID
            entity_name: Entity name for query filtering
            entity_type: Entity type for query customization
            since_time: Start time for log collection (defaults to 30 minutes ago)
            until_time: End time for log collection (defaults to now)
            limit: Maximum number of log entries to retrieve
            
        Returns:
            List of log entries
        """
        logger.info(f"Collecting logs for entity {entity_guid} ({entity_name})")
        
        # Set default times if not provided
        if not since_time:
            since_time = datetime.now(UTC) - timedelta(minutes=30)
        if not until_time:
            until_time = datetime.now(UTC)
        
        try:
            # Determine appropriate log partition based on entity type
            partition = "default"
            
            # Use different query approaches based on entity type
            if self.analyzer._is_kubernetes_entity(entity_type):
                # For Kubernetes entities, use more specific query
                if self.analyzer._is_pod_entity(entity_type):
                    # Query by pod name
                    query = f"podName:'{entity_name}'"
                elif self.analyzer._is_node_entity(entity_type):
                    # Query by node name
                    query = f"nodeName:'{entity_name}'"
                else:
                    # Generic entity query
                    query = f"entity.guid:'{entity_guid}'"
                    
                logs = self.logs_client.query_logs(
                    query=query,
                    since=since_time,
                    until=until_time,
                    limit=limit
                )
            else:
                # For other entities, use the generic entity logs method
                logs = self.logs_client.get_entity_logs(
                    entity_guid=entity_guid,
                    partitions=partition,
                    since=since_time,
                    until=until_time,
                    limit=limit
                )
            
            return logs
            
        except Exception as e:
            logger.error(f"Error collecting logs for entity {entity_guid}: {str(e)}")
            return []
    
    def collect_kubernetes_events(
        self,
        entity_name: str,
        entity_type: str,
        cluster_name: Optional[str] = None,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        Collect Kubernetes events for a specific entity.
        
        Args:
            entity_name: Name of the entity (pod name, node name, etc.)
            entity_type: Type of entity (KUBERNETES_POD, KUBERNETES_NODE, etc.)
            cluster_name: Kubernetes cluster name (optional)
            since_time: Start time for event collection (defaults to 30 minutes ago)
            until_time: End time for event collection (defaults to now)
            
        Returns:
            List of Kubernetes events
        """
        logger.info(f"Collecting Kubernetes events for {entity_type} {entity_name}")
        
        # Set default times if not provided
        if not since_time:
            since_time = datetime.now(UTC) - timedelta(minutes=30)
        if not until_time:
            until_time = datetime.now(UTC)
        
        try:
            # Map entity type to Kubernetes object kind
            if self.analyzer._is_pod_entity(entity_type):
                object_kind = "Pod"
            elif self.analyzer._is_node_entity(entity_type):
                object_kind = "Node"
            else:
                # Default to generic events query
                return self._collect_generic_k8s_events(entity_name, since_time, until_time)
            
            # Use the analyzer to get events
            events = self.analyzer._get_kubernetes_events(
                entity_name=entity_name,
                entity_type=entity_type,
                cluster_name=cluster_name or "unknown",
                since_time=since_time,
                until_time=until_time
            )
            
            return events
            
        except Exception as e:
            logger.error(f"Error collecting Kubernetes events for {entity_type} {entity_name}: {str(e)}")
            return []
    
    def _collect_generic_k8s_events(
        self,
        entity_name: str,
        since_time: datetime,
        until_time: datetime
    ) -> List[Dict[str, Any]]:
        """
        Collect generic Kubernetes events when the entity type is unclear.
        
        Args:
            entity_name: Name of the entity
            since_time: Start time for event collection
            until_time: End time for event collection
            
        Returns:
            List of Kubernetes events
        """
        try:
            # Query NRQL for events
            query = f"""
            SELECT *
            FROM K8sEventSample
            WHERE involvedObjectName = '{entity_name}'
            SINCE '{since_time.isoformat()}'
            UNTIL '{until_time.isoformat()}'
            LIMIT 100
            """
            
            results = self.query_client.query_nrql(query)
            
            # Format results similarly to _get_kubernetes_events
            events = []
            for result in results:
                events.append({
                    "timestamp": result.get("timestamp"),
                    "type": result.get("type"),
                    "reason": result.get("reason"),
                    "message": result.get("message"),
                    "involvedObject": {
                        "kind": result.get("involvedObjectKind"),
                        "name": result.get("involvedObjectName")
                    },
                    "source": {
                        "component": result.get("sourceComponent")
                    }
                })
            
            return events
            
        except Exception as e:
            logger.error(f"Error collecting generic Kubernetes events: {str(e)}")
            return []
    
    async def get_entity_details(self, entity_guid: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about an entity.
        
        Args:
            entity_guid: New Relic entity GUID
            
        Returns:
            Entity details dictionary or None if not found
        """
        try:
            # First try to use direct query_client to get entity details
            entity_details = self.query_client.get_entity_details(entity_guid)
            if entity_details:
                return entity_details
                
            # If that fails, try the analyzer as fallback
            logger.info(f"Using analyzer fallback for entity {entity_guid}")
            try:
                entity_details = self.analyzer._get_entity_details(entity_guid)
                return entity_details
            except Exception as e:
                logger.warning(f"Error using analyzer for entity details: {str(e)}")
            
            # Neither method worked
            logger.error(f"Could not retrieve entity details for {entity_guid}")
            return None
            
        except Exception as e:
            logger.error(f"Error getting entity details for {entity_guid}: {str(e)}")
            return None
    
    def analyze_entity(
        self,
        entity_guid: str,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None,
        product: Optional[str] = None,
        region: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze an entity to get comprehensive metrics and logs.
        
        Args:
            entity_guid: The New Relic entity GUID
            since_time: Start time for analysis (defaults to 30 minutes ago)
            until_time: End time for analysis (defaults to now)
            product: Optional product name for context
            region: Optional region name for context
            
        Returns:
            Dictionary with analysis results
        """
        # Get entity details
        entity_details = self.get_entity_details(entity_guid)
        if not entity_details:
            return {"error": f"Entity {entity_guid} not found"}
        
        # Set default times if not provided
        if not since_time:
            since_time = datetime.now(UTC) - timedelta(minutes=30)
        if not until_time:
            until_time = datetime.now(UTC)
            
        # Call the analyzer
        try:
            result = self.analyzer.analyze_entity(
                entity_details,
                since_time=since_time,
                until_time=until_time
            )
            return result
        except Exception as e:
            logger.error(f"Error analyzing entity {entity_guid}: {str(e)}")
            return {"error": f"Analysis failed: {str(e)}"}

    # --- Entity-Specific Methods ---
    
    async def get_entity_logs(
        self,
        entity_guid: str,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None,
        limit: int = 100
    ) -> Dict[str, Any]:
        """
        Get logs for an entity using entity GUID.
        
        Args:
            entity_guid: New Relic entity GUID
            since_time: Start time for log collection (defaults to 30 minutes ago)
            until_time: End time for log collection (defaults to now)
            limit: Maximum number of log entries to retrieve
            
        Returns:
            Dictionary containing logs and entity information
        """
        try:
            # Get entity details first
            entity_details = await self.get_entity_details(entity_guid)
            
            if not entity_details:
                return {
                    "error": f"Entity {entity_guid} not found",
                    "logs": []
                }
            
            # Extract entity details for log collection
            entity_name = entity_details.get("name", "unknown")
            entity_type = entity_details.get("type", "unknown")
            
            # Get logs for the entity
            logs = self.collect_entity_logs(
                entity_guid,
                entity_name,
                entity_type,
                since_time,
                until_time,
                limit
            )
            
            # Format result
            return {
                "entity_guid": entity_guid,
                "entity_name": entity_name,
                "entity_type": entity_type,
                "log_count": len(logs),
                "logs": logs,
                "time_window": {
                    "since": since_time.isoformat() if since_time else None,
                    "until": until_time.isoformat() if until_time else None
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting logs for entity {entity_guid}: {str(e)}")
            return {
                "error": f"Error getting logs: {str(e)}",
                "logs": []
            }
    
    async def get_pod_metrics(
        self,
        entity_guid: str,
        pod_name: str,
        cluster_name: str,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None,
        metrics: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get comprehensive metrics for a Kubernetes pod.
        
        Args:
            entity_guid: New Relic entity GUID for the pod
            pod_name: Name of the pod
            cluster_name: Name of the cluster
            since_time: Start time for metrics collection
            until_time: End time for metrics collection
            metrics: List of specific metrics to collect
            
        Returns:
            Dictionary of pod metrics
        """
        try:
            # Set default times if not provided
            if not since_time:
                since_time = datetime.now(UTC) - timedelta(minutes=30)
            if not until_time:
                until_time = datetime.now(UTC)
            
            # Convert to milliseconds for queries
            since_time_ms = int(since_time.timestamp() * 1000)
            until_time_ms = int(until_time.timestamp() * 1000)
            
            # Define what metrics to collect if not specified
            if not metrics or len(metrics) == 0:
                metrics = ["cpu_usage", "memory_usage", "restart_count", "container_status"]
            
            # Collect metrics using PodEntity
            metrics_data = {}
            
            for metric in metrics:
                try:
                    if metric == "cpu_usage":
                        metric_data = self.pod_entity.get_cpu_usage(
                            pod_name=pod_name,
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["cpu_usage"] = metric_data
                    
                    elif metric == "memory_usage":
                        metric_data = self.pod_entity.get_memory_usage(
                            pod_name=pod_name,
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["memory_usage"] = metric_data
                    
                    elif metric == "restart_count":
                        metric_data = self.pod_entity.get_restart_count(
                            pod_name=pod_name,
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["restart_count"] = metric_data
                    
                    elif metric == "container_status":
                        metric_data = self.pod_entity.get_container_status(
                            pod_name=pod_name,
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["container_status"] = metric_data
                    
                except Exception as e:
                    logger.error(f"Error collecting {metric} for pod {pod_name}: {str(e)}")
                    metrics_data[f"{metric}_error"] = str(e)
            
            return {
                "entity_guid": entity_guid,
                "pod_name": pod_name,
                "cluster_name": cluster_name,
                "metrics": metrics_data,
                "time_window": {
                    "since": since_time.isoformat(),
                    "until": until_time.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting pod metrics: {str(e)}")
            return {"error": f"Error getting pod metrics: {str(e)}"}
    
    async def get_node_metrics(
        self,
        entity_guid: str,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None,
        metrics: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get comprehensive metrics for a Kubernetes node.
        
        Args:
            entity_guid: New Relic entity GUID for the node
            since_time: Start time for metrics collection
            until_time: End time for metrics collection
            metrics: List of specific metrics to collect
            
        Returns:
            Dictionary of node metrics
        """
        try:
            # Get entity details
            entity_details = await self.get_entity_details(entity_guid)
            
            if not entity_details:
                return {"error": f"Node entity {entity_guid} not found"}
            
            # Verify it's a node
            entity_type = entity_details.get("type", "")
            if not entity_type.startswith("KUBERNETES_NODE"):
                return {"error": f"Entity {entity_guid} is not a Kubernetes node"}
            
            # Extract node name and cluster from entity details
            node_name = entity_details.get("name", "unknown")
            cluster_name = None
            
            # Extract cluster from tags
            tags = entity_details.get("tags", [])
            for tag in tags:
                if tag.get("key") == "clusterName":
                    values = tag.get("values", [])
                    if values:
                        cluster_name = values[0]
                        break
            
            if not cluster_name:
                logger.warning(f"Cluster name not found for node {node_name}")
                cluster_name = "unknown"
            
            # Set default times if not provided
            if not since_time:
                since_time = datetime.now(UTC) - timedelta(minutes=30)
            if not until_time:
                until_time = datetime.now(UTC)
            
            # Convert to milliseconds for queries
            since_time_ms = int(since_time.timestamp() * 1000)
            until_time_ms = int(until_time.timestamp() * 1000)
            
            # Define what metrics to collect if not specified
            if not metrics or len(metrics) == 0:
                metrics = ["cpu_usage", "memory_usage", "pod_count", "condition"]
            
            # Collect metrics using NodeEntity
            metrics_data = {}
            
            for metric in metrics:
                try:
                    if metric == "cpu_usage":
                        metric_data = self.node_entity.get_cpu_usage(
                            node_name=node_name,
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["cpu_usage"] = metric_data
                    
                    elif metric == "memory_usage":
                        metric_data = self.node_entity.get_memory_usage(
                            node_name=node_name,
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["memory_usage"] = metric_data
                    
                    elif metric == "pod_count":
                        metric_data = self.node_entity.get_pod_count(
                            node_name=node_name,
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["pod_count"] = metric_data
                    
                    elif metric == "condition":
                        metric_data = self.node_entity.get_condition(
                            node_name=node_name,
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["condition"] = metric_data
                    
                except Exception as e:
                    logger.error(f"Error collecting {metric} for node {node_name}: {str(e)}")
                    metrics_data[f"{metric}_error"] = str(e)
            
            return {
                "entity_guid": entity_guid,
                "node_name": node_name,
                "cluster_name": cluster_name,
                "metrics": metrics_data,
                "time_window": {
                    "since": since_time.isoformat(),
                    "until": until_time.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting node metrics: {str(e)}")
            return {"error": f"Error getting node metrics: {str(e)}"}
    
    async def get_application_metrics(
        self,
        entity_guid: str,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None,
        metrics: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get comprehensive metrics for an application.
        
        Args:
            entity_guid: New Relic entity GUID for the application
            since_time: Start time for metrics collection
            until_time: End time for metrics collection
            metrics: List of specific metrics to collect
            
        Returns:
            Dictionary of application metrics
        """
        try:
            # Get entity details
            entity_details = await self.get_entity_details(entity_guid)
            
            if not entity_details:
                return {"error": f"Application entity {entity_guid} not found"}
            
            # Verify it's an application
            entity_type = entity_details.get("type", "")
            if not entity_type.startswith("APPLICATION"):
                return {"error": f"Entity {entity_guid} is not an application"}
            
            # Extract application name from entity details
            app_name = entity_details.get("name", "unknown")
            
            # Set default times if not provided
            if not since_time:
                since_time = datetime.now(UTC) - timedelta(minutes=30)
            if not until_time:
                until_time = datetime.now(UTC)
            
            # Calculate time window for queries
            since_minutes = int((until_time - since_time).total_seconds() / 60)
            period_minutes = max(1, min(5, since_minutes / 60))  # Adjust period for optimal data points
            
            # Define what metrics to collect if not specified
            if not metrics or len(metrics) == 0:
                metrics = ["error_rate", "response_time", "throughput"]
            
            # Collect metrics using ApplicationEntity
            metrics_data = {}
            
            for metric in metrics:
                try:
                    if metric == "error_rate":
                        metric_data = self.app_entity.get_error_rate(
                            app_name=app_name,
                            since_minutes=since_minutes,
                            period_minutes=period_minutes
                        )
                        metrics_data["error_rate"] = metric_data
                    
                    elif metric == "response_time":
                        metric_data = self.app_entity.get_response_time(
                            app_name=app_name,
                            since_minutes=since_minutes,
                            period_minutes=period_minutes
                        )
                        metrics_data["response_time"] = metric_data
                    
                    elif metric == "throughput":
                        metric_data = self.app_entity.get_throughput(
                            app_name=app_name,
                            since_minutes=since_minutes,
                            period_minutes=period_minutes
                        )
                        metrics_data["throughput"] = metric_data
                    
                except Exception as e:
                    logger.error(f"Error collecting {metric} for application {app_name}: {str(e)}")
                    metrics_data[f"{metric}_error"] = str(e)
            
            return {
                "entity_guid": entity_guid,
                "application_name": app_name,
                "metrics": metrics_data,
                "time_window": {
                    "since": since_time.isoformat(),
                    "until": until_time.isoformat(),
                    "since_minutes": since_minutes,
                    "period_minutes": period_minutes
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting application metrics: {str(e)}")
            return {"error": f"Error getting application metrics: {str(e)}"}
    
    async def get_host_metrics(
        self,
        entity_guid: str,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None,
        metrics: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get comprehensive metrics for a host.
        
        Args:
            entity_guid: New Relic entity GUID for the host
            since_time: Start time for metrics collection
            until_time: End time for metrics collection
            metrics: List of specific metrics to collect
            
        Returns:
            Dictionary of host metrics
        """
        try:
            # Get entity details
            entity_details = await self.get_entity_details(entity_guid)
            
            if not entity_details:
                return {"error": f"Host entity {entity_guid} not found"}
            
            # Verify it's a host
            entity_type = entity_details.get("type", "")
            if not entity_type.startswith("HOST"):
                return {"error": f"Entity {entity_guid} is not a host"}
            
            # Extract hostname from entity details
            hostname = entity_details.get("name", "unknown")
            
            # Set default times if not provided
            if not since_time:
                since_time = datetime.now(UTC) - timedelta(minutes=30)
            if not until_time:
                until_time = datetime.now(UTC)
            
            # Convert to milliseconds for queries
            since_time_ms = int(since_time.timestamp() * 1000)
            until_time_ms = int(until_time.timestamp() * 1000)
            
            # Define what metrics to collect if not specified
            if not metrics or len(metrics) == 0:
                metrics = ["cpu_usage", "memory_usage", "disk_usage", "network_io"]
            
            # Collect metrics using HostEntity
            metrics_data = {}
            
            for metric in metrics:
                try:
                    if metric == "cpu_usage":
                        metric_data = self.host_entity.get_cpu_usage(
                            hostname=hostname,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["cpu_usage"] = metric_data
                    
                    elif metric == "memory_usage":
                        metric_data = self.host_entity.get_memory_usage(
                            hostname=hostname,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["memory_usage"] = metric_data
                    
                    elif metric == "disk_usage":
                        metric_data = self.host_entity.get_disk_usage(
                            hostname=hostname,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["disk_usage"] = metric_data
                    
                    elif metric == "network_io":
                        metric_data = self.host_entity.get_network_io(
                            hostname=hostname,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["network_io"] = metric_data
                    
                except Exception as e:
                    logger.error(f"Error collecting {metric} for host {hostname}: {str(e)}")
                    metrics_data[f"{metric}_error"] = str(e)
            
            return {
                "entity_guid": entity_guid,
                "hostname": hostname,
                "metrics": metrics_data,
                "time_window": {
                    "since": since_time.isoformat(),
                    "until": until_time.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting host metrics: {str(e)}")
            return {"error": f"Error getting host metrics: {str(e)}"}
    
    async def get_pod_network_metrics(
        self,
        entity_guid: str,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get network metrics for a Kubernetes pod.
        
        Args:
            entity_guid: New Relic entity GUID for the pod
            since_time: Start time for metrics collection
            until_time: End time for metrics collection
            
        Returns:
            Dictionary of pod network metrics
        """
        try:
            # Get entity details
            entity_details = await self.get_entity_details(entity_guid)
            
            if not entity_details:
                return {"error": f"Pod entity {entity_guid} not found"}
            
            # Verify it's a pod
            entity_type = entity_details.get("type", "")
            if not entity_type.startswith("KUBERNETES_POD"):
                return {"error": f"Entity {entity_guid} is not a Kubernetes pod"}
            
            # Extract pod name and cluster from entity details
            pod_name = entity_details.get("name", "unknown")
            cluster_name = None
            
            # Extract cluster from tags
            tags = entity_details.get("tags", [])
            for tag in tags:
                if tag.get("key") == "clusterName":
                    values = tag.get("values", [])
                    if values:
                        cluster_name = values[0]
                        break
            
            if not cluster_name:
                logger.warning(f"Cluster name not found for pod {pod_name}")
                cluster_name = "unknown"
            
            # Set default times if not provided
            if not since_time:
                since_time = datetime.now(UTC) - timedelta(minutes=30)
            if not until_time:
                until_time = datetime.now(UTC)
            
            # Convert to milliseconds for queries
            since_time_ms = int(since_time.timestamp() * 1000)
            until_time_ms = int(until_time.timestamp() * 1000)
            
            # Collect network metrics using PodEntity
            metrics_data = {}
            
            try:
                network_io_data = self.pod_entity.get_network_io(
                    pod_name=pod_name,
                    cluster_name=cluster_name,
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms
                )
                metrics_data["network_io"] = network_io_data
            except Exception as e:
                logger.error(f"Error collecting network metrics for pod {pod_name}: {str(e)}")
                metrics_data["network_io_error"] = str(e)
            
            return {
                "entity_guid": entity_guid,
                "pod_name": pod_name,
                "cluster_name": cluster_name,
                "metrics": metrics_data,
                "time_window": {
                    "since": since_time.isoformat(),
                    "until": until_time.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting pod network metrics: {str(e)}")
            return {"error": f"Error getting pod network metrics: {str(e)}"}
    
    async def get_pod_volume_metrics(
        self,
        entity_guid: str,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get volume metrics for a Kubernetes pod.
        
        Args:
            entity_guid: New Relic entity GUID for the pod
            since_time: Start time for metrics collection
            until_time: End time for metrics collection
            
        Returns:
            Dictionary of pod volume metrics
        """
        try:
            # Get entity details
            entity_details = await self.get_entity_details(entity_guid)
            
            if not entity_details:
                return {"error": f"Pod entity {entity_guid} not found"}
            
            # Verify it's a pod
            entity_type = entity_details.get("type", "")
            if not entity_type.startswith("KUBERNETES_POD"):
                return {"error": f"Entity {entity_guid} is not a Kubernetes pod"}
            
            # Extract pod name and cluster from entity details
            pod_name = entity_details.get("name", "unknown")
            cluster_name = None
            
            # Extract cluster from tags
            tags = entity_details.get("tags", [])
            for tag in tags:
                if tag.get("key") == "clusterName":
                    values = tag.get("values", [])
                    if values:
                        cluster_name = values[0]
                        break
            
            if not cluster_name:
                logger.warning(f"Cluster name not found for pod {pod_name}")
                cluster_name = "unknown"
            
            # Set default times if not provided
            if not since_time:
                since_time = datetime.now(UTC) - timedelta(minutes=30)
            if not until_time:
                until_time = datetime.now(UTC)
            
            # Convert to milliseconds for queries
            since_time_ms = int(since_time.timestamp() * 1000)
            until_time_ms = int(until_time.timestamp() * 1000)
            
            # Collect volume metrics
            query = f"""
            SELECT latest(fsUsedPercent) as 'volumeUsedPercent',
                   latest(fsUsedBytes) as 'volumeUsedBytes',
                   latest(fsCapacityBytes) as 'volumeCapacityBytes'
            FROM K8sVolumeSample
            WHERE podName = '{pod_name}' AND clusterName = '{cluster_name}'
            FACET volumeName, pvcName
            SINCE {since_time_ms} UNTIL {until_time_ms}
            """
            
            volume_data = self.query_client.execute_nrql_query(query)
            
            return {
                "entity_guid": entity_guid,
                "pod_name": pod_name,
                "cluster_name": cluster_name,
                "volumes": volume_data.get("facets", []),
                "time_window": {
                    "since": since_time.isoformat(),
                    "until": until_time.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting pod volume metrics: {str(e)}")
            return {"error": f"Error getting pod volume metrics: {str(e)}"}
            
    async def get_cluster_metrics(
        self,
        cluster_name: str,
        metrics: Optional[List[str]] = None,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get comprehensive metrics for a Kubernetes cluster.
        
        Args:
            cluster_name: Name of the Kubernetes cluster
            metrics: List of specific metrics to collect
            since_time: Start time for metrics collection
            until_time: End time for metrics collection
            
        Returns:
            Dictionary of cluster metrics
        """
        try:
            # Set default times if not provided
            if not since_time:
                since_time = datetime.now(UTC) - timedelta(minutes=30)
            if not until_time:
                until_time = datetime.now(UTC)
            
            # Convert to milliseconds for queries
            since_time_ms = int(since_time.timestamp() * 1000)
            until_time_ms = int(until_time.timestamp() * 1000)
            
            # Define what metrics to collect if not specified
            if not metrics or len(metrics) == 0:
                metrics = ["resource_usage", "pod_counts", "nodes", "deployments"]
            
            # Collect metrics using ClusterEntity
            metrics_data = {}
            
            for metric in metrics:
                try:
                    if metric == "resource_usage":
                        resource_usage = self.cluster_entity.get_resource_usage(
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["resource_usage"] = resource_usage
                    
                    elif metric == "pod_counts":
                        pod_counts = self.cluster_entity.get_pod_counts(
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["pod_counts"] = pod_counts
                    
                    elif metric == "nodes":
                        nodes_info = self.cluster_entity.get_cluster_nodes(
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["nodes"] = nodes_info
                    
                    elif metric == "deployments":
                        deployment_info = self.cluster_entity.get_deployment_status(
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms
                        )
                        metrics_data["deployments"] = deployment_info
                    
                    elif metric == "events":
                        events = self.cluster_entity.get_events(
                            cluster_name=cluster_name,
                            since_time_ms=since_time_ms,
                            until_time_ms=until_time_ms,
                            limit=100
                        )
                        metrics_data["events"] = events
                    
                except Exception as e:
                    logger.error(f"Error collecting {metric} for cluster {cluster_name}: {str(e)}")
                    metrics_data[f"{metric}_error"] = str(e)
            
            return {
                "cluster_name": cluster_name,
                "metrics": metrics_data,
                "time_window": {
                    "since": since_time.isoformat(),
                    "until": until_time.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting cluster metrics: {str(e)}")
            return {"error": f"Error getting cluster metrics: {str(e)}"}
    
    async def get_entity_metrics(
        self,
        entity_guid: str,
        metrics: Optional[List[str]] = None,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get metrics for any entity type by automatically determining the right entity-specific method.
        
        Args:
            entity_guid: New Relic entity GUID
            metrics: List of specific metrics to collect (optional)
            since_time: Start time for metrics collection (defaults to 30 minutes ago)
            until_time: End time for metrics collection (defaults to now)
            
        Returns:
            Dictionary with entity metrics
        """
        try:
            # Get entity details to determine type
            entity_details = await self.get_entity_details(entity_guid)
            
            if not entity_details:
                return {"error": f"Entity {entity_guid} not found"}
            
            entity_type = entity_details.get("type", "unknown")
            
            # Route to appropriate entity-specific method based on type
            if entity_type.startswith("KUBERNETES_POD"):
                return await self.get_pod_metrics(entity_guid, since_time, until_time, metrics)
            elif entity_type.startswith("KUBERNETES_NODE"):
                return await self.get_node_metrics(entity_guid, since_time, until_time, metrics)
            elif entity_type.startswith("KUBERNETES_CLUSTER"):
                # For clusters, we need to extract the cluster name
                cluster_name = entity_details.get("name", "unknown")
                return await self.get_cluster_metrics(cluster_name, metrics, since_time, until_time)
            elif entity_type.startswith("APPLICATION"):
                return await self.get_application_metrics(entity_guid, since_time, until_time, metrics)
            elif entity_type.startswith("HOST"):
                return await self.get_host_metrics(entity_guid, since_time, until_time, metrics)
            else:
                # For unsupported entity types, fall back to the generic method
                return self.collect_entity_metrics(
                    entity_guid=entity_guid,
                    entity_type=entity_type,
                    since_time=since_time,
                    until_time=until_time,
                    metrics_list=metrics
                )
            
        except Exception as e:
            logger.error(f"Error getting entity metrics: {str(e)}")
            return {"error": f"Error getting entity metrics: {str(e)}"}

    # Additional methods for Kubernetes deployment investigation
    
    def get_deployment_status(
        self,
        deployment_name: str,
        namespace: Optional[str] = None,
        cluster_name: Optional[str] = None,
        include_conditions: bool = True,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get the status of a Kubernetes deployment.
        
        Args:
            deployment_name: Name of the deployment
            namespace: Kubernetes namespace (optional)
            cluster_name: Kubernetes cluster name (optional)
            include_conditions: Whether to include deployment conditions
            since_time: Start time for data collection (defaults to 30 minutes ago)
            until_time: End time for data collection (defaults to now)
            
        Returns:
            Dictionary with deployment status information
        """
        logger.info(f"Getting status for deployment {deployment_name}")
        
        # Set default times if not provided
        if not since_time:
            since_time = datetime.now(UTC) - timedelta(minutes=30)
        if not until_time:
            until_time = datetime.now(UTC)
            
        # Calculate time window for query
        since_str = since_time.strftime('%Y-%m-%d %H:%M:%S')
        until_str = until_time.strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            # First, try to find the deployment entity
            query = f"""
            FROM K8sDeploymentSample
            WHERE deploymentName = '{deployment_name}'
            """
            
            if namespace:
                query += f" AND namespaceName = '{namespace}'"
            
            if cluster_name:
                query += f" AND clusterName = '{cluster_name}'"
                
            query += f" LIMIT 1 SINCE '{since_str}' UNTIL '{until_str}'"
            
            result = self.query_client.execute_nrql_query(query)
            
            if not result or "results" not in result or len(result["results"]) == 0:
                return {"error": f"Deployment {deployment_name} not found"}
            
            deployment_data = result["results"][0]
            
            # Get detailed status including replicas
            status_query = f"""
            FROM K8sDeploymentSample
            SELECT 
                latest(deploymentName) as deploymentName,
                latest(namespaceName) as namespace,
                latest(clusterName) as clusterName,
                latest(podsDesired) as podsDesired,
                latest(podsAvailable) as podsAvailable,
                latest(podsUnavailable) as podsUnavailable,
                latest(podsReady) as podsReady,
                latest(podsUpdated) as podsUpdated
            WHERE deploymentName = '{deployment_name}'
            """
            
            if namespace:
                status_query += f" AND namespaceName = '{namespace}'"
            
            if cluster_name:
                status_query += f" AND clusterName = '{cluster_name}'"
                
            status_query += f" FACET deploymentName LIMIT 1 SINCE '{since_str}' UNTIL '{until_str}'"
            
            status_result = self.query_client.execute_nrql_query(status_query)
            
            if not status_result or "facets" not in status_result or len(status_result["facets"]) == 0:
                return {"error": f"Could not get detailed status for deployment {deployment_name}"}
            
            status_data = status_result["facets"][0]["results"][0]
            
            # Prepare the response
            response = {
                "deployment_name": deployment_name,
                "namespace": status_data.get("namespace", "unknown"),
                "cluster": status_data.get("clusterName", "unknown"),
                "replica_status": {
                    "desired": status_data.get("podsDesired", 0),
                    "available": status_data.get("podsAvailable", 0),
                    "unavailable": status_data.get("podsUnavailable", 0),
                    "ready": status_data.get("podsReady", 0),
                    "updated": status_data.get("podsUpdated", 0)
                }
            }
            
            # Get conditions if requested
            if include_conditions:
                # This requires Kubernetes API access which New Relic might not expose directly,
                # so we'll check for any related events
                events_query = f"""
                FROM InfrastructureEvent
                SELECT
                    timestamp,
                    message,
                    attributes
                WHERE (category = 'kubernetes' OR category = 'k8s')
                AND message LIKE '%{deployment_name}%'
                LIMIT 100 SINCE '{since_str}' UNTIL '{until_str}'
                """
                
                events_result = self.query_client.execute_nrql_query(events_query)
                
                if events_result and "results" in events_result and len(events_result["results"]) > 0:
                    response["recent_events"] = events_result["results"]
            
            return response
            
        except Exception as e:
            logger.error(f"Error getting deployment status for {deployment_name}: {str(e)}")
            return {"error": f"Could not get deployment status: {str(e)}"}
    
    def get_deployment_pods(
        self,
        deployment_name: str,
        namespace: Optional[str] = None,
        cluster_name: Optional[str] = None,
        include_status: bool = True,
        include_conditions: bool = False,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get pods associated with a Kubernetes deployment.
        
        Args:
            deployment_name: Name of the deployment
            namespace: Kubernetes namespace (optional)
            cluster_name: Kubernetes cluster name (optional)
            include_status: Whether to include pod status details
            include_conditions: Whether to include pod conditions
            since_time: Start time for data collection (defaults to 30 minutes ago)
            until_time: End time for data collection (defaults to now)
            
        Returns:
            Dictionary with pods information
        """
        logger.info(f"Getting pods for deployment {deployment_name}")
        
        # Set default times if not provided
        if not since_time:
            since_time = datetime.now(UTC) - timedelta(minutes=30)
        if not until_time:
            until_time = datetime.now(UTC)
            
        # Calculate time window for query
        since_str = since_time.strftime('%Y-%m-%d %H:%M:%S')
        until_str = until_time.strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            # Query to find pods associated with the deployment
            query = f"""
            FROM K8sPodSample
            WHERE podName LIKE '{deployment_name}-%'
            """
            
            if namespace:
                query += f" AND namespaceName = '{namespace}'"
            
            if cluster_name:
                query += f" AND clusterName = '{cluster_name}'"
                
            query += f" LIMIT 100 SINCE '{since_str}' UNTIL '{until_str}'"
            
            result = self.query_client.execute_nrql_query(query)
            
            if not result or "results" not in result or len(result["results"]) == 0:
                return {"error": f"No pods found for deployment {deployment_name}"}
            
            # Process pods data
            pods = []
            for pod_data in result["results"]:
                pod = {
                    "pod_name": pod_data.get("podName", "unknown"),
                    "namespace": pod_data.get("namespaceName", "unknown"),
                    "node": pod_data.get("nodeName", "unknown")
                }
                
                if include_status:
                    pod["status"] = pod_data.get("status", "unknown")
                    pod["phase"] = pod_data.get("phase", "unknown")
                    pod["restart_count"] = pod_data.get("restartCount", 0)
                
                pods.append(pod)
                
            # If we want conditions, collect additional data
            if include_conditions:
                for pod in pods:
                    pod_events = self.collect_kubernetes_events(
                        pod["pod_name"], 
                        "KUBERNETES_POD",
                        cluster_name=cluster_name,
                        since_time=since_time,
                        until_time=until_time
                    )
                    pod["events"] = pod_events
            
            return {
                "deployment_name": deployment_name,
                "pod_count": len(pods),
                "pods": pods
            }
            
        except Exception as e:
            logger.error(f"Error getting pods for deployment {deployment_name}: {str(e)}")
            return {"error": f"Could not get pods: {str(e)}"}
    
    def analyze_deployment_resources(
        self,
        deployment_name: str,
        namespace: Optional[str] = None,
        cluster_name: Optional[str] = None,
        include_pods: bool = True,
        time_window_minutes: int = 60,
        include_recommendations: bool = False,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Analyze resource usage of a Kubernetes deployment.
        
        Args:
            deployment_name: Name of the deployment
            namespace: Kubernetes namespace (optional)
            cluster_name: Kubernetes cluster name (optional)
            include_pods: Whether to include individual pod metrics
            time_window_minutes: Time window for metrics analysis in minutes
            include_recommendations: Whether to include resource recommendations
            since_time: Start time for data collection (if None, uses time_window_minutes)
            until_time: End time for data collection (defaults to now)
            
        Returns:
            Dictionary with resource analysis
        """
        logger.info(f"Analyzing resources for deployment {deployment_name}")
        
        # Set default times if not provided
        if not until_time:
            until_time = datetime.now(UTC)
        
        if not since_time:
            # Use time_window_minutes if since_time not provided
            since_time = until_time - timedelta(minutes=time_window_minutes)
            
        # Calculate time window for query
        since_str = since_time.strftime('%Y-%m-%d %H:%M:%S')
        until_str = until_time.strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            # Get pods first
            pods_info = self.get_deployment_pods(
                deployment_name,
                namespace=namespace,
                cluster_name=cluster_name,
                since_time=since_time,
                until_time=until_time
            )
            
            if "error" in pods_info:
                return pods_info
            
            # Collect CPU and memory metrics for each pod
            deployment_metrics = {
                "cpu": {
                    "total": 0,
                    "average": 0,
                    "max": 0,
                    "by_pod": {}
                },
                "memory": {
                    "total": 0,
                    "average": 0,
                    "max": 0,
                    "by_pod": {}
                }
            }
            
            for pod in pods_info.get("pods", []):
                pod_name = pod.get("pod_name")
                
                # Query for CPU usage
                cpu_query = f"""
                FROM Metric SELECT average(pod.cpuUsedCores), max(pod.cpuUsedCores)
                WHERE podName = '{pod_name}' SINCE '{since_str}' UNTIL '{until_str}'
                """
                
                cpu_result = self.query_client.execute_nrql_query(cpu_query)
                
                if cpu_result and "results" in cpu_result and len(cpu_result["results"]) > 0:
                    cpu_avg = cpu_result["results"][0].get("average.pod.cpuUsedCores", 0)
                    cpu_max = cpu_result["results"][0].get("max.pod.cpuUsedCores", 0)
                    
                    deployment_metrics["cpu"]["by_pod"][pod_name] = {
                        "average": cpu_avg,
                        "max": cpu_max
                    }
                    
                    deployment_metrics["cpu"]["total"] += cpu_avg
                    if cpu_max > deployment_metrics["cpu"]["max"]:
                        deployment_metrics["cpu"]["max"] = cpu_max
                
                # Query for memory usage
                memory_query = f"""
                FROM Metric SELECT average(pod.memoryUsedBytes), max(pod.memoryUsedBytes)
                WHERE podName = '{pod_name}' SINCE '{since_str}' UNTIL '{until_str}'
                """
                
                memory_result = self.query_client.execute_nrql_query(memory_query)
                
                if memory_result and "results" in memory_result and len(memory_result["results"]) > 0:
                    memory_avg = memory_result["results"][0].get("average.pod.memoryUsedBytes", 0)
                    memory_max = memory_result["results"][0].get("max.pod.memoryUsedBytes", 0)
                    
                    # Convert to MB for readability
                    memory_avg_mb = memory_avg / (1024 * 1024)
                    memory_max_mb = memory_max / (1024 * 1024)
                    
                    deployment_metrics["memory"]["by_pod"][pod_name] = {
                        "average": memory_avg_mb,
                        "max": memory_max_mb,
                        "unit": "MB"
                    }
                    
                    deployment_metrics["memory"]["total"] += memory_avg_mb
                    if memory_max_mb > deployment_metrics["memory"]["max"]:
                        deployment_metrics["memory"]["max"] = memory_max_mb
            
            # Calculate averages
            pod_count = len(pods_info.get("pods", []))
            if pod_count > 0:
                deployment_metrics["cpu"]["average"] = deployment_metrics["cpu"]["total"] / pod_count
                deployment_metrics["memory"]["average"] = deployment_metrics["memory"]["total"] / pod_count
            
            # Don't include per-pod metrics if not requested
            if not include_pods:
                deployment_metrics["cpu"].pop("by_pod", None)
                deployment_metrics["memory"].pop("by_pod", None)
            
            # Add resource recommendations if requested
            if include_recommendations:
                recommendations = {
                    "cpu": {},
                    "memory": {}
                }
                
                # CPU recommendations
                current_cpu_usage = deployment_metrics["cpu"]["average"]
                max_cpu_usage = deployment_metrics["cpu"]["max"]
                
                if max_cpu_usage > 0:
                    cpu_headroom = 1.5  # 50% headroom
                    recommended_cpu = max_cpu_usage * cpu_headroom
                    recommendations["cpu"] = {
                        "current_average": current_cpu_usage,
                        "current_max": max_cpu_usage,
                        "recommended": recommended_cpu,
                        "unit": "cores"
                    }
                
                # Memory recommendations
                current_memory_usage = deployment_metrics["memory"]["average"]
                max_memory_usage = deployment_metrics["memory"]["max"]
                
                if max_memory_usage > 0:
                    memory_headroom = 1.5  # 50% headroom
                    recommended_memory = max_memory_usage * memory_headroom
                    recommendations["memory"] = {
                        "current_average": current_memory_usage,
                        "current_max": max_memory_usage,
                        "recommended": recommended_memory,
                        "unit": "MB"
                    }
                
                deployment_metrics["recommendations"] = recommendations
            
            return {
                "deployment_name": deployment_name,
                "pod_count": pod_count,
                "time_window": {
                    "since": since_time.isoformat(),
                    "until": until_time.isoformat(),
                    "duration_minutes": int((until_time - since_time).total_seconds() / 60)
                },
                "metrics": deployment_metrics
            }
            
        except Exception as e:
            logger.error(f"Error analyzing resources for deployment {deployment_name}: {str(e)}")
            return {"error": f"Could not analyze resources: {str(e)}"}
    
    def get_deployment_history(
        self,
        deployment_name: str,
        namespace: Optional[str] = None,
        cluster_name: Optional[str] = None,
        revisions_to_show: int = 5,
        include_diff: bool = False,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get history of changes to a Kubernetes deployment.
        
        Args:
            deployment_name: Name of the deployment
            namespace: Kubernetes namespace (optional)
            cluster_name: Kubernetes cluster name (optional)
            revisions_to_show: Number of revision histories to show
            include_diff: Whether to include differences between revisions
            since_time: Start time for data collection (defaults to 7 days ago)
            until_time: End time for data collection (defaults to now)
            
        Returns:
            Dictionary with deployment history
        """
        logger.info(f"Getting history for deployment {deployment_name}")
        
        # Set default times if not provided
        if not since_time:
            since_time = datetime.now(UTC) - timedelta(days=7)
        if not until_time:
            until_time = datetime.now(UTC)
            
        # Calculate time window for query
        since_str = since_time.strftime('%Y-%m-%d %H:%M:%S')
        until_str = until_time.strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            # New Relic doesn't directly store deployment history,
            # so we'll look for deployment-related events instead
            
            query = f"""
            FROM InfrastructureEvent
            SELECT
                timestamp,
                message,
                attributes
            WHERE (category = 'kubernetes' OR category = 'k8s')
            AND message LIKE '%{deployment_name}%'
            AND (message LIKE '%deploy%' OR message LIKE '%updat%' OR message LIKE '%scal%' OR message LIKE '%revis%')
            """
            
            if cluster_name:
                query += f" AND clusterName = '{cluster_name}'"
                
            query += f" LIMIT {revisions_to_show * 2} SINCE '{since_str}' UNTIL '{until_str}'"
            query += " ORDER BY timestamp DESC"
            
            result = self.query_client.execute_nrql_query(query)
            
            if not result or "results" not in result or len(result["results"]) == 0:
                return {"error": f"No deployment history found for {deployment_name}"}
            
            # Process the events
            history = []
            for event in result["results"]:
                event_data = {
                    "timestamp": event.get("timestamp"),
                    "message": event.get("message"),
                }
                
                # Extract attributes if available
                attributes = event.get("attributes", {})
                if attributes:
                    event_data["details"] = attributes
                
                history.append(event_data)
            
            # Limit the number of entries if we got more than requested
            if len(history) > revisions_to_show:
                history = history[:revisions_to_show]
            
            # Include diffs if requested
            if include_diff and len(history) > 1:
                for i in range(1, len(history)):
                    history[i-1]["changes_from_previous"] = "Changes not available in New Relic events data"
            
            return {
                "deployment_name": deployment_name,
                "revision_count": len(history),
                "time_window": {
                    "since": since_time.isoformat(),
                    "until": until_time.isoformat()
                },
                "history": history
            }
            
        except Exception as e:
            logger.error(f"Error getting history for deployment {deployment_name}: {str(e)}")
            return {"error": f"Could not get deployment history: {str(e)}"}
    
    def check_nodes_status(
        self,
        nodes_selector: str,
        cluster_name: Optional[str] = None,
        include_metrics: Optional[List[str]] = None,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Check status of Kubernetes nodes.
        
        Args:
            nodes_selector: Selector for nodes ('pods-from-deployment' or specific node names)
            cluster_name: Kubernetes cluster name (optional)
            include_metrics: List of metrics to include (cpu, memory, pods, etc.)
            since_time: Start time for data collection (defaults to 30 minutes ago)
            until_time: End time for data collection (defaults to now)
            
        Returns:
            Dictionary with nodes status
        """
        logger.info(f"Checking nodes status with selector: {nodes_selector}")
        
        # Set default times if not provided
        if not since_time:
            since_time = datetime.now(UTC) - timedelta(minutes=30)
        if not until_time:
            until_time = datetime.now(UTC)
            
        # Calculate time window for query
        since_str = since_time.strftime('%Y-%m-%d %H:%M:%S')
        until_str = until_time.strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            # Handle special selector for pods-from-deployment
            if nodes_selector == "pods-from-deployment":
                # This requires context from another call, so we'll just get all nodes
                # from the cluster instead
                
                query = "FROM K8sNodeSample SELECT latest(nodeName) as nodeName"
                
                if cluster_name:
                    query += f" WHERE clusterName = '{cluster_name}'"
                    
                query += f" FACET nodeName LIMIT 100 SINCE '{since_str}' UNTIL '{until_str}'"
                
                node_names_result = self.query_client.execute_nrql_query(query)
                
                if not node_names_result or "facets" not in node_names_result:
                    return {"error": "Could not retrieve node list"}
                
                node_names = [facet["name"] for facet in node_names_result.get("facets", [])]
            else:
                # Assume it's a comma-separated list of node names
                node_names = [name.strip() for name in nodes_selector.split(",")]
            
            # Default metrics if not specified
            if not include_metrics:
                include_metrics = ["cpu", "memory", "pods"]
            
            # Collect node status and metrics
            nodes_status = []
            
            for node_name in node_names:
                # Basic node info
                node_query = f"""
                FROM K8sNodeSample
                SELECT
                    latest(nodeName) as nodeName,
                    latest(clusterName) as clusterName,
                    latest(status) as status,
                    latest(containerRuntimeVersion) as containerRuntime,
                    latest(kubeletVersion) as kubeletVersion
                WHERE nodeName = '{node_name}'
                LIMIT 1 SINCE '{since_str}' UNTIL '{until_str}'
                """
                
                node_result = self.query_client.execute_nrql_query(node_query)
                
                if not node_result or "results" not in node_result or len(node_result["results"]) == 0:
                    continue
                
                node_info = node_result["results"][0]
                
                node_status = {
                    "node_name": node_name,
                    "cluster": node_info.get("clusterName", "unknown"),
                    "status": node_info.get("status", "unknown"),
                    "container_runtime": node_info.get("containerRuntime", "unknown"),
                    "kubelet_version": node_info.get("kubeletVersion", "unknown"),
                    "metrics": {}
                }
                
                # Add requested metrics
                if "cpu" in include_metrics:
                    cpu_query = f"""
                    FROM K8sNodeSample
                    SELECT
                        latest(allocatableCpuCores) as allocatableCpu,
                        latest(capacityCpuCores) as capacityCpu,
                        latest(cpuUsedCores) as usedCpu
                    WHERE nodeName = '{node_name}'
                    LIMIT 1 SINCE '{since_str}' UNTIL '{until_str}'
                    """
                    
                    cpu_result = self.query_client.execute_nrql_query(cpu_query)
                    
                    if cpu_result and "results" in cpu_result and len(cpu_result["results"]) > 0:
                        cpu_data = cpu_result["results"][0]
                        
                        allocatable = cpu_data.get("allocatableCpu", 0)
                        capacity = cpu_data.get("capacityCpu", 0)
                        used = cpu_data.get("usedCpu", 0)
                        
                        # Calculate percentages
                        usage_percent = (used / allocatable * 100) if allocatable > 0 else 0
                        
                        node_status["metrics"]["cpu"] = {
                            "allocatable": allocatable,
                            "capacity": capacity,
                            "used": used,
                            "usage_percent": usage_percent
                        }
                
                if "memory" in include_metrics:
                    memory_query = f"""
                    FROM K8sNodeSample
                    SELECT
                        latest(allocatableMemoryBytes) as allocatableMemory,
                        latest(capacityMemoryBytes) as capacityMemory,
                        latest(memoryUsedBytes) as usedMemory
                    WHERE nodeName = '{node_name}'
                    LIMIT 1 SINCE '{since_str}' UNTIL '{until_str}'
                    """
                    
                    memory_result = self.query_client.execute_nrql_query(memory_query)
                    
                    if memory_result and "results" in memory_result and len(memory_result["results"]) > 0:
                        memory_data = memory_result["results"][0]
                        
                        allocatable = memory_data.get("allocatableMemory", 0)
                        capacity = memory_data.get("capacityMemory", 0)
                        used = memory_data.get("usedMemory", 0)
                        
                        # Convert to MB for readability
                        allocatable_mb = allocatable / (1024 * 1024)
                        capacity_mb = capacity / (1024 * 1024)
                        used_mb = used / (1024 * 1024)
                        
                        # Calculate percentages
                        usage_percent = (used / allocatable * 100) if allocatable > 0 else 0
                        
                        node_status["metrics"]["memory"] = {
                            "allocatable": allocatable_mb,
                            "capacity": capacity_mb,
                            "used": used_mb,
                            "usage_percent": usage_percent,
                            "unit": "MB"
                        }
                
                if "pods" in include_metrics:
                    pods_query = f"""
                    FROM K8sNodeSample
                    SELECT
                        latest(allocatablePods) as allocatablePods,
                        latest(capacityPods) as capacityPods,
                        latest(podCount) as podCount
                    WHERE nodeName = '{node_name}'
                    LIMIT 1 SINCE '{since_str}' UNTIL '{until_str}'
                    """
                    
                    pods_result = self.query_client.execute_nrql_query(pods_query)
                    
                    if pods_result and "results" in pods_result and len(pods_result["results"]) > 0:
                        pods_data = pods_result["results"][0]
                        
                        allocatable = pods_data.get("allocatablePods", 0)
                        capacity = pods_data.get("capacityPods", 0)
                        count = pods_data.get("podCount", 0)
                        
                        # Calculate percentages
                        usage_percent = (count / allocatable * 100) if allocatable > 0 else 0
                        
                        node_status["metrics"]["pods"] = {
                            "allocatable": allocatable,
                            "capacity": capacity,
                            "count": count,
                            "usage_percent": usage_percent
                        }
                
                nodes_status.append(node_status)
            
            return {
                "node_count": len(nodes_status),
                "time_window": {
                    "since": since_time.isoformat(),
                    "until": until_time.isoformat()
                },
                "nodes": nodes_status
            }
            
        except Exception as e:
            logger.error(f"Error checking nodes status: {str(e)}")
            return {"error": f"Could not check nodes status: {str(e)}"}

    def get_kubernetes_entity_description(
        self,
        entity_guid: Optional[str] = None,
        entity_type: str = "Pod",
        entity_name: Optional[str] = None,
        cluster_name: Optional[str] = None,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None,
        account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get the detailed description of a Kubernetes entity from New Relic.
        
        This retrieves data similar to what you would get from 'kubectl describe'
        for various Kubernetes resources (Pod, Node, Deployment, etc).
        
        Args:
            entity_guid: The New Relic entity GUID (optional)
            entity_type: Type of Kubernetes entity (Pod, Node, Deployment, etc.)
            entity_name: Name of the entity (optional, required if entity_guid not provided)
            cluster_name: Name of the Kubernetes cluster (optional)
            since_time: Start time for data collection (defaults to 24 hours ago)
            until_time: End time for data collection (defaults to now)
            account_id: Optional account ID to override client default
            
        Returns:
            Dictionary containing the entity description and metadata
        """
        log_context = ""
        if entity_guid:
            log_context += f"entity GUID {entity_guid}"
        if entity_type:
            log_context += f"{', ' if log_context else ''}type {entity_type}"
        if entity_name:
            log_context += f"{', ' if log_context else ''}name {entity_name}"
        if cluster_name:
            log_context += f"{', ' if log_context else ''}cluster {cluster_name}"
            
        logger.info(f"Getting Kubernetes description for {log_context}")
        
        # Set default times if not provided
        if not since_time:
            since_time = datetime.now(UTC) - timedelta(hours=24)
        if not until_time:
            until_time = datetime.now(UTC)
            
        # Convert to epoch milliseconds for the query
        since_ms = int(since_time.timestamp() * 1000)
        until_ms = int(until_time.timestamp() * 1000)
        
        # Get account ID from client if not provided
        if not account_id:
            account_id = self.query_client.client.account_id
        
        try:
            # Normalize entity type to match expected format in InfrastructureEvent.type
            k8s_type = entity_type.capitalize() if entity_type else "Pod"
            event_type = f"{k8s_type}.Description"

            # Construct filters based on available information
            filters = []
            filters.append("category = 'kubernetes'")
            filters.append(f"type = '{event_type}'")
            
            # Don't filter by entityGuid as some events don't have this field
            # and we don't want to miss those events
            
            if entity_name:
                filters.append(f"displayName LIKE '%{entity_name}%'")
            
            if cluster_name:
                filters.append(f"clusterName = '{cluster_name}'")
            
            # Combine all filters
            where_clause = " AND ".join(filters)
            
            # Construct the NRQL query to fetch description data
            query = f"""
            FROM InfrastructureEvent 
            SELECT latest(timestamp), latest(`summary.part[0]`), latest(`summary.part[1]`), 
                   latest(`summary.part[2]`), latest(`summary.part[3]`), latest(`summary.part[4]`), 
                   latest(`summary.part[5]`), latest(`summary.part[6]`), latest(`summary.part[7]`), 
                   latest(`summary.part[8]`), latest(`summary.part[9]`), latest(type), 
                   latest(displayName), latest(clusterName), latest(entityGuid)
            WHERE {where_clause} 
            SINCE {since_ms} UNTIL {until_ms}
            LIMIT 1
            """
            
            logger.debug(f"Executing query: {query}")
            
            # Execute the query using the correct method
            results = self.query_client.execute_nrql(query, account_id)
            
            if not results or len(results) == 0:
                error_message = f"No description data found for {k8s_type}"
                if entity_name:
                    error_message += f" named '{entity_name}'"
                if cluster_name:
                    error_message += f" in cluster '{cluster_name}'"
                if entity_guid:
                    error_message += f" with GUID '{entity_guid}'"
                
                logger.warning(error_message)
                return {
                    "error": error_message,
                    "entity_guid": entity_guid,
                    "entity_type": k8s_type,
                    "entity_name": entity_name,
                    "cluster_name": cluster_name
                }
            
            # Process results to combine all parts into a full description
            result_data = results[0]
            description_parts = []
            
            # Collect all non-empty description parts
            for i in range(10):  # Checking summary.part[0] through summary.part[9]
                part_key = f"latest.summary.part[{i}]"
                if part_key in result_data and result_data[part_key]:
                    description_parts.append(result_data[part_key])
            
            # Join all parts to create the full description
            full_description = "".join(description_parts)
            
            # Get timestamp if available
            timestamp = result_data.get("latest.timestamp")
            timestamp_iso = None
            if timestamp:
                # Convert epoch milliseconds to ISO format
                try:
                    timestamp_iso = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc).isoformat()
                except Exception as e:
                    logger.error(f"Error converting timestamp: {str(e)}")
            
            # Get entity information from result
            retrieved_entity_name = result_data.get("latest.displayName", "")
            retrieved_cluster_name = result_data.get("latest.clusterName", "")
            retrieved_entity_guid = result_data.get("latest.entityGuid", entity_guid)
            retrieved_entity_type = k8s_type
            
            # Parse additional information from the description
            additional_info = {}
            if full_description:
                lines = full_description.split('\n')
                current_section = "metadata"
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                        
                    # Check for section headers (usually ending with ':')
                    if line.endswith(':') and not ': ' in line:
                        current_section = line.rstrip(':').lower().replace(' ', '_')
                        additional_info[current_section] = []
                    elif ': ' in line and current_section == "metadata":
                        key, value = line.split(': ', 1)
                        additional_info[key.lower().replace(' ', '_')] = value
                    elif current_section in additional_info and isinstance(additional_info[current_section], list):
                        additional_info[current_section].append(line)
            
            return {
                "entity_guid": retrieved_entity_guid,
                "entity_name": retrieved_entity_name or entity_name,
                "entity_type": retrieved_entity_type,
                "cluster_name": retrieved_cluster_name or cluster_name,
                "description": full_description,
                "timestamp": timestamp_iso,
                "raw_timestamp": timestamp,
                "metadata": additional_info
            }
            
        except Exception as e:
            error_context = f"Error getting Kubernetes description for {entity_type}"
            if entity_name:
                error_context += f" named '{entity_name}'"
            if cluster_name:
                error_context += f" in cluster '{cluster_name}'"
            if entity_guid:
                error_context += f" with GUID '{entity_guid}'"
                
            logger.error(f"{error_context}: {str(e)}")
            return {
                "error": f"Failed to get Kubernetes description: {str(e)}",
                "entity_guid": entity_guid,
                "entity_type": entity_type,
                "entity_name": entity_name,
                "cluster_name": cluster_name
            }

    # Example of a new method that could be registered as a tool
    async def get_database_metrics(
        self,
        database_id: str,
        metric_names: Optional[List[str]] = None,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get metrics for a database.
        
        This is an example of a new method that could be added to the metrics collector
        and registered as a tool in the runbook service.
        
        Args:
            database_id: ID of the database
            metric_names: List of metrics to retrieve (optional)
            since_time_ms: Start time in epoch milliseconds (optional)
            until_time_ms: End time in epoch milliseconds (optional)
            
        Returns:
            Dictionary with database metrics
        """
        logger.info(f"Getting metrics for database: {database_id}")
        
        # Set default times if not provided
        now_ms = int(datetime.now(UTC).timestamp() * 1000)
        if not since_time_ms:
            since_time_ms = now_ms - (30 * 60 * 1000)  # 30 minutes ago
        if not until_time_ms:
            until_time_ms = now_ms
            
        # Convert epoch milliseconds to datetime for logging
        since_time = datetime.fromtimestamp(since_time_ms / 1000, UTC)
        until_time = datetime.fromtimestamp(until_time_ms / 1000, UTC)
        
        # Set default metrics if not provided
        if not metric_names:
            metric_names = ["cpu_usage", "memory_usage", "connections", "queries_per_second"]
            
        try:
            # This is where you would implement the actual metric collection
            # For this example, we'll just return dummy data
            metrics = {}
            
            for metric in metric_names:
                if metric == "cpu_usage":
                    metrics[metric] = {"average": 45.2, "max": 78.5, "min": 22.1, "unit": "%"}
                elif metric == "memory_usage":
                    metrics[metric] = {"average": 2048, "max": 3500, "min": 1024, "unit": "MB"}
                elif metric == "connections":
                    metrics[metric] = {"average": 125, "max": 250, "min": 50, "unit": "connections"}
                elif metric == "queries_per_second":
                    metrics[metric] = {"average": 350, "max": 1200, "min": 100, "unit": "qps"}
            
            return {
                "database_id": database_id,
                "database_name": f"db-{database_id}",  # Would come from actual query
                "database_type": "PostgreSQL",  # Would come from actual query
                "time_window": {
                    "since": since_time.isoformat(),
                    "until": until_time.isoformat(),
                    "duration_minutes": int((until_time - since_time).total_seconds() / 60)
                },
                "metrics": metrics
            }
            
        except Exception as e:
            logger.error(f"Error getting database metrics: {str(e)}")
            return {"error": f"Could not get database metrics: {str(e)}"} 