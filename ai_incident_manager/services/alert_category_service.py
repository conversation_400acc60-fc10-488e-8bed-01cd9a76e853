"""
Alert Category Service.

This service provides a unified interface for working with alert categories,
handling both database and in-memory lookups, and providing caching for performance.
"""

import os
import re
import json
import yaml
from loguru import logger
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from functools import lru_cache

import dotenv
from ai_incident_manager.database.postgres import PostgresDB

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logger = logger.bind(name="alert_category_service")


class AlertCategoryService:
    """
    Service for managing alert categories.
    
    This service provides a unified interface for working with alert categories,
    whether they are stored in the database or loaded from YAML files.
    It also provides caching for performance.
    """
    
    def __init__(self, config_path: str = "ai_incident_manager/config/alert_categories.yaml", refresh_cache_interval: int = 300):
        """
        Initialize the alert category service.
        
        Args:
            config_path: Path to the YAML configuration file (default: "ai_incident_manager/config/alert_categories.yaml")
            refresh_cache_interval: How often to refresh the cache in seconds (default: 5 minutes)
        """
        self.db = PostgresDB()
        self.config_path = config_path
        self.refresh_cache_interval = refresh_cache_interval
        self.last_cache_refresh = datetime.now() - timedelta(seconds=refresh_cache_interval + 1)
        self._cached_categories = {}
        self._cached_condition_categories = {}
        
        # Initial cache load
        self._refresh_cache()
    
    def _load_yaml_categories(self) -> List[Dict[str, Any]]:
        """
        Load categories from the YAML file.
        
        Returns:
            List of category dictionaries
        """
        try:
            if not os.path.exists(self.config_path):
                logger.warning(f"Config file not found: {self.config_path}")
                return []
                
            with open(self.config_path, 'r') as file:
                config = yaml.safe_load(file)
                
            # Check which format we're using
            if config and 'condition_categories' in config:
                categories = config['condition_categories']
                logger.info(f"Loaded {len(categories)} alert categories from config (new format)")
                return categories
            elif config and 'categories' in config:
                categories = config['categories']
                logger.info(f"Loaded {len(categories)} alert categories from config (old format)")
                return categories
            else:
                logger.warning(f"No categories found in config file: {self.config_path}")
                return []
                
        except Exception as e:
            logger.error(f"Error loading alert categories from YAML: {str(e)}")
            return []
    
    def _refresh_cache(self) -> None:
        """Refresh the category cache from database and memory."""
        now = datetime.now()
        
        # Only refresh if the cache interval has elapsed
        if (now - self.last_cache_refresh).total_seconds() < self.refresh_cache_interval:
            return
            
        logger.debug("Refreshing alert category cache")
        
        # Reset caches
        self._cached_categories = {}
        self._cached_condition_categories = {}
        
        # Load categories from database
        try:
            with self.db.get_connection() as conn:
                with conn.cursor() as cur:
                    # Get all categories with their details
                    cur.execute("""
                    SELECT id, category_name, description 
                    FROM alert_categories
                    """)
                    
                    for category_id, category_name, description in cur.fetchall():
                        self._cached_categories[category_name] = {
                            "id": category_id,
                            "category": category_name,
                            "description": description,
                            "likely_causes": [],
                            "metrics_to_check": [],
                            "metrics": [],
                            "runbook": "",
                            "entity_relationships": [],
                            "source": "database"
                        }
                    
                    # Load related data for each category
                    for category_name, category in self._cached_categories.items():
                        category_id = category["id"]
                        
                        # Get likely causes
                        cur.execute("""
                        SELECT cause_description 
                        FROM category_likely_causes
                        WHERE category_id = %s
                        ORDER BY probability DESC
                        """, (category_id,))
                        
                        category["likely_causes"] = [row[0] for row in cur.fetchall()]
                        
                        # Get metrics to check (new format)
                        cur.execute("""
                        SELECT metric_name 
                        FROM category_metrics
                        WHERE category_id = %s
                        ORDER BY importance DESC
                        """, (category_id,))
                        
                        metrics = []
                        for row in cur.fetchall():
                            metrics.append({"name": row[0]})
                        
                        category["metrics"] = metrics
                        # Keep old format for backward compatibility
                        category["metrics_to_check"] = [m["name"] for m in metrics]
                        
                        # Get runbook steps
                        cur.execute("""
                        SELECT title, description, tool_name, tool_parameters
                        FROM category_runbooks
                        WHERE category_id = %s
                        ORDER BY step_number
                        """, (category_id,))
                        
                        runbook_steps = []
                        for i, (title, description, tool_name, tool_parameters) in enumerate(cur.fetchall()):
                            step_text = f"{i+1}. {title}"
                            if tool_name:
                                step_text += f":\n   - Tool: {tool_name}"
                                if tool_parameters:
                                    params_str = json.dumps(tool_parameters)
                                    step_text += f"({params_str})"
                            runbook_steps.append(step_text)
                        
                        if runbook_steps:
                            category["runbook"] = "\n".join(runbook_steps)
                        
                        # Get entity relationships
                        cur.execute("""
                        SELECT target_type, relationship_type, metrics_to_collect
                        FROM category_entity_relationships
                        WHERE category_id = %s
                        """, (category_id,))
                        
                        entity_relationships = []
                        for target_type, relationship_type, metrics_to_collect in cur.fetchall():
                            entity_relationships.append({
                                "target_type": target_type,
                                "relationship_type": relationship_type,
                                "metrics": metrics_to_collect
                            })
                        
                        category["entity_relationships"] = entity_relationships
                    
                    # Get condition to category mappings from database
                    cur.execute("""
                    SELECT condition_id, ac.category_name
                    FROM condition_category_mapping ccm
                    JOIN alert_categories ac ON ccm.category_id = ac.id
                    """)
                    
                    for condition_id, category_name in cur.fetchall():
                        self._cached_condition_categories[condition_id] = category_name
        
        except Exception as e:
            logger.error(f"Error refreshing category cache from database: {str(e)}")
        
        # Add categories from YAML that aren't in the database
        yaml_categories = self._load_yaml_categories()
        for yaml_category in yaml_categories:
            category_name = yaml_category.get("category")
            if category_name and category_name not in self._cached_categories:
                # Convert to our standard format
                category_info = {
                    "category": category_name,
                    "description": yaml_category.get("description", ""),
                    "likely_causes": yaml_category.get("likely_causes", []),
                    "title_pattern": yaml_category.get("title_pattern", ""),
                    "condition_pattern": yaml_category.get("condition_pattern", ""),
                    "source": "yaml"
                }
                
                # Handle metrics in new or old format
                if "metrics" in yaml_category:
                    category_info["metrics"] = yaml_category["metrics"]
                    # Create metrics_to_check from metrics for backward compatibility
                    category_info["metrics_to_check"] = [m.get("name") for m in yaml_category["metrics"]]
                elif "metrics_to_check" in yaml_category:
                    # Old format: convert to new format
                    metrics = []
                    for metric_name in yaml_category["metrics_to_check"]:
                        metrics.append({"name": metric_name})
                    category_info["metrics"] = metrics
                    category_info["metrics_to_check"] = yaml_category["metrics_to_check"]
                else:
                    category_info["metrics"] = []
                    category_info["metrics_to_check"] = []
                
                # Add entity relationships
                if "entity_relationships" in yaml_category:
                    category_info["entity_relationships"] = yaml_category["entity_relationships"]
                else:
                    category_info["entity_relationships"] = []
                
                # Add entity relationship mapping
                if "entity_relationship_mapping" in yaml_category:
                    category_info["entity_relationship_mapping"] = yaml_category["entity_relationship_mapping"]
                else:
                    category_info["entity_relationship_mapping"] = {}
                
                # Add runbook
                if "runbook" in yaml_category:
                    category_info["runbook"] = yaml_category["runbook"]
                else:
                    category_info["runbook"] = ""
                
                # Store in cache
                self._cached_categories[category_name] = category_info
                
                # Add condition_id mappings if present
                if "condition_ids" in yaml_category:
                    for condition_id in yaml_category["condition_ids"]:
                        self._cached_condition_categories[condition_id] = category_name
        
        self.last_cache_refresh = now
    
    def get_category_by_condition_id(self, condition_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a category by condition ID.
        
        Args:
            condition_id: The condition ID to look up
            
        Returns:
            Category details or None if not found
        """
        self._refresh_cache()
        
        # Check if we have a mapping in cache
        if condition_id in self._cached_condition_categories:
            category_name = self._cached_condition_categories[condition_id]
            if category_name in self._cached_categories:
                result = self._cached_categories[category_name].copy()
                result["condition_id"] = condition_id
                return result
        
        return None
    
    def get_category_by_patterns(self, alert_title: str, condition_name: str) -> Optional[Dict[str, Any]]:
        """
        Get a category by matching patterns in the title and condition name.
        
        Args:
            alert_title: The alert title to match
            condition_name: The condition name to match
            
        Returns:
            Category details or None if no match found
        """
        self._refresh_cache()
        
        for category_name, category in self._cached_categories.items():
            # Check for condition pattern match
            condition_pattern = category.get("condition_pattern")
            if condition_pattern and re.search(condition_pattern, condition_name, re.IGNORECASE):
                return category.copy()
            
            # Check for title pattern match
            title_pattern = category.get("title_pattern")
            if title_pattern and re.search(title_pattern, alert_title, re.IGNORECASE):
                return category.copy()
        
        return None
    
    def get_runbooks_for_category(self, category_name: str) -> List[str]:
        """
        Get the list of runbook IDs associated with this alert category.
        
        Args:
            category_name: Name of the category
            
        Returns:
            List of runbook IDs recommended for this category
        """
        self._refresh_cache()
        
        # First check if we have the category
        if category_name not in self._cached_categories:
            logger.warning(f"Category not found: {category_name}")
            return []
            
        category = self._cached_categories[category_name]
        
        # Check if we have runbook IDs in the category (new field)
        if "runbook_ids" in category:
            return category["runbook_ids"]
            
        # Legacy support - extract runbook IDs from the runbook text
        # This is a fallback for categories that don't have explicit runbook_ids
        runbook_text = category.get("runbook", "")
        runbook_ids = []
        
        # Use regex to find runbook IDs in the format "runbook://{id}"
        matches = re.findall(r"runbook://([a-zA-Z0-9_-]+)", runbook_text)
        if matches:
            runbook_ids.extend(matches)
            
        return runbook_ids
    
    def get_category(self, alert_title: str, condition_name: str, condition_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get category information using all available data.
        
        This is the main method to use for categorizing alerts. It will:
        1. Try to find a category by condition ID
        2. If not found, try to find by pattern matching
        3. If still not found, return an unknown category
        
        Args:
            alert_title: The alert title
            condition_name: The condition name
            condition_id: Optional condition ID for exact matching
            
        Returns:
            Category information
        """
        # Try by condition ID first if provided
        if condition_id:
            category = self.get_category_by_condition_id(condition_id)
            if category:
                return category
        
        # Try by pattern matching
        category = self.get_category_by_patterns(alert_title, condition_name)
        if category:
            return category
        
        # Return unknown category
        return {
            "category": "unknown",
            "description": "This alert does not match any known categories. Use general troubleshooting steps.",
            "likely_causes": ["Unknown issue"],
            "metrics_to_check": [],
            "runbook": "General troubleshooting",
            "source": "default"
        }
    
    def get_all_categories(self) -> List[Dict[str, Any]]:
        """Get all available categories."""
        self._refresh_cache()
        return list(self._cached_categories.values())


# Singleton instance
_instance = None

def get_alert_category_service() -> AlertCategoryService:
    """Get the singleton instance of the alert category service."""
    global _instance
    if _instance is None:
        _instance = AlertCategoryService()
    return _instance 