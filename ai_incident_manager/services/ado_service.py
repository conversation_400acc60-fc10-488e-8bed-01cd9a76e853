"""
Azure DevOps Service for incident tracking.

This service provides methods for creating and updating tickets in Azure DevOps,
which allows for tracking incidents through their lifecycle.
"""

import os
import json
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger
import dotenv
from utils.azure_devops import AzureDevops

# Load environment variables
dotenv.load_dotenv()

# Configure logging
logger = logger.bind(name="ado_service")

class ADOService:
    """
    Service for managing Azure DevOps tickets.
    
    This service provides methods to create tickets when incidents start,
    and update them when investigations are complete.
    """
    
    def __init__(self):
        """
        Initialize the Azure DevOps service with credentials from environment variables.
        """
        self.api_token = os.environ.get("ADO_PERSONAL_ACCESS_TOKEN")
        self.organization = os.environ.get("ADO_ORG_NAME", "Ivanti")
        self.project = os.environ.get("ADO_PROJECT_NAME", "AI Automation and Observability")
        self.api_version = os.environ.get("ADO_API_VERSION", "7.0")
        self.area_path = os.environ.get("ADO_AREA_PATH", "AI Automation and Observability")
        self.ticket_base_url = os.environ.get("ADO_TICKET_BASE_URL", f"https://dev.azure.com/{self.organization}/{self.project}/_workitems/edit/")
        self.dashboard_base_url = os.environ.get("IIM_DASHBOARD_BASE_URL", "https://obv-ai-compute.ivantiai.com:8080/incident/")
        
        if not self.api_token:
            logger.warning("ADO_PERSONAL_ACCESS_TOKEN not set in environment variables")
            self.ado_client = None
        else:
            self.ado_client = AzureDevops(
                self.api_token, 
                self.organization, 
                self.project, 
                self.api_version
            )
            logger.info("ADO service initialized successfully")
    
    def _serialize_state(self, state_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert Pydantic models and nested objects to plain dictionaries for serialization.
        
        Args:
            state_data: The state data to serialize
            
        Returns:
            Dictionary with all data converted to serializable formats
        """
        serializable_state = {}
        for key, value in state_data.items():
            if hasattr(value, "model_dump"):
                # Handle Pydantic v2 models
                serializable_state[key] = value.model_dump()
            elif hasattr(value, "dict"):
                # Handle Pydantic v1 models
                serializable_state[key] = value.dict()
            elif isinstance(value, list):
                # Handle lists of Pydantic models
                serializable_list = []
                for item in value:
                    if hasattr(item, "model_dump"):
                        serializable_list.append(item.model_dump())
                    elif hasattr(item, "dict"):
                        serializable_list.append(item.dict())
                    else:
                        serializable_list.append(item)
                serializable_state[key] = serializable_list
            else:
                serializable_state[key] = value
        return serializable_state
    
    def create_initial_ticket(self, incident_state: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Create an initial ticket when an incident starts being analyzed.
        
        Args:
            incident_state: The current incident state
            
        Returns:
            Dictionary with ticket ID and URL or None if creation failed
        """
        if not self.ado_client:
            logger.error("ADO client not initialized")
            return None
        
        try:
            # Ensure all data is serializable
            serializable_state = self._serialize_state(incident_state)
            
            # Extract incident details
            incident_id = serializable_state.get("incident_id", "Unknown")
            title = serializable_state.get("title", "Unknown Alert")
            severity = serializable_state.get("severity", "Unknown")
            product = serializable_state.get("product", "Unknown")
            region = serializable_state.get("region", "Unknown")
            
            # Create dashboard URL
            dashboard_url = f"{self.dashboard_base_url}{incident_id}"
            
            # Check if we already have RCA results
            root_cause = serializable_state.get("root_cause")
            has_rca = root_cause is not None and serializable_state.get("rca_details")
            
            # Format ticket title
            ticket_title = f"[{severity}] {title} (ID: {incident_id})"
            
            # If we have RCA, include it in the title
            if has_rca:
                # Truncate the root cause for the title
                short_root_cause = root_cause[:50] + "..." if len(root_cause) > 50 else root_cause
                ticket_title = f"[{severity}] {title} - {short_root_cause} (ID: {incident_id})"
            
            # Extract entities if available
            entities = serializable_state.get("entities", [])
            entity_count = len(entities)
            primary_entities = []
            
            for entity in entities:
                if isinstance(entity, dict) and entity.get("is_primary", False):
                    primary_entities.append(entity)
                elif hasattr(entity, "is_primary") and entity.is_primary:
                    primary_entities.append(entity)
                    
            # Get primary entity names
            primary_entity_names = []
            for entity in primary_entities:
                if isinstance(entity, dict):
                    primary_entity_names.append(entity.get("entity_name", "Unknown"))
                else:
                    primary_entity_names.append(getattr(entity, "entity_name", "Unknown"))
            
            # Get severity styles
            severity_bg_color = "#FDE7E9" if severity.upper() in ["CRITICAL", "FATAL"] else (
                "#FEF5E7" if severity.upper() == "WARNING" else (
                    "#E7F4E4" if severity.upper() in ["LOW", "INFO", "INFORMATION"] else "#F3F2F1"
                )
            )
            severity_color = "#D13438" if severity.upper() in ["CRITICAL", "FATAL"] else (
                "#FF8C00" if severity.upper() == "WARNING" else (
                    "#107C10" if severity.upper() in ["LOW", "INFO", "INFORMATION"] else "#252525"
                )
            )
            
            # Create basic HTML description with improved styling
            description_html = f"""
            <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 800px; margin: 0 auto;">
                <div style="background-color: #0078D4; color: white; padding: 15px; border-radius: 5px 5px 0 0;">
                    <h2 style="margin: 0; padding: 0;">AI Incident Analysis: {title}</h2>
                    <p style="margin: 5px 0 0 0; font-size: 14px;">ID: {incident_id}</p>
                </div>
                
                <div style="padding: 20px; border: 1px solid #E1DFDD; border-top: none; border-radius: 0 0 5px 5px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="background-color: {severity_bg_color}; color: {severity_color}; font-weight: bold; padding: 5px 10px; border-radius: 3px; margin-right: 10px;">
                            {severity}
                        </div>
                        <div>
                            <span style="color: #666; font-size: 14px;">Analysis started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <a href="{dashboard_url}" style="display: inline-block; background-color: #0078D4; color: white; text-decoration: none; padding: 8px 16px; border-radius: 4px; font-weight: bold;">
                            View in Incident Management Dashboard
                        </a>
                    </div>
                    
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <tr>
                            <th style="padding: 10px; text-align: left; background-color: #F3F2F1; border: 1px solid #E1DFDD; width: 150px;">Product</th>
                            <td style="padding: 10px; text-align: left; border: 1px solid #E1DFDD;">{product}</td>
                        </tr>
                        <tr>
                            <th style="padding: 10px; text-align: left; background-color: #F3F2F1; border: 1px solid #E1DFDD;">Region</th>
                            <td style="padding: 10px; text-align: left; border: 1px solid #E1DFDD;">{region}</td>
                        </tr>
                        <tr>
                            <th style="padding: 10px; text-align: left; background-color: #F3F2F1; border: 1px solid #E1DFDD;">Entities</th>
                            <td style="padding: 10px; text-align: left; border: 1px solid #E1DFDD;">
                                {entity_count} entities{f" (Primary: {', '.join(primary_entity_names[:3])}{' and more' if len(primary_entity_names) > 3 else ''})" if primary_entity_names else ""}
                            </td>
                        </tr>
                    </table>
            """
            
            # Add RCA information if available
            if has_rca:
                rca_details = serializable_state.get("rca_details", {})
                confidence_level = rca_details.get("confidence_level", "Unknown")
                secondary_factors = rca_details.get("secondary_factors", [])
                recommendations = rca_details.get("recommendations", [])
                
                description_html += f"""
                    <div style="margin-bottom: 20px;">
                        <h3 style="color: #D13438; margin-top: 0; padding-bottom: 5px; border-bottom: 1px solid #D13438;">Root Cause Analysis</h3>
                        <div style="background-color: #FDE7E9; padding: 15px; border-radius: 5px; margin-top: 10px;">
                            <p style="font-weight: bold; margin-bottom: 5px;">Primary Root Cause:</p>
                            <p style="padding: 10px; background-color: white; border-left: 4px solid #D13438; margin: 10px 0;">{root_cause}</p>
                            <p><strong>Confidence Level:</strong> {confidence_level}/10</p>
                        </div>
                        
                        {f'''
                        <div style="margin-top: 15px;">
                            <p style="font-weight: bold; margin-bottom: 5px;">Secondary Contributing Factors:</p>
                            <ul style="margin: 10px 0;">
                                {"".join(f"<li>{factor}</li>" for factor in secondary_factors[:3])}
                                {"<li>+ " + str(len(secondary_factors) - 3) + " more factors...</li>" if len(secondary_factors) > 3 else ""}
                            </ul>
                        </div>
                        ''' if secondary_factors else ''}
                        
                        {f'''
                        <div style="margin-top: 15px;">
                            <h3 style="color: #107C10; margin-top: 20px; padding-bottom: 5px; border-bottom: 1px solid #107C10;">Recommendations</h3>
                            <ul style="list-style-type: none; padding-left: 0;">
                                {"".join(f'<li style="margin-bottom: 10px; padding: 8px; background-color: #E7F4E4; border-left: 4px solid #107C10; border-radius: 3px;">✅ {recommendation}</li>' for recommendation in recommendations[:3])}
                                {"<li>+ " + str(len(recommendations) - 3) + " more recommendations...</li>" if len(recommendations) > 3 else ""}
                            </ul>
                        </div>
                        ''' if recommendations else ''}
                    </div>
                """
            else:
                description_html += f"""
                    <div style="background-color: #EFF6FC; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                        <h3 style="margin-top: 0;">Analysis in Progress</h3>
                        <p>AI-powered incident analysis is currently in progress. This ticket will be updated with detailed results when the analysis is complete.</p>
                        <p>The analysis will include:</p>
                        <ul>
                            <li>Root cause identification</li>
                            <li>Detailed entity analysis</li>
                            <li>Runbook execution results</li>
                            <li>Remediation recommendations</li>
                        </ul>
                    </div>
                """
                
            description_html += """
                    <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
                        <p>Generated by AI Incident Management System</p>
                    </div>
                </div>
            </div>
            """
            
            # Create the work item
            result = self.ado_client.create_workitem(
                workitem_type="task", 
                title=ticket_title,
                description=description_html,
                area_path=self.area_path
            )
            
            ticket_id = result.get("id")
            # Construct proper user-facing ticket URL
            ticket_url = f"{self.ticket_base_url}{ticket_id}"
            
            # Add tags
            tags = [
                "AI-Incident-Analysis", 
                f"Severity-{severity}", 
                f"Product-{product.replace(' ', '')}", 
                f"Region-{region.replace(' ', '')}"
            ]
            
            # Add RCA-related tags if available
            if has_rca:
                tags.append("RCA-Complete")
                
                # Add confidence level tag
                if rca_details.get("confidence_level"):
                    conf_level = int(rca_details.get("confidence_level", 0))
                    confidence_tag = "HighConfidence" if conf_level >= 8 else ("MediumConfidence" if conf_level >= 5 else "LowConfidence")
                    tags.append(confidence_tag)
            
            self.ado_client.set_tags(ticket_id, tags)
            
            logger.info(f"Created ADO ticket {ticket_id} for incident {incident_id}")
            
            return {
                "ticket_id": ticket_id,
                "ticket_url": ticket_url
            }
            
        except Exception as e:
            logger.error(f"Error creating ADO ticket: {str(e)}")
            return None
    
    def update_ticket_with_results(self, ticket_id: int, incident_state: Dict[str, Any]) -> bool:
        """
        Update an existing ticket with analysis results.
        
        Args:
            ticket_id: The ID of the ticket to update
            incident_state: The current incident state with analysis results
            
        Returns:
            Boolean indicating success or failure
        """
        if not self.ado_client:
            logger.error("ADO client not initialized")
            return False
        
        try:
            # Ensure all data is serializable
            serializable_state = self._serialize_state(incident_state)
            
            # Add comment with analysis results
            comment = self._format_analysis_results(serializable_state)
            self.ado_client.add_comment(ticket_id, comment)
            
            # Update ticket title and tags if we have a root cause
            if serializable_state.get("root_cause"):
                root_cause = serializable_state.get("root_cause", "")
                # Truncate the root cause for the title
                short_root_cause = root_cause[:50] + "..." if len(root_cause) > 50 else root_cause
                new_title = f"[RESOLVED] {serializable_state.get('title')} - {short_root_cause}"
                self.ado_client.update_workitem_title(ticket_id, new_title)
                
                # Add RCA-related tags
                tags = [
                    "AI-Incident-Analysis",
                    "RCA-Complete"
                ]
                
                # Add confidence level tag if available
                rca_details = serializable_state.get("rca_details", {})
                if rca_details.get("confidence_level"):
                    conf_level = int(rca_details.get("confidence_level", 0))
                    confidence_tag = "HighConfidence" if conf_level >= 8 else ("MediumConfidence" if conf_level >= 5 else "LowConfidence")
                    tags.append(confidence_tag)
                
                # Add severity tag if available
                severity = serializable_state.get("severity")
                if severity:
                    tags.append(f"Severity-{severity}")
                
                # Update tags
                self.ado_client.set_tags(ticket_id, tags)
            
            logger.info(f"Updated ADO ticket {ticket_id} with analysis results")
            return True
            
        except Exception as e:
            logger.error(f"Error updating ADO ticket: {str(e)}")
            return False
    
    def _format_analysis_results(self, incident_state: Dict[str, Any]) -> str:
        """
        Format incident analysis results as a structured HTML comment.
        
        Args:
            incident_state: The current incident state with analysis results
            
        Returns:
            HTML-formatted string with analysis results
        """
        # Extract relevant fields
        incident_id = incident_state.get("incident_id", "Unknown")
        title = incident_state.get("title", "Unknown Alert")
        severity = incident_state.get("severity", "Unknown")
        product = incident_state.get("product", "Unknown")
        region = incident_state.get("region", "Unknown")
        start_time = incident_state.get("start_time", "Unknown")
        root_cause = incident_state.get("root_cause", "Unknown")
        analysis_summary = incident_state.get("analysis_summary", "No summary available")
        
        # Create dashboard URL
        dashboard_url = f"{self.dashboard_base_url}{incident_id}"
        
        # Extract RCA details if available
        rca_details = incident_state.get("rca_details", {})
        confidence_level = rca_details.get("confidence_level", "Unknown")
        secondary_factors = rca_details.get("secondary_factors", [])
        prioritized_factors = rca_details.get("prioritized_factors", [])
        recommendations = rca_details.get("recommendations", [])
        evidence_summary = rca_details.get("evidence_summary", "No evidence summary available")
        timeline_reconstruction = rca_details.get("timeline_reconstruction", "No timeline available")
        affected_components = rca_details.get("affected_components", [])
        uncertainty_factors = rca_details.get("uncertainty_factors", [])
        
        # Format timestamps
        end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Get entities analyzed
        entities = incident_state.get("entities", [])
        entity_names = []
        if entities:
            for entity in entities:
                if isinstance(entity, dict):
                    entity_names.append(entity.get("entity_name", "Unknown"))
                else:
                    # Handle Pydantic model entities
                    entity_names.append(getattr(entity, "entity_name", "Unknown"))
        else:
            entity_names = ["No entities found"]
        
        # Get runbook results
        runbook_results = incident_state.get("runbook_results", [])
        runbook_steps = []
        collected_metrics = []
        collected_logs = []
        
        for step in runbook_results:
            step_dict = step if isinstance(step, dict) else (step.model_dump() if hasattr(step, "model_dump") else {})
            step_title = step_dict.get("step_title", "Unknown step")
            step_description = step_dict.get("step_description", "")
            step_summary = step_dict.get("summary", "No summary")
            issues_found = step_dict.get("issues_found", False)
            status_icon = "❌" if issues_found else "✅"
            
            # Process collected information
            collected_info = step_dict.get("collected_information", [])
            for info in collected_info:
                info_dict = info if isinstance(info, dict) else (info.model_dump() if hasattr(info, "model_dump") else {})
                content_type = info_dict.get("content_type", "information")
                
                if content_type == "metric":
                    collected_metrics.append(info_dict)
                elif content_type == "log":
                    collected_logs.append(info_dict)
            
            step_info = {
                "title": step_title,
                "description": step_description,
                "summary": step_summary,
                "status": status_icon,
                "issues_found": issues_found
            }
            runbook_steps.append(step_info)
        
        # HTML formatted content
        html = f"""
        <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
            <h2 style="color: #333; border-bottom: 2px solid #3498db; padding-bottom: 10px;">Incident Analysis Results</h2>
            
            <div style="margin-bottom: 20px; text-align: center;">
                <a href="{dashboard_url}" style="display: inline-block; background-color: #0078D4; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; font-weight: bold; margin: 10px 0;">
                    View in Incident Management Dashboard
                </a>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h3 style="color: #3498db;">Incident Details</h3>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                    <tr style="background-color: #f2f2f2;">
                        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Incident ID</th>
                        <td style="padding: 8px; text-align: left; border: 1px solid #ddd;">{incident_id}</td>
                    </tr>
                    <tr>
                        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Title</th>
                        <td style="padding: 8px; text-align: left; border: 1px solid #ddd;">{title}</td>
                    </tr>
                    <tr style="background-color: #f2f2f2;">
                        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Severity</th>
                        <td style="padding: 8px; text-align: left; border: 1px solid #ddd;">{severity}</td>
                    </tr>
                    <tr>
                        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Product</th>
                        <td style="padding: 8px; text-align: left; border: 1px solid #ddd;">{product}</td>
                    </tr>
                    <tr style="background-color: #f2f2f2;">
                        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Region</th>
                        <td style="padding: 8px; text-align: left; border: 1px solid #ddd;">{region}</td>
                    </tr>
                    <tr>
                        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Analysis Duration</th>
                        <td style="padding: 8px; text-align: left; border: 1px solid #ddd;">From {start_time} to {end_time}</td>
                    </tr>
                </table>
            </div>
            
            <div style="margin-bottom: 30px;">
                <h3 style="color: #e74c3c; border-bottom: 1px solid #e74c3c; padding-bottom: 5px;">Root Cause Analysis</h3>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <p style="font-weight: bold; margin-bottom: 5px;">Primary Root Cause:</p>
                    <p style="padding: 10px; background-color: #fff; border-left: 4px solid #e74c3c; margin: 10px 0;">{root_cause}</p>
                    <p style="font-weight: bold; margin-bottom: 5px;">Confidence Level: {confidence_level}/10</p>
                    
                    <p style="font-weight: bold; margin-bottom: 5px;">Evidence Summary:</p>
                    <p style="padding: 10px; background-color: #fff; border-left: 4px solid #3498db; margin: 10px 0;">{evidence_summary}</p>
                </div>
            """
        
        # Add affected components if available
        if affected_components:
            html += f"""
                <div style="margin-top: 15px;">
                    <p style="font-weight: bold; margin-bottom: 5px;">Affected Components:</p>
                    <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                        <tr style="background-color: #f2f2f2;">
                            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Component</th>
                            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Impact</th>
                        </tr>
                        {"".join(f'<tr><td style="padding: 8px; text-align: left; border: 1px solid #ddd;">{component.get("name", "Unknown")}</td><td style="padding: 8px; text-align: left; border: 1px solid #ddd;">{component.get("impact", "Unknown")}</td></tr>' for component in affected_components)}
                    </table>
                </div>
            """
            
        # Add secondary factors if available
        if secondary_factors:
            html += f"""
                <div style="margin-top: 15px;">
                    <p style="font-weight: bold; margin-bottom: 5px;">Secondary Contributing Factors:</p>
                    <ul style="margin: 10px 0; background-color: #fff; padding: 10px 30px; border-radius: 5px;">
                        {"".join(f"<li style='margin-bottom: 5px;'>{factor}</li>" for factor in secondary_factors)}
                    </ul>
                </div>
            """
            
        # Add prioritized factors if available
        if prioritized_factors:
            html += f"""
                <div style="margin-top: 15px;">
                    <p style="font-weight: bold; margin-bottom: 5px;">Prioritized Factors:</p>
                    <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                        <tr style="background-color: #f2f2f2;">
                            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Factor</th>
                            <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">Importance</th>
                            <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">Confidence</th>
                        </tr>
                        {"".join(f'<tr><td style="padding: 8px; text-align: left; border: 1px solid #ddd;">{factor.get("factor", "Unknown")}</td><td style="padding: 8px; text-align: center; border: 1px solid #ddd;">{factor.get("importance", "N/A")}/10</td><td style="padding: 8px; text-align: center; border: 1px solid #ddd;">{factor.get("confidence", "N/A")}/10</td></tr>' for factor in prioritized_factors)}
                    </table>
                </div>
            """
            
        # Add uncertainty factors if available
        if uncertainty_factors:
            html += f"""
                <div style="margin-top: 15px;">
                    <p style="font-weight: bold; margin-bottom: 5px;">Uncertainty Factors:</p>
                    <ul style="margin: 10px 0; background-color: #fff; padding: 10px 30px; border-radius: 5px;">
                        {"".join(f"<li style='margin-bottom: 5px;'>{factor}</li>" for factor in uncertainty_factors)}
                    </ul>
                </div>
            """
            
        # Add timeline reconstruction if available
        if timeline_reconstruction and timeline_reconstruction != "No timeline available":
            html += f"""
                <div style="margin-top: 15px;">
                    <p style="font-weight: bold; margin-bottom: 5px;">Timeline Reconstruction:</p>
                    <div style="padding: 15px; background-color: #fff; border-left: 4px solid #9b59b6; margin: 10px 0; white-space: pre-wrap; border-radius: 5px;">
                        {timeline_reconstruction}
                    </div>
                </div>
            """
            
        html += "</div>"
            
        # Add recommendations if available
        if recommendations:
            html += f"""
                <div style="margin-bottom: 30px;">
                    <h3 style="color: #27ae60; border-bottom: 1px solid #27ae60; padding-bottom: 5px; margin-top: 20px;">Recommendations</h3>
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                        <ul style="list-style-type: none; padding-left: 0;">
                            {"".join(f'<li style="margin-bottom: 10px; padding: 8px; background-color: #fff; border-left: 4px solid #27ae60; border-radius: 3px;">✅ {recommendation}</li>' for recommendation in recommendations)}
                        </ul>
                    </div>
                </div>
            """
            
        # Add entities analyzed
        html += f"""
            <div style="margin-bottom: 30px;">
                <h3 style="color: #3498db; border-bottom: 1px solid #3498db; padding-bottom: 5px;">Entities Analyzed</h3>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <ul style="list-style-type: disc; padding-left: 20px;">
                        {"".join(f"<li style='margin-bottom: 5px;'>{entity}</li>" for entity in entity_names)}
                    </ul>
                </div>
            </div>
        """
        
        # Add runbook results if available
        if runbook_steps:
            html += f"""
            <div style="margin-bottom: 30px;">
                <h3 style="color: #3498db; border-bottom: 1px solid #3498db; padding-bottom: 5px;">Runbook Steps Executed</h3>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <div style="margin-bottom: 15px;">
                        {"".join(f'''
                        <div style="margin-bottom: 15px; padding: 12px; background-color: {('#fff6f6' if step['issues_found'] else '#f6fff6')}; border-radius: 5px; border-left: 4px solid {('#e74c3c' if step['issues_found'] else '#27ae60')};">
                            <p style="font-weight: bold; margin-bottom: 5px;">{step['status']} {step['title']}</p>
                            {f'<p style="color: #666; margin-bottom: 8px; font-style: italic;">{step["description"]}</p>' if step['description'] else ''}
                            <p>{step['summary']}</p>
                        </div>
                        ''' for step in runbook_steps)}
                    </div>
                </div>
            </div>
            """
        
        # Add collected metrics summary if available
        if collected_metrics:
            html += f"""
            <div style="margin-bottom: 30px;">
                <h3 style="color: #3498db; border-bottom: 1px solid #3498db; padding-bottom: 5px;">Collected Metrics</h3>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <p>Collected {len(collected_metrics)} metrics during investigation</p>
                    <ul style="list-style-type: none; padding-left: 0;">
                        {"".join(f'<li style="margin-bottom: 8px; padding: 8px; background-color: #fff; border-radius: 3px;">📊 {metric.get("content_summary", "Unknown metric")}</li>' for metric in collected_metrics[:5])}
                        {f'<li style="margin-bottom: 8px; padding: 8px; background-color: #fff; border-radius: 3px;">...and {len(collected_metrics) - 5} more metrics</li>' if len(collected_metrics) > 5 else ''}
                    </ul>
                </div>
            </div>
            """
        
        # Add collected logs summary if available
        if collected_logs:
            html += f"""
            <div style="margin-bottom: 30px;">
                <h3 style="color: #3498db; border-bottom: 1px solid #3498db; padding-bottom: 5px;">Collected Logs</h3>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <p>Collected {len(collected_logs)} log entries during investigation</p>
                    <ul style="list-style-type: none; padding-left: 0;">
                        {"".join(f'<li style="margin-bottom: 8px; padding: 8px; background-color: #fff; border-radius: 3px;">📜 {log.get("content_summary", "Unknown log entry")}</li>' for log in collected_logs[:5])}
                        {f'<li style="margin-bottom: 8px; padding: 8px; background-color: #fff; border-radius: 3px;">...and {len(collected_logs) - 5} more log entries</li>' if len(collected_logs) > 5 else ''}
                    </ul>
                </div>
            </div>
            """
        
        # Add analysis summary
        html += f"""
            <div style="margin-bottom: 30px;">
                <h3 style="color: #3498db; border-bottom: 1px solid #3498db; padding-bottom: 5px;">Analysis Summary</h3>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <div style="padding: 15px; background-color: #fff; border-left: 4px solid #3498db; margin: 10px 0; white-space: pre-wrap; border-radius: 5px;">
                        {analysis_summary}
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                <p style="color: #777; margin-bottom: 5px;">Generated by AI Incident Management System</p>
                <p style="color: #999; font-size: 12px;">Analysis completed at {end_time}</p>
            </div>
        </div>
        """
        
        return html


# Singleton instance
_ado_service = None

def get_ado_service() -> ADOService:
    """Get the singleton instance of the ADO service."""
    global _ado_service
    if _ado_service is None:
        _ado_service = ADOService()
    return _ado_service 