"""
Entity Type Service - Service for managing entity types.

This service provides a unified interface for working with entity types,
whether they are stored in the database or loaded from YAML files.
"""

import os
import yaml
from loguru import logger
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from ai_incident_manager.database.postgres import PostgresDB

# Set up logging
logger = logger.bind(name="entity_type_service")

class EntityTypeService:
    """
    Service for managing entity types.
    
    This service provides a unified interface for working with entity types,
    whether they are stored in the database or loaded from YAML files.
    It also provides caching for performance.
    """
    
    def __init__(self, config_path: str = "ai_incident_manager/config/entity_types.yaml", refresh_cache_interval: int = 300):
        """
        Initialize the entity type service.
        
        Args:
            config_path: Path to the YAML configuration file (default: "ai_incident_manager/config/entity_types.yaml")
            refresh_cache_interval: How often to refresh the cache in seconds (default: 5 minutes)
        """
        self.db = PostgresDB()
        self.config_path = config_path
        self.refresh_cache_interval = refresh_cache_interval
        self.last_cache_refresh = datetime.now() - timedelta(seconds=refresh_cache_interval + 1)
        self._cached_entity_types = {}
        
        # Initial cache load
        self._refresh_cache()
    
    def _load_yaml_entity_types(self) -> List[Dict[str, Any]]:
        """
        Load entity types from the YAML file.
        
        Returns:
            List of entity type dictionaries
        """
        try:
            if not os.path.exists(self.config_path):
                logger.warning(f"Config file not found: {self.config_path}")
                return []
                
            with open(self.config_path, 'r') as file:
                config = yaml.safe_load(file)
                
            # Check which format we're using
            if config and 'entity_types' in config:
                entity_types = config['entity_types']
                logger.info(f"Loaded {len(entity_types)} entity types from config")
                return entity_types
            else:
                logger.warning(f"No entity types found in config file: {self.config_path}")
                return []
                
        except Exception as e:
            logger.error(f"Error loading entity types from YAML: {str(e)}")
            return []
    
    def _refresh_cache(self) -> None:
        """Refresh the entity type cache from database and YAML."""
        now = datetime.now()
        
        # Only refresh if the cache interval has elapsed
        if (now - self.last_cache_refresh).total_seconds() < self.refresh_cache_interval:
            return
            
        logger.debug("Refreshing entity type cache")
        
        # Reset cache
        self._cached_entity_types = {}
        
        # Load entity types from database
        try:
            with self.db.get_connection() as conn:
                with conn.cursor() as cur:
                    # Check if the entity_types table exists
                    cur.execute("""
                    SELECT EXISTS (
                       SELECT FROM information_schema.tables 
                       WHERE table_schema = 'public' 
                       AND table_name = 'entity_types'
                    );
                    """)
                    
                    table_exists = cur.fetchone()[0]
                    
                    if table_exists:
                        # Get all entity types with their details
                        cur.execute("""
                        SELECT id, type_name, description, importance 
                        FROM entity_types
                        """)
                        
                        for entity_id, type_name, description, importance in cur.fetchall():
                            self._cached_entity_types[type_name] = {
                                "id": entity_id,
                                "type": type_name,
                                "description": description,
                                "importance": importance,
                                "metrics": [],
                                "logs": [],
                                "related_entities": [],
                                "source": "database"
                            }
                        
                        # Load metrics for each entity type
                        for type_name, entity_type in self._cached_entity_types.items():
                            entity_id = entity_type["id"]
                            
                            # Get metrics
                            cur.execute("""
                            SELECT metric_name, description, importance
                            FROM entity_type_metrics
                            WHERE entity_type_id = %s
                            ORDER BY importance DESC
                            """, (entity_id,))
                            
                            for metric_name, description, importance in cur.fetchall():
                                entity_type["metrics"].append({
                                    "name": metric_name,
                                    "description": description,
                                    "importance": importance
                                })
                            
                            # Get logs
                            cur.execute("""
                            SELECT log_type, importance
                            FROM entity_type_logs
                            WHERE entity_type_id = %s
                            ORDER BY importance DESC
                            """, (entity_id,))
                            
                            for log_type, importance in cur.fetchall():
                                entity_type["logs"].append({
                                    "type": log_type,
                                    "importance": importance
                                })
                            
                            # Get related entities
                            cur.execute("""
                            SELECT related_type, relationship, importance
                            FROM entity_type_relationships
                            WHERE entity_type_id = %s
                            ORDER BY importance DESC
                            """, (entity_id,))
                            
                            for related_type, relationship, importance in cur.fetchall():
                                entity_type["related_entities"].append({
                                    "type": related_type,
                                    "relationship": relationship,
                                    "importance": importance
                                })
                    else:
                        logger.info("Entity types table does not exist in database. Using only YAML configuration.")
                        
        except Exception as e:
            logger.error(f"Error loading entity types from database: {str(e)}")
        
        # Load entity types from YAML
        yaml_entity_types = self._load_yaml_entity_types()
        
        # Add entity types from YAML that don't exist in database
        for entity_type in yaml_entity_types:
            type_name = entity_type.get("type")
            if type_name and type_name not in self._cached_entity_types:
                entity_type["source"] = "yaml"
                self._cached_entity_types[type_name] = entity_type
                
        # Update timestamp
        self.last_cache_refresh = now
    
    def get_all_entity_types(self) -> List[Dict[str, Any]]:
        """
        Get all entity types.
        
        Returns:
            List of entity type dictionaries
        """
        self._refresh_cache()
        return list(self._cached_entity_types.values())
    
    def get_entity_type(self, type_name: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific entity type by name.
        
        Args:
            type_name: The name of the entity type
            
        Returns:
            Entity type dictionary or None if not found
        """
        self._refresh_cache()
        return self._cached_entity_types.get(type_name)
    
    def get_entity_type_for_entity(self, entity_metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Determine the best entity type for an entity based on its metadata.
        
        Args:
            entity_metadata: Metadata for the entity
            
        Returns:
            Entity type dictionary or None if not found
        """
        self._refresh_cache()
        
        # Try to find an exact match based on entity type from New Relic
        nr_entity_type = entity_metadata.get("type", "").upper()
        if nr_entity_type in self._cached_entity_types:
            return self._cached_entity_types[nr_entity_type]
            
        # Map New Relic entity types to our entity types
        nr_to_our_type_map = {
            "APM_APPLICATION_ENTITY": "APPLICATION",
            "KUBERNETES_CONTAINER_ENTITY": "CONTAINER",
            "KUBERNETES_CLUSTER_ENTITY": "KUBERNETES_CLUSTER",
            "KUBERNETES_NODE_ENTITY": "KUBERNETES_NODE",
            "KUBERNETES_POD_ENTITY": "KUBERNETES_POD",
            "KUBERNETES_DEPLOYMENT_ENTITY": "KUBERNETES_DEPLOYMENT",
            "DATABASE_ENTITY": "DATABASE",
            "EXTERNAL_SERVICE_ENTITY": "EXTERNAL_SERVICE"
        }
        
        if nr_entity_type in nr_to_our_type_map:
            mapped_type = nr_to_our_type_map[nr_entity_type]
            if mapped_type in self._cached_entity_types:
                return self._cached_entity_types[mapped_type]
        
        # If no match, return None
        return None
    
    def create_entity_type_tables(self) -> None:
        """Create database tables for storing entity types if they don't exist."""
        with self.db.get_connection() as conn:
            with conn.cursor() as cur:
                # Create entity types table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_types (
                    id SERIAL PRIMARY KEY,
                    type_name VARCHAR(100) UNIQUE,
                    description TEXT,
                    importance INTEGER DEFAULT 5,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                """)
                
                # Create metrics table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_type_metrics (
                    id SERIAL PRIMARY KEY,
                    entity_type_id INTEGER REFERENCES entity_types(id) ON DELETE CASCADE,
                    metric_name VARCHAR(100),
                    description TEXT,
                    importance INTEGER DEFAULT 5,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(entity_type_id, metric_name)
                );
                """)
                
                # Create logs table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_type_logs (
                    id SERIAL PRIMARY KEY,
                    entity_type_id INTEGER REFERENCES entity_types(id) ON DELETE CASCADE,
                    log_type VARCHAR(100),
                    importance INTEGER DEFAULT 5,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(entity_type_id, log_type)
                );
                """)
                
                # Create relationships table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_type_relationships (
                    id SERIAL PRIMARY KEY,
                    entity_type_id INTEGER REFERENCES entity_types(id) ON DELETE CASCADE,
                    related_type VARCHAR(100),
                    relationship VARCHAR(100),
                    importance INTEGER DEFAULT 5,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(entity_type_id, related_type, relationship)
                );
                """)
                
                # Create entity instance table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_instances (
                    id SERIAL PRIMARY KEY,
                    entity_guid VARCHAR(255) UNIQUE,
                    entity_name VARCHAR(255),
                    entity_type_id INTEGER REFERENCES entity_types(id),
                    cluster_id VARCHAR(100),
                    product VARCHAR(100),
                    region VARCHAR(50),
                    landscape VARCHAR(50),
                    metadata JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                """)
                
                # Create entity metrics table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_instance_metrics (
                    id SERIAL PRIMARY KEY,
                    entity_instance_id INTEGER REFERENCES entity_instances(id) ON DELETE CASCADE,
                    metric_name VARCHAR(100),
                    metric_data JSONB,
                    collected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                """)
                
                # Create entity logs table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_instance_logs (
                    id SERIAL PRIMARY KEY,
                    entity_instance_id INTEGER REFERENCES entity_instances(id) ON DELETE CASCADE,
                    log_type VARCHAR(100),
                    log_data JSONB,
                    collected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                """)
                
                # Create entity relationships table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS entity_instance_relationships (
                    id SERIAL PRIMARY KEY,
                    source_entity_id INTEGER REFERENCES entity_instances(id) ON DELETE CASCADE,
                    target_entity_id INTEGER REFERENCES entity_instances(id) ON DELETE CASCADE,
                    relationship VARCHAR(100),
                    metadata JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(source_entity_id, target_entity_id, relationship)
                );
                """)
                
                # Create incident entities table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS incident_entities (
                    id SERIAL PRIMARY KEY,
                    incident_id VARCHAR(255) REFERENCES incidents(id) ON DELETE CASCADE,
                    entity_instance_id INTEGER REFERENCES entity_instances(id) ON DELETE CASCADE,
                    is_primary BOOLEAN DEFAULT FALSE,
                    analysis_data JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(incident_id, entity_instance_id)
                );
                """)
    
    def sync_yaml_to_database(self) -> None:
        """Synchronize entity types from YAML to the database."""
        # Load entity types from YAML
        yaml_entity_types = self._load_yaml_entity_types()
        
        if not yaml_entity_types:
            logger.warning("No entity types found in YAML. Nothing to sync.")
            return
        
        # Create tables if they don't exist
        self.create_entity_type_tables()
        
        with self.db.get_connection() as conn:
            with conn.cursor() as cur:
                for entity_type in yaml_entity_types:
                    type_name = entity_type.get("type")
                    description = entity_type.get("description", "")
                    importance = entity_type.get("importance", 5)
                    
                    # Insert or update entity type
                    cur.execute("""
                    INSERT INTO entity_types (type_name, description, importance)
                    VALUES (%s, %s, %s)
                    ON CONFLICT (type_name) DO UPDATE
                    SET description = EXCLUDED.description,
                        importance = EXCLUDED.importance,
                        updated_at = NOW()
                    RETURNING id;
                    """, (type_name, description, importance))
                    
                    entity_type_id = cur.fetchone()[0]
                    
                    # Insert metrics
                    for metric in entity_type.get("metrics", []):
                        metric_name = metric.get("name")
                        metric_desc = metric.get("description", "")
                        metric_importance = metric.get("importance", 5)
                        
                        cur.execute("""
                        INSERT INTO entity_type_metrics (entity_type_id, metric_name, description, importance)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT (entity_type_id, metric_name) DO UPDATE
                        SET description = EXCLUDED.description,
                            importance = EXCLUDED.importance;
                        """, (entity_type_id, metric_name, metric_desc, metric_importance))
                    
                    # Insert logs
                    for log in entity_type.get("logs", []):
                        log_type = log.get("type")
                        log_importance = log.get("importance", 5)
                        
                        cur.execute("""
                        INSERT INTO entity_type_logs (entity_type_id, log_type, importance)
                        VALUES (%s, %s, %s)
                        ON CONFLICT (entity_type_id, log_type) DO UPDATE
                        SET importance = EXCLUDED.importance;
                        """, (entity_type_id, log_type, log_importance))
                    
                    # Insert relationships
                    for rel in entity_type.get("related_entities", []):
                        related_type = rel.get("type")
                        relationship = rel.get("relationship")
                        rel_importance = rel.get("importance", 5)
                        
                        cur.execute("""
                        INSERT INTO entity_type_relationships (entity_type_id, related_type, relationship, importance)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT (entity_type_id, related_type, relationship) DO UPDATE
                        SET importance = EXCLUDED.importance;
                        """, (entity_type_id, related_type, relationship, rel_importance))
        
        logger.info(f"Synchronized {len(yaml_entity_types)} entity types from YAML to database")
        
        # Reset cache after sync
        self.last_cache_refresh = datetime.now() - timedelta(seconds=self.refresh_cache_interval + 1)
        self._cached_entity_types = {}

# Singleton pattern
_entity_type_service = None

def get_entity_type_service() -> EntityTypeService:
    """
    Get the singleton instance of the EntityTypeService.
    
    Returns:
        EntityTypeService instance
    """
    global _entity_type_service
    if _entity_type_service is None:
        _entity_type_service = EntityTypeService()
    return _entity_type_service
