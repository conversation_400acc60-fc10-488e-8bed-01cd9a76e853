"""
Microsoft Teams Service for incident notifications.

This service provides methods for sending notifications to Microsoft Teams
channels using Adaptive Cards for rich formatting.
"""

import os
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
import requests
from loguru import logger
import dotenv

# Load environment variables
dotenv.load_dotenv(override=True)

# Configure logging
logger = logger.bind(name="teams_service")

class TeamsService:
    """
    Service for sending notifications to Microsoft Teams.
    
    This service provides methods to send adaptive cards to Teams
    webhooks for incident alerts and analysis results.
    """
    
    def __init__(self):
        """
        Initialize the Teams service with webhook URLs from environment variables.
        """
        self.webhook_url = os.environ.get("TEAMS_WEBHOOK_URL")
        self.dashboard_base_url = os.environ.get("IIM_DASHBOARD_BASE_URL", "https://obv-ai-compute.ivantiai.com:8080/incident/")
        
        if not self.webhook_url:
            logger.warning("TEAMS_WEBHOOK_URL not set in environment variables")
    
    def _serialize_state(self, state_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert Pydantic models and nested objects to plain dictionaries for serialization.
        
        Args:
            state_data: The state data to serialize
            
        Returns:
            Dictionary with all data converted to serializable formats
        """
        serializable_state = {}
        for key, value in state_data.items():
            if hasattr(value, "model_dump"):
                # Handle Pydantic v2 models
                serializable_state[key] = value.model_dump()
            elif hasattr(value, "dict"):
                # Handle Pydantic v1 models
                serializable_state[key] = value.dict()
            elif isinstance(value, list):
                # Handle lists of Pydantic models
                serializable_list = []
                for item in value:
                    if hasattr(item, "model_dump"):
                        serializable_list.append(item.model_dump())
                    elif hasattr(item, "dict"):
                        serializable_list.append(item.dict())
                    else:
                        serializable_list.append(item)
                serializable_state[key] = serializable_list
            else:
                serializable_state[key] = value
        return serializable_state
    
    def send_incident_notification(self, incident_state: Dict[str, Any]) -> bool:
        """
        Send a notification to Teams when incident analysis completes.
        
        Args:
            incident_state: The current incident state with analysis results
            
        Returns:
            Boolean indicating success or failure
        """
        if not self.webhook_url:
            logger.error("Teams webhook URL not configured")
            return False
        
        try:
            # Convert any Pydantic models to plain dictionaries
            serializable_state = self._serialize_state(incident_state)
            
            # Create adaptive card payload
            card_payload = self._create_incident_card(serializable_state)
            
            # Send the card to Teams
            response = requests.post(
                self.webhook_url,
                json=card_payload,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"Sent Teams notification for incident {serializable_state.get('incident_id', 'Unknown')}")
                return True
            else:
                logger.error(f"Failed to send Teams notification: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending Teams notification: {str(e)}")
            return False
    
    def _create_incident_card(self, incident_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an adaptive card for incident results notification.
        
        Args:
            incident_state: The current incident state with analysis results
            
        Returns:
            Dictionary with adaptive card content
        """
        # Extract relevant fields
        incident_id = incident_state.get("incident_id", "Unknown")
        title = incident_state.get("title", "Unknown Alert")
        severity = incident_state.get("severity", "Unknown")
        product = incident_state.get("product", "Unknown")
        region = incident_state.get("region", "Unknown")
        start_time = incident_state.get("start_time", "Unknown")
        root_cause = incident_state.get("root_cause", "Unknown")
        
        # Create dashboard URL
        dashboard_url = f"{self.dashboard_base_url}{incident_id}"
        
        # Extract RCA details if available
        rca_details = incident_state.get("rca_details", {})
        confidence_level = rca_details.get("confidence_level", "Unknown")
        secondary_factors = rca_details.get("secondary_factors", [])
        recommendations = rca_details.get("recommendations", [])
        evidence_summary = rca_details.get("evidence_summary", "")
        affected_components = rca_details.get("affected_components", [])
        prioritized_factors = rca_details.get("prioritized_factors", [])
        
        # Format timestamps
        end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Get ado ticket info if available
        ado_ticket_id = incident_state.get("ado_ticket_id")
        ado_ticket_url = incident_state.get("ado_ticket_url")
        
        # Get severity color
        severity_color = self._get_severity_color(severity)
        severity_bg_color = self._get_severity_bg_color(severity)
        
        # Get entities for display
        entities = incident_state.get("entities", [])
        entity_count = len(entities)
        primary_entities = []
        
        # Safely extract primary entities, handling both dict and Pydantic model formats
        for entity in entities:
            is_primary = False
            if isinstance(entity, dict):
                is_primary = entity.get("is_primary", False)
            else:
                is_primary = getattr(entity, "is_primary", False)
                
            if is_primary:
                primary_entities.append(entity)
        
        # Safely extract entity names
        primary_entity_names = []
        for entity in primary_entities:
            if isinstance(entity, dict):
                entity_name = entity.get("entity_name", "Unknown")
            else:
                entity_name = getattr(entity, "entity_name", "Unknown")
            primary_entity_names.append(entity_name)
        
        # Get runbook results
        runbook_results = incident_state.get("runbook_results", [])
        issues_found = False
        runbook_items = []
        
        for step in runbook_results[:3]:  # Only show first 3 steps in the card
            step_dict = step if isinstance(step, dict) else (step.model_dump() if hasattr(step, "model_dump") else {})
            step_title = step_dict.get("step_title", "Unknown step")
            step_issues = step_dict.get("issues_found", False)
            issues_found = issues_found or step_issues
            
            runbook_items.append({
                "title": step_title,
                "has_issues": step_issues
            })
        
        # Determine action buttons
        actions = []
        
        # Add dashboard link
        actions.append({
            "type": "Action.OpenUrl",
            "title": "View In Dashboard",
            "url": dashboard_url
        })
        
        # Add ticket link if available
        if ado_ticket_url:
            actions.append({
                "type": "Action.OpenUrl",
                "title": f"View Ticket #{ado_ticket_id}",
                "url": ado_ticket_url
            })
        
        # Add timestamp information 
        timestamp_text = f"Analysis: {start_time} to {end_time}"
        
        # Create header container
        header_container = {
            "type": "Container",
            "style": "emphasis",
            "bleed": True,
            "items": [
                {
                    "type": "ColumnSet",
                    "columns": [
                        {
                            "type": "Column",
                            "width": "stretch",
                            "items": [
                                {
                                    "type": "TextBlock",
                                    "text": "Incident Analysis Results",
                                    "weight": "bolder",
                                    "size": "large",
                                    "wrap": True
                                }
                            ]
                        },
                        {
                            "type": "Column",
                            "width": "auto",
                            "items": [
                                {
                                    "type": "TextBlock",
                                    "text": severity,
                                    "color": severity_color,
                                    "weight": "bolder",
                                    "horizontalAlignment": "right"
                                }
                            ]
                        }
                    ]
                },
                {
                    "type": "TextBlock",
                    "text": title,
                    "wrap": True,
                    "size": "medium",
                    "weight": "bolder"
                },
                {
                    "type": "TextBlock",
                    "text": timestamp_text,
                    "wrap": True,
                    "isSubtle": True,
                    "size": "small",
                    "spacing": "none"
                }
            ]
        }
        
        # Create facts section
        facts_container = {
            "type": "FactSet",
            "facts": [
                {"title": "Incident ID", "value": incident_id},
                {"title": "Product", "value": product},
                {"title": "Region", "value": region},
                {"title": "Entities Affected", "value": f"{entity_count} ({', '.join(primary_entity_names[:2])}{' and more' if len(primary_entity_names) > 2 else ''})"},
                {"title": "RCA Confidence", "value": f"{confidence_level}/10"}
            ]
        }
        
        # Create root cause container (primary focus)
        root_cause_container = {
            "type": "Container",
            "style": "warning",
            "items": [
                {
                    "type": "TextBlock",
                    "text": "Root Cause",
                    "weight": "bolder"
                },
                {
                    "type": "TextBlock",
                    "text": root_cause,
                    "wrap": True
                }
            ]
        }
        
        # Build the body items
        body_items = [
            header_container,
            {
                "type": "TextBlock",
                "text": "Incident Details",
                "weight": "bolder",
                "size": "medium",
                "spacing": "medium"
            },
            facts_container,
            {
                "type": "TextBlock",
                "text": "Analysis Results",
                "weight": "bolder",
                "size": "medium",
                "spacing": "medium"
            },
            root_cause_container
        ]
        
        # Add secondary factors if available (limited to top 3)
        if secondary_factors:
            secondary_items = [
                {
                    "type": "TextBlock",
                    "text": "Contributing Factors",
                    "weight": "bolder"
                }
            ]
            
            for i, factor in enumerate(secondary_factors[:3]):
                secondary_items.append({
                    "type": "TextBlock",
                    "text": f"• {factor}",
                    "wrap": True
                })
                
            if len(secondary_factors) > 3:
                secondary_items.append({
                    "type": "TextBlock",
                    "text": f"+ {len(secondary_factors) - 3} more factors...",
                    "isSubtle": True,
                    "wrap": True
                })
                
            body_items.append({
                "type": "Container",
                "items": secondary_items,
                "spacing": "medium"
            })
        
        # Add prioritized factors visual if available (top 3 only)
        if prioritized_factors:
            prioritized_items = [
                {
                    "type": "TextBlock",
                    "text": "Key Contributing Factors",
                    "weight": "bolder"
                }
            ]
            
            # Create a grid of factor visualization
            for i, factor in enumerate(prioritized_factors[:3]):
                factor_name = factor.get("factor", "Unknown factor")
                importance = int(factor.get("importance", 5))
                confidence = int(factor.get("confidence", 5))
                
                # Create a visual indicator of importance
                importance_indicator = "🔴" if importance >= 8 else ("🟠" if importance >= 5 else "🟡")
                
                prioritized_items.append({
                    "type": "ColumnSet",
                    "columns": [
                        {
                            "type": "Column",
                            "width": "stretch",
                            "items": [
                                {
                                    "type": "TextBlock",
                                    "text": f"{importance_indicator} {factor_name}",
                                    "wrap": True
                                }
                            ]
                        },
                        {
                            "type": "Column",
                            "width": "auto",
                            "items": [
                                {
                                    "type": "TextBlock",
                                    "text": f"Imp: {importance}/10",
                                    "isSubtle": True
                                }
                            ]
                        }
                    ]
                })
                
            body_items.append({
                "type": "Container",
                "items": prioritized_items,
                "spacing": "medium",
                "style": "default"
            })
        
        # Add affected components if available
        if affected_components:
            component_items = [
                {
                    "type": "TextBlock",
                    "text": "Affected Components",
                    "weight": "bolder"
                }
            ]
            
            for component in affected_components[:3]:
                component_name = component.get("name", "Unknown")
                component_impact = component.get("impact", "Unknown")
                component_items.append({
                    "type": "TextBlock",
                    "text": f"• {component_name}: {component_impact}",
                    "wrap": True
                })
                
            if len(affected_components) > 3:
                component_items.append({
                    "type": "TextBlock",
                    "text": f"+ {len(affected_components) - 3} more components...",
                    "isSubtle": True,
                    "wrap": True
                })
                
            body_items.append({
                "type": "Container",
                "items": component_items,
                "spacing": "medium"
            })
            
        # Add runbook execution results
        if runbook_items:
            runbook_container_items = [
                {
                    "type": "TextBlock",
                    "text": "Runbook Steps Executed",
                    "weight": "bolder"
                }
            ]
            
            for item in runbook_items:
                status_icon = "❌" if item["has_issues"] else "✅"
                runbook_container_items.append({
                    "type": "TextBlock",
                    "text": f"{status_icon} {item['title']}",
                    "wrap": True,
                    "color": "attention" if item["has_issues"] else "good"
                })
                
            if len(runbook_results) > 3:
                runbook_container_items.append({
                    "type": "TextBlock",
                    "text": f"+ {len(runbook_results) - 3} more steps...",
                    "isSubtle": True,
                    "wrap": True
                })
                
            body_items.append({
                "type": "Container",
                "items": runbook_container_items,
                "spacing": "medium",
                "style": "default"
            })
            
        # Add recommendations if available
        if recommendations:
            recommendation_items = [
                {
                    "type": "TextBlock",
                    "text": "Recommendations",
                    "weight": "bolder"
                }
            ]
            
            for i, recommendation in enumerate(recommendations[:3]):
                recommendation_items.append({
                    "type": "TextBlock",
                    "text": f"✅ {recommendation}",
                    "wrap": True,
                    "color": "good"
                })
                
            if len(recommendations) > 3:
                recommendation_items.append({
                    "type": "TextBlock",
                    "text": f"+ {len(recommendations) - 3} more recommendations...",
                    "isSubtle": True,
                    "wrap": True
                })
                
            body_items.append({
                "type": "Container",
                "items": recommendation_items,
                "spacing": "medium",
                "style": "good"
            })
            
        # Create the card structure
        card = {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
                        "version": "1.5",
                        "msteams": {
                            "width": "Full"
                        },
                        "body": body_items,
                        "actions": actions
                    }
                }
            ]
        }
        
        return card
    
    def _get_severity_color(self, severity: str) -> str:
        """
        Get the color code for severity level.
        
        Args:
            severity: Severity string
            
        Returns:
            Color string for the adaptive card
        """
        severity = severity.upper()
        if severity in ["CRITICAL", "FATAL"]:
            return "attention"
        elif severity == "WARNING":
            return "warning"
        elif severity in ["LOW", "INFO", "INFORMATION"]:
            return "good"
        else:
            return "default"
            
    def _get_severity_bg_color(self, severity: str) -> str:
        """
        Get the background color code for severity level.
        
        Args:
            severity: Severity string
            
        Returns:
            Background color string for the adaptive card
        """
        severity = severity.upper()
        if severity in ["CRITICAL", "FATAL"]:
            return "#FDE7E9"  # Light red
        elif severity == "WARNING":
            return "#FEF5E7"  # Light yellow
        elif severity in ["LOW", "INFO", "INFORMATION"]:
            return "#E7F4E4"  # Light green
        else:
            return "#F3F2F1"  # Light gray


# Singleton instance
_teams_service = None

def get_teams_service() -> TeamsService:
    """Get the singleton instance of the Teams service."""
    global _teams_service
    if _teams_service is None:
        _teams_service = TeamsService()
    return _teams_service 