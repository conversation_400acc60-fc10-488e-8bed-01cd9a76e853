"""
Entity Relationship Service.

Refactored service that manages relationships between different entities
based on a unified configuration. This service is designed to work with
the EntityRelationshipAgent.
"""

import os
import re
import yaml
from loguru import logger
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta

# Set up logging
logger = logger.bind(name="entity_relationship_service")


class EntityRelationshipService:
    """
    Service for managing entity relationships.

    This service loads relationship definitions from a unified config file
    and provides methods to lookup relationships, build queries, and traverse
    relationship paths.
    """

    def __init__(self, config_path: str = "config/entity_relationships.yaml"):
        """
        Initialize the entity relationship service.

        Args:
            config_path: Path to the YAML configuration file
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.query_client = None

    def _load_config(self) -> Dict[str, Any]:
        """
        Load relationship configuration from YAML file.

        Returns:
            Parsed configuration dictionary
        """
        try:
            if not os.path.exists(self.config_path):
                logger.warning(f"Entity relationships config file not found at {self.config_path}")
                return {
                    "entity_types": {},
                    "alert_categories": {},
                    "alert_patterns": [],
                    "metadata_fields": {},
                    "aliases": {},
                }

            with open(self.config_path, "r") as f:
                config = yaml.safe_load(f)
                logger.info(f"Loaded entity relationships config from {self.config_path}")

                # Ensure all necessary sections exist
                if "entity_types" not in config:
                    config["entity_types"] = {}
                if "alert_categories" not in config:
                    config["alert_categories"] = {}
                if "alert_patterns" not in config:
                    config["alert_patterns"] = []
                if "metadata_fields" not in config:
                    config["metadata_fields"] = {}
                if "aliases" not in config:
                    config["aliases"] = {}

                return config

        except Exception as e:
            logger.error(f"Error loading entity relationships config: {str(e)}")
            return {
                "entity_types": {},
                "alert_categories": {},
                "alert_patterns": [],
                "metadata_fields": {},
                "aliases": {},
            }

    def normalize_entity_type(self, entity_type: str) -> str:
        """
        Normalize an entity type using the alias mapping from config.

        Args:
            entity_type: The entity type to normalize

        Returns:
            Normalized entity type
        """
        aliases = self.config.get("aliases", {})
        return aliases.get(entity_type, entity_type)

    def get_entity_type_config(self, entity_type: str) -> Dict[str, Any]:
        """
        Get the configuration for an entity type.

        Args:
            entity_type: The entity type to look up

        Returns:
            Entity type configuration or empty dict if not found
        """
        # Normalize the entity type
        normalized_type = self.normalize_entity_type(entity_type)

        # Look up in the entity types configuration
        entity_types = self.config.get("entity_types", {})
        return entity_types.get(normalized_type, {})

    def get_entity_relationships(self, entity_type: str) -> List[Dict[str, Any]]:
        """
        Get relationship definitions for an entity type.

        Args:
            entity_type: The entity type to get relationships for

        Returns:
            List of relationship definitions
        """
        entity_config = self.get_entity_type_config(entity_type)
        return entity_config.get("relationships", [])

    def get_metadata_fields(self, entity_type: str) -> List[str]:
        """
        Get the metadata fields to extract for a specific entity type.

        Args:
            entity_type: The entity type

        Returns:
            List of metadata field names
        """
        # Check in the entity_types section first
        entity_config = self.get_entity_type_config(entity_type)
        if "metadata" in entity_config:
            return entity_config.get("metadata", [])

        # Fall back to metadata_fields section for backward compatibility
        entity_type = self.normalize_entity_type(entity_type)
        return self.config.get("metadata_fields", {}).get(entity_type, [])

    def get_alert_category_config(self, alert_category: str) -> Dict[str, Any]:
        """
        Get the configuration for an alert category.

        Args:
            alert_category: The alert category to look up

        Returns:
            Alert category configuration or empty dict if not found
        """
        alert_categories = self.config.get("alert_categories", {})
        return alert_categories.get(alert_category, {})

    def build_query(
        self,
        relationship_config: Dict[str, Any],
        entity_name: str,
        entity_guid: str,
        cluster_name: Optional[str] = None,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        additional_params: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Build a NRQL query from a relationship configuration.

        Args:
            relationship_config: Relationship configuration
            entity_name: Name of the source entity
            cluster_name: Optional cluster name for Kubernetes entities
            since_time_ms: Start time in epoch milliseconds
            until_time_ms: End time in epoch milliseconds
            additional_params: Additional parameters for query formatting

        Returns:
            Formatted NRQL query or empty string
        """
        # Check if we have a query in the config
        query_template = relationship_config.get("query")
        if not query_template:
            return ""

        # Prepare parameters for query formatting
        params = {"entity_name": entity_name, "entity_guid": entity_guid}

        # Set time range parameters
        if since_time_ms is not None:
            params["since_time_ms"] = since_time_ms
        else:
            # Default to last hour
            params["since_time_ms"] = "1 hour ago"

        if until_time_ms is not None:
            params["until_time_ms"] = until_time_ms
        else:
            # Default to now
            params["until_time_ms"] = "now"

        if cluster_name:
            params["cluster_name"] = cluster_name

        if additional_params:
            params.update(additional_params)

        # Format the query with parameters
        try:
            query = query_template.format(**params)
            return query
        except KeyError as e:
            logger.error(f"Missing parameter for relationship query: {e}")
            return ""
        except Exception as e:
            logger.error(f"Error formatting query: {str(e)}")
            return ""

    async def traverse_relationships_with_validation(
        self,
        primary_entity_guid: str,
        entity_type: str,
        entity_name: str,
        alert_category: str,
        cluster_name: Optional[str] = None,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Enhanced traverse relationships with architectural augmentation and metric validation.
        
        This method combines telemetry-based relationship discovery with architectural 
        business logic relationships, and validates each relationship with live metrics
        to determine if cascading failure is actually occurring.
        
        Args:
            primary_entity_guid: The GUID of the primary failing entity
            entity_type: The type of the primary entity
            entity_name: The name of the primary entity  
            alert_category: The alert category (determines traversal patterns)
            cluster_name: Optional cluster name for Kubernetes entities
            since_time_ms: Start time in epoch milliseconds for queries
            until_time_ms: End time in epoch milliseconds for queries
            
        Returns:
            Dictionary with cascading failure analysis results
        """
        if not self.query_client:
            logger.error("query_client not initialized for relationship traversal")
            return {
                "primary_entity": {"entityGuid": primary_entity_guid, "entityType": entity_type, "entityName": entity_name},
                "related_entities": [],
                "cascading_failures": [],
                "architectural_relationships": [],
            }

        # Get alert category configuration
        alert_config = self.get_alert_category_config(alert_category)
        
        # Check if architectural augmentation is enabled
        architectural_augmentation = alert_config.get("architectural_augmentation", False)
        architecture_scope = alert_config.get("architecture_scope", "mi_production_architecture")
        
        logger.info(f"Starting enhanced traversal for {entity_name} (type: {entity_type}) with architectural augmentation: {architectural_augmentation}")
        
        # Initialize results
        primary_entity = {
            "entityGuid": primary_entity_guid,
            "entityType": entity_type,
            "entityName": entity_name,
            "cluster_name": cluster_name,
            "is_primary": True,
            "failure_probability": 1.0  # Primary entity has failed
        }
        
        investigated_entities = []
        cascading_failures = []
        architectural_relationships = []
        
        # Get traversal relationships from alert config
        traverse_relationships = alert_config.get("traverse_relationships", [])
        
        for relationship_config in traverse_relationships:
            discovery_method = relationship_config.get("discovery_method", "telemetry")
            
            if discovery_method == "telemetry":
                # Standard telemetry-based relationship discovery
                entities = await self._discover_telemetry_relationships(
                    primary_entity_guid, entity_type, entity_name, 
                    relationship_config, cluster_name, since_time_ms, until_time_ms
                )
                investigated_entities.extend(entities)
                
            elif discovery_method == "architectural_config" and architectural_augmentation:
                # Architectural relationship discovery
                entities = await self._discover_architectural_relationships(
                    primary_entity_guid, entity_type, entity_name,
                    relationship_config, architecture_scope, since_time_ms, until_time_ms
                )
                architectural_relationships.extend(entities)
                investigated_entities.extend(entities)
        
        # Validate metrics for all discovered entities and determine cascading failures
        validated_entities = await self._validate_entity_metrics(investigated_entities, since_time_ms, until_time_ms)
        
        # Identify actual cascading failures
        for entity in validated_entities:
            if entity.get("cascading_failure_detected", False):
                cascading_failures.append(entity)
                
                # Continue traversal from this failing entity if configured
                if entity.get("continue_traversal", False):
                    logger.info(f"Continuing traversal from failing entity: {entity['entityName']}")
                    # Recursive traversal would go here if needed
        
        # Calculate cascading failure metrics
        failure_analysis = self._analyze_cascading_failures(
            primary_entity, validated_entities, cascading_failures
        )
        
        return {
            "primary_entity": primary_entity,
            "related_entities": validated_entities,
            "cascading_failures": cascading_failures,
            "architectural_relationships": architectural_relationships,
            "failure_analysis": failure_analysis,
            "investigation_summary": {
                "total_entities_investigated": len(investigated_entities),
                "cascading_failures_detected": len(cascading_failures),
                "architectural_relationships_found": len(architectural_relationships),
                "blast_radius": len(cascading_failures),
                "risk_level": failure_analysis.get("risk_level", "unknown")
            }
        }

    def traverse_relationships(
        self,
        entity_guid: str,
        entity_type: str,
        entity_name: str,
        alert_category: str,
        cluster_name: Optional[str] = None,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Traverse entity relationships according to the mapping for the alert category.

        SIMPLIFIED VERSION: Gets all relationships defined in the alert category mapping
        without filtering by source/target type.

        Args:
            entity_guid: The GUID of the primary entity
            entity_type: The type of the primary entity
            entity_name: The name of the primary entity
            alert_category: The alert category (determines which relationship mapping to use)
            cluster_name: Optional cluster name for Kubernetes entities
            since_time_ms: Start time in epoch milliseconds for queries
            until_time_ms: End time in epoch milliseconds for queries

        Returns:
            Dictionary with primary entity and related entities information
        """
        if not self.query_client:
            logger.error("query_client not initialized for relationship traversal")
            return {
                "primary_entity": {"entityGuid": entity_guid, "entityType": entity_type, "entityName": entity_name},
                "related_entities": [],
            }

        # Get relationship mapping for this alert category
        alert_config = self.get_alert_category_config(alert_category)

        # Create primary entity structure
        primary_entity = {
            "entityGuid": entity_guid,
            "entityType": entity_type,
            "entityName": entity_name,
            "cluster_name": cluster_name,
        }

        # Initialize related entities list
        related_entities = []

        # Get the traverse relationships from the config
        traverse_relationships = alert_config.get("traverse_relationships", [])

        # Process all relationships
        for relationship_info in traverse_relationships:
            # Check if this is a relationship we can process
            target_type = relationship_info.get("target_type")
            relationship = relationship_info.get("relationship", "RELATED_TO")
            metrics_to_collect = relationship_info.get("metrics_to_collect", [])

            logger.info(
                f"Processing relationship: {entity_type} -> {relationship} -> {target_type}"
            )

            # Get the entity type configuration
            entity_relationships = self.get_entity_relationships(entity_type)

            # Find the appropriate relationship configuration
            rel_config = None
            for rel in entity_relationships:
                if rel.get("target") == target_type or rel.get(
                    "target"
                ) == self.normalize_entity_type(target_type):
                    rel_config = rel
                    break

            if not rel_config:
                logger.warning(f"No relationship config found for {entity_type} -> {target_type}")
                # Try the reverse lookup - get relationships where target type is the source
                target_relationships = self.get_entity_relationships(target_type)
                for rel in target_relationships:
                    if rel.get("target") == entity_type or rel.get(
                        "target"
                    ) == self.normalize_entity_type(entity_type):
                        # Found a reverse relationship
                        rel_config = rel
                        logger.info(f"Found reverse relationship {target_type} -> {entity_type}")
                        break

                # If still no relationship found, continue to next one
                if not rel_config:
                    continue

            # Build the query
            query = self.build_query(
                relationship_config=rel_config,
                entity_name=entity_name,
                entity_guid=entity_guid,
                cluster_name=cluster_name,
                since_time_ms=since_time_ms,
                until_time_ms=until_time_ms,
                )
                
            if not query:
                logger.warning(f"Could not build query for {entity_type} -> {target_type}")
                continue
                
            # Execute the query
            try:
                query_results = self.query_client.execute_nrql(query)
                    
                if not query_results:
                    logger.debug(f"No results for relationship query: {query}")
                    continue

                # Process the results and add to related entities
                for result in query_results:
                    # Create a related entity entry
                    related_entity = {
                        "entityGuid": result.get("entityGuid"),
                        "entityType": target_type,
                        "entityName": result.get("entityName", ""),
                        "relationship": relationship,
                        "metrics_to_collect": metrics_to_collect,
                        "source": "relationship_traversal",
                    }

                    # Improve name extraction by checking for various *Name fields in the result
                    if not related_entity["entityName"]:
                        # For pod entities
                        if target_type in ["KUBERNETES_POD", "K8S_POD"] and "podName" in result:
                            related_entity["entityName"] = result["podName"]
                        elif target_type in ["KUBERNETES_POD", "K8S_POD"] and "latest.podName" in result:
                            related_entity["entityName"] = result["latest.podName"]
                        # For node entities
                        elif target_type in ["KUBERNETES_NODE", "K8S_NODE"] and "nodeName" in result:
                            related_entity["entityName"] = result["nodeName"]
                        elif target_type in ["KUBERNETES_NODE", "K8S_NODE"] and "latest.nodeName" in result:
                            related_entity["entityName"] = result["latest.nodeName"]
                        # For deployment entities
                        elif target_type in ["KUBERNETES_DEPLOYMENT", "K8S_DEPLOYMENT"] and "deploymentName" in result:
                            related_entity["entityName"] = result["deploymentName"]
                        elif target_type in ["KUBERNETES_DEPLOYMENT", "K8S_DEPLOYMENT"] and "latest.deploymentName" in result:
                            related_entity["entityName"] = result["latest.deploymentName"]

                    # if not guid, try to get it using the name
                    if not related_entity["entityGuid"]:
                        # get using
                        if target_type in ["KUBERNETES_POD", "K8S_POD"]:
                            related_entity["entityGuid"] = self.query_client.get_pod_guid(
                                pod_name=related_entity["entityName"],
                                cluster_name=cluster_name,
                            )
                        elif target_type in ["KUBERNETES_NODE", "K8S_NODE"]:
                            related_entity["entityGuid"] = self.query_client.get_node_guid(
                                node_name=related_entity["entityName"],
                                cluster_name=cluster_name,
                            )
                        elif target_type in ["KUBERNETES_DEPLOYMENT", "K8S_DEPLOYMENT"]:
                            related_entity["entityGuid"] = self.query_client.get_deployment_guid(
                                deployment_name=related_entity["entityName"],
                                cluster_name=cluster_name,
                            )
                        elif target_type in ["KUBERNETES_CONTAINER", "K8S_CONTAINER"]:
                            related_entity["entityGuid"] = self.query_client.get_container_guid(
                                container_name=related_entity["entityName"],
                                cluster_name=cluster_name,
                            )
                            
                    # Add metadata fields if available
                    for key, value in result.items():
                        if key not in ["entityGuid", "entityName", "name"]:
                            related_entity[key] = value

                    # Add to related entities
                    related_entities.append(related_entity)

            except Exception as e:
                logger.error(f"Error executing relationship query: {str(e)}")
                logger.exception(e)
                logger.debug(f"Failed query: {query}")

        # Return the structured entity relationships
        return {"primary_entity": primary_entity, "related_entities": related_entities}

    async def _discover_telemetry_relationships(
        self,
        primary_entity_guid: str,
        entity_type: str,
        entity_name: str,
        relationship_config: Dict[str, Any],
        cluster_name: Optional[str] = None,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        Discover relationships using telemetry data (existing NRQL queries).
        
        Args:
            primary_entity_guid: Primary entity GUID
            entity_type: Primary entity type
            entity_name: Primary entity name
            relationship_config: Relationship configuration from alert category
            cluster_name: Optional cluster name
            since_time_ms: Start time for queries
            until_time_ms: End time for queries
            
        Returns:
            List of discovered related entities
        """
        entities = []
        
        try:
            target_type = relationship_config.get("target_type")
            relationship = relationship_config.get("relationship", "RELATED_TO")
            metrics_to_collect = relationship_config.get("metrics_to_collect", [])
            continue_conditions = relationship_config.get("continue_traversal_if", [])
            
            logger.info(f"Discovering telemetry relationships: {entity_type} -> {target_type}")
            
            # Get entity type configuration for NRQL queries
            entity_relationships = self.get_entity_relationships(entity_type)
            
            # Find the appropriate relationship configuration
            rel_config = None
            for rel in entity_relationships:
                if rel.get("target") == target_type or rel.get("target") == self.normalize_entity_type(target_type):
                    rel_config = rel
                    break
            
            if not rel_config:
                logger.warning(f"No telemetry relationship config found for {entity_type} -> {target_type}")
                return entities
            
            # Build and execute query
            query = self.build_query(
                relationship_config=rel_config,
                entity_name=entity_name,
                entity_guid=primary_entity_guid,
                cluster_name=cluster_name,
                since_time_ms=since_time_ms,
                until_time_ms=until_time_ms,
            )
            
            if not query:
                logger.warning(f"Could not build telemetry query for {entity_type} -> {target_type}")
                return entities
            
            # Execute the query
            query_results = self.query_client.execute_nrql(query)
            
            if not query_results:
                logger.debug(f"No telemetry results for relationship query: {query}")
                return entities
            
            # Process results
            for result in query_results:
                entity = {
                    "entityGuid": result.get("entityGuid"),
                    "entityType": target_type,
                    "entityName": result.get("entityName", ""),
                    "relationship": relationship,
                    "discovery_method": "telemetry",
                    "metrics_to_collect": metrics_to_collect,
                    "continue_conditions": continue_conditions,
                    "source": "telemetry_discovery",
                    "cluster_name": cluster_name,
                }
                
                # Add all result fields as metadata
                for key, value in result.items():
                    if key not in ["entityGuid", "entityName"]:
                        entity[key] = value
                
                entities.append(entity)
                
        except Exception as e:
            logger.error(f"Error discovering telemetry relationships: {str(e)}")
            
        return entities

    async def _discover_architectural_relationships(
        self,
        primary_entity_guid: str,
        entity_type: str,
        entity_name: str,
        relationship_config: Dict[str, Any],
        architecture_scope: str,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        Discover relationships using architectural configuration (business logic dependencies).
        
        Args:
            primary_entity_guid: Primary entity GUID
            entity_type: Primary entity type
            entity_name: Primary entity name
            relationship_config: Relationship configuration from alert category
            architecture_scope: Architecture scope (e.g., 'mi_production_architecture')
            since_time_ms: Start time for queries
            until_time_ms: End time for queries
            
        Returns:
            List of discovered architectural relationships
        """
        entities = []
        
        try:
            # Get architectural relationships configuration
            arch_config = self.config.get("architectural_relationships", {})
            architecture = arch_config.get(architecture_scope, {})
            
            if not architecture:
                logger.warning(f"No architectural configuration found for scope: {architecture_scope}")
                return entities
            
            # Find the service in the architecture
            services = architecture.get("services", {})
            
            # Try to match the entity name to a service
            matching_service = None
            for service_name, service_config in services.items():
                # Match by entity name pattern
                entity_name_pattern = service_config.get("entity_name_pattern", "")
                if entity_name_pattern and entity_name_pattern in entity_name:
                    matching_service = service_name
                    break
                    
                # Match by entity GUID pattern
                entity_guid_pattern = service_config.get("entity_guid_pattern", "")
                if entity_guid_pattern and entity_guid_pattern in primary_entity_guid:
                    matching_service = service_name
                    break
            
            if not matching_service:
                logger.info(f"No architectural service match found for entity: {entity_name}")
                return entities
            
            logger.info(f"Found architectural service match: {matching_service} for entity: {entity_name}")
            
            # Get business dependencies for this service
            service_config = services[matching_service]
            business_dependencies = service_config.get("business_dependencies", [])
            
            # Filter dependencies based on relationship config
            target_relationship = relationship_config.get("relationship")
            
            for dependency in business_dependencies:
                if dependency.get("relationship_type") == target_relationship:
                    # Create entity for the dependency
                    target_service = dependency.get("target_service")
                    
                    entity = {
                        "entityGuid": f"arch-{target_service}-{matching_service}",  # Synthetic GUID
                        "entityType": "APPLICATION",  # Default for business dependencies
                        "entityName": target_service,
                        "relationship": dependency.get("relationship_type"),
                        "discovery_method": "architectural_config",
                        "criticality": dependency.get("criticality", "medium"),
                        "failure_propagation_probability": dependency.get("failure_propagation_probability", 0.5),
                        "failure_indicators": dependency.get("failure_indicators", {}),
                        "metrics_to_collect": dependency.get("metrics_to_validate", []),
                        "continue_conditions": relationship_config.get("continue_traversal_if", []),
                        "source": "architectural_discovery",
                        "source_service": matching_service,
                        "typical_response_time_ms": dependency.get("typical_response_time_ms"),
                    }
                    
                    entities.append(entity)
                    
        except Exception as e:
            logger.error(f"Error discovering architectural relationships: {str(e)}")
            
        return entities

    async def _validate_entity_metrics(
        self,
        entities: List[Dict[str, Any]],
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        Validate metrics for discovered entities to determine if cascading failure is occurring.
        
        Args:
            entities: List of discovered entities
            since_time_ms: Start time for metric collection
            until_time_ms: End time for metric collection
            
        Returns:
            List of entities with metric validation results
        """
        validated_entities = []
        
        for entity in entities:
            try:
                metrics_to_collect = entity.get("metrics_to_collect", [])
                continue_conditions = entity.get("continue_conditions", [])
                entity_guid = entity.get("entityGuid")
                entity_name = entity.get("entityName")
                
                logger.debug(f"Validating metrics for entity: {entity_name}")
                
                # Collect metrics for this entity
                collected_metrics = {}
                if metrics_to_collect and entity_guid and self.query_client:
                    collected_metrics = await self._collect_entity_metrics(
                        entity_guid, entity_name, metrics_to_collect, since_time_ms, until_time_ms
                    )
                
                # Evaluate continue conditions to determine if cascading failure is detected
                cascading_failure_detected = False
                continue_traversal = False
                
                if continue_conditions and collected_metrics:
                    cascading_failure_detected, continue_traversal = self._evaluate_continue_conditions(
                        continue_conditions, collected_metrics
                    )
                
                # Update entity with validation results
                entity.update({
                    "collected_metrics": collected_metrics,
                    "cascading_failure_detected": cascading_failure_detected,
                    "continue_traversal": continue_traversal,
                    "validation_timestamp": datetime.now().isoformat(),
                })
                
                validated_entities.append(entity)
                
            except Exception as e:
                logger.error(f"Error validating metrics for entity {entity.get('entityName', 'unknown')}: {str(e)}")
                # Add entity without validation
                entity.update({
                    "collected_metrics": {},
                    "cascading_failure_detected": False,
                    "continue_traversal": False,
                    "validation_error": str(e),
                })
                validated_entities.append(entity)
        
        return validated_entities

    async def _collect_entity_metrics(
        self,
        entity_guid: str,
        entity_name: str,
        metrics_to_collect: List[str],
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Collect specific metrics for an entity.
        
        Args:
            entity_guid: Entity GUID
            entity_name: Entity name
            metrics_to_collect: List of metric names to collect
            since_time_ms: Start time for metrics
            until_time_ms: End time for metrics
            
        Returns:
            Dictionary of collected metric values
        """
        metrics = {}
        
        try:
            # Build time range parameters
            time_params = ""
            if since_time_ms and until_time_ms:
                time_params = f"SINCE {since_time_ms} UNTIL {until_time_ms}"
            else:
                time_params = "SINCE 15 MINUTES AGO"
            
            # Collect each metric
            for metric_name in metrics_to_collect:
                try:
                    # Build metric-specific query
                    query = self._build_metric_query(entity_guid, entity_name, metric_name, time_params)
                    
                    if query:
                        result = self.query_client.execute_nrql(query)
                        if result and len(result) > 0:
                            # Extract metric value from result
                            metric_value = self._extract_metric_value(result[0], metric_name)
                            metrics[metric_name] = metric_value
                        else:
                            metrics[metric_name] = None
                    else:
                        metrics[metric_name] = None
                        
                except Exception as e:
                    logger.warning(f"Error collecting metric {metric_name} for {entity_name}: {str(e)}")
                    metrics[metric_name] = None
                    
        except Exception as e:
            logger.error(f"Error collecting metrics for entity {entity_name}: {str(e)}")
            
        return metrics

    def _build_metric_query(self, entity_guid: str, entity_name: str, metric_name: str, time_params: str) -> str:
        """Build NRQL query for a specific metric."""
        
        # Define metric query templates
        metric_queries = {
            "error_rate": f"SELECT percentage(count(*), WHERE error = true) as error_rate FROM Transaction WHERE entityGuid = '{entity_guid}' {time_params}",
            "response_time": f"SELECT average(duration) as response_time FROM Transaction WHERE entityGuid = '{entity_guid}' {time_params}",
            "availability": f"SELECT percentage(count(*), WHERE responseTimeApp IS NOT NULL) as availability FROM PageView WHERE entityGuid = '{entity_guid}' {time_params}",
            "cpu_usage": f"SELECT average(cpuUsedPercent) as cpu_usage FROM SystemSample WHERE entityGuid = '{entity_guid}' {time_params}",
            "memory_usage": f"SELECT average(memoryUsedPercent) as memory_usage FROM SystemSample WHERE entityGuid = '{entity_guid}' {time_params}",
            "restart_count": f"SELECT latest(restartCount) as restart_count FROM K8sPodSample WHERE entityGuid = '{entity_guid}' {time_params}",
            "pod_status": f"SELECT latest(status) as pod_status FROM K8sPodSample WHERE entityGuid = '{entity_guid}' {time_params}",
        }
        
        return metric_queries.get(metric_name, "")

    def _extract_metric_value(self, result: Dict[str, Any], metric_name: str) -> Any:
        """Extract metric value from NRQL result."""
        return result.get(metric_name)

    def _evaluate_continue_conditions(self, conditions: List[str], metrics: Dict[str, Any]) -> tuple[bool, bool]:
        """
        Evaluate continue conditions to determine if cascading failure is detected.
        
        Args:
            conditions: List of condition strings (e.g., "error_rate > 0.05")
            metrics: Dictionary of collected metric values
            
        Returns:
            Tuple of (cascading_failure_detected, continue_traversal)
        """
        cascading_failure_detected = False
        continue_traversal = False
        
        try:
            for condition in conditions:
                # Parse condition (e.g., "error_rate > 0.05")
                if self._evaluate_condition(condition, metrics):
                    cascading_failure_detected = True
                    continue_traversal = True
                    logger.info(f"Cascading failure condition met: {condition}")
                    break
                    
        except Exception as e:
            logger.error(f"Error evaluating continue conditions: {str(e)}")
            
        return cascading_failure_detected, continue_traversal

    def _evaluate_condition(self, condition: str, metrics: Dict[str, Any]) -> bool:
        """Evaluate a single condition against metrics."""
        try:
            # Simple condition parsing
            condition = condition.strip()
            
            # Handle different operators
            for operator in [" > ", " < ", " >= ", " <= ", " == ", " != "]:
                if operator in condition:
                    metric_name, threshold = condition.split(operator)
                    metric_name = metric_name.strip()
                    threshold = threshold.strip()
                    
                    metric_value = metrics.get(metric_name)
                    if metric_value is None:
                        return False
                    
                    # Convert threshold to appropriate type
                    if threshold.replace(".", "").isdigit():
                        threshold = float(threshold)
                    elif threshold.lower() in ["true", "false"]:
                        threshold = threshold.lower() == "true"
                    elif threshold.startswith("'") or threshold.startswith('"'):
                        threshold = threshold[1:-1]  # Remove quotes
                    
                    # Evaluate condition
                    if operator.strip() == ">":
                        return metric_value > threshold
                    elif operator.strip() == "<":
                        return metric_value < threshold
                    elif operator.strip() == ">=":
                        return metric_value >= threshold
                    elif operator.strip() == "<=":
                        return metric_value <= threshold
                    elif operator.strip() == "==":
                        return metric_value == threshold
                    elif operator.strip() == "!=":
                        return metric_value != threshold
                    
        except Exception as e:
            logger.error(f"Error evaluating condition '{condition}': {str(e)}")
            
        return False

    def _analyze_cascading_failures(
        self,
        primary_entity: Dict[str, Any],
        investigated_entities: List[Dict[str, Any]],
        cascading_failures: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Analyze cascading failures and calculate risk metrics.
        
        Args:
            primary_entity: Primary failing entity
            investigated_entities: All investigated entities
            cascading_failures: Entities with detected cascading failures
            
        Returns:
            Dictionary with failure analysis results
        """
        try:
            total_entities = len(investigated_entities)
            failing_entities = len(cascading_failures)
            blast_radius = failing_entities
            
            # Calculate impact score
            if total_entities > 0:
                impact_score = failing_entities / total_entities
            else:
                impact_score = 0.0
            
            # Determine risk level
            if impact_score >= 0.7:
                risk_level = "critical"
            elif impact_score >= 0.4:
                risk_level = "high"
            elif impact_score >= 0.2:
                risk_level = "medium"
            else:
                risk_level = "low"
            
            # Generate recommendations
            recommendations = []
            if blast_radius > 3:
                recommendations.append("Immediate monitoring of all affected services")
                recommendations.append("Consider implementing circuit breakers")
            if impact_score > 0.5:
                recommendations.append("Escalate to senior engineering team")
                recommendations.append("Prepare for potential service isolation")
            
            return {
                "total_entities_investigated": total_entities,
                "cascading_failures_detected": failing_entities,
                "blast_radius": blast_radius,
                "impact_score": round(impact_score, 3),
                "risk_level": risk_level,
                "recommendations": recommendations,
                "analysis_timestamp": datetime.now().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Error analyzing cascading failures: {str(e)}")
            return {
                "error": str(e),
                "analysis_timestamp": datetime.now().isoformat(),
            }

    def find_related_entities_from_missing_entity(
        self, 
        entity_guid: str,
        alert_category: str,
        since_time: Optional[datetime] = None,
        until_time: Optional[datetime] = None,
        lookback_days: int = 10
    ) -> Dict[str, Any]:
        """
        Find related entities when the primary entity is missing.

        Uses the fallback queries defined in the alert category configuration.
        
        Args:
            entity_guid: The entity GUID that might be missing
            alert_category: The alert category to determine relationship mapping
            since_time: Start time for looking back in history
            until_time: End time for looking back in history
            
        Returns:
            Dictionary with primary and related entities information
        """
        if not self.query_client:
            logger.error(
                "query_client not initialized for finding related entities from missing entity"
            )
            return {"primary_entity": None, "related_entities": []}

        # Get alert category config
        alert_config = self.get_alert_category_config(alert_category)

        # If no config found, return empty result
        if not alert_config:
            logger.warning(f"No alert category config found for {alert_category}")
            return {"primary_entity": None, "related_entities": []}

        # Get the primary entity type from the config
        primary_entity_type = alert_config.get("primary_entity_type")

        if not primary_entity_type:
            logger.warning(f"No primary entity type defined in alert category {alert_category}")
            return {"primary_entity": None, "related_entities": []}

        # Calculate lookback period (default to 7 days if not specified)
        # lookback_days = 10
        # if since_time and until_time:
        #     # Calculate days between timestamps, at least 1 day
        #     delta = until_time - since_time
        #     lookback_days = max(1, delta.days + 1)

        # Get the fallback queries from the config
        fallback_queries = alert_config.get("fallback_queries", [])

        if not fallback_queries:
            logger.warning(f"No fallback queries defined for alert category {alert_category}")
            return {"primary_entity": None, "related_entities": []}

        # Initialize primary entity and related entities
        primary_entity = None
        related_entities = []

        # Execute each fallback query
        for query_info in fallback_queries:
            query_type = query_info.get("type", "")
            relationship = query_info.get("relationship", "related_to")
            params = query_info.get("params", {})

            # Add entity_guid and lookback_days to params
            params["entity_guid"] = entity_guid
            params["lookback_days"] = lookback_days

            # Execute the appropriate query based on type
            try:
                if query_type == "container_related":
                    # Execute the container related entities query
                    container_related = self.query_client.get_container_related_entities(
                        entity_guid=entity_guid, since_days=lookback_days
                    )

                    if container_related:
                        # Set primary entity
                        primary_entity = {
                            "entityGuid": entity_guid,
                            "entityType": primary_entity_type,
                            "entityName": container_related.get("containerName", "unknown-container"),
                            "container_missing": True,
                        }

                        # Process pod information
                        pod_name = container_related.get("podName")
                        cluster_name = container_related.get("clusterName")
                        namespace_name = container_related.get("namespaceName")
                        node_name = container_related.get("nodeName")
                        deployment_name = container_related.get("deploymentName")

                        if pod_name and cluster_name:
                            # Get pod GUID
                            pod_guid = self.query_client.get_pod_guid(
                                pod_name=pod_name,
                                cluster_name=cluster_name,
                                namespace_name=namespace_name,
                                since_days=lookback_days,
                            )

                            if pod_guid:
                                # Add pod entity
                                pod_entity = {
                                    "guid": pod_guid,
                                    "type": "KUBERNETES_POD",
                                    "name": pod_name,
                                    "relationship": relationship,
                                    "source": "fallback_query",
                                    "cluster_name": cluster_name,
                                    "namespace": namespace_name,
                                }
                                related_entities.append(pod_entity)

                        if node_name and cluster_name:
                            # Get node GUID
                            node_guid = self.query_client.get_node_guid(
                                node_name=node_name,
                                cluster_name=cluster_name,
                                since_days=lookback_days,
                            )
                            
                            if node_guid:
                                # Add node entity
                                node_entity = {
                                    "guid": node_guid,
                                    "type": "KUBERNETES_NODE",
                                    "name": node_name,
                                    "relationship": relationship,
                                    "source": "fallback_query",
                                    "cluster_name": cluster_name,
                                }
                                related_entities.append(node_entity)
                            
                        if deployment_name and cluster_name:
                            # Get deployment GUID
                            deployment_guid = self.query_client.get_deployment_guid(
                                deployment_name=deployment_name,
                                cluster_name=cluster_name,
                                namespace_name=namespace_name,
                                since_days=lookback_days,
                            )

                            if deployment_guid:
                                # Add deployment entity
                                deployment_entity = {
                                    "guid": deployment_guid,
                                    "type": "KUBERNETES_DEPLOYMENT",
                                    "name": deployment_name,
                                    "relationship": relationship,
                                    "source": "fallback_query",
                                    "cluster_name": cluster_name,
                                    "namespace": namespace_name,
                                }
                                related_entities.append(deployment_entity)

                elif query_type == "pod_related":
                    # Execute the pod related entities query
                    pod_related = self.query_client.get_pod_related_entities(
                        entity_guid=entity_guid, since_days=lookback_days
                    )

                    if pod_related:
                        # Set primary entity
                        primary_entity = {
                            "entityGuid": entity_guid,
                            "entityType": primary_entity_type,
                            "entityName": pod_related.get("podName", "unknown-pod"),
                            "pod_missing": True,
                        }

                        # Process node information
                        node_name = pod_related.get("nodeName")
                        cluster_name = pod_related.get("clusterName")

                        if node_name and cluster_name:
                            # Get node GUID
                            node_guid = self.query_client.get_node_guid(
                                node_name=node_name,
                                cluster_name=cluster_name,
                                since_days=lookback_days,
                            )

                            if node_guid:
                                # Add node entity
                                node_entity = {
                                    "entityGuid": node_guid,
                                    "entityType": "KUBERNETES_NODE",
                                    "entityName": node_name,
                                    "relationship": relationship,
                                    "source": "fallback_query",
                                    "cluster_name": cluster_name,
                                }
                                related_entities.append(node_entity)

                elif query_type == "deployment_related":
                    # Execute the deployment related entities query
                    deployment_related = self.query_client.get_deployment_related_entities(
                        entity_guid=entity_guid, since_days=lookback_days
                    )

                    if deployment_related:
                        # Set primary entity
                        primary_entity = {
                            "entityGuid": entity_guid,
                            "entityType": primary_entity_type,
                            "entityName": deployment_related.get("deploymentName", "unknown-deployment"),
                            "deployment_missing": True,
                        }

                        # Process pods information
                        pods = deployment_related.get("pods", [])
                        cluster_name = deployment_related.get("clusterName")
                        namespace_name = deployment_related.get("namespaceName")

                        if pods and cluster_name and namespace_name:
                            for pod_name in pods:
                                # Get pod GUID
                                pod_guid = self.query_client.get_pod_guid(
                                    pod_name=pod_name,
                                    cluster_name=cluster_name,
                                    namespace_name=namespace_name,
                                    since_days=lookback_days,
                                )

                                if pod_guid:
                                    # Add pod entity
                                    pod_entity = {
                                        "entityGuid": pod_guid,
                                        "entityType": "KUBERNETES_POD",
                                        "entityName": pod_name,
                                        "relationship": relationship,
                                        "source": "fallback_query",
                                        "cluster_name": cluster_name,
                                        "namespace": namespace_name,
                                    }
                                    related_entities.append(pod_entity)

                elif query_type == "custom_nrql":
                    # Execute a custom NRQL query defined in the config
                    query_template = query_info.get("query", "")
                    result_mapping = query_info.get("result_mapping", {})
                    target_type = query_info.get("target_type", "UNKNOWN")

                    if query_template:
                        # Format the query with parameters
                        try:
                            # Add standard params
                            format_params = {
                                "entity_guid": entity_guid,
                                "since": f"{lookback_days} days ago",
                                "until": "now",
                            }
                            # Add custom params
                            format_params.update(params)

                            # Format the query
                            query = query_template.format(**format_params)

                            # Execute the query
                            results = self.query_client.execute_nrql(query)

                            if results:
                                for result in results:
                                    # Map result fields to entity fields
                                    entity_guid = result.get(
                                        result_mapping.get("guid", "entityGuid")
                                    )
                                    entity_name = result.get(
                                        result_mapping.get("name", "name"),
                                        result.get(
                                            result_mapping.get("entityName", "entityName"),
                                            "unknown",
                                        ),
                                    )

                                    if entity_guid:
                                        # Create related entity
                                        entity = {
                                            "entityGuid": entity_guid,
                                            "entityType": target_type,
                                            "entityName": entity_name,
                                            "relationship": relationship,
                                            "source": "custom_nrql",
                                        }

                                        # Add any additional fields specified in the mapping
                                        for target_field, source_field in result_mapping.items():
                                            if (
                                                target_field not in ["guid", "name", "entityName"]
                                                and source_field in result
                                            ):
                                                entity[target_field] = result[source_field]

                                        related_entities.append(entity)

                        except Exception as e:
                            logger.error(f"Error executing custom NRQL query: {str(e)}")
                            logger.debug(f"Query template: {query_template}")

                # Add more query types as needed

            except Exception as e:
                logger.error(f"Error executing fallback query of type {query_type}: {str(e)}")

        # Return the primary entity and related entities
        return {"primary_entity": primary_entity, "related_entities": related_entities}

    def find_entities_for_alert(
        self, alert_data: Dict[str, Any], alert_category: str
    ) -> List[Dict[str, Any]]:
        """
        Find primary and related entities for an alert without direct entity GUID.

        Uses the alert category configuration to determine which entities are relevant.

        Args:
            alert_data: The alert data including title, condition name, etc.
            alert_category: The alert category

        Returns:
            List of entities associated with the alert
        """
        if not self.query_client:
            logger.error("query_client not initialized for entity discovery")
            return []

        # Get the alert category config
        alert_config = self.get_alert_category_config(alert_category)

        if not alert_config:
            logger.warning(f"No alert category config found for {alert_category}")
            return []

        # Check if this alert category has an entity discovery configuration
        if not alert_config.get("entity_discovery"):
            logger.warning(f"No entity discovery configuration for alert category {alert_category}")
            return []

        # Get the entity discovery config
        discovery_config = alert_config.get("entity_discovery", {})
        nrql_queries = discovery_config.get("nrql_queries", [])

        if not nrql_queries:
            logger.warning(
                f"No NRQL queries defined for entity discovery in alert category {alert_category}"
            )
            return []

        # Extract context from the alert
        alert_title = alert_data.get("title", "")
        condition_name = alert_data.get("condition_name", "")
        
        # Extract additional context
        alert_context = self._extract_alert_context(alert_title, condition_name)

        # Add product and region if available
        product = alert_data.get("product")
        if product:
            alert_context["product"] = product

        nr_region = alert_data.get("nr_region")
        if nr_region:
            alert_context["nr_region"] = nr_region

        # List to store discovered entities
        discovered_entities = []

        # Execute each NRQL query
        for query_config in nrql_queries:
            query_template = query_config.get("query", "")
            entity_type = query_config.get("entity_type", "UNKNOWN")
            is_primary = query_config.get("is_primary", False)
            result_mapping = query_config.get("result_mapping", {})

            if not query_template:
                logger.warning(f"Missing query template in entity discovery config")
                continue

            # Format the query template with context
            try:
                # Start with a copy of alert_context
                format_params = dict(alert_context)

                # Add default time window if not specified
                if "since" not in format_params:
                    format_params["since"] = "1 hour ago"
                if "until" not in format_params:
                    format_params["until"] = "now"

                # Format the query
                query = query_template.format(**format_params)

                # Execute the query
                results = self.query_client.execute_nrql(query)

                # Process results
                if not results:
                    logger.debug(f"No results for entity discovery query: {query}")
                    continue
                    
                # Create entities from results
                for result in results:
                    # Map result fields to entity fields
                    entity_guid = result.get(result_mapping.get("guid", "entityGuid"))
                    entity_name = result.get(
                        result_mapping.get("name", "name"),
                        result.get(result_mapping.get("entityName", "entityName"), "unknown"),
                    )

                    if entity_guid:
                        # Create entity
                        entity = {
                            "entity_guid": entity_guid,
                            "entity_type": entity_type,
                            "entity_name": entity_name,
                            "is_primary": is_primary,
                            "discovery_source": "nrql_query",
                        }

                        # Add any additional fields specified in the mapping
                        for target_field, source_field in result_mapping.items():
                            if (
                                target_field not in ["guid", "name", "entityName"]
                                and source_field in result
                            ):
                                entity[target_field] = result[source_field]

                        discovered_entities.append(entity)
                    
            except Exception as e:
                logger.error(f"Error executing entity discovery query: {str(e)}")
                logger.debug(f"Query template: {query_template}")

        return discovered_entities

    def _extract_alert_context(self, alert_title: str, condition_name: str) -> Dict[str, Any]:
        """
        Extract contextual information from alert title and condition.
        
        Args:
            alert_title: The alert title
            condition_name: The alert condition name
            
        Returns:
            Dictionary of extracted context parameters
        """
        context = {}
        
        # Extract cluster name from alert title
        cluster_patterns = [
            r"cluster[: ]([^\s,]+)",  # Match "cluster: name" or "cluster name"
            r"for cluster ([^\s,]+)",  # Match "for cluster name"
            r"in cluster ([^\s,]+)",  # Match "in cluster name"
            r"on cluster ([^\s,]+)",  # Match "on cluster name"
        ]
        
        for pattern in cluster_patterns:
            match = re.search(pattern, alert_title, re.IGNORECASE)
            if match:
                context["cluster_name"] = match.group(1)
                break
                
        # Extract namespace from alert title
        namespace_patterns = [
            r"namespace[: ]([^\s,]+)",  # Match "namespace: name" or "namespace name"
            r"in namespace ([^\s,]+)",  # Match "in namespace name"
        ]
        
        for pattern in namespace_patterns:
            match = re.search(pattern, alert_title, re.IGNORECASE)
            if match:
                context["namespace"] = match.group(1)
                break
                
        # Extract pod name from alert title
        pod_patterns = [
            r"pod[: ]([^\s,]+)",  # Match "pod: name" or "pod name"
            r"for pod ([^\s,]+)",  # Match "for pod name"
        ]
        
        for pattern in pod_patterns:
            match = re.search(pattern, alert_title, re.IGNORECASE)
            if match:
                context["pod_name"] = match.group(1)
                break
                
        # Extract deployment name from alert title
        deployment_patterns = [
            r"deployment[: ]([^\s,]+)",  # Match "deployment: name" or "deployment name"
            r"for deployment ([^\s,]+)",  # Match "for deployment name"
        ]
        
        for pattern in deployment_patterns:
            match = re.search(pattern, alert_title, re.IGNORECASE)
            if match:
                context["deployment_name"] = match.group(1)
                break
                
        # Extract region from alert title
        region_patterns = [
            r"region[: ]([^\s,]+)",  # Match "region: name" or "region name"
            r"in region ([^\s,]+)",  # Match "in region name"
        ]
        
        for pattern in region_patterns:
            match = re.search(pattern, alert_title, re.IGNORECASE)
            if match:
                context["region"] = match.group(1)
                break
                
        # Extract additional context from condition name if needed
        if "kafka" in condition_name.lower() or "kafka" in alert_title.lower():
            context["service_type"] = "kafka"
            
        if "debezium" in condition_name.lower() or "debezium" in alert_title.lower():
            context["service_type"] = "debezium"
            
        return context

    def set_query_client(self, query_client):
        """
        Set the query client for the relationship service.
        
        Args:
            query_client: The NewRelicQueryClient instance to use for queries
        """
        self.query_client = query_client
        logger.info("Query client set for entity relationship service")
        
    def _build_entity_discovery_query(
        self, target_type: str, pattern: str, param_values: Dict[str, Any]
    ) -> str:
        """
        Build a NRQL query to discover entities based on type and pattern.
        
        Args:
            target_type: Type of entity to discover
            pattern: Pattern to match against entity names
            param_values: Parameter values for query constraints
            
        Returns:
            NRQL query string or empty string if unable to build query
        """
        # Use case insensitive LIKE for pattern matching
        # Escape any special characters in the pattern for LIKE
        like_pattern = pattern.replace("%", "\\%").replace("_", "\\_")
        # If the pattern is a regex with ^ or $, convert it to SQL LIKE format
        if like_pattern.startswith("^"):
            like_pattern = like_pattern[1:]
        else:
            like_pattern = "%" + like_pattern

        if like_pattern.endswith("$"):
            like_pattern = like_pattern[:-1]
        else:
            like_pattern = like_pattern + "%"

        # Replace .* with % for LIKE syntax
        like_pattern = like_pattern.replace(".*", "%")

        # Normalize to handle both NewRelic and K8s prefixed types
        normalized_type = self.normalize_entity_type(target_type)

        # Time window for querying - default to last hour
        time_window = "SINCE 1 hour ago"

        # K8s pod entity query
        if normalized_type in ["KUBERNETES_POD", "K8S_POD"]:
            query = f"""
                SELECT 
                    entityName, 
                    entityGuid, 
                    entityType,
                    podName as name,
                    namespaceName,
                    clusterName,
                    nodeName
                FROM K8sPodSample 
                WHERE podName LIKE '{like_pattern}'
            """

            # Add cluster constraint if available
            if "cluster_name" in param_values and param_values["cluster_name"]:
                query += f" AND clusterName = '{param_values['cluster_name']}'"

            # Add namespace constraint if available
            if "namespace" in param_values and param_values["namespace"]:
                query += f" AND namespaceName = '{param_values['namespace']}'"

        # K8s node entity query
        elif normalized_type in ["KUBERNETES_NODE", "K8S_NODE"]:
            query = f"""
                SELECT 
                    entityName, 
                    entityGuid, 
                    entityType,
                    nodeName as name,
                    clusterName
                FROM K8sNodeSample 
                WHERE nodeName LIKE '{like_pattern}'
            """

            # Add cluster constraint if available
            if "cluster_name" in param_values and param_values["cluster_name"]:
                query += f" AND clusterName = '{param_values['cluster_name']}'"

        # K8s deployment entity query
        elif normalized_type in ["KUBERNETES_DEPLOYMENT", "K8S_DEPLOYMENT"]:
            query = f"""
                SELECT 
                    entityName, 
                    entityGuid, 
                    entityType,
                    deploymentName as name,
                    namespaceName,
                    clusterName
                FROM K8sDeploymentSample 
                WHERE deploymentName LIKE '{like_pattern}'
            """

            # Add cluster constraint if available
            if "cluster_name" in param_values and param_values["cluster_name"]:
                query += f" AND clusterName = '{param_values['cluster_name']}'"

            # Add namespace constraint if available
            if "namespace" in param_values and param_values["namespace"]:
                query += f" AND namespaceName = '{param_values['namespace']}'"

        # Application entity query
        elif normalized_type == "APPLICATION":
            query = f"""
                SELECT 
                    entityName, 
                    entityGuid, 
                    entityType,
                    appName as name
                FROM Transaction 
                WHERE appName LIKE '{like_pattern}'
            """

        # Host entity query
        elif normalized_type in ["HOST", "INFRASTRUCTURE"]:
            query = f"""
                SELECT 
                    entityName, 
                    entityGuid, 
                    entityType,
                    hostname as name,
                    regionName,
                    awsRegion,
                    azureRegion
                FROM SystemSample 
                WHERE hostname LIKE '{like_pattern}'
            """

            # Add region constraint if available
            if "region" in param_values and param_values["region"]:
                region = param_values["region"]
                query += f" AND (regionName = '{region}' OR awsRegion = '{region}' OR azureRegion = '{region}')"

        # Kafka entity query - assuming custom metrics or events for Kafka
        elif normalized_type == "KAFKA":
            query = f"""
                SELECT 
                    entityName, 
                    entityGuid, 
                    entityType,
                    podName as name,
                    namespaceName,
                    clusterName
                FROM K8sPodSample 
                WHERE podName LIKE 'kafka-%'
            """

            # Add cluster constraint if available
            if "cluster_name" in param_values and param_values["cluster_name"]:
                query += f" AND clusterName = '{param_values['cluster_name']}'"

        # Default query if no specific type matched
        else:
            logger.warning(f"No query template available for entity type: {target_type}")
            return ""

        # Add time window and limit
        query += f" {time_window} LIMIT 10 FACET entityGuid"

        return query


# Singleton instance
_entity_relationship_service = None


def get_entity_relationship_service() -> EntityRelationshipService:
    """Get or create the singleton EntityRelationshipService instance."""
    global _entity_relationship_service
    if _entity_relationship_service is None:
        _entity_relationship_service = EntityRelationshipService()
    return _entity_relationship_service
