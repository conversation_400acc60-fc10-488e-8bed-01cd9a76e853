"""
MongoDB service for incident management.

This module provides a service for interacting with MongoDB to store incident data,
tool execution results, metrics, logs, events, and topology data.
"""

import os
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from loguru import logger
import motor.motor_asyncio
from bson import ObjectId
import dotenv
import json
import urllib.parse

# Load environment variables
dotenv.load_dotenv()

class MongoDBService:
    """Service for MongoDB operations related to incident management."""
    
    _instance = None
    
    def __new__(cls):
        """Singleton pattern to ensure only one instance of the service exists."""
        if cls._instance is None:
            cls._instance = super(MongoDBService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the MongoDB service."""
        if self._initialized:
            return
            
        # Get MongoDB connection string from environment variables
        self._connection_string = os.getenv("MONGODB_CONNECTION_STRING")
        
        if not self._connection_string:
            logger.warning("MONGODB_CONNECTION_STRING environment variable not set. Using default local connection.")
            self._connection_string = "mongodb://localhost:27017"
        else:
            logger.info(f"Using MongoDB connection string from environment variable (masked for security)")
            
        # Parse and escape username and password if present in the connection string
        try:
            if '@' in self._connection_string:
                # Extract the credentials part
                protocol, rest = self._connection_string.split('://', 1)
                credentials, host_part = rest.split('@', 1)
                
                if ':' in credentials:
                    username, password = credentials.split(':', 1)
                    # URL-encode the username and password
                    safe_username = urllib.parse.quote_plus(username)
                    safe_password = urllib.parse.quote_plus(password)
                    
                    # Reconstruct the connection string
                    self._connection_string = f"{protocol}://{safe_username}:{safe_password}@{host_part}"
                    logger.info("Sanitized MongoDB connection string with properly escaped credentials")
        except Exception as e:
            logger.error(f"Error sanitizing MongoDB connection string: {str(e)}")
            # Continue with the original string, the error will be caught when connecting
            
        # Initialize client
        try:
            logger.debug(f"Attempting to connect to MongoDB...")
            self._client = motor.motor_asyncio.AsyncIOMotorClient(
                self._connection_string,
                serverSelectionTimeoutMS=5000  # 5 second timeout for testing connectivity
            )
            
            # Test connection
            try:
                # This will validate the connection by trying to get server info
                server_info = self._client.server_info()
                logger.info("MongoDB connection test successful")
            except Exception as e:
                logger.error(f"MongoDB connection test failed: {str(e)}")
                raise
            
            # Get database name from environment variables or use default
            db_name = os.getenv("MONGODB_DATABASE_NAME", "incident_management")
            logger.info(f"Using MongoDB database: {db_name}")
            self._db = self._client[db_name]
            
            # Primary collection - we'll now store everything in the incidents collection
            self._incidents_collection = self._db["incidents"]
            
            # Keep references to the separate collections for backward compatibility
            # but we'll primarily use the incidents collection for new data
            self._metrics_collection = self._db["metrics"]
            self._logs_collection = self._db["logs"]
            self._events_collection = self._db["events"]
            self._tool_results_collection = self._db["tool_results"]
            
            # Add topology collection
            self._topology_collection = self._db["topology"]
            
            self._initialized = True
            logger.info(f"MongoDB service initialized with database: {db_name}")
        except Exception as e:
            logger.error(f"Failed to initialize MongoDB connection: {str(e)}")
            # Set collections to None to indicate initialization failure
            self._incidents_collection = None
            self._metrics_collection = None
            self._logs_collection = None
            self._events_collection = None
            self._tool_results_collection = None
            self._topology_collection = None
            self._initialized = False  # Mark as not initialized to try again next time
            raise
    
    def _serialize_for_mongodb(self, data: Any) -> Any:
        """
        Serialize data for MongoDB storage, handling Pydantic models.
        
        Args:
            data: Data to serialize, could be a Pydantic model, list, dict, or scalar
            
        Returns:
            MongoDB-compatible serialized data
        """
        # If it's a Pydantic model, convert to dict
        if hasattr(data, "model_dump"):
            return self._serialize_for_mongodb(data.model_dump())
        elif hasattr(data, "dict"):  # Older Pydantic versions
            return self._serialize_for_mongodb(data.dict())
        # If it's a list, serialize each item
        elif isinstance(data, list):
            return [self._serialize_for_mongodb(item) for item in data]
        # If it's a dict, serialize each value
        elif isinstance(data, dict):
            return {k: self._serialize_for_mongodb(v) for k, v in data.items()}
        # If it's a datetime, convert to ISO string
        elif isinstance(data, datetime):
            return data.isoformat()
        # Otherwise, return as is
        return data
        
    def _deserialize_from_mongodb(self, data: Any) -> Any:
        """
        Deserialize data from MongoDB, handling ObjectIds and other MongoDB-specific types.
        
        Args:
            data: Data to deserialize, could be a MongoDB document, list, dict, or scalar
            
        Returns:
            Pydantic-compatible deserialized data
        """
        from bson.objectid import ObjectId
        
        # If it's None, return None
        if data is None:
            return None
            
        # If it's an ObjectId, convert to string
        if isinstance(data, ObjectId):
            return str(data)
            
        # If it's a list, deserialize each item
        if isinstance(data, list):
            return [self._deserialize_from_mongodb(item) for item in data]
            
        # If it's a dict, deserialize each value
        if isinstance(data, dict):
            result = {}
            for k, v in data.items():
                # Convert _id to string
                if k == '_id':
                    result[k] = str(v) if isinstance(v, ObjectId) else v
                else:
                    result[k] = self._deserialize_from_mongodb(v)
            return result
            
        # Otherwise, return as is
        return data
        
    async def store_incident_state(self, state: Any, update_tool_results: bool = False) -> str:
        """
        Store the complete incident state in MongoDB.
        This method handles Pydantic models and serializes them properly.
        
        Args:
            state: Complete incident state (typically an IncidentState model)
            update_tool_results: Whether to update tool_results in the incident document
                                (normally false as tool_results are stored separately)
            
        Returns:
            ID of the stored document
        """
        # Check if the database is initialized
        if self._incidents_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot store incident state."
            logger.error(error_msg)
            raise RuntimeError(error_msg)
            
        # First serialize the state
        serialized_state = self._serialize_for_mongodb(state)
        
        # Add timestamp if not present
        if "timestamp" not in serialized_state:
            serialized_state["timestamp"] = datetime.utcnow()
            
        # Get incident_id from state
        incident_id = serialized_state.get("incident_id")
        if not incident_id:
            raise ValueError("incident_id is required in incident state")
        
        # Get run_id from state if available (important for tracking)
        run_id = serialized_state.get("run_id")
        
        try:
            # Check if the incident already exists
            logger.debug(f"Checking if incident {incident_id} exists in database")
            existing = await self._incidents_collection.find_one({"incident_id": incident_id})
            
            if existing:
                logger.debug(f"Updating existing incident {incident_id}")
                
                # Don't overwrite metrics, logs, and events arrays if they aren't in the new state
                # This prevents wiping out collections that were built up over time
                if "metrics" not in serialized_state and "metrics" in existing:
                    serialized_state["metrics"] = existing["metrics"]
                if "logs" not in serialized_state and "logs" in existing:
                    serialized_state["logs"] = existing["logs"]
                if "events" not in serialized_state and "events" in existing:
                    serialized_state["events"] = existing["events"]
                
                # Only update tool_results if specifically requested
                if "tool_results" not in serialized_state and "tool_results" in existing and not update_tool_results:
                    serialized_state["tool_results"] = existing["tool_results"]
                elif not update_tool_results:
                    # Remove tool_results from serialized_state to avoid updating them
                    serialized_state.pop("tool_results", None)
                    
                # Update existing incident
                result = await self._incidents_collection.update_one(
                    {"incident_id": incident_id},
                    {"$set": serialized_state}
                )
                logger.info(f"Updated incident {incident_id} in MongoDB (matched: {result.matched_count}, modified: {result.modified_count})")
                
                # If metrics, logs, or events are provided in the state but empty, check if we should fetch from collections
                if run_id and ('metrics' in serialized_state and not serialized_state['metrics'] or 
                            'logs' in serialized_state and not serialized_state['logs'] or
                            'events' in serialized_state and not serialized_state['events']):
                    await self._populate_incident_with_run_data(incident_id, run_id)
                
                return str(existing["_id"])
            else:
                logger.debug(f"Inserting new incident {incident_id}")
                
                # Initialize empty arrays for metrics, logs, events, and tool_results if not present
                if "metrics" not in serialized_state:
                    serialized_state["metrics"] = []
                if "logs" not in serialized_state:
                    serialized_state["logs"] = []
                if "events" not in serialized_state:
                    serialized_state["events"] = []
                if "tool_results" not in serialized_state and update_tool_results:
                    serialized_state["tool_results"] = []
                elif not update_tool_results:
                    # Remove tool_results from serialized_state for new documents too
                    serialized_state.pop("tool_results", None)
                
                # Insert new incident
                result = await self._incidents_collection.insert_one(serialized_state)
                logger.info(f"Inserted new incident {incident_id} in MongoDB with _id: {result.inserted_id}")
                
                # If run_id is provided, populate with data from this run
                if run_id:
                    await self._populate_incident_with_run_data(incident_id, run_id)
                
                return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Failed to store incident state in MongoDB: {str(e)}")
            raise
    
    async def _populate_incident_with_run_data(self, incident_id: str, run_id: str) -> None:
        """
        Populate incident document with metrics, logs, and events from a specific run.
        
        Args:
            incident_id: ID of the incident
            run_id: Run ID to filter data
        """
        try:
            # Fetch metrics, logs, and events from collections for this run
            metrics_query = {"incident_id": incident_id, "run_id": run_id}
            metrics_cursor = self._metrics_collection.find(metrics_query)
            metrics = await metrics_cursor.to_list(length=None)
            
            logs_query = {"incident_id": incident_id, "run_id": run_id}
            logs_cursor = self._logs_collection.find(logs_query)
            logs = await logs_cursor.to_list(length=None)
            
            events_query = {"incident_id": incident_id, "run_id": run_id}
            events_cursor = self._events_collection.find(events_query)
            events = await events_cursor.to_list(length=None)
            
            # Create references for each item
            metrics_refs = [{
                "_id": str(metric["_id"]),
                "entity_guid": metric["entity_guid"],
                "metric_name": metric["metric_name"],
                "data": metric["data"],
                "timestamp": metric["timestamp"],
                "run_id": run_id
            } for metric in metrics]
            
            logs_refs = [{
                "_id": str(log["_id"]),
                "entity_guid": log["entity_guid"],
                "data": log["data"],
                "timestamp": log["timestamp"],
                "run_id": run_id
            } for log in logs]
            
            events_refs = [{
                "_id": str(event["_id"]),
                "entity_guid": event["entity_guid"],
                "data": event["data"],
                "timestamp": event["timestamp"],
                "run_id": run_id
            } for event in events]
            
            # Update the incident document with references if any found
            if metrics_refs:
                await self._incidents_collection.update_one(
                    {"incident_id": incident_id},
                    {"$push": {"metrics": {"$each": metrics_refs}}}
                )
                logger.info(f"Added {len(metrics_refs)} metrics references to incident {incident_id}")
                
            if logs_refs:
                await self._incidents_collection.update_one(
                    {"incident_id": incident_id},
                    {"$push": {"logs": {"$each": logs_refs}}}
                )
                logger.info(f"Added {len(logs_refs)} logs references to incident {incident_id}")
                
            if events_refs:
                await self._incidents_collection.update_one(
                    {"incident_id": incident_id},
                    {"$push": {"events": {"$each": events_refs}}}
                )
                logger.info(f"Added {len(events_refs)} events references to incident {incident_id}")
                
        except Exception as e:
            logger.error(f"Error populating incident with run data: {str(e)}")
            # Continue execution - this is a best-effort method
    
    async def store_incident(self, incident_data: Dict[str, Any]) -> str:
        """
        Store incident data in the incidents collection.
        
        Args:
            incident_data: Dictionary containing incident data
            
        Returns:
            ID of the stored document
        """
        # Check if the database is initialized
        if self._incidents_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot store incident data."
            logger.error(error_msg)
            raise RuntimeError(error_msg)
            
        # Ensure incident_id exists and use it as the _id for easier lookups
        incident_id = incident_data.get("incident_id")
        if not incident_id:
            raise ValueError("incident_id is required in incident_data")
            
        # Add timestamp if not present
        if "timestamp" not in incident_data:
            incident_data["timestamp"] = datetime.utcnow()
            
        try:
            # Check if the incident already exists
            logger.debug(f"Checking if incident {incident_id} exists in database")
            existing = await self._incidents_collection.find_one({"incident_id": incident_id})
            
            if existing:
                logger.debug(f"Updating existing incident {incident_id}")
                
                # Preserve existing arrays if not in the new data
                if "metrics" not in incident_data and "metrics" in existing:
                    incident_data["metrics"] = existing["metrics"]
                if "logs" not in incident_data and "logs" in existing:
                    incident_data["logs"] = existing["logs"]
                if "events" not in incident_data and "events" in existing:
                    incident_data["events"] = existing["events"]
                if "tool_results" not in incident_data and "tool_results" in existing:
                    incident_data["tool_results"] = existing["tool_results"]
                
                # Update existing incident
                result = await self._incidents_collection.update_one(
                    {"incident_id": incident_id},
                    {"$set": incident_data}
                )
                logger.info(f"Updated incident {incident_id} in MongoDB (matched: {result.matched_count}, modified: {result.modified_count})")
                return str(existing["_id"])
            else:
                logger.debug(f"Inserting new incident {incident_id}")
                
                # Initialize empty arrays for metrics, logs, events, and tool_results if not present
                if "metrics" not in incident_data:
                    incident_data["metrics"] = []
                if "logs" not in incident_data:
                    incident_data["logs"] = []
                if "events" not in incident_data:
                    incident_data["events"] = []
                if "tool_results" not in incident_data:
                    incident_data["tool_results"] = []
                
                # Insert new incident
                result = await self._incidents_collection.insert_one(incident_data)
                logger.info(f"Inserted new incident {incident_id} in MongoDB with _id: {result.inserted_id}")
                return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Failed to store incident data in MongoDB: {str(e)}")
            raise
    
    async def store_tool_result(self, 
                              incident_id: str, 
                              tool_name: str, 
                              tool_parameters: Dict[str, Any], 
                              result_data: Dict[str, Any], 
                              entity_guid: Optional[str] = None,
                              run_id: Optional[str] = None) -> str:
        """
        Store tool execution result in the tool_results collection.
        
        Args:
            incident_id: ID of the incident
            tool_name: Name of the tool executed
            tool_parameters: Parameters passed to the tool
            result_data: Result returned by the tool
            entity_guid: Optional entity GUID related to the tool execution
            run_id: Optional run ID to identify which workflow run produced this data
            
        Returns:
            ID of the stored document
        """
        # Check if the database is initialized
        if self._tool_results_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot store tool result."
            logger.error(error_msg)
            raise RuntimeError(error_msg)
        
        # Create tool result document
        tool_result = {
            "incident_id": incident_id,
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
            "result": result_data,
            "entity_guid": entity_guid,
            "timestamp": datetime.utcnow(),
            "run_id": run_id  # Add run_id to the document
        }
        
        try:
            logger.debug(f"Storing tool result for {tool_name} in incident {incident_id}")
            
            # Store directly in the tool_results collection (no longer adding to incident document)
            result = await self._tool_results_collection.insert_one(tool_result)
            result_id = str(result.inserted_id)
            
            logger.info(f"Added tool result {result_id} to tool_results collection for incident {incident_id}")
            
            return result_id
        except Exception as e:
            logger.error(f"Failed to store tool result in MongoDB: {str(e)}")
            raise
    
    async def store_metrics(self, 
                          incident_id: str, 
                          entity_guid: str, 
                          metric_name: str, 
                          metric_data: Dict[str, Any],
                          run_id: Optional[str] = None) -> str:
        """
        Store metrics data in both the metrics collection and the incident document.
        
        Args:
            incident_id: ID of the incident
            entity_guid: Entity GUID the metrics are related to
            metric_name: Name of the metric
            metric_data: Metric data
            run_id: Optional run ID to identify which workflow run produced this data
            
        Returns:
            ID of the stored metric
        """
        # Check if the database is initialized
        if self._metrics_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot store metrics."
            logger.error(error_msg)
            raise RuntimeError(error_msg)
        
        # Create metric document for the metrics collection
        metric_doc = {
            "incident_id": incident_id,
            "entity_guid": entity_guid,
            "metric_name": metric_name,
            "data": metric_data,
            "timestamp": datetime.utcnow(),
            "run_id": run_id  # Add run_id to the document
        }
        
        try:
            logger.debug(f"Storing metric {metric_name} for entity {entity_guid} in incident {incident_id}")
            
            # Store in the metrics collection
            result = await self._metrics_collection.insert_one(metric_doc)
            metric_id = str(result.inserted_id)
            
            # Create metric document for the incident document
            metric_ref = {
                "_id": metric_id,  # Store the ID for cross-reference
                "entity_guid": entity_guid,
                "metric_name": metric_name,
                "data": metric_data,
                "timestamp": datetime.utcnow(),
                "run_id": run_id
            }
            
            # Add the metric to the incident's metrics array
            await self._incidents_collection.update_one(
                {"incident_id": incident_id},
                {"$push": {"metrics": metric_ref}}
            )
            
            logger.info(f"Added metric {metric_id} to incident {incident_id}")
            
            return metric_id
        except Exception as e:
            logger.error(f"Failed to store metrics in MongoDB: {str(e)}")
            raise
    
    async def store_logs(self, 
                       incident_id: str, 
                       entity_guid: str, 
                       log_data: Dict[str, Any],
                       run_id: Optional[str] = None) -> str:
        """
        Store logs data in both the logs collection and the incident document.
        
        Args:
            incident_id: ID of the incident
            entity_guid: Entity GUID the logs are related to
            log_data: Log data
            run_id: Optional run ID to identify which workflow run produced this data
            
        Returns:
            ID of the stored log
        """
        # Check if the database is initialized
        if self._logs_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot store logs."
            logger.error(error_msg)
            raise RuntimeError(error_msg)
        
        # Create log document for the logs collection
        log_doc = {
            "incident_id": incident_id,
            "entity_guid": entity_guid,
            "data": log_data,
            "timestamp": datetime.utcnow(),
            "run_id": run_id  # Add run_id to the document
        }
        
        try:
            logger.debug(f"Storing logs for entity {entity_guid} in incident {incident_id}")
            
            # Store in the logs collection
            result = await self._logs_collection.insert_one(log_doc)
            log_id = str(result.inserted_id)
            
            # Create log document for the incident document
            log_ref = {
                "_id": log_id,  # Store the ID for cross-reference
                "entity_guid": entity_guid,
                "data": log_data,
                "timestamp": datetime.utcnow(),
                "run_id": run_id
            }
            
            # Add the log to the incident's logs array
            await self._incidents_collection.update_one(
                {"incident_id": incident_id},
                {"$push": {"logs": log_ref}}
            )
            
            logger.info(f"Added log {log_id} to incident {incident_id}")
            
            return log_id
        except Exception as e:
            logger.error(f"Failed to store logs in MongoDB: {str(e)}")
            raise
    
    async def store_events(self, 
                         incident_id: str, 
                         entity_guid: str, 
                         event_data: Dict[str, Any],
                         run_id: Optional[str] = None) -> str:
        """
        Store events data in both the events collection and the incident document.
        
        Args:
            incident_id: ID of the incident
            entity_guid: Entity GUID the events are related to
            event_data: Event data
            run_id: Optional run ID to identify which workflow run produced this data
            
        Returns:
            ID of the stored event
        """
        # Check if the database is initialized
        if self._events_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot store events."
            logger.error(error_msg)
            raise RuntimeError(error_msg)
        
        # Create event document for the events collection
        event_doc = {
            "incident_id": incident_id,
            "entity_guid": entity_guid,
            "data": event_data,
            "timestamp": datetime.utcnow(),
            "run_id": run_id  # Add run_id to the document
        }
        
        try:
            logger.debug(f"Storing events for entity {entity_guid} in incident {incident_id}")
            
            # Store in the events collection
            result = await self._events_collection.insert_one(event_doc)
            event_id = str(result.inserted_id)
            
            # Create event document for the incident document
            event_ref = {
                "_id": event_id,  # Store the ID for cross-reference
                "entity_guid": entity_guid,
                "data": event_data,
                "timestamp": datetime.utcnow(),
                "run_id": run_id
            }
            
            # Add the event to the incident's events array
            await self._incidents_collection.update_one(
                {"incident_id": incident_id},
                {"$push": {"events": event_ref}}
            )
            
            logger.info(f"Added event {event_id} to incident {incident_id}")
            
            return event_id
        except Exception as e:
            logger.error(f"Failed to store events in MongoDB: {str(e)}")
            raise
    
    async def get_tool_results_by_incident_id(self, incident_id: str) -> List[Dict[str, Any]]:
        """
        Get all tool results for a specific incident.
        
        Args:
            incident_id: ID of the incident
            
        Returns:
            List of tool result documents
        """
        # Check if the database is initialized
        if self._incidents_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot retrieve tool results."
            logger.error(error_msg)
            return []
        
        try:
            logger.debug(f"Retrieving tool results for incident {incident_id}")
            
            # Get the incident document
            incident = await self._incidents_collection.find_one({"incident_id": incident_id})
            
            # Return the tool_results array if it exists, otherwise an empty list
            if incident and "tool_results" in incident:
                tool_results = incident.get("tool_results", [])
                logger.info(f"Retrieved {len(tool_results)} tool results for incident {incident_id}")
                # Convert MongoDB documents to Pydantic-friendly format
                return [self._deserialize_from_mongodb(result) for result in tool_results]
            else:
                # If no tool_results in incident document, fall back to the tool_results collection
                cursor = self._tool_results_collection.find({"incident_id": incident_id})
                results = await cursor.to_list(length=None)
                logger.info(f"Retrieved {len(results)} tool results from separate collection for incident {incident_id}")
                # Convert MongoDB documents to Pydantic-friendly format
                return [self._deserialize_from_mongodb(result) for result in results]
        except Exception as e:
            logger.error(f"Failed to retrieve tool results from MongoDB: {str(e)}")
            return []
    
    async def get_incident_by_id(self, incident_id: str) -> Optional[Dict[str, Any]]:
        """
        Get incident by ID.
        
        Args:
            incident_id: ID of the incident
            
        Returns:
            Incident document or None if not found
        """
        # Check if the database is initialized
        if self._incidents_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot retrieve incident."
            logger.error(error_msg)
            return None
        
        try:
            logger.debug(f"Retrieving incident {incident_id}")
            result = await self._incidents_collection.find_one({"incident_id": incident_id})
            if result:
                logger.info(f"Retrieved incident {incident_id}")
                # Convert MongoDB document to Pydantic-friendly format
                return self._deserialize_from_mongodb(result)
            else:
                logger.warning(f"Incident {incident_id} not found")
            return None
        except Exception as e:
            logger.error(f"Failed to retrieve incident from MongoDB: {str(e)}")
            return None
    
    async def get_metrics_by_incident_id(self, incident_id: str) -> List[Dict[str, Any]]:
        """
        Get all metrics for a specific incident.
        
        Args:
            incident_id: ID of the incident
            
        Returns:
            List of metric documents
        """
        # Check if the database is initialized
        if self._incidents_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot retrieve metrics."
            logger.error(error_msg)
            return []
        
        try:
            logger.debug(f"Retrieving metrics for incident {incident_id}")
            
            # Get the incident document
            incident = await self._incidents_collection.find_one({"incident_id": incident_id})
            
            # Return the metrics array if it exists, otherwise an empty list
            if incident and "metrics" in incident:
                metrics = incident.get("metrics", [])
                logger.info(f"Retrieved {len(metrics)} metrics for incident {incident_id}")
                # Convert MongoDB documents to Pydantic-friendly format
                return [self._deserialize_from_mongodb(metric) for metric in metrics]
            else:
                # If no metrics in incident document, fall back to the metrics collection
                cursor = self._metrics_collection.find({"incident_id": incident_id})
                results = await cursor.to_list(length=None)
                logger.info(f"Retrieved {len(results)} metrics from separate collection for incident {incident_id}")
                # Convert MongoDB documents to Pydantic-friendly format
                return [self._deserialize_from_mongodb(result) for result in results]
        except Exception as e:
            logger.error(f"Failed to retrieve metrics from MongoDB: {str(e)}")
            return []
    
    async def get_logs_by_incident_id(self, incident_id: str) -> List[Dict[str, Any]]:
        """
        Get all logs for a specific incident.
        
        Args:
            incident_id: ID of the incident
            
        Returns:
            List of log documents
        """
        # Check if the database is initialized
        if self._incidents_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot retrieve logs."
            logger.error(error_msg)
            return []
        
        try:
            logger.debug(f"Retrieving logs for incident {incident_id}")
            
            # Get the incident document
            incident = await self._incidents_collection.find_one({"incident_id": incident_id})
            
            # Return the logs array if it exists, otherwise an empty list
            if incident and "logs" in incident:
                logs = incident.get("logs", [])
                logger.info(f"Retrieved {len(logs)} logs for incident {incident_id}")
                # Convert MongoDB documents to Pydantic-friendly format
                return [self._deserialize_from_mongodb(log) for log in logs]
            else:
                # If no logs in incident document, fall back to the logs collection
                cursor = self._logs_collection.find({"incident_id": incident_id})
                results = await cursor.to_list(length=None)
                logger.info(f"Retrieved {len(results)} logs from separate collection for incident {incident_id}")
                # Convert MongoDB documents to Pydantic-friendly format
                return [self._deserialize_from_mongodb(result) for result in results]
        except Exception as e:
            logger.error(f"Failed to retrieve logs from MongoDB: {str(e)}")
            return []
    
    async def get_events_by_incident_id(self, incident_id: str) -> List[Dict[str, Any]]:
        """
        Get all events for a specific incident.
        
        Args:
            incident_id: ID of the incident
            
        Returns:
            List of event documents
        """
        # Check if the database is initialized
        if self._incidents_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot retrieve events."
            logger.error(error_msg)
            return []
        
        try:
            logger.debug(f"Retrieving events for incident {incident_id}")
            
            # Get the incident document
            incident = await self._incidents_collection.find_one({"incident_id": incident_id})
            
            # Return the events array if it exists, otherwise an empty list
            if incident and "events" in incident:
                events = incident.get("events", [])
                logger.info(f"Retrieved {len(events)} events for incident {incident_id}")
                # Convert MongoDB documents to Pydantic-friendly format
                return [self._deserialize_from_mongodb(event) for event in events]
            else:
                # If no events in incident document, fall back to the events collection
                cursor = self._events_collection.find({"incident_id": incident_id})
                results = await cursor.to_list(length=None)
                logger.info(f"Retrieved {len(results)} events from separate collection for incident {incident_id}")
                # Convert MongoDB documents to Pydantic-friendly format
                return [self._deserialize_from_mongodb(result) for result in results]
        except Exception as e:
            logger.error(f"Failed to retrieve events from MongoDB: {str(e)}")
            return []
    
    async def get_tool_result_by_id(self, result_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a tool result by its ID from the tool_results collection.
        
        Args:
            result_id: ID of the tool result to retrieve
            
        Returns:
            The tool result or None if not found
        """
        # Check if the database is initialized
        if self._tool_results_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot retrieve tool result."
            logger.error(error_msg)
            raise RuntimeError(error_msg)
            
        try:
            # Try to convert to ObjectId (for old-style IDs)
            try:
                object_id = ObjectId(result_id)
                result = await self._tool_results_collection.find_one({"_id": object_id})
            except Exception:
                # If conversion fails, try as a string ID
                result = await self._tool_results_collection.find_one({"_id": result_id})
                    
            if result:
                return self._deserialize_from_mongodb(result)
            else:
                logger.warning(f"Tool result with ID {result_id} not found")
                return None
        except Exception as e:
            logger.error(f"Error retrieving tool result by ID: {str(e)}")
            return None
    
    async def store_rca_results(self, incident_id: str, rca_results: Dict[str, Any], timestamp: datetime = None) -> str:
        """
        Store or update RCA results for an incident.
        
        Args:
            incident_id: ID of the incident 
            rca_results: Complete RCA results as a dictionary
            timestamp: Optional timestamp (defaults to current time)
            
        Returns:
            ID of the updated document
        """
        # Check if the database is initialized
        if self._incidents_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot store RCA results."
            logger.error(error_msg)
            raise RuntimeError(error_msg)
            
        # Serialize the RCA results
        serialized_rca = self._serialize_for_mongodb(rca_results)
        
        # Set timestamp if not provided
        if timestamp is None:
            timestamp = datetime.utcnow()
        
        try:
            # Update the incident document with the RCA results
            existing = await self._incidents_collection.find_one({"incident_id": incident_id})
            
            if existing:
                logger.debug(f"Updating incident {incident_id} with RCA results")
                
                # Update existing incident with RCA details
                result = await self._incidents_collection.update_one(
                    {"incident_id": incident_id},
                    {"$set": {
                        "rca_details": serialized_rca,
                        "root_cause": serialized_rca.get("primary_root_cause", "Unknown"),
                        "last_updated": timestamp
                    }}
                )
                
                logger.info(f"Updated incident {incident_id} with RCA results (matched: {result.matched_count}, modified: {result.modified_count})")
                return str(existing["_id"])
            else:
                # If incident doesn't exist, create a new one
                logger.warning(f"Incident {incident_id} not found for RCA update, creating new document")
                
                # Create a minimal incident document with RCA details
                incident_doc = {
                    "incident_id": incident_id,
                    "rca_details": serialized_rca,
                    "root_cause": serialized_rca.get("primary_root_cause", "Unknown"),
                    "timestamp": timestamp,
                    "last_updated": timestamp
                }
                
                result = await self._incidents_collection.insert_one(incident_doc)
                logger.info(f"Created new incident {incident_id} with RCA results, _id: {result.inserted_id}")
                return str(result.inserted_id)
                
        except Exception as e:
            logger.error(f"Failed to store RCA results in MongoDB: {str(e)}")
            raise
            
    # Debug method to test MongoDB connection
    async def test_connection(self) -> Dict[str, Any]:
        """
        Test the MongoDB connection and return diagnostic information.
        
        Returns:
            Dictionary with connection status and diagnostic information
        """
        result = {
            "connection_success": False,
            "initialized": self._initialized,
            "database_name": os.getenv("MONGODB_DATABASE_NAME", "incident_management"),
            "collections_available": {},
            "error": None
        }
        
        try:
            # Try to get server info
            server_info = await self._client.server_info()
            result["connection_success"] = True
            result["server_version"] = server_info.get("version", "unknown")
            
            # Check collections
            collections = await self._db.list_collection_names()
            result["collections"] = collections
            
            # Check if our collections are initialized
            result["collections_available"] = {
                "incidents": self._incidents_collection is not None,
                "tool_results": self._tool_results_collection is not None,
                "metrics": self._metrics_collection is not None,
                "logs": self._logs_collection is not None,
                "events": self._events_collection is not None
            }
            
            # Get counts for each collection
            if self._incidents_collection:
                result["incidents_count"] = await self._incidents_collection.count_documents({})
                # Count incidents with run_id
                result["incidents_with_run_id"] = await self._incidents_collection.count_documents({"run_id": {"$exists": True}})
            if self._tool_results_collection:
                result["tool_results_count"] = await self._tool_results_collection.count_documents({})
                # Count tool results with run_id
                result["tool_results_with_run_id"] = await self._tool_results_collection.count_documents({"run_id": {"$exists": True}})
            if self._metrics_collection:
                result["metrics_count"] = await self._metrics_collection.count_documents({})
                # Count metrics with run_id  
                result["metrics_with_run_id"] = await self._metrics_collection.count_documents({"run_id": {"$exists": True}})
            if self._logs_collection:
                result["logs_count"] = await self._logs_collection.count_documents({})
                # Count logs with run_id
                result["logs_with_run_id"] = await self._logs_collection.count_documents({"run_id": {"$exists": True}})
            if self._events_collection:
                result["events_count"] = await self._events_collection.count_documents({})
                # Count events with run_id
                result["events_with_run_id"] = await self._events_collection.count_documents({"run_id": {"$exists": True}})
                
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"MongoDB connection test failed: {str(e)}")
            
        return result
        
    async def migrate_to_consolidated_structure(self) -> Dict[str, Any]:
        """
        Migrate data from separate collections to consolidated incident documents.
        
        This is a one-time migration that moves existing data from separate collections
        into the consolidated incident document structure. This ensures all historical
        data is available in the new format.
        
        Returns:
            Migration statistics
        """
        stats = {
            "incidents_processed": 0,
            "metrics_migrated": 0,
            "logs_migrated": 0,
            "events_migrated": 0,
            "tool_results_migrated": 0,
            "errors": []
        }
        
        try:
            logger.info("Starting migration to consolidated incident structure")
            
            # Get all incidents
            cursor = self._incidents_collection.find({})
            incidents = await cursor.to_list(length=None)
            stats["incidents_processed"] = len(incidents)
            
            for incident in incidents:
                incident_id = incident.get("incident_id")
                if not incident_id:
                    stats["errors"].append(f"Incident with _id {incident.get('_id')} has no incident_id")
                    continue
                    
                try:
                    # Initialize arrays if they don't exist
                    if "metrics" not in incident:
                        # Fetch metrics from metrics collection
                        metrics_cursor = self._metrics_collection.find({"incident_id": incident_id})
                        metrics = await metrics_cursor.to_list(length=None)
                        
                        if metrics:
                            # Update the incident with the metrics
                            await self._incidents_collection.update_one(
                                {"incident_id": incident_id},
                                {"$set": {"metrics": metrics}}
                            )
                            stats["metrics_migrated"] += len(metrics)
                            logger.info(f"Migrated {len(metrics)} metrics for incident {incident_id}")
                    
                    if "logs" not in incident:
                        # Fetch logs from logs collection
                        logs_cursor = self._logs_collection.find({"incident_id": incident_id})
                        logs = await logs_cursor.to_list(length=None)
                        
                        if logs:
                            # Update the incident with the logs
                            await self._incidents_collection.update_one(
                                {"incident_id": incident_id},
                                {"$set": {"logs": logs}}
                            )
                            stats["logs_migrated"] += len(logs)
                            logger.info(f"Migrated {len(logs)} logs for incident {incident_id}")
                    
                    if "events" not in incident:
                        # Fetch events from events collection
                        events_cursor = self._events_collection.find({"incident_id": incident_id})
                        events = await events_cursor.to_list(length=None)
                        
                        if events:
                            # Update the incident with the events
                            await self._incidents_collection.update_one(
                                {"incident_id": incident_id},
                                {"$set": {"events": events}}
                            )
                            stats["events_migrated"] += len(events)
                            logger.info(f"Migrated {len(events)} events for incident {incident_id}")
                    
                    if "tool_results" not in incident:
                        # Fetch tool results from tool_results collection
                        tool_results_cursor = self._tool_results_collection.find({"incident_id": incident_id})
                        tool_results = await tool_results_cursor.to_list(length=None)
                        
                        if tool_results:
                            # Update the incident with the tool results
                            await self._incidents_collection.update_one(
                                {"incident_id": incident_id},
                                {"$set": {"tool_results": tool_results}}
                            )
                            stats["tool_results_migrated"] += len(tool_results)
                            logger.info(f"Migrated {len(tool_results)} tool results for incident {incident_id}")
                    
                except Exception as e:
                    error_msg = f"Error migrating data for incident {incident_id}: {str(e)}"
                    stats["errors"].append(error_msg)
                    logger.error(error_msg)
            
            logger.info("Migration to consolidated incident structure completed")
        except Exception as e:
            stats["errors"].append(f"Global migration error: {str(e)}")
            logger.error(f"Migration failed: {str(e)}")
        
        return stats

    async def store_topology(self, product: str, topology_data: Dict[str, Any], cluster_id: Optional[str] = None, source: str = "direct") -> str:
        """
        Store topology data in the topology collection.
        
        Args:
            product: The product (MDM or Neurons)
            topology_data: Topology data to store (nodes and links)
            cluster_id: Optional cluster ID
            source: Source of the data (direct, fallback, etc.)
            
        Returns:
            ID of the stored document
        """
        # Check if the database is initialized
        if self._topology_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot store topology data."
            logger.error(error_msg)
            raise RuntimeError(error_msg)
        
        # Create document to store
        doc = {
            "product": product.lower(),
            "timestamp": datetime.utcnow(),
            "links": topology_data.get("links", []),
            "source": source
        }
        
        # Add cluster_id if provided
        if cluster_id and cluster_id != "unknown":
            doc["cluster_id"] = cluster_id
            
        try:
            logger.debug(f"Storing topology data for {product} {cluster_id or 'unknown'}")
            
            # Store in the topology collection
            result = await self._topology_collection.insert_one(doc)
            logger.info(f"Stored topology data with ID {result.inserted_id}")
            
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Failed to store topology data in MongoDB: {str(e)}")
            raise

    async def get_topology(self, product: str, cluster_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get topology data from the topology collection.
        
        Args:
            product: The product (MDM or Neurons)
            cluster_id: Optional cluster ID
            
        Returns:
            Topology data (nodes and links)
        """
        # Check if the database is initialized
        if self._topology_collection is None:
            error_msg = "MongoDB connection not initialized. Cannot get topology data."
            logger.error(error_msg)
            return {"nodes": [], "links": []}
        
        try:
            logger.debug(f"Getting topology data for {product} {cluster_id or 'unknown'}")
            
            # Query to find topology data
            query = {
                "product": product.lower()
            }
            
            # Add cluster_id to query if provided
            if cluster_id and cluster_id != "unknown":
                query["cluster_id"] = cluster_id
                
            # Find the most recent topology document for this product/cluster
            cursor = self._topology_collection.find(query).sort("timestamp", -1).limit(1)
            results = await cursor.to_list(length=1)
            
            if results:
                topology_doc = self._deserialize_from_mongodb(results[0])
                logger.info(f"Retrieved topology data with {len(topology_doc.get('nodes', []))} nodes")
                return {
                    "nodes": topology_doc.get("nodes", []),
                    "links": topology_doc.get("links", [])
                }
            else:
                logger.warning(f"No topology data found for {product} {cluster_id or 'unknown'}")
                return {"nodes": [], "links": []}
        except Exception as e:
            logger.error(f"Failed to get topology data from MongoDB: {str(e)}")
            return {"nodes": [], "links": []}

def get_mongodb_service():
    """Get the singleton instance of the MongoDB service."""
    return MongoDBService() 