"""
Runbook Service.

This service provides a unified interface for working with runbooks, 
handling both database and in-memory lookups, and providing caching for performance.
"""

import os
import re
import json
import yaml
from loguru import logger
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timedelta, timezone
from functools import lru_cache

import dotenv
from lib.new_relic.base import UTC

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logger = logger.bind(name="runbook_service")


class RunbookService:
    """
    Service for managing runbooks.
    
    This service provides a unified interface for working with runbooks,
    whether they are stored in the database or loaded from YAML files.
    It also provides caching for performance.
    """
    
    def __init__(self, config_path: str = "ai_incident_manager/config/runbooks.yaml", refresh_cache_interval: int = 300):
        """
        Initialize the runbook service.
        
        Args:
            config_path: Path to the YAML configuration file (default: "ai_incident_manager/config/runbooks.yaml")
            refresh_cache_interval: How often to refresh the cache in seconds (default: 5 minutes)
        """
        self.config_path = config_path
        self.refresh_cache_interval = refresh_cache_interval
        self.last_cache_refresh = datetime.now() - timedelta(seconds=refresh_cache_interval + 1)
        self._cached_runbooks = {}
        self._cached_runbooks_by_category = {}
        self._cached_runbooks_by_entity = {}
        
        # Tool configuration registry
        self._tool_configs = {
            "get_pod_logs": {
                "method": "get_pod_logs",
                "required_params": ["pod_name", "namespace"],
                "optional_params": ["container", "tail_lines", "since_time_ms", "until_time_ms"],
                "entity_params": {
                    "pod_name": "entity_name",
                    "namespace": "namespace"
                }
            },
            "get_pod_metrics": {
                "method": "get_pod_metrics",
                "required_params": ["pod_name", "namespace"],
                "optional_params": ["since_time_ms", "until_time_ms"],
                "entity_params": {
                    "pod_name": "entity_name",
                    "namespace": "namespace"
                }
            },
            "get_entity_metrics": {
                "method": "get_entity_metrics",
                "required_params": ["entity_guid"],
                "optional_params": ["metric_names", "since_time_ms", "until_time_ms"],
                "entity_params": {
                    "entity_guid": "entity_guid"
                }
            },
            "get_entity_logs": {
                "method": "get_entity_logs",
                "required_params": ["entity_guid"],
                "optional_params": ["query", "since_time_ms", "until_time_ms"],
                "entity_params": {
                    "entity_guid": "entity_guid"
                }
            },
            "get_entity_details": {
                "method": "get_entity_details",
                "required_params": ["entity_guid"],
                "optional_params": [],
                "entity_params": {
                    "entity_guid": "entity_guid"
                }
            },
            "get_related_entities": {
                "method": "get_related_entities",
                "required_params": ["entity_guid"],
                "optional_params": [],
                "entity_params": {
                    "entity_guid": "entity_guid"
                }
            },
            "collect_kubernetes_events": {
                "method": "collect_kubernetes_events",
                "required_params": ["entity_name", "entity_type"],
                "optional_params": ["cluster_name", "since_time_ms", "until_time_ms"],
                "entity_params": {
                    "entity_name": "entity_name",
                    "entity_type": "entity_type",
                    "cluster_name": "cluster_name"
                }
            },
            "analyze_entity": {
                "method": "analyze_entity",
                "required_params": ["entity_guid"],
                "optional_params": ["since_time_ms", "until_time_ms", "product", "region"],
                "entity_params": {
                    "entity_guid": "entity_guid"
                }
            },
            "get_deployment_status": {
                "method": "get_deployment_status",
                "required_params": ["deployment_name"],
                "optional_params": ["namespace", "cluster_name", "include_conditions", "since_time_ms", "until_time_ms"],
                "entity_params": {
                    "namespace": "namespace",
                    "cluster_name": "cluster_name"
                }
            },
            "get_deployment_pods": {
                "method": "get_deployment_pods",
                "required_params": ["deployment_name"],
                "optional_params": ["namespace", "cluster_name", "include_status", "include_conditions", "since_time_ms", "until_time_ms"],
                "entity_params": {
                    "namespace": "namespace",
                    "cluster_name": "cluster_name"
                }
            },
            "analyze_deployment_resources": {
                "method": "analyze_deployment_resources",
                "required_params": ["deployment_name"],
                "optional_params": ["namespace", "cluster_name", "include_pods", "time_window_minutes", "include_recommendations", "since_time_ms", "until_time_ms"],
                "entity_params": {
                    "namespace": "namespace",
                    "cluster_name": "cluster_name"
                }
            },
            "get_deployment_history": {
                "method": "get_deployment_history",
                "required_params": ["deployment_name"],
                "optional_params": ["namespace", "cluster_name", "revisions_to_show", "include_diff", "since_time_ms", "until_time_ms"],
                "entity_params": {
                    "namespace": "namespace",
                    "cluster_name": "cluster_name"
                }
            },
            "check_nodes_status": {
                "method": "check_nodes_status",
                "required_params": ["nodes_selector"],
                "optional_params": ["cluster_name", "include_metrics", "since_time_ms", "until_time_ms"],
                "entity_params": {
                    "cluster_name": "cluster_name"
                }
            }
        }
        
        # Initial cache load
        self._refresh_cache()
    
    def _load_yaml_runbooks(self) -> List[Dict[str, Any]]:
        """
        Load runbooks from the YAML file.
        
        Returns:
            List of runbook dictionaries
        """
        try:
            if not os.path.exists(self.config_path):
                logger.warning(f"Runbook config file not found: {self.config_path}")
                return []
                
            with open(self.config_path, 'r') as file:
                config = yaml.safe_load(file)
                
            # Check which format we're using
            if config and 'runbooks' in config:
                runbooks = config['runbooks']
                logger.info(f"Loaded {len(runbooks)} runbooks from config")
                return runbooks
            else:
                logger.warning(f"No runbooks found in config file: {self.config_path}")
                return []
                
        except Exception as e:
            logger.error(f"Error loading runbooks from YAML: {str(e)}")
            return []
    
    def _refresh_cache(self) -> None:
        """Refresh the runbook cache."""
        now = datetime.now()
        
        # Only refresh if the cache interval has elapsed
        if (now - self.last_cache_refresh).total_seconds() < self.refresh_cache_interval:
            return
            
        logger.debug("Refreshing runbook cache")
        
        # Reset caches
        self._cached_runbooks = {}
        self._cached_runbooks_by_category = {}
        self._cached_runbooks_by_entity = {}
        self._cached_runbooks_by_relationship = {}  # New cache for relationships
        
        # Add runbooks from YAML
        yaml_runbooks = self._load_yaml_runbooks()
        for yaml_runbook in yaml_runbooks:
            runbook_id = yaml_runbook.get("id")
            if not runbook_id:
                logger.warning(f"Skipping runbook with no ID: {yaml_runbook}")
                continue
                
            # Store in the main cache
            self._cached_runbooks[runbook_id] = yaml_runbook
            
            # Index by applicable categories
            for category in yaml_runbook.get("applicable_categories", []):
                if category not in self._cached_runbooks_by_category:
                    self._cached_runbooks_by_category[category] = []
                self._cached_runbooks_by_category[category].append(runbook_id)
                
            # Index by applicable entity types
            for entity_type in yaml_runbook.get("applicable_entities", []):
                if entity_type not in self._cached_runbooks_by_entity:
                    self._cached_runbooks_by_entity[entity_type] = []
                self._cached_runbooks_by_entity[entity_type].append(runbook_id)
            
            # Index by applicable relationships
            for relationship in yaml_runbook.get("applicable_relationships", []):
                source_type = relationship.get("source_type")
                target_type = relationship.get("target_type")
                relationship_type = relationship.get("relationship")
                
                if not source_type or not target_type or not relationship_type:
                    continue
                
                rel_key = f"{source_type}:{relationship_type}:{target_type}"
                if rel_key not in self._cached_runbooks_by_relationship:
                    self._cached_runbooks_by_relationship[rel_key] = []
                self._cached_runbooks_by_relationship[rel_key].append(runbook_id)
                
        logger.info(f"Runbook cache refreshed with {len(self._cached_runbooks)} runbooks")
        self.last_cache_refresh = now
    
    def get_runbook(self, runbook_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a runbook by ID.
        
        Args:
            runbook_id: ID of the runbook to retrieve
            
        Returns:
            Runbook dictionary or None if not found
        """
        self._refresh_cache()
        return self._cached_runbooks.get(runbook_id)
    
    def get_runbooks_for_category(self, category: str) -> List[Dict[str, Any]]:
        """
        Get all runbooks applicable to a specific alert category.
        
        Args:
            category: Alert category
            
        Returns:
            List of runbooks dictionaries
        """
        self._refresh_cache()
        runbook_ids = self._cached_runbooks_by_category.get(category, [])
        return [self._cached_runbooks[rid] for rid in runbook_ids if rid in self._cached_runbooks]
    
    def get_runbooks_for_entity(self, entity_type: str) -> List[Dict[str, Any]]:
        """
        Get all runbooks applicable to a specific entity type.
        
        Args:
            entity_type: Entity type
            
        Returns:
            List of runbooks dictionaries
        """
        self._refresh_cache()
        runbook_ids = self._cached_runbooks_by_entity.get(entity_type, [])
        return [self._cached_runbooks[rid] for rid in runbook_ids if rid in self._cached_runbooks]
    
    def get_recommended_runbooks(self, alert_category: str, entity_types: List[str], 
                                max_results: int = 3) -> List[Dict[str, Any]]:
        """
        Get recommended runbooks based on alert category and entity types.
        
        Args:
            alert_category: Alert category
            entity_types: List of entity types
            max_results: Maximum number of results to return (default: 3)
            
        Returns:
            List of recommended runbooks, ordered by priority
        """
        self._refresh_cache()
        
        # Get runbooks by category
        category_runbooks_ids = set(self._cached_runbooks_by_category.get(alert_category, []))
        
        # Get runbooks by entity type
        entity_runbooks_ids = set()
        for entity_type in entity_types:
            entity_runbooks_ids.update(self._cached_runbooks_by_entity.get(entity_type, []))
        
        # Find runbooks that match both category and entity type
        matching_runbook_ids = category_runbooks_ids.intersection(entity_runbooks_ids)
        
        # If no exact matches, use category matches
        if not matching_runbook_ids:
            matching_runbook_ids = category_runbooks_ids
            
        # If still no matches, use entity type matches
        if not matching_runbook_ids:
            matching_runbook_ids = entity_runbooks_ids
        
        # Get runbooks and sort by priority
        matching_runbooks = [
            self._cached_runbooks[rid] for rid in matching_runbook_ids 
            if rid in self._cached_runbooks
        ]
        
        # Sort by priority (lower number = higher priority)
        matching_runbooks.sort(key=lambda r: r.get("priority", 10))
        
        return matching_runbooks[:max_results]
    
    def get_runbook_execution_plan(self, runbook_ids: List[str]) -> List[Dict[str, Any]]:
        """
        Create an execution plan for multiple runbooks, accounting for prerequisites.
        
        Args:
            runbook_ids: List of runbook IDs to execute
            
        Returns:
            Ordered list of runbooks to execute
        """
        self._refresh_cache()
        
        # Collect all runbooks including prerequisites
        all_runbooks = {}
        runbook_dependencies = {}
        runbooks_to_process = set(runbook_ids)
        
        # Build dependency graph
        while runbooks_to_process:
            runbook_id = runbooks_to_process.pop()
            if runbook_id not in self._cached_runbooks:
                logger.warning(f"Runbook not found: {runbook_id}")
                continue
                
            runbook = self._cached_runbooks[runbook_id]
            all_runbooks[runbook_id] = runbook
            
            # Add prerequisites
            prereqs = runbook.get("prerequisites", [])
            runbook_dependencies[runbook_id] = set(prereqs)
            
            # Add prerequisites to processing list if not already processed
            for prereq in prereqs:
                if prereq not in all_runbooks and prereq not in runbooks_to_process:
                    runbooks_to_process.add(prereq)
        
        # Topological sort
        execution_plan = []
        visited = set()
        temp_visited = set()
        
        def visit(runbook_id):
            if runbook_id in visited:
                return
            if runbook_id in temp_visited:
                logger.warning(f"Circular dependency detected in runbooks: {runbook_id}")
                return
                
            temp_visited.add(runbook_id)
            
            # Visit prerequisites first
            for prereq in runbook_dependencies.get(runbook_id, set()):
                visit(prereq)
                
            temp_visited.remove(runbook_id)
            visited.add(runbook_id)
            if runbook_id in all_runbooks:
                execution_plan.append(all_runbooks[runbook_id])
        
        # Start with requested runbooks
        for runbook_id in runbook_ids:
            if runbook_id in all_runbooks:
                visit(runbook_id)
                
        return execution_plan
    
    def get_all_runbooks(self) -> List[Dict[str, Any]]:
        """Get all available runbooks."""
        self._refresh_cache()
        return list(self._cached_runbooks.values())
        
    def get_runbooks_for_entity_relationships(
        self, 
        entity_relationships: Dict[str, Any],
        alert_category: str,
        max_results_per_entity: int = 2
    ) -> List[Dict[str, Any]]:
        """
        Get recommended runbooks based on entity relationships.
        
        This method analyzes primary and related entities to find the most appropriate
        runbooks for the entire entity relationship graph, prioritizing runbooks that
        are specifically designed for the relationships that exist in the graph.
        
        Args:
            entity_relationships: Entity relationships structure from entity_relationship_service
            alert_category: The alert category
            max_results_per_entity: Maximum number of results per entity type
            
        Returns:
            List of runbooks with execution metadata, ordered by priority
        """
        self._refresh_cache()
        
        primary_entity = entity_relationships.get("primary_entity", {})
        related_entities = entity_relationships.get("related_entities", [])
        
        # Initialize the list of runbooks to return
        result_runbooks = []
        added_runbook_ids = set()
        
        # 1. First, get runbooks that match both the alert category and primary entity type
        primary_entity_type = primary_entity.get("type")
        if primary_entity_type and alert_category:
            category_runbooks_ids = set(self._cached_runbooks_by_category.get(alert_category, []))
            entity_runbooks_ids = set(self._cached_runbooks_by_entity.get(primary_entity_type, []))
            
            # Find matching runbooks
            matching_runbook_ids = category_runbooks_ids.intersection(entity_runbooks_ids)
            
            # Add matching runbooks with metadata
            for runbook_id in matching_runbook_ids:
                if runbook_id in self._cached_runbooks:
                    runbook = self._cached_runbooks[runbook_id].copy()
                    runbook["entity_guid"] = primary_entity.get("guid")
                    runbook["entity_type"] = primary_entity_type
                    runbook["is_primary"] = True
                    runbook["source"] = "primary_entity_match"
                    result_runbooks.append(runbook)
                    added_runbook_ids.add(runbook_id)
        
        # 2. Next, get runbooks that match specific relationships
        for related_entity in related_entities:
            source_type = primary_entity.get("type")
            target_type = related_entity.get("type")
            relationship_type = related_entity.get("relationship")
            
            if not source_type or not target_type or not relationship_type:
                continue
                
            rel_key = f"{source_type}:{relationship_type}:{target_type}"
            relationship_runbooks_ids = set(self._cached_runbooks_by_relationship.get(rel_key, []))
            
            # Filter by alert category if applicable
            if alert_category:
                category_runbooks_ids = set(self._cached_runbooks_by_category.get(alert_category, []))
                relationship_runbooks_ids = relationship_runbooks_ids.intersection(category_runbooks_ids)
            
            # Add relationship-specific runbooks with metadata
            for runbook_id in relationship_runbooks_ids:
                if runbook_id in self._cached_runbooks and runbook_id not in added_runbook_ids:
                    runbook = self._cached_runbooks[runbook_id].copy()
                    runbook["entity_guid"] = related_entity.get("guid")
                    runbook["entity_type"] = related_entity.get("type")
                    runbook["is_primary"] = False
                    runbook["relationship"] = relationship_type
                    runbook["related_to_guid"] = primary_entity.get("guid")
                    runbook["related_to_type"] = primary_entity.get("type")
                    runbook["source"] = "relationship_match"
                    runbook["importance"] = related_entity.get("importance", "medium")
                    result_runbooks.append(runbook)
                    added_runbook_ids.add(runbook_id)
            
            # Also add runbooks that apply to the related entity type
            # Limit per entity to avoid too many runbooks
            entity_runbooks_ids = set(self._cached_runbooks_by_entity.get(target_type, []))
            if alert_category:
                entity_runbooks_ids = entity_runbooks_ids.intersection(category_runbooks_ids)
                
            # Count how many we've already added for this entity type
            added_for_this_entity = sum(1 for rb in result_runbooks 
                                     if rb.get("entity_type") == target_type and rb.get("source") != "relationship_match")
            
            # Add entity-specific runbooks up to the limit
            for runbook_id in list(entity_runbooks_ids)[:max_results_per_entity - added_for_this_entity]:
                if runbook_id in self._cached_runbooks and runbook_id not in added_runbook_ids:
                    runbook = self._cached_runbooks[runbook_id].copy()
                    runbook["entity_guid"] = related_entity.get("guid")
                    runbook["entity_type"] = related_entity.get("type")
                    runbook["is_primary"] = False
                    runbook["relationship"] = relationship_type
                    runbook["related_to_guid"] = primary_entity.get("guid")
                    runbook["related_to_type"] = primary_entity.get("type")
                    runbook["source"] = "related_entity_match"
                    runbook["importance"] = related_entity.get("importance", "medium")
                    result_runbooks.append(runbook)
                    added_runbook_ids.add(runbook_id)
        
        # 3. If we have too few results, add more category matches
        if len(result_runbooks) < 3 and alert_category:
            category_runbooks_ids = set(self._cached_runbooks_by_category.get(alert_category, []))
            
            # Add up to 3 more runbooks
            for runbook_id in list(category_runbooks_ids)[:3]:
                if runbook_id in self._cached_runbooks and runbook_id not in added_runbook_ids:
                    runbook = self._cached_runbooks[runbook_id].copy()
                    runbook["entity_guid"] = primary_entity.get("guid")
                    runbook["entity_type"] = primary_entity_type
                    runbook["is_primary"] = True
                    runbook["source"] = "category_match"
                    result_runbooks.append(runbook)
                    added_runbook_ids.add(runbook_id)
        
        # Sort by priority, then by importance
        def sort_key(runbook):
            # Lower priority number = higher priority
            priority = runbook.get("priority", 10)
            
            # Importance order: high > medium > low
            importance_value = {"high": 1, "medium": 2, "low": 3}
            importance = importance_value.get(runbook.get("importance", "medium"), 2)
            
            # Source order: relationship_match > primary_entity_match > related_entity_match > category_match
            source_value = {
                "relationship_match": 1,
                "primary_entity_match": 2,
                "related_entity_match": 3,
                "category_match": 4
            }
            source = source_value.get(runbook.get("source", ""), 5)
            
            return (priority, source, importance)
            
        result_runbooks.sort(key=sort_key)
        
        return result_runbooks
        
    @staticmethod
    def extract_entity_details(entity_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract important details from an entity's information.
        
        Args:
            entity_details: Raw entity details from New Relic
            
        Returns:
            Dictionary with extracted entity details
        """
        if not entity_details:
            return {
                "found": False
            }
            
        # Basic information
        result = {
            "found": True,
            "entity_guid": entity_details.get("guid"),
            "entity_name": entity_details.get("name"),
            "entity_type": entity_details.get("type"),
            "account_id": entity_details.get("account_id"),
            "cluster_name": None,
            "namespace": None,
            "tags": {}
        }
        
        # Extract information from tags
        tags = entity_details.get("tags", [])
        
        if isinstance(tags, list):
            for tag in tags:
                if isinstance(tag, dict):
                    tag_key = tag.get("key")
                    if not tag_key:
                        continue
                        
                    tag_values = tag.get("values", [])
                    if not tag_values or not isinstance(tag_values, list):
                        continue
                        
                    # Extract specific important tags
                    if tag_key in ["clusterName", "k8s.clusterName"]:
                        result["cluster_name"] = tag_values[0]
                    elif tag_key in ["namespace", "k8s.namespace.name"]:
                        result["namespace"] = tag_values[0]
                        
                    # Store all tags for reference
                    result["tags"][tag_key] = tag_values
        
        return result
        
    def get_tool_execution_params(
        self,
        tool_name: str,
        entity_info: Dict[str, Any],
        parameters: Dict[str, Any],
        state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process dynamic parameters for tool execution.
        
        This method processes any dynamic parameters with expressions
        in curly braces, evaluating them with the given context.
        
        Args:
            tool_name: Name of the tool to execute
            entity_info: Entity information dictionary
            parameters: Tool-specific parameters
            state: Full workflow state containing all context
            
        Returns:
            Dictionary with processed parameters
        """
        # Common parameters from entity info
        common_params = {
            "entity_guid": entity_info.get("entity_guid"),
            "entity_name": entity_info.get("entity_name"),
            "entity_type": entity_info.get("entity_type"),
            "cluster_name": entity_info.get("cluster_name"),
            "namespace": entity_info.get("namespace")
        }
        
        # Extract time window if available in the state
        time_window_ms = {}
        if state and "since_time_ms" in state and "until_time_ms" in state:
            time_window_ms = {
                "since_time_ms": state["since_time_ms"],
                "until_time_ms": state["until_time_ms"]
            }
        
        # Process dynamic parameters in the parameters dictionary
        processed_params = {}
        for key, value in parameters.items():
            if isinstance(value, str) and value.startswith("{") and value.endswith("}"):
                # This is a dynamic parameter that needs to be evaluated
                try:
                    # Extract the expression
                    expr = value[1:-1].strip()
                    
                    # Create safe locals with limited variables
                    safe_locals = {
                        "datetime": datetime,
                        "timedelta": timedelta,
                        "max": max,
                        "min": min,
                        "entity_info": entity_info
                    }
                    
                    # Add time window values to the locals
                    for time_param, time_value in time_window_ms.items():
                        safe_locals[time_param] = time_value
                    
                    # Evaluate the expression using the safe locals
                    result = eval(expr, {"__builtins__": {}}, safe_locals)
                    processed_params[key] = result
                except Exception as e:
                    logger.error(f"Error evaluating dynamic parameter {key}: {value} - {str(e)}")
                    # Fall back to the original string
            else:
                # No processing needed
                processed_params[key] = value
        
        # Merge parameters
        result_params = {**common_params, **processed_params}
        
        # Ensure time parameters are proper types
        for time_param in ["since_time_ms", "until_time_ms"]:
            if time_param in result_params and isinstance(result_params[time_param], str):
                try:
                    result_params[time_param] = int(result_params[time_param])
                except Exception as e:
                    logger.warning(f"Error parsing time parameter {time_param}: {str(e)}")
        
        return result_params
        
    def generate_step_summary(self, step_title: str, result: Dict[str, Any]) -> str:
        """
        Generate a summary for a runbook step execution.
        
        Args:
            step_title: Title of the runbook step
            result: Result of executing the step
            
        Returns:
            Summary string
        """
        if "error" in result:
            return f"Failed to execute step '{step_title}': {result['error']}"
            
        # For different tools, create appropriate summaries
        if "logs" in result:
            log_count = len(result.get("logs", []))
            return f"Retrieved {log_count} log entries for {result.get('entity_name', 'unknown entity')}"
            
        if "metrics" in result:
            metrics = result.get("metrics", {})
            metric_names = ", ".join(metrics.keys())
            return f"Collected metrics ({metric_names}) for {result.get('entity_name', 'unknown entity')}"
            
        if "status" in result:
            return f"Status of {result.get('entity_name', 'unknown entity')}: {result.get('status', 'unknown')}. Found {result.get('events_count', 0)} related events."
            
        if "containers" in result:
            container_count = len(result.get("containers", {}))
            return f"Retrieved specification for {result.get('entity_name', 'unknown entity')} with {container_count} containers"
            
        # Generic summary
        return f"Executed step '{step_title}' successfully"
        
    async def execute_tool(
        self,
        metrics_collector,
        tool_name: str,
        entity_info: Dict[str, Any],
        parameters: Dict[str, Any],
        state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a specific tool with the provided parameters and entity info
        
        Args:
            metrics_collector: MetricsCollector instance
            tool_name: Name of the tool to execute
            entity_info: Entity details including name, guid, etc.
            parameters: Tool-specific parameters
            state: Full workflow state containing all necessary context
            
        Returns:
            Result of the tool execution
        """
        try:
            logger.info(f"Executing tool: {tool_name}")
            
            # Clone parameters to avoid modifying the original
            params = {**parameters}
            
            # Extract and normalize time windows from state
            time_window = {}
            
            # Extract datetime objects if available
            if state and "since_time" in state and "until_time" in state:
                since_time = state["since_time"]
                until_time = state["until_time"]
                
                # Ensure datetime objects for datetimes
                if isinstance(since_time, str):
                    try:
                        since_time = datetime.fromisoformat(since_time.replace('Z', '+00:00'))
                    except ValueError:
                        logger.warning(f"Could not parse since_time: {since_time}")
                
                if isinstance(until_time, str):
                    try:
                        until_time = datetime.fromisoformat(until_time.replace('Z', '+00:00'))
                    except ValueError:
                        logger.warning(f"Could not parse until_time: {until_time}")
                
                # Only add if successfully converted to datetime objects
                if isinstance(since_time, datetime) and isinstance(until_time, datetime):
                    time_window["since_time"] = since_time
                    time_window["until_time"] = until_time
                    
                    # Also add millisecond versions
                    time_window["since_time_ms"] = int(since_time.timestamp() * 1000)
                    time_window["until_time_ms"] = int(until_time.timestamp() * 1000)
            
            # Extract millisecond timestamps if available and datetime not already populated
            if state and "since_time_ms" in state and "until_time_ms" in state and "since_time" not in time_window:
                since_time_ms = state["since_time_ms"]
                until_time_ms = state["until_time_ms"]
                
                # Store millisecond timestamps
                time_window["since_time_ms"] = since_time_ms
                time_window["until_time_ms"] = until_time_ms
                
                # Also add datetime versions
                time_window["since_time"] = datetime.fromtimestamp(since_time_ms / 1000, tz=timezone.utc)
                time_window["until_time"] = datetime.fromtimestamp(until_time_ms / 1000, tz=timezone.utc)
            
            # Check if tool exists in our configuration
            if tool_name not in self._tool_configs:
                available_tools = self.get_registered_tools()
                return {
                    "error": f"Unknown tool: {tool_name}",
                    "available_tools": available_tools
                }
            
            tool_config = self._tool_configs[tool_name]
            method_name = tool_config["method"]
            
            # Check if the method exists in the metrics_collector
            if not hasattr(metrics_collector, method_name):
                return {
                    "error": f"Method {method_name} not found in metrics_collector"
                }
            
            # Get the method to call
            method = getattr(metrics_collector, method_name)
            
            # Prepare the parameters dictionary
            exec_params = {}
            
            # Add entity parameters if available
            for param_name, entity_key in tool_config["entity_params"].items():
                if entity_key in entity_info and entity_info[entity_key] is not None:
                    exec_params[param_name] = entity_info[entity_key]
            
            # Add provided parameters
            for param_name, param_value in params.items():
                exec_params[param_name] = param_value
            
            # Add normalized time window parameters if available and not already provided
            # Check method signature to determine if it needs datetime or ms format
            method_accepts_datetime = any(
                param_name in ["since_time", "until_time"] 
                for param_name in tool_config["required_params"] + tool_config["optional_params"]
            )
            
            method_accepts_ms = any(
                param_name in ["since_time_ms", "until_time_ms"] 
                for param_name in tool_config["required_params"] + tool_config["optional_params"]
            )
            
            if method_accepts_datetime and "since_time" in time_window and "until_time" in time_window:
                if "since_time" not in exec_params:
                    exec_params["since_time"] = time_window["since_time"]
                if "until_time" not in exec_params:
                    exec_params["until_time"] = time_window["until_time"]
            
            if method_accepts_ms and "since_time_ms" in time_window and "until_time_ms" in time_window:
                if "since_time_ms" not in exec_params:
                    exec_params["since_time_ms"] = time_window["since_time_ms"]
                if "until_time_ms" not in exec_params:
                    exec_params["until_time_ms"] = time_window["until_time_ms"]
            
            # Process dynamic parameters (expressions in curly braces)
            for param_name, param_value in list(exec_params.items()):
                if isinstance(param_value, str) and param_value.startswith("{") and param_value.endswith("}"):
                    try:
                        # Extract the expression
                        expr = param_value[1:-1].strip()
                        
                        # Create safe locals with limited variables
                        safe_locals = {
                            "datetime": datetime,
                            "timedelta": timedelta,
                            "max": max,
                            "min": min,
                            "entity_info": entity_info
                        }
                        
                        # Add time window values to the locals
                        for time_param, time_value in time_window.items():
                            safe_locals[time_param] = time_value
                        
                        # Evaluate the expression using the safe locals
                        result = eval(expr, {"__builtins__": {}}, safe_locals)
                        exec_params[param_name] = result
                        
                    except Exception as e:
                        logger.error(f"Error evaluating dynamic parameter {param_name}: {param_value} - {str(e)}")
                        # Keep the original string value
            
            # Check for required parameters
            missing_params = [param for param in tool_config["required_params"] if param not in exec_params]
            if missing_params:
                return {
                    "error": f"Missing required parameters: {', '.join(missing_params)}"
                }
            
            # Filter parameters to only include those expected by the method
            expected_params = tool_config["required_params"] + tool_config["optional_params"]
            filtered_params = {k: v for k, v in exec_params.items() if k in expected_params}
            
            # Log what we're doing
            logger.info(f"Calling {method_name} with parameters: {filtered_params}")
            
            # Call the method
            result = await method(**filtered_params)
            
            # Add entity info to the result if not present
            if isinstance(result, dict):
                if "entity_name" not in result and "entity_name" in entity_info:
                    result["entity_name"] = entity_info["entity_name"]
                
                if "entity_type" not in result and "entity_type" in entity_info:
                    result["entity_type"] = entity_info["entity_type"]
            
            return result
            
        except Exception as e:
            logger.exception(f"Error executing tool {tool_name}")
            return {"error": str(e)}

    def register_tool(
        self, 
        tool_name: str, 
        method_name: str, 
        required_params: List[str], 
        optional_params: List[str] = None,
        entity_params: Dict[str, str] = None
    ) -> None:
        """
        Register a new tool with the runbook service.
        
        This allows extending the system with new tools without modifying the core code.
        
        Args:
            tool_name: Name of the tool as used in runbooks
            method_name: Name of the method to call on the metrics_collector
            required_params: List of required parameter names
            optional_params: List of optional parameter names
            entity_params: Mapping of parameter names to entity_info keys
            
        Returns:
            None
        """
        if optional_params is None:
            optional_params = []
            
        if entity_params is None:
            entity_params = {}
            
        self._tool_configs[tool_name] = {
            "method": method_name,
            "required_params": required_params,
            "optional_params": optional_params,
            "entity_params": entity_params
        }
        
        logger.info(f"Registered tool: {tool_name} -> {method_name}")
        
    def get_registered_tools(self) -> List[str]:
        """
        Get a list of all registered tool names.
        
        Returns:
            List of tool names
        """
        return list(self._tool_configs.keys())


# Singleton instance to be used throughout the application
_runbook_service_instance = None

def get_runbook_service() -> RunbookService:
    """
    Get the singleton instance of the RunbookService.
    
    Returns:
        RunbookService instance
    """
    global _runbook_service_instance
    
    if _runbook_service_instance is None:
        _runbook_service_instance = RunbookService()
        
    return _runbook_service_instance 

# Example usage for demonstration purposes
async def main():
    """Example demonstrating how to use the RunbookService with custom tools."""
    # Get the runbook service
    runbook_service = get_runbook_service()
    
    # Example of registering a new tool
    runbook_service.register_tool(
        tool_name="get_database_metrics",
        method_name="get_database_metrics",
        required_params=["database_id"],
        optional_params=["metric_names", "since_time_ms", "until_time_ms"],
        entity_params={"database_id": "entity_guid"}
    )
    
    # List all registered tools
    tools = runbook_service.get_registered_tools()
    print(f"Registered tools: {tools}")
    
    # Demonstrate tool execution (this would fail without implementing the actual method)
    # This is just an example of how you'd use it
    print("To execute a tool:")
    print("await runbook_service.execute_tool(")
    print("    metrics_collector=metrics_collector,")
    print("    tool_name='get_database_metrics',")
    print("    entity_info={'entity_guid': 'db123'},")
    print("    parameters={'metric_names': ['cpu', 'memory']},")
    print("    state={'since_time_ms': 1234567890, 'until_time_ms': 1234657890}")
    print(")")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main()) 