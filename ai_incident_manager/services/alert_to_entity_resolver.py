"""
Alert to Entity Resolver Service.

This service is responsible for resolving alerts to affected entities,
either directly using the entity GUID from the alert or indirectly
using patterns when the entity GUID is not available.
"""

import re
import logging
from typing import Dict, List, Any, Optional, Set, Tuple

from ai_incident_manager.services.entity_relationship_service import (
    get_entity_relationship_service,
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AlertToEntityResolver:
    """
    Service for resolving alerts to affected entities.

    This service is responsible for:
    1. Extracting entity information from alerts
    2. Resolving alerts to affected entities when no direct entity GUID is available
    3. Finding related entities that might be affected by the alert
    """

    def __init__(self):
        """Initialize the alert to entity resolver."""
        # Get the entity relationship service
        self.relationship_service = get_entity_relationship_service()

    def resolve_alert_to_entities(
        self, alert_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Resolve an alert to affected entities.

        Args:
            alert_data: The alert data

        Returns:
            List of affected entities
        """
        # Check if we have a direct entity GUID
        entity_guid = self._extract_entity_guid_from_alert(alert_data)

        if entity_guid:
            logger.info(f"<PERSON><PERSON> has direct entity GUID: {entity_guid}")
            return self._resolve_with_entity_guid(entity_guid, alert_data)
        else:
            logger.info("No direct entity GUID found in alert, using pattern matching")
            return self._resolve_with_patterns(alert_data)

    def _extract_entity_guid_from_alert(
        self, alert_data: Dict[str, Any]
    ) -> Optional[str]:
        """
        Extract the entity GUID from the alert data.

        Args:
            alert_data: The alert data

        Returns:
            Entity GUID or None if not found
        """
        # Check various places where the entity GUID might be

        # 1. Check entity field directly
        if "entity" in alert_data and "guid" in alert_data["entity"]:
            return alert_data["entity"]["guid"]

        # 2. Check entityId or entityGuid fields
        entity_guid = alert_data.get("entityId") or alert_data.get("entityGuid")
        if entity_guid:
            return entity_guid

        # 3. Check for GUID in incident targets
        targets = alert_data.get("targets", [])
        for target in targets:
            if "entityGuid" in target:
                return target["entityGuid"]

        # 4. Check for GUID in violatingEntities
        violating_entities = alert_data.get("violatingEntities", [])
        if (
            violating_entities
            and isinstance(violating_entities, list)
            and len(violating_entities) > 0
        ):
            for entity in violating_entities:
                if isinstance(entity, dict) and "guid" in entity:
                    return entity["guid"]

        # No entity GUID found
        return None

    def _resolve_with_entity_guid(
        self, entity_guid: str, alert_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Resolve an alert to entities using a direct entity GUID.

        Args:
            entity_guid: The entity GUID
            alert_data: The alert data

        Returns:
            List of affected entities
        """
        # In a real implementation, we would fetch the entity details from New Relic
        # For now, we'll use a placeholder
        mock_entity = {
            "entity_guid": entity_guid,
            "entity_name": f"entity-with-guid-{entity_guid}",
            "entity_type": "UNKNOWN",
            "relationship_source": "direct",
            "metrics_to_collect": ["cpu", "memory"],
        }

        # Later we would also find related entities based on this entity

        return [mock_entity]

    def _resolve_with_patterns(
        self, alert_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Resolve an alert to entities using pattern matching.

        Args:
            alert_data: The alert data

        Returns:
            List of affected entities
        """
        # Use the entity relationship service to map the alert to entities
        return self.relationship_service.map_alert_to_entity(alert_data)

    def find_related_entities(
        self, entity_guid: str, entity_type: str = None
    ) -> List[Dict[str, Any]]:
        """
        Find entities related to a specific entity.

        Args:
            entity_guid: The entity GUID
            entity_type: Optional entity type (will be determined if not provided)

        Returns:
            List of related entities
        """
        # In a real implementation, we would use the relationship service to find related entities
        # For now, we'll use a placeholder
        mock_related = [
            {
                "entity_guid": f"related-to-{entity_guid}-1",
                "entity_name": f"related-entity-1",
                "entity_type": "K8S_POD",
                "relationship_source": "related",
                "relationship_type": "CONTAINS",
                "metrics_to_collect": ["cpu", "memory"],
            },
            {
                "entity_guid": f"related-to-{entity_guid}-2",
                "entity_name": f"related-entity-2",
                "entity_type": "K8S_NODE",
                "relationship_source": "related",
                "relationship_type": "RUNS_ON",
                "metrics_to_collect": ["cpu", "memory", "disk"],
            },
        ]

        return mock_related


# Singleton instance
_alert_to_entity_resolver = None


def get_alert_to_entity_resolver() -> AlertToEntityResolver:
    """Get or create the singleton AlertToEntityResolver instance."""
    global _alert_to_entity_resolver
    if _alert_to_entity_resolver is None:
        _alert_to_entity_resolver = AlertToEntityResolver()
    return _alert_to_entity_resolver
