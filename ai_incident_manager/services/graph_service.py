"""
Graph Service for Entity Relationship and Cascading Failure Analysis.

This service provides advanced graph-based analysis capabilities for identifying 
cascading failures, calculating dependency impact, and performing sophisticated 
entity relationship analysis using NetworkX.
"""

import json
import asyncio
import networkx as nx
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timed<PERSON>ta
from loguru import logger
from dataclasses import dataclass
from enum import Enum
import numpy as np
from collections import defaultdict, deque

from ai_incident_manager.services.mongodb_service import get_mongodb_service
from ai_incident_manager.services.entity_relationship_service import get_entity_relationship_service


class NodeType(Enum):
    """Types of nodes in the graph"""
    KUBERNETES_POD = "KUBERNETES_POD"
    KUBERNETES_NODE = "KUBERNETES_NODE"
    KUBERNETES_DEPLOYMENT = "KUBERNETES_DEPLOYMENT"
    KUBERNETES_NAMESPACE = "KUBERNETES_NAMESPACE"
    KUBERNETES_CLUSTER = "KUBERNETES_CLUSTER"
    CONTAINER = "CONTAINER"
    APPLICATION = "APPLICATION"
    HOST = "HOST"
    DATABASE = "DATABASE"
    KAFKA = "KAFKA"
    DEBEZIUM = "DEBEZIUM"
    UNKNOWN = "UNKNOWN"


class RelationshipType(Enum):
    """Types of relationships between entities"""
    RUNS_ON = "RUNS_ON"
    CONTAINS = "CONTAINS"
    DEPENDS_ON = "DEPENDS_ON"
    COMMUNICATES_WITH = "COMMUNICATES_WITH"
    MANAGES = "MANAGES"
    BELONGS_TO = "BELONGS_TO"
    WRITES_TO = "WRITES_TO"
    READS_FROM = "READS_FROM"
    RELATED_TO = "RELATED_TO"


@dataclass
class GraphNode:
    """Represents a node in the entity relationship graph"""
    id: str
    name: str
    node_type: NodeType
    entity_guid: Optional[str] = None
    properties: Dict[str, Any] = None
    metrics: Dict[str, Any] = None
    health_status: str = "unknown"
    criticality_score: float = 0.0
    failure_probability: float = 0.0
    
    def __post_init__(self):
        if self.properties is None:
            self.properties = {}
        if self.metrics is None:
            self.metrics = {}


@dataclass
class GraphEdge:
    """Represents an edge in the entity relationship graph"""
    source: str
    target: str
    relationship_type: RelationshipType
    weight: float = 1.0
    strength: float = 1.0
    failure_propagation_probability: float = 0.5
    properties: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.properties is None:
            self.properties = {}


@dataclass
class CascadingFailureAnalysis:
    """Results of cascading failure analysis"""
    primary_entity: str
    affected_entities: List[str]
    failure_paths: List[List[str]]
    risk_scores: Dict[str, float]
    critical_dependencies: List[str]
    potential_blast_radius: int
    estimated_impact_score: float
    recommended_actions: List[str]


@dataclass
class GraphMetrics:
    """Graph-level metrics for analysis"""
    node_count: int
    edge_count: int
    density: float
    average_clustering: float
    average_path_length: float
    diameter: int
    largest_component_size: int
    centrality_metrics: Dict[str, Dict[str, float]]


class GraphService:
    """
    Advanced graph-based service for entity relationship and cascading failure analysis.
    
    This service provides:
    1. Graph construction from topology and relationship data
    2. Cascading failure analysis with configurable parameters
    3. Critical path identification
    4. Dependency impact assessment
    5. Graph-based entity discovery
    6. Temporal analysis capabilities
    """
    
    def __init__(self, cache_timeout_seconds: int = 300):
        """
        Initialize the graph service.
        
        Args:
            cache_timeout_seconds: Cache timeout for graph data
        """
        self.cache_timeout = cache_timeout_seconds
        self.graph_cache = {}
        self.cache_timestamps = {}
        self.logger = logger.bind(name="graph_service")
        
        # Graph construction parameters
        self.default_failure_probability = 0.1
        self.default_propagation_probability = 0.5
        self.critical_path_threshold = 0.7
        
    async def build_entity_graph(
        self,
        primary_entity_guid: str,
        entity_type: str,
        entity_name: str,
        alert_category: str,
        cluster_name: Optional[str] = None,
        max_depth: int = 3,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None
    ) -> nx.DiGraph:
        """
        Build a comprehensive entity relationship graph.
        
        Args:
            primary_entity_guid: GUID of the primary entity
            entity_type: Type of the primary entity
            entity_name: Name of the primary entity
            alert_category: Alert category for context
            cluster_name: Optional cluster name
            max_depth: Maximum depth for relationship traversal
            since_time_ms: Start time for data collection
            until_time_ms: End time for data collection
            
        Returns:
            NetworkX directed graph representing entity relationships
        """
        # Check cache first
        cache_key = f"{primary_entity_guid}_{alert_category}_{max_depth}"
        if self._is_cache_valid(cache_key):
            self.logger.info(f"Using cached graph for {primary_entity_guid}")
            return self.graph_cache[cache_key]
        
        # Create new graph
        G = nx.DiGraph()
        
        # Add primary entity
        primary_node = GraphNode(
            id=primary_entity_guid,
            name=entity_name,
            node_type=NodeType(entity_type) if entity_type in NodeType._value2member_map_ else NodeType.UNKNOWN,
            entity_guid=primary_entity_guid,
            health_status="issue",
            criticality_score=1.0,
            failure_probability=0.9
        )
        
        G.add_node(
            primary_entity_guid,
            **primary_node.__dict__
        )
        
        # Get entity relationship service
        relationship_service = get_entity_relationship_service()
        
        # Build graph using BFS traversal
        visited = set()
        queue = deque([(primary_entity_guid, entity_type, entity_name, 0)])
        
        while queue:
            current_guid, current_type, current_name, depth = queue.popleft()
            
            if current_guid in visited or depth >= max_depth:
                continue
                
            visited.add(current_guid)
            
            try:
                # Get related entities
                related_info = relationship_service.traverse_relationships(
                    entity_guid=current_guid,
                    entity_type=current_type,
                    entity_name=current_name,
                    alert_category=alert_category,
                    cluster_name=cluster_name,
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms
                )
                
                # Process related entities
                for entity in related_info.get("related_entities", []):
                    target_guid = entity.get("entityGuid")
                    target_name = entity.get("entityName", "")
                    target_type = entity.get("entityType", "UNKNOWN")
                    relationship = entity.get("relationship", "RELATED_TO")
                    
                    if not target_guid or target_guid == current_guid:
                        continue
                    
                    # Add target node if not exists
                    if target_guid not in G:
                        target_node = GraphNode(
                            id=target_guid,
                            name=target_name,
                            node_type=NodeType(target_type) if target_type in NodeType._value2member_map_ else NodeType.UNKNOWN,
                            entity_guid=target_guid,
                            health_status="unknown",
                            criticality_score=self._calculate_criticality_score(entity),
                            failure_probability=self.default_failure_probability
                        )
                        
                        G.add_node(target_guid, **target_node.__dict__)
                        
                        # Add to queue for further exploration
                        queue.append((target_guid, target_type, target_name, depth + 1))
                    
                    # Add edge
                    edge = GraphEdge(
                        source=current_guid,
                        target=target_guid,
                        relationship_type=RelationshipType(relationship) if relationship in RelationshipType._value2member_map_ else RelationshipType.RELATED_TO,
                        weight=self._calculate_edge_weight(entity),
                        strength=self._calculate_relationship_strength(entity),
                        failure_propagation_probability=self._calculate_propagation_probability(entity, relationship)
                    )
                    
                    G.add_edge(
                        current_guid,
                        target_guid,
                        **edge.__dict__
                    )
                    
            except Exception as e:
                self.logger.error(f"Error processing entity {current_guid}: {str(e)}")
                continue
        
        # Enhance graph with additional metrics
        self._enhance_graph_with_metrics(G)
        
        # Cache the result
        self.graph_cache[cache_key] = G
        self.cache_timestamps[cache_key] = datetime.now()
        
        self.logger.info(f"Built graph with {G.number_of_nodes()} nodes and {G.number_of_edges()} edges")
        return G
    
    def analyze_cascading_failures(
        self,
        graph: nx.DiGraph,
        primary_entity_guid: str,
        failure_threshold: float = 0.1,
        propagation_decay: float = 0.8,
        max_hops: int = 5
    ) -> CascadingFailureAnalysis:
        """
        Analyze potential cascading failures in the graph.
        
        Args:
            graph: Entity relationship graph
            primary_entity_guid: GUID of the failing entity
            failure_threshold: Minimum probability to consider a failure
            propagation_decay: Decay factor for failure propagation
            max_hops: Maximum hops to consider for cascading
            
        Returns:
            Cascading failure analysis results
        """
        if primary_entity_guid not in graph:
            raise ValueError(f"Primary entity {primary_entity_guid} not found in graph")
        
        # Initialize failure probabilities
        failure_probabilities = {node: graph.nodes[node].get('failure_probability', 0.0) for node in graph.nodes()}
        failure_probabilities[primary_entity_guid] = 1.0  # Primary entity has failed
        
        # Track failure paths
        failure_paths = []
        affected_entities = set()
        risk_scores = {}
        
        # Simulate failure propagation using BFS
        queue = deque([(primary_entity_guid, 1.0, [primary_entity_guid])])
        visited_edges = set()
        
        while queue:
            current_entity, current_prob, path = queue.popleft()
            
            if len(path) > max_hops:
                continue
                
            # Get all outgoing edges (dependencies)
            for neighbor in graph.successors(current_entity):
                edge_key = (current_entity, neighbor)
                
                if edge_key in visited_edges:
                    continue
                    
                visited_edges.add(edge_key)
                
                # Get edge properties
                edge_data = graph.get_edge_data(current_entity, neighbor)
                propagation_prob = edge_data.get('failure_propagation_probability', self.default_propagation_probability)
                edge_weight = edge_data.get('weight', 1.0)
                
                # Calculate failure probability for the neighbor
                neighbor_base_prob = failure_probabilities.get(neighbor, 0.0)
                propagated_prob = current_prob * propagation_prob * (propagation_decay ** (len(path) - 1))
                
                # Update failure probability (combine with existing)
                new_prob = min(1.0, neighbor_base_prob + propagated_prob)
                failure_probabilities[neighbor] = new_prob
                
                # If probability exceeds threshold, consider it affected
                if new_prob >= failure_threshold:
                    affected_entities.add(neighbor)
                    new_path = path + [neighbor]
                    failure_paths.append(new_path)
                    
                    # Continue propagation
                    queue.append((neighbor, new_prob, new_path))
        
        # Calculate risk scores
        for entity in affected_entities:
            criticality = graph.nodes[entity].get('criticality_score', 0.0)
            failure_prob = failure_probabilities[entity]
            risk_scores[entity] = criticality * failure_prob
        
        # Identify critical dependencies
        critical_dependencies = [
            entity for entity, score in risk_scores.items()
            if score >= self.critical_path_threshold
        ]
        
        # Calculate overall impact
        total_impact = sum(risk_scores.values())
        blast_radius = len(affected_entities)
        
        # Generate recommendations
        recommendations = self._generate_failure_recommendations(
            graph, primary_entity_guid, affected_entities, critical_dependencies
        )
        
        return CascadingFailureAnalysis(
            primary_entity=primary_entity_guid,
            affected_entities=list(affected_entities),
            failure_paths=failure_paths,
            risk_scores=risk_scores,
            critical_dependencies=critical_dependencies,
            potential_blast_radius=blast_radius,
            estimated_impact_score=total_impact,
            recommended_actions=recommendations
        )
    
    def find_critical_paths(
        self,
        graph: nx.DiGraph,
        source: str,
        target: str
    ) -> List[List[str]]:
        """
        Find all critical paths between two entities.
        
        Args:
            graph: Entity relationship graph
            source: Source entity GUID
            target: Target entity GUID
            
        Returns:
            List of critical paths (each path is a list of entity GUIDs)
        """
        try:
            # Find all simple paths
            all_paths = list(nx.all_simple_paths(graph, source, target, cutoff=10))
            
            # Calculate criticality score for each path
            path_scores = []
            for path in all_paths:
                score = self._calculate_path_criticality(graph, path)
                path_scores.append((path, score))
            
            # Sort by criticality score (descending)
            path_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Return top critical paths
            return [path for path, score in path_scores if score >= self.critical_path_threshold]
            
        except nx.NetworkXNoPath:
            return []
    
    def calculate_graph_metrics(self, graph: nx.DiGraph) -> GraphMetrics:
        """
        Calculate comprehensive graph metrics.
        
        Args:
            graph: Entity relationship graph
            
        Returns:
            Graph metrics object
        """
        # Convert to undirected for some metrics
        undirected_graph = graph.to_undirected()
        
        # Basic metrics
        node_count = graph.number_of_nodes()
        edge_count = graph.number_of_edges()
        density = nx.density(graph)
        
        # Clustering and path metrics
        try:
            average_clustering = nx.average_clustering(undirected_graph)
            if nx.is_connected(undirected_graph):
                average_path_length = nx.average_shortest_path_length(undirected_graph)
                diameter = nx.diameter(undirected_graph)
            else:
                average_path_length = 0
                diameter = 0
        except:
            average_clustering = 0
            average_path_length = 0
            diameter = 0
        
        # Component analysis
        largest_component = max(nx.connected_components(undirected_graph), key=len) if node_count > 0 else set()
        largest_component_size = len(largest_component)
        
        # Centrality metrics
        centrality_metrics = {
            'degree': dict(graph.degree()),
            'betweenness': nx.betweenness_centrality(graph),
            'closeness': nx.closeness_centrality(graph),
            'eigenvector': nx.eigenvector_centrality(graph, max_iter=1000) if node_count > 0 else {}
        }
        
        return GraphMetrics(
            node_count=node_count,
            edge_count=edge_count,
            density=density,
            average_clustering=average_clustering,
            average_path_length=average_path_length,
            diameter=diameter,
            largest_component_size=largest_component_size,
            centrality_metrics=centrality_metrics
        )
    
    def find_entities_by_criteria(
        self,
        graph: nx.DiGraph,
        node_type: Optional[NodeType] = None,
        min_criticality: float = 0.0,
        max_distance_from_issue: int = 999,
        health_status: Optional[str] = None
    ) -> List[str]:
        """
        Find entities in the graph based on specific criteria.
        
        Args:
            graph: Entity relationship graph
            node_type: Filter by node type
            min_criticality: Minimum criticality score
            max_distance_from_issue: Maximum distance from issue entity
            health_status: Filter by health status
            
        Returns:
            List of entity GUIDs matching criteria
        """
        matching_entities = []
        
        # Find issue entities (entities with health_status = "issue")
        issue_entities = [
            node for node in graph.nodes()
            if graph.nodes[node].get('health_status') == 'issue'
        ]
        
        for node in graph.nodes():
            node_data = graph.nodes[node]
            
            # Check node type
            if node_type and node_data.get('node_type') != node_type:
                continue
            
            # Check criticality
            if node_data.get('criticality_score', 0.0) < min_criticality:
                continue
            
            # Check health status
            if health_status and node_data.get('health_status') != health_status:
                continue
            
            # Check distance from issue
            if issue_entities and max_distance_from_issue < 999:
                min_distance = float('inf')
                for issue_entity in issue_entities:
                    try:
                        distance = nx.shortest_path_length(graph, issue_entity, node)
                        min_distance = min(min_distance, distance)
                    except nx.NetworkXNoPath:
                        continue
                
                if min_distance > max_distance_from_issue:
                    continue
            
            matching_entities.append(node)
        
        return matching_entities
    
    async def persist_graph(
        self,
        graph: nx.DiGraph,
        incident_id: str,
        primary_entity_guid: str,
        metadata: Dict[str, Any] = None
    ) -> str:
        """
        Persist graph data to MongoDB.
        
        Args:
            graph: Entity relationship graph
            incident_id: Incident ID
            primary_entity_guid: Primary entity GUID
            metadata: Additional metadata
            
        Returns:
            Document ID of stored graph
        """
        try:
            # Convert graph to JSON-serializable format
            graph_data = {
                'nodes': [
                    {
                        'id': node,
                        **graph.nodes[node]
                    }
                    for node in graph.nodes()
                ],
                'edges': [
                    {
                        'source': edge[0],
                        'target': edge[1],
                        **graph.get_edge_data(edge[0], edge[1])
                    }
                    for edge in graph.edges()
                ],
                'metadata': metadata or {},
                'timestamp': datetime.now().isoformat(),
                'incident_id': incident_id,
                'primary_entity_guid': primary_entity_guid
            }
            
            # Get MongoDB service
            mongodb_service = get_mongodb_service()
            
            # Store graph data
            result = await mongodb_service.store_graph_data(
                incident_id=incident_id,
                graph_data=graph_data,
                primary_entity_guid=primary_entity_guid
            )
            
            self.logger.info(f"Persisted graph for incident {incident_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error persisting graph: {str(e)}")
            raise
    
    def _calculate_criticality_score(self, entity: Dict[str, Any]) -> float:
        """Calculate criticality score for an entity."""
        # Base criticality
        criticality = 0.5
        
        # Increase based on entity type
        entity_type = entity.get('entityType', '')
        if entity_type in ['KUBERNETES_NODE', 'DATABASE', 'KAFKA']:
            criticality += 0.3
        elif entity_type in ['KUBERNETES_DEPLOYMENT', 'APPLICATION']:
            criticality += 0.2
        elif entity_type in ['KUBERNETES_POD', 'CONTAINER']:
            criticality += 0.1
        
        # Increase based on metrics
        metrics = entity.get('metrics_to_collect', [])
        if 'cpu_usage' in metrics or 'memory_usage' in metrics:
            criticality += 0.1
        
        return min(1.0, criticality)
    
    def _calculate_edge_weight(self, entity: Dict[str, Any]) -> float:
        """Calculate edge weight based on entity properties."""
        weight = 1.0
        
        # Increase weight based on relationship type
        relationship = entity.get('relationship', '')
        if relationship in ['DEPENDS_ON', 'RUNS_ON']:
            weight += 0.5
        elif relationship in ['MANAGES', 'CONTAINS']:
            weight += 0.3
        elif relationship in ['COMMUNICATES_WITH']:
            weight += 0.2
        
        return weight
    
    def _calculate_relationship_strength(self, entity: Dict[str, Any]) -> float:
        """Calculate relationship strength."""
        strength = 0.5
        
        # Increase based on metrics available
        metrics = entity.get('metrics_to_collect', [])
        strength += len(metrics) * 0.1
        
        return min(1.0, strength)
    
    def _calculate_propagation_probability(self, entity: Dict[str, Any], relationship: str) -> float:
        """Calculate failure propagation probability."""
        base_prob = self.default_propagation_probability
        
        # Adjust based on relationship type
        if relationship in ['DEPENDS_ON', 'RUNS_ON']:
            base_prob += 0.3
        elif relationship in ['MANAGES', 'CONTAINS']:
            base_prob += 0.2
        elif relationship in ['COMMUNICATES_WITH']:
            base_prob += 0.1
        
        return min(1.0, base_prob)
    
    def _enhance_graph_with_metrics(self, graph: nx.DiGraph):
        """Enhance graph nodes with additional computed metrics."""
        # Calculate degree centrality
        degree_centrality = nx.degree_centrality(graph)
        
        # Calculate betweenness centrality
        betweenness_centrality = nx.betweenness_centrality(graph)
        
        # Update node attributes
        for node in graph.nodes():
            graph.nodes[node]['degree_centrality'] = degree_centrality.get(node, 0.0)
            graph.nodes[node]['betweenness_centrality'] = betweenness_centrality.get(node, 0.0)
    
    def _calculate_path_criticality(self, graph: nx.DiGraph, path: List[str]) -> float:
        """Calculate criticality score for a path."""
        if not path:
            return 0.0
        
        # Calculate based on node criticalities and edge weights
        total_score = 0.0
        for i in range(len(path) - 1):
            node_score = graph.nodes[path[i]].get('criticality_score', 0.0)
            edge_weight = graph.get_edge_data(path[i], path[i + 1]).get('weight', 1.0)
            total_score += node_score * edge_weight
        
        # Add final node score
        total_score += graph.nodes[path[-1]].get('criticality_score', 0.0)
        
        return total_score / len(path)
    
    def _generate_failure_recommendations(
        self,
        graph: nx.DiGraph,
        primary_entity: str,
        affected_entities: Set[str],
        critical_dependencies: List[str]
    ) -> List[str]:
        """Generate recommendations based on failure analysis."""
        recommendations = []
        
        # Primary entity recommendations
        primary_type = graph.nodes[primary_entity].get('node_type')
        if primary_type == NodeType.KUBERNETES_POD:
            recommendations.append("Check pod logs and events for crash reasons")
            recommendations.append("Verify resource limits and requests")
        elif primary_type == NodeType.KUBERNETES_NODE:
            recommendations.append("Check node health and resource utilization")
            recommendations.append("Verify network connectivity and storage")
        
        # Critical dependency recommendations
        if critical_dependencies:
            recommendations.append(f"Monitor critical dependencies: {', '.join(critical_dependencies[:3])}")
        
        # High-risk entity recommendations
        high_risk_entities = [
            entity for entity in affected_entities
            if graph.nodes[entity].get('criticality_score', 0.0) > 0.7
        ]
        
        if high_risk_entities:
            recommendations.append(f"Proactively check high-risk entities: {', '.join(high_risk_entities[:3])}")
        
        # General recommendations
        recommendations.append("Implement circuit breakers for external dependencies")
        recommendations.append("Consider scaling critical services")
        
        return recommendations
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self.cache_timestamps:
            return False
        
        timestamp = self.cache_timestamps[cache_key]
        return (datetime.now() - timestamp).total_seconds() < self.cache_timeout


# Singleton instance
_graph_service_instance = None


def get_graph_service() -> GraphService:
    """Get the singleton instance of the graph service."""
    global _graph_service_instance
    if _graph_service_instance is None:
        _graph_service_instance = GraphService()
    return _graph_service_instance