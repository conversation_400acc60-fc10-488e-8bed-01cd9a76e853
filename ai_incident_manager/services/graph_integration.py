"""
Graph Integration Service.

This module provides integration between the graph service and existing
entity relationship service, enhancing the current functionality with
graph-based analysis capabilities.
"""

import yaml
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

from ai_incident_manager.services.graph_service import get_graph_service
from ai_incident_manager.services.entity_relationship_service import get_entity_relationship_service
from ai_incident_manager.utils.graph_algorithms import get_graph_algorithms
from ai_incident_manager.models.graph_models import (
    GraphAnalysisConfig, CascadingFailureAnalysisModel, GraphMetricsModel
)


class GraphIntegrationService:
    """
    Integration service that bridges the graph service with existing entity relationship service.
    
    This service provides:
    1. Enhanced relationship traversal with graph analysis
    2. Configuration loading from YAML with graph parameters
    3. Seamless integration with existing workflow
    4. Backward compatibility with current functionality
    """
    
    def __init__(self, config_path: str = "config/entity_relationships.yaml"):
        """
        Initialize the integration service.
        
        Args:
            config_path: Path to the enhanced entity relationships configuration
        """
        self.config_path = config_path
        self.config = self._load_enhanced_config()
        self.graph_service = get_graph_service()
        self.relationship_service = get_entity_relationship_service()
        self.graph_algorithms = get_graph_algorithms()
        self.logger = logger.bind(name="graph_integration")
        
    def _load_enhanced_config(self) -> Dict[str, Any]:
        """Load enhanced configuration with graph analysis parameters."""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
                
            # Validate graph analysis configuration
            if 'graph_analysis' not in config:
                config['graph_analysis'] = {
                    'default_criticality_score': 0.5,
                    'default_failure_probability': 0.1,
                    'default_propagation_probability': 0.5,
                    'critical_path_threshold': 0.7,
                    'failure_threshold': 0.1,
                    'propagation_decay': 0.8,
                    'max_hops': 5,
                    'max_graph_depth': 3
                }
                
            return config
            
        except Exception as e:
            self.logger.error(f"Error loading enhanced config: {str(e)}")
            return {}
    
    def get_graph_analysis_config(self) -> GraphAnalysisConfig:
        """Get graph analysis configuration."""
        graph_config = self.config.get('graph_analysis', {})
        return GraphAnalysisConfig(
            max_depth=graph_config.get('max_graph_depth', 3),
            failure_threshold=graph_config.get('failure_threshold', 0.1),
            propagation_decay=graph_config.get('propagation_decay', 0.8),
            max_hops=graph_config.get('max_hops', 5),
            critical_path_threshold=graph_config.get('critical_path_threshold', 0.7),
            default_failure_probability=graph_config.get('default_failure_probability', 0.1),
            default_propagation_probability=graph_config.get('default_propagation_probability', 0.5)
        )
    
    def get_entity_config(self, entity_type: str) -> Dict[str, Any]:
        """Get enhanced entity configuration with graph parameters."""
        entity_types = self.config.get('entity_types', {})
        return entity_types.get(entity_type, {})
    
    def get_failure_characteristics(self, alert_category: str) -> Dict[str, Any]:
        """Get failure characteristics for an alert category."""
        alert_categories = self.config.get('alert_categories', {})
        category_config = alert_categories.get(alert_category, {})
        return category_config.get('failure_characteristics', {})
    
    def get_graph_analysis_rules(self) -> Dict[str, Any]:
        """Get graph analysis rules from configuration."""
        return self.config.get('graph_analysis_rules', {})
    
    async def enhanced_relationship_traversal(
        self,
        entity_guid: str,
        entity_type: str,
        entity_name: str,
        alert_category: str,
        cluster_name: Optional[str] = None,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        include_graph_analysis: bool = True
    ) -> Dict[str, Any]:
        """
        Enhanced relationship traversal with graph analysis.
        
        Args:
            entity_guid: GUID of the primary entity
            entity_type: Type of the primary entity
            entity_name: Name of the primary entity
            alert_category: Alert category for context
            cluster_name: Optional cluster name
            since_time_ms: Start time for data collection
            until_time_ms: End time for data collection
            include_graph_analysis: Whether to include graph analysis
            
        Returns:
            Enhanced relationship information with graph analysis
        """
        try:
            # Get standard relationship traversal
            standard_result = self.relationship_service.traverse_relationships(
                entity_guid=entity_guid,
                entity_type=entity_type,
                entity_name=entity_name,
                alert_category=alert_category,
                cluster_name=cluster_name,
                since_time_ms=since_time_ms,
                until_time_ms=until_time_ms
            )
            
            if not include_graph_analysis:
                return standard_result
            
            # Build graph for advanced analysis
            graph = await self.graph_service.build_entity_graph(
                primary_entity_guid=entity_guid,
                entity_type=entity_type,
                entity_name=entity_name,
                alert_category=alert_category,
                cluster_name=cluster_name,
                max_depth=self.get_graph_analysis_config().max_depth,
                since_time_ms=since_time_ms,
                until_time_ms=until_time_ms
            )
            
            # Perform graph analysis
            graph_analysis = await self._perform_graph_analysis(graph, entity_guid, alert_category)
            
            # Enhance standard result with graph analysis
            enhanced_result = standard_result.copy()
            enhanced_result['graph_analysis'] = graph_analysis
            
            return enhanced_result
            
        except Exception as e:
            self.logger.error(f"Error in enhanced relationship traversal: {str(e)}")
            return standard_result if 'standard_result' in locals() else {
                'primary_entity': {
                    'entityGuid': entity_guid,
                    'entityType': entity_type,
                    'entityName': entity_name
                },
                'related_entities': [],
                'error': str(e)
            }
    
    async def _perform_graph_analysis(
        self,
        graph,
        primary_entity_guid: str,
        alert_category: str
    ) -> Dict[str, Any]:
        """Perform comprehensive graph analysis."""
        analysis_results = {}
        
        try:
            # Graph metrics
            metrics = self.graph_service.calculate_graph_metrics(graph)
            analysis_results['metrics'] = {
                'node_count': metrics.node_count,
                'edge_count': metrics.edge_count,
                'density': metrics.density,
                'average_clustering': metrics.average_clustering,
                'diameter': metrics.diameter
            }
            
            # Cascading failure analysis
            cascading_analysis = self.graph_service.analyze_cascading_failures(
                graph=graph,
                primary_entity_guid=primary_entity_guid,
                failure_threshold=self.get_graph_analysis_config().failure_threshold,
                propagation_decay=self.get_graph_analysis_config().propagation_decay,
                max_hops=self.get_graph_analysis_config().max_hops
            )
            
            analysis_results['cascading_failure'] = {
                'affected_entities': cascading_analysis.affected_entities,
                'critical_dependencies': cascading_analysis.critical_dependencies,
                'potential_blast_radius': cascading_analysis.potential_blast_radius,
                'estimated_impact_score': cascading_analysis.estimated_impact_score,
                'failure_paths': [
                    {'path': path, 'length': len(path)}
                    for path in cascading_analysis.failure_paths
                ]
            }
            
            # Critical paths
            critical_paths = self.graph_service.find_critical_paths(
                graph=graph,
                source=primary_entity_guid,
                target=None  # Will find paths to all critical entities
            )
            
            analysis_results['critical_paths'] = [
                {
                    'source': path.source,
                    'target': path.target,
                    'path': path.shortest_path,
                    'risk_level': path.risk_level,
                    'total_weight': path.total_weight
                }
                for path in critical_paths
            ]
            
            # Bottleneck analysis
            bottlenecks = self.graph_algorithms.find_bottlenecks(graph)
            analysis_results['bottlenecks'] = bottlenecks
            
            # Resilience analysis
            resilience = self.graph_algorithms.analyze_graph_resilience(graph)
            analysis_results['resilience'] = {
                'resilience_score': resilience.get('resilience_score', 0.0),
                'removal_impact': resilience.get('removal_impact', [])
            }
            
            return analysis_results
            
        except Exception as e:
            self.logger.error(f"Error performing graph analysis: {str(e)}")
            return {'error': str(e)}
    
    def enhance_alert_with_graph_context(
        self,
        alert_data: Dict[str, Any],
        graph_analysis: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Enhance alert with graph-based context.
        
        Args:
            alert_data: Original alert data
            graph_analysis: Optional graph analysis results
            
        Returns:
            Enhanced alert with graph context
        """
        try:
            enhanced_alert = alert_data.copy()
            
            if not graph_analysis:
                return enhanced_alert
            
            # Add graph-based enrichment
            enrichment = {
                'graph_enrichment': {
                    'timestamp': datetime.now().isoformat(),
                    'analysis_available': True
                }
            }
            
            # Add cascading failure context
            cascading = graph_analysis.get('cascading_failure', {})
            if cascading:
                enrichment['graph_enrichment']['cascading_risk'] = {
                    'blast_radius': cascading.get('potential_blast_radius', 0),
                    'impact_score': cascading.get('estimated_impact_score', 0.0),
                    'affected_entities_count': len(cascading.get('affected_entities', [])),
                    'critical_dependencies_count': len(cascading.get('critical_dependencies', []))
                }
            
            # Add criticality context
            metrics = graph_analysis.get('metrics', {})
            if metrics:
                enrichment['graph_enrichment']['topology_health'] = {
                    'node_count': metrics.get('node_count', 0),
                    'edge_count': metrics.get('edge_count', 0),
                    'density': metrics.get('density', 0.0),
                    'diameter': metrics.get('diameter', 0)
                }
            
            # Add urgency scoring
            urgency_score = self._calculate_urgency_score(graph_analysis)
            enrichment['graph_enrichment']['urgency_score'] = urgency_score
            
            # Add recommended actions
            recommendations = self._generate_graph_recommendations(graph_analysis)
            enrichment['graph_enrichment']['recommendations'] = recommendations
            
            enhanced_alert['enrichment'] = enrichment
            
            return enhanced_alert
            
        except Exception as e:
            self.logger.error(f"Error enhancing alert with graph context: {str(e)}")
            return alert_data
    
    def _calculate_urgency_score(self, graph_analysis: Dict[str, Any]) -> float:
        """Calculate urgency score based on graph analysis."""
        try:
            urgency_rules = self.get_graph_analysis_rules().get('alert_enrichment', {}).get('urgency_scoring_factors', {})
            
            score = 0.0
            total_weight = 0.0
            
            # Cascading failure impact
            cascading = graph_analysis.get('cascading_failure', {})
            if cascading:
                impact_score = cascading.get('estimated_impact_score', 0.0)
                blast_radius = min(1.0, cascading.get('potential_blast_radius', 0) / 10.0)
                
                score += impact_score * urgency_rules.get('blast_radius', 0.2)
                score += blast_radius * urgency_rules.get('blast_radius', 0.2)
                total_weight += urgency_rules.get('blast_radius', 0.2) * 2
            
            # Graph health metrics
            metrics = graph_analysis.get('metrics', {})
            if metrics:
                density = metrics.get('density', 0.0)
                # High density can indicate complexity and potential for cascading
                complexity_score = min(1.0, density * 2.0)
                
                score += complexity_score * urgency_rules.get('relationship_strength', 0.15)
                total_weight += urgency_rules.get('relationship_strength', 0.15)
            
            # Resilience score (lower resilience = higher urgency)
            resilience = graph_analysis.get('resilience', {})
            if resilience:
                resilience_score = resilience.get('resilience_score', 1.0)
                urgency_from_resilience = 1.0 - resilience_score
                
                score += urgency_from_resilience * urgency_rules.get('recovery_time', 0.15)
                total_weight += urgency_rules.get('recovery_time', 0.15)
            
            return score / max(total_weight, 0.1)
            
        except Exception as e:
            self.logger.error(f"Error calculating urgency score: {str(e)}")
            return 0.5
    
    def _generate_graph_recommendations(self, graph_analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on graph analysis."""
        recommendations = []
        
        try:
            # Cascading failure recommendations
            cascading = graph_analysis.get('cascading_failure', {})
            if cascading:
                blast_radius = cascading.get('potential_blast_radius', 0)
                if blast_radius > 5:
                    recommendations.append("High blast radius detected - consider implementing circuit breakers")
                
                critical_deps = cascading.get('critical_dependencies', [])
                if len(critical_deps) > 3:
                    recommendations.append(f"Monitor {len(critical_deps)} critical dependencies closely")
            
            # Bottleneck recommendations
            bottlenecks = graph_analysis.get('bottlenecks', [])
            if bottlenecks:
                top_bottleneck = bottlenecks[0]
                recommendations.append(f"Address bottleneck at {top_bottleneck.get('node', 'unknown')} (score: {top_bottleneck.get('bottleneck_score', 0.0):.2f})")
            
            # Resilience recommendations
            resilience = graph_analysis.get('resilience', {})
            if resilience:
                resilience_score = resilience.get('resilience_score', 1.0)
                if resilience_score < 0.5:
                    recommendations.append("Low system resilience - consider redundancy improvements")
            
            # Graph health recommendations
            metrics = graph_analysis.get('metrics', {})
            if metrics:
                density = metrics.get('density', 0.0)
                if density > 0.7:
                    recommendations.append("High graph density - simplify dependencies where possible")
                elif density < 0.1:
                    recommendations.append("Low graph density - verify all dependencies are captured")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Error generating graph recommendations: {str(e)}")
            return ["Unable to generate recommendations due to analysis error"]
    
    def get_monitoring_recommendations(self, alert_category: str, graph_analysis: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Get monitoring recommendations based on alert category and graph analysis.
        
        Args:
            alert_category: Alert category
            graph_analysis: Optional graph analysis results
            
        Returns:
            List of monitoring recommendations
        """
        recommendations = []
        
        try:
            # Get base recommendations from configuration
            monitoring_rules = self.get_graph_analysis_rules().get('monitoring_recommendations', {})
            
            # Alert category specific recommendations
            failure_characteristics = self.get_failure_characteristics(alert_category)
            if failure_characteristics:
                mitigation_strategies = failure_characteristics.get('mitigation_strategies', [])
                for strategy in mitigation_strategies:
                    recommendations.append({
                        'type': 'mitigation',
                        'strategy': strategy,
                        'priority': 'high',
                        'source': 'alert_category'
                    })
            
            # Graph analysis based recommendations
            if graph_analysis:
                cascading = graph_analysis.get('cascading_failure', {})
                if cascading:
                    critical_deps = cascading.get('critical_dependencies', [])
                    for dep in critical_deps[:3]:  # Top 3 critical dependencies
                        recommendations.append({
                            'type': 'critical_dependency_monitoring',
                            'entity': dep,
                            'priority': 'high',
                            'source': 'graph_analysis'
                        })
                
                bottlenecks = graph_analysis.get('bottlenecks', [])
                for bottleneck in bottlenecks[:2]:  # Top 2 bottlenecks
                    recommendations.append({
                        'type': 'bottleneck_monitoring',
                        'entity': bottleneck.get('node'),
                        'bottleneck_score': bottleneck.get('bottleneck_score', 0.0),
                        'priority': 'medium',
                        'source': 'graph_analysis'
                    })
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Error getting monitoring recommendations: {str(e)}")
            return []


# Singleton instance
_graph_integration_service = None


def get_graph_integration_service() -> GraphIntegrationService:
    """Get the singleton instance of the graph integration service."""
    global _graph_integration_service
    if _graph_integration_service is None:
        _graph_integration_service = GraphIntegrationService()
    return _graph_integration_service