"""
Alert parser for New Relic alerts.

This module contains functions to parse and extract information from New Relic alert messages.
"""

import re
import json
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime


def parse_alert_message(message: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse a New Relic alert message and extract key information.
    
    Args:
        message: The New Relic alert message
        
    Returns:
        Dictionary containing extracted information
    """
    # Initialize extracted data
    extracted = {
        "alert_id": message.get("id"),
        "incident_id": message.get("incident_id"),
        "account_id": message.get("account_id"),
        "title": message.get("title", "Unknown Alert"),
        "description": message.get("description", ""),
        "severity": _extract_severity(message),
        "timestamp": message.get("created_at"),
        "entity_type": None,
        "entity_guid": None,
        "entity_name": None,
        "metric_name": None,
        "condition": message.get("condition_name", ""),
        "policy_name": message.get("policy_name", ""),
        "url": message.get("url", ""),
        "raw_message": message,
    }
    
    # Extract entity information
    if "targets" in message and message["targets"]:
        target = message["targets"][0]
        extracted["entity_name"] = target.get("name", "")
        extracted["entity_guid"] = target.get("id", "")
        extracted["entity_type"] = target.get("type", "")
    
    # Extract additional details from description
    if extracted["description"]:
        # Extract metric name from description
        metric_match = re.search(r"metric: '([^']+)'", extracted["description"])
        if metric_match:
            extracted["metric_name"] = metric_match.group(1)
            
        # Try to extract entity information if not found in targets
        if not extracted["entity_type"]:
            entity_match = re.search(r"entity: ([A-Za-z0-9_]+)", extracted["description"])
            if entity_match:
                extracted["entity_type"] = entity_match.group(1)
                
        # Extract threshold values if available
        threshold_match = re.search(r"threshold: ([0-9.]+)", extracted["description"])
        if threshold_match:
            extracted["threshold"] = float(threshold_match.group(1))
            
        # Extract current value if available
        value_match = re.search(r"current: ([0-9.]+)", extracted["description"])
        if value_match:
            extracted["current_value"] = float(value_match.group(1))
    
    # If this is a NRQL alert condition, extract the NRQL query
    if "details" in message and "nrql_query" in message["details"]:
        extracted["nrql_query"] = message["details"]["nrql_query"]
    
    # Add a timestamp if not present
    if not extracted["timestamp"]:
        extracted["timestamp"] = datetime.utcnow().isoformat()
    
    return extracted


def _extract_severity(message: Dict[str, Any]) -> str:
    """
    Extract severity from a New Relic alert message.
    
    Args:
        message: The New Relic alert message
        
    Returns:
        Severity level (CRITICAL, WARNING, INFO)
    """
    # Try to get from priority field
    priority = message.get("priority", "").upper()
    if priority in ["CRITICAL", "WARNING", "INFO"]:
        return priority
    
    # Check severity field
    severity = message.get("severity", "").upper()
    if severity in ["CRITICAL", "WARNING", "INFO"]:
        return severity
    
    # Try to infer from content
    description = message.get("description", "").lower()
    title = message.get("title", "").lower()
    
    if "critical" in description or "critical" in title:
        return "CRITICAL"
    elif "warning" in description or "warning" in title:
        return "WARNING"
    
    # Default to INFO if can't determine
    return "INFO"


def extract_entity_info(message: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract entity information from alert message.
    
    Args:
        message: The New Relic alert message
        
    Returns:
        Dictionary with entity information
    """
    entity_info = {
        "guid": None,
        "name": None,
        "type": None,
        "metadata": {}
    }
    
    # Try to get from targets
    if "targets" in message and message["targets"]:
        target = message["targets"][0]
        entity_info["guid"] = target.get("id")
        entity_info["name"] = target.get("name")
        entity_info["type"] = target.get("type")
        
        # Additional metadata
        if "labels" in target:
            entity_info["metadata"]["labels"] = target["labels"]
        
        if "correlated" in target:
            entity_info["metadata"]["correlated"] = target["correlated"]
    
    # Try to extract from details if available
    if "details" in message:
        details = message["details"]
        if not entity_info["guid"] and "entity_guid" in details:
            entity_info["guid"] = details["entity_guid"]
            
        if not entity_info["name"] and "entity_name" in details:
            entity_info["name"] = details["entity_name"]
            
        if not entity_info["type"] and "entity_type" in details:
            entity_info["type"] = details["entity_type"]
            
        # If there's a component name but no entity name, use it
        if not entity_info["name"] and "component_name" in details:
            entity_info["name"] = details["component_name"]
    
    # Try to infer entity type from title or description if not found
    if not entity_info["type"]:
        title = message.get("title", "").lower()
        description = message.get("description", "").lower()
        
        if "pod" in title or "pod" in description:
            entity_info["type"] = "KUBERNETES_POD"
        elif "node" in title or "node" in description:
            entity_info["type"] = "KUBERNETES_NODE"
        elif "cluster" in title or "cluster" in description:
            entity_info["type"] = "KUBERNETES_CLUSTER"
        elif "application" in title or "application" in description:
            entity_info["type"] = "APPLICATION"
    
    return entity_info


def extract_metric_info(message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Extract metric information from alert message.
    
    Args:
        message: The New Relic alert message
        
    Returns:
        Dictionary with metric information or None if no metric found
    """
    metric_info = {
        "name": None,
        "value": None,
        "threshold": None,
        "unit": None,
        "comparison": None
    }
    
    # Try to get from violation data
    if "details" in message and "violation_chart_url" in message["details"]:
        metric_info["chart_url"] = message["details"]["violation_chart_url"]
    
    # Extract from description
    description = message.get("description", "")
    
    # Look for metric name
    metric_match = re.search(r"metric: '([^']+)'", description)
    if metric_match:
        metric_info["name"] = metric_match.group(1)
    
    # Look for current value
    value_match = re.search(r"current(?: value)?: ([0-9.]+)", description, re.IGNORECASE)
    if value_match:
        try:
            metric_info["value"] = float(value_match.group(1))
        except ValueError:
            pass
    
    # Look for threshold
    threshold_match = re.search(r"threshold(?: value)?: ([0-9.]+)", description, re.IGNORECASE)
    if threshold_match:
        try:
            metric_info["threshold"] = float(threshold_match.group(1))
        except ValueError:
            pass
    
    # Try to determine comparison operator
    if "above" in description.lower():
        metric_info["comparison"] = "above"
    elif "below" in description.lower():
        metric_info["comparison"] = "below"
    elif "equals" in description.lower():
        metric_info["comparison"] = "equals"
    
    # Try to determine unit
    unit_patterns = [
        (r"(\d+(?:\.\d+)?)\s*%", "%"),
        (r"(\d+(?:\.\d+)?)\s*ms", "ms"),
        (r"(\d+(?:\.\d+)?)\s*seconds", "seconds"),
        (r"(\d+(?:\.\d+)?)\s*bytes", "bytes"),
        (r"(\d+(?:\.\d+)?)\s*MB", "MB"),
        (r"(\d+(?:\.\d+)?)\s*GB", "GB"),
        (r"(\d+(?:\.\d+)?)\s*requests", "requests"),
        (r"(\d+(?:\.\d+)?)\s*errors", "errors")
    ]
    
    for pattern, unit in unit_patterns:
        if re.search(pattern, description):
            metric_info["unit"] = unit
            break
    
    # If we couldn't find a metric name, return None
    if not metric_info["name"] and not metric_info["value"] and not metric_info["threshold"]:
        return None
        
    return metric_info


def detect_alert_type(message: Dict[str, Any]) -> str:
    """
    Detect the type of alert from the message.
    
    Args:
        message: The New Relic alert message
        
    Returns:
        Alert type string
    """
    # Check for specific alert types based on conditions
    
    # If the alert has NRQL query, it's a NRQL alert
    if "details" in message and "nrql_query" in message["details"]:
        return "NRQL_ALERT"
    
    # Check for specific alert types based on title or description
    title = message.get("title", "").lower()
    description = message.get("description", "").lower()
    
    if "apm" in title or "apm" in description:
        return "APM_ALERT"
    
    if "infrastructure" in title or "infrastructure" in description:
        return "INFRASTRUCTURE_ALERT"
    
    if "kubernetes" in title or "kubernetes" in description or "k8s" in title or "k8s" in description:
        return "KUBERNETES_ALERT"
    
    if "synthetics" in title or "synthetics" in description:
        return "SYNTHETICS_ALERT"
    
    if "browser" in title or "browser" in description:
        return "BROWSER_ALERT"
    
    if "mobile" in title or "mobile" in description:
        return "MOBILE_ALERT"
    
    # Default to generic alert type
    return "GENERIC_ALERT"


def extract_incident_info(message: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract incident information from New Relic alert.
    
    Args:
        message: The New Relic alert message
        
    Returns:
        Dictionary with incident information
    """
    # Parse the alert message
    alert_data = parse_alert_message(message)
    entity_info = extract_entity_info(message)
    metric_info = extract_metric_info(message)
    alert_type = detect_alert_type(message)
    
    # Build incident information
    incident = {
        "id": str(message.get("incident_id", "")),
        "title": alert_data["title"],
        "status": "OPEN",
        "severity": alert_data["severity"],
        "description": alert_data["description"],
        "customer": message.get("account_name", ""),
        "startTime": alert_data["timestamp"],
        "endTime": None,
        "impact": _generate_impact_description(alert_data, entity_info, metric_info),
        "teams": [],
        "sources": ["New Relic"],
        "rootCause": None,
        "preventiveActions": None,
        "alert": {
            "id": alert_data["alert_id"],
            "type": alert_type,
            "condition": alert_data["condition"],
            "policy": alert_data["policy_name"],
            "url": alert_data["url"]
        },
        "entities": [
            {
                "guid": entity_info["guid"],
                "name": entity_info["name"],
                "type": entity_info["type"],
                "metadata": entity_info["metadata"]
            }
        ],
        "metrics": []
    }
    
    # Add metric if available
    if metric_info and metric_info["name"]:
        incident["metrics"].append({
            "name": metric_info["name"],
            "data": [
                {
                    "timestamp": alert_data["timestamp"],
                    "value": metric_info["value"],
                    "threshold": metric_info["threshold"],
                    "unit": metric_info["unit"],
                    "comparison": metric_info["comparison"]
                }
            ]
        })
    
    # Add timeline event for the alert
    incident["timelineEvents"] = [
        {
            "id": f"alert-{alert_data['alert_id']}",
            "timestamp": alert_data["timestamp"],
            "title": "Alert Triggered",
            "description": alert_data["description"],
            "type": "ALERT",
            "source": "New Relic",
            "tags": ["alert", "trigger", alert_type.lower()],
            "metric": metric_info
        }
    ]
    
    return incident


def _generate_impact_description(
    alert_data: Dict[str, Any], 
    entity_info: Dict[str, Any], 
    metric_info: Optional[Dict[str, Any]]
) -> str:
    """
    Generate an impact description based on alert, entity, and metric data.
    
    Args:
        alert_data: Parsed alert data
        entity_info: Entity information
        metric_info: Metric information
        
    Returns:
        Impact description string
    """
    impact = f"Alert triggered for {entity_info['type'] or 'entity'}"
    
    if entity_info["name"]:
        impact += f" {entity_info['name']}"
    
    if metric_info and metric_info["name"]:
        impact += f". {metric_info['name']}"
        
        if metric_info["value"] is not None:
            impact += f" value: {metric_info['value']}"
            
            if metric_info["unit"]:
                impact += f" {metric_info['unit']}"
        
        if metric_info["comparison"] and metric_info["threshold"] is not None:
            impact += f" is {metric_info['comparison']} threshold: {metric_info['threshold']}"
            
            if metric_info["unit"] and "value" not in impact:
                impact += f" {metric_info['unit']}"
    
    return impact 