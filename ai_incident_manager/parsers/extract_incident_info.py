"""
Extract incident information from alerts using Pydantic AI agent.

This module replaces the traditional alert_parser with an AI-powered version
that can extract more detailed and accurate information from alerts.
"""

import os
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Union

from pydantic_ai import RunContext
import dotenv

from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.logs import NewRelicLogsClient
from lib.new_relic.analyzer import EntityAnalyzer
from ai_incident_manager.agents.alert_parser_agent import alert_parser_agent, AlertParserDeps
from ai_incident_manager.services.alert_category_service import get_alert_category_service

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize alert category service
alert_category_service = get_alert_category_service()


def extract_incident_info_legacy(alert_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Legacy function to extract incident information from alert data.
    This is kept for backward compatibility.
    
    Args:
        alert_data: The alert data from New Relic
        
    Returns:
        Dictionary with extracted information about the incident
    """
    # Import legacy function here to avoid circular import
    from ai_incident_manager.parsers.alert_parser import extract_incident_info as legacy_extract
    
    return legacy_extract(alert_data)


async def extract_incident_info_ai(alert_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract incident information from alert data using AI agent.
    
    Args:
        alert_data: The alert data from New Relic
        
    Returns:
        Dictionary with extracted information about the incident
    """
    logger.info(f"Extracting incident information from alert: {alert_data.get('title', 'Unknown')}")
    
    try:
        # Initialize the New Relic client
        api_key = os.environ.get("NEWRELIC_API_KEY")
        account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
        
        if not api_key or not account_id:
            logger.warning("New Relic API credentials not set, using legacy parser")
            return extract_incident_info_legacy(alert_data)
            
        graphql_client = NewRelicGraphQLClient(api_key, account_id)
        query_client = NewRelicQueryClient(graphql_client)
        logs_client = NewRelicLogsClient(graphql_client)
        
        # Initialize the entity analyzer
        entity_analyzer = EntityAnalyzer(graphql_client, query_client, logs_client)
        
        # Get OpenAI client from the agent
        # We need to directly use the Azure OpenAI client initialized in alert_parser_agent.py
        from ai_incident_manager.agents.alert_parser_agent import openai_client
        
        # Set up dependencies for the agent
        deps = AlertParserDeps(
            openai_client=openai_client,
            nr_query_client=query_client,
            entity_analyzer=entity_analyzer,
            alert_categories=alert_category_service.get_all_categories()
        )
        
        # Prepare the user prompt
        user_prompt = f"""
        Please analyze the following New Relic alert to extract incident information:
        
        ```json
        {json.dumps(alert_data, indent=2)}
        ```
        
        Extract detailed information including:
        1. Alert title and severity
        2. Entity information (name, type, ID)
        3. Metrics mentioned in the alert
        4. Condition details
        5. Determine the alert category using the get_alert_category tool
        6. Get additional details about the issue using the get_issue_details tool if available
        
        Based on the alert category, identify relevant metrics to collect and potential root causes.
        """
        
        # Run the agent
        result = await alert_parser_agent.run(user_prompt, deps=deps)
        analysis_result = result.data
        
        # Format the result for the incident workflow
        incident_info = {
            "incident_id": alert_data.get("issueId", ""),
            "title": alert_data.get("title", "Unknown Alert"),
            "description": analysis_result.get("description", ""),
            "severity": alert_data.get("priority", "INFO"),
            "start_time": alert_data.get("createdAt", ""),
            "alert_category": analysis_result.get("category", "unknown"),
            "runbook": analysis_result.get("runbook", ""),
            "entities": [],
            "metrics": [],
            "likely_causes": analysis_result.get("likely_causes", []),
            "raw_alert": alert_data
        }
        
        # Add entity information
        if "entity" in analysis_result:
            entity_data = analysis_result["entity"]
            
            # Handle the format from analyze_entity
            if "entity_guid" in entity_data:
                # Format from analyze_entity method
                entity_info = {
                    "id": entity_data.get("entity_guid", ""),
                    "name": entity_data.get("entity_name", ""),
                    "type": entity_data.get("entity_type", ""),
                    "metadata": {
                        "cluster_id": entity_data.get("cluster_id"),
                        "product": entity_data.get("product"),
                        "region": entity_data.get("region"),
                        **entity_data.get("metadata", {})
                    }
                }
            else:
                # Legacy format
                entity_info = {
                    "id": entity_data.get("guid", ""),
                    "name": entity_data.get("name", ""),
                    "type": entity_data.get("type", ""),
                    "metadata": entity_data.get("metadata", {})
                }
            
            # Add metrics found in the entity data
            if "metrics" in entity_data and entity_data["metrics"]:
                for metric_name, metric_data in entity_data["metrics"].items():
                    metric_info = {
                        "name": metric_name,
                        "data": metric_data.get("data", []),
                        "unit": metric_data.get("unit", ""),
                        "threshold": None
                    }
                    incident_info["metrics"].append(metric_info)
            
            # Add logs reference
            if "logs" in entity_data and entity_data["logs"]:
                entity_info["logs"] = len(entity_data["logs"])
            
            incident_info["entities"].append(entity_info)
        
        # Add metrics information from analysis result (if not already added from entity)
        if "metrics" in analysis_result and not incident_info["metrics"]:
            for metric in analysis_result["metrics"]:
                incident_info["metrics"].append({
                    "name": metric.get("name", ""),
                    "data": metric.get("data", []),
                    "unit": metric.get("unit", ""),
                    "threshold": metric.get("threshold")
                })
        
        return incident_info
        
    except Exception as e:
        logger.error(f"Error using AI parser: {str(e)}")
        logger.info("Falling back to legacy parser")
        return extract_incident_info_legacy(alert_data)


def extract_incident_info(alert_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract incident information from alert data.
    This function decides whether to use the AI or legacy approach.
    
    Args:
        alert_data: The alert data from New Relic
        
    Returns:
        Dictionary with extracted information about the incident
    """
    # Check if we can use the AI parser (needs API credentials)
    can_use_ai = os.environ.get("NEWRELIC_API_KEY") and os.environ.get("NEWRELIC_ACCOUNT_ID") and \
                (os.environ.get("AZURE_OPENAI_ENDPOINT") and os.environ.get("AZURE_OPENAI_API_KEY"))
    
    if can_use_ai:
        # Run the async function in a new event loop
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            incident_info = loop.run_until_complete(extract_incident_info_ai(alert_data))
            loop.close()
            return incident_info
        except Exception as e:
            logger.error(f"Error running AI parser: {str(e)}")
            return extract_incident_info_legacy(alert_data)
    else:
        # Use legacy parser
        logger.info("Using legacy parser due to missing API credentials")
        return extract_incident_info_legacy(alert_data) 