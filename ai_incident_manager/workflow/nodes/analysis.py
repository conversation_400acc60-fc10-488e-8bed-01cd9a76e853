"""
Analysis workflow nodes for root cause analysis and topology generation.
"""

import os
import json
import traceback
from datetime import datetime, timezone
from typing import Dict
from loguru import logger
from openai import AsyncAzureOpenAI

from ai_incident_manager.models.workflow_state import IncidentState, InvestigationNote, TimelineEvent
from ai_incident_manager.agents.rca_agent import rca_agent, RCAAgentDeps
from ai_incident_manager.agents.topology_agent import topology_agent, TopologyAgentDeps
from ..utils.decorators import log_node_execution
from ..utils.serialization import ensure_serializable
from ..config import get_workflow_config


@log_node_execution("analyze_root_cause")
async def analyze_root_cause(state: IncidentState) -> Dict:
    """
    Analyze the root cause of the incident based on collected data.
    
    This node aggregates all the data collected in previous steps (alert details,
    entity information, metrics, logs, runbook execution results) and uses an
    AI agent to determine the likely root cause of the incident.
    
    Args:
        state: Current workflow state with all collected data
        
    Returns:
        Updated fields of workflow state with root cause analysis
    """
    logger.info(f"Starting root cause analysis for incident {state.incident_id}")
    
    # Get configuration
    config = get_workflow_config()
    
    # Create a timeline event for starting RCA
    start_event: TimelineEvent = {
        "id": f"evt-rca-start-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Root Cause Analysis",
        "description": "Analyzing collected data to determine root cause",
        "type": "analysis",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "RCA", "variant": "secondary"}
        ]
    }
    
    # Initialize collections for update
    timeline_events = [start_event]
    investigation_notes = []
    
    try:
        # Initialize OpenAI client (use o1 model for better reasoning capabilities)
        # This is especially important for complex RCA tasks
        openai_client = AsyncAzureOpenAI(
            azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
            azure_deployment="o1",  # Use more powerful model for RCA
            api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
            api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
        )
        
        # Set up dependencies for the agent
        deps = RCAAgentDeps(
            openai_client=openai_client,
            state=state,
            nr_query_client=config.nr_query_client,
            logs_client=config.nr_logs_client,
            metrics_collector=config.metrics_collector
        )
        
        # Prepare the prompt with structured summary of collected data
        # Extract key information from state for the prompt
        alert_category = state.alert_category
        alert_title = state.title
        
        # Format entities information
        entities_info = []
        for entity in state.entities:
            entity_info = {
                "guid": entity.entity_guid,
                "name": entity.entity_name,
                "type": entity.entity_type,
                "is_primary": getattr(entity, "is_primary", False)
            }
            entities_info.append(entity_info)
        
        # Format runbook results
        runbook_summary = []
        if state.runbook_results:
            for step in state.runbook_results:
                # Convert to dict if it's a Pydantic model
                if hasattr(step, "model_dump"):
                    step_dict = step.model_dump()
                else:
                    step_dict = step
                
                step_info = {
                    "title": step_dict.get("step_title", "Unknown"),
                    "summary": step_dict.get("summary", ""),
                    "issues_found": step_dict.get("issues_found", False)
                }
                runbook_summary.append(step_info)
        
        # Create a structured prompt with all relevant information
        user_prompt = f"""
Please perform a comprehensive root cause analysis for this incident:

INCIDENT SUMMARY:
- ID: {state.incident_id}
- Title: {state.title}
- Alert Category: {alert_category}
- Alert Title: {alert_title}
- Severity: {state.severity}
- Product: {state.product or "Unknown"}
- Landscape: {state.landscape or "Unknown"}
- Region: {state.region or "Unknown"}
- Time Window: {state.since_time or "Unknown"} to {state.until_time or "Unknown"}

ENTITIES ({len(state.entities)}):
{json.dumps(ensure_serializable(entities_info), indent=2)}

ENTITY RELATIONSHIPS:
{json.dumps(ensure_serializable(state.entity_relationships), indent=2) if state.entity_relationships else "No relationships defined"}

RUNBOOK RESULTS:
{json.dumps(ensure_serializable(runbook_summary), indent=2) if runbook_summary else "No runbook results available"}

Based on the comprehensive data available in the incident state, determine the most likely root cause of this incident.
Identify primary factors, secondary factors, and provide evidence for your conclusions.

Use the available tools to retrieve additional data from MongoDB if needed to support your analysis.

Be as detailed as possible in your analysis with specific evidence pointing to the root cause.
Include confidence levels, uncertainty factors, and a timeline of events leading to the incident.
"""
        
        # Execute the agent
        result = await rca_agent.run(user_prompt, deps=deps)
        rca_result = result.data
        
        # Convert Pydantic model to dict for easier handling
        rca_dict = ensure_serializable(rca_result.model_dump())
        
        # Extract the main findings
        primary_root_cause = rca_result.primary_root_cause
        confidence_level = rca_result.confidence_level
        evidence_summary = rca_result.evidence_summary
        secondary_factors = rca_result.secondary_factors
        recommendations = rca_result.recommendations
        remediation_actions = rca_result.remediation_actions
        alert_summary = rca_result.alert_summary
        
        # Create a detailed dictionary for the rca_details field
        # Note: This replaces the previous root_cause_analysis field with a cleaner, more structured format
        rca_details = {
            "primary_root_cause": primary_root_cause,
            "confidence_level": confidence_level,
            "evidence_summary": evidence_summary,
            "secondary_factors": secondary_factors,
            "timeline_reconstruction": rca_result.timeline_reconstruction,
            "affected_components": rca_result.affected_components,
            "recommendations": recommendations,
            "uncertainty_factors": rca_result.uncertainty_factors,
            "prioritized_factors": [factor.model_dump() for factor in rca_result.prioritized_factors],
            "analysis_time": datetime.now(timezone.utc).isoformat()
        }
        
        # Create a completion event with the root cause
        completion_event: TimelineEvent = {
            "id": f"evt-rca-complete-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Root Cause Identified",
            "description": primary_root_cause,
            "type": "analysis",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "RCA", "variant": "secondary"},
                {"label": f"Confidence: {confidence_level}/10", "variant": "outline"}
            ]
        }
        timeline_events.append(completion_event)
        
        # Create detailed notes for the investigation
        rca_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "root_cause_analyzer",
            "note": f"Primary Root Cause: {primary_root_cause}",
            "data": rca_details  # Use the comprehensive details dictionary
        }
        investigation_notes.append(rca_note)
        
        # Create recommendations event for the timeline if available
        if recommendations:
            recommendations_event: TimelineEvent = {
                "id": f"evt-rca-recommendations-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Recommendations",
                "description": f"{len(recommendations)} recommendations provided",
                "type": "analysis",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Recommendations", "variant": "success"}
                ]
            }
            timeline_events.append(recommendations_event)
        
        # Create factors events for the timeline
        if rca_result.prioritized_factors:
            for i, factor in enumerate(rca_result.prioritized_factors[:3]):  # Show top 3 in timeline
                factor_event: TimelineEvent = {
                    "id": f"evt-rca-factor-{i}-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": f"Factor {i+1}: {factor.factor}",
                    "description": f"Importance: {factor.importance}/10, Confidence: {factor.confidence}/10",
                    "type": "analysis",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Factor", "variant": "outline"}
                    ]
                }
                timeline_events.append(factor_event)
        
        # Store the RCA results in MongoDB
        try:
            # Import here to avoid circular imports
            from ai_incident_manager.services.mongodb_service import get_mongodb_service
            mongodb_service = get_mongodb_service()
            
            # Store RCA results in a dedicated collection
            rca_doc_id = await mongodb_service.store_rca_results(
                incident_id=state.incident_id,
                rca_results=rca_details,  # Store the comprehensive details dictionary
                timestamp=datetime.now(timezone.utc)
            )
            logger.info(f"Stored RCA results in MongoDB for incident {state.incident_id}, document ID: {rca_doc_id}")
            
        except Exception as db_error:
            logger.error(f"Error storing RCA results in MongoDB: {str(db_error)}", exc_info=True)
            # Continue processing even if MongoDB storage fails
        
        # Return the updated state fields
        # Note: root_cause_analysis is kept for backward compatibility, but rca_details is the preferred field
        return {
            "root_cause": primary_root_cause,
            "root_cause_analysis": rca_dict,  # Keep for backward compatibility
            "rca_details": rca_details,  # New field with more structured data
            "timeline": timeline_events,
            "investigation_notes": investigation_notes,
            "current_phase": "root_cause_analyzed",
            "remediation_actions": remediation_actions,
            "alert_summary": alert_summary
        }
        
    except Exception as e:
        logger.error(f"Error in root cause analysis: {str(e)}")
        
        # Create an error event
        error_event: TimelineEvent = {
            "id": f"evt-rca-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Error in Root Cause Analysis",
            "description": f"Error analyzing root cause: {str(e)}",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        timeline_events.append(error_event)
        
        # Create an error note
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "root_cause_analyzer",
            "note": f"Error analyzing root cause: {str(e)}",
            "data": {"error": str(e)}
        }
        investigation_notes.append(error_note)
        
        return {
            "error": str(e),
            "timeline": timeline_events,
            "investigation_notes": investigation_notes,
            "current_phase": "root_cause_error"
        }


@log_node_execution("generate_topology")
async def generate_topology(state: IncidentState) -> Dict:
    """
    Generate a topology map of the entities involved in the incident.
    
    This function takes the entities and entity relationships identified in previous
    steps and generates a topology map showing how they are connected.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state with topology information
    """
    logger.info(f"Generating topology for incident {state.incident_id}")
    
    # Get configuration
    config = get_workflow_config()
    
    # Check if we have entities for topology generation
    if not state.entities and not hasattr(state, "entity_details"):
        logger.warning("No entities found for topology generation")
        # Add an error note
        investigation_notes = []
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "topology_generator",
            "note": "No entities found for topology generation",
            "data": {"error": "No entities found"}
        }
        
        # Add an error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Topology Generation Error",
            "description": "No entities found for topology generation",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        return {
            "error": "No entities found",
            "investigation_notes": investigation_notes,
            "timeline": [error_event]
        }
    
    # Create start event for timeline
    start_event: TimelineEvent = {
        "id": f"evt-topology-start-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Topology Generation",
        "description": "Generating service topology map based on affected entities",
        "type": "investigation",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Topology", "variant": "secondary"}
        ]
    }
    
    # Use first entity as primary if not specified
    primary_entity_guid = getattr(state, "primary_entity_guid", None)
    if not primary_entity_guid and state.entities:
        primary_entity_guid = state.entities[0].entity_guid
        logger.info(f"Using first entity as primary: {primary_entity_guid}")
    
    # Extract required information
    product = getattr(state, "product", "Unknown")
    nr_region = getattr(state, "nr_region", "Unknown") 
    landscape = getattr(state, "landscape", "Unknown")
    region = getattr(state, "region", "Unknown")
    cluster_name = getattr(state, "cluster_name", "Unknown")
    incident_id = state.incident_id
    
    if not primary_entity_guid:
        logger.warning("Missing required information for topology generation")
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "topology_generator",
            "note": "Missing required information for topology generation",
            "data": {"error": "Missing primary entity information"}
        }
        
        # Add error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-topology-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Topology Generation Error",
            "description": "Missing primary entity information",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        return {
            "error": "Missing primary entity information",
            "investigation_notes": [error_note],
            "timeline": [start_event, error_event]
        }
    
    # Initialize the agent dependencies
    try:
        # Initialize OpenAI client
        openai_client = AsyncAzureOpenAI(
            azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
            azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
            api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
            api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
        )
        
        # Set up dependencies for the agent
        deps = TopologyAgentDeps(
            openai_client=openai_client,
            nr_query_client=config.nr_query_client
        )
    except Exception as e:
        logger.error(f"Error initializing topology agent dependencies: {str(e)}")
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "topology_generator",
            "note": f"Error initializing dependencies: {str(e)}",
            "data": {"error": str(e)}
        }
        
        # Add error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-topology-dep-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Topology Generation Error",
            "description": f"Error initializing dependencies: {str(e)}",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        return {
            "error": "Error initializing dependencies",
            "investigation_notes": [error_note],
            "timeline": [start_event, error_event]
        }
    
    # Prepare the user prompt
    user_prompt = f"""
    Please generate and analyze topology data for the following entity:
    
    Entity GUID: {primary_entity_guid}
    Product: {product}
    Landscape: {landscape}
    Region: {region}
    New Relic Region: {nr_region}
    Issue ID: {incident_id}
    Cluster Name: {cluster_name}
    
    I need a detailed analysis of the topology, including:
    1. Key services and their relationships
    2. Potential impact of this issue on the system
    3. Recommendations for further investigation based on the topology
    """
    
    # Run the agent
    try:
        result = await topology_agent.run(user_prompt, deps=deps)
        
        # Extract the topology data
        topology_data = result.data.topology.model_dump()
        
        # Add an investigation note
        investigation_notes = []
        note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "topology_generator",
            "note": f"Topology generated with {len(topology_data['nodes'])} nodes and {len(topology_data['links'])} links",
            "data": {
                "product": product,
                "landscape": landscape,
                "region": region,
                "nr_region": nr_region,
                "cluster_name": cluster_name,
                "analysis": result.data.analysis,
                "impacted_services": result.data.impacted_services,
                "adjacent_services": result.data.adjacent_services,
                "potential_cascading_impacts": result.data.potential_cascading_impacts,
                "visualization_notes": result.data.topology_visualization_notes
            }
        }
        investigation_notes.append(note)
        
        # Add completion event to timeline
        completion_event: TimelineEvent = {
            "id": f"evt-topology-complete-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Topology Generated",
            "description": f"Generated topology with {len(topology_data['nodes'])} services and {len(topology_data['links'])} relationships",
            "type": "analysis",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Topology", "variant": "secondary"},
                {"label": "Complete", "variant": "outline"}
            ]
        }
        
        # Add impact event to timeline if there are impacted services
        if result.data.impacted_services:
            impact_event: TimelineEvent = {
                "id": f"evt-topology-impact-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Service Impact Assessment",
                "description": f"Identified {len(result.data.impacted_services)} directly impacted services and {len(result.data.adjacent_services)} adjacent services",
                "type": "analysis",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Impact", "variant": "destructive"}
                ]
            }
            
            # Update the state
            return {
                "topology": topology_data,
                "topology_analysis": result.data.analysis,
                "impacted_services": result.data.impacted_services,
                "adjacent_services": result.data.adjacent_services,
                "investigation_notes": investigation_notes,
                "timeline": [start_event, completion_event, impact_event],
                "current_phase": "topology_generated"
            }
        else:
            # Update the state
            return {
                "topology": topology_data,
                "topology_analysis": result.data.analysis,
                "impacted_services": result.data.impacted_services,
                "adjacent_services": result.data.adjacent_services,
                "investigation_notes": investigation_notes,
                "timeline": [start_event, completion_event],
                "current_phase": "topology_generated"
            }
        
    except Exception as e:
        logger.error(f"Error generating topology: {str(e)}")
        # Add an error note
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "topology_generator",
            "note": f"Error generating topology: {str(e)}",
            "data": {"error": str(e)}
        }
        
        # Add error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-topology-exec-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Topology Generation Error",
            "description": f"Error generating topology: {str(e)}",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        return {
            "error": "Error generating topology",
            "investigation_notes": [error_note],
            "timeline": [start_event, error_event]
        } 