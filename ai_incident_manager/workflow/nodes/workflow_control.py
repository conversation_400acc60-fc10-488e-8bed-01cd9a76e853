"""
Workflow control nodes for managing flow and completion.
"""

from datetime import datetime, timezone
from typing import Dict
from loguru import logger
from langgraph.graph import END

from ai_incident_manager.models.workflow_state import IncidentState, InvestigationNote, TimelineEvent, EventTag
from ..utils.decorators import log_node_execution


@log_node_execution("should_continue")
async def should_continue(state: IncidentState) -> Dict:
    """
    Determine if the workflow should continue to the next step or end.
    
    Args:
        state: Current workflow state
        
    Returns:
        Dictionary with updates to state, including value for __next__ to specify the next node
    """
    # Check the current phase to determine the next step
    current_phase = state.current_phase
    next_node = None
    
    if current_phase == "initialize":
        # After initialization, go to alert analysis
        next_node = "analyze_alert"
    elif current_phase == "alert_analysis_completed":
        # After alert parsing, create a ticket
        next_node = "create_ticket"
    elif current_phase == "ticket_created":
        # After ticket creation, analyze entity relationships
        next_node = "analyze_entity_relationships"
    elif current_phase == "entity_analysis_completed":
        # After entity analysis, analyze entity relationships
        next_node = "analyze_entity_relationships"
    elif current_phase == "entity_relationships_analyzed":
        # After relationship analysis, execute runbooks
        next_node = "execute_runbooks"
    elif current_phase == "runbooks_executed":
        # After runbook execution, perform root cause analysis
        logger.info("Runbooks have been executed, moving to root cause analysis")
        next_node = "analyze_root_cause"
    elif current_phase == "root_cause_analyzed":
        # After root cause analysis, generate topology
        logger.info("Root cause analysis complete, moving to generate topology")
        next_node = "generate_topology"
    elif current_phase == "topology_generated":
        # After topology generation, report results
        logger.info("Topology generated, reporting results")
        next_node = "report_results"
    elif current_phase == "results_reported":
        # After reporting results, go to finish
        logger.info("Results reported, moving to finish node")
        next_node = "finish"
    elif current_phase == "completed":
        # End the workflow after completion
        logger.info("Workflow processing finished")
        next_node = END
    else:
        # Default to end if we don't recognize the phase
        logger.info(f"Workflow ending with unknown phase: {current_phase}")
        next_node = END
    
    logger.info(f"Determined next node: {next_node}")
    
    # Return a dictionary with __next__ key indicating the next node
    return {"__next": next_node}


@log_node_execution("finish")
async def finish(state: IncidentState) -> Dict:
    """
    Final node in the workflow that wraps up the incident analysis.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Finishing workflow for incident {state.incident_id}")
    
    # Extract contextual information from state
    product = state.product or "Unknown"
    nr_region = state.nr_region or "Unknown"
    landscape = state.landscape or "Unknown"
    region = state.region or "Unknown"
    cluster_name = state.cluster_name or "Unknown"
    
    # Create a timeline event for workflow completion
    completion_event = TimelineEvent(
        id=f"evt-workflow-complete-{datetime.now(timezone.utc).timestamp():.0f}",
        incidentId=state.incident_id,
        timestamp=datetime.now(timezone.utc).isoformat(),
        title="Incident Analysis Workflow Complete",
        description="All incident analysis steps completed",
        type="investigation",
        source="AI Agent Framework",
        tags=[
            EventTag(label="Workflow", variant="secondary"),
            EventTag(label="Complete", variant="outline")
        ]
    )
    
    # Add a summary timeline event with findings
    alert_category = state.alert_category
    entity_count = len(state.entities)
    
    summary_event = TimelineEvent(
        id=f"evt-workflow-summary-{datetime.now(timezone.utc).timestamp():.0f}",
        incidentId=state.incident_id,
        timestamp=datetime.now(timezone.utc).isoformat(),
        title="Incident Analysis Summary",
        description=f"Alert Category: {alert_category}, Entities Analyzed: {entity_count}, Product: {product}, Landscape: {landscape}",
        type="analysis",
        source="AI Agent Framework",
        tags=[
            EventTag(label="Summary", variant="secondary")
        ]
    )
    
    # Add an investigation note for the completion
    investigation_notes = []
    note = InvestigationNote(
        timestamp=datetime.now(timezone.utc).isoformat(),
        agent="workflow_manager",
        note="Workflow execution completed",
        data={
            "alert_category": alert_category,
            "entity_count": entity_count,
            "product": product,
            "region": region,
            "nr_region": nr_region,
            "cluster_name": cluster_name
        }
    )
    investigation_notes.append(note)
    
    # Create a summary of the analysis results
    analysis_summary = "### Incident Analysis Summary\n\n"
    
    # Count metrics and logs
    metric_count = len(state.metrics)
    log_count = len(state.logs)
    
    analysis_summary += f"* Analyzed {entity_count} entities\n"
    analysis_summary += f"* Collected {metric_count} metrics\n"
    analysis_summary += f"* Analyzed {log_count} log entries\n"
    
    # Add root cause information if available
    if state.root_cause:
        analysis_summary += f"\n#### Root Cause Analysis:\n"
        analysis_summary += f"* Primary Root Cause: {state.root_cause}\n"
        
        # Add more RCA details if available
        if state.rca_details:
            if "secondary_factors" in state.rca_details and state.rca_details["secondary_factors"]:
                analysis_summary += f"* Secondary Factors:\n"
                for factor in state.rca_details["secondary_factors"]:
                    analysis_summary += f"  - {factor}\n"
            
            if "confidence_level" in state.rca_details:
                analysis_summary += f"* Confidence Level: {state.rca_details['confidence_level']}/10\n"
            
            if "evidence_summary" in state.rca_details:
                analysis_summary += f"* Evidence Summary: {state.rca_details['evidence_summary']}\n"
                
            # Add prioritized factors section if available
            if "prioritized_factors" in state.rca_details and state.rca_details["prioritized_factors"]:
                analysis_summary += f"\n#### Prioritized Factors:\n"
                for i, factor in enumerate(state.rca_details["prioritized_factors"]):
                    analysis_summary += f"* Factor {i+1}: {factor.get('factor', 'Unknown')}\n"
                    analysis_summary += f"  - Importance: {factor.get('importance', 'N/A')}/10\n"
                    analysis_summary += f"  - Confidence: {factor.get('confidence', 'N/A')}/10\n"
            
            if "recommendations" in state.rca_details and state.rca_details["recommendations"]:
                analysis_summary += f"\n#### Recommendations:\n"
                for rec in state.rca_details["recommendations"]:
                    analysis_summary += f"* {rec}\n"
    
    # Add system check results if available
    if state.system_checks:
        analysis_summary += "\n#### System Check Results:\n"
        for check in state.system_checks:
            status_icon = "✅" if check.status == "ALL_GOOD" else "❌"
            analysis_summary += f"* {status_icon} {check.category}: {check.description}\n"
    
    # Add remediation actions summary if available
    if state.remediation_actions:
        analysis_summary += "\n#### Recommended Actions:\n"
        for action in state.remediation_actions:
            analysis_summary += f"* {action.title}: {action.description}\n"
    
    # Add steps from runbook results if available
    if state.runbook_results:
        analysis_summary += "\n#### Runbook Steps Executed:\n"
        for step in state.runbook_results:
            status_icon = "❌" if step.issues_found else "✅"
            analysis_summary += f"* {status_icon} {step.step_title}: {step.summary}\n"
    
    # Add the analysis summary to the investigation notes
    summary_note = InvestigationNote(
        timestamp=datetime.now(timezone.utc).isoformat(),
        agent="analysis_engine",
        note=analysis_summary,
        data={
            "metrics_count": metric_count,
            "logs_count": log_count,
            "entity_count": entity_count
        }
    )
    investigation_notes.append(summary_note)
    
    # Add the summary notes and events to the state
    # These need to be added first so they're included in the MongoDB storage
    state.investigation_notes.extend(investigation_notes)
    state.timeline.extend([completion_event, summary_event])
    
    # Update the state with the final status
    state.current_phase = "completed"
    state.analysis_summary = analysis_summary
    
    # Store the final incident state in MongoDB
    try:
        # Import here to avoid circular imports
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Store the complete state directly with update_tool_results=False
        # This ensures tool_results are not overwritten and not stored in the incident document
        incident_doc_id = await mongodb_service.store_incident_state(
            state=state,
            update_tool_results=False
        )
        logger.info(f"Stored final incident state in MongoDB for incident {state.incident_id}, document ID: {incident_doc_id}")
        
    except Exception as db_error:
        logger.error(f"Error storing final incident in MongoDB: {str(db_error)}", exc_info=True)
    
    # Return the updated fields (not the entire state)
    return {
        "current_phase": "completed",
        "investigation_notes": investigation_notes,  # Only return the new notes
        "timeline": [completion_event, summary_event],  # Only return the new events
        "analysis_summary": analysis_summary
    } 