"""
Runbook execution workflow node.
"""

import json
from datetime import datetime, timezone
from typing import Dict
from loguru import logger

from ai_incident_manager.models.workflow_state import IncidentState, InvestigationNote, TimelineEvent
from ai_incident_manager.agents.runbook_agent import runbook_agent, RunbookAgentDeps, get_alert_category_config
from ..utils.decorators import log_node_execution
from ..config import get_workflow_config


@log_node_execution("execute_runbooks")
async def execute_runbooks(state: IncidentState) -> Dict:
    """
    Execute runbooks based on the entities identified in the incident.
    
    This step:
    1. Identifies the alert category and primary entity
    2. Sets up the runbook context with entity relationships
    3. Invokes the runbook agent to execute the investigation
    4. Processes results and updates incident state
    
    Returns:
        Dict containing execution results
    """
    # Get configuration
    config = get_workflow_config()
    
    # Initialize tracking structures
    runbooks_executed = []
    runbook_results = []
    timeline_events = []
    findings = []
    investigation_notes = []
    entities_analyzed = set()
    # Initialize entity_updates dictionary to avoid UnboundLocalError
    entity_updates = {}
    
    # Get the current alert information
    alert_category = state.alert_category
    condition_name = state.condition_name
    
    if not alert_category:
        logger.error("No alert category found in state")
        return {
            "runbooks_executed": [],
            "findings": [],
            "timeline_events": [
                {
                    "id": f"evt-runbook-error-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "No Runbooks Executed",
                    "description": "Missing alert category information",
                    "type": "error",
                    "source": "AI Agent Framework",
                    "tags": [{"label": "Error", "variant": "error"}]
                }
            ],
            "error": "Missing alert category information",
            "current_phase": "runbooks_executed"
        }
    
    # Get entity information
    entities = state.entities
    primary_entity_guid = None
    
    # find the primary entity
    primary_entity = next((e for e in entities if e.is_primary), None)
    primary_entity_guid = primary_entity.entity_guid
    
    # If no entities, cannot continue
    if not primary_entity:
        logger.error("No entities found in state")
        return {
            "runbooks_executed": [],
            "findings": [],
            "timeline_events": [
                {
                    "id": f"evt-runbook-error-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "No Runbooks Executed",
                    "description": "No entities found for runbook execution",
                    "type": "error",
                    "source": "AI Agent Framework",
                    "tags": [{"label": "Error", "variant": "error"}]
                }
            ],
            "error": "No entities found for runbook execution",
            "current_phase": "runbooks_executed"
        }
    
    # Add a timeline event for starting runbook execution
    timeline_events.append({
        "id": f"evt-runbook-start-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Runbook Execution",
        "description": f"Executing runbook for {alert_category}",
        "type": "info",
        "source": "AI Agent Framework",
        "tags": [{"label": "Runbooks", "variant": "secondary"}]
    })
    
    try:
        # Get the runbook service and fetch the runbook for the alert category
        alert_category_config = await get_alert_category_config(alert_category)
        
        if not alert_category_config:
            logger.warning(f"No alert category config found for {alert_category}")
            alert_category_config = {}
        
        # Set up runbook agent dependencies with all needed information
        # since its crucial agent to use o1 model
        deps = RunbookAgentDeps(
            openai_client=config.openai_o1_client,
            metrics_collector=config.metrics_collector,
            query_client=config.nr_query_client,
            logs_client=config.nr_logs_client,
            state=state, # state has the entities, alert_category, condition_name, etc.
            alert_category=alert_category_config,  # Pass the alert category config
            runbook=alert_category_config.get("runbook", "")  # Pass the runbook directly
        )
        
        # Execute the alert category runbook directly
        execution_start_event = {
            "id": f"evt-runbook-exec-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": f"Executing Runbook for {alert_category}",
            "description": f"Starting execution of runbook for alert category {alert_category}",
            "type": "action",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Runbook", "variant": "secondary"},
                {"label": "Executing", "variant": "outline"}
            ]
        }
        timeline_events.append(execution_start_event)
        
        # Construct a prompt that includes time window information
        time_window_info = ""
        if hasattr(state, "since_time") and hasattr(state, "until_time") and state.since_time and state.until_time:
            since_time_str = state.since_time
            until_time_str = state.until_time
            time_window_info = f"\nTime Window for Analysis: {since_time_str} to {until_time_str}"
        
        prompt = f"""
Investigate and resolve the {alert_category} issue for the primary entity.

I need you to execute the runbook for this alert category. The primary entity is {primary_entity.entity_name} with GUID {primary_entity.entity_guid}. 
{time_window_info}

Environmental Context:
- Product: {state.product}
- Region: {state.region}
- Landscape: {state.landscape}
- New Relic Region: {state.nr_region}
- Cluster Name: {state.cluster_name}

Follow all runbook steps, collecting metrics, logs, and information as needed.
Use the tools available to perform each step in the runbook.

All the entities in the state are relevant to the alert category.
{json.dumps([{"guid": e.entity_guid, "name": e.entity_name, "type": e.entity_type} for e in entities])}

For each step:
1. Call the appropriate tools to collect information
2. Categorize collected information as metrics, logs, or information
3. Create a summary for the collected information and the step
4. For collected metrics, logs and information, add the entity information (guid, name, type) to the collected information

Note:
Actual metrics, logs and information collected are stored in db by each tool call, so you only have to summarize the collected information

In your response, include:
1. Each Step result with the step title, description, tool, parameters, summary, issues_found, collected information summary
2. A summary of the runbook execution
"""
        
        runbook_execution_result = await runbook_agent.run(prompt, deps=deps)
        result = runbook_execution_result.data
        
        # Get the runbook metadata
        runbook_id = result.runbook_id
        runbook_name = result.runbook_name
        runbook_description = result.runbook_description
        runbook_summary = result.summary
        issues_found = result.issues_found
            
        # Process the step results
        step_results = result.step_results
        
        # Track metrics, logs, and information for investigation notes
        collected_metrics = []
        collected_logs = []
        collected_info = []
        
        # Process each step result
        for step in step_results:
            step_title = step.step_title
            step_description = step.step_description
            tool = step.tool
            step_summary = step.summary
            issues_found_in_step = step.issues_found

            # Separate collected information by type for tracking
            step_metrics = []
            step_logs = []
            step_info = []

            collected_information_list = []
            for collected_information in step.collected_information:
                info_item = {
                    "content_type": collected_information.content_type,
                    "content_summary": collected_information.content_summary,
                    "entity_guid": collected_information.entity_guid,
                    "entity_name": collected_information.entity_name,
                    "entity_type": collected_information.entity_type,
                    "nrql_query": collected_information.nrql_query
                }
                collected_information_list.append(info_item)
                
                # Categorize by content type
                if collected_information.content_type == "metric":
                    step_metrics.append(info_item)
                    collected_metrics.append(info_item)
                elif collected_information.content_type == "log":
                    step_logs.append(info_item)
                    collected_logs.append(info_item)
                else:  # information
                    step_info.append(info_item)
                    collected_info.append(info_item)
                
            # Format the result into a standardized structure
            step_data = {
                "title": step_title,
                "description": step_description,
                "tool": tool,
                "summary": step_summary,
                "collected_information": collected_information_list,
                "issues_found": issues_found_in_step,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Add step result to timeline events
            timeline_events.append({
                "id": f"evt-runbook-step-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": f"Runbook Step: {step_title}",
                "description": step_summary,
                "type": "action",
                "source": "Runbook Agent",
                "tags": [
                    {"label": "Step", "variant": "secondary"},
                    {"label": "Issue" if issues_found_in_step else "OK", 
                     "variant": "destructive" if issues_found_in_step else "success"}
                ]
            })
            
            # Add step result to investigation notes
            investigation_notes.append({
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "agent": "Runbook Agent",
                "note": f"Step: {step_title} - {step_summary}",
                "data": step_data
            })
        
        # Record the runbook execution
        runbooks_executed.append({
            "runbook_id": runbook_id,
            "runbook_name": runbook_name,
            "entity_guid": primary_entity_guid,
            "issues_found": issues_found,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        
        # Add timeline event for the runbook execution results
        status_label = "Issues Found" if issues_found else "All Clear"
        status_variant = "destructive" if issues_found else "success"
        
        timeline_events.append({
            "id": f"evt-runbook-result-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": f"Runbook Execution Results: {status_label}",
            "description": runbook_summary,
            "type": "analysis",
            "source": "Runbook Agent",
            "tags": [
                {"label": status_label, "variant": status_variant},
                {"label": "Runbook", "variant": "secondary"}
            ]
        })

        # Add a summary investigation note
        investigation_notes.append({
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "Runbook Agent",
            "note": f"Executed runbook '{runbook_name}' - {runbook_summary}",
            "data": {
                "runbook_id": runbook_id,
                "runbook_name": runbook_name,
                "runbook_description": runbook_description,
                "issues_found": issues_found,
                "metrics_count": len(collected_metrics),
                "logs_count": len(collected_logs),
                "info_count": len(collected_info)
            }
        })
        
        # Add a final completion event
        timeline_events.append({
            "id": f"evt-runbook-complete-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Runbook Execution Complete",
            "description": f"Completed execution of '{runbook_name}' runbook for {alert_category}",
            "type": "info",
            "source": "AI Agent Framework",
            "tags": [{"label": "Complete", "variant": "success"}]
        })
        
        # Store the complete incident state in MongoDB
        try:
            # Import here to avoid circular imports
            from ai_incident_manager.services.mongodb_service import get_mongodb_service
            mongodb_service = get_mongodb_service()
            
            # Store the IncidentState model directly
            # This will handle serialization of Pydantic models
            incident_doc_id = await mongodb_service.store_incident_state(state)
            logger.info(f"Stored incident state in MongoDB for incident {state.incident_id}, document ID: {incident_doc_id}")
            
        except Exception as db_error:
            logger.error(f"Error storing incident in MongoDB: {str(db_error)}", exc_info=True)
        
    except Exception as e:
        logger.error(f"Error executing runbooks: {str(e)}", exc_info=True)
        
        # Add error event to timeline
        timeline_events.append({
            "id": f"evt-runbook-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Runbook Execution Error",
            "description": str(e),
            "type": "error",
            "source": "AI Agent Framework",
            "tags": [{"label": "Error", "variant": "error"}]
        })
    
    # Return the updated state values
    return_values = {
        "runbooks_executed": runbooks_executed,
        "runbook_results": step_results if 'step_results' in locals() else [],
        "runbook_id": result.runbook_id if 'result' in locals() else None,
        "runbook_name": result.runbook_name if 'result' in locals() else None,
        "runbook_summary": result.summary if 'result' in locals() else None,
        "timeline": timeline_events,
        "investigation_notes": investigation_notes,
        "current_phase": "runbooks_executed"
    }
    
    # Now, fetch all metrics, logs, and events for this run to update the state
    try:
        if hasattr(state, "run_id") and state.run_id:
            # Import here to avoid circular imports
            from ai_incident_manager.services.mongodb_service import get_mongodb_service
            mongodb_service = get_mongodb_service()
            
            run_id = state.run_id
            logger.info(f"Fetching data from MongoDB for run_id: {run_id}")
            
            # Fetch metrics
            metrics_query = {"incident_id": state.incident_id, "run_id": run_id}
            metrics_cursor = mongodb_service._metrics_collection.find(metrics_query)
            metrics = await metrics_cursor.to_list(length=None)
            logger.info(f"Found {len(metrics)} metrics for run_id: {run_id}")
            
            # Fetch logs
            logs_query = {"incident_id": state.incident_id, "run_id": run_id}
            logs_cursor = mongodb_service._logs_collection.find(logs_query)
            logs = await logs_cursor.to_list(length=None)
            logger.info(f"Found {len(logs)} logs for run_id: {run_id}")
            
            # Fetch events
            events_query = {"incident_id": state.incident_id, "run_id": run_id}
            events_cursor = mongodb_service._events_collection.find(events_query)
            events = await events_cursor.to_list(length=None)
            logger.info(f"Found {len(events)} events for run_id: {run_id}")
            
            # Update the return values to include these collections
            if metrics:
                return_values["metrics"] = [mongodb_service._deserialize_from_mongodb(metric) for metric in metrics]
            if logs:
                return_values["logs"] = [mongodb_service._deserialize_from_mongodb(log) for log in logs]
            if events:
                return_values["events"] = [mongodb_service._deserialize_from_mongodb(event) for event in events]
    except Exception as e:
        logger.error(f"Error fetching data from MongoDB: {str(e)}")
    
    return return_values 