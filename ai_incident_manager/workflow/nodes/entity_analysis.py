"""
Entity analysis workflow nodes.
"""

import json
from datetime import datetime, timezone
from typing import Dict
from loguru import logger

from ai_incident_manager.models.workflow_state import IncidentState, InvestigationNote, TimelineEvent
from ai_incident_manager.agents.entity_analyzer_agent import entity_analyzer_agent, EntityAnalyzerDeps
from ai_incident_manager.agents.entity_relationship_agent import entity_relationship_agent, EntityRelationshipDeps
from ai_incident_manager.services.entity_type_service import get_entity_type_service
from ..utils.decorators import log_node_execution
from ..config import get_workflow_config


@log_node_execution("analyze_entity")
async def analyze_entity(state: IncidentState) -> Dict:
    """
    Analyze the entities identified by the alert parser using the entity analyzer agent.
    
    This function takes the entities identified by the alert parser and enriches them
    with detailed information about their type, metrics, logs, and relationships.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Analyzing entities for incident {state.incident_id}")
    
    # Get configuration
    config = get_workflow_config()
    
    # Check if we have entities to analyze
    if not state.entities:
        logger.warning("No entities found to analyze")

        investigation_notes = []
        # Add an error note
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "entity_analyzer",
            "note": "No entities found to analyze",
            "data": {"error": "No entities found"}
        }
        investigation_notes.append(error_note)
        
        # Add an error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Entity Analysis Error",
            "description": "No entities found to analyze",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        return {
            "error": "No entities found",
            "investigation_notes": investigation_notes,
            "timeline": [error_event]
        }
    
    # Get entity types from service
    entity_types = get_entity_type_service().get_all_entity_types()
    
    # Set up dependencies for the agent
    deps = EntityAnalyzerDeps(
        openai_client=config.openai_client,
        nr_query_client=config.nr_query_client,
        nr_logs_client=config.nr_logs_client,
        entity_analyzer=config.entity_analyzer,
        entity_types=entity_types,
        state=state
    )
    
    # Process each entity
    updated_entities = []
    timeline_events = []
    investigation_notes = []
    
    # Create a timeline event for starting entity analysis
    start_event: TimelineEvent = {
        "id": f"evt-start-entity-analysis-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Entity Analysis",
        "description": f"Analyzing {len(state.entities)} entities for additional details",
        "type": "investigation",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Analysis", "variant": "secondary"}
        ]
    }
    timeline_events.append(start_event)
    
    for entity in state.entities:
        entity_guid = entity.entity_guid
        if not entity_guid:
            logger.warning("Entity without GUID found. Skipping.")
            continue
        
        try:
            # Get entity details from New Relic
            logger.info(f"Starting get_entity_details")
            entity_details = await config.entity_analyzer.get_entity_details(entity_guid)
            
            if not entity_details:
                logger.warning(f"No details found for entity {entity_guid}. Skipping.")
                continue
            
            # Add contextual information to the prompt
            context_info = {
                "product": state.product,
                "nr_region": state.nr_region,
                "landscape": state.landscape,
                "region": state.region,
                "cluster_name": state.cluster_name,
                "since_time": state.since_time,
                "until_time": state.until_time
            }
            
            # Create combined entity context for the prompt
            entity_prompt = {
                "entity_details": entity_details,
                "context": context_info
            }
            
            entity_details_json = json.dumps(entity_prompt, indent=2)
            
            # Create a timeline event for entity retrieval
            retrieval_event: TimelineEvent = {
                "id": f"evt-entity-retrieval-{entity_guid[:8]}-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": f"Retrieved Entity Details",
                "description": f"Retrieved detailed information for entity {entity_guid}",
                "type": "investigation",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Entity Analysis", "variant": "secondary"}
                ]
            }
            timeline_events.append(retrieval_event)
            
            # Run the entity analyzer agent with the entity details
            entity_type_result = await entity_analyzer_agent.run(
                entity_details_json,
                deps=deps
            )
            
            # Extract entity type information
            analysis_result = entity_type_result.data
            entity_details = analysis_result.entity_details
            
            # Add to the list of updated entities
            updated_entities.append(entity_details)
            
            # Create a timeline event for the analysis
            analysis_event: TimelineEvent = {
                "id": f"evt-entity-analysis-{entity_guid[:8]}-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": f"Entity Analysis: {entity_details.entity_name}",
                "description": f"Analyzed entity {entity_guid}: Type={entity_details.entity_type}",
                "type": "analysis",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Entity Analysis", "variant": "secondary"},
                    {"label": entity_details.entity_type, "variant": "outline"}
                ]
            }
            timeline_events.append(analysis_event)
            
            # Add to investigation notes
            note: InvestigationNote = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "agent": "entity_analyzer",
                "note": f"Analyzed entity {entity_details.entity_name} ({entity_guid}): {entity_details.entity_type}",
                "data": {
                    "entity_guid": entity_guid,
                    "entity_type": entity_details.entity_type,
                    "entity_name": entity_details.entity_name,
                    "product": state.product,
                    "region": state.region,
                    "landscape": state.landscape
                }
            }
            investigation_notes.append(note)
                
        except Exception as e:
            logger.error(f"Error analyzing entity {entity_guid}: {str(e)}")
            error_event: TimelineEvent = {
                "id": f"evt-entity-error-{entity_guid[:8] if entity_guid else 'unknown'}-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Entity Analysis Error",
                "description": f"Error analyzing entity: {str(e)}",
                "type": "investigation",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Error", "variant": "destructive"}
                ]
            }
            timeline_events.append(error_event)
            
            error_note: InvestigationNote = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "agent": "entity_analyzer",
                "note": f"Error analyzing entity: {str(e)}",
                "data": {"error": str(e)}
            }
            investigation_notes.append(error_note)
    
    # Create a completion timeline event
    completion_event: TimelineEvent = {
        "id": f"evt-entity-analysis-complete-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Entity Analysis Complete",
        "description": f"Completed analysis of {len(updated_entities)} entities",
        "type": "investigation",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Analysis", "variant": "secondary"},
            {"label": "Complete", "variant": "outline"}
        ]
    }
    timeline_events.append(completion_event)
    
    # Create a summary note
    note: InvestigationNote = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "agent": "entity_analyzer",
        "note": f"Analyzed {len(updated_entities)} entities",
        "data": {
            "entity_count": len(updated_entities),
            "entity_types": list(set(getattr(e, "entity_type", "Unknown") for e in updated_entities)),
            "product": state.product,
            "landscape": state.landscape,
            "region": state.region
        }
    }
    investigation_notes.append(note)
    
    # TODO enrich state entity with entity_details from updated_entities
    # combine updated_entities with state.entities
    state_entities = []
    state_entities.extend(state.entities)
    # flag to say if any entity was updated
    entity_updated = False
    if updated_entities:
        for entity in updated_entities:
            for state_entity in state_entities:
                if entity.entity_guid == state_entity.entity_guid:
                    # which field is not none from updated_entity update the state_entity with that field
                    for field in entity.__dict__:
                        if getattr(entity, field) is not None:
                            setattr(state_entity, field, getattr(entity, field))
                            logger.info(f"Updated {field} for {state_entity.entity_name}")
                            entity_updated = True
    # if updated entity is not in state_entities, add it
    for entity in updated_entities:
        if entity not in state_entities:
            state_entities.append(entity)
    if entity_updated:
        logger.info("Entities updated")
    else:
        logger.info("No entities updated")

    # Update the state with the processed entities
    return {
        "entities": state_entities,
        "current_phase": "entity_analysis_completed",
        "investigation_notes": investigation_notes,
        "timeline": timeline_events
    }


@log_node_execution("analyze_entity_relationships")
async def analyze_entity_relationships(state: IncidentState) -> Dict:
    """
    Analyze relationships between entities to build a topology of the affected system.
    
    This node uses the EntityRelationshipAgent to discover and map relationships
    between entities for a comprehensive understanding of the incident context.
    """
    logger.info(f"Analyzing entity relationships for incident {state.incident_id}")
    
    # Get configuration
    config = get_workflow_config()
    
    # Set up dependencies for the agent
    deps = EntityRelationshipDeps(
        openai_client=config.openai_client,
        nr_query_client=config.nr_query_client,
        state=state
    )
        
    # Prepare alert data and entities for the agent
    alert_category = state.alert_category
    entities = state.entities
    alert_data = state.raw_alert
    
    # Get time window from state
    since_time = getattr(state, "since_time", None)
    until_time = getattr(state, "until_time", None)
    
    # Create a timeline event for starting relationship analysis
    start_event = {
        "id": f"evt-start-rel-analysis-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Entity Relationship Analysis",
        "description": f"Analyzing relationships for {len(entities)} entities",
        "type": "investigation",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Relationships", "variant": "secondary"}
        ]
    }
    
    # Prepare the prompt for the agent
    prompt = f"""
    Please analyze the entity relationships for this incident.
    
    Alert Category: {alert_category}
    Primary Entities: {json.dumps([{"guid": e.entity_guid, "name": getattr(e, "entity_name", "Unknown"), 
                                  "type": getattr(e, "entity_type", "Unknown")} for e in entities])}
    Time Window: {since_time} to {until_time}
    
    Environmental Context:
    - Issue ID: {state.incident_id}
    - Alert Title: {state.title}
    - Alert Description: {state.description}
    - Product: {state.product}
    - Region: {state.region}
    - Landscape: {state.landscape}
    - New Relic Region: {state.nr_region}
    - Cluster Name: {state.cluster_name}

    Full Alert Data:
    {json.dumps(alert_data, indent=2)}
    
    I need a complete relationship map of all relevant entities including:
    1. Primary entities from the alert
    2. Related entities that provide context
    3. Important relationships between entities
    4. Any fallback entities if primary entities are missing
    """
    
    try:
        # Run the agent
        result = await entity_relationship_agent.run(prompt, deps=deps)
        
        # Extract the relationship data
        relationship_data = result.data

        # if the state entities is not in the relationship_data.entities, add it
        for entity in state.entities:
            if entity not in relationship_data.entities:
                relationship_data.entities.append(entity)

        # count of primary and related entities
        primary_entity_count = len([e for e in relationship_data.entities if e.is_primary])
        related_entity_count = len([e for e in relationship_data.entities if not e.is_primary])
        
        # Create a completion event
        completion_event = {
            "id": f"evt-complete-rel-analysis-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Entity Relationship Analysis Complete",
            "description": f"Found {relationship_data.total_entity_count} entities and {len(relationship_data.relationships)} relationships",
            "type": "analysis",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Relationships", "variant": "secondary"},
                {"label": "Complete", "variant": "outline"}
            ]
        }
        
        # Add an investigation note
        investigation_note = {
            "timestamp": datetime.now().isoformat(),
            "phase": "entity_relationships",
            "note": f"Analyzed entity relationships and identified {primary_entity_count} primary entities and {related_entity_count} related entities",
            "source": "EntityRelationshipAgent",
            "data": {
                "product": state.product,
                "region": state.region,
                "landscape": state.landscape,
                "cluster_name": state.cluster_name,
                "entity_count": relationship_data.total_entity_count,
                "relationship_count": len(relationship_data.relationships)
            }
        }
        
        # Return the updated state fields
        return {
            "entities": relationship_data.entities,
            "entity_relationships": relationship_data.relationships,
            "investigation_notes": [investigation_note],
            "timeline": [start_event, completion_event],
            "current_phase": "entity_relationships_analyzed"
        }
    
    except Exception as e:
        logger.error(f"Error analyzing entity relationships: {str(e)}")
        
        # Create an error event
        error_event = {
            "id": f"evt-error-rel-analysis-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Entity Relationship Analysis Error",
            "description": f"Error analyzing entity relationships: {str(e)}",
            "type": "error",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        # Return error information
        return {
            "error": str(e),
            "timeline": [start_event, error_event],
            "current_phase": "entity_relationships_error"
        } 