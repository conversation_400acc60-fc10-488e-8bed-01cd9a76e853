"""
Reporting workflow nodes for ticket creation and result reporting.
"""

import os
from datetime import datetime, timezone
from typing import Dict
from loguru import logger

from ai_incident_manager.models.workflow_state import IncidentState, InvestigationNote, TimelineEvent
from ai_incident_manager.agents.reporting_agent import ReportingAgentDeps, get_reporting_agent, ReportingResult
from ai_incident_manager.services.ado_service import get_ado_service
from ai_incident_manager.services.teams_service import get_teams_service
from ..utils.decorators import log_node_execution
from ..config import get_workflow_config


@log_node_execution("create_ticket")
async def create_ticket(state: IncidentState) -> Dict:
    """
    Create an Azure DevOps ticket for the incident.
    
    This node is executed after the alert analysis to create a ticket
    that will be updated with analysis results as they become available.
    
    Args:
        state: Current workflow state with alert analysis results
        
    Returns:
        Updated fields of workflow state with ticket information
    """
    logger.info(f"Creating ticket for incident {state.incident_id}")
    
    # Get configuration
    config = get_workflow_config()
    
    # Create a timeline event for starting ticket creation
    start_event: TimelineEvent = {
        "id": f"evt-ticket-start-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Creating Incident Ticket",
        "description": "Creating a ticket in Azure DevOps for incident tracking",
        "type": "integration",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Ticket", "variant": "outline"}
        ]
    }
    
    # Initialize ticket creation results
    events = [start_event]
    notes = []
    ticket_id = None
    ticket_url = None
    dashboard_url = os.environ.get("IIM_DASHBOARD_BASE_URL", "https://obv-ai-compute.ivantiai.com:8080/incident/") + state.incident_id
    error_message = None
    
    # Attempt to create a ticket
    try:
        # Get the services
        ado_service = get_ado_service()
        reporting_agent = get_reporting_agent()
        
        # Set up the dependencies for the agent
        deps = ReportingAgentDeps(
            openai_client=config.openai_client
        )
        
        # Convert entities to dictionaries
        entities_list = []
        if state.entities:
            entities_list = [entity.model_dump() if hasattr(entity, "model_dump") else entity for entity in state.entities]
            
        # Generate prompt for the agent
        prompt = f"""
        Generate a title and description for an incident tracking ticket for an Azure DevOps work item.
        
        Here is the incident information:
        - Incident ID: {state.incident_id}
        - Alert Title: {state.alert_title if hasattr(state, 'alert_title') else 'Unknown'}
        - Condition Name: {state.condition_name if hasattr(state, 'condition_name') else 'Unknown'}
        - Policy Name: {state.policy_name if hasattr(state, 'policy_name') else 'Unknown'}
        - Cluster: {state.cluster_name if hasattr(state, 'cluster_name') else 'Unknown'}
        - Severity: {state.severity if hasattr(state, 'severity') else 'Unknown'}
        - Product: {state.product if hasattr(state, 'product') else 'Unknown'}
        - Region: {state.region if hasattr(state, 'region') else 'Unknown'}
        - Start Time: {state.start_time if hasattr(state, 'start_time') else 'Unknown'}
        
        The title should be concise but informative, including the severity level.
        The description should include all relevant details about the incident.
        """
        
        # Get ticket content from the agent
        logger.info(f"Generating ticket content for incident {state.incident_id}")
        agent_response = await reporting_agent.run(prompt, deps=deps)
        report_content = agent_response.data
        
        # Create the ticket using ADO service directly
        logger.info(f"Creating new ticket for incident {state.incident_id}")
        
        # Create a custom state with the elements needed for ticket creation
        ticket_state = {
            "incident_id": state.incident_id,
            "title": report_content.ticket_title,
            "severity": state.severity,
            "product": state.product,
            "region": state.region,
            "root_cause": state.root_cause,
            "rca_details": state.rca_details,
            "entities": entities_list,  # Use the pre-converted entities
            "runbook_results": state.runbook_results,  # Use the pre-converted runbook results
            "start_time": state.start_time
        }
        
        # Create the ticket
        ticket_result = ado_service.create_initial_ticket(ticket_state)
        
        if ticket_result:
            logger.info(f"Successfully created ticket {ticket_result.get('ticket_id')}")
            ticket_id = ticket_result.get("ticket_id")
            ticket_url = ticket_result.get("ticket_url")
            
            # Create success event
            success_event: TimelineEvent = {
                "id": f"evt-ticket-created-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Azure DevOps Ticket Created",
                "description": f"Created ticket #{ticket_id}",
                "type": "integration",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Azure DevOps", "variant": "outline"},
                    {"label": "Success", "variant": "success"}
                ]
            }
            events.append(success_event)
            
            # Add ticket note
            ticket_note: InvestigationNote = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "agent": "reporting_agent",
                "note": f"Created Azure DevOps ticket #{ticket_id}",
                "data": {
                    "ticket_id": ticket_id,
                    "ticket_url": ticket_url
                }
            }
            notes.append(ticket_note)
            
            # Return the updated state fields
            return {
                "ado_ticket_id": ticket_id,
                "ado_ticket_url": ticket_url,
                "dashboard_url": dashboard_url,
                "timeline": events,
                "investigation_notes": notes,
                "current_phase": "ticket_created"
            }
        else:
            error_message = "Failed to create ticket"
            logger.error(error_message)
            
            # Create error event
            error_event: TimelineEvent = {
                "id": f"evt-ticket-error-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Azure DevOps Ticket Creation Failed",
                "description": error_message,
                "type": "integration",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Azure DevOps", "variant": "outline"},
                    {"label": "Error", "variant": "destructive"}
                ]
            }
            events.append(error_event)
            
            # Add error note
            error_note: InvestigationNote = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "agent": "reporting_agent",
                "note": f"Failed to create Azure DevOps ticket: {error_message}",
                "data": {"error": error_message}
            }
            notes.append(error_note)
            
            # Return error information
            return {
                "error": error_message,
                "timeline": events,
                "investigation_notes": notes
            }
    
    except Exception as e:
        error_message = str(e)
        logger.error(f"Error creating ticket: {error_message}")
        
        # Create an error event
        error_event: TimelineEvent = {
            "id": f"evt-ticket-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Azure DevOps Ticket Creation Error",
            "description": error_message,
            "type": "integration",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Azure DevOps", "variant": "outline"},
                {"label": "Error", "variant": "destructive"}
            ]
        }
        events.append(error_event)
        
        # Add error note
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "reporting_agent",
            "note": f"Error creating Azure DevOps ticket: {error_message}",
            "data": {"error": error_message}
        }
        notes.append(error_note)
        
        # Return error information
        return {
            "error": error_message,
            "timeline": events,
            "investigation_notes": notes
        }


@log_node_execution("report_results")
async def report_results(state: IncidentState) -> Dict:
    """
    Report the incident analysis results by updating the ADO ticket and sending a Teams notification.
    
    This node is executed at the end of the workflow, right before the finish node,
    to update the ticket with all analysis results and send a notification.
    
    Args:
        state: Current workflow state with complete analysis results
        
    Returns:
        Updated fields of workflow state with reporting results
    """
    logger.info(f"Reporting results for incident {state.incident_id}")
    
    # Get configuration
    config = get_workflow_config()
    
    # Create a timeline event for starting results reporting
    start_event: TimelineEvent = {
        "id": f"evt-report-start-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Reporting Analysis Results",
        "description": "Updating Azure DevOps ticket and sending Teams notification",
        "type": "integration",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Reporting", "variant": "outline"}
        ]
    }
    
    # Initialize reporting results
    events = [start_event]
    notes = []
    ticket_id = None
    ticket_url = None
    dashboard_url = os.environ.get("IIM_DASHBOARD_BASE_URL", "https://obv-ai-compute.ivantiai.com:8080/incident/") + state.incident_id
    success = False
    error_message = None
    
    # Get existing ticket ID from state if available
    existing_ticket_id = state.ado_ticket_id if hasattr(state, "ado_ticket_id") else None
    existing_ticket_url = state.ado_ticket_url if hasattr(state, "ado_ticket_url") else None
    
    # Attempt to report results
    try:
        # Get the services
        ado_service = get_ado_service()
        teams_service = get_teams_service()
        reporting_agent = get_reporting_agent()
        
        # Set up the dependencies for the agent
        deps = ReportingAgentDeps(
            openai_client=config.openai_client
        )
        
        # Extract entities from state
        entities_list = []
        if state.entities:
            entities_list = [entity.model_dump() if hasattr(entity, "model_dump") else entity for entity in state.entities]
        
        # Extract runbook results from state
        runbook_results_list = []
        if state.runbook_results:
            runbook_results_list = [result.model_dump() if hasattr(result, "model_dump") else result for result in state.runbook_results]
        
        prompt = f"""
        Analyze the incident data and generate reporting content for tickets and notifications.
        
        Here is entire incident state:
        {state.model_dump_json(indent=2)}
        """
        # Generate reporting content using the agent
        logger.info(f"Generating reporting content for incident {state.incident_id}")
        agent_response = await reporting_agent.run(
            prompt,
            deps=deps
        )
        
        # Extract the reporting content
        report_content = agent_response.data
        
        # Handle ticket creation or update
        if existing_ticket_id:
            # Update existing ticket
            logger.info(f"Updating existing ticket {existing_ticket_id} for incident {state.incident_id}")
            
            # Create a custom state with the elements needed for the ticket update
            ticket_state = {
                "incident_id": state.incident_id,
                "title": report_content.ticket_title,
                "severity": state.severity,
                "product": state.product,
                "region": state.region,
                "root_cause": state.root_cause,
                "rca_details": state.rca_details,
                "entities": entities_list,  # Use the pre-converted entities_list
                "runbook_results": runbook_results_list,  # Use the pre-converted runbook_results
                "analysis_summary": state.analysis_summary,
                "start_time": state.start_time
            }
            
            # Update the ticket
            update_result = ado_service.update_ticket_with_results(existing_ticket_id, ticket_state)
            
            if update_result:
                logger.info(f"Successfully updated ticket {existing_ticket_id}")
                ticket_id = existing_ticket_id
                ticket_url = existing_ticket_url
                
                # Create success event
                ticket_event: TimelineEvent = {
                    "id": f"evt-ticket-updated-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Azure DevOps Ticket Updated",
                    "description": f"Updated ticket #{ticket_id} with analysis results",
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Azure DevOps", "variant": "outline"},
                        {"label": "Success", "variant": "success"}
                    ]
                }
                events.append(ticket_event)
                
                # Add ticket note
                ticket_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": f"Updated Azure DevOps ticket #{ticket_id} with analysis results",
                    "data": {
                        "ticket_id": ticket_id,
                        "ticket_url": ticket_url
                    }
                }
                notes.append(ticket_note)
            else:
                logger.error(f"Failed to update ticket {existing_ticket_id}")
                error_message = f"Failed to update ticket {existing_ticket_id}"
                
                # Create error event
                error_event: TimelineEvent = {
                    "id": f"evt-ticket-error-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Azure DevOps Ticket Update Failed",
                    "description": error_message,
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Azure DevOps", "variant": "outline"},
                        {"label": "Error", "variant": "destructive"}
                    ]
                }
                events.append(error_event)
                
                # Add error note
                error_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": f"Failed to update Azure DevOps ticket: {error_message}",
                    "data": {"error": error_message}
                }
                notes.append(error_note)
        else:
            # Create new ticket
            logger.info(f"Creating new ticket for incident {state.incident_id}")
            
            # Create a custom state with the elements needed for ticket creation
            ticket_state = {
                "incident_id": state.incident_id,
                "title": report_content.ticket_title,
                "severity": state.severity,
                "product": state.product,
                "region": state.region,
                "root_cause": state.root_cause,
                "rca_details": state.rca_details,
                "start_time": state.start_time
            }
            
            # Create the ticket
            ticket_result = ado_service.create_initial_ticket(ticket_state)
            
            if ticket_result:
                logger.info(f"Successfully created ticket {ticket_result.get('ticket_id')}")
                ticket_id = ticket_result.get("ticket_id")
                ticket_url = ticket_result.get("ticket_url")
                
                # Create success event
                ticket_event: TimelineEvent = {
                    "id": f"evt-ticket-created-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Azure DevOps Ticket Created",
                    "description": f"Created ticket #{ticket_id}",
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Azure DevOps", "variant": "outline"},
                        {"label": "Success", "variant": "success"}
                    ]
                }
                events.append(ticket_event)
                
                # Add ticket note
                ticket_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": f"Created Azure DevOps ticket #{ticket_id}",
                    "data": {
                        "ticket_id": ticket_id,
                        "ticket_url": ticket_url
                    }
                }
                notes.append(ticket_note)
            else:
                logger.error("Failed to create ticket")
                error_message = "Failed to create ticket"
                
                # Create error event
                error_event: TimelineEvent = {
                    "id": f"evt-ticket-error-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Azure DevOps Ticket Creation Failed",
                    "description": error_message,
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Azure DevOps", "variant": "outline"},
                        {"label": "Error", "variant": "destructive"}
                    ]
                }
                events.append(error_event)
                
                # Add error note
                error_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": f"Failed to create Azure DevOps ticket: {error_message}",
                    "data": {"error": error_message}
                }
                notes.append(error_note)
        
        # Send Teams notification if ticket was created/updated successfully
        if ticket_id:
            logger.info(f"Sending Teams notification for incident {state.incident_id}")
            
            # Create a notification state with ticket information
            notification_state = {
                "incident_id": state.incident_id,
                "title": report_content.notification_title,
                "severity": state.severity,
                "product": state.product,
                "region": state.region,
                "root_cause": state.root_cause,
                "rca_details": state.rca_details,
                "entities": entities_list,  # Use pre-converted entities
                "runbook_results": runbook_results_list,  # Use pre-converted runbook results
                "ado_ticket_id": ticket_id,
                "ado_ticket_url": ticket_url,
                "dashboard_url": dashboard_url,
                "start_time": state.start_time
            }
            
            # Send the notification
            notification_result = teams_service.send_incident_notification(notification_state)
            
            if notification_result:
                logger.info("Successfully sent Teams notification")
                success = True
                
                # Create success event
                teams_event: TimelineEvent = {
                    "id": f"evt-teams-sent-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Teams Notification Sent",
                    "description": "Sent incident analysis results to Teams channel",
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Teams", "variant": "outline"},
                        {"label": "Success", "variant": "success"}
                    ]
                }
                events.append(teams_event)
                
                # Add Teams note
                teams_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": "Sent incident analysis results to Teams channel",
                    "data": {}
                }
                notes.append(teams_note)
            else:
                logger.error("Failed to send Teams notification")
                error_message = "Failed to send Teams notification"
                
                # Create error event
                error_event: TimelineEvent = {
                    "id": f"evt-teams-error-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Teams Notification Failed",
                    "description": error_message,
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Teams", "variant": "outline"},
                        {"label": "Error", "variant": "destructive"}
                    ]
                }
                events.append(error_event)
                
                # Add error note
                error_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": f"Failed to send Teams notification: {error_message}",
                    "data": {"error": error_message}
                }
                notes.append(error_note)
        
        # Set success flag if ticket was successfully created/updated
        success = ticket_id is not None
        
    except Exception as e:
        logger.error(f"Error reporting results: {str(e)}")
        error_message = str(e)
        
        # Create an error event
        error_event: TimelineEvent = {
            "id": f"evt-report-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Results Reporting Error",
            "description": str(e),
            "type": "integration",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        events.append(error_event)
        
        # Add error note
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "reporting_agent",
            "note": f"Error reporting results: {str(e)}",
            "data": {"error": str(e)}
        }
        notes.append(error_note)
    
    # Return the updated state
    return {
        "ado_ticket_id": ticket_id or existing_ticket_id,
        "ado_ticket_url": ticket_url or existing_ticket_url,
        "dashboard_url": dashboard_url,
        "timeline": events,
        "investigation_notes": notes,
        "current_phase": "results_reported" if success else "reporting_error",
        "reporting_complete": success,
        "error": error_message
    } 