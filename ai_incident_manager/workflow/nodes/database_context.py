"""
Database context workflow node for retrieving additional context information.
"""

import json
import traceback
from datetime import datetime, timezone
from typing import Dict
from loguru import logger

from ai_incident_manager.models.workflow_state import IncidentState, InvestigationNote, TimelineEvent
from ai_incident_manager.agents.database_agent import database_agent, DatabaseAgentDeps, get_database_client
from ..utils.decorators import log_node_execution
from ..config import get_workflow_config


@log_node_execution("retrieve_database_context")
async def retrieve_database_context(state: IncidentState) -> Dict:
    """
    Retrieve additional context from PostgreSQL database for incident analysis.
    
    This node queries the database to get additional information related to the incident
    such as deployment history, recent configuration changes, historical incidents,
    and other relevant context that can help with root cause analysis.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Retrieving database context for incident {state.incident_id}")
    
    # Get configuration
    config = get_workflow_config()
    
    # Initialize tracking structures
    timeline_events = []
    investigation_notes = []
    
    # Create a timeline event for starting database context retrieval
    start_event = {
        "id": f"evt-start-db-context-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Database Context Retrieval",
        "description": f"Retrieving additional context from database for {state.product} incident",
        "type": "investigation",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Database", "variant": "secondary"}
        ]
    }
    timeline_events.append(start_event)
    
    try:
        # Set up dependencies for the agent
        deps = DatabaseAgentDeps(
            openai_client=config.openai_client,
            database_client=get_database_client(),
            state=state
        )
        
        # Prepare the prompt for the agent
        product = state.product if state.product else "Unknown"
        entity_name = "Unknown"
        entity_type = "Unknown"
        
        # Get primary entity details if available
        if state.entities:
            primary_entity = next((e for e in state.entities if e.is_primary), state.entities[0])
            entity_name = primary_entity.entity_name
            entity_type = primary_entity.entity_type
        
        # Construct the prompt
        prompt = f"""
        Please retrieve relevant database context for this incident:
        
        Incident ID: {state.incident_id}
        Product: {product}
        Primary Entity: {entity_name} ({entity_type})
        Alert: {state.alert_title}
        Alert Category: {state.alert_category}
        
        Time Window: {state.since_time} to {state.until_time}
        
        I need comprehensive context from the database that might help understand 
        what's happening with this incident. Look for recent deployments, configuration
        changes, similar historical incidents, and any other relevant data.
        
        For Neurons incidents, focus on Kubernetes-related data.
        For MDM incidents, focus on application and dependency data.
        
        Return a structured analysis of the most relevant findings.
        """
        
        # Call the database agent
        result = await database_agent.run(prompt, deps=deps)
        database_context = result.data
        
        # Create timeline events for database context retrieval
        context_event = {
            "id": f"evt-db-context-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Database Context Retrieved",
            "description": database_context.context_summary,
            "type": "analysis",
            "source": "Database Agent",
            "tags": [
                {"label": "Database", "variant": "secondary"},
                {"label": "Context", "variant": "outline"}
            ]
        }
        timeline_events.append(context_event)
        
        # Create investigation notes for each context item
        for item in database_context.context_items:
            note = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "agent": "database_agent",
                "note": f"Database context: {item.query_name} - {item.summary}",
                "data": {
                    "query_name": item.query_name,
                    "query_type": item.query_type,
                    "summary": item.summary,
                    "relevance_score": item.relevance_score,
                    "data_count": item.data_count,
                    "key_findings": item.key_findings
                }
            }
            investigation_notes.append(note)
        
        # Create a summary investigation note
        summary_note = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "database_agent",
            "note": f"Database context summary: {database_context.context_summary}",
            "data": {
                "priority_findings": database_context.priority_findings,
                "entities_examined": database_context.entities_examined,
                "additional_data_recommendations": database_context.additional_data_recommendations,
                "confidence_level": database_context.confidence_level
            }
        }
        investigation_notes.append(summary_note)
        
        # Create timeline events for priority findings
        if database_context.priority_findings:
            for i, finding in enumerate(database_context.priority_findings):
                priority_event = {
                    "id": f"evt-db-finding-{i}-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": f"Database Finding #{i+1}",
                    "description": finding,
                    "type": "analysis",
                    "source": "Database Agent",
                    "tags": [
                        {"label": "Finding", "variant": "secondary"}
                    ]
                }
                timeline_events.append(priority_event)
        
        # Final completion event
        completion_event = {
            "id": f"evt-db-context-complete-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Database Context Analysis Complete",
            "description": f"Retrieved and analyzed relevant database context with confidence level: {database_context.confidence_level}/10",
            "type": "analysis",
            "source": "Database Agent",
            "tags": [
                {"label": "Complete", "variant": "success"}
            ]
        }
        timeline_events.append(completion_event)
        
        # Return state updates
        return {
            "database_context": database_context.model_dump(),
            "investigation_notes": investigation_notes,
            "timeline": timeline_events,
            "current_phase": "database_context_retrieved"
        }
    
    except Exception as e:
        logger.error(f"Error retrieving database context: {str(e)}")
        error_traceback = traceback.format_exc()
        logger.error(f"Traceback: {error_traceback}")
        
        # Create error event
        error_event = {
            "id": f"evt-db-context-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Database Context Error",
            "description": f"Error retrieving database context: {str(e)}",
            "type": "error",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        timeline_events.append(error_event)
        
        # Create error note
        error_note = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "database_agent",
            "note": f"Error retrieving database context: {str(e)}",
            "data": {"error": str(e), "traceback": error_traceback}
        }
        investigation_notes.append(error_note)
        
        return {
            "error": str(e),
            "investigation_notes": investigation_notes,
            "timeline": timeline_events,
            "current_phase": "database_context_error"
        } 