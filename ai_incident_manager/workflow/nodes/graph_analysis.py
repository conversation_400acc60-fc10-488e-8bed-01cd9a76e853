"""
Graph Analysis Workflow Node.

This module provides a workflow node for performing graph-based entity relationship
analysis and cascading failure detection as part of the incident management workflow.
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

from ai_incident_manager.models.workflow_state import IncidentState
from ai_incident_manager.services.graph_service import get_graph_service
from ai_incident_manager.agents.cascading_failure_agent import (
    cascading_failure_agent, CascadingFailureAgentDeps
)
from ai_incident_manager.workflow.utils.decorators import log_node_execution
from ai_incident_manager.workflow.utils.mongodb_utils import store_workflow_result
from ai_incident_manager.workflow.config import get_query_client, get_openai_client
from ai_incident_manager.models.graph_models import GraphAnalysisRequest, GraphAnalysisResponse


@log_node_execution("graph_analysis")
async def graph_analysis_node(state: IncidentState) -> IncidentState:
    """
    Perform comprehensive graph-based analysis for the incident.
    
    This node:
    1. Builds entity relationship graphs
    2. Analyzes cascading failure potential
    3. Identifies critical paths and bottlenecks
    4. Provides recommendations for monitoring and mitigation
    
    Args:
        state: Current incident state
        
    Returns:
        Updated incident state with graph analysis results
    """
    # Handle both dict and object state formats
    incident_id = state.get("incident_id") if isinstance(state, dict) else state.incident_id
    entities = state.get("entities", []) if isinstance(state, dict) else state.entities
    alert_category = state.get("alert_category") if isinstance(state, dict) else state.alert_category
    since_time = state.get("since_time") if isinstance(state, dict) else state.since_time
    until_time = state.get("until_time") if isinstance(state, dict) else state.until_time
    
    logger.info(f"Starting graph analysis for incident {incident_id}")
    
    try:
        # Get primary entity information
        primary_entity = None
        for entity in entities:
            if entity.get("is_primary", False) or len(entities) == 1:
                primary_entity = entity
                break
        
        if not primary_entity:
            primary_entity = entities[0] if entities else None
        
        if not primary_entity:
            logger.warning("No primary entity found for graph analysis")
            return state
        
        # Extract entity information
        entity_guid = primary_entity.get("entity_guid")
        entity_type = primary_entity.get("entity_type", "UNKNOWN")
        entity_name = primary_entity.get("entity_name", "")
        cluster_name = primary_entity.get("cluster_name")
        
        # Get time range
        since_time_ms = _parse_time_to_ms(since_time) if since_time else None
        until_time_ms = _parse_time_to_ms(until_time) if until_time else None
        
        # Build graph analysis request
        analysis_request = GraphAnalysisRequest(
            primary_entity_guid=entity_guid,
            entity_type=entity_type,
            entity_name=entity_name,
            alert_category=alert_category,
            cluster_name=cluster_name,
            since_time_ms=since_time_ms,
            until_time_ms=until_time_ms,
            requested_analyses=["cascading_failure", "critical_paths", "graph_metrics"]
        )
        
        # Perform enhanced graph analysis using entity relationship service with validation
        enhanced_analysis = await _run_enhanced_graph_analysis(state, analysis_request)
        
        # Store results in state
        graph_analysis_results = {
            "analysis_timestamp": datetime.now().isoformat(),
            "primary_entity": {
                "guid": entity_guid,
                "type": entity_type,
                "name": entity_name,
                "cluster": cluster_name
            },
            "enhanced_analysis": enhanced_analysis,
            "analysis_metadata": {
                "since_time": since_time,
                "until_time": until_time,
                "alert_category": alert_category,
                "graph_depth": analysis_request.analysis_config.max_depth,
                "failure_threshold": analysis_request.analysis_config.failure_threshold
            }
        }
        
        # Update state with graph analysis results
        if isinstance(state, dict):
            state["graph_analysis"] = graph_analysis_results
            
            # Update investigation state to continue to graph enrichment
            if "investigation_state" not in state or state["investigation_state"] is None:
                state["investigation_state"] = {
                    "notes": [],
                    "current_step": 0,
                    "next_steps": ["graph_enrichment"]
                }
            else:
                state["investigation_state"]["next_steps"] = ["graph_enrichment"]
        else:
            if not hasattr(state, 'graph_analysis') or state.graph_analysis is None:
                state.graph_analysis = {}
            
            state.graph_analysis = graph_analysis_results
            
            # Update investigation state to continue to graph enrichment
            if not hasattr(state, 'investigation_state') or state.investigation_state is None:
                state.investigation_state = {
                    "notes": [],
                    "current_step": 0,
                    "next_steps": ["graph_enrichment"]
                }
            else:
                state.investigation_state["next_steps"] = ["graph_enrichment"]
        
        # Store in MongoDB
        await store_workflow_result(
            incident_id=incident_id,
            node_name="graph_analysis",
            result=graph_analysis_results
        )
        
        logger.info(f"Graph analysis completed for incident {incident_id}")
        return state
        
    except Exception as e:
        logger.error(f"Error in graph analysis node: {str(e)}")
        # Store error information
        error_result = {
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
            "node": "graph_analysis"
        }
        
        if isinstance(state, dict):
            if "graph_analysis" not in state or state["graph_analysis"] is None:
                state["graph_analysis"] = {}
            state["graph_analysis"]["error"] = error_result
        else:
            if not hasattr(state, 'graph_analysis') or state.graph_analysis is None:
                state.graph_analysis = {}
            state.graph_analysis["error"] = error_result
        
        # Store error in MongoDB
        await store_workflow_result(
            incident_id=incident_id,
            node_name="graph_analysis",
            result=error_result
        )
        
        return state


async def _run_enhanced_graph_analysis(state: IncidentState, request: GraphAnalysisRequest) -> Dict[str, Any]:
    """
    Run enhanced graph analysis using the entity relationship service with validation.
    
    This method uses the enhanced entity relationship service that combines
    telemetry-based discovery with architectural relationships and metric validation.
    
    Args:
        state: Current incident state
        request: Graph analysis request
        
    Returns:
        Enhanced analysis results with cascading failure detection
    """
    try:
        # Get entity relationship service
        from ai_incident_manager.services.entity_relationship_service import EntityRelationshipService
        
        # Initialize the service with New Relic client
        query_client = get_query_client()
        entity_service = EntityRelationshipService()
        entity_service.query_client = query_client
        
        # Run enhanced traversal with validation
        logger.info("Running enhanced graph analysis with architectural augmentation")
        analysis_result = await entity_service.traverse_relationships_with_validation(
            primary_entity_guid=request.primary_entity_guid,
            entity_type=request.entity_type,
            entity_name=request.entity_name,
            alert_category=state.get("alert_category") if isinstance(state, dict) else state.alert_category,
            cluster_name=request.cluster_name,
            since_time_ms=request.since_time_ms,
            until_time_ms=request.until_time_ms
        )
        
        # Transform the result into the expected format
        enhanced_result = {
            "analysis_summary": f"Enhanced graph analysis for {request.entity_name} identified {analysis_result['investigation_summary']['cascading_failures_detected']} cascading failures",
            "risk_assessment": f"Risk level: {analysis_result['investigation_summary']['risk_level']} with blast radius of {analysis_result['investigation_summary']['blast_radius']} entities",
            "critical_insights": [
                f"Total entities investigated: {analysis_result['investigation_summary']['total_entities_investigated']}",
                f"Cascading failures detected: {analysis_result['investigation_summary']['cascading_failures_detected']}",
                f"Architectural relationships found: {analysis_result['investigation_summary']['architectural_relationships_found']}",
            ],
            "recommended_actions": analysis_result.get('failure_analysis', {}).get('recommendations', []),
            "entities_to_monitor": [entity['entityName'] for entity in analysis_result.get('cascading_failures', [])],
            "estimated_resolution_time": "15-30 minutes based on cascading failure complexity",
            "confidence_score": 0.9 if analysis_result['investigation_summary']['cascading_failures_detected'] > 0 else 0.7,
            "analysis_methodology": "Enhanced traversal using alert-category patterns with architectural augmentation and metric validation",
            "limitations": "Analysis limited to configured architectural relationships and available telemetry",
            "cascading_failure_analysis": {
                "primary_entity": request.entity_name,
                "affected_entities": [entity['entityName'] for entity in analysis_result.get('cascading_failures', [])],
                "critical_dependencies": [entity['entityName'] for entity in analysis_result.get('architectural_relationships', [])],
                "potential_blast_radius": analysis_result['investigation_summary']['blast_radius'],
                "estimated_impact_score": analysis_result.get('failure_analysis', {}).get('impact_score', 0.0),
                "failure_paths": []  # Could be enhanced with path analysis
            },
            "raw_analysis_result": analysis_result  # Include full result for debugging
        }
        
        return enhanced_result
        
    except Exception as e:
        logger.error(f"Error running enhanced graph analysis: {str(e)}")
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
            "analysis_summary": f"Error during analysis: {str(e)}",
            "risk_assessment": "Unable to assess risk due to analysis error",
            "critical_insights": [],
            "recommended_actions": ["Check system configuration and connectivity"],
            "entities_to_monitor": [],
            "estimated_resolution_time": "Unknown due to analysis error",
            "confidence_score": 0.0,
            "analysis_methodology": "Enhanced graph analysis with error",
            "limitations": f"Analysis failed: {str(e)}",
            "cascading_failure_analysis": {
                "primary_entity": request.entity_name,
                "affected_entities": [],
                "critical_dependencies": [],
                "potential_blast_radius": 0,
                "estimated_impact_score": 0.0,
                "failure_paths": []
            }
        }


async def _run_graph_analysis_agent(state: IncidentState, request: GraphAnalysisRequest) -> Dict[str, Any]:
    """
    Run the cascading failure analysis agent.
    
    Args:
        state: Current incident state
        request: Graph analysis request
        
    Returns:
        Agent analysis results
    """
    try:
        # Get dependencies
        openai_client = get_openai_client()
        query_client = get_query_client()
        
        # Set up agent dependencies
        deps = CascadingFailureAgentDeps(
            openai_client=openai_client,
            nr_query_client=query_client,
            state=state
        )
        
        # Create analysis prompt
        analysis_prompt = f"""
        Please perform a comprehensive cascading failure analysis for the following incident:
        
        Incident ID: {state.incident_id}
        Primary Entity: {request.entity_name} (GUID: {request.primary_entity_guid})
        Entity Type: {request.entity_type}
        Alert Category: {request.alert_category}
        Cluster: {request.cluster_name or 'Unknown'}
        
        Alert Details:
        - Title: {state.alert_title}
        - Severity: {state.severity}
        - Description: {state.description}
        
        Please provide:
        1. Build the entity relationship graph (max depth: {request.analysis_config.max_depth})
        2. Analyze potential cascading failures (threshold: {request.analysis_config.failure_threshold})
        3. Identify critical paths and bottlenecks
        4. Assess system resilience and blast radius
        5. Provide specific, actionable recommendations
        
        Focus on:
        - Entities most likely to be affected by cascading failures
        - Critical dependencies that need immediate attention
        - Recommended actions to prevent failure propagation
        - Monitoring and alerting enhancements
        - Estimated impact and recovery time
        
        Time Range: {state.since_time} to {state.until_time}
        """
        
        # Run the agent
        logger.info("Running cascading failure analysis agent")
        result = await cascading_failure_agent.run(analysis_prompt, deps=deps)
        
        # Convert result to dictionary
        agent_result = {
            "analysis_summary": result.data.analysis_summary,
            "risk_assessment": result.data.risk_assessment,
            "critical_insights": result.data.critical_insights,
            "recommended_actions": result.data.recommended_actions,
            "entities_to_monitor": result.data.entities_to_monitor,
            "estimated_resolution_time": result.data.estimated_resolution_time,
            "confidence_score": result.data.confidence_score,
            "analysis_methodology": result.data.analysis_methodology,
            "limitations": result.data.limitations,
            "cascading_failure_analysis": {
                "primary_entity": result.data.cascading_failure_analysis.primary_entity,
                "affected_entities": result.data.cascading_failure_analysis.affected_entities,
                "critical_dependencies": result.data.cascading_failure_analysis.critical_dependencies,
                "potential_blast_radius": result.data.cascading_failure_analysis.potential_blast_radius,
                "estimated_impact_score": result.data.cascading_failure_analysis.estimated_impact_score,
                "failure_paths": [
                    {
                        "path": path.path,
                        "probability": path.probability,
                        "total_weight": path.total_weight,
                        "criticality_score": path.criticality_score
                    }
                    for path in result.data.cascading_failure_analysis.failure_paths
                ]
            }
        }
        
        return agent_result
        
    except Exception as e:
        logger.error(f"Error running graph analysis agent: {str(e)}")
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@log_node_execution("graph_enrichment")
async def graph_enrichment_node(state: IncidentState) -> IncidentState:
    """
    Enrich the incident with graph-based insights.
    
    This node takes the graph analysis results and enriches the incident
    with additional context, recommendations, and monitoring suggestions.
    
    Args:
        state: Current incident state
        
    Returns:
        Updated incident state with enriched information
    """
    # Handle both dict and object state formats
    incident_id = state.get("incident_id") if isinstance(state, dict) else state.incident_id
    
    logger.info(f"Starting graph enrichment for incident {incident_id}")
    
    try:
        graph_analysis = state.get('graph_analysis', {}) if isinstance(state, dict) else getattr(state, 'graph_analysis', {})
        
        if not graph_analysis or 'enhanced_analysis' not in graph_analysis:
            logger.warning("No graph analysis results found for enrichment")
            return state
        
        enhanced_analysis = graph_analysis['enhanced_analysis']
        
        # Extract enrichment data from enhanced analysis
        enrichment = {
            "timestamp": datetime.now().isoformat(),
            "critical_entities": enhanced_analysis.get("entities_to_monitor", []),
            "blast_radius": enhanced_analysis.get("cascading_failure_analysis", {}).get("potential_blast_radius", 0),
            "impact_score": enhanced_analysis.get("cascading_failure_analysis", {}).get("estimated_impact_score", 0.0),
            "recommended_actions": enhanced_analysis.get("recommended_actions", []),
            "risk_level": _calculate_risk_level_enhanced(enhanced_analysis),
            "monitoring_recommendations": _generate_monitoring_recommendations_enhanced(enhanced_analysis),
            "escalation_criteria": _generate_escalation_criteria_enhanced(enhanced_analysis),
            "architectural_relationships": enhanced_analysis.get("cascading_failure_analysis", {}).get("critical_dependencies", []),
            "confidence_score": enhanced_analysis.get("confidence_score", 0.0),
            "analysis_methodology": enhanced_analysis.get("analysis_methodology", "unknown")
        }
        
        # Update state with enrichment
        if isinstance(state, dict):
            if "enrichment" not in state or state["enrichment"] is None:
                state["enrichment"] = {}
            
            state["enrichment"]["graph_based"] = enrichment
            
            # Update investigation state to continue to system checks
            if "investigation_state" not in state or state["investigation_state"] is None:
                state["investigation_state"] = {
                    "notes": [],
                    "current_step": 0,
                    "next_steps": ["determine_system_checks"]
                }
            else:
                state["investigation_state"]["next_steps"] = ["determine_system_checks"]
        else:
            if not hasattr(state, 'enrichment') or state.enrichment is None:
                state.enrichment = {}
            
            state.enrichment["graph_based"] = enrichment
            
            # Update investigation state to continue to system checks
            if not hasattr(state, 'investigation_state') or state.investigation_state is None:
                state.investigation_state = {
                    "notes": [],
                    "current_step": 0,
                    "next_steps": ["determine_system_checks"]
                }
            else:
                state.investigation_state["next_steps"] = ["determine_system_checks"]
        
        # Store enrichment in MongoDB
        await store_workflow_result(
            incident_id=incident_id,
            node_name="graph_enrichment",
            result=enrichment
        )
        
        logger.info(f"Graph enrichment completed for incident {incident_id}")
        return state
        
    except Exception as e:
        logger.error(f"Error in graph enrichment node: {str(e)}")
        return state


def _parse_time_to_ms(time_str: str) -> Optional[int]:
    """Parse ISO time string to milliseconds."""
    try:
        dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        return int(dt.timestamp() * 1000)
    except:
        return None


def _calculate_risk_level_enhanced(enhanced_analysis: Dict[str, Any]) -> str:
    """Calculate risk level based on enhanced analysis results."""
    try:
        impact_score = enhanced_analysis.get("cascading_failure_analysis", {}).get("estimated_impact_score", 0.0)
        blast_radius = enhanced_analysis.get("cascading_failure_analysis", {}).get("potential_blast_radius", 0)
        confidence_score = enhanced_analysis.get("confidence_score", 0.0)
        
        # Calculate combined risk score
        risk_score = (impact_score * 0.4 + (blast_radius / 10.0) * 0.3 + confidence_score * 0.3)
        
        if risk_score >= 0.8:
            return "critical"
        elif risk_score >= 0.6:
            return "high"
        elif risk_score >= 0.4:
            return "medium"
        else:
            return "low"
            
    except:
        return "unknown"


def _calculate_risk_level(agent_analysis: Dict[str, Any]) -> str:
    """Calculate risk level based on agent analysis."""
    try:
        impact_score = agent_analysis.get("cascading_failure_analysis", {}).get("estimated_impact_score", 0.0)
        blast_radius = agent_analysis.get("cascading_failure_analysis", {}).get("potential_blast_radius", 0)
        confidence_score = agent_analysis.get("confidence_score", 0.0)
        
        # Calculate combined risk score
        risk_score = (impact_score * 0.4 + (blast_radius / 10.0) * 0.3 + confidence_score * 0.3)
        
        if risk_score >= 0.8:
            return "critical"
        elif risk_score >= 0.6:
            return "high"
        elif risk_score >= 0.4:
            return "medium"
        else:
            return "low"
            
    except:
        return "unknown"


def _generate_monitoring_recommendations_enhanced(enhanced_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Generate monitoring recommendations based on enhanced analysis."""
    recommendations = []
    
    try:
        # Critical entities monitoring
        critical_entities = enhanced_analysis.get("entities_to_monitor", [])
        if critical_entities:
            recommendations.append({
                "type": "entity_monitoring",
                "priority": "high",
                "description": f"Monitor {len(critical_entities)} critical entities with cascading failures",
                "entities": critical_entities[:5],  # Limit to top 5
                "metrics": ["error_rate", "response_time", "availability", "cpu_usage", "memory_usage"]
            })
        
        # Architectural relationship monitoring
        arch_dependencies = enhanced_analysis.get("cascading_failure_analysis", {}).get("critical_dependencies", [])
        if arch_dependencies:
            recommendations.append({
                "type": "architectural_dependency_monitoring", 
                "priority": "high",
                "description": f"Monitor {len(arch_dependencies)} architectural dependencies",
                "dependencies": arch_dependencies[:3],
                "metrics": ["service_availability", "response_time", "connection_errors"]
            })
        
        # Cascading failure monitoring
        cascading_analysis = enhanced_analysis.get("cascading_failure_analysis", {})
        if cascading_analysis.get("potential_blast_radius", 0) > 3:
            recommendations.append({
                "type": "cascading_failure_monitoring",
                "priority": "critical",
                "description": "Monitor for cascading failure propagation with enhanced detection",
                "blast_radius": cascading_analysis.get("potential_blast_radius", 0),
                "affected_entities": cascading_analysis.get("affected_entities", [])[:3],
                "validation_method": enhanced_analysis.get("analysis_methodology", "unknown")
            })
        
    except Exception as e:
        logger.error(f"Error generating enhanced monitoring recommendations: {str(e)}")
    
    return recommendations


def _generate_monitoring_recommendations(agent_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Generate monitoring recommendations based on analysis."""
    recommendations = []
    
    try:
        # Critical entities monitoring
        critical_entities = agent_analysis.get("entities_to_monitor", [])
        if critical_entities:
            recommendations.append({
                "type": "entity_monitoring",
                "priority": "high",
                "description": f"Monitor {len(critical_entities)} critical entities",
                "entities": critical_entities[:5],  # Limit to top 5
                "metrics": ["cpu_usage", "memory_usage", "error_rate", "availability"]
            })
        
        # Cascading failure monitoring
        cascading_analysis = agent_analysis.get("cascading_failure_analysis", {})
        if cascading_analysis.get("potential_blast_radius", 0) > 3:
            recommendations.append({
                "type": "cascading_failure_monitoring",
                "priority": "high",
                "description": "Monitor for cascading failure propagation",
                "blast_radius": cascading_analysis.get("potential_blast_radius", 0),
                "affected_entities": cascading_analysis.get("affected_entities", [])[:3]
            })
        
        # Critical path monitoring
        critical_dependencies = cascading_analysis.get("critical_dependencies", [])
        if critical_dependencies:
            recommendations.append({
                "type": "critical_path_monitoring",
                "priority": "medium",
                "description": "Monitor critical dependency paths",
                "dependencies": critical_dependencies[:3]
            })
        
    except Exception as e:
        logger.error(f"Error generating monitoring recommendations: {str(e)}")
    
    return recommendations


def _generate_escalation_criteria_enhanced(enhanced_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Generate escalation criteria based on enhanced analysis."""
    criteria = []
    
    try:
        cascading_analysis = enhanced_analysis.get("cascading_failure_analysis", {})
        confidence_score = enhanced_analysis.get("confidence_score", 0.0)
        
        # High impact escalation with confidence consideration
        impact_score = cascading_analysis.get("estimated_impact_score", 0.0)
        if impact_score > 0.7 and confidence_score > 0.8:
            criteria.append({
                "condition": "high_impact_with_high_confidence",
                "threshold": 0.7,
                "current_value": impact_score,
                "confidence": confidence_score,
                "action": "escalate_to_senior_engineer",
                "urgency": "immediate",
                "reasoning": "High impact cascading failure detected with high confidence"
            })
        
        # Large blast radius escalation
        blast_radius = cascading_analysis.get("potential_blast_radius", 0)
        if blast_radius > 5:
            criteria.append({
                "condition": "large_blast_radius_validated",
                "threshold": 5,
                "current_value": blast_radius,
                "action": "escalate_to_incident_commander",
                "urgency": "high",
                "reasoning": "Large number of entities affected by cascading failure"
            })
        
        # Architectural dependency failures
        critical_deps = cascading_analysis.get("critical_dependencies", [])
        if len(critical_deps) > 2:
            criteria.append({
                "condition": "critical_architectural_dependencies_affected",
                "threshold": 2,
                "current_value": len(critical_deps),
                "action": "notify_architecture_team",
                "urgency": "medium",
                "reasoning": "Multiple critical business logic dependencies affected"
            })
        
        # Low confidence but high impact - needs human validation
        if impact_score > 0.6 and confidence_score < 0.6:
            criteria.append({
                "condition": "high_impact_low_confidence",
                "threshold": 0.6,
                "current_value": impact_score,
                "confidence": confidence_score,
                "action": "request_manual_validation",
                "urgency": "medium",
                "reasoning": "Potential high impact but analysis confidence is low"
            })
        
    except Exception as e:
        logger.error(f"Error generating enhanced escalation criteria: {str(e)}")
    
    return criteria


def _generate_escalation_criteria(agent_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Generate escalation criteria based on analysis."""
    criteria = []
    
    try:
        cascading_analysis = agent_analysis.get("cascading_failure_analysis", {})
        
        # High impact escalation
        if cascading_analysis.get("estimated_impact_score", 0.0) > 0.7:
            criteria.append({
                "condition": "high_impact_detected",
                "threshold": 0.7,
                "current_value": cascading_analysis.get("estimated_impact_score", 0.0),
                "action": "escalate_to_senior_engineer",
                "urgency": "immediate"
            })
        
        # Large blast radius escalation
        if cascading_analysis.get("potential_blast_radius", 0) > 5:
            criteria.append({
                "condition": "large_blast_radius",
                "threshold": 5,
                "current_value": cascading_analysis.get("potential_blast_radius", 0),
                "action": "escalate_to_incident_commander",
                "urgency": "high"
            })
        
        # Critical dependencies affected
        critical_deps = cascading_analysis.get("critical_dependencies", [])
        if len(critical_deps) > 2:
            criteria.append({
                "condition": "critical_dependencies_affected",
                "threshold": 2,
                "current_value": len(critical_deps),
                "action": "notify_stakeholders",
                "urgency": "medium"
            })
        
    except Exception as e:
        logger.error(f"Error generating escalation criteria: {str(e)}")
    
    return criteria


# Export the workflow nodes
__all__ = [
    "graph_analysis_node",
    "graph_enrichment_node"
]