"""
Alert analysis workflow node.
"""

import json
from datetime import datetime, timezone
from typing import Dict
from loguru import logger

from ai_incident_manager.models.workflow_state import IncidentState, InvestigationNote, TimelineEvent
from ai_incident_manager.agents.alert_parser_agent import alert_parser_agent, AlertParserDeps
from ..utils.decorators import log_node_execution
from ..config import get_workflow_config


@log_node_execution("analyze_alert")
async def analyze_alert(state: IncidentState) -> Dict:
    """
    Analyze the alert using the alert parser agent.
    
    This is a simplified version of the analyze_alert node from the main workflow,
    focused on using the alert parser agent to extract relevant information.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Analyzing alert for Issue {state.incident_id}")
    
    # Get configuration
    config = get_workflow_config()
    
    # Extract alert information
    alert = state.raw_alert
    
    # Extract alert creation timestamp (in milliseconds)
    alert_created_at = alert.get("createdAt")
    logger.info(f"Alert created at timestamp: {alert_created_at}")
    
    # Set up dependencies for the agent
    deps = AlertParserDeps(
        openai_client=config.openai_client,
        nr_query_client=config.nr_query_client,
        entity_analyzer=config.entity_analyzer,
    )

    # Initialize timeline events list
    timeline_events = []
    
    # Prepare the user prompt with the alert data
    user_prompt = f"""
    ```json
    {json.dumps(alert, indent=2)}
    ```
    """
    
    # Run the agent
    try:
        # Add the alert creation time to the context for the agent to use
        # Format the user prompt to include explicit instruction about the alert creation time
        user_prompt_with_context = f"""
        Analyze the following alert data.
        The alert was created at timestamp {alert_created_at} (epoch milliseconds).
        Please use this timestamp for time window calculations.
        
        ```json
        {json.dumps(alert, indent=2)}
        ```
        """
        
        result = await alert_parser_agent.run(user_prompt_with_context, deps=deps)
        analysis_result = result.data
        
        # Convert Pydantic model to dict for easier handling
        analysis_dict = analysis_result.model_dump()
        
        # Create a timeline event for the alert trigger
        alert_event: TimelineEvent = {
            "id": f"evt-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Alert Triggered",
            "description": f"{analysis_result.alert_title}",
            "type": "alert",
            "source": "New Relic",
            "tags": [
                {"label": state.severity, "variant": "destructive"},
                {"label": "Alert", "variant": "secondary"}
            ]
        }

        # Add the alert event to the timeline
        timeline_events.append(alert_event)
        
        # Add an investigation note
        investigation_notes = []
        note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "alert_parser",
            "note": f"Alert analyzed. Category: {analysis_result.alert_category}",
            "data": analysis_dict
        }
        investigation_notes.append(note)
        
        # Create a timeline event for the alert analysis
        analysis_event: TimelineEvent = {
            "id": f"evt-{datetime.now(timezone.utc).timestamp():.0f}-analysis",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Alert Analysis",
            "description": f"Analyzed alert and identified category: {analysis_result.alert_category}",
            "type": "analysis",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Analysis", "variant": "secondary"}
            ]
        }
        
        # Add a timeline event for the analysis time window
        if analysis_result.since_time and analysis_result.until_time:
            time_window_event: TimelineEvent = {
                "id": f"evt-{datetime.now(timezone.utc).timestamp():.0f}-time-window",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Analysis Time Window",
                "description": f"Analyzing data from {analysis_result.since_time} to {analysis_result.until_time}",
                "type": "analysis",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Time Window", "variant": "secondary"}
                ]
            }
            # Add this event to the timeline list later
            timeline_events = [alert_event, analysis_event, time_window_event]
        else:
            time_window_event = None
            timeline_events = [alert_event, analysis_event]
        
        # Get epoch millisecond timestamps directly from AlertParserResponse
        # No need to convert from ISO format anymore as they're already provided
        since_time_ms = analysis_result.since_time_ms
        until_time_ms = analysis_result.until_time_ms
        
        # Log the time window information
        logger.info(f"Using time window for analysis: since={analysis_result.since_time} ({since_time_ms}), until={analysis_result.until_time} ({until_time_ms})")
        logger.info(f"Original alert creation time: {analysis_result.alert_created_at}")
        
        # Return the updated state fields
        return {
            "alert_category": analysis_result.alert_category,
            "alert_runbook": analysis_result.alert_runbook,
            "entities": analysis_result.entities,
            "condition_name": analysis_result.condition_name,
            "condition_id": analysis_result.condition_id,
            "policy_name": analysis_result.policy_name,
            "policy_id": analysis_result.policy_id,
            "cluster_name": analysis_result.cluster_name,
            "product": analysis_result.product,
            "nr_region": analysis_result.nr_region,
            "landscape": analysis_result.landscape,
            "region": analysis_result.region,
            "since_time": analysis_result.since_time,
            "until_time": analysis_result.until_time,
            "since_time_ms": since_time_ms,
            "until_time_ms": until_time_ms,
            "threshold_duration": analysis_result.threshold_duration,
            "aggregation_window": analysis_result.aggregation_window,
            "investigation_notes": investigation_notes,  # Return the list of notes to be appended the new note
            "timeline": timeline_events,  # Only return the new events
            "current_phase": "alert_analysis_completed"
        }
        
    except Exception as e:
        logger.error(f"Error analyzing alert: {str(e)}")
        # Add an error note
        investigation_notes = []
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "alert_parser",
            "note": f"Error occurred during alert analysis: {str(e)}",
            "data": {"error": str(e)}
        }
        investigation_notes.append(error_note)
        
        # Add an error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Alert Analysis Error",
            "description": f"Error occurred during alert analysis: {str(e)}",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        # Return only the updated fields with the error
        return {
            "error": str(e),
            "investigation_notes": investigation_notes,  # Only return the new note
            "timeline": [error_event]  # Only return the new event
        } 