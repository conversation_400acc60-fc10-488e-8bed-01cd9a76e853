#!/usr/bin/env python3
"""
Enhanced graph-based cascading failure test scenario demonstrating architectural relationships.

This test showcases the improved approach that combines:
1. Alert-category-aware traversal patterns
2. Architectural business logic relationships
3. Metric-driven validation during traversal
4. Intelligent continue/stop decisions based on live data

The scenario simulates a connector-service failure in the MI architecture
and demonstrates how the system discovers both telemetry-visible and 
business logic dependencies.
"""

import asyncio
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from loguru import logger

# Import enhanced components
from ai_incident_manager.models.workflow_state import IncidentState
from ai_incident_manager.services.entity_relationship_service import EntityRelationshipService
from ai_incident_manager.workflow.nodes.graph_analysis import graph_analysis_node, graph_enrichment_node


async def create_enhanced_connector_service_scenario() -> IncidentState:
    """
    Create an enhanced test scenario that demonstrates architectural relationship discovery.
    
    This scenario represents a connector-service pod availability issue that should
    trigger investigation of:
    1. Kubernetes relationships (visible in telemetry)  
    2. Business logic dependencies (architectural config)
    3. Metric validation to confirm cascading failures
    
    Returns:
        Enhanced IncidentState with realistic failure scenario
    """
    now = datetime.now(timezone.utc)
    since_time = now - timedelta(minutes=15)
    
    # Realistic alert data based on the existing pattern
    test_alert_data = {
        "issueId": "enhanced-connector-pods-failure-001",
        "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/enhanced-connector-pods-failure-001",
        "title": "Percentage of Unavailable pods > 25% for cluster ivmdm-uk1-uks-aks-instance, deployment connector-service",
        "priority": "CRITICAL",
        "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtNzcyOTU1MzAxNDQ5ODQ0MDQ1MA"],  # connector-service deployment
        "impactedEntities": ["connector-service"],
        "totalIncidents": "1",
        "state": "ACTIVATED",
        "trigger": "STATE_CHANGE",
        "isCorrelated": "false",
        "createdAt": int(now.timestamp() * 1000),
        "updatedAt": int(now.timestamp() * 1000),
        "sources": ["newrelic"],
        "alertPolicyNames": ["SRE - MI Azure Alert - Infrastructure"],
        "alertConditionNames": ["Percentage of Unavailable pods > 25%"],
        "workflowName": "obv-ai-processing-enhanced-graph",
        "chartLink": "Not Available",
        "product": "MDM",
        "nr_region": "us",
        "landscape": "production",
        "region": "uk-south",
        "cluster_name": "ivmdm-uk1-uks-aks-instance"
    }
    
    # Enhanced entities with more realistic data
    entities = [
        {
            "entity_guid": "MTA5MzYyMHxJTkZSQXxOQXwtNzcyOTU1MzAxNDQ5ODQ0MDQ1MA",
            "entity_name": "connector-service",
            "entity_type": "KUBERNETES_DEPLOYMENT", 
            "cluster_name": "ivmdm-uk1-uks-aks-instance",
            "namespace": "mi-prod",
            "is_primary": True,
            "health_status": "critical",
            "availability": 0.72,  # 72% - below 75% threshold
            "cpu_usage": 0.68,
            "memory_usage": 0.71,
            "error_rate": 0.12,  # Above 0.05 threshold - should trigger architectural investigation
            "pod_count": 3,
            "unavailable_pods": 1,
            "restart_count": 8  # Above 5 threshold
        }
    ]
    
    # Create incident state with enhanced alert category
    incident_state: IncidentState = {
        "incident_id": test_alert_data["issueId"],
        "raw_alert": test_alert_data,
        "title": test_alert_data["title"],
        "description": f"Critical: connector-service availability at 72% with elevated error rate of 12%",
        "severity": test_alert_data["priority"],
        "start_time": now.isoformat(),
        "since_time": since_time.isoformat(),
        "until_time": now.isoformat(),
        "current_phase": "enhanced_graph_analysis_ready",
        "investigation_notes": [],
        "timeline": [],
        "primary_entity_guid": entities[0]["entity_guid"],
        "primary_entity_type": entities[0]["entity_type"],
        "entities": entities,
        "entity_relationships": [],
        "metrics": [],
        "logs": [],
        "system_checks": [],
        "root_cause": None,
        "remediation_actions": [],
        "alert_summary": "",
        
        # CRITICAL: Use the enhanced alert category
        "alert_category": "kubernetes_deployment_unavailable_pods",  # This will trigger architectural augmentation
        
        "alert_runbook": "",
        "alert_title": test_alert_data["title"],
        "condition_name": test_alert_data["alertConditionNames"][0],
        "policy_name": test_alert_data["alertPolicyNames"][0],
        "product": test_alert_data["product"],
        "nr_region": test_alert_data["nr_region"],
        "landscape": test_alert_data["landscape"],
        "region": test_alert_data["region"],
        "cluster_name": test_alert_data["cluster_name"],
        "run_id": f"enhanced-graph-test-{int(now.timestamp())}"
    }
    
    return incident_state


async def test_enhanced_graph_analysis():
    """
    Test the enhanced graph analysis that combines telemetry + architectural relationships.
    
    This test demonstrates:
    1. Alert-category-aware traversal patterns
    2. Architectural relationship discovery for business logic
    3. Metric validation to confirm cascading failures
    4. Enhanced analysis results with confidence scoring
    """
    logger.info("=" * 80)
    logger.info("ENHANCED GRAPH ANALYSIS TEST - ARCHITECTURAL RELATIONSHIPS")
    logger.info("=" * 80)
    
    # Create enhanced test scenario
    logger.info("Creating enhanced connector-service failure scenario...")
    incident_state = await create_enhanced_connector_service_scenario()
    
    # Log scenario details
    logger.info(f"Incident ID: {incident_state['incident_id']}")
    logger.info(f"Primary Entity: {incident_state['primary_entity_guid']}")
    logger.info(f"Alert Category: {incident_state['alert_category']}")
    logger.info(f"Entity Error Rate: {incident_state['entities'][0]['error_rate']:.2%} (triggers architectural investigation)")
    logger.info(f"Entity Availability: {incident_state['entities'][0]['availability']:.1%}")
    
    logger.info("\n" + "=" * 60)
    logger.info("RUNNING ENHANCED GRAPH ANALYSIS NODE")
    logger.info("=" * 60)
    
    try:
        # Run the enhanced graph analysis node
        logger.info("Executing graph_analysis_node with architectural augmentation...")
        updated_state = await graph_analysis_node(incident_state)
        
        # Check if graph analysis was successful
        if hasattr(updated_state, 'graph_analysis') and updated_state.graph_analysis:
            graph_analysis = updated_state.graph_analysis
            enhanced_analysis = graph_analysis.get('enhanced_analysis', {})
            
            logger.info("✓ Graph analysis completed successfully")
            logger.info(f"Analysis Summary: {enhanced_analysis.get('analysis_summary', 'No summary')}")
            logger.info(f"Risk Assessment: {enhanced_analysis.get('risk_assessment', 'No assessment')}")
            
            # Display critical insights
            critical_insights = enhanced_analysis.get('critical_insights', [])
            if critical_insights:
                logger.info("\nCritical Insights:")
                for i, insight in enumerate(critical_insights, 1):
                    logger.info(f"  {i}. {insight}")
            
            # Display cascading failure analysis
            cascading_analysis = enhanced_analysis.get('cascading_failure_analysis', {})
            if cascading_analysis:
                logger.info(f"\nCascading Failure Analysis:")
                logger.info(f"  - Primary Entity: {cascading_analysis.get('primary_entity', 'Unknown')}")
                logger.info(f"  - Affected Entities: {len(cascading_analysis.get('affected_entities', []))}")
                logger.info(f"  - Critical Dependencies: {len(cascading_analysis.get('critical_dependencies', []))}")
                logger.info(f"  - Blast Radius: {cascading_analysis.get('potential_blast_radius', 0)}")
                logger.info(f"  - Impact Score: {cascading_analysis.get('estimated_impact_score', 0.0):.3f}")
            
            # Show raw analysis for debugging
            raw_result = enhanced_analysis.get('raw_analysis_result')
            if raw_result:
                logger.info(f"\nInvestigation Summary:")
                summary = raw_result.get('investigation_summary', {})
                logger.info(f"  - Entities Investigated: {summary.get('total_entities_investigated', 0)}")
                logger.info(f"  - Cascading Failures: {summary.get('cascading_failures_detected', 0)}")
                logger.info(f"  - Architectural Relationships: {summary.get('architectural_relationships_found', 0)}")
                logger.info(f"  - Risk Level: {summary.get('risk_level', 'unknown')}")
        else:
            logger.error("✗ Graph analysis failed - no results found")
            
    except Exception as e:
        logger.error(f"✗ Graph analysis failed with error: {str(e)}")
        logger.exception(e)
        return
    
    logger.info("\n" + "=" * 60)
    logger.info("RUNNING ENHANCED GRAPH ENRICHMENT NODE")
    logger.info("=" * 60)
    
    try:
        # Run the graph enrichment node
        logger.info("Executing graph_enrichment_node...")
        final_state = await graph_enrichment_node(updated_state)
        
        # Check enrichment results
        if hasattr(final_state, 'enrichment') and final_state.enrichment:
            enrichment = final_state.enrichment.get('graph_based', {})
            
            logger.info("✓ Graph enrichment completed successfully")
            logger.info(f"Risk Level: {enrichment.get('risk_level', 'unknown')}")
            logger.info(f"Blast Radius: {enrichment.get('blast_radius', 0)}")
            logger.info(f"Impact Score: {enrichment.get('impact_score', 0.0):.3f}")
            logger.info(f"Confidence Score: {enrichment.get('confidence_score', 0.0):.3f}")
            
            # Display monitoring recommendations
            monitoring_recs = enrichment.get('monitoring_recommendations', [])
            if monitoring_recs:
                logger.info(f"\nMonitoring Recommendations ({len(monitoring_recs)}):")
                for i, rec in enumerate(monitoring_recs, 1):
                    logger.info(f"  {i}. [{rec.get('priority', 'unknown')}] {rec.get('description', 'No description')}")
                    if rec.get('type') == 'architectural_dependency_monitoring':
                        logger.info(f"     Dependencies: {', '.join(rec.get('dependencies', []))}")
            
            # Display escalation criteria
            escalation_criteria = enrichment.get('escalation_criteria', [])
            if escalation_criteria:
                logger.info(f"\nEscalation Criteria ({len(escalation_criteria)}):")
                for i, criteria in enumerate(escalation_criteria, 1):
                    logger.info(f"  {i}. {criteria.get('condition', 'unknown')}: {criteria.get('action', 'no action')}")
                    logger.info(f"     Reasoning: {criteria.get('reasoning', 'no reasoning')}")
                    logger.info(f"     Urgency: {criteria.get('urgency', 'unknown')}")
            
            # Display architectural relationships
            arch_relationships = enrichment.get('architectural_relationships', [])
            if arch_relationships:
                logger.info(f"\nArchitectural Relationships Found ({len(arch_relationships)}):")
                for i, rel in enumerate(arch_relationships, 1):
                    logger.info(f"  {i}. {rel}")
        else:
            logger.error("✗ Graph enrichment failed - no enrichment data found")
            
    except Exception as e:
        logger.error(f"✗ Graph enrichment failed with error: {str(e)}")
        logger.exception(e)
    
    logger.info("\n" + "=" * 80)
    logger.info("ENHANCED GRAPH ANALYSIS TEST COMPLETED")
    logger.info("=" * 80)
    
    # Summary of what this test demonstrates
    logger.info("\n🎯 KEY FEATURES DEMONSTRATED:")
    logger.info("✅ Alert-category-aware traversal using kubernetes_deployment_unavailable_pods patterns")
    logger.info("✅ Architectural relationship discovery for business logic dependencies")
    logger.info("✅ Metric-driven validation during traversal (error_rate > 5% triggers investigation)")
    logger.info("✅ Enhanced analysis with confidence scoring and risk assessment")
    logger.info("✅ Intelligent monitoring recommendations based on architectural context")
    logger.info("✅ Escalation criteria with confidence and reasoning")
    
    return final_state


async def test_direct_entity_service():
    """
    Test the enhanced entity relationship service directly to show detailed traversal results.
    """
    logger.info("\n" + "=" * 80)
    logger.info("DIRECT ENTITY RELATIONSHIP SERVICE TEST")
    logger.info("=" * 80)
    
    try:
        # Create service instance
        entity_service = EntityRelationshipService()
        
        # Note: In a real test, we would set up a mock query_client
        # For this demonstration, we'll show what would happen
        logger.info("📝 Note: This test shows the enhanced traversal logic")
        logger.info("    In production, this would connect to New Relic for live metrics")
        
        # Test architectural relationship discovery
        logger.info("\n🔍 Testing architectural relationship discovery...")
        logger.info("Entity: 'connector-service' with alert category: 'kubernetes_deployment_unavailable_pods'")
        logger.info("Expected architectural relationships:")
        logger.info("  - identity-service (DEPENDS_ON, critical)")
        logger.info("  - auth-service (DEPENDS_ON, critical)")  
        logger.info("  - kafka-cluster (PUBLISHES_TO, medium)")
        logger.info("  - redis-cluster (CACHES_TO, medium)")
        logger.info("  - payment-gateway (INTEGRATES_WITH, high)")
        
        logger.info("\n📊 Metric validation would check:")
        logger.info("  - error_rate > 0.05 → Continue traversal")
        logger.info("  - response_time > 500ms → Continue traversal")
        logger.info("  - availability < 0.95 → Continue traversal")
        
        logger.info("\n⚡ Enhanced features:")
        logger.info("  ✅ Combines telemetry + architectural config")
        logger.info("  ✅ Validates each relationship with live metrics")
        logger.info("  ✅ Makes intelligent continue/stop decisions")
        logger.info("  ✅ Provides confidence scoring")
        logger.info("  ✅ Generates specific recommendations")
        
    except Exception as e:
        logger.error(f"Direct service test error: {str(e)}")


if __name__ == "__main__":
    asyncio.run(test_enhanced_graph_analysis())
    asyncio.run(test_direct_entity_service())