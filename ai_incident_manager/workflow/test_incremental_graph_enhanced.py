#!/usr/bin/env python3
"""
Enhanced incremental test for the incident analysis workflow with graph-based
cascading failure detection for the MI architecture.

This combines the existing refactored workflow with the new graph analysis
capabilities to demonstrate end-to-end cascading failure detection.
"""

import asyncio
from datetime import datetime, timezone
from langgraph.graph import StateGraph, END, START
from loguru import logger

from ai_incident_manager.models.workflow_state import IncidentState

# Import workflow nodes from modularized files
try:
    # Try relative imports first (when run as module)
    from .nodes.alert_analysis import analyze_alert
    from .nodes.entity_analysis import analyze_entity, analyze_entity_relationships
    from .nodes.graph_analysis import graph_analysis_node, graph_enrichment_node
    from .nodes.runbook_execution import execute_runbooks
    from .nodes.analysis import analyze_root_cause, generate_topology
    from .nodes.reporting import create_ticket, report_results
    from .nodes.database_context import retrieve_database_context
    from .nodes.workflow_control import should_continue, finish
    from .config import get_workflow_config
    from .utils.serialization import ensure_serializable
    from .test_graph_scenario import create_connector_service_failure_scenario
except ImportError:
    # Fall back to absolute imports (when run as standalone script)
    import sys
    import os
    # Add the project root to the Python path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
    
    from ai_incident_manager.workflow.nodes.alert_analysis import analyze_alert
    from ai_incident_manager.workflow.nodes.entity_analysis import analyze_entity, analyze_entity_relationships
    from ai_incident_manager.workflow.nodes.graph_analysis import graph_analysis_node, graph_enrichment_node
    from ai_incident_manager.workflow.nodes.runbook_execution import execute_runbooks
    from ai_incident_manager.workflow.nodes.analysis import analyze_root_cause, generate_topology
    from ai_incident_manager.workflow.nodes.reporting import create_ticket, report_results
    from ai_incident_manager.workflow.nodes.database_context import retrieve_database_context
    from ai_incident_manager.workflow.nodes.workflow_control import should_continue, finish
    from ai_incident_manager.workflow.config import get_workflow_config
    from ai_incident_manager.workflow.utils.serialization import ensure_serializable
    from ai_incident_manager.workflow.test_graph_scenario import create_connector_service_failure_scenario


def create_enhanced_graph_workflow() -> StateGraph:
    """Create an enhanced workflow with graph analysis capabilities."""
    
    # Define the workflow nodes including graph analysis
    nodes = {
        "analyze_alert": analyze_alert,
        "analyze_entity": analyze_entity,
        "analyze_entity_relationships": analyze_entity_relationships,
        "graph_analysis": graph_analysis_node,
        "graph_enrichment": graph_enrichment_node,
        "execute_runbooks": execute_runbooks,
        "retrieve_database_context": retrieve_database_context,
        "analyze_root_cause": analyze_root_cause,
        "should_continue": should_continue,
        "generate_topology": generate_topology,
        "report_results": report_results,
        "create_ticket": create_ticket,
        "finish": finish
    }
    
    # Create the workflow graph
    workflow = StateGraph(IncidentState)
    
    # Add all nodes to the graph
    for name, node in nodes.items():
        workflow.add_node(name, node)
    
    # Define the enhanced flow with graph analysis
    workflow.add_edge(START, "analyze_alert")
    workflow.add_edge("analyze_alert", "analyze_entity")
    workflow.add_edge("analyze_entity", "analyze_entity_relationships")
    
    # NEW: Add graph analysis after entity relationships
    workflow.add_edge("analyze_entity_relationships", "graph_analysis")
    workflow.add_edge("graph_analysis", "graph_enrichment")
    
    # Continue with existing flow
    workflow.add_edge("graph_enrichment", "execute_runbooks")
    workflow.add_edge("execute_runbooks", "retrieve_database_context")
    workflow.add_edge("retrieve_database_context", "analyze_root_cause")
    workflow.add_edge("analyze_root_cause", "should_continue")
    
    # Optional branch based on should_continue decision
    workflow.add_conditional_edges(
        "should_continue",
        lambda x: "analyze_topology" if x.get("should_continue", False) else "report_results",
        {
            "analyze_topology": "generate_topology",
            "report_results": "report_results"
        }
    )
    
    workflow.add_edge("generate_topology", "report_results")
    workflow.add_edge("report_results", "create_ticket")
    workflow.add_edge("create_ticket", "finish")
    workflow.add_edge("finish", END)
    
    # Compile the workflow
    workflow.compile()
    
    return workflow


async def test_enhanced_workflow():
    """Test the enhanced workflow with graph analysis capabilities."""
    logger.info("=" * 80)
    logger.info("ENHANCED WORKFLOW TEST - WITH GRAPH ANALYSIS")
    logger.info("=" * 80)
    
    # Create and compile the enhanced workflow
    workflow = create_enhanced_graph_workflow()
    compiled_workflow = workflow.compile()
    
    # Set up MongoDB connection (optional)
    mongodb_available = False
    try:
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Test connection by checking if the incidents collection is initialized
        if mongodb_service._incidents_collection is not None:
            mongodb_available = True
            logger.info("MongoDB service initialized for testing")
        else:
            logger.warning("MongoDB service was created but collections are not initialized. Check connection.")
            
    except Exception as e:
        mongodb_error = str(e)
        logger.error(f"Could not initialize MongoDB service: {mongodb_error}")
        
        # Check if this is a connection string formatting error
        if "Username and password must be escaped" in mongodb_error:
            logger.error("The MongoDB connection string contains special characters in username/password that need URL encoding.")
            logger.error("Solution: Update your .env file with properly escaped username and password.")
            logger.error("Example: If your password is 'p@ssw0rd', it should be encoded as 'p%40ssw0rd'")
        
        logger.warning("The workflow will run without MongoDB persistence.")
    
    if not mongodb_available:
        logger.warning("MongoDB storage is disabled. Tool results, metrics, logs, and events will not be persisted.")
        logger.warning("To enable MongoDB storage, ensure the MONGODB_CONNECTION_STRING environment variable is set correctly.")
    
    # Create the enhanced test scenario with graph data
    logger.info("Creating enhanced connector-service failure scenario with graph analysis...")
    initial_state = await create_connector_service_failure_scenario()
    
    # Log enhanced scenario details
    logger.info(f"Incident ID: {initial_state['incident_id']}")
    logger.info(f"Primary Entity: {initial_state['primary_entity_guid']}")
    logger.info(f"Alert Category: {initial_state['alert_category']}")
    logger.info(f"Cluster: {initial_state['cluster_name']}")
    
    # Log graph context
    sample_data = initial_state.get("graph_analysis", {}).get("sample_data", {})
    if sample_data:
        logger.info(f"Graph Nodes: {len(sample_data.get('nodes', []))}")
        logger.info(f"Graph Edges: {len(sample_data.get('edges', []))}")
        logger.info(f"Architecture: {sample_data.get('metadata', {}).get('architecture_name', 'Unknown')}")
    
    # Execute the enhanced workflow
    logger.info(f"\nStarting enhanced workflow for incident {initial_state['incident_id']}")
    logger.info(f"Initial phase: {initial_state['current_phase']}")
    
    try:
        # Track workflow progress
        node_execution_count = 0
        graph_analysis_completed = False
        
        # Use astream_events for real-time progress tracking
        async for event in compiled_workflow.astream_events(initial_state):
            event_type = event.get("type")
            
            # Track node executions
            if event_type == "node" and event.get("name"):
                node_name = event["name"]
                if event.get("action") == "start":
                    node_execution_count += 1
                    logger.info(f"[{node_execution_count}] Starting: {node_name}")
                    
                    # Special logging for graph analysis nodes
                    if node_name in ["graph_analysis", "graph_enrichment"]:
                        logger.info(f"    → Graph analysis step: {node_name}")
                
                elif event.get("action") == "end":
                    logger.info(f"[{node_execution_count}] Completed: {node_name}")
                    
                    # Check if graph analysis was completed
                    if node_name == "graph_enrichment":
                        graph_analysis_completed = True
                        logger.info("    ✓ Graph-based cascading failure analysis completed")
            
            # Process final result
            if event_type == "end":
                result = event.get("data", {})
                
                # Print comprehensive results
                logger.info("\n" + "=" * 80)
                logger.info("ENHANCED WORKFLOW EXECUTION RESULTS")
                logger.info("=" * 80)
                
                if hasattr(result, 'model_dump'):
                    result_dict = result.model_dump()
                else:
                    result_dict = result
                
                if "error" in result_dict:
                    logger.error(f"Workflow error: {result_dict['error']}")
                else:
                    # Standard workflow results
                    logger.info(f"Final phase: {result_dict.get('current_phase', 'unknown')}")
                    logger.info(f"Alert Category: {result_dict.get('alert_category', 'Unknown')}")
                    logger.info(f"Condition Name: {result_dict.get('condition_name', 'Unknown')}")
                    logger.info(f"Policy Name: {result_dict.get('policy_name', 'Unknown')}")
                    logger.info(f"Cluster Name: {result_dict.get('cluster_name', 'Unknown')}")
                    
                    # Print entities
                    entities = result_dict.get('entities', [])
                    if entities:
                        logger.info(f"\nEntities ({len(entities)}):")
                        for entity in entities:
                            entity_name = entity.get('entity_name', 'Unknown')
                            entity_type = entity.get('entity_type', 'Unknown')
                            health_status = entity.get('health_status', 'Unknown')
                            logger.info(f"  - {entity_name} ({entity_type}) - Health: {health_status}")
                    
                    # Print graph analysis results if available
                    graph_analysis = result_dict.get('graph_analysis')
                    if graph_analysis and graph_analysis_completed:
                        logger.info(f"\nGraph Analysis Results:")
                        
                        agent_analysis = graph_analysis.get('agent_analysis', {})
                        if agent_analysis:
                            # Cascading failure analysis
                            cascading_analysis = agent_analysis.get('cascading_failure_analysis', {})
                            if cascading_analysis:
                                blast_radius = cascading_analysis.get('potential_blast_radius', 0)
                                impact_score = cascading_analysis.get('estimated_impact_score', 0.0)
                                logger.info(f"  - Blast Radius: {blast_radius} entities")
                                logger.info(f"  - Impact Score: {impact_score:.2f}")
                                
                                affected_entities = cascading_analysis.get('affected_entities', [])
                                if affected_entities:
                                    logger.info(f"  - Affected Entities: {len(affected_entities)}")
                                    for entity in affected_entities[:3]:  # Show top 3
                                        logger.info(f"    • {entity}")
                            
                            # Risk assessment
                            risk_assessment = agent_analysis.get('risk_assessment', '')
                            if risk_assessment:
                                logger.info(f"  - Risk Assessment: {risk_assessment[:100]}...")
                            
                            # Recommended actions
                            recommended_actions = agent_analysis.get('recommended_actions', [])
                            if recommended_actions:
                                logger.info(f"  - Recommended Actions ({len(recommended_actions)}):")
                                for i, action in enumerate(recommended_actions[:3], 1):
                                    logger.info(f"    {i}. {action}")
                    
                    # Print graph enrichment results if available
                    enrichment = result_dict.get('enrichment', {})
                    graph_enrichment = enrichment.get('graph_based')
                    if graph_enrichment:
                        logger.info(f"\nGraph Enrichment Results:")
                        logger.info(f"  - Risk Level: {graph_enrichment.get('risk_level', 'Unknown')}")
                        logger.info(f"  - Critical Entities: {len(graph_enrichment.get('critical_entities', []))}")
                        
                        monitoring_recs = graph_enrichment.get('monitoring_recommendations', [])
                        if monitoring_recs:
                            logger.info(f"  - Monitoring Recommendations: {len(monitoring_recs)}")
                            for rec in monitoring_recs[:2]:
                                logger.info(f"    • [{rec.get('priority', 'Unknown')}] {rec.get('description', 'No description')}")
                    
                    # Print investigation notes
                    notes = result_dict.get('investigation_notes', [])
                    if notes:
                        logger.info(f"\nInvestigation Notes ({len(notes)}):")
                        for note in notes[-5:]:  # Show last 5 notes
                            agent = note.get('agent', 'Unknown')
                            note_text = note.get('note', 'No content')
                            logger.info(f"  - [{agent}] {note_text}")
                    
                    # Summary
                    logger.info(f"\nWorkflow Summary:")
                    logger.info(f"  - Total Nodes Executed: {node_execution_count}")
                    logger.info(f"  - Graph Analysis: {'✓ Completed' if graph_analysis_completed else '✗ Not executed'}")
                    logger.info(f"  - Entities Analyzed: {len(entities)}")
                    logger.info(f"  - Investigation Notes: {len(notes)}")
    
    except Exception as e:
        logger.error(f"Error executing enhanced workflow: {str(e)}")
        raise

    logger.info("\n" + "=" * 80)
    logger.info("ENHANCED WORKFLOW TEST COMPLETED")
    logger.info("=" * 80)


if __name__ == "__main__":
    asyncio.run(test_enhanced_workflow())