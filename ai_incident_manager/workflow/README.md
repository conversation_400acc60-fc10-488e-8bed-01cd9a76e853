# Workflow Modularization

This directory contains the modularized version of the incident management workflow. The original large file has been broken down into smaller, more manageable modules for better maintainability and organization.

## Directory Structure

```
ai_incident_manager/workflow/
├── config.py                          # Configuration and client initialization
├── test_incremental_graph.py          # Original large file (kept for reference)
├── test_incremental_graph_refactored.py # Refactored main test file
├── README.md                          # This file
├── nodes/                             # Workflow node functions
│   ├── __init__.py
│   ├── alert_analysis.py              # Alert parsing and analysis
│   ├── entity_analysis.py             # Entity analysis and relationships
│   └── workflow_control.py            # Flow control and completion
└── utils/                             # Utility functions
    ├── __init__.py
    ├── decorators.py                   # Logging decorators
    ├── mongodb_utils.py                # MongoDB operations
    └── serialization.py               # JSON serialization utilities
```

## Modules Overview

### 1. Configuration (`config.py`)
Centralizes all client initialization and configuration:
- OpenAI client setup (GPT-4o and O1 models)
- New Relic client initialization
- Service initialization (entity types, runbooks, metrics collector)
- Global configuration management

### 2. Workflow Nodes (`nodes/`)
Individual workflow step implementations:

#### `alert_analysis.py`
- `analyze_alert()`: Parses alerts and extracts relevant information

#### `entity_analysis.py` 
- `analyze_entity()`: Enriches entities with detailed information
- `analyze_entity_relationships()`: Maps entity relationships

#### `workflow_control.py`
- `should_continue()`: Controls workflow flow between steps
- `finish()`: Handles workflow completion and final state storage

### 3. Utilities (`utils/`)
Common functionality used across workflow nodes:

#### `decorators.py`
- `log_node_execution()`: Decorator for structured logging of node execution

#### `serialization.py`
- `ensure_serializable()`: Handles JSON serialization of complex objects
- `generate_reference_id()`: Creates unique reference IDs

#### `mongodb_utils.py`
- `process_collected_information()`: Stores workflow data in MongoDB
- `retrieve_tool_result()`: Retrieves stored tool results

## Usage

### Running the Modularized Workflow

```python
from ai_incident_manager.workflow.test_incremental_graph_refactored import test_workflow
import asyncio

# Run the refactored workflow
asyncio.run(test_workflow())
```

### Extending the Workflow

1. **Adding new nodes**: Create new functions in appropriate modules under `nodes/`
2. **Adding utilities**: Place shared functionality in `utils/`
3. **Configuration changes**: Update `config.py` for new services or clients

### Migration Status

✅ **Completed Modules:**
- Configuration and client setup
- Alert analysis 
- Entity analysis and relationships
- Workflow control and completion
- Core utilities (logging, serialization, MongoDB)

🚧 **TODO - Remaining modules to extract:**
- Runbook execution (`execute_runbooks`)
- Root cause analysis (`analyze_root_cause`) 
- Topology generation (`generate_topology`)
- Ticket creation and reporting (`create_ticket`, `report_results`)
- Database context retrieval (`retrieve_database_context`)

## Benefits of Modularization

1. **Maintainability**: Smaller, focused files are easier to understand and modify
2. **Testability**: Individual components can be tested in isolation
3. **Reusability**: Common utilities can be shared across different workflow implementations
4. **Separation of Concerns**: Each module has a clear, single responsibility
5. **Reduced Complexity**: The original 3000+ line file is now broken into manageable chunks

## Migration Guide

To continue the modularization:

1. Extract remaining workflow nodes from `test_incremental_graph.py`
2. Create new module files under `nodes/` for each functional area
3. Update imports in `test_incremental_graph_refactored.py`
4. Test each extracted module individually
5. Remove original functions from `test_incremental_graph.py` once fully migrated

## Configuration Management

The `WorkflowConfig` class provides centralized access to all workflow dependencies:

```python
from ai_incident_manager.workflow.config import get_workflow_config

config = get_workflow_config()
# Access clients and services
openai_client = config.openai_client
nr_query_client = config.nr_query_client
entity_analyzer = config.entity_analyzer
```

This ensures consistent configuration across all workflow components and makes dependency injection straightforward. 