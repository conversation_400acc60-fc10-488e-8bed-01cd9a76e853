"""
Configuration and client initialization for the incident management workflow.
"""

import os
import dotenv
from openai import AsyncAzureOpenAI
from lib.new_relic.analyzer import <PERSON><PERSON>tyAnalyzer
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.logs import NewRelicLogsClient
from ai_incident_manager.services.entity_type_service import get_entity_type_service
from ai_incident_manager.services.runbook_service import get_runbook_service
from ai_incident_manager.services.metrics_collector import MetricsCollector
from ai_incident_manager.services.entity_relationship_service import get_entity_relationship_service

# Load environment variables
dotenv.load_dotenv()


def initialize_openai_clients():
    """Initialize OpenAI clients for different models."""
    azure_openai_enabled = "AZURE_OPENAI_ENDPOINT" in os.environ and "AZURE_OPENAI_API_KEY" in os.environ

    if not azure_openai_enabled:
        raise ValueError("Azure OpenAI is required for this script")

    # Standard GPT-4o client
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    
    # O1 model client for advanced reasoning
    openai_o1_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment='o1',
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    
    return openai_client, openai_o1_client


def initialize_newrelic_clients():
    """Initialize New Relic API clients."""
    api_key = os.environ.get("NEWRELIC_API_KEY")
    account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        raise ValueError("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set in environment variables")

    # Initialize the New Relic clients
    nr_graphql_client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
    nr_query_client = NewRelicQueryClient(nr_graphql_client)
    nr_logs_client = NewRelicLogsClient(nr_graphql_client)
    entity_analyzer = EntityAnalyzer(nr_graphql_client, debug=True)
    
    return nr_graphql_client, nr_query_client, nr_logs_client, entity_analyzer


def initialize_services():
    """Initialize various workflow services."""
    metrics_collector = MetricsCollector()
    entity_relationship_service = get_entity_relationship_service()
    entity_type_service = get_entity_type_service()
    runbook_service = get_runbook_service()
    
    return metrics_collector, entity_relationship_service, entity_type_service, runbook_service


class WorkflowConfig:
    """Central configuration class for workflow components."""
    
    def __init__(self):
        # Initialize OpenAI clients
        self.openai_client, self.openai_o1_client = initialize_openai_clients()
        
        # Initialize New Relic clients  
        (self.nr_graphql_client, self.nr_query_client, 
         self.nr_logs_client, self.entity_analyzer) = initialize_newrelic_clients()
        
        # Initialize services
        (self.metrics_collector, self.entity_relationship_service,
         self.entity_type_service, self.runbook_service) = initialize_services()


# Global configuration instance
_config = None


def get_workflow_config() -> WorkflowConfig:
    """Get the global workflow configuration instance."""
    global _config
    if _config is None:
        _config = WorkflowConfig()
    return _config 


def get_query_client():
    """Get the New Relic query client."""
    config = get_workflow_config()
    return config.nr_query_client


def get_openai_client():
    """Get the OpenAI client."""
    config = get_workflow_config()
    return config.openai_client