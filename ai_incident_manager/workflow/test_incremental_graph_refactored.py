#!/usr/bin/env python3
"""
Refactored incremental test for the incident analysis workflow using LangGraph.

This module provides a simplified version of the incident analysis workflow
to test components incrementally, starting with alert parsing and entity analysis.

The workflow has been modularized into separate files for better maintainability.
"""

import asyncio
from datetime import datetime, timezone
from langgraph.graph import StateGraph, END, START
from loguru import logger

from ai_incident_manager.models.workflow_state import IncidentState

# Import workflow nodes from modularized files
# Handle both relative and absolute imports for flexibility
try:
    # Try relative imports first (when run as module)
    from .nodes.alert_analysis import analyze_alert
    from .nodes.entity_analysis import analyze_entity, analyze_entity_relationships
    from .nodes.runbook_execution import execute_runbooks
    from .nodes.analysis import analyze_root_cause, generate_topology
    from .nodes.reporting import create_ticket, report_results
    from .nodes.database_context import retrieve_database_context
    from .nodes.workflow_control import should_continue, finish
    from .config import get_workflow_config
    from .utils.serialization import ensure_serializable
except ImportError:
    # Fall back to absolute imports (when run as standalone script)
    import sys
    import os
    # Add the project root to the Python path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
    
    from ai_incident_manager.workflow.nodes.alert_analysis import analyze_alert
    from ai_incident_manager.workflow.nodes.entity_analysis import analyze_entity, analyze_entity_relationships
    from ai_incident_manager.workflow.nodes.runbook_execution import execute_runbooks
    from ai_incident_manager.workflow.nodes.analysis import analyze_root_cause, generate_topology
    from ai_incident_manager.workflow.nodes.reporting import create_ticket, report_results
    from ai_incident_manager.workflow.nodes.database_context import retrieve_database_context
    from ai_incident_manager.workflow.nodes.workflow_control import should_continue, finish
    from ai_incident_manager.workflow.config import get_workflow_config
    from ai_incident_manager.workflow.utils.serialization import ensure_serializable


def create_test_workflow() -> StateGraph:
    """Create a simplified test workflow for incremental testing."""
    
    # Define the workflow nodes
    nodes = {
        "analyze_alert": analyze_alert,
        "analyze_entity": analyze_entity,
        "analyze_entity_relationships": analyze_entity_relationships,
        "execute_runbooks": execute_runbooks,
        "retrieve_database_context": retrieve_database_context,
        "analyze_root_cause": analyze_root_cause,
        "should_continue": should_continue,
        "generate_topology": generate_topology,
        "report_results": report_results,
        "create_ticket": create_ticket,
        "finish": finish
    }
    
    # Create the workflow graph
    workflow = StateGraph(IncidentState)
    
    # Add all nodes to the graph
    for name, node in nodes.items():
        workflow.add_node(name, node)
    
    # Define the edges
    # Simplified flow for incremental testing
    workflow.add_edge(START, "analyze_alert")
    workflow.add_edge("analyze_alert", "analyze_entity")
    workflow.add_edge("analyze_entity", "analyze_entity_relationships")
    workflow.add_edge("analyze_entity_relationships", "execute_runbooks")
    workflow.add_edge("execute_runbooks", "retrieve_database_context")
    workflow.add_edge("retrieve_database_context", "analyze_root_cause")
    workflow.add_edge("analyze_root_cause", "should_continue")
    
    # Optional branch based on should_continue decision
    workflow.add_conditional_edges(
        "should_continue",
        lambda x: "analyze_topology" if x.get("should_continue", False) else "report_results",
        {
            "analyze_topology": "generate_topology",
            "report_results": "report_results"
        }
    )
    
    workflow.add_edge("generate_topology", "report_results")
    workflow.add_edge("report_results", "create_ticket")
    workflow.add_edge("create_ticket", "finish")
    workflow.add_edge("finish", END)
    
    # Compile the workflow
    workflow.compile()
    
    return workflow


async def test_workflow():
    """Test the workflow execution."""
    # Create and compile the workflow
    workflow = create_test_workflow()
    compiled_workflow = workflow.compile()
    
    # Set up MongoDB connection
    mongodb_available = False
    try:
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Test connection by checking if the incidents collection is initialized
        if mongodb_service._incidents_collection is not None:
            mongodb_available = True
            logger.info("MongoDB service initialized for testing")
        else:
            logger.warning("MongoDB service was created but collections are not initialized. Check connection.")
            
    except Exception as e:
        mongodb_error = str(e)
        logger.error(f"Could not initialize MongoDB service: {mongodb_error}")
        
        # Check if this is a connection string formatting error
        if "Username and password must be escaped" in mongodb_error:
            logger.error("The MongoDB connection string contains special characters in username/password that need URL encoding.")
            logger.error("Solution: Update your .env file with properly escaped username and password.")
            logger.error("Example: If your password is 'p@ssw0rd', it should be encoded as 'p%40ssw0rd'")
        
        logger.warning("The workflow will run without MongoDB persistence.")
    
    if not mongodb_available:
        logger.warning("MongoDB storage is disabled. Tool results, metrics, logs, and events will not be persisted.")
        logger.warning("To enable MongoDB storage, ensure the MONGODB_CONNECTION_STRING environment variable is set correctly.")
    
    # Test data for a Kubernetes deployment unavailable pods alert
    test_data = {
        "issueId": "9a0625bd-fc0e-48cd-b1b5-7065e2127bee",
        "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/9a0625bd-fc0e-48cd-b1b5-7065e2127bee?notifier=WEBHOOK",
        "title": "Percentage of Unavailable pods > 25% for cluster ivmdm-uk1-uks-aks-instance, deployment connector-service",
        "priority": "CRITICAL",
        "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtNzcyOTU1MzAxNDQ5ODQ0MDQ1MA"],
        "impactedEntities": ["connector-service"],
        "totalIncidents": "1",
        "state": "ACTIVATED",
        "trigger": "STATE_CHANGE",
        "isCorrelated": "false",
        "createdAt": *************,
        "updatedAt": *************,
        "sources": ["newrelic"],
        "alertPolicyNames": ["SRE - MI Azure Alert - Infrastructure"],
        "alertConditionNames": ["Percentage of Unavailable pods > 25%"],
        "workflowName": "obv-ai-processing",
        "chartLink": "Not Available",
        "product": "MDM",
        "nr_region": "us"
    }
    
    # Initialize proper state structure for the workflow
    incident_id = test_data.get("issueId")
    initial_state: IncidentState = {
        "incident_id": incident_id,
        "raw_alert": test_data,
        "title": test_data.get("title", "Unknown"),
        "description": f"Alert from {test_data.get('alertPolicyNames', ['Unknown'])[0]}", 
        "severity": test_data.get("priority", "UNKNOWN"),
        "start_time": datetime.now(timezone.utc).isoformat(),
        "current_phase": "initialize",
        "investigation_notes": [],
        "timeline": [],  # Initialize empty timeline events list
        "primary_entity_guid": test_data.get("EntityId", [""])[0],
        "primary_entity_type": None,
        "entity_details": [],
        "related_entity_details": [],
        "metrics": [],
        "logs": [],
        "system_checks": [],
        "root_cause": None,
        "remediation_actions": [],
        "alert_summary": "",
        "alert_category": "", # Will be filled by alert_parser
        "alert_runbook": "",  # Will be filled by alert_parser
        "entities": [],       # Will be filled by alert_parser
        "alert_title": test_data.get("title", "Unknown"),  # Required field
        "condition_name": test_data.get("alertConditionNames", ["Unknown"])[0],  # Required field
        "run_id": f"run-{datetime.now(timezone.utc).timestamp():.0f}"  # Add unique run_id for tracking
    }
    
    # Execute the workflow with the test data
    logger.info(f"Starting workflow for incident {incident_id}")
    logger.info(f"Initial phase: {initial_state['current_phase']}")
    
    try:
        # Use astream_events instead of ainvoke for newer LangGraph versions
        async for event in compiled_workflow.astream_events(initial_state):
            # You can process events here if needed
            if event.get("type") == "end":
                result = event.get("data", {})
                result_dict = result.model_dump()
                
                # Print key results
                logger.info("-" * 60)
                logger.info("WORKFLOW EXECUTION RESULTS")
                logger.info("-" * 60)
                
                if "error" in result:
                    logger.error(f"Workflow error: {result['error']}")
                else:
                    logger.info(f"Final phase: {result.get('current_phase', 'unknown')}")
                    logger.info(f"Alert Category: {result.get('alert_category', 'Unknown')}")
                    logger.info(f"Condition Name: {result.get('condition_name', 'Unknown')}")
                    logger.info(f"Policy Name: {result.get('policy_name', 'Unknown')}")
                    logger.info(f"Cluster Name: {result.get('cluster_name', 'Unknown')}")
                    
                    # Print entities
                    entities = result.get('entities', [])
                    if entities:
                        logger.info(f"\nEntities ({len(entities)}):")
                        for entity in entities:
                            logger.info(f"  - {entity.get('entity_guid', 'Unknown')}")
                    
                    # Print investigation notes
                    notes = result.get('investigation_notes', [])
                    if notes:
                        logger.info(f"\nInvestigation Notes ({len(notes)}):")
                        for note in notes:
                            logger.info(f"  - [{note.get('agent', 'Unknown')}] {note.get('note', 'No content')}")
    
    except Exception as e:
        logger.error(f"Error executing workflow: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(test_workflow()) 