#!/usr/bin/env python3
"""
Incremental test for the incident analysis workflow using LangGraph.

This module provides a simplified version of the incident analysis workflow
to test components incrementally, starting with alert parsing and entity analysis.
"""

import os
import json
import asyncio
from typing import Dict, Any, List, Optional, Tuple, Callable, Annotated
from datetime import datetime, timezone, timedelta
from enum import Enum
import functools
import traceback
import uuid

from langgraph.graph import StateGraph, END, START
from devtools import pprint
import dotenv
from langgraph.graph.message import AnyMessage

from ai_incident_manager.services.ado_service import get_ado_service
from ai_incident_manager.services.teams_service import get_teams_service

# Load environment variables
dotenv.load_dotenv()

# Import Loguru logger instead of standard logging
from loguru import logger

# Import our custom formatting functions
from lib.new_relic.logger import format_node_start, format_node_end, format_node_error

# Import the reporting agent
from ai_incident_manager.agents.reporting_agent import ReportingAgentDeps, get_reporting_agent, ReportingResult

# Define a decorator to log node entry and exit
def log_node_execution(node_name):
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            state = args[0] if args else kwargs.get('state')
            
            # Handle different state types (dict or Pydantic model)
            if state:
                if hasattr(state, 'incident_id'):
                    incident_id = state.incident_id
                elif isinstance(state, dict) and 'incident_id' in state:
                    incident_id = state['incident_id']
                else:
                    incident_id = 'unknown'
            else:
                incident_id = 'unknown'
            
            # Create a contextual logger with node and incident information
            node_logger = logger.bind(node=node_name, incident_id=incident_id)
            
            # Log node start with the new formatting
            node_logger.info(format_node_start(node_name, incident_id))
            
            start_time = datetime.now(timezone.utc)
            try:
                result = await func(*args, **kwargs)
                
                # Calculate duration
                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()
                
                # Log node end with the new formatting
                node_logger.success(format_node_end(node_name, incident_id, duration))
                return result
            except Exception as e:
                # Calculate duration even on error
                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()
                
                # Log error with special formatting
                error_msg = str(e)
                node_logger.error(format_node_error(node_name, incident_id, error_msg, duration))
                
                # Log the traceback for debugging
                node_logger.error(f"Traceback:\n{''.join(traceback.format_tb(e.__traceback__))}")
                raise
                
        return wrapper
    return decorator

# Utility function to handle MongoDB ObjectId serialization
def ensure_serializable(obj):
    """
    Ensure all values in a nested structure are JSON serializable.
    Converts ObjectId, datetime objects and other non-serializable types to strings.
    
    Args:
        obj: Any Python object that might contain non-serializable values
        
    Returns:
        A JSON-serializable version of the object
    """
    if obj is None:
        return None
        
    # Handle MongoDB ObjectId
    if hasattr(obj, "__str__") and str(type(obj)).find("bson.objectid.ObjectId") > -1:
        return str(obj)
        
    # Handle datetime objects
    if isinstance(obj, datetime):
        return obj.isoformat()
        
    # Handle lists
    if isinstance(obj, list):
        return [ensure_serializable(item) for item in obj]
        
    # Handle dictionaries
    if isinstance(obj, dict):
        return {k: ensure_serializable(v) for k, v in obj.items()}
        
    # Handle Pydantic models or anything with model_dump() method
    if hasattr(obj, "model_dump"):
        return ensure_serializable(obj.model_dump())
        
    # Handle older Pydantic models
    if hasattr(obj, "dict"):
        return ensure_serializable(obj.dict())
    
    # Return basic types as is
    return obj

# Import necessary components for the graph
from ai_incident_manager.models.workflow_state import IncidentState, InvestigationNote, TimelineEvent, EventTag
from ai_incident_manager.agents.alert_parser_agent import (
    alert_parser_agent, AlertParserDeps
)
from ai_incident_manager.agents.entity_analyzer_agent import (
    entity_analyzer_agent, EntityAnalyzerDeps
)
from ai_incident_manager.agents.runbook_agent import (
    runbook_agent, RunbookAgentDeps, get_alert_category_config
)
from ai_incident_manager.agents.entity_relationship_agent import (
    entity_relationship_agent, EntityRelationshipDeps
)
from ai_incident_manager.agents.database_agent import (
    database_agent, DatabaseAgentDeps, get_database_client
)
from ai_incident_manager.services.entity_type_service import get_entity_type_service
from ai_incident_manager.services.runbook_service import get_runbook_service
from ai_incident_manager.services.metrics_collector import MetricsCollector
from ai_incident_manager.services.entity_relationship_service import get_entity_relationship_service
from lib.new_relic.analyzer import EntityAnalyzer
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.logs import NewRelicLogsClient
from ai_incident_manager.agents.topology_agent import (
    topology_agent, TopologyAgentDeps, TopologyResponse
)
from ai_incident_manager.agents.rca_agent import (
    rca_agent, RCAAgentDeps
)

from openai import AsyncAzureOpenAI

from pydantic_ai.models.openai import OpenAIModel

# Initialize OpenAI client
azure_openai_enabled = "AZURE_OPENAI_ENDPOINT" in os.environ and "AZURE_OPENAI_API_KEY" in os.environ

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
    # add o1 model client
    openai_o1_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment='o1',
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
else:
    raise ValueError("Azure OpenAI is required for this script")

 # Initialize the New Relic clients
api_key = os.environ.get("NEWRELIC_API_KEY")
account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
if not api_key or not account_id:
    raise ValueError("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set in environment variables")

# Deifine the clients needed for the workflow
nr_graphql_client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
nr_query_client = NewRelicQueryClient(nr_graphql_client)
nr_logs_client = NewRelicLogsClient(nr_graphql_client)
entity_analyzer = EntityAnalyzer(nr_graphql_client, debug=True)
metrics_collector = MetricsCollector()
entity_relationship_service = get_entity_relationship_service()
entity_type_service = get_entity_type_service()
runbook_service = get_runbook_service()

# Utility functions for MongoDB storage
def generate_reference_id(prefix: str = "ref") -> str:
    """Generate a unique reference ID for collected information."""
    return f"{prefix}-{uuid.uuid4()}"

async def process_collected_information(
    collected_information: Dict[str, Any],
    incident_id: str,
    entity_guid: str,
    content_type: str
) -> Dict[str, Any]:
    """
    Process collected information and store in MongoDB.
    
    Args:
        collected_information: Information collected by a tool
        incident_id: ID of the incident
        entity_guid: GUID of the entity related to the information
        content_type: Type of content ('metric', 'log', 'information')
        
    Returns:
        Dictionary with reference ID and content summary
    """
    try:
        # Import mongodb service
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Generate a reference ID
        reference_id = generate_reference_id()
        
        # Create a content summary based on the content type
        if content_type == "metric":
            # Store in metrics collection
            metric_name = collected_information.get("name", "unknown_metric")
            metric_data = collected_information.get("data", {})
            
            db_id = await mongodb_service.store_metrics(
                incident_id=incident_id,
                entity_guid=entity_guid,
                metric_name=metric_name,
                metric_data=metric_data
            )
            
            # Create a summary
            metric_value = collected_information.get("value", "N/A")
            summary = f"Metric '{metric_name}' collected with value: {metric_value}"
            
        elif content_type == "log":
            # Store in logs collection
            log_data = collected_information.get("data", {})
            
            db_id = await mongodb_service.store_logs(
                incident_id=incident_id,
                entity_guid=entity_guid,
                log_data=log_data
            )
            
            # Create a summary
            log_count = len(log_data.get("logs", []))
            summary = f"Collected {log_count} log entries"
            
        else:  # information
            # Store in events collection
            event_data = collected_information
            
            db_id = await mongodb_service.store_events(
                incident_id=incident_id,
                entity_guid=entity_guid,
                event_data=event_data
            )
            
            # Create a summary
            event_type = event_data.get("type", "information")
            summary = f"Information of type '{event_type}' collected"
        
        # Return the reference information
        return {
            "reference_id": reference_id,
            "db_id": db_id,
            "content_type": content_type,
            "content_summary": summary,
            "entity_guid": entity_guid
        }
    
    except Exception as e:
        logger.error(f"Error processing collected information: {str(e)}")
        return {
            "reference_id": generate_reference_id("error"),
            "content_type": content_type,
            "content_summary": f"Error storing information: {str(e)}",
            "entity_guid": entity_guid,
            "error": str(e)
        }

async def retrieve_tool_result(result_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve a tool result from MongoDB by its ID.
    
    Args:
        result_id: ID of the tool result to retrieve
        
    Returns:
        The tool result or None if not found
    """
    try:
        # Import mongodb service
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Retrieve the tool result
        return await mongodb_service.get_tool_result_by_id(result_id)
    
    except Exception as e:
        logger.error(f"Error retrieving tool result: {str(e)}")
        return None

@log_node_execution("analyze_alert")
async def analyze_alert(state: IncidentState) -> Dict:
    """
    Analyze the alert using the alert parser agent.
    
    This is a simplified version of the analyze_alert node from the main workflow,
    focused on using the alert parser agent to extract relevant information.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Analyzing alert for Issue {state.incident_id}")
    
    # Extract alert information
    alert = state.raw_alert
    
    # Extract alert creation timestamp (in milliseconds)
    alert_created_at = alert.get("createdAt")
    logger.info(f"Alert created at timestamp: {alert_created_at}")
    
    # Set up dependencies for the agent
    deps = AlertParserDeps(
        openai_client=openai_client,
        nr_query_client=nr_query_client,
        entity_analyzer=entity_analyzer,
    )

    # Initialize timeline events list
    timeline_events = []
    
    # Prepare the user prompt with the alert data
    user_prompt = f"""
    ```json
    {json.dumps(alert, indent=2)}
    ```
    """
    
    # Run the agent
    try:
        # Add the alert creation time to the context for the agent to use
        # Format the user prompt to include explicit instruction about the alert creation time
        user_prompt_with_context = f"""
        Analyze the following alert data.
        The alert was created at timestamp {alert_created_at} (epoch milliseconds).
        Please use this timestamp for time window calculations.
        
        ```json
        {json.dumps(alert, indent=2)}
        ```
        """
        
        result = await alert_parser_agent.run(user_prompt_with_context, deps=deps)
        analysis_result = result.data
        
        # Convert Pydantic model to dict for easier handling
        analysis_dict = analysis_result.model_dump()
        
        # Create a timeline event for the alert trigger
        alert_event: TimelineEvent = {
            "id": f"evt-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Alert Triggered",
            "description": f"{analysis_result.alert_title}",
            "type": "alert",
            "source": "New Relic",
            "tags": [
                {"label": state.severity, "variant": "destructive"},
                {"label": "Alert", "variant": "secondary"}
            ]
        }

        # Add the alert event to the timeline
        timeline_events.append(alert_event)
        
        # Add an investigation note
        investigation_notes = []
        note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "alert_parser",
            "note": f"Alert analyzed. Category: {analysis_result.alert_category}",
            "data": analysis_dict
        }
        investigation_notes.append(note)
        
        # Create a timeline event for the alert analysis
        analysis_event: TimelineEvent = {
            "id": f"evt-{datetime.now(timezone.utc).timestamp():.0f}-analysis",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Alert Analysis",
            "description": f"Analyzed alert and identified category: {analysis_result.alert_category}",
            "type": "analysis",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Analysis", "variant": "secondary"}
            ]
        }
        
        # Add a timeline event for the analysis time window
        if analysis_result.since_time and analysis_result.until_time:
            time_window_event: TimelineEvent = {
                "id": f"evt-{datetime.now(timezone.utc).timestamp():.0f}-time-window",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Analysis Time Window",
                "description": f"Analyzing data from {analysis_result.since_time} to {analysis_result.until_time}",
                "type": "analysis",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Time Window", "variant": "secondary"}
                ]
            }
            # Add this event to the timeline list later
            timeline_events = [alert_event, analysis_event, time_window_event]
        else:
            time_window_event = None
            timeline_events = [alert_event, analysis_event]
        
        # Update the entities in the state
        # entities = []
        # for entity in analysis_result.entities:
        #     # Check if we need to verify entity existence in New Relic
        #     entity_missing = entity.entity_missing
        #     entity_guid = entity.entity_guid
            
        #     entities.append({
        #         "entity_guid": entity_guid,
        #         "entity_name": "Unknown",  # Default values since EntityDetails in alert_parser is minimal
        #         "entity_type": "Unknown",
        #         "cluster_id": None,
        #         "product": None,
        #         "region": None,
        #         "metrics": {},
        #         "logs": [],
        #         "events": [],
        #         "tags": {},
        #         "entity_missing": entity_missing,
        #         "primary_entity": True
        #     })
        
        # Get epoch millisecond timestamps directly from AlertParserResponse
        # No need to convert from ISO format anymore as they're already provided
        since_time_ms = analysis_result.since_time_ms
        until_time_ms = analysis_result.until_time_ms
        
        # Log the time window information
        logger.info(f"Using time window for analysis: since={analysis_result.since_time} ({since_time_ms}), until={analysis_result.until_time} ({until_time_ms})")
        logger.info(f"Original alert creation time: {analysis_result.alert_created_at}")
        
        # Return the updated state fields
        return {
            "alert_category": analysis_result.alert_category,
            "alert_runbook": analysis_result.alert_runbook,
            "entities": analysis_result.entities,
            "condition_name": analysis_result.condition_name,
            "condition_id": analysis_result.condition_id,
            "policy_name": analysis_result.policy_name,
            "policy_id": analysis_result.policy_id,
            "cluster_name": analysis_result.cluster_name,
            "product": analysis_result.product,
            "nr_region": analysis_result.nr_region,
            "landscape": analysis_result.landscape,
            "region": analysis_result.region,
            "since_time": analysis_result.since_time,
            "until_time": analysis_result.until_time,
            "since_time_ms": since_time_ms,
            "until_time_ms": until_time_ms,
            "threshold_duration": analysis_result.threshold_duration,
            "aggregation_window": analysis_result.aggregation_window,
            "investigation_notes": investigation_notes,  # Return the list of notes to be appended the new note
            "timeline": timeline_events,  # Only return the new events
            "current_phase": "alert_analysis_completed"
        }
        
    except Exception as e:
        logger.error(f"Error analyzing alert: {str(e)}")
        # Add an error note
        investigation_notes = []
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "alert_parser",
            "note": f"Error occurred during alert analysis: {str(e)}",
            "data": {"error": str(e)}
        }
        investigation_notes.append(error_note)
        
        # Add an error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Alert Analysis Error",
            "description": f"Error occurred during alert analysis: {str(e)}",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        # Return only the updated fields with the error
        return {
            "error": str(e),
            "investigation_notes": investigation_notes,  # Only return the new note
            "timeline": [error_event]  # Only return the new event
        }

@log_node_execution("analyze_entity_relationships")
async def analyze_entity_relationships(state: IncidentState) -> Dict:
    """
    Analyze relationships between entities to build a topology of the affected system.
    
    This node uses the EntityRelationshipAgent to discover and map relationships
    between entities for a comprehensive understanding of the incident context.
    """
    logger.info(f"Analyzing entity relationships for incident {state.incident_id}")
    
    
    # Extract contextual information from state
    # product = getattr(state, "product", "Unknown")
    # nr_region = getattr(state, "nr_region", "Unknown")
    # landscape = getattr(state, "landscape", "Unknown")
    # region = getattr(state, "region", "Unknown")
    # cluster_name = getattr(state, "cluster_name", "Unknown")
    
    # Set up dependencies for the agent
    deps = EntityRelationshipDeps(
        openai_client=openai_client,
        nr_query_client=nr_query_client,
        state=state
    )
        
    # Prepare alert data and entities for the agent
    alert_category = state.alert_category
    entities = state.entities
    alert_data = state.raw_alert
    
    # Get time window from state
    since_time = getattr(state, "since_time", None)
    until_time = getattr(state, "until_time", None)
    
    # Create a timeline event for starting relationship analysis
    start_event = {
        "id": f"evt-start-rel-analysis-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Entity Relationship Analysis",
        "description": f"Analyzing relationships for {len(entities)} entities",
        "type": "investigation",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Relationships", "variant": "secondary"}
        ]
    }
    
    # Prepare the prompt for the agent
    prompt = f"""
    Please analyze the entity relationships for this incident.
    
    Alert Category: {alert_category}
    Primary Entities: {json.dumps([{"guid": e.entity_guid, "name": getattr(e, "entity_name", "Unknown"), 
                                  "type": getattr(e, "entity_type", "Unknown")} for e in entities])}
    Time Window: {since_time} to {until_time}
    
    Environmental Context:
    - Issue ID: {state.incident_id}
    - Alert Title: {state.title}
    - Alert Description: {state.description}
    - Product: {state.product}
    - Region: {state.region}
    - Landscape: {state.landscape}
    - New Relic Region: {state.nr_region}
    - Cluster Name: {state.cluster_name}

    Full Alert Data:
    {json.dumps(alert_data, indent=2)}
    
    I need a complete relationship map of all relevant entities including:
    1. Primary entities from the alert
    2. Related entities that provide context
    3. Important relationships between entities
    4. Any fallback entities if primary entities are missing
    """
    
    try:
        # Run the agent
        result = await entity_relationship_agent.run(prompt, deps=deps)
        
        # Extract the relationship data
        relationship_data = result.data
        
        # # Create list to store enriched entities
        # enriched_entities = []
        
        # # Process primary entities
        # for entity in relationship_data.primary_entities:
        #     entity_dict = entity.model_dump()
        #     entity_dict["primary_entity"] = True
        #     # Add contextual information
        #     entity_dict["product"] = product
        #     entity_dict["region"] = region
        #     entity_dict["landscape"] = landscape
        #     entity_dict["nr_region"] = nr_region
        #     enriched_entities.append(entity_dict)
        
        # # Process related entities
        # for entity in relationship_data.related_entities:
        #     entity_dict = entity.model_dump()
        #     entity_dict["primary_entity"] = False
        #     # Add contextual information
        #     entity_dict["product"] = product
        #     entity_dict["region"] = region
        #     entity_dict["landscape"] = landscape
        #     entity_dict["nr_region"] = nr_region
        #     enriched_entities.append(entity_dict)

        # if the state entities is not in the relationship_data.entities, add it
        for entity in state.entities:
            if entity not in relationship_data.entities:
                relationship_data.entities.append(entity)

        # count of primary and related entities
        primary_entity_count = len([e for e in relationship_data.entities if e.is_primary])
        related_entity_count = len([e for e in relationship_data.entities if not e.is_primary])
        
        # Create a completion event
        completion_event = {
            "id": f"evt-complete-rel-analysis-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Entity Relationship Analysis Complete",
            "description": f"Found {relationship_data.total_entity_count} entities and {len(relationship_data.relationships)} relationships",
            "type": "analysis",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Relationships", "variant": "secondary"},
                {"label": "Complete", "variant": "outline"}
            ]
        }
        
        # Add an investigation note
        investigation_note = {
            "timestamp": datetime.now().isoformat(),
            "phase": "entity_relationships",
            "note": f"Analyzed entity relationships and identified {primary_entity_count} primary entities and {related_entity_count} related entities",
            "source": "EntityRelationshipAgent",
            "data": {
                "product": state.product,
                "region": state.region,
                "landscape": state.landscape,
                "cluster_name": state.cluster_name,
                "entity_count": relationship_data.total_entity_count,
                "relationship_count": len(relationship_data.relationships)
            }
        }
        
        # Return the updated state fields
        return {
            "entities": relationship_data.entities,
            "entity_relationships": relationship_data.relationships,
            "investigation_notes": [investigation_note],
            "timeline": [start_event, completion_event],
            "current_phase": "entity_relationships_analyzed"
        }
    
    except Exception as e:
        logger.error(f"Error analyzing entity relationships: {str(e)}")
        
        # Create an error event
        error_event = {
            "id": f"evt-error-rel-analysis-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Entity Relationship Analysis Error",
            "description": f"Error analyzing entity relationships: {str(e)}",
            "type": "error",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        # Return error information
        return {
            "error": str(e),
            "timeline": [start_event, error_event],
            "current_phase": "entity_relationships_error"
        }

@log_node_execution("analyze_entity")
async def analyze_entity(state: IncidentState) -> Dict:
    """
    Analyze the entities identified by the alert parser using the entity analyzer agent.
    
    This function takes the entities identified by the alert parser and enriches them
    with detailed information about their type, metrics, logs, and relationships.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Analyzing entities for incident {state.incident_id}")
    
    # Check if we have entities to analyze
    if not state.entities:
        logger.warning("No entities found to analyze")

        investigation_notes = []
        # Add an error note
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "entity_analyzer",
            "note": "No entities found to analyze",
            "data": {"error": "No entities found"}
        }
        investigation_notes.append(error_note)
        
        # Add an error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Entity Analysis Error",
            "description": "No entities found to analyze",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        return {
            "error": "No entities found",
            "investigation_notes": investigation_notes,
            "timeline": [error_event]
        }
    
    # Get entity types from service
    entity_types = entity_type_service.get_all_entity_types()
    
    # # Extract contextual information from state
    # product = getattr(state, "product", "Unknown")
    # nr_region = getattr(state, "nr_region", "Unknown")
    # landscape = getattr(state, "landscape", "Unknown")
    # region = getattr(state, "region", "Unknown")
    # cluster_name = getattr(state, "cluster_name", "Unknown")
    
    # Set up dependencies for the agent
    deps = EntityAnalyzerDeps(
        openai_client=openai_client,
        nr_query_client=nr_query_client,
        nr_logs_client=nr_logs_client,
        entity_analyzer=entity_analyzer,
        entity_types=entity_types,
        state=state
    )
    
    # # Get time window from state (populated by the alert_parser)
    # since_time_str = state.since_time if hasattr(state, "since_time") else None
    # until_time_str = state.until_time if hasattr(state, "until_time") else None
    
    # Parse the ISO timestamps if they're available, otherwise use defaults
    # try:
    #     if since_time_str:
    #         since_time = datetime.fromisoformat(since_time_str.replace('Z', '+00:00'))
    #     else:
    #         # Default to 30 minutes ago if not specified
    #         since_time = datetime.now(timezone.utc) - timedelta(minutes=30)
            
    #     if until_time_str:
    #         until_time = datetime.fromisoformat(until_time_str.replace('Z', '+00:00'))
    #     else:
    #         # Default to current time if not specified
    #         until_time = datetime.now(timezone.utc)
            
    #     # Convert to epoch milliseconds for New Relic API
    #     since_time_ms = int(since_time.timestamp() * 1000)
    #     until_time_ms = int(until_time.timestamp() * 1000)
            
    #     logger.info(f"Using time window for entity analysis: {since_time.isoformat()} to {until_time.isoformat()}")
    #     logger.info(f"Epoch milliseconds: {since_time_ms} to {until_time_ms}")
    # except Exception as e:
    #     logger.warning(f"Error parsing time window from state, using defaults: {str(e)}")
    #     # Fall back to default 30-minute window
    #     until_time = datetime.now(timezone.utc)
    #     since_time = until_time - timedelta(minutes=30)
    #     # Convert to epoch milliseconds for New Relic API
    #     since_time_ms = int(since_time.timestamp() * 1000)
    #     until_time_ms = int(until_time.timestamp() * 1000)
    
    # Process each entity
    updated_entities = []
    timeline_events = []
    investigation_notes = []
    
    # Create a timeline event for starting entity analysis
    start_event: TimelineEvent = {
        "id": f"evt-start-entity-analysis-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Entity Analysis",
        "description": f"Analyzing {len(state.entities)} entities for additional details",
        "type": "investigation",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Analysis", "variant": "secondary"}
        ]
    }
    timeline_events.append(start_event)
    
    for entity in state.entities:
        entity_guid = entity.entity_guid
        if not entity_guid:
            logger.warning("Entity without GUID found. Skipping.")
            continue
        
        try:
            # Get entity details from New Relic
            logger.info(f"Starting get_entity_details")
            entity_details = await entity_analyzer.get_entity_details(entity_guid)
            
            if not entity_details:
                logger.warning(f"No details found for entity {entity_guid}. Skipping.")
                continue
            
            # Format entity details for the prompt and add contextual information
            # entity_details_with_context = entity_details.copy() if isinstance(entity_details, dict) else entity_details
            
            # Add contextual information to the prompt
            context_info = {
                "product": state.product,
                "nr_region": state.nr_region,
                "landscape": state.landscape,
                "region": state.region,
                "cluster_name": state.cluster_name,
                "since_time": state.since_time,
                "until_time": state.until_time
            }
            
            # Create combined entity context for the prompt
            entity_prompt = {
                "entity_details": entity_details,
                "context": context_info
            }
            
            entity_details_json = json.dumps(entity_prompt, indent=2)
            
            # Create a timeline event for entity retrieval
            retrieval_event: TimelineEvent = {
                "id": f"evt-entity-retrieval-{entity_guid[:8]}-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": f"Retrieved Entity Details",
                "description": f"Retrieved detailed information for entity {entity_guid}",
                "type": "investigation",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Entity Analysis", "variant": "secondary"}
                ]
            }
            timeline_events.append(retrieval_event)
            
            # Run the entity analyzer agent with the entity details
            entity_type_result = await entity_analyzer_agent.run(
                entity_details_json,
                deps=deps
            )
            
            # Extract entity type information
            analysis_result = entity_type_result.data
            entity_details = analysis_result.entity_details
            
            # Get entity metadata and logs for the time window
            try:
                
                # Combine all data into entity details
                # detailed_entity = {
                #     "entity_guid": entity_guid,
                #     "entity_name": entity_details.entity_name,
                #     "entity_type": entity_details.entity_type,
                #     "cluster_id": entity_details.cluster_id,
                #     "product": state.product,
                #     "region": state.region,
                #     "nr_region": state.nr_region,
                #     "landscape": state.landscape,
                #     "tags": entity_details.tags
                # }
                
                # Add to the list of updated entities
                updated_entities.append(entity_details)
                
                # Create a timeline event for the analysis
                analysis_event: TimelineEvent = {
                    "id": f"evt-entity-analysis-{entity_guid[:8]}-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": f"Entity Analysis: {entity_details.entity_name}",
                    "description": f"Analyzed entity {entity_guid}: Type={entity_details.entity_type}",
                    "type": "analysis",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Entity Analysis", "variant": "secondary"},
                        {"label": entity_details.entity_type, "variant": "outline"}
                    ]
                }
                timeline_events.append(analysis_event)
                
                # Add to investigation notes
                note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "entity_analyzer",
                    "note": f"Analyzed entity {entity_details.entity_name} ({entity_guid}): {entity_details.entity_type}",
                    "data": {
                        "entity_guid": entity_guid,
                        "entity_type": entity_details.entity_type,
                        "entity_name": entity_details.entity_name,
                        "product": state.product,
                        "region": state.region,
                        "landscape": state.landscape
                    }
                }
                investigation_notes.append(note)
                
            except Exception as e:
                logger.error(f"Error enriching entity data for {entity_guid}: {str(e)}")
                error_event: TimelineEvent = {
                    "id": f"evt-entity-error-{entity_guid[:8] if entity_guid else 'unknown'}-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Entity Analysis Error",
                    "description": f"Error enriching entity data: {str(e)}",
                    "type": "investigation",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Error", "variant": "destructive"}
                    ]
                }
                timeline_events.append(error_event)
                
                error_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "entity_analyzer",
                    "note": f"Error enriching entity data for {entity_guid}: {str(e)}",
                    "data": {"error": str(e)}
                }
                investigation_notes.append(error_note)
        
        except Exception as e:
            logger.error(f"Error analyzing entity {entity_guid}: {str(e)}")
            error_event: TimelineEvent = {
                "id": f"evt-entity-error-{entity_guid[:8] if entity_guid else 'unknown'}-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Entity Analysis Error",
                "description": f"Error analyzing entity: {str(e)}",
                "type": "investigation",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Error", "variant": "destructive"}
                ]
            }
            timeline_events.append(error_event)
            
            error_note: InvestigationNote = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "agent": "entity_analyzer",
                "note": f"Error analyzing entity: {str(e)}",
                "data": {"error": str(e)}
            }
            investigation_notes.append(error_note)
    
    # Create a completion timeline event
    completion_event: TimelineEvent = {
        "id": f"evt-entity-analysis-complete-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Entity Analysis Complete",
        "description": f"Completed analysis of {len(updated_entities)} entities",
        "type": "investigation",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Analysis", "variant": "secondary"},
            {"label": "Complete", "variant": "outline"}
        ]
    }
    timeline_events.append(completion_event)
    
    # Create a summary note
    note: InvestigationNote = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "agent": "entity_analyzer",
        "note": f"Analyzed {len(updated_entities)} entities",
        "data": {
            "entity_count": len(updated_entities),
            "entity_types": list(set(getattr(e, "entity_type", "Unknown") for e in updated_entities)),
            "product": state.product,
            "landscape": state.landscape,
            "region": state.region
        }
    }
    investigation_notes.append(note)
    
    # TODO enrich state entity with entity_details from updated_entities
    # combine updated_entities with state.entities
    state_entities = []
    state_entities.extend(state.entities)
    # flag to say if any entity was updated
    entity_updated = False
    if updated_entities:
        for entity in updated_entities:
            for state_entity in state_entities:
                if entity.entity_guid == state_entity.entity_guid:
                    # which field is not none from updated_entity update the state_entity with that field
                    for field in entity.__dict__:
                        if getattr(entity, field) is not None:
                            setattr(state_entity, field, getattr(entity, field))
                            logger.info(f"Updated {field} for {state_entity.entity_name}")
                            entity_updated = True
   # if updated entity is not in state_entities, add it
    for entity in updated_entities:
        if entity not in state_entities:
            state_entities.append(entity)
    if entity_updated:
        logger.info("Entities updated")
    else:
        logger.info("No entities updated")

    # Update the state with the processed entities
    return {
        "entities": state_entities,
        "current_phase": "entity_analysis_completed",
        "investigation_notes": investigation_notes,
        "timeline": timeline_events
    }

@log_node_execution("execute_runbooks")
async def execute_runbooks(state: IncidentState) -> Dict:
    """
    Execute runbooks based on the entities identified in the incident.
    
    This step:
    1. Identifies the alert category and primary entity
    2. Sets up the runbook context with entity relationships
    3. Invokes the runbook agent to execute the investigation
    4. Processes results and updates incident state
    
    Returns:
        Dict containing execution results
    """
    # Initialize tracking structures
    runbooks_executed = []
    runbook_results = []
    timeline_events = []
    findings = []
    investigation_notes = []
    entities_analyzed = set()
    # Initialize entity_updates dictionary to avoid UnboundLocalError
    entity_updates = {}
    
    # Get the current alert information
    alert_category = state.alert_category
    condition_name = state.condition_name
    
    # Extract contextual information from state
    # product = getattr(state, "product", "Unknown")
    # nr_region = getattr(state, "nr_region", "Unknown")
    # landscape = getattr(state, "landscape", "Unknown")
    # region = getattr(state, "region", "Unknown")
    # cluster_name = getattr(state, "cluster_name", "Unknown")
    
    if not alert_category:
        logger.error("No alert category found in state")
        return {
            "runbooks_executed": [],
            "findings": [],
            "timeline_events": [
                {
                    "id": f"evt-runbook-error-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "No Runbooks Executed",
                    "description": "Missing alert category information",
                    "type": "error",
                    "source": "AI Agent Framework",
                    "tags": [{"label": "Error", "variant": "error"}]
                }
            ],
            "error": "Missing alert category information",
            "current_phase": "runbooks_executed"
        }
    
    # Get entity information
    entities = state.entities
    primary_entity_guid = None
    
    # find the primary entity
    primary_entity = next((e for e in entities if e.is_primary), None)
    primary_entity_guid = primary_entity.entity_guid
    
    # If no entities, cannot continue
    if not primary_entity:
        logger.error("No entities found in state")
        return {
            "runbooks_executed": [],
            "findings": [],
            "timeline_events": [
                {
                    "id": f"evt-runbook-error-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "No Runbooks Executed",
                    "description": "No entities found for runbook execution",
                    "type": "error",
                    "source": "AI Agent Framework",
                    "tags": [{"label": "Error", "variant": "error"}]
                }
            ],
            "error": "No entities found for runbook execution",
            "current_phase": "runbooks_executed"
        }
    
    # Add a timeline event for starting runbook execution
    timeline_events.append({
        "id": f"evt-runbook-start-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Runbook Execution",
        "description": f"Executing runbook for {alert_category}",
        "type": "info",
        "source": "AI Agent Framework",
        "tags": [{"label": "Runbooks", "variant": "secondary"}]
    })
    
    try:
        
        # Get the runbook service and fetch the runbook for the alert category
        # runbook_service = get_runbook_service()
        alert_category_config = await get_alert_category_config(alert_category)
        
        if not alert_category_config:
            logger.warning(f"No alert category config found for {alert_category}")
            alert_category_config = {}
        
        # # Convert primary entity to dict for the agent deps
        # primary_entity_dict = {
        #     "guid": primary_entity.entity_guid,
        #     "name": getattr(primary_entity, "entity_name", "Unknown"),
        #     "type": getattr(primary_entity, "entity_type", "Unknown"),
        #     "product": state.product,
        #     "region": state.region,
        #     "landscape": state.landscape,
        #     "nr_region": state.nr_region,
        #     "cluster_name": state.cluster_name
        # }
        
        # # Create list of related entities as dicts
        # related_entities_list = []
        # for e in entities:
        #     if e != primary_entity:
        #         related_entities_list.append({
        #             "guid": e.entity_guid,
        #             "name": getattr(e, "entity_name", "Unknown"),
        #             "type": getattr(e, "entity_type", "Unknown"),
        #             "product": state.product,
        #             "region": state.region,
        #             "landscape": state.landscape,
        #             "nr_region": state.nr_region
        #         })
        
        # Set up runbook agent dependencies with all needed information
        # since its crucial agent to use o1 model
        deps = RunbookAgentDeps(
            openai_client=openai_o1_client,
            metrics_collector=metrics_collector,
            query_client=nr_query_client,
            logs_client=nr_logs_client,
            state=state, # state has the entities, alert_category, condition_name, etc.
            alert_category=alert_category_config,  # Pass the alert category config
            runbook=alert_category_config.get("runbook", "")  # Pass the runbook directly
        )
        
        # Create the runbook agent
        # runbook_agent = get_runbook_agent()
        
        # Execute the alert category runbook directly
        execution_start_event = {
            "id": f"evt-runbook-exec-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": f"Executing Runbook for {alert_category}",
            "description": f"Starting execution of runbook for alert category {alert_category}",
            "type": "action",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Runbook", "variant": "secondary"},
                {"label": "Executing", "variant": "outline"}
            ]
        }
        timeline_events.append(execution_start_event)
        
        # Construct a prompt that includes time window information
        time_window_info = ""
        if hasattr(state, "since_time") and hasattr(state, "until_time") and state.since_time and state.until_time:
            since_time_str = state.since_time
            until_time_str = state.until_time
            time_window_info = f"\nTime Window for Analysis: {since_time_str} to {until_time_str}"
        
        prompt = f"""
Investigate and resolve the {alert_category} issue for the primary entity.

I need you to execute the runbook for this alert category. The primary entity is {primary_entity.entity_name} with GUID {primary_entity.entity_guid}. 
{time_window_info}

Environmental Context:
- Product: {state.product}
- Region: {state.region}
- Landscape: {state.landscape}
- New Relic Region: {state.nr_region}
- Cluster Name: {state.cluster_name}

Follow all runbook steps, collecting metrics, logs, and information as needed.
Use the tools available to perform each step in the runbook.

All the entities in the state are relevant to the alert category.
{json.dumps([{"guid": e.entity_guid, "name": e.entity_name, "type": e.entity_type} for e in entities])}

For each step:
1. Call the appropriate tools to collect information
2. Categorize collected information as metrics, logs, or information
3. Create a summary for the collected information and the step
4. For collected metrics, logs and information, add the entity information (guid, name, type) to the collected information

Note:
Actual metrics, logs and information collected are stored in db by each tool call, so you only have to summarize the collected information

In your response, include:
1. Each Step result with the step title, description, tool, parameters, summary, issues_found, collected information summary
2. A summary of the runbook execution
"""
        
        runbook_execution_result = await runbook_agent.run(prompt, deps=deps)
        result = runbook_execution_result.data
        
        # Get the runbook metadata
        runbook_id = result.runbook_id
        runbook_name = result.runbook_name
        runbook_description = result.runbook_description
        runbook_summary = result.summary
        issues_found = result.issues_found
            
        # Process the step results
        step_results = result.step_results
        
        # Track metrics, logs, and information for investigation notes
        collected_metrics = []
        collected_logs = []
        collected_info = []
        
        # Process each step result
        for step in step_results:
            step_title = step.step_title
            step_description = step.step_description
            tool = step.tool
            step_summary = step.summary
            issues_found_in_step = step.issues_found

            # Separate collected information by type for tracking
            step_metrics = []
            step_logs = []
            step_info = []

            collected_information_list = []
            for collected_information in step.collected_information:
                info_item = {
                    "content_type": collected_information.content_type,
                    "content_summary": collected_information.content_summary,
                    "entity_guid": collected_information.entity_guid,
                    "entity_name": collected_information.entity_name,
                    "entity_type": collected_information.entity_type,
                    "nrql_query": collected_information.nrql_query
                }
                collected_information_list.append(info_item)
                
                # Categorize by content type
                if collected_information.content_type == "metric":
                    step_metrics.append(info_item)
                    collected_metrics.append(info_item)
                elif collected_information.content_type == "log":
                    step_logs.append(info_item)
                    collected_logs.append(info_item)
                else:  # information
                    step_info.append(info_item)
                    collected_info.append(info_item)
                
            # Format the result into a standardized structure
            step_data = {
                "title": step_title,
                "description": step_description,
                "tool": tool,
                "summary": step_summary,
                "collected_information": collected_information_list,
                "issues_found": issues_found_in_step,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Add step result to timeline events
            timeline_events.append({
                "id": f"evt-runbook-step-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": f"Runbook Step: {step_title}",
                "description": step_summary,
                "type": "action",
                "source": "Runbook Agent",
                "tags": [
                    {"label": "Step", "variant": "secondary"},
                    {"label": "Issue" if issues_found_in_step else "OK", 
                     "variant": "destructive" if issues_found_in_step else "success"}
                ]
            })
            
            # Add step result to investigation notes
            investigation_notes.append({
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "agent": "Runbook Agent",
                "note": f"Step: {step_title} - {step_summary}",
                "data": step_data
            })
        
        # Record the runbook execution
        runbooks_executed.append({
            "runbook_id": runbook_id,
            "runbook_name": runbook_name,
            "entity_guid": primary_entity_guid,
            "issues_found": issues_found,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        
        # Add timeline event for the runbook execution results
        status_label = "Issues Found" if issues_found else "All Clear"
        status_variant = "destructive" if issues_found else "success"
        
        timeline_events.append({
            "id": f"evt-runbook-result-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": f"Runbook Execution Results: {status_label}",
            "description": runbook_summary,
            "type": "analysis",
            "source": "Runbook Agent",
            "tags": [
                {"label": status_label, "variant": status_variant},
                {"label": "Runbook", "variant": "secondary"}
            ]
        })

        # Add a summary investigation note
        investigation_notes.append({
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "Runbook Agent",
            "note": f"Executed runbook '{runbook_name}' - {runbook_summary}",
            "data": {
                "runbook_id": runbook_id,
                "runbook_name": runbook_name,
                "runbook_description": runbook_description,
                "issues_found": issues_found,
                "metrics_count": len(collected_metrics),
                "logs_count": len(collected_logs),
                "info_count": len(collected_info)
            }
        })
        
        # Add a final completion event
        timeline_events.append({
            "id": f"evt-runbook-complete-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Runbook Execution Complete",
            "description": f"Completed execution of '{runbook_name}' runbook for {alert_category}",
            "type": "info",
            "source": "AI Agent Framework",
            "tags": [{"label": "Complete", "variant": "success"}]
        })
        
        # Store the complete incident state in MongoDB
        try:
            # Import here to avoid circular imports
            from ai_incident_manager.services.mongodb_service import get_mongodb_service
            mongodb_service = get_mongodb_service()
            
            # Store the IncidentState model directly
            # This will handle serialization of Pydantic models
            incident_doc_id = await mongodb_service.store_incident_state(state)
            logger.info(f"Stored incident state in MongoDB for incident {state.incident_id}, document ID: {incident_doc_id}")
            
        except Exception as db_error:
            logger.error(f"Error storing incident in MongoDB: {str(db_error)}", exc_info=True)
        
    except Exception as e:
        logger.error(f"Error executing runbooks: {str(e)}", exc_info=True)
        
        # Add error event to timeline
        timeline_events.append({
            "id": f"evt-runbook-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Runbook Execution Error",
            "description": str(e),
            "type": "error",
            "source": "AI Agent Framework",
            "tags": [{"label": "Error", "variant": "error"}]
        })
    
    # Return the updated state values
    return_values = {
        "runbooks_executed": runbooks_executed,
        "runbook_results": step_results if 'step_results' in locals() else [],
        "runbook_id": result.runbook_id if 'result' in locals() else None,
        "runbook_name": result.runbook_name if 'result' in locals() else None,
        "runbook_summary": result.summary if 'result' in locals() else None,
        "timeline": timeline_events,
        "investigation_notes": investigation_notes,
        "current_phase": "runbooks_executed"
    }
    
    # Now, fetch all metrics, logs, and events for this run to update the state
    try:
        if hasattr(state, "run_id") and state.run_id:
            # Import here to avoid circular imports
            from ai_incident_manager.services.mongodb_service import get_mongodb_service
            mongodb_service = get_mongodb_service()
            
            run_id = state.run_id
            logger.info(f"Fetching data from MongoDB for run_id: {run_id}")
            
            # Fetch metrics
            metrics_query = {"incident_id": state.incident_id, "run_id": run_id}
            metrics_cursor = mongodb_service._metrics_collection.find(metrics_query)
            metrics = await metrics_cursor.to_list(length=None)
            logger.info(f"Found {len(metrics)} metrics for run_id: {run_id}")
            
            # Fetch logs
            logs_query = {"incident_id": state.incident_id, "run_id": run_id}
            logs_cursor = mongodb_service._logs_collection.find(logs_query)
            logs = await logs_cursor.to_list(length=None)
            logger.info(f"Found {len(logs)} logs for run_id: {run_id}")
            
            # Fetch events
            events_query = {"incident_id": state.incident_id, "run_id": run_id}
            events_cursor = mongodb_service._events_collection.find(events_query)
            events = await events_cursor.to_list(length=None)
            logger.info(f"Found {len(events)} events for run_id: {run_id}")
            
            # Update the return values to include these collections
            if metrics:
                return_values["metrics"] = [mongodb_service._deserialize_from_mongodb(metric) for metric in metrics]
            if logs:
                return_values["logs"] = [mongodb_service._deserialize_from_mongodb(log) for log in logs]
            if events:
                return_values["events"] = [mongodb_service._deserialize_from_mongodb(event) for event in events]
    except Exception as e:
        logger.error(f"Error fetching data from MongoDB: {str(e)}")
    
    return return_values

@log_node_execution("analyze_root_cause")
async def analyze_root_cause(state: IncidentState) -> Dict:
    """
    Analyze the root cause of the incident based on collected data.
    
    This node aggregates all the data collected in previous steps (alert details,
    entity information, metrics, logs, runbook execution results) and uses an
    AI agent to determine the likely root cause of the incident.
    
    Args:
        state: Current workflow state with all collected data
        
    Returns:
        Updated fields of workflow state with root cause analysis
    """
    logger.info(f"Starting root cause analysis for incident {state.incident_id}")
    
    # Create a timeline event for starting RCA
    start_event: TimelineEvent = {
        "id": f"evt-rca-start-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Root Cause Analysis",
        "description": "Analyzing collected data to determine root cause",
        "type": "analysis",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "RCA", "variant": "secondary"}
        ]
    }
    
    # Initialize collections for update
    timeline_events = [start_event]
    investigation_notes = []
    
    try:
        # Import the RCA agent
        from ai_incident_manager.agents.rca_agent import rca_agent, RCAAgentDeps
        
        # Initialize OpenAI client (use o1 model for better reasoning capabilities)
        # This is especially important for complex RCA tasks
        openai_client = AsyncAzureOpenAI(
            azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
            azure_deployment="o1",  # Use more powerful model for RCA
            api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
            api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
        )
        
        # Set up dependencies for the agent
        deps = RCAAgentDeps(
            openai_client=openai_client,
            state=state,
            nr_query_client=nr_query_client if 'nr_query_client' in globals() else None,
            logs_client=nr_logs_client if 'nr_logs_client' in globals() else None,
            metrics_collector=metrics_collector if 'metrics_collector' in globals() else None
        )
        
        # Prepare the prompt with structured summary of collected data
        # Extract key information from state for the prompt
        alert_category = state.alert_category
        alert_title = state.title
        
        # Format entities information
        entities_info = []
        for entity in state.entities:
            entity_info = {
                "guid": entity.entity_guid,
                "name": entity.entity_name,
                "type": entity.entity_type,
                "is_primary": getattr(entity, "is_primary", False)
            }
            entities_info.append(entity_info)
        
        # Format runbook results
        runbook_summary = []
        if state.runbook_results:
            for step in state.runbook_results:
                # Convert to dict if it's a Pydantic model
                if hasattr(step, "model_dump"):
                    step_dict = step.model_dump()
                else:
                    step_dict = step
                
                step_info = {
                    "title": step_dict.get("step_title", "Unknown"),
                    "summary": step_dict.get("summary", ""),
                    "issues_found": step_dict.get("issues_found", False)
                }
                runbook_summary.append(step_info)
        
        # Create a structured prompt with all relevant information
        user_prompt = f"""
Please perform a comprehensive root cause analysis for this incident:

INCIDENT SUMMARY:
- ID: {state.incident_id}
- Title: {state.title}
- Alert Category: {alert_category}
- Alert Title: {alert_title}
- Severity: {state.severity}
- Product: {state.product or "Unknown"}
- Landscape: {state.landscape or "Unknown"}
- Region: {state.region or "Unknown"}
- Time Window: {state.since_time or "Unknown"} to {state.until_time or "Unknown"}

ENTITIES ({len(state.entities)}):
{json.dumps(ensure_serializable(entities_info), indent=2)}

ENTITY RELATIONSHIPS:
{json.dumps(ensure_serializable(state.entity_relationships), indent=2) if state.entity_relationships else "No relationships defined"}

RUNBOOK RESULTS:
{json.dumps(ensure_serializable(runbook_summary), indent=2) if runbook_summary else "No runbook results available"}

Based on the comprehensive data available in the incident state, determine the most likely root cause of this incident.
Identify primary factors, secondary factors, and provide evidence for your conclusions.

Use the available tools to retrieve additional data from MongoDB if needed to support your analysis.

Be as detailed as possible in your analysis with specific evidence pointing to the root cause.
Include confidence levels, uncertainty factors, and a timeline of events leading to the incident.
"""
        
        # Execute the agent
        result = await rca_agent.run(user_prompt, deps=deps)
        rca_result = result.data
        
        # Convert Pydantic model to dict for easier handling
        rca_dict = ensure_serializable(rca_result.model_dump())
        
        # Extract the main findings
        primary_root_cause = rca_result.primary_root_cause
        confidence_level = rca_result.confidence_level
        evidence_summary = rca_result.evidence_summary
        secondary_factors = rca_result.secondary_factors
        recommendations = rca_result.recommendations
        remediation_actions = rca_result.remediation_actions
        alert_summary = rca_result.alert_summary
        
        # Create a detailed dictionary for the rca_details field
        # Note: This replaces the previous root_cause_analysis field with a cleaner, more structured format
        rca_details = {
            "primary_root_cause": primary_root_cause,
            "confidence_level": confidence_level,
            "evidence_summary": evidence_summary,
            "secondary_factors": secondary_factors,
            "timeline_reconstruction": rca_result.timeline_reconstruction,
            "affected_components": rca_result.affected_components,
            "recommendations": recommendations,
            "uncertainty_factors": rca_result.uncertainty_factors,
            "prioritized_factors": [factor.model_dump() for factor in rca_result.prioritized_factors],
            "analysis_time": datetime.now(timezone.utc).isoformat()
        }
        
        # Create a completion event with the root cause
        completion_event: TimelineEvent = {
            "id": f"evt-rca-complete-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Root Cause Identified",
            "description": primary_root_cause,
            "type": "analysis",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "RCA", "variant": "secondary"},
                {"label": f"Confidence: {confidence_level}/10", "variant": "outline"}
            ]
        }
        timeline_events.append(completion_event)
        
        # Create detailed notes for the investigation
        rca_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "root_cause_analyzer",
            "note": f"Primary Root Cause: {primary_root_cause}",
            "data": rca_details  # Use the comprehensive details dictionary
        }
        investigation_notes.append(rca_note)
        
        # Create recommendations event for the timeline if available
        if recommendations:
            recommendations_event: TimelineEvent = {
                "id": f"evt-rca-recommendations-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Recommendations",
                "description": f"{len(recommendations)} recommendations provided",
                "type": "analysis",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Recommendations", "variant": "success"}
                ]
            }
            timeline_events.append(recommendations_event)
        
        # Create factors events for the timeline
        if rca_result.prioritized_factors:
            for i, factor in enumerate(rca_result.prioritized_factors[:3]):  # Show top 3 in timeline
                factor_event: TimelineEvent = {
                    "id": f"evt-rca-factor-{i}-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": f"Factor {i+1}: {factor.factor}",
                    "description": f"Importance: {factor.importance}/10, Confidence: {factor.confidence}/10",
                    "type": "analysis",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Factor", "variant": "outline"}
                    ]
                }
                timeline_events.append(factor_event)
        
        # Store the RCA results in MongoDB
        try:
            # Import here to avoid circular imports
            from ai_incident_manager.services.mongodb_service import get_mongodb_service
            mongodb_service = get_mongodb_service()
            
            # Store RCA results in a dedicated collection
            rca_doc_id = await mongodb_service.store_rca_results(
                incident_id=state.incident_id,
                rca_results=rca_details,  # Store the comprehensive details dictionary
                timestamp=datetime.now(timezone.utc)
            )
            logger.info(f"Stored RCA results in MongoDB for incident {state.incident_id}, document ID: {rca_doc_id}")
            
        except Exception as db_error:
            logger.error(f"Error storing RCA results in MongoDB: {str(db_error)}", exc_info=True)
            # Continue processing even if MongoDB storage fails
        
        # Return the updated state fields
        # Note: root_cause_analysis is kept for backward compatibility, but rca_details is the preferred field
        return {
            "root_cause": primary_root_cause,
            "root_cause_analysis": rca_dict,  # Keep for backward compatibility
            "rca_details": rca_details,  # New field with more structured data
            "timeline": timeline_events,
            "investigation_notes": investigation_notes,
            "current_phase": "root_cause_analyzed",
            "remediation_actions": remediation_actions,
            "alert_summary": alert_summary
        }
        
    except Exception as e:
        logger.error(f"Error in root cause analysis: {str(e)}")
        
        # Create an error event
        error_event: TimelineEvent = {
            "id": f"evt-rca-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Error in Root Cause Analysis",
            "description": f"Error analyzing root cause: {str(e)}",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        timeline_events.append(error_event)
        
        # Create an error note
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "root_cause_analyzer",
            "note": f"Error analyzing root cause: {str(e)}",
            "data": {"error": str(e)}
        }
        investigation_notes.append(error_note)
        
        return {
            "error": str(e),
            "timeline": timeline_events,
            "investigation_notes": investigation_notes,
            "current_phase": "root_cause_error"
        }

@log_node_execution("should_continue")
async def should_continue(state: IncidentState) -> Dict:
    """
    Determine if the workflow should continue to the next step or end.
    
    Args:
        state: Current workflow state
        
    Returns:
        Dictionary with updates to state, including value for __next__ to specify the next node
    """
    # Check the current phase to determine the next step
    current_phase = state.current_phase
    next_node = None
    
    if current_phase == "initialize":
        # After initialization, go to alert analysis
        next_node = "analyze_alert"
    elif current_phase == "alert_analysis_completed":
        # After alert parsing, create a ticket
        next_node = "create_ticket"
    elif current_phase == "ticket_created":
        # After ticket creation, analyze entity relationships
        next_node = "analyze_entity_relationships"
    elif current_phase == "entity_analysis_completed":
        # After entity analysis, analyze entity relationships
        next_node = "analyze_entity_relationships"
    elif current_phase == "entity_relationships_analyzed":
        # After relationship analysis, execute runbooks
        next_node = "execute_runbooks"
    elif current_phase == "runbooks_executed":
        # After runbook execution, perform root cause analysis
        logger.info("Runbooks have been executed, moving to root cause analysis")
        next_node = "analyze_root_cause"
    elif current_phase == "root_cause_analyzed":
        # After root cause analysis, generate topology
        logger.info("Root cause analysis complete, moving to generate topology")
        next_node = "generate_topology"
    elif current_phase == "topology_generated":
        # After topology generation, report results
        logger.info("Topology generated, reporting results")
        next_node = "report_results"
    elif current_phase == "results_reported":
        # After reporting results, go to finish
        logger.info("Results reported, moving to finish node")
        next_node = "finish"
    elif current_phase == "completed":
        # End the workflow after completion
        logger.info("Workflow processing finished")
        next_node = END
    else:
        # Default to end if we don't recognize the phase
        logger.info(f"Workflow ending with unknown phase: {current_phase}")
        next_node = END
    
    logger.info(f"Determined next node: {next_node}")
    
    # Return a dictionary with __next__ key indicating the next node
    return {"__next": next_node}

@log_node_execution("generate_topology")
async def generate_topology(state: IncidentState) -> Dict:
    """
    Generate a topology map of the entities involved in the incident.
    
    This function takes the entities and entity relationships identified in previous
    steps and generates a topology map showing how they are connected.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state with topology information
    """
    logger.info(f"Generating topology for incident {state.incident_id}")
    
    # Check if we have entities for topology generation
    if not state.entities and not hasattr(state, "entity_details"):
        logger.warning("No entities found for topology generation")
        # Add an error note
        investigation_notes = []
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "topology_generator",
            "note": "No entities found for topology generation",
            "data": {"error": "No entities found"}
        }
        
        # Add an error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Topology Generation Error",
            "description": "No entities found for topology generation",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        return {
            "error": "No entities found",
            "investigation_notes": investigation_notes,
            "timeline": [error_event]
        }
    
    # Create start event for timeline
    start_event: TimelineEvent = {
        "id": f"evt-topology-start-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Topology Generation",
        "description": "Generating service topology map based on affected entities",
        "type": "investigation",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Topology", "variant": "secondary"}
        ]
    }
    
    # Use first entity as primary if not specified
    primary_entity_guid = getattr(state, "primary_entity_guid", None)
    if not primary_entity_guid and state.entities:
        primary_entity_guid = state.entities[0].entity_guid
        logger.info(f"Using first entity as primary: {primary_entity_guid}")
    
    # Extract required information
    product = getattr(state, "product", "Unknown")
    nr_region = getattr(state, "nr_region", "Unknown") 
    landscape = getattr(state, "landscape", "Unknown")
    region = getattr(state, "region", "Unknown")
    cluster_name = getattr(state, "cluster_name", "Unknown")
    incident_id = state.incident_id
    
    if not primary_entity_guid:
        logger.warning("Missing required information for topology generation")
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "topology_generator",
            "note": "Missing required information for topology generation",
            "data": {"error": "Missing primary entity information"}
        }
        
        # Add error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-topology-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Topology Generation Error",
            "description": "Missing primary entity information",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        return {
            "error": "Missing primary entity information",
            "investigation_notes": [error_note],
            "timeline": [start_event, error_event]
        }
    
    # Initialize the agent dependencies
    try:
        # Initialize OpenAI client
        openai_client = AsyncAzureOpenAI(
            azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
            azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
            api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
            api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
        )
        
        # Initialize New Relic client
        nr_query_client = getattr(state, "nr_query_client", None)
        
        # Set up dependencies for the agent
        deps = TopologyAgentDeps(
            openai_client=openai_client,
            nr_query_client=nr_query_client
        )
    except Exception as e:
        logger.error(f"Error initializing topology agent dependencies: {str(e)}")
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "topology_generator",
            "note": f"Error initializing dependencies: {str(e)}",
            "data": {"error": str(e)}
        }
        
        # Add error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-topology-dep-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Topology Generation Error",
            "description": f"Error initializing dependencies: {str(e)}",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        return {
            "error": "Error initializing dependencies",
            "investigation_notes": [error_note],
            "timeline": [start_event, error_event]
        }
    
    # Prepare the user prompt
    user_prompt = f"""
    Please generate and analyze topology data for the following entity:
    
    Entity GUID: {primary_entity_guid}
    Product: {product}
    Landscape: {landscape}
    Region: {region}
    New Relic Region: {nr_region}
    Issue ID: {incident_id}
    Cluster Name: {cluster_name}
    
    I need a detailed analysis of the topology, including:
    1. Key services and their relationships
    2. Potential impact of this issue on the system
    3. Recommendations for further investigation based on the topology
    """
    
    # Run the agent
    try:
        result = await topology_agent.run(user_prompt, deps=deps)
        
        # Extract the topology data
        topology_data = result.data.topology.model_dump()
        
        # Add an investigation note
        investigation_notes = []
        note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "topology_generator",
            "note": f"Topology generated with {len(topology_data['nodes'])} nodes and {len(topology_data['links'])} links",
            "data": {
                "product": product,
                "landscape": landscape,
                "region": region,
                "nr_region": nr_region,
                "cluster_name": cluster_name,
                "analysis": result.data.analysis,
                "impacted_services": result.data.impacted_services,
                "adjacent_services": result.data.adjacent_services,
                "potential_cascading_impacts": result.data.potential_cascading_impacts,
                "visualization_notes": result.data.topology_visualization_notes
            }
        }
        investigation_notes.append(note)
        
        # Add completion event to timeline
        completion_event: TimelineEvent = {
            "id": f"evt-topology-complete-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Topology Generated",
            "description": f"Generated topology with {len(topology_data['nodes'])} services and {len(topology_data['links'])} relationships",
            "type": "analysis",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Topology", "variant": "secondary"},
                {"label": "Complete", "variant": "outline"}
            ]
        }
        
        # Add impact event to timeline if there are impacted services
        if result.data.impacted_services:
            impact_event: TimelineEvent = {
                "id": f"evt-topology-impact-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Service Impact Assessment",
                "description": f"Identified {len(result.data.impacted_services)} directly impacted services and {len(result.data.adjacent_services)} adjacent services",
                "type": "analysis",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Impact", "variant": "destructive"}
                ]
            }
            
            # Update the state
            return {
                "topology": topology_data,
                "topology_analysis": result.data.analysis,
                "impacted_services": result.data.impacted_services,
                "adjacent_services": result.data.adjacent_services,
                "investigation_notes": investigation_notes,
                "timeline": [start_event, completion_event, impact_event],
                "current_phase": "topology_generated"
            }
        else:
            # Update the state
            return {
                "topology": topology_data,
                "topology_analysis": result.data.analysis,
                "impacted_services": result.data.impacted_services,
                "adjacent_services": result.data.adjacent_services,
                "investigation_notes": investigation_notes,
                "timeline": [start_event, completion_event],
                "current_phase": "topology_generated"
            }
        
    except Exception as e:
        logger.error(f"Error generating topology: {str(e)}")
        # Add an error note
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "topology_generator",
            "note": f"Error generating topology: {str(e)}",
            "data": {"error": str(e)}
        }
        
        # Add error event to timeline
        error_event: TimelineEvent = {
            "id": f"evt-topology-exec-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Topology Generation Error",
            "description": f"Error generating topology: {str(e)}",
            "type": "investigation",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        
        return {
            "error": "Error generating topology",
            "investigation_notes": [error_note],
            "timeline": [start_event, error_event]
        }

@log_node_execution("finish")
async def finish(state: IncidentState) -> Dict:
    """
    Final node in the workflow that wraps up the incident analysis.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Finishing workflow for incident {state.incident_id}")
    
    # Extract contextual information from state
    product = state.product or "Unknown"
    nr_region = state.nr_region or "Unknown"
    landscape = state.landscape or "Unknown"
    region = state.region or "Unknown"
    cluster_name = state.cluster_name or "Unknown"
    
    # Create a timeline event for workflow completion
    completion_event = TimelineEvent(
        id=f"evt-workflow-complete-{datetime.now(timezone.utc).timestamp():.0f}",
        incidentId=state.incident_id,
        timestamp=datetime.now(timezone.utc).isoformat(),
        title="Incident Analysis Workflow Complete",
        description="All incident analysis steps completed",
        type="investigation",
        source="AI Agent Framework",
        tags=[
            EventTag(label="Workflow", variant="secondary"),
            EventTag(label="Complete", variant="outline")
        ]
    )
    
    # Add a summary timeline event with findings
    alert_category = state.alert_category
    entity_count = len(state.entities)
    
    summary_event = TimelineEvent(
        id=f"evt-workflow-summary-{datetime.now(timezone.utc).timestamp():.0f}",
        incidentId=state.incident_id,
        timestamp=datetime.now(timezone.utc).isoformat(),
        title="Incident Analysis Summary",
        description=f"Alert Category: {alert_category}, Entities Analyzed: {entity_count}, Product: {product}, Landscape: {landscape}",
        type="analysis",
        source="AI Agent Framework",
        tags=[
            EventTag(label="Summary", variant="secondary")
        ]
    )
    
    # Add an investigation note for the completion
    investigation_notes = []
    note = InvestigationNote(
        timestamp=datetime.now(timezone.utc).isoformat(),
        agent="workflow_manager",
        note="Workflow execution completed",
        data={
            "alert_category": alert_category,
            "entity_count": entity_count,
            "product": product,
            "region": region,
            "nr_region": nr_region,
            "cluster_name": cluster_name
        }
    )
    investigation_notes.append(note)
    
    # Create a summary of the analysis results
    analysis_summary = "### Incident Analysis Summary\n\n"
    
    # Count metrics and logs
    metric_count = len(state.metrics)
    log_count = len(state.logs)
    
    analysis_summary += f"* Analyzed {entity_count} entities\n"
    analysis_summary += f"* Collected {metric_count} metrics\n"
    analysis_summary += f"* Analyzed {log_count} log entries\n"
    
    # Add root cause information if available
    if state.root_cause:
        analysis_summary += f"\n#### Root Cause Analysis:\n"
        analysis_summary += f"* Primary Root Cause: {state.root_cause}\n"
        
        # Add more RCA details if available
        if state.rca_details:
            if "secondary_factors" in state.rca_details and state.rca_details["secondary_factors"]:
                analysis_summary += f"* Secondary Factors:\n"
                for factor in state.rca_details["secondary_factors"]:
                    analysis_summary += f"  - {factor}\n"
            
            if "confidence_level" in state.rca_details:
                analysis_summary += f"* Confidence Level: {state.rca_details['confidence_level']}/10\n"
            
            if "evidence_summary" in state.rca_details:
                analysis_summary += f"* Evidence Summary: {state.rca_details['evidence_summary']}\n"
                
            # Add prioritized factors section if available
            if "prioritized_factors" in state.rca_details and state.rca_details["prioritized_factors"]:
                analysis_summary += f"\n#### Prioritized Factors:\n"
                for i, factor in enumerate(state.rca_details["prioritized_factors"]):
                    analysis_summary += f"* Factor {i+1}: {factor.get('factor', 'Unknown')}\n"
                    analysis_summary += f"  - Importance: {factor.get('importance', 'N/A')}/10\n"
                    analysis_summary += f"  - Confidence: {factor.get('confidence', 'N/A')}/10\n"
            
            if "recommendations" in state.rca_details and state.rca_details["recommendations"]:
                analysis_summary += f"\n#### Recommendations:\n"
                for rec in state.rca_details["recommendations"]:
                    analysis_summary += f"* {rec}\n"
    
    # Add system check results if available
    if state.system_checks:
        analysis_summary += "\n#### System Check Results:\n"
        for check in state.system_checks:
            status_icon = "✅" if check.status == "ALL_GOOD" else "❌"
            analysis_summary += f"* {status_icon} {check.category}: {check.description}\n"
    
    # Add remediation actions summary if available
    if state.remediation_actions:
        analysis_summary += "\n#### Recommended Actions:\n"
        for action in state.remediation_actions:
            analysis_summary += f"* {action.title}: {action.description}\n"
    
    # Add steps from runbook results if available
    if state.runbook_results:
        analysis_summary += "\n#### Runbook Steps Executed:\n"
        for step in state.runbook_results:
            status_icon = "❌" if step.issues_found else "✅"
            analysis_summary += f"* {status_icon} {step.step_title}: {step.summary}\n"
    
    # Add the analysis summary to the investigation notes
    summary_note = InvestigationNote(
        timestamp=datetime.now(timezone.utc).isoformat(),
        agent="analysis_engine",
        note=analysis_summary,
        data={
            "metrics_count": metric_count,
            "logs_count": log_count,
            "entity_count": entity_count
        }
    )
    investigation_notes.append(summary_note)
    
    # Add the summary notes and events to the state
    # These need to be added first so they're included in the MongoDB storage
    state.investigation_notes.extend(investigation_notes)
    state.timeline.extend([completion_event, summary_event])
    
    # Update the state with the final status
    state.current_phase = "completed"
    state.analysis_summary = analysis_summary
    
    # Store the final incident state in MongoDB
    try:
        # Import here to avoid circular imports
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Store the complete state directly with update_tool_results=False
        # This ensures tool_results are not overwritten and not stored in the incident document
        incident_doc_id = await mongodb_service.store_incident_state(
            state=state,
            update_tool_results=False
        )
        logger.info(f"Stored final incident state in MongoDB for incident {state.incident_id}, document ID: {incident_doc_id}")
        
    except Exception as db_error:
        logger.error(f"Error storing final incident in MongoDB: {str(db_error)}", exc_info=True)
    
    # Return the updated fields (not the entire state)
    return {
        "current_phase": "completed",
        "investigation_notes": investigation_notes,  # Only return the new notes
        "timeline": [completion_event, summary_event],  # Only return the new events
        "analysis_summary": analysis_summary
    }

@log_node_execution("create_ticket")
async def create_ticket(state: IncidentState) -> Dict:
    """
    Create an Azure DevOps ticket for the incident.
    
    This node is executed after the alert analysis to create a ticket
    that will be updated with analysis results as they become available.
    
    Args:
        state: Current workflow state with alert analysis results
        
    Returns:
        Updated fields of workflow state with ticket information
    """
    logger.info(f"Creating ticket for incident {state.incident_id}")
    
    # Create a timeline event for starting ticket creation
    start_event: TimelineEvent = {
        "id": f"evt-ticket-start-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Creating Incident Ticket",
        "description": "Creating a ticket in Azure DevOps for incident tracking",
        "type": "integration",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Ticket", "variant": "outline"}
        ]
    }
    
    # Initialize ticket creation results
    events = [start_event]
    notes = []
    ticket_id = None
    ticket_url = None
    dashboard_url = os.environ.get("IIM_DASHBOARD_BASE_URL", "https://obv-ai-compute.ivantiai.com:8080/incident/") + state.incident_id
    error_message = None
    
    # Attempt to create a ticket
    try:
        # Get the services
        ado_service = get_ado_service()
        reporting_agent = get_reporting_agent()
        
        # Set up the dependencies for the agent
        deps = ReportingAgentDeps(
            openai_client=openai_client
        )
        
        # Convert entities to dictionaries
        entities_list = []
        if state.entities:
            entities_list = [entity.model_dump() if hasattr(entity, "model_dump") else entity for entity in state.entities]
            
        # Generate prompt for the agent
        prompt = f"""
        Generate a title and description for an incident tracking ticket for an Azure DevOps work item.
        
        Here is the incident information:
        - Incident ID: {state.incident_id}
        - Alert Title: {state.alert_title if hasattr(state, 'alert_title') else 'Unknown'}
        - Condition Name: {state.condition_name if hasattr(state, 'condition_name') else 'Unknown'}
        - Policy Name: {state.policy_name if hasattr(state, 'policy_name') else 'Unknown'}
        - Cluster: {state.cluster_name if hasattr(state, 'cluster_name') else 'Unknown'}
        - Severity: {state.severity if hasattr(state, 'severity') else 'Unknown'}
        - Product: {state.product if hasattr(state, 'product') else 'Unknown'}
        - Region: {state.region if hasattr(state, 'region') else 'Unknown'}
        - Start Time: {state.start_time if hasattr(state, 'start_time') else 'Unknown'}
        
        The title should be concise but informative, including the severity level.
        The description should include all relevant details about the incident.
        """
        
        # Get ticket content from the agent
        logger.info(f"Generating ticket content for incident {state.incident_id}")
        agent_response = await reporting_agent.run(prompt, deps=deps)
        report_content = agent_response.data
        
        # Create the ticket using ADO service directly
        logger.info(f"Creating new ticket for incident {state.incident_id}")
        
        # Create a custom state with the elements needed for ticket creation
        ticket_state = {
            "incident_id": state.incident_id,
            "title": report_content.ticket_title,
            "severity": state.severity,
            "product": state.product,
            "region": state.region,
            "root_cause": state.root_cause,
            "rca_details": state.rca_details,
            "entities": entities_list,  # Use the pre-converted entities
            "runbook_results": state.runbook_results,  # Use the pre-converted runbook results
            "start_time": state.start_time
        }
        
        # Create the ticket
        ticket_result = ado_service.create_initial_ticket(ticket_state)
        
        if ticket_result:
            logger.info(f"Successfully created ticket {ticket_result.get('ticket_id')}")
            ticket_id = ticket_result.get("ticket_id")
            ticket_url = ticket_result.get("ticket_url")
            
            # Create success event
            success_event: TimelineEvent = {
                "id": f"evt-ticket-created-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Azure DevOps Ticket Created",
                "description": f"Created ticket #{ticket_id}",
                "type": "integration",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Azure DevOps", "variant": "outline"},
                    {"label": "Success", "variant": "success"}
                ]
            }
            events.append(success_event)
            
            # Add ticket note
            ticket_note: InvestigationNote = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "agent": "reporting_agent",
                "note": f"Created Azure DevOps ticket #{ticket_id}",
                "data": {
                    "ticket_id": ticket_id,
                    "ticket_url": ticket_url
                }
            }
            notes.append(ticket_note)
            
            # Return the updated state fields
            return {
                "ado_ticket_id": ticket_id,
                "ado_ticket_url": ticket_url,
                "dashboard_url": dashboard_url,
                "timeline": events,
                "investigation_notes": notes,
                "current_phase": "ticket_created"
            }
        else:
            error_message = "Failed to create ticket"
            logger.error(error_message)
            
            # Create error event
            error_event: TimelineEvent = {
                "id": f"evt-ticket-error-{datetime.now(timezone.utc).timestamp():.0f}",
                "incidentId": state.incident_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "title": "Azure DevOps Ticket Creation Failed",
                "description": error_message,
                "type": "integration",
                "source": "AI Agent Framework",
                "tags": [
                    {"label": "Azure DevOps", "variant": "outline"},
                    {"label": "Error", "variant": "destructive"}
                ]
            }
            events.append(error_event)
            
            # Add error note
            error_note: InvestigationNote = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "agent": "reporting_agent",
                "note": f"Failed to create Azure DevOps ticket: {error_message}",
                "data": {"error": error_message}
            }
            notes.append(error_note)
            
            # Return error information
            return {
                "error": error_message,
                "timeline": events,
                "investigation_notes": notes
            }
    
    except Exception as e:
        error_message = str(e)
        logger.error(f"Error creating ticket: {error_message}")
        
        # Create an error event
        error_event: TimelineEvent = {
            "id": f"evt-ticket-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Azure DevOps Ticket Creation Error",
            "description": error_message,
            "type": "integration",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Azure DevOps", "variant": "outline"},
                {"label": "Error", "variant": "destructive"}
            ]
        }
        events.append(error_event)
        
        # Add error note
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "reporting_agent",
            "note": f"Error creating Azure DevOps ticket: {error_message}",
            "data": {"error": error_message}
        }
        notes.append(error_note)
        
        # Return error information
        return {
            "error": error_message,
            "timeline": events,
            "investigation_notes": notes
        }

@log_node_execution("report_results")
async def report_results(state: IncidentState) -> Dict:
    """
    Report the incident analysis results by updating the ADO ticket and sending a Teams notification.
    
    This node is executed at the end of the workflow, right before the finish node,
    to update the ticket with all analysis results and send a notification.
    
    Args:
        state: Current workflow state with complete analysis results
        
    Returns:
        Updated fields of workflow state with reporting results
    """
    logger.info(f"Reporting results for incident {state.incident_id}")
    
    # Create a timeline event for starting results reporting
    start_event: TimelineEvent = {
        "id": f"evt-report-start-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Reporting Analysis Results",
        "description": "Updating Azure DevOps ticket and sending Teams notification",
        "type": "integration",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Reporting", "variant": "outline"}
        ]
    }
    
    # Initialize reporting results
    events = [start_event]
    notes = []
    ticket_id = None
    ticket_url = None
    dashboard_url = os.environ.get("IIM_DASHBOARD_BASE_URL", "https://obv-ai-compute.ivantiai.com:8080/incident/") + state.incident_id
    success = False
    error_message = None
    
    # Get existing ticket ID from state if available
    existing_ticket_id = state.ado_ticket_id if hasattr(state, "ado_ticket_id") else None
    existing_ticket_url = state.ado_ticket_url if hasattr(state, "ado_ticket_url") else None
    
    # Attempt to report results
    try:
        # Get the services
        ado_service = get_ado_service()
        teams_service = get_teams_service()
        reporting_agent = get_reporting_agent()
        
        # Set up the dependencies for the agent
        deps = ReportingAgentDeps(
            openai_client=openai_client
        )
        
        # Extract entities from state
        entities_list = []
        if state.entities:
            entities_list = [entity.model_dump() if hasattr(entity, "model_dump") else entity for entity in state.entities]
        
        # Extract runbook results from state
        runbook_results_list = []
        if state.runbook_results:
            runbook_results_list = [result.model_dump() if hasattr(result, "model_dump") else result for result in state.runbook_results]
        
        prompt = f"""
        Analyze the incident data and generate reporting content for tickets and notifications.
        
        Here is entire incident state:
        {state.model_dump_json(indent=2)}
        """
        # Generate reporting content using the agent
        logger.info(f"Generating reporting content for incident {state.incident_id}")
        agent_response = await reporting_agent.run(
            prompt,
            deps=deps
        )
        
        # Extract the reporting content
        report_content = agent_response.data
        
        # Handle ticket creation or update
        if existing_ticket_id:
            # Update existing ticket
            logger.info(f"Updating existing ticket {existing_ticket_id} for incident {state.incident_id}")
            
            # Create a custom state with the elements needed for the ticket update
            ticket_state = {
                "incident_id": state.incident_id,
                "title": report_content.ticket_title,
                "severity": state.severity,
                "product": state.product,
                "region": state.region,
                "root_cause": state.root_cause,
                "rca_details": state.rca_details,
                "entities": entities_list,  # Use the pre-converted entities_list
                "runbook_results": runbook_results_list,  # Use the pre-converted runbook_results
                "analysis_summary": state.analysis_summary,
                "start_time": state.start_time
            }
            
            # Update the ticket
            update_result = ado_service.update_ticket_with_results(existing_ticket_id, ticket_state)
            
            if update_result:
                logger.info(f"Successfully updated ticket {existing_ticket_id}")
                ticket_id = existing_ticket_id
                ticket_url = existing_ticket_url
                
                # Create success event
                ticket_event: TimelineEvent = {
                    "id": f"evt-ticket-updated-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Azure DevOps Ticket Updated",
                    "description": f"Updated ticket #{ticket_id} with analysis results",
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Azure DevOps", "variant": "outline"},
                        {"label": "Success", "variant": "success"}
                    ]
                }
                events.append(ticket_event)
                
                # Add ticket note
                ticket_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": f"Updated Azure DevOps ticket #{ticket_id} with analysis results",
                    "data": {
                        "ticket_id": ticket_id,
                        "ticket_url": ticket_url
                    }
                }
                notes.append(ticket_note)
            else:
                logger.error(f"Failed to update ticket {existing_ticket_id}")
                error_message = f"Failed to update ticket {existing_ticket_id}"
                
                # Create error event
                error_event: TimelineEvent = {
                    "id": f"evt-ticket-error-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Azure DevOps Ticket Update Failed",
                    "description": error_message,
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Azure DevOps", "variant": "outline"},
                        {"label": "Error", "variant": "destructive"}
                    ]
                }
                events.append(error_event)
                
                # Add error note
                error_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": f"Failed to update Azure DevOps ticket: {error_message}",
                    "data": {"error": error_message}
                }
                notes.append(error_note)
        else:
            # Create new ticket
            logger.info(f"Creating new ticket for incident {state.incident_id}")
            
            # Create a custom state with the elements needed for ticket creation
            ticket_state = {
                "incident_id": state.incident_id,
                "title": report_content.ticket_title,
                "severity": state.severity,
                "product": state.product,
                "region": state.region,
                "root_cause": state.root_cause,
                "rca_details": state.rca_details,
                "start_time": state.start_time
            }
            
            # Create the ticket
            ticket_result = ado_service.create_initial_ticket(ticket_state)
            
            if ticket_result:
                logger.info(f"Successfully created ticket {ticket_result.get('ticket_id')}")
                ticket_id = ticket_result.get("ticket_id")
                ticket_url = ticket_result.get("ticket_url")
                
                # Create success event
                ticket_event: TimelineEvent = {
                    "id": f"evt-ticket-created-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Azure DevOps Ticket Created",
                    "description": f"Created ticket #{ticket_id}",
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Azure DevOps", "variant": "outline"},
                        {"label": "Success", "variant": "success"}
                    ]
                }
                events.append(ticket_event)
                
                # Add ticket note
                ticket_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": f"Created Azure DevOps ticket #{ticket_id}",
                    "data": {
                        "ticket_id": ticket_id,
                        "ticket_url": ticket_url
                    }
                }
                notes.append(ticket_note)
            else:
                logger.error("Failed to create ticket")
                error_message = "Failed to create ticket"
                
                # Create error event
                error_event: TimelineEvent = {
                    "id": f"evt-ticket-error-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Azure DevOps Ticket Creation Failed",
                    "description": error_message,
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Azure DevOps", "variant": "outline"},
                        {"label": "Error", "variant": "destructive"}
                    ]
                }
                events.append(error_event)
                
                # Add error note
                error_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": f"Failed to create Azure DevOps ticket: {error_message}",
                    "data": {"error": error_message}
                }
                notes.append(error_note)
        
        # Send Teams notification if ticket was created/updated successfully
        if ticket_id:
            logger.info(f"Sending Teams notification for incident {state.incident_id}")
            
            # Create a notification state with ticket information
            notification_state = {
                "incident_id": state.incident_id,
                "title": report_content.notification_title,
                "severity": state.severity,
                "product": state.product,
                "region": state.region,
                "root_cause": state.root_cause,
                "rca_details": state.rca_details,
                "entities": entities_list,  # Use pre-converted entities
                "runbook_results": runbook_results_list,  # Use pre-converted runbook results
                "ado_ticket_id": ticket_id,
                "ado_ticket_url": ticket_url,
                "dashboard_url": dashboard_url,
                "start_time": state.start_time
            }
            
            # Send the notification
            notification_result = teams_service.send_incident_notification(notification_state)
            
            if notification_result:
                logger.info("Successfully sent Teams notification")
                success = True
                
                # Create success event
                teams_event: TimelineEvent = {
                    "id": f"evt-teams-sent-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Teams Notification Sent",
                    "description": "Sent incident analysis results to Teams channel",
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Teams", "variant": "outline"},
                        {"label": "Success", "variant": "success"}
                    ]
                }
                events.append(teams_event)
                
                # Add Teams note
                teams_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": "Sent incident analysis results to Teams channel",
                    "data": {}
                }
                notes.append(teams_note)
            else:
                logger.error("Failed to send Teams notification")
                error_message = "Failed to send Teams notification"
                
                # Create error event
                error_event: TimelineEvent = {
                    "id": f"evt-teams-error-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": "Teams Notification Failed",
                    "description": error_message,
                    "type": "integration",
                    "source": "AI Agent Framework",
                    "tags": [
                        {"label": "Teams", "variant": "outline"},
                        {"label": "Error", "variant": "destructive"}
                    ]
                }
                events.append(error_event)
                
                # Add error note
                error_note: InvestigationNote = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "agent": "reporting_agent",
                    "note": f"Failed to send Teams notification: {error_message}",
                    "data": {"error": error_message}
                }
                notes.append(error_note)
        
        # Set success flag if ticket was successfully created/updated
        success = ticket_id is not None
        
    except Exception as e:
        logger.error(f"Error reporting results: {str(e)}")
        error_message = str(e)
        
        # Create an error event
        error_event: TimelineEvent = {
            "id": f"evt-report-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Results Reporting Error",
            "description": str(e),
            "type": "integration",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        events.append(error_event)
        
        # Add error note
        error_note: InvestigationNote = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "reporting_agent",
            "note": f"Error reporting results: {str(e)}",
            "data": {"error": str(e)}
        }
        notes.append(error_note)
    
    # Return the updated state
    return {
        "ado_ticket_id": ticket_id or existing_ticket_id,
        "ado_ticket_url": ticket_url or existing_ticket_url,
        "dashboard_url": dashboard_url,
        "timeline": events,
        "investigation_notes": notes,
        "current_phase": "results_reported" if success else "reporting_error",
        "reporting_complete": success,
        "error": error_message
    }

@log_node_execution("retrieve_database_context")
async def retrieve_database_context(state: IncidentState) -> Dict:
    """
    Retrieve additional context from PostgreSQL database for incident analysis.
    
    This node queries the database to get additional information related to the incident
    such as deployment history, recent configuration changes, historical incidents,
    and other relevant context that can help with root cause analysis.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Retrieving database context for incident {state.incident_id}")
    
    # Initialize tracking structures
    timeline_events = []
    investigation_notes = []
    
    # Create a timeline event for starting database context retrieval
    start_event = {
        "id": f"evt-start-db-context-{datetime.now(timezone.utc).timestamp():.0f}",
        "incidentId": state.incident_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "title": "Starting Database Context Retrieval",
        "description": f"Retrieving additional context from database for {state.product} incident",
        "type": "investigation",
        "source": "AI Agent Framework",
        "tags": [
            {"label": "Database", "variant": "secondary"}
        ]
    }
    timeline_events.append(start_event)
    
    try:
        # Set up dependencies for the agent
        deps = DatabaseAgentDeps(
            openai_client=openai_client,
            database_client=get_database_client(),
            state=state
        )
        
        # Prepare the prompt for the agent
        product = state.product if state.product else "Unknown"
        entity_name = "Unknown"
        entity_type = "Unknown"
        
        # Get primary entity details if available
        if state.entities:
            primary_entity = next((e for e in state.entities if e.is_primary), state.entities[0])
            entity_name = primary_entity.entity_name
            entity_type = primary_entity.entity_type
        
        # Construct the prompt
        prompt = f"""
        Please retrieve relevant database context for this incident:
        
        Incident ID: {state.incident_id}
        Product: {product}
        Primary Entity: {entity_name} ({entity_type})
        Alert: {state.alert_title}
        Alert Category: {state.alert_category}
        
        Time Window: {state.since_time} to {state.until_time}
        
        I need comprehensive context from the database that might help understand 
        what's happening with this incident. Look for recent deployments, configuration
        changes, similar historical incidents, and any other relevant data.
        
        For Neurons incidents, focus on Kubernetes-related data.
        For MDM incidents, focus on application and dependency data.
        
        Return a structured analysis of the most relevant findings.
        """
        
        # Call the database agent
        result = await database_agent.run(prompt, deps=deps)
        database_context = result.data
        
        # Create timeline events for database context retrieval
        context_event = {
            "id": f"evt-db-context-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Database Context Retrieved",
            "description": database_context.context_summary,
            "type": "analysis",
            "source": "Database Agent",
            "tags": [
                {"label": "Database", "variant": "secondary"},
                {"label": "Context", "variant": "outline"}
            ]
        }
        timeline_events.append(context_event)
        
        # Create investigation notes for each context item
        for item in database_context.context_items:
            note = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "agent": "database_agent",
                "note": f"Database context: {item.query_name} - {item.summary}",
                "data": {
                    "query_name": item.query_name,
                    "query_type": item.query_type,
                    "summary": item.summary,
                    "relevance_score": item.relevance_score,
                    "data_count": item.data_count,
                    "key_findings": item.key_findings
                }
            }
            investigation_notes.append(note)
        
        # Create a summary investigation note
        summary_note = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "database_agent",
            "note": f"Database context summary: {database_context.context_summary}",
            "data": {
                "priority_findings": database_context.priority_findings,
                "entities_examined": database_context.entities_examined,
                "additional_data_recommendations": database_context.additional_data_recommendations,
                "confidence_level": database_context.confidence_level
            }
        }
        investigation_notes.append(summary_note)
        
        # Create timeline events for priority findings
        if database_context.priority_findings:
            for i, finding in enumerate(database_context.priority_findings):
                priority_event = {
                    "id": f"evt-db-finding-{i}-{datetime.now(timezone.utc).timestamp():.0f}",
                    "incidentId": state.incident_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "title": f"Database Finding #{i+1}",
                    "description": finding,
                    "type": "analysis",
                    "source": "Database Agent",
                    "tags": [
                        {"label": "Finding", "variant": "secondary"}
                    ]
                }
                timeline_events.append(priority_event)
        
        # Final completion event
        completion_event = {
            "id": f"evt-db-context-complete-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Database Context Analysis Complete",
            "description": f"Retrieved and analyzed relevant database context with confidence level: {database_context.confidence_level}/10",
            "type": "analysis",
            "source": "Database Agent",
            "tags": [
                {"label": "Complete", "variant": "success"}
            ]
        }
        timeline_events.append(completion_event)
        
        # Return state updates
        return {
            "database_context": database_context.model_dump(),
            "investigation_notes": investigation_notes,
            "timeline": timeline_events,
            "current_phase": "database_context_retrieved"
        }
    
    except Exception as e:
        logger.error(f"Error retrieving database context: {str(e)}")
        error_traceback = traceback.format_exc()
        logger.error(f"Traceback: {error_traceback}")
        
        # Create error event
        error_event = {
            "id": f"evt-db-context-error-{datetime.now(timezone.utc).timestamp():.0f}",
            "incidentId": state.incident_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "title": "Database Context Error",
            "description": f"Error retrieving database context: {str(e)}",
            "type": "error",
            "source": "AI Agent Framework",
            "tags": [
                {"label": "Error", "variant": "destructive"}
            ]
        }
        timeline_events.append(error_event)
        
        # Create error note
        error_note = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent": "database_agent",
            "note": f"Error retrieving database context: {str(e)}",
            "data": {"error": str(e), "traceback": error_traceback}
        }
        investigation_notes.append(error_note)
        
        return {
            "error": str(e),
            "investigation_notes": investigation_notes,
            "timeline": timeline_events,
            "current_phase": "database_context_error"
        }

def create_test_workflow() -> StateGraph:
    """Create a simplified test workflow for incremental testing."""
    
    # Define the workflow nodes
    nodes = {
        "analyze_alert": analyze_alert,
        "analyze_entity": analyze_entity,
        "analyze_entity_relationships": analyze_entity_relationships,
        "execute_runbooks": execute_runbooks,
        "retrieve_database_context": retrieve_database_context,
        "analyze_root_cause": analyze_root_cause,
        "should_continue": should_continue,
        "generate_topology": generate_topology,
        "report_results": report_results,
        "create_ticket": create_ticket,
        "finish": finish
    }
    
    # Create the workflow graph
    workflow = StateGraph(IncidentState)
    
    # Add all nodes to the graph
    for name, node in nodes.items():
        workflow.add_node(name, node)
    
    # Define the edges
    # Simplified flow for incremental testing
    workflow.add_edge(START, "analyze_alert")
    workflow.add_edge("analyze_alert", "analyze_entity")
    workflow.add_edge("analyze_entity", "analyze_entity_relationships")
    workflow.add_edge("analyze_entity_relationships", "execute_runbooks")
    workflow.add_edge("execute_runbooks", "retrieve_database_context")
    workflow.add_edge("retrieve_database_context", "analyze_root_cause")
    workflow.add_edge("analyze_root_cause", "should_continue")
    
    # Optional branch based on should_continue decision
    workflow.add_conditional_edges(
        "should_continue",
        lambda x: "analyze_topology" if x.get("should_continue", False) else "report_results",
        {
            "analyze_topology": "generate_topology",
            "report_results": "report_results"
        }
    )
    
    workflow.add_edge("generate_topology", "report_results")
    workflow.add_edge("report_results", "create_ticket")
    workflow.add_edge("create_ticket", "finish")
    workflow.add_edge("finish", END)
    
    # Compile the workflow
    workflow.compile()
    
    return workflow

async def test_workflow():
    """Test the workflow execution."""
    # Create and compile the workflow
    workflow = create_test_workflow()
    compiled_workflow = workflow.compile()
    
    # Set up MongoDB connection
    mongodb_available = False
    try:
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Test connection by checking if the incidents collection is initialized
        if mongodb_service._incidents_collection is not None:
            mongodb_available = True
            logger.info("MongoDB service initialized for testing")
        else:
            logger.warning("MongoDB service was created but collections are not initialized. Check connection.")
            
    except Exception as e:
        mongodb_error = str(e)
        logger.error(f"Could not initialize MongoDB service: {mongodb_error}")
        
        # Check if this is a connection string formatting error
        if "Username and password must be escaped" in mongodb_error:
            logger.error("The MongoDB connection string contains special characters in username/password that need URL encoding.")
            logger.error("Solution: Update your .env file with properly escaped username and password.")
            logger.error("Example: If your password is 'p@ssw0rd', it should be encoded as 'p%40ssw0rd'")
        
        logger.warning("The workflow will run without MongoDB persistence.")
    
    if not mongodb_available:
        logger.warning("MongoDB storage is disabled. Tool results, metrics, logs, and events will not be persisted.")
        logger.warning("To enable MongoDB storage, ensure the MONGODB_CONNECTION_STRING environment variable is set correctly.")
    
    # Test data for a Kubernetes deployment unavailable pods alert
    test_data = {
        "issueId": "9a0625bd-fc0e-48cd-b1b5-7065e2127bee",
        "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/9a0625bd-fc0e-48cd-b1b5-7065e2127bee?notifier=WEBHOOK",
        "title": "Percentage of Unavailable pods > 25% for cluster ivmdm-uk1-uks-aks-instance, deployment connector-service",
        "priority": "CRITICAL",
        "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtNzcyOTU1MzAxNDQ5ODQ0MDQ1MA"],
        "impactedEntities": ["connector-service"],
        "totalIncidents": "1",
        "state": "ACTIVATED",
        "trigger": "STATE_CHANGE",
        "isCorrelated": "false",
        "createdAt": *************,
        "updatedAt": *************,
        "sources": ["newrelic"],
        "alertPolicyNames": ["SRE - MI Azure Alert - Infrastructure"],
        "alertConditionNames": ["Percentage of Unavailable pods > 25%"],
        "workflowName": "obv-ai-processing",
        "chartLink": "Not Available",
        "product": "MDM",
        "nr_region": "us"
    }
    
    # Initialize proper state structure for the workflow
    incident_id = test_data.get("issueId")
    initial_state: IncidentState = {
        "incident_id": incident_id,
        "raw_alert": test_data,
        "title": test_data.get("title", "Unknown"),
        "description": f"Alert from {test_data.get('alertPolicyNames', ['Unknown'])[0]}", 
        "severity": test_data.get("priority", "UNKNOWN"),
        "start_time": datetime.now(timezone.utc).isoformat(),
        "current_phase": "initialize",
        "investigation_notes": [],
        "timeline": [],  # Initialize empty timeline events list
        "primary_entity_guid": test_data.get("EntityId", [""])[0],
        "primary_entity_type": None,
        "entity_details": [],
        "related_entity_details": [],
        "metrics": [],
        "logs": [],
        "system_checks": [],
        "root_cause": None,
        "remediation_actions": [],
        "alert_summary": "",
        # "output_incident": None,
        "alert_category": "", # Will be filled by alert_parser
        "alert_runbook": "",  # Will be filled by alert_parser
        "entities": [],       # Will be filled by alert_parser
        "alert_title": test_data.get("title", "Unknown"),  # Required field
        "condition_name": test_data.get("alertConditionNames", ["Unknown"])[0],  # Required field
        "run_id": f"run-{datetime.now(timezone.utc).timestamp():.0f}"  # Add unique run_id for tracking
    }
    
    # Execute the workflow with the test data
    logger.info(f"Starting workflow for incident {incident_id}")
    logger.info(f"Initial phase: {initial_state['current_phase']}")
    
    try:
        # Use astream_events instead of ainvoke for newer LangGraph versions
        async for event in compiled_workflow.astream_events(initial_state):
            # You can process events here if needed
            if event.get("type") == "end":
                result = event.get("data", {})
                result_dict = result.model_dump()
                
                # Print key results
                logger.info("-" * 60)
                logger.info("WORKFLOW EXECUTION RESULTS")
                logger.info("-" * 60)
                
                if "error" in result:
                    logger.error(f"Workflow error: {result['error']}")
                else:
                    logger.info(f"Final phase: {result.get('current_phase', 'unknown')}")
                    logger.info(f"Alert Category: {result.get('alert_category', 'Unknown')}")
                    logger.info(f"Condition Name: {result.get('condition_name', 'Unknown')}")
                    logger.info(f"Policy Name: {result.get('policy_name', 'Unknown')}")
                    logger.info(f"Cluster Name: {result.get('cluster_name', 'Unknown')}")
                    
                    # Print entities
                    entities = result.get('entities', [])
                    if entities:
                        logger.info(f"\nEntities ({len(entities)}):")
                        for entity in entities:
                            logger.info(f"  - {entity.get('entity_guid', 'Unknown')}")
                    
                    # Print investigation notes
                    notes = result.get('investigation_notes', [])
                    if notes:
                        logger.info(f"\nInvestigation Notes ({len(notes)}):")
                        for note in notes:
                            logger.info(f"  - [{note.get('agent', 'Unknown')}] {note.get('note', 'No content')}")
    
    except Exception as e:
        logger.error(f"Error executing workflow: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(test_workflow())
