"""
Utilities for serializing complex objects to JSON-compatible formats.
"""

import uuid
from datetime import datetime
from typing import Any, Dict


def ensure_serializable(obj):
    """
    Ensure all values in a nested structure are JSON serializable.
    Converts ObjectId, datetime objects and other non-serializable types to strings.
    
    Args:
        obj: Any Python object that might contain non-serializable values
        
    Returns:
        A JSON-serializable version of the object
    """
    if obj is None:
        return None
        
    # Handle MongoDB ObjectId
    if hasattr(obj, "__str__") and str(type(obj)).find("bson.objectid.ObjectId") > -1:
        return str(obj)
        
    # Handle datetime objects
    if isinstance(obj, datetime):
        return obj.isoformat()
        
    # Handle lists
    if isinstance(obj, list):
        return [ensure_serializable(item) for item in obj]
        
    # Handle dictionaries
    if isinstance(obj, dict):
        return {k: ensure_serializable(v) for k, v in obj.items()}
        
    # Handle Pydantic models or anything with model_dump() method
    if hasattr(obj, "model_dump"):
        return ensure_serializable(obj.model_dump())
        
    # Handle older Pydantic models
    if hasattr(obj, "dict"):
        return ensure_serializable(obj.dict())
    
    # Return basic types as is
    return obj


def generate_reference_id(prefix: str = "ref") -> str:
    """Generate a unique reference ID for collected information."""
    return f"{prefix}-{uuid.uuid4()}" 