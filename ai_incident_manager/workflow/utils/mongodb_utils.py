"""
MongoDB utilities for workflow data storage and retrieval.
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional
from loguru import logger


async def process_collected_information(
    collected_information: Dict[str, Any],
    incident_id: str,
    entity_guid: str,
    content_type: str
) -> Dict[str, Any]:
    """
    Process collected information and store in MongoDB.
    
    Args:
        collected_information: Information collected by a tool
        incident_id: ID of the incident
        entity_guid: GUID of the entity related to the information
        content_type: Type of content ('metric', 'log', 'information')
        
    Returns:
        Dictionary with reference ID and content summary
    """
    try:
        # Import mongodb service
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        from .serialization import generate_reference_id
        
        mongodb_service = get_mongodb_service()
        
        # Generate a reference ID
        reference_id = generate_reference_id()
        
        # Create a content summary based on the content type
        if content_type == "metric":
            # Store in metrics collection
            metric_name = collected_information.get("name", "unknown_metric")
            metric_data = collected_information.get("data", {})
            
            db_id = await mongodb_service.store_metrics(
                incident_id=incident_id,
                entity_guid=entity_guid,
                metric_name=metric_name,
                metric_data=metric_data
            )
            
            # Create a summary
            metric_value = collected_information.get("value", "N/A")
            summary = f"Metric '{metric_name}' collected with value: {metric_value}"
            
        elif content_type == "log":
            # Store in logs collection
            log_data = collected_information.get("data", {})
            
            db_id = await mongodb_service.store_logs(
                incident_id=incident_id,
                entity_guid=entity_guid,
                log_data=log_data
            )
            
            # Create a summary
            log_count = len(log_data.get("logs", []))
            summary = f"Collected {log_count} log entries"
            
        else:  # information
            # Store in events collection
            event_data = collected_information
            
            db_id = await mongodb_service.store_events(
                incident_id=incident_id,
                entity_guid=entity_guid,
                event_data=event_data
            )
            
            # Create a summary
            event_type = event_data.get("type", "information")
            summary = f"Information of type '{event_type}' collected"
        
        # Return the reference information
        return {
            "reference_id": reference_id,
            "db_id": db_id,
            "content_type": content_type,
            "content_summary": summary,
            "entity_guid": entity_guid
        }
    
    except Exception as e:
        logger.error(f"Error processing collected information: {str(e)}")
        from .serialization import generate_reference_id
        return {
            "reference_id": generate_reference_id("error"),
            "content_type": content_type,
            "content_summary": f"Error storing information: {str(e)}",
            "entity_guid": entity_guid,
            "error": str(e)
        }


async def retrieve_tool_result(result_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve a tool result from MongoDB by its ID.
    
    Args:
        result_id: ID of the tool result to retrieve
        
    Returns:
        The tool result or None if not found
    """
    try:
        # Import mongodb service
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Retrieve the tool result
        return await mongodb_service.get_tool_result_by_id(result_id)
    
    except Exception as e:
        logger.error(f"Error retrieving tool result: {str(e)}")
        return None 


async def store_workflow_result(
    incident_id: str,
    node_name: str,
    result: Dict[str, Any]
) -> Optional[str]:
    """
    Store workflow node result in MongoDB.
    
    Args:
        incident_id: ID of the incident
        node_name: Name of the workflow node
        result: Result data to store
        
    Returns:
        Storage ID or None if error
    """
    try:
        # Import mongodb service
        from ai_incident_manager.services.mongodb_service import get_mongodb_service
        mongodb_service = get_mongodb_service()
        
        # Create event data for the workflow result
        event_data = {
            "node_name": node_name,
            "result": result,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "type": "workflow_result"
        }
        
        # Store as event
        db_id = await mongodb_service.store_events(
            incident_id=incident_id,
            entity_guid="workflow",
            event_data=event_data
        )
        
        logger.info(f"Stored workflow result for {node_name} in incident {incident_id}")
        return db_id
    
    except Exception as e:
        logger.error(f"Error storing workflow result: {str(e)}")
        return None