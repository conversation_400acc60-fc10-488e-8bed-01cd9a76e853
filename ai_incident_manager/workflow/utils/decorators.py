"""
Decorators for workflow node execution logging and monitoring.
"""

import functools
import traceback
from datetime import datetime, timezone
from loguru import logger
from lib.new_relic.logger import format_node_start, format_node_end, format_node_error


def log_node_execution(node_name):
    """
    Decorator to log node entry and exit with structured logging.
    
    Args:
        node_name: Name of the workflow node for logging purposes
        
    Returns:
        Decorated function with logging capabilities
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            state = args[0] if args else kwargs.get('state')
            
            # Handle different state types (dict or Pydantic model)
            if state:
                if hasattr(state, 'incident_id'):
                    incident_id = state.incident_id
                elif isinstance(state, dict) and 'incident_id' in state:
                    incident_id = state['incident_id']
                else:
                    incident_id = 'unknown'
            else:
                incident_id = 'unknown'
            
            # Create a contextual logger with node and incident information
            node_logger = logger.bind(node=node_name, incident_id=incident_id)
            
            # Log node start with the new formatting
            node_logger.info(format_node_start(node_name, incident_id))
            
            start_time = datetime.now(timezone.utc)
            try:
                result = await func(*args, **kwargs)
                
                # Calculate duration
                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()
                
                # Log node end with the new formatting
                node_logger.success(format_node_end(node_name, incident_id, duration))
                return result
            except Exception as e:
                # Calculate duration even on error
                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()
                
                # Log error with special formatting
                error_msg = str(e)
                node_logger.error(format_node_error(node_name, incident_id, error_msg, duration))
                
                # Log the traceback for debugging
                node_logger.error(f"Traceback:\n{''.join(traceback.format_tb(e.__traceback__))}")
                raise
                
        return wrapper
    return decorator 