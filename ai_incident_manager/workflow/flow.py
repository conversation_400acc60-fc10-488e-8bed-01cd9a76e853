"""
Incident analysis workflow using LangGraph with Pydantic AI agents.

This module defines the incident analysis workflow using LangGraph for orchestration
and Pydantic AI agents for specialized tasks.
"""

import os
import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, TypedDict, Tuple, Union, Annotated
from datetime import datetime, timedelta

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
import dotenv

from ai_incident_manager.models.workflow_state import (
    IncidentState, EntityDetails, MetricData, SystemCheckResult, 
    RemediationAction, InvestigationNote
)
from ai_incident_manager.services.metrics_collector import MetricsCollector
from ai_incident_manager.agents.incident_agents import (
    alert_analyzer, AlertAnalyzerDeps,
    metrics_analyzer, MetricsAnalyzerDeps,
    logs_analyzer, LogsAnalyzerDeps,
    system_checks_agent, SystemChecksDeps,
    root_cause_analyzer, RootCauseAnalyzerDeps,
    remediation_advisor, RemediationAdvisorDeps
)
from ai_incident_manager.workflow.nodes.graph_analysis import graph_analysis_node, graph_enrichment_node

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize metrics collector
metrics_collector = MetricsCollector()

# Node functions for the workflow
async def analyze_alert(state: IncidentState) -> Dict:
    """
    Analyze the alert to understand the incident context using a Pydantic AI agent.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Analyzing alert for incident {state['incident_id']}")
    
    # Extract alert information
    alert = state["raw_alert"]
    
    # Prepare the prompt for the alert analyzer
    user_prompt = f"""
    Please analyze the following alert to understand the incident context:
    
    Alert ID: {alert.get('id', 'Unknown')}
    Type: {alert.get('type', 'Unknown')}
    Title: {state['output']['title']}
    Description: {state['output']['summary']}
    Severity: {state['output']['severity']}
    Entity Type: {state['entities'][0]['type'] if state['entities'] else 'Unknown'}
    Entity Name: {state['entities'][0]['name'] if state['entities'] else 'Unknown'}
    
    I need to understand:
    1. The likely scope and impact of this issue
    2. What entities are involved
    3. What metrics should be investigated
    4. Initial hypotheses about the cause
    """
    
    # Prepare dependencies for the agent
    deps = AlertAnalyzerDeps(
        metrics_collector=metrics_collector,
        incident_state=state
    )
    
    investigation_state = state["investigation_state"].copy()
    
    # Run the agent
    try:
        result = await alert_analyzer.run(user_prompt, deps=deps)
        analysis_result = result.data
        
        # Add an investigation note
        note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Alert Analysis",
            "content": analysis_result,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        investigation_state["notes"].append(note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["collect_metrics"]
        
    except Exception as e:
        logger.error(f"Error analyzing alert: {str(e)}")
        # Add an error note
        error_note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Alert Analysis Error",
            "content": f"Error occurred during alert analysis: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }
        investigation_state["notes"].append(error_note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["collect_metrics"]  # Continue with next step despite error
    
    # Return only the updated fields, not the entire state
    return {
        "investigation_state": investigation_state
    }


async def collect_metrics(state: IncidentState) -> Dict:
    """
    Collect metrics for the entities involved in the incident.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Collecting metrics for incident {state['incident_id']}")
    
    # Check if we have entities to collect metrics for
    if not state["entities"]:
        logger.warning("No entities found for metrics collection")
        
        # Add a note about missing entities
        note: InvestigationNote = {
            "step": state["investigation_state"]["current_step"] + 1,
            "title": "Metrics Collection",
            "content": "No entities found to collect metrics for. Skipping metrics collection.",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        state["investigation_state"]["notes"].append(note)
        state["investigation_state"]["current_step"] += 1
        state["investigation_state"]["next_steps"] = ["analyze_metrics"]  # Continue to next step
        
        return {
            "investigation_state": state["investigation_state"]
        }
    
    # Collect metrics for each entity
    for entity in state["entities"]:
        entity_guid = entity["id"]
        entity_type = entity["type"]
        entity_name = entity["name"]
        
        if not entity_guid or not entity_type:
            logger.warning(f"Missing entity_guid or entity_type for {entity_name}. Skipping.")
            continue
        
        try:
            # Prepare dependencies for the agent
            deps = MetricsAnalyzerDeps(
                metrics_collector=metrics_collector,
                incident_state=state
            )
            
            # Use the metrics_analyzer agent's tool to collect metrics
            metrics_data = await metrics_analyzer.deps(deps).execute_tool(
                "collect_entity_metrics",
                {
                    "entity_guid": entity_guid,
                    "entity_type": entity_type
                }
            )
            
            # Format the metrics data for the state
            for metric_name, metric_data in metrics_data.items():
                # Create a standardized metric entry
                metric_entry: MetricData = {
                    "name": metric_name,
                    "entity_id": entity_guid,
                    "data": [],
                    "unit": metric_data.get("unit", ""),
                    "threshold": metric_data.get("threshold", None)
                }
                
                # Add data points
                for datapoint in metric_data.get("data", []):
                    metric_entry["data"].append({
                        "timestamp": datapoint.get("timestamp", datetime.utcnow().isoformat()),
                        "value": datapoint.get("value", 0.0)
                    })
                
                # Add to state metrics
                state["metrics"].append(metric_entry)
            
        except Exception as e:
            logger.error(f"Error collecting metrics for entity {entity_name} ({entity_guid}): {str(e)}")
    
    # Add an investigation note
    note: InvestigationNote = {
        "step": state["investigation_state"]["current_step"] + 1,
        "title": "Metrics Collection",
        "content": f"Collected {len(state['metrics'])} metrics for {len(state['entities'])} entities.",
        "timestamp": datetime.utcnow().isoformat()
    }
    
    # Update the state
    state["investigation_state"]["notes"].append(note)
    state["investigation_state"]["current_step"] += 1
    state["investigation_state"]["next_steps"] = ["analyze_metrics"]
    
    return {
        "investigation_state": state["investigation_state"]
    }


async def analyze_metrics(state: IncidentState) -> Dict:
    """
    Analyze collected metrics using a Pydantic AI agent.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Analyzing metrics for incident {state['incident_id']}")
    
    # Check if we have metrics to analyze
    if not state["metrics"]:
        logger.warning("No metrics found for analysis")
        
        # Add a note about missing metrics
        note: InvestigationNote = {
            "step": state["investigation_state"]["current_step"] + 1,
            "title": "Metrics Analysis",
            "content": "No metrics found to analyze. Skipping metrics analysis.",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        state["investigation_state"]["notes"].append(note)
        state["investigation_state"]["current_step"] += 1
        state["investigation_state"]["next_steps"] = ["collect_logs"]  # Continue to next step
        
        return {
            "investigation_state": state["investigation_state"]
        }
    
    # Format metrics for the prompt
    metrics_text = ""
    for metric in state["metrics"]:
        metrics_text += f"\nMetric: {metric['name']} (Entity: {metric['entity_id']})\n"
        
        for datapoint in metric["data"]:
            metrics_text += f"  {datapoint['timestamp']}: {datapoint['value']}"
            
            if "threshold" in metric and metric["threshold"] is not None:
                if datapoint['value'] > metric['threshold']:
                    metrics_text += f" (EXCEEDED THRESHOLD: {metric['threshold']})"
            
            metrics_text += "\n"
    
    # Prepare the prompt for the metrics analyzer
    user_prompt = f"""
    Please analyze the following metrics for incident: {state['incident_id']}
    
    Entity: {state['entities'][0]['name'] if state['entities'] else 'Unknown'} (Type: {state['entities'][0]['type'] if state['entities'] else 'Unknown'})
    
    {metrics_text}
    
    I need to understand:
    1. What patterns or anomalies are visible in these metrics
    2. Potential causes of the observed anomalies
    3. Recommendations for further investigation
    """
    
    # Prepare dependencies for the agent
    deps = MetricsAnalyzerDeps(
        metrics_collector=metrics_collector,
        incident_state=state
    )
    
    investigation_state = state["investigation_state"].copy()
    
    # Run the agent
    try:
        result = await metrics_analyzer.run(user_prompt, deps=deps)
        analysis_result = result.data
        
        # Add an investigation note
        note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Metrics Analysis",
            "content": analysis_result,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        investigation_state["notes"].append(note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["collect_logs"]
        
    except Exception as e:
        logger.error(f"Error analyzing metrics: {str(e)}")
        # Add an error note
        error_note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Metrics Analysis Error",
            "content": f"Error occurred during metrics analysis: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }
        investigation_state["notes"].append(error_note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["collect_logs"]  # Continue with next step despite error
    
    # Return only the updated fields, not the entire state
    return {
        "investigation_state": investigation_state
    }


async def collect_logs(state: IncidentState) -> Dict:
    """
    Collect logs for the entities involved in the incident.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Collecting logs for incident {state['incident_id']}")
    
    # Check if we have entities to collect logs for
    if not state["entities"]:
        logger.warning("No entities found for logs collection")
        
        # Add a note about missing entities
        note: InvestigationNote = {
            "step": state["investigation_state"]["current_step"] + 1,
            "title": "Logs Collection",
            "content": "No entities found to collect logs for. Skipping logs collection.",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        state["investigation_state"]["notes"].append(note)
        state["investigation_state"]["current_step"] += 1
        state["investigation_state"]["next_steps"] = ["analyze_logs"]  # Continue to next step
        
        return {
            "investigation_state": state["investigation_state"]
        }
    
    # Collect logs for each entity
    for entity in state["entities"]:
        entity_guid = entity["id"]
        entity_type = entity["type"]
        entity_name = entity["name"]
        
        if not entity_guid or not entity_type or not entity_name:
            logger.warning(f"Missing entity information. Skipping logs collection.")
            continue
        
        try:
            # Prepare dependencies for the agent
            deps = LogsAnalyzerDeps(
                metrics_collector=metrics_collector,
                incident_state=state
            )
            
            # Use the logs_analyzer agent's tool to collect logs
            logs_data = await logs_analyzer.deps(deps).execute_tool(
                "collect_entity_logs",
                {
                    "entity_guid": entity_guid,
                    "entity_name": entity_name,
                    "entity_type": entity_type,
                    "limit": 100
                }
            )
            
            # Add logs to state
            for log_entry in logs_data:
                state["logs"].append({
                    "timestamp": log_entry.get("timestamp", datetime.utcnow().isoformat()),
                    "level": log_entry.get("level", "INFO"),
                    "source": log_entry.get("source", entity_name),
                    "message": log_entry.get("message", "")
                })
            
            # If this is a Kubernetes entity, also collect Kubernetes events
            if entity_type in ["KUBERNETES_POD", "KUBERNETES_NODE", "K8S_POD", "K8S_NODE"]:
                k8s_events = await logs_analyzer.deps(deps).execute_tool(
                    "collect_kubernetes_events",
                    {
                        "entity_name": entity_name,
                        "entity_type": entity_type
                    }
                )
                
                # Add Kubernetes events as logs
                for event in k8s_events:
                    state["logs"].append({
                        "timestamp": event.get("timestamp", datetime.utcnow().isoformat()),
                        "level": "K8S_EVENT",
                        "source": f"{event.get('involvedObject', {}).get('kind', 'Kubernetes')}/{entity_name}",
                        "message": event.get("message", "")
                    })
            
        except Exception as e:
            logger.error(f"Error collecting logs for entity {entity_name} ({entity_guid}): {str(e)}")
    
    # Add an investigation note
    note: InvestigationNote = {
        "step": state["investigation_state"]["current_step"] + 1,
        "title": "Logs Collection",
        "content": f"Collected {len(state['logs'])} log entries for the affected entities.",
        "timestamp": datetime.utcnow().isoformat()
    }
    
    # Update the state
    state["investigation_state"]["notes"].append(note)
    state["investigation_state"]["current_step"] += 1
    state["investigation_state"]["next_steps"] = ["analyze_logs"]
    
    return {
        "investigation_state": state["investigation_state"]
    }


async def analyze_logs(state: IncidentState) -> Dict:
    """
    Analyze collected logs using a Pydantic AI agent.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Analyzing logs for incident {state['incident_id']}")
    
    # Check if we have logs to analyze
    if not state["logs"]:
        logger.warning("No logs found for analysis")
        
        # Add a note about missing logs
        note: InvestigationNote = {
            "step": state["investigation_state"]["current_step"] + 1,
            "title": "Logs Analysis",
            "content": "No logs found to analyze. Skipping logs analysis.",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        state["investigation_state"]["notes"].append(note)
        state["investigation_state"]["current_step"] += 1
        state["investigation_state"]["next_steps"] = ["graph_analysis"]  # Continue to graph analysis after logs
        
        return {
            "investigation_state": state["investigation_state"]
        }
    
    # Format logs for the prompt
    logs_text = ""
    # Sort logs by timestamp
    sorted_logs = sorted(state["logs"], key=lambda x: x.get("timestamp", ""))
    # Get up to 20 most recent logs for analysis
    recent_logs = sorted_logs[-20:]
    
    for log in recent_logs:
        logs_text += f"\n[{log['timestamp']}] [{log['level']}] {log['source']}: {log['message']}\n"
    
    # Prepare the prompt for the logs analyzer
    user_prompt = f"""
    Please analyze the following logs for incident: {state['incident_id']}
    
    Entity: {state['entities'][0]['name'] if state['entities'] else 'Unknown'} (Type: {state['entities'][0]['type'] if state['entities'] else 'Unknown'})
    
    {logs_text}
    
    I need to understand:
    1. What patterns or issues are evident in these logs
    2. Any error messages or warnings that might indicate the root cause
    3. A timeline of events leading to the incident
    """
    
    # Prepare dependencies for the agent
    deps = LogsAnalyzerDeps(
        metrics_collector=metrics_collector,
        incident_state=state
    )
    
    investigation_state = state["investigation_state"].copy()
    
    # Run the agent
    try:
        result = await logs_analyzer.run(user_prompt, deps=deps)
        analysis_result = result.data
        
        # Add an investigation note
        note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Logs Analysis",
            "content": analysis_result,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        investigation_state["notes"].append(note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["graph_analysis"]
        
    except Exception as e:
        logger.error(f"Error analyzing logs: {str(e)}")
        # Add an error note
        error_note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Logs Analysis Error",
            "content": f"Error occurred during logs analysis: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }
        investigation_state["notes"].append(error_note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["graph_analysis"]  # Continue with graph analysis despite error
    
    # Return only the updated fields, not the entire state
    return {
        "investigation_state": investigation_state
    }


async def determine_system_checks(state: IncidentState) -> Dict:
    """
    Determine what system checks should be performed using a Pydantic AI agent.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Determining system checks for incident {state['incident_id']}")
    
    # Compile context from previous steps
    context = f"""
    Incident ID: {state['incident_id']}
    Title: {state['output']['title']}
    Summary: {state['output']['summary']}
    Entity: {state['entities'][0]['name'] if state['entities'] else 'Unknown'} (Type: {state['entities'][0]['type'] if state['entities'] else 'Unknown'})
    
    Investigation notes so far:
    """
    
    for note in state["investigation_state"]["notes"]:
        context += f"\n--- {note['title']} ---\n{note['content'][:300]}...\n"
    
    # User prompt
    user_prompt = f"""
    {context}
    
    Based on the above information, what system checks should be performed to investigate this incident?
    Please provide a comprehensive list of checks that will help diagnose the root cause.
    
    For each check, specify:
    1. A descriptive name
    2. Why it's relevant to this incident
    3. What should be checked
    4. Criteria for success/failure
    """
    
    # Prepare dependencies for the agent
    deps = SystemChecksDeps(
        metrics_collector=metrics_collector,
        incident_state=state
    )
    
    investigation_state = state["investigation_state"].copy()
    
    # Run the agent
    try:
        result = await system_checks_agent.run(user_prompt, deps=deps)
        analysis_result = result.data
        
        # Add an investigation note
        note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "System Check Planning",
            "content": analysis_result,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        investigation_state["notes"].append(note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["run_system_checks"]
        
    except Exception as e:
        logger.error(f"Error determining system checks: {str(e)}")
        # Add an error note
        error_note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "System Check Planning Error",
            "content": f"Error occurred during system check planning: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }
        investigation_state["notes"].append(error_note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["run_system_checks"]  # Continue with next step despite error
    
    # Return only the updated fields, not the entire state
    return {
        "investigation_state": investigation_state
    }


async def run_system_checks(state: IncidentState) -> Dict:
    """
    Run system checks to diagnose the incident using real metrics and logs.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Running system checks for incident {state['incident_id']}")
    
    # Check if we have entities to run checks on
    if not state["entities"]:
        logger.warning("No entities found for system checks")
        
        # Add a note about missing entities
        note: InvestigationNote = {
            "step": state["investigation_state"]["current_step"] + 1,
            "title": "System Checks",
            "content": "No entities found to run system checks on. Skipping system checks.",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        state["investigation_state"]["notes"].append(note)
        state["investigation_state"]["current_step"] += 1
        state["investigation_state"]["next_steps"] = ["determine_root_cause"]  # Continue to next step
        
        return {
            "investigation_state": state["investigation_state"]
        }
    
    # Run system checks for each entity
    for entity in state["entities"]:
        entity_guid = entity["id"]
        entity_type = entity["type"]
        entity_name = entity["name"]
        
        if not entity_guid or not entity_type:
            logger.warning(f"Missing entity_guid or entity_type for {entity_name}. Skipping system checks.")
            continue
        
        try:
            # Prepare dependencies for the agent
            deps = SystemChecksDeps(
                metrics_collector=metrics_collector,
                incident_state=state
            )
            
            # Use the system_checks_agent's tool to analyze the entity
            entity_analysis = await system_checks_agent.deps(deps).execute_tool(
                "analyze_entity",
                {
                    "entity_guid": entity_guid
                }
            )
            
            # Parse the analysis results to create system checks
            # Resource usage check
            if "metrics" in entity_analysis:
                metrics = entity_analysis["metrics"]
                resource_details = []
                
                # Check for CPU issues
                if "cpu" in metrics and metrics["cpu"]["data"]:
                    cpu_data = metrics["cpu"]["data"][0]
                    cpu_value = cpu_data.get("value", 0)
                    cpu_threshold = metrics["cpu"].get("threshold", 80)
                    
                    if cpu_value > cpu_threshold:
                        resource_details.append(f"CPU usage exceeded threshold: {cpu_value}% > {cpu_threshold}%")
                    else:
                        resource_details.append(f"CPU usage normal: {cpu_value}% < {cpu_threshold}%")
                
                # Check for memory issues
                if "memory" in metrics and metrics["memory"]["data"]:
                    memory_data = metrics["memory"]["data"][0]
                    memory_value = memory_data.get("value", 0)
                    memory_threshold = metrics["memory"].get("threshold", 80)
                    
                    if memory_value > memory_threshold:
                        resource_details.append(f"Memory usage exceeded threshold: {memory_value}% > {memory_threshold}%")
                    else:
                        resource_details.append(f"Memory usage normal: {memory_value}% < {memory_threshold}%")
                
                # Check for disk issues
                if "disk" in metrics and metrics["disk"]["data"]:
                    disk_data = metrics["disk"]["data"][0]
                    disk_value = disk_data.get("value", 0)
                    disk_threshold = metrics["disk"].get("threshold", 80)
                    
                    if disk_value > disk_threshold:
                        resource_details.append(f"Disk usage exceeded threshold: {disk_value}% > {disk_threshold}%")
                    else:
                        resource_details.append(f"Disk usage normal: {disk_value}% < {disk_threshold}%")
                
                # Add resource usage check
                if resource_details:
                    state["system_checks"].append({
                        "name": f"{entity_type} Resource Usage",
                        "status": "FAILED" if any("exceeded" in detail for detail in resource_details) else "PASSED",
                        "description": f"Check {entity_type.lower()} resource usage",
                        "details": resource_details
                    })
            
            # Event check (for Kubernetes entities)
            if "events" in entity_analysis and entity_analysis["events"]:
                event_details = []
                
                for event in entity_analysis["events"][:5]:  # Limit to 5 most recent events
                    event_message = event.get("message", "")
                    event_reason = event.get("reason", "")
                    event_details.append(f"{event_reason}: {event_message}")
                
                # Add events check
                state["system_checks"].append({
                    "name": f"{entity_type} Events",
                    "status": "FAILED" if any(("error" in detail.lower() or "fail" in detail.lower() or "evict" in detail.lower()) for detail in event_details) else "PASSED",
                    "description": f"Check {entity_type.lower()} events",
                    "details": event_details
                })
            
            # Log check
            if "logs" in entity_analysis and entity_analysis["logs"]:
                log_details = []
                
                # Count errors and warnings
                error_count = sum(1 for log in entity_analysis["logs"] if log.get("level", "").upper() == "ERROR")
                warning_count = sum(1 for log in entity_analysis["logs"] if log.get("level", "").upper() == "WARN")
                
                if error_count > 0:
                    log_details.append(f"Found {error_count} ERROR level log entries")
                if warning_count > 0:
                    log_details.append(f"Found {warning_count} WARNING level log entries")
                
                # Sample critical log messages
                critical_logs = [log for log in entity_analysis["logs"] if log.get("level", "").upper() == "ERROR"]
                for log in critical_logs[:3]:  # Limit to 3 error logs
                    log_details.append(f"Error log: {log.get('message', '')}")
                
                # Add logs check
                if log_details:
                    state["system_checks"].append({
                        "name": f"{entity_type} Logs",
                        "status": "FAILED" if error_count > 0 else "WARNING" if warning_count > 0 else "PASSED",
                        "description": f"Check {entity_type.lower()} logs for errors and warnings",
                        "details": log_details
                    })
            
        except Exception as e:
            logger.error(f"Error performing system checks for entity {entity_name} ({entity_guid}): {str(e)}")
            # Add a basic error check
            state["system_checks"].append({
                "name": f"{entity_type} Analysis",
                "status": "ERROR",
                "description": f"Error analyzing {entity_type.lower()}",
                "details": [f"Error: {str(e)}"]
            })
    
    # Add an investigation note
    note: InvestigationNote = {
        "step": state["investigation_state"]["current_step"] + 1,
        "title": "System Checks Execution",
        "content": f"Executed {len(state['system_checks'])} system checks. {sum(1 for c in state['system_checks'] if c['status'] == 'FAILED')} checks failed.",
        "timestamp": datetime.utcnow().isoformat()
    }
    
    # Update the state
    state["investigation_state"]["notes"].append(note)
    state["investigation_state"]["current_step"] += 1
    state["investigation_state"]["next_steps"] = ["determine_root_cause"]
    
    return {
        "investigation_state": state["investigation_state"]
    }


async def determine_root_cause(state: IncidentState) -> Dict:
    """
    Determine the root cause of the incident using a Pydantic AI agent.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Determining root cause for incident {state['incident_id']}")
    
    # Compile context from all investigation steps
    metrics_text = "\n".join([f"- {m['name']}: Latest value: {m['data'][0]['value']} (Threshold: {m['threshold']})" 
                             for m in state["metrics"] if m['data']])
    
    logs_text = "\n".join([f"- [{l['timestamp']}] [{l['level']}] {l['message']}" 
                          for l in state["logs"][:5]])  # Limit to 5 logs for brevity
    
    checks_text = "\n".join([f"- {c['name']}: {c['status']}\n  {'; '.join(c['details'])}" 
                            for c in state["system_checks"]])
    
    context = f"""
    Incident ID: {state['incident_id']}
    Title: {state['output']['title']}
    Summary: {state['output']['summary']}
    Entity: {state['entities'][0]['name'] if state['entities'] else 'Unknown'} (Type: {state['entities'][0]['type'] if state['entities'] else 'Unknown'})
    
    Key Metrics:
    {metrics_text}
    
    Key Logs:
    {logs_text}
    
    System Check Results:
    {checks_text}
    
    Investigation Steps:
    """
    
    for note in state["investigation_state"]["notes"]:
        context += f"\n--- {note['title']} ---\n{note['content'][:200]}...\n"
    
    # User prompt
    user_prompt = f"""
    {context}
    
    Based on all the evidence collected, determine the root cause of this incident.
    Provide a comprehensive analysis that explains what happened, why it happened, and the impact.
    
    Your response should include:
    1. The primary root cause
    2. Any contributing factors
    3. The impact of the incident
    4. How the issue propagated through the system
    """
    
    # Prepare dependencies for the agent
    deps = RootCauseAnalyzerDeps(
        metrics_collector=metrics_collector,
        incident_state=state
    )
    
    investigation_state = state["investigation_state"].copy()
    
    # Run the agent
    try:
        result = await root_cause_analyzer.run(user_prompt, deps=deps)
        root_cause_analysis = result.data
        
        # Add an investigation note
        note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Root Cause Analysis",
            "content": root_cause_analysis,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        investigation_state["notes"].append(note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["recommend_remediation"]
        
        # Update the output with the root cause
        state["output"]["root_cause"] = root_cause_analysis
        
    except Exception as e:
        logger.error(f"Error determining root cause: {str(e)}")
        # Add an error note
        error_note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Root Cause Analysis Error",
            "content": f"Error occurred during root cause analysis: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }
        investigation_state["notes"].append(error_note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["recommend_remediation"]  # Continue with next step despite error
    
    # Return only the updated fields, not the entire state
    return {
        "investigation_state": investigation_state
    }


async def recommend_remediation(state: IncidentState) -> Dict:
    """
    Recommend remediation actions using a Pydantic AI agent.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Recommending remediation for incident {state['incident_id']}")
    
    # Compile context with focus on root cause
    context = f"""
    Incident ID: {state['incident_id']}
    Title: {state['output']['title']}
    Entity: {state['entities'][0]['name'] if state['entities'] else 'Unknown'} (Type: {state['entities'][0]['type'] if state['entities'] else 'Unknown'})
    
    Root Cause Analysis:
    {state['output']['root_cause']}
    
    System Check Results:
    """
    
    for check in state["system_checks"]:
        context += f"\n- {check['name']}: {check['status']}\n  {'; '.join(check['details'])}"
    
    # User prompt
    user_prompt = f"""
    {context}
    
    Based on the root cause and investigation findings, what remediation actions do you recommend?
    Provide both immediate fixes to resolve the incident and long-term preventive measures.
    
    Format your response as a prioritized list of actions, with clear steps for each action.
    """
    
    # Prepare dependencies for the agent
    deps = RemediationAdvisorDeps(
        metrics_collector=metrics_collector,
        incident_state=state
    )
    
    investigation_state = state["investigation_state"].copy()
    
    # Run the agent
    try:
        result = await remediation_advisor.run(user_prompt, deps=deps)
        remediation_recommendations = result.data
        
        # Add an investigation note
        note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Remediation Recommendations",
            "content": remediation_recommendations,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        investigation_state["notes"].append(note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["generate_summary"]
        
        # Update the recommendations in output
        state["output"]["recommendations"] = remediation_recommendations
        
        # Parse remediation actions
        # First, see if we can split into immediate and preventive sections
        sections = remediation_recommendations.split("\n\n")
        
        if len(sections) >= 2:
            # Try to identify the immediate actions (usually first section) and preventive actions (later sections)
            immediate_actions = sections[0]
            preventive_actions = "\n\n".join(sections[1:])
            
            state["remediation"]["actions"] = [
                {
                    "title": "Immediate Action",
                    "description": "Actions to take immediately to resolve the incident",
                    "steps": immediate_actions
                },
                {
                    "title": "Preventive Measures",
                    "description": "Long-term actions to prevent recurrence",
                    "steps": preventive_actions
                }
            ]
        else:
            # If we can't easily split, put everything in immediate actions
            state["remediation"]["actions"] = [
                {
                    "title": "Recommended Actions",
                    "description": "Actions to resolve the incident and prevent recurrence",
                    "steps": remediation_recommendations
                }
            ]
        
    except Exception as e:
        logger.error(f"Error recommending remediation: {str(e)}")
        # Add an error note
        error_note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Remediation Recommendations Error",
            "content": f"Error occurred during remediation recommendations: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }
        investigation_state["notes"].append(error_note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = ["generate_summary"]  # Continue with next step despite error
    
    # Return only the updated fields, not the entire state
    return {
        "investigation_state": investigation_state
    }


async def generate_summary(state: IncidentState) -> Dict:
    """
    Generate a comprehensive summary of the incident.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Generating summary for incident {state['incident_id']}")
    
    # Compile context from the whole investigation
    metrics_summary = "; ".join([f"{m['name']}: {m['data'][0]['value']} {m['unit']}" 
                               for m in state["metrics"] if m['data']])
    
    checks_summary = "; ".join([f"{c['name']}: {c['status']}" 
                              for c in state["system_checks"]])
    
    root_cause_excerpt = state['output']['root_cause'][:300] + "..." if len(state['output']['root_cause']) > 300 else state['output']['root_cause']
    
    recommendations_excerpt = state['output']['recommendations'][:300] + "..." if len(state['output']['recommendations']) > 300 else state['output']['recommendations']
    
    context = f"""
    Incident ID: {state['incident_id']}
    Title: {state['output']['title']}
    Entity: {state['entities'][0]['name'] if state['entities'] else 'Unknown'} (Type: {state['entities'][0]['type'] if state['entities'] else 'Unknown'})
    
    Key Metrics:
    {metrics_summary}
    
    System Checks:
    {checks_summary}
    
    Root Cause:
    {root_cause_excerpt}
    
    Recommendations:
    {recommendations_excerpt}
    
    """
    
    # User prompt - use the same model as root_cause_analyzer
    user_prompt = f"""
    {context}
    
    Based on the incident investigation, provide a comprehensive summary of the incident,
    its root cause, impact, and recommended actions.
    
    The summary should be professional, objective, and suitable for technical stakeholders.
    Include:
    1. Incident overview - what happened and when
    2. Affected systems and impact
    3. Root cause analysis
    4. Resolution steps and current status
    5. Preventive measures for the future
    """
    
    # We'll use the root_cause_analyzer agent for this as it has similar requirements
    deps = RootCauseAnalyzerDeps(
        metrics_collector=metrics_collector,
        incident_state=state
    )
    
    investigation_state = state["investigation_state"].copy()
    
    # Run the agent
    try:
        result = await root_cause_analyzer.run(user_prompt, deps=deps)
        summary = result.data
        
        # Add an investigation note
        note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Incident Summary",
            "content": summary,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Update the state
        investigation_state["notes"].append(note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = []  # End of workflow
        
        # Update the output
        state["output"]["summary"] = summary
        state["output"]["status"] = "RESOLVED" if state["remediation"]["actions"] else "INVESTIGATING"
        
    except Exception as e:
        logger.error(f"Error generating summary: {str(e)}")
        # Add an error note
        error_note: InvestigationNote = {
            "step": investigation_state["current_step"] + 1,
            "title": "Summary Generation Error",
            "content": f"Error occurred during summary generation: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }
        investigation_state["notes"].append(error_note)
        investigation_state["current_step"] += 1
        investigation_state["next_steps"] = []  # End of workflow
    
    # Return only the updated fields, not the entire state
    return {
        "investigation_state": investigation_state
    }


# Conditional routing
async def should_continue(state: IncidentState) -> str:
    """
    Determine if the analysis should continue or end.
    
    Args:
        state: Current workflow state
        
    Returns:
        Next node name or END
    """
    # If there are next steps defined, continue to the first one
    if state["investigation_state"]["next_steps"]:
        return state["investigation_state"]["next_steps"][0]
    
    # Otherwise end the workflow
    return END


# Create the workflow graph
def create_analysis_workflow() -> StateGraph:
    """
    Create the incident analysis workflow graph with Pydantic AI agents.
    
    Returns:
        StateGraph for incident analysis
    """
    # Define the workflow
    workflow = StateGraph(IncidentState)
    
    # Add nodes
    workflow.add_node("analyze_alert", analyze_alert)
    workflow.add_node("collect_metrics", collect_metrics)
    workflow.add_node("analyze_metrics", analyze_metrics)
    workflow.add_node("collect_logs", collect_logs)
    workflow.add_node("analyze_logs", analyze_logs)
    workflow.add_node("graph_analysis", graph_analysis_node)
    workflow.add_node("graph_enrichment", graph_enrichment_node)
    workflow.add_node("determine_system_checks", determine_system_checks)
    workflow.add_node("run_system_checks", run_system_checks)
    workflow.add_node("determine_root_cause", determine_root_cause)
    workflow.add_node("recommend_remediation", recommend_remediation)
    workflow.add_node("generate_summary", generate_summary)
    
    # Set conditional routing
    workflow.add_conditional_edges(
        "analyze_alert",
        should_continue
    )
    
    workflow.add_conditional_edges(
        "collect_metrics",
        should_continue
    )
    
    workflow.add_conditional_edges(
        "analyze_metrics",
        should_continue
    )
    
    workflow.add_conditional_edges(
        "collect_logs",
        should_continue
    )
    
    workflow.add_conditional_edges(
        "analyze_logs",
        should_continue
    )
    
    workflow.add_conditional_edges(
        "graph_analysis",
        should_continue
    )
    
    workflow.add_conditional_edges(
        "graph_enrichment",
        should_continue
    )
    
    workflow.add_conditional_edges(
        "determine_system_checks",
        should_continue
    )
    
    workflow.add_conditional_edges(
        "run_system_checks",
        should_continue
    )
    
    workflow.add_conditional_edges(
        "determine_root_cause",
        should_continue
    )
    
    workflow.add_conditional_edges(
        "recommend_remediation",
        should_continue
    )
    
    workflow.add_conditional_edges(
        "generate_summary",
        should_continue
    )
    
    # Set the entry point
    workflow.set_entry_point("analyze_alert")
    
    return workflow


async def run_analysis_workflow(state: IncidentState) -> IncidentState:
    """
    Run the incident analysis workflow with Pydantic AI agents.
    
    Args:
        state: Initial workflow state
        
    Returns:
        Final workflow state
    """
    # Create the workflow
    workflow = create_analysis_workflow()
    
    # Compile the workflow with memory to maintain state
    memory = MemorySaver()
    app = workflow.compile(checkpointer=memory)
    
    # Generate a unique thread ID for this incident
    # thread_id = f"incident-1"
    
    # # Run the workflow
    # logger.info(f"Starting analysis workflow for incident 1")
    
    # config = {
    #     "configurable": {
    #         "thread_id": thread_id
    #     }
    # }

    # # Create initial incident state using the test alert
    # incident_state = {
    #     "incident_id": "afb1be25-3714-4115-9005-9929ef244089",  # Using issueId from alert
    #     "raw_alert": {
    #         "issueId": "afb1be25-3714-4115-9005-9929ef244089", 
    #         "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/afb1be25-3714-4115-9005-9929ef244089?notifier=WEBHOOK",
    #         "title": "castai-workload-autoscaler query result is > 10.0 on 'Pod with CrashLoopBackOff -- '", 
    #         "priority": "CRITICAL", 
    #         "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtODY3MTE3NTgxODg1MDAzMTg3NQ"], 
    #         "impactedEntities": ["castai-workload-autoscaler"], 
    #         "totalIncidents": "1",
    #         "state": "ACTIVATED",
    #         "trigger": "STATE_CHANGE",
    #         "isCorrelated": "false",
    #         "createdAt": *************,
    #         "updatedAt": *************,
    #         "sources": ["newrelic"],
    #         "alertPolicyNames": ["Neurons k8s Infra - Critical"],
    #         "alertConditionNames": ["Pod with CrashLoopBackOff -- "],
    #         "workflowName": "obv-ai-processing-neurons",
    #         "chartLink": "https://gorgon.nr-assets.net/image/5f52ac1f-eabc-4086-ba53-77a6ee9322f8?config.legend.enabled=false&width=400&height=210",
    #         "product": "neurons",
    #         "nr_region": "us"
    #     },
    #     "output": {
    #         "title": "castai-workload-autoscaler query result is > 10.0 on 'Pod with CrashLoopBackOff -- '",
    #         "summary": "Critical alert for castai-workload-autoscaler: Pod with CrashLoopBackOff",
    #         "severity": "CRITICAL",
    #         "root_cause": "",
    #         "recommendations": "",
    #         "status": "INVESTIGATING"
    #     },
    #     "entities": [
    #         {
    #             "id": "MTA5MzYyMHxJTkZSQXxOQXwtODY3MTE3NTgxODg1MDAzMTg3NQ",
    #             "type": "KUBERNETES_POD",
    #             "name": "castai-workload-autoscaler"
    #         }
    #     ],
    #     "metrics": [],
    #     "logs": [],
    #     "system_checks": [],
    #     "remediation": {
    #         "actions": []
    #     },
    #     "investigation_state": {
    #         "notes": [],
    #         "current_step": 0,
    #         "next_steps": []
    #     }
    # }

    # state = IncidentState(**incident_state)

    
    # result = await app.ainvoke(state, config)
    
    # logger.info(f"Completed analysis workflow for incident 1")
    
    # return result 


workflow = create_analysis_workflow()

# Compile the workflow with memory to maintain state
memory = MemorySaver()
app = workflow.compile(checkpointer=memory)

print(app.get_graph().draw_mermaid())