#!/usr/bin/env python3
"""
Graph-based cascading failure test scenario for MI architecture.

This test demonstrates how the graph-based entity relationship system
can detect and analyze cascading failures in the Mobile Identity (MI)
microservices architecture.
"""

import asyncio
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from loguru import logger

from ai_incident_manager.models.workflow_state import IncidentState
from ai_incident_manager.services.graph_service import get_graph_service
from ai_incident_manager.models.graph_models import (
    GraphNodeModel, GraphEdgeModel, NodeTypeEnum, RelationshipTypeEnum, HealthStatusEnum
)


def create_mi_architecture_graph_data() -> Dict[str, List[Dict]]:
    """
    Create sample graph data representing the MI (Mobile Identity) architecture
    based on the architecture diagram showing interconnected microservices.
    
    Returns:
        Dictionary containing nodes and edges for the MI architecture graph
    """
    
    # Define the MI architecture nodes based on the diagram
    nodes = [
        # Core MI Services
        {
            "entity_guid": "mi-connector-service-001",
            "entity_name": "connector-service",
            "entity_type": "kubernetes_deployment",
            "cluster_name": "ivmdm-uk1-uks-aks-instance",
            "namespace": "mi-prod",
            "is_primary": True,
            "health_status": "unhealthy",
            "availability": 0.65,  # 65% - below 75% threshold
            "cpu_usage": 0.85,
            "memory_usage": 0.78,
            "error_rate": 0.15,
            "criticality_score": 0.9
        },
        {
            "entity_guid": "mi-identity-service-001", 
            "entity_name": "identity-service",
            "entity_type": "kubernetes_deployment",
            "cluster_name": "ivmdm-uk1-uks-aks-instance",
            "namespace": "mi-prod",
            "is_primary": False,
            "health_status": "healthy",
            "availability": 0.95,
            "cpu_usage": 0.45,
            "memory_usage": 0.52,
            "error_rate": 0.02,
            "criticality_score": 0.95
        },
        {
            "entity_guid": "mi-auth-service-001",
            "entity_name": "auth-service", 
            "entity_type": "kubernetes_deployment",
            "cluster_name": "ivmdm-uk1-uks-aks-instance",
            "namespace": "mi-prod",
            "is_primary": False,
            "health_status": "healthy",
            "availability": 0.92,
            "cpu_usage": 0.38,
            "memory_usage": 0.44,
            "error_rate": 0.03,
            "criticality_score": 0.85
        },
        {
            "entity_guid": "mi-profile-service-001",
            "entity_name": "profile-service",
            "entity_type": "kubernetes_deployment", 
            "cluster_name": "ivmdm-uk1-uks-aks-instance",
            "namespace": "mi-prod",
            "is_primary": False,
            "health_status": "degraded",
            "availability": 0.88,
            "cpu_usage": 0.62,
            "memory_usage": 0.71,
            "error_rate": 0.08,
            "criticality_score": 0.75
        },
        
        # Infrastructure Components  
        {
            "entity_guid": "kafka-cluster-001",
            "entity_name": "kafka-cluster",
            "entity_type": "kafka_cluster",
            "cluster_name": "ivmdm-uk1-uks-aks-instance",
            "namespace": "kafka",
            "is_primary": False,
            "health_status": "healthy",
            "availability": 0.98,
            "cpu_usage": 0.35,
            "memory_usage": 0.41,
            "error_rate": 0.01,
            "criticality_score": 0.98
        },
        {
            "entity_guid": "redis-cluster-001",
            "entity_name": "redis-cluster",
            "entity_type": "redis_cluster",
            "cluster_name": "ivmdm-uk1-uks-aks-instance", 
            "namespace": "redis",
            "is_primary": False,
            "health_status": "healthy",
            "availability": 0.97,
            "cpu_usage": 0.28,
            "memory_usage": 0.33,
            "error_rate": 0.01,
            "criticality_score": 0.90
        },
        {
            "entity_guid": "postgresql-db-001",
            "entity_name": "postgresql-primary",
            "entity_type": "postgresql_database",
            "cluster_name": "ivmdm-uk1-uks-aks-instance",
            "namespace": "database",
            "is_primary": False,
            "health_status": "healthy",
            "availability": 0.99,
            "cpu_usage": 0.42,
            "memory_usage": 0.38,
            "error_rate": 0.005,
            "criticality_score": 0.95
        },
        {
            "entity_guid": "elasticsearch-cluster-001",
            "entity_name": "elasticsearch-cluster",
            "entity_type": "elasticsearch_cluster",
            "cluster_name": "ivmdm-uk1-uks-aks-instance",
            "namespace": "elastic",
            "is_primary": False,
            "health_status": "healthy", 
            "availability": 0.94,
            "cpu_usage": 0.48,
            "memory_usage": 0.55,
            "error_rate": 0.02,
            "criticality_score": 0.80
        },
        
        # API Gateway and Load Balancers
        {
            "entity_guid": "api-gateway-001",
            "entity_name": "api-gateway",
            "entity_type": "kubernetes_deployment",
            "cluster_name": "ivmdm-uk1-uks-aks-instance",
            "namespace": "gateway",
            "is_primary": False,
            "health_status": "healthy",
            "availability": 0.96,
            "cpu_usage": 0.55,
            "memory_usage": 0.48,
            "error_rate": 0.04,
            "criticality_score": 0.88
        },
        {
            "entity_guid": "load-balancer-001", 
            "entity_name": "nginx-ingress",
            "entity_type": "kubernetes_service",
            "cluster_name": "ivmdm-uk1-uks-aks-instance",
            "namespace": "ingress-nginx",
            "is_primary": False,
            "health_status": "healthy",
            "availability": 0.99,
            "cpu_usage": 0.25,
            "memory_usage": 0.30,
            "error_rate": 0.01,
            "criticality_score": 0.92
        },
        
        # External Dependencies
        {
            "entity_guid": "external-payment-api-001",
            "entity_name": "payment-gateway",
            "entity_type": "external_service",
            "cluster_name": "external",
            "namespace": "external",
            "is_primary": False,
            "health_status": "healthy",
            "availability": 0.94,
            "cpu_usage": 0.0,  # External service
            "memory_usage": 0.0,
            "error_rate": 0.03,
            "criticality_score": 0.70
        },
        {
            "entity_guid": "external-notification-api-001",
            "entity_name": "notification-service",
            "entity_type": "external_service", 
            "cluster_name": "external",
            "namespace": "external",
            "is_primary": False,
            "health_status": "healthy",
            "availability": 0.91,
            "cpu_usage": 0.0,
            "memory_usage": 0.0,
            "error_rate": 0.05,
            "criticality_score": 0.65
        }
    ]
    
    # Define relationships based on the MI architecture dependencies
    edges = [
        # Load Balancer -> API Gateway
        {
            "source_entity_guid": "load-balancer-001",
            "target_entity_guid": "api-gateway-001", 
            "relationship_type": "routes_to",
            "weight": 0.95,
            "failure_probability": 0.02,
            "is_critical_path": True
        },
        
        # API Gateway -> Core Services
        {
            "source_entity_guid": "api-gateway-001",
            "target_entity_guid": "mi-connector-service-001",
            "relationship_type": "routes_to",
            "weight": 0.90,
            "failure_probability": 0.05,
            "is_critical_path": True
        },
        {
            "source_entity_guid": "api-gateway-001", 
            "target_entity_guid": "mi-identity-service-001",
            "relationship_type": "routes_to",
            "weight": 0.85,
            "failure_probability": 0.03,
            "is_critical_path": True
        },
        {
            "source_entity_guid": "api-gateway-001",
            "target_entity_guid": "mi-auth-service-001",
            "relationship_type": "routes_to", 
            "weight": 0.88,
            "failure_probability": 0.03,
            "is_critical_path": True
        },
        
        # Connector Service Dependencies
        {
            "source_entity_guid": "mi-connector-service-001",
            "target_entity_guid": "mi-identity-service-001",
            "relationship_type": "depends_on",
            "weight": 0.95,
            "failure_probability": 0.08,
            "is_critical_path": True
        },
        {
            "source_entity_guid": "mi-connector-service-001",
            "target_entity_guid": "mi-auth-service-001", 
            "relationship_type": "depends_on",
            "weight": 0.90,
            "failure_probability": 0.06,
            "is_critical_path": True
        },
        {
            "source_entity_guid": "mi-connector-service-001",
            "target_entity_guid": "kafka-cluster-001",
            "relationship_type": "publishes_to",
            "weight": 0.80,
            "failure_probability": 0.10,
            "is_critical_path": False
        },
        {
            "source_entity_guid": "mi-connector-service-001",
            "target_entity_guid": "redis-cluster-001",
            "relationship_type": "caches_to",
            "weight": 0.75,
            "failure_probability": 0.12,
            "is_critical_path": False
        },
        
        # Identity Service Dependencies
        {
            "source_entity_guid": "mi-identity-service-001",
            "target_entity_guid": "postgresql-db-001",
            "relationship_type": "stores_data",
            "weight": 0.98,
            "failure_probability": 0.02,
            "is_critical_path": True
        },
        {
            "source_entity_guid": "mi-identity-service-001",
            "target_entity_guid": "redis-cluster-001",
            "relationship_type": "caches_to",
            "weight": 0.85,
            "failure_probability": 0.08,
            "is_critical_path": False
        },
        {
            "source_entity_guid": "mi-identity-service-001",
            "target_entity_guid": "mi-profile-service-001",
            "relationship_type": "communicates_with",
            "weight": 0.70,
            "failure_probability": 0.15,
            "is_critical_path": False
        },
        
        # Auth Service Dependencies
        {
            "source_entity_guid": "mi-auth-service-001",
            "target_entity_guid": "postgresql-db-001",
            "relationship_type": "stores_data",
            "weight": 0.95,
            "failure_probability": 0.03,
            "is_critical_path": True
        },
        {
            "source_entity_guid": "mi-auth-service-001",
            "target_entity_guid": "redis-cluster-001",
            "relationship_type": "caches_to",
            "weight": 0.80,
            "failure_probability": 0.10,
            "is_critical_path": False
        },
        
        # Profile Service Dependencies
        {
            "source_entity_guid": "mi-profile-service-001",
            "target_entity_guid": "postgresql-db-001", 
            "relationship_type": "stores_data",
            "weight": 0.92,
            "failure_probability": 0.05,
            "is_critical_path": True
        },
        {
            "source_entity_guid": "mi-profile-service-001",
            "target_entity_guid": "elasticsearch-cluster-001",
            "relationship_type": "indexes_to",
            "weight": 0.75,
            "failure_probability": 0.12,
            "is_critical_path": False
        },
        
        # External Service Dependencies
        {
            "source_entity_guid": "mi-connector-service-001",
            "target_entity_guid": "external-payment-api-001",
            "relationship_type": "integrates_with",
            "weight": 0.65,
            "failure_probability": 0.20,
            "is_critical_path": False
        },
        {
            "source_entity_guid": "mi-identity-service-001",
            "target_entity_guid": "external-notification-api-001",
            "relationship_type": "integrates_with",
            "weight": 0.60,
            "failure_probability": 0.25,
            "is_critical_path": False
        },
        
        # Cross-service Communication
        {
            "source_entity_guid": "kafka-cluster-001",
            "target_entity_guid": "mi-identity-service-001",
            "relationship_type": "delivers_events_to",
            "weight": 0.70,
            "failure_probability": 0.08,
            "is_critical_path": False
        },
        {
            "source_entity_guid": "kafka-cluster-001",
            "target_entity_guid": "mi-profile-service-001",
            "relationship_type": "delivers_events_to",
            "weight": 0.68,
            "failure_probability": 0.10,
            "is_critical_path": False
        }
    ]
    
    return {
        "nodes": nodes,
        "edges": edges,
        "metadata": {
            "architecture_name": "Mobile Identity (MI)",
            "cluster_name": "ivmdm-uk1-uks-aks-instance",
            "environment": "production", 
            "region": "uk-south",
            "total_nodes": len(nodes),
            "total_edges": len(edges),
            "created_at": datetime.now(timezone.utc).isoformat()
        }
    }


async def create_connector_service_failure_scenario() -> IncidentState:
    """
    Create a test scenario where the connector-service has pods failing,
    triggering cascading failure analysis across the MI architecture.
    
    Returns:
        IncidentState configured for the connector-service failure scenario
    """
    
    # Create the incident based on the test_incremental_graph_refactored.py data
    # but enhanced with graph analysis context
    now = datetime.now(timezone.utc)
    since_time = now - timedelta(minutes=15)
    
    test_alert_data = {
        "issueId": "connector-service-pods-failure-001",
        "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/connector-service-pods-failure-001",
        "title": "Percentage of Unavailable pods > 25% for cluster ivmdm-uk1-uks-aks-instance, deployment connector-service",
        "priority": "CRITICAL",
        "EntityId": ["mi-connector-service-001"],
        "impactedEntities": ["connector-service"],
        "totalIncidents": "1",
        "state": "ACTIVATED",
        "trigger": "STATE_CHANGE",
        "isCorrelated": "false",
        "createdAt": int(now.timestamp() * 1000),
        "updatedAt": int(now.timestamp() * 1000),
        "sources": ["newrelic"],
        "alertPolicyNames": ["SRE - MI Azure Alert - Infrastructure"],
        "alertConditionNames": ["Percentage of Unavailable pods > 25%"],
        "workflowName": "obv-ai-processing-graph-analysis",
        "chartLink": "Not Available",
        "product": "MDM",
        "nr_region": "us",
        "landscape": "production",
        "region": "uk-south",
        "cluster_name": "ivmdm-uk1-uks-aks-instance"
    }
    
    # Create entities from the MI architecture
    graph_data = create_mi_architecture_graph_data()
    primary_entity = next(node for node in graph_data["nodes"] if node["is_primary"])
    
    entities = [
        {
            "entity_guid": primary_entity["entity_guid"],
            "entity_name": primary_entity["entity_name"],
            "entity_type": primary_entity["entity_type"],
            "cluster_name": primary_entity["cluster_name"],
            "namespace": primary_entity["namespace"],
            "is_primary": True,
            "health_status": primary_entity["health_status"],
            "availability": primary_entity["availability"],
            "cpu_usage": primary_entity["cpu_usage"],
            "memory_usage": primary_entity["memory_usage"],
            "error_rate": primary_entity["error_rate"]
        }
    ]
    
    # Create the incident state
    incident_state: IncidentState = {
        "incident_id": test_alert_data["issueId"],
        "raw_alert": test_alert_data,
        "title": test_alert_data["title"],
        "description": f"Critical alert: {primary_entity['availability']*100:.1f}% availability for {primary_entity['entity_name']} in {primary_entity['cluster_name']}",
        "severity": test_alert_data["priority"],
        "start_time": now.isoformat(),
        "since_time": since_time.isoformat(),
        "until_time": now.isoformat(),
        "current_phase": "graph_analysis_ready",
        "investigation_notes": [],
        "timeline": [],
        "primary_entity_guid": primary_entity["entity_guid"],
        "primary_entity_type": primary_entity["entity_type"],
        "entities": entities,
        "entity_relationships": [],
        "metrics": [],
        "logs": [],
        "system_checks": [],
        "root_cause": None,
        "remediation_actions": [],
        "alert_summary": "",
        "alert_category": "kubernetes_availability",
        "alert_runbook": "",
        "alert_title": test_alert_data["title"],
        "condition_name": test_alert_data["alertConditionNames"][0],
        "policy_name": test_alert_data["alertPolicyNames"][0],
        "product": test_alert_data["product"],
        "nr_region": test_alert_data["nr_region"],
        "landscape": test_alert_data["landscape"],
        "region": test_alert_data["region"],
        "cluster_name": test_alert_data["cluster_name"],
        "run_id": f"graph-test-{int(now.timestamp())}",
        
        # Add graph-specific data
        "graph_analysis": {
            "sample_data": graph_data,
            "analysis_config": {
                "max_depth": 3,
                "failure_threshold": 0.25,
                "include_external_dependencies": True,
                "prioritize_critical_paths": True
            }
        }
    }
    
    return incident_state


async def test_graph_analysis_scenario():
    """
    Test the graph analysis scenario with the MI architecture.
    
    This test demonstrates:
    1. Building entity relationship graphs from the MI architecture
    2. Detecting cascading failure patterns
    3. Identifying critical paths and dependencies
    4. Providing actionable insights and recommendations
    """
    logger.info("=" * 80)
    logger.info("GRAPH ANALYSIS SCENARIO TEST - MI ARCHITECTURE")
    logger.info("=" * 80)
    
    # Create the test scenario
    logger.info("Creating connector-service failure scenario...")
    incident_state = await create_connector_service_failure_scenario()
    
    # Log scenario details
    logger.info(f"Incident ID: {incident_state['incident_id']}")
    logger.info(f"Primary Entity: {incident_state['primary_entity_guid']}")
    logger.info(f"Alert Category: {incident_state['alert_category']}")
    logger.info(f"Cluster: {incident_state['cluster_name']}")
    
    # Get the graph service
    graph_service = get_graph_service()
    
    # Extract graph data from incident state
    sample_data = incident_state["graph_analysis"]["sample_data"]
    
    # Get primary entity information
    primary_entity_guid = incident_state["primary_entity_guid"]
    
    # Get time range
    since_time_ms = _parse_time_to_ms(incident_state.get("since_time")) if incident_state.get("since_time") else None
    until_time_ms = _parse_time_to_ms(incident_state.get("until_time")) if incident_state.get("until_time") else None
    
    logger.info("\n" + "=" * 60)
    logger.info("BUILDING ENTITY RELATIONSHIP GRAPH")
    logger.info("=" * 60)
    
    # Convert sample data to graph models for building the graph
    graph_nodes = []
    for node_data in sample_data["nodes"]:
        # Map entity type
        if node_data["entity_type"] == "kubernetes_deployment":
            node_type = NodeTypeEnum.KUBERNETES_DEPLOYMENT
        elif "database" in node_data["entity_type"]:
            node_type = NodeTypeEnum.DATABASE
        elif "kafka" in node_data["entity_type"]:
            node_type = NodeTypeEnum.KAFKA
        elif node_data["entity_type"] == "redis_cluster":
            node_type = NodeTypeEnum.DATABASE  # Redis as database type
        elif node_data["entity_type"] == "elasticsearch_cluster":
            node_type = NodeTypeEnum.DATABASE  # Elasticsearch as database type
        elif "service" in node_data["entity_type"]:
            node_type = NodeTypeEnum.APPLICATION
        else:
            node_type = NodeTypeEnum.UNKNOWN
        
        # Map health status to valid enum value
        health_status_map = {
            "healthy": HealthStatusEnum.HEALTHY,
            "degraded": HealthStatusEnum.DEGRADED,
            "unhealthy": HealthStatusEnum.CRITICAL,
            "issue": HealthStatusEnum.ISSUE,
            "unknown": HealthStatusEnum.UNKNOWN
        }
        health_status = health_status_map.get(node_data.get("health_status", "unknown"), HealthStatusEnum.UNKNOWN)
        
        graph_node = GraphNodeModel(
            id=node_data["entity_guid"],  # Use entity_guid as id
            name=node_data["entity_name"],  # Use entity_name as name
            node_type=node_type,
            entity_guid=node_data["entity_guid"],
            cluster_name=node_data["cluster_name"],
            namespace=node_data.get("namespace"),
            health_status=health_status,
            metrics={
                "availability": node_data.get("availability", 0.0),
                "cpu_usage": node_data.get("cpu_usage", 0.0),
                "memory_usage": node_data.get("memory_usage", 0.0),
                "error_rate": node_data.get("error_rate", 0.0)
            },
            properties={
                "is_primary": node_data.get("is_primary", False)
            },
            criticality_score=node_data.get("criticality_score", 0.5)
        )
        graph_nodes.append(graph_node)
    
    graph_edges = []
    for edge_data in sample_data["edges"]:
        # Map relationship type
        relationship_map = {
            "depends_on": RelationshipTypeEnum.DEPENDS_ON,
            "routes_to": RelationshipTypeEnum.COMMUNICATES_WITH,
            "publishes_to": RelationshipTypeEnum.WRITES_TO,
            "caches_to": RelationshipTypeEnum.WRITES_TO,
            "stores_data": RelationshipTypeEnum.WRITES_TO,
            "communicates_with": RelationshipTypeEnum.COMMUNICATES_WITH,
            "indexes_to": RelationshipTypeEnum.WRITES_TO,
            "integrates_with": RelationshipTypeEnum.COMMUNICATES_WITH,
            "delivers_events_to": RelationshipTypeEnum.COMMUNICATES_WITH
        }
        relationship_type = relationship_map.get(edge_data["relationship_type"], RelationshipTypeEnum.RELATED_TO)
        
        graph_edge = GraphEdgeModel(
            source=edge_data["source_entity_guid"],  # Use 'source' not 'source_entity_guid'
            target=edge_data["target_entity_guid"],  # Use 'target' not 'target_entity_guid'
            relationship_type=relationship_type,
            weight=edge_data.get("weight", 1.0),
            failure_propagation_probability=edge_data.get("failure_probability", 0.05),
            properties={
                "is_critical_path": edge_data.get("is_critical_path", False)
            }
        )
        graph_edges.append(graph_edge)
    
    # Build the graph using the primary entity information from incident state
    primary_entity_data = next((node for node in sample_data["nodes"] if node["is_primary"]), None)
    if not primary_entity_data:
        primary_entity_data = sample_data["nodes"][0] if sample_data["nodes"] else {}
    
    graph = await graph_service.build_entity_graph(
        primary_entity_guid=primary_entity_guid,
        entity_type=primary_entity_data.get("entity_type", "UNKNOWN"),
        entity_name=primary_entity_data.get("entity_name", "unknown"),
        alert_category=incident_state["alert_category"],
        cluster_name=incident_state.get("cluster_name"),
        max_depth=3,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    logger.info(f"Graph built successfully:")
    logger.info(f"  - Nodes: {graph.number_of_nodes()}")
    logger.info(f"  - Edges: {graph.number_of_edges()}")
    
    # Find primary entity
    primary_node = next((node for node in graph_nodes if node.entity_guid == primary_entity_guid), None)
    
    if not primary_node:
        logger.error("Primary entity not found in graph!")
        return
    
    logger.info(f"  - Primary Entity: {primary_node.name} (Health: {primary_node.health_status.value})")
    logger.info(f"  - Availability: {primary_node.metrics.get('availability', 0)*100:.1f}%")
    
    logger.info("\n" + "=" * 60)
    logger.info("ANALYZING CASCADING FAILURES")
    logger.info("=" * 60)
    
    # Analyze cascading failures
    cascading_analysis = graph_service.analyze_cascading_failures(
        graph=graph,
        primary_entity_guid=primary_entity_guid,
        failure_threshold=0.25,
        max_hops=3
    )
    
    logger.info(f"Cascading failure analysis completed:")
    logger.info(f"  - Blast Radius: {cascading_analysis.potential_blast_radius} entities")
    logger.info(f"  - Impact Score: {cascading_analysis.estimated_impact_score:.2f}")
    
    logger.info(f"\nAffected Entities ({len(cascading_analysis.affected_entities)}):")
    for entity_guid in cascading_analysis.affected_entities[:5]:  # Show top 5
        # Find entity name from sample data
        entity_data = next((n for n in sample_data["nodes"] if n["entity_guid"] == entity_guid), None)
        entity_name = entity_data["entity_name"] if entity_data else entity_guid[:8]
        risk_score = cascading_analysis.risk_scores.get(entity_guid, 0.0)
        logger.info(f"  - {entity_name} (Risk Score: {risk_score:.2f})")
    
    logger.info(f"\nCritical Dependencies ({len(cascading_analysis.critical_dependencies)}):")
    for dep_guid in cascading_analysis.critical_dependencies[:3]:  # Show top 3
        entity_data = next((n for n in sample_data["nodes"] if n["entity_guid"] == dep_guid), None)
        entity_name = entity_data["entity_name"] if entity_data else dep_guid[:8]
        logger.info(f"  - {entity_name}")
    
    logger.info("\n" + "=" * 60)
    logger.info("CRITICAL PATH ANALYSIS")
    logger.info("=" * 60)
    
    # Find critical paths to downstream entities
    if len(list(graph.successors(primary_entity_guid))) > 0:
        # Get first successor as target for demonstration
        target_entity = list(graph.successors(primary_entity_guid))[0]
        
        critical_paths = graph_service.find_critical_paths(
            graph=graph,
            source=primary_entity_guid,
            target=target_entity
        )
        
        logger.info(f"Found {len(critical_paths)} critical paths:")
        for i, path in enumerate(critical_paths[:3], 1):  # Show top 3
            path_names = []
            for entity_guid in path:
                # Find entity name from original sample data
                entity_data = next((n for n in sample_data["nodes"] if n["entity_guid"] == entity_guid), None)
                if entity_data:
                    path_names.append(entity_data["entity_name"])
                else:
                    path_names.append(entity_guid[:8])  # Fallback to shortened GUID
            
            logger.info(f"  {i}. {' -> '.join(path_names)}")
    else:
        logger.info("No downstream entities found for critical path analysis.")
    
    logger.info("\n" + "=" * 60)
    logger.info("RECOMMENDATIONS")
    logger.info("=" * 60)
    
    # Generate recommendations based on analysis
    recommendations = []
    
    # High-priority monitoring recommendations
    if cascading_analysis.potential_blast_radius > 3:
        recommendations.append({
            "priority": "CRITICAL",
            "action": "Immediate Monitoring",
            "description": f"Monitor {len(cascading_analysis.affected_entities)} potentially affected entities",
            "entities": [e.entity_name for e in cascading_analysis.affected_entities[:3]]
        })
    
    # Critical dependency recommendations
    if cascading_analysis.critical_dependencies:
        recommendations.append({
            "priority": "HIGH", 
            "action": "Dependency Validation",
            "description": "Validate critical service dependencies",
            "dependencies": [f"{d.source_entity_name} -> {d.target_entity_name}" 
                           for d in cascading_analysis.critical_dependencies[:2]]
        })
    
    # Failure prevention recommendations
    if cascading_analysis.estimated_impact_score > 0.7:
        recommendations.append({
            "priority": "HIGH",
            "action": "Failure Prevention",
            "description": "Implement circuit breakers and graceful degradation",
            "focus_areas": ["API rate limiting", "Database connection pooling", "Cache fallback mechanisms"]
        })
    
    for i, rec in enumerate(recommendations, 1):
        logger.info(f"{i}. [{rec['priority']}] {rec['action']}")
        logger.info(f"   {rec['description']}")
        if 'entities' in rec:
            logger.info(f"   Entities: {', '.join(rec['entities'])}")
        if 'dependencies' in rec:
            logger.info(f"   Dependencies: {', '.join(rec['dependencies'])}")
        if 'focus_areas' in rec:
            logger.info(f"   Focus Areas: {', '.join(rec['focus_areas'])}")
        logger.info("")
    
    logger.info("=" * 80)
    logger.info("GRAPH ANALYSIS SCENARIO TEST COMPLETED")
    logger.info("=" * 80)
    
    # Return results for further processing if needed
    return {
        "incident_state": incident_state,
        "graph": graph,
        "cascading_analysis": cascading_analysis,
        "critical_paths": locals().get("critical_paths", []),
        "recommendations": recommendations
    }


def _parse_time_to_ms(time_str: str) -> Optional[int]:
    """Parse ISO time string to milliseconds."""
    try:
        dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        return int(dt.timestamp() * 1000)
    except:
        return None


if __name__ == "__main__":
    asyncio.run(test_graph_analysis_scenario())