#!/usr/bin/env python3
"""
Import <PERSON>ert Conditions script.

This script imports alert conditions and categories from YAML files into the PostgreSQL database.
"""

import os
import sys
import yaml
import logging
import re
import json
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add the project root to the Python path
script_dir = Path(__file__).resolve().parent
project_root = script_dir.parent
sys.path.append(str(project_root))

import dotenv
from ai_incident_manager.database.postgres import PostgresDB
from ai_incident_manager.services.alert_category_service import get_alert_category_service

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Map of category names to common patterns for automatic categorization
CATEGORY_PATTERNS = {
    "kubernetes_crashloopbackoff": ["crashloopbackoff", "restartcount", "container restart"],
    "kubernetes_evicted_pod": ["evicted", "pod eviction"],
    "kubernetes_scheduling_failure": ["failedscheduling", "scheduling", "scheduler"],
    "kubernetes_node_pressure": ["node pressure", "memory pressure", "disk pressure"],
    "kubernetes_deployment_unhealthy": ["deployment", "replicas", "podsavailable"],
    "debezium_lag": ["debezium", "lag", "cdc"],
    "elasticsearch_health": ["elasticsearch", "es"],
    "database_performance": ["database", "sql", "query time", "postgres", "mysql"],
    "network_issues": ["network", "connectivity", "dns", "timeout", "connection"],
    "infrastructure_issues": ["infrastructure", "host", "server", "vm", "node"],
    "application_performance": ["application", "response time", "latency", "apdex", "error rate"]
}

def load_alert_categories() -> List[Dict[str, Any]]:
    """Load alert categories from the YAML file."""
    try:
        yaml_file = "ai_incident_manager/config/alert_categories.yaml"
        with open(yaml_file, 'r') as file:
            data = yaml.safe_load(file)
            
        # Check which format we're using (old or new)
        if "condition_categories" in data:
            # New format
            return data["condition_categories"]
        elif "categories" in data:
            # Old format
            return data["categories"]
        else:
            logger.warning(f"No categories found in {yaml_file}")
            return []
    except Exception as e:
        logger.error(f"Error loading alert categories: {str(e)}")
        return []

def load_alert_conditions(file_path: str) -> Dict[str, List[Dict[str, Any]]]:
    """Load alert conditions from the YAML file."""
    try:
        with open(file_path, 'r') as file:
            return yaml.safe_load(file)
    except Exception as e:
        logger.error(f"Error loading alert conditions from {file_path}: {str(e)}")
        return {}

def guess_category(condition: Dict[str, Any]) -> str:
    """Guess the category based on condition details."""
    condition_name = condition.get("name", "").lower()
    description = condition.get("description", "").lower() if condition.get("description") else ""
    nrql_query = condition.get("nrql", {}).get("query", "").lower() if condition.get("nrql") else ""
    
    text_to_check = f"{condition_name} {description} {nrql_query}"
    
    for category, patterns in CATEGORY_PATTERNS.items():
        for pattern in patterns:
            if pattern.lower() in text_to_check:
                return category
    
    return "unknown"

def import_categories(db: PostgresDB, categories: List[Dict[str, Any]]) -> Dict[str, int]:
    """Import categories into the database."""
    logger.info("Importing alert categories...")
    category_id_map = {}
    
    with db.get_connection() as conn:
        with conn.cursor() as cur:
            for category in categories:
                category_name = category.get("category")
                
                # Skip if no category name
                if not category_name:
                    continue
                
                # Insert category if it doesn't exist
                cur.execute("""
                INSERT INTO alert_categories 
                (category_name, title_pattern, description) 
                VALUES (%s, %s, %s)
                ON CONFLICT (category_name) DO UPDATE 
                SET title_pattern = EXCLUDED.title_pattern, 
                    description = EXCLUDED.description,
                    updated_at = NOW()
                RETURNING id
                """, (
                    category_name,
                    category.get("title_pattern", ""),
                    category.get("description", "")
                ))
                
                # Get the category ID
                category_id = cur.fetchone()[0]
                category_id_map[category_name] = category_id
                
                # Import likely causes
                if "likely_causes" in category:
                    for cause in category["likely_causes"]:
                        cur.execute("""
                        INSERT INTO category_likely_causes
                        (category_id, cause_description)
                        VALUES (%s, %s)
                        ON CONFLICT (category_id, cause_description) DO NOTHING
                        """, (category_id, cause))
                
                # Import metrics (new format)
                if "metrics" in category:
                    for i, metric in enumerate(category["metrics"]):
                        metric_name = metric.get("name")
                        if metric_name:
                            cur.execute("""
                            INSERT INTO category_metrics
                            (category_id, metric_name, importance)
                            VALUES (%s, %s, %s)
                            ON CONFLICT (category_id, metric_name) DO NOTHING
                            """, (category_id, metric_name, len(category["metrics"]) - i))
                # Fallback to old format if needed
                elif "metrics_to_check" in category:
                    for i, metric in enumerate(category["metrics_to_check"]):
                        cur.execute("""
                        INSERT INTO category_metrics
                        (category_id, metric_name, importance)
                        VALUES (%s, %s, %s)
                        ON CONFLICT (category_id, metric_name) DO NOTHING
                        """, (category_id, metric, len(category["metrics_to_check"]) - i))
                
                # Import entity relationships
                if "entity_relationships" in category:
                    for rel in category["entity_relationships"]:
                        target_type = rel.get("target_type")
                        relationship_type = rel.get("relationship_type", "related")
                        metrics = rel.get("metrics", [])
                        
                        cur.execute("""
                        INSERT INTO category_entity_relationships
                        (category_id, source_type, target_type, relationship_type, metrics_to_collect)
                        VALUES (%s, %s, %s, %s, %s)
                        ON CONFLICT (category_id, source_type, target_type) DO UPDATE
                        SET relationship_type = EXCLUDED.relationship_type,
                            metrics_to_collect = EXCLUDED.metrics_to_collect
                        """, (
                            category_id,
                            "alert",  # Default source type
                            target_type,
                            relationship_type,
                            metrics  # Will be stored as JSON
                        ))
                
                # Import runbook steps
                if "runbook" in category:
                    # Handle string or multiline string format
                    if isinstance(category["runbook"], str):
                        runbook_text = category["runbook"].strip()
                        steps = runbook_text.split("\n")
                    else:
                        steps = []
                    
                    # Process each step
                    for i, step in enumerate(steps):
                        step_text = step.strip()
                        if not step_text:
                            continue
                            
                        # Default values
                        title = step_text
                        description = ""
                        tool_name = None
                        tool_parameters = None
                        
                        # Parse step number if present (e.g., "1. Check logs")
                        step_pattern = r'^\d+\.\s+(.+)$'
                        step_match = re.match(step_pattern, step_text)
                        if step_match:
                            title = step_match.group(1)
                            
                        # Look for tool information
                        tool_pattern = r'[-\s]*Tool:\s+([a-zA-Z0-9_]+)(?:\((.+)\))?'
                        tool_matches = re.findall(tool_pattern, step_text)
                        
                        if tool_matches:
                            for tool_name_match, tool_params_match in tool_matches:
                                tool_name = tool_name_match
                                if tool_params_match:
                                    try:
                                        # Try to parse as JSON
                                        tool_parameters = json.loads(tool_params_match)
                                    except json.JSONDecodeError:
                                        # If not valid JSON, store as string
                                        tool_parameters = tool_params_match
                        
                        # Insert the step
                        cur.execute("""
                        INSERT INTO category_runbooks
                        (category_id, step_number, title, description, tool_name, tool_parameters)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        ON CONFLICT (category_id, step_number) DO UPDATE
                        SET title = EXCLUDED.title,
                            description = EXCLUDED.description,
                            tool_name = EXCLUDED.tool_name,
                            tool_parameters = EXCLUDED.tool_parameters,
                            updated_at = NOW()
                        """, (
                            category_id, 
                            i+1, 
                            title, 
                            description, 
                            tool_name,
                            tool_parameters
                        ))
                
                # Import condition IDs
                if "condition_ids" in category:
                    for condition_id in category["condition_ids"]:
                        cur.execute("""
                        INSERT INTO condition_category_mapping
                        (condition_id, category_id)
                        VALUES (%s, %s)
                        ON CONFLICT (condition_id, category_id) DO NOTHING
                        """, (condition_id, category_id))
            
            conn.commit()
    
    logger.info(f"Imported {len(category_id_map)} categories")
    return category_id_map

def import_conditions(db: PostgresDB, conditions: Dict[str, List[Dict[str, Any]]], category_id_map: Dict[str, int]) -> None:
    """Import conditions into the database."""
    logger.info("Importing alert conditions...")
    total_imported = 0
    
    with db.get_connection() as conn:
        with conn.cursor() as cur:
            for policy_id, policy_conditions in conditions.items():
                for condition in policy_conditions:
                    condition_id = condition.get("id")
                    if not condition_id:
                        continue
                    
                    # Get threshold info
                    threshold_operator = None
                    threshold_value = None
                    threshold_duration = None
                    threshold_occurrences = None
                    priority = None
                    
                    if "terms" in condition and condition["terms"]:
                        term = condition["terms"][0]  # Use first term
                        threshold_operator = term.get("operator")
                        threshold_value = term.get("threshold")
                        threshold_duration = term.get("thresholdDuration")
                        threshold_occurrences = term.get("thresholdOccurrences")
                        priority = term.get("priority")
                    
                    # Get NRQL query
                    nrql_query = condition.get("nrql", {}).get("query", "") if condition.get("nrql") else None
                    
                    # Insert condition
                    cur.execute("""
                    INSERT INTO alert_conditions
                    (condition_id, policy_id, name, description, nrql_query, enabled,
                     threshold_operator, threshold_value, threshold_duration, threshold_occurrences,
                     priority, runbook_url, condition_data)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (condition_id) DO UPDATE
                    SET policy_id = EXCLUDED.policy_id,
                        name = EXCLUDED.name,
                        description = EXCLUDED.description,
                        nrql_query = EXCLUDED.nrql_query,
                        enabled = EXCLUDED.enabled,
                        threshold_operator = EXCLUDED.threshold_operator,
                        threshold_value = EXCLUDED.threshold_value,
                        threshold_duration = EXCLUDED.threshold_duration,
                        threshold_occurrences = EXCLUDED.threshold_occurrences,
                        priority = EXCLUDED.priority,
                        runbook_url = EXCLUDED.runbook_url,
                        condition_data = EXCLUDED.condition_data,
                        updated_at = NOW()
                    """, (
                        condition_id,
                        policy_id,
                        condition.get("name"),
                        condition.get("description"),
                        nrql_query,
                        condition.get("enabled", True),
                        threshold_operator,
                        threshold_value,
                        threshold_duration,
                        threshold_occurrences,
                        priority,
                        condition.get("runbookUrl"),
                        condition  # Store full condition data as JSON
                    ))
                    
                    # Guess category and create mapping
                    guessed_category = guess_category(condition)
                    if guessed_category in category_id_map:
                        category_id = category_id_map[guessed_category]
                        
                        cur.execute("""
                        INSERT INTO condition_category_mapping
                        (condition_id, category_id)
                        VALUES (%s, %s)
                        ON CONFLICT (condition_id, category_id) DO NOTHING
                        """, (condition_id, category_id))
                    
                    total_imported += 1
            
            conn.commit()
    
    logger.info(f"Imported {total_imported} alert conditions")

def main():
    """Main function."""
    # Load environment variables
    dotenv.load_dotenv()
    
    # Initialize the database
    db = PostgresDB()
    
    # Load categories and conditions
    categories = load_alert_categories()
    
    # Import alert categories first
    category_id_map = import_categories(db, categories)
    
    # Load conditions from the provided file
    alert_conditions_file = os.path.join(project_root, "ai_incident_manager/config/neurons_alert_conditions.yaml")
    if os.path.exists(alert_conditions_file):
        conditions = load_alert_conditions(alert_conditions_file)
        import_conditions(db, conditions, category_id_map)
    else:
        logger.error(f"Alert conditions file not found: {alert_conditions_file}")
    
    logger.info("Import completed successfully")

if __name__ == "__main__":
    main() 