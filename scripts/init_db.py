#!/usr/bin/env python3
"""
Database initialization script.

This script initializes the PostgreSQL database with the necessary tables and initial data.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
script_dir = Path(__file__).resolve().parent
project_root = script_dir.parent
sys.path.append(str(project_root))

import dotenv
from ai_incident_manager.database.postgres import PostgresDB

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_postgres():
    """Check if PostgreSQL is available."""
    logger.info("Checking PostgreSQL connection...")
    db = PostgresDB()
    try:
        with db.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT version();")
                version = cur.fetchone()[0]
                logger.info(f"PostgreSQL is available: {version}")
        return True
    except Exception as e:
        logger.error(f"PostgreSQL connection failed: {str(e)}")
        return False

def init_database():
    """Initialize the database with tables."""
    logger.info("Initializing database tables...")
    db = PostgresDB()
    db.create_tables()
    logger.info("Database tables initialized successfully.")

def create_test_data():
    """Create some test data in the database."""
    logger.info("Creating test data...")
    db = PostgresDB()
    
    # Create a test incident
    test_incident = {
        "id": "test-incident-1",
        "title": "Test Incident - Pod CrashLoopBackOff",
        "description": "This is a test incident for a pod in CrashLoopBackOff state",
        "severity": "CRITICAL",
        "status": "OPEN",
        "alert_category": "kubernetes_crashloopbackoff",
        "runbook": "1. Check pod logs\n2. Verify resources\n3. Check dependencies",
        "created_at": "2023-08-01T10:00:00Z",
        "updated_at": "2023-08-01T10:00:00Z",
        "entities": [
            {
                "id": "test-entity-1",
                "name": "test-pod",
                "type": "KUBERNETES_POD",
                "metadata": {
                    "namespace": "default",
                    "cluster": "test-cluster"
                }
            }
        ],
        "metrics": [
            {
                "name": "cpu_usage",
                "data": [
                    {
                        "timestamp": "2023-08-01T09:55:00Z",
                        "value": 85.5
                    }
                ]
            }
        ],
        "likely_causes": [
            "Application error",
            "Resource constraints"
        ],
        "tags": {
            "environment": "test",
            "team": "platform"
        }
    }
    
    # Insert the test incident
    incident_id = db.create_incident(test_incident)
    logger.info(f"Created test incident with ID: {incident_id}")
    
    # Add investigation steps
    step1 = {
        "step_number": 1,
        "title": "Alert Analysis",
        "content": "Analyzed the alert and identified a pod in CrashLoopBackOff state",
        "timestamp": "2023-08-01T10:05:00Z"
    }
    
    step2 = {
        "step_number": 2,
        "title": "Metrics Analysis",
        "content": "Analyzed metrics and found high CPU usage",
        "timestamp": "2023-08-01T10:10:00Z"
    }
    
    db.add_investigation_step(incident_id, step1)
    db.add_investigation_step(incident_id, step2)
    
    # Add timeline events
    event1 = {
        "event_type": "ALERT",
        "title": "Alert Triggered",
        "description": "New Relic alert triggered for pod in CrashLoopBackOff",
        "timestamp": "2023-08-01T10:00:00Z",
        "source": "New Relic"
    }
    
    event2 = {
        "event_type": "ANALYSIS",
        "title": "Root Cause Identified",
        "description": "Identified resource constraints as the root cause",
        "timestamp": "2023-08-01T10:15:00Z",
        "source": "AI Analysis"
    }
    
    db.add_timeline_event(incident_id, event1)
    db.add_timeline_event(incident_id, event2)
    
    logger.info("Test data created successfully.")

def main():
    """Main function."""
    # Check if .env file exists
    env_file = project_root / ".env"
    if not env_file.exists():
        logger.warning(f".env file not found at {env_file}. Using default values.")
    
    # Load environment variables
    dotenv.load_dotenv(str(env_file))
    
    # Check if PostgreSQL is available
    if not check_postgres():
        logger.error("PostgreSQL is not available. Please make sure it's running and configured correctly.")
        sys.exit(1)
    
    # Initialize database
    init_database()
    
    # Create test data if requested
    if "--with-test-data" in sys.argv:
        create_test_data()
    
    logger.info("Database initialization completed successfully.")

if __name__ == "__main__":
    main() 