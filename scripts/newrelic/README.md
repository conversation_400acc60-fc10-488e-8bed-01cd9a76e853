# New Relic Alerts Formatter

This tool converts raw New Relic alert conditions to human-readable format using Azure OpenAI and exports them to Excel with proper formatting.

## Features

- Takes raw New Relic API response data as input
- Uses Azure OpenAI to convert technical alert conditions to human-readable language
- Formats the output in Excel with color coding based on severity
- Handles different New Relic API response structures

## Prerequisites

- Python 3.8+
- Azure OpenAI API access
- The following environment variables set:
  - `AZURE_OPENAI_API_KEY`
  - `AZURE_OPENAI_ENDPOINT`
  - `AZURE_OPENAI_DEPLOYMENT_NAME`

## Installation

This project uses `rye` for package management. Make sure you have `rye` installed.

```bash
# From the root of the repository
rye add pandas openpyxl openai
```

## Usage

```bash
# Run the script with input and output paths
python scripts/newrelic/newrelic_alerts_formatter.py --input path/to/newrelic_conditions.json --output path/to/output.xlsx
```

## Example Input

The input should be a JSON file containing New Relic alert conditions. The script handles different JSON structures, including:

- A list of condition objects
- An object with a `conditions` key containing a list of condition objects
- An object with a `data` key containing a list of condition objects

## Output

The script generates an Excel file with the following columns:
- Alert Name
- Entity
- Alert Type
- Human Readable Condition
- Severity

The rows are color-coded based on severity:
- Critical alerts: Light red background
- Warning alerts: Light yellow background
