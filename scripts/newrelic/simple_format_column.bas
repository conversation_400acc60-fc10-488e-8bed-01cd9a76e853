Attribute VB_Name = "SimpleFormatColumn"
Option Explicit

Sub FormatHumanReadableColumn()
    ' Simple script to format just one column with colors based on cell content
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim colNum As Integer
    Dim i As Long
    
    ' Use active sheet
    Set ws = ActiveSheet
    
    ' Ask for column number (simpler than trying to find it)
    colNum = InputBox("Enter the column number where Human Readable text is located (e.g., 4 for column D):", "Column Selection")
    
    ' Validate input
    If colNum < 1 Then
        MsgBox "Invalid column number. Operation cancelled.", vbExclamation
        Exit Sub
    End If
    
    ' Find last row with data in that column
    lastRow = ws.Cells(ws.Rows.Count, colNum).End(xlUp).Row
    
    ' Format header (row 1)
    With ws.Cells(1, colNum)
        .Interior.Color = RGB(68, 114, 196)  ' Blue
        .Font.Color = RGB(255, 255, 255)     ' White
        .Font.Bold = True
        .Borders.LineStyle = xlContinuous
        .WrapText = True
    End With
    
    ' Format each data cell based on content
    For i = 2 To lastRow
        ' Format the cell
        With ws.Cells(i, colNum)
            ' Check for "critical alert" text
            If InStr(1, .Value, "critical alert", vbTextCompare) > 0 Then
                .Interior.Color = RGB(248, 203, 173)  ' Light red/peach
            Else
                .Interior.Color = RGB(255, 235, 156)  ' Light yellow
            End If
            
            ' Apply borders and text wrapping
            .Borders.LineStyle = xlContinuous
            .WrapText = True
        End With
    Next i
    
    ' Set column width
    ws.Columns(colNum).ColumnWidth = 60
    
    ' Auto-fit rows for better readability
    ws.Rows.AutoFit
    
    MsgBox "Column formatting completed!", vbInformation
End Sub

Sub ClearColumnFormatting()
    ' Simple script to clear formatting from a column
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim colNum As Integer
    
    ' Use active sheet
    Set ws = ActiveSheet
    
    ' Ask for column number 
    colNum = InputBox("Enter the column number to clear formatting (e.g., 4 for column D):", "Column Selection")
    
    ' Validate input
    If colNum < 1 Then
        MsgBox "Invalid column number. Operation cancelled.", vbExclamation
        Exit Sub
    End If
    
    ' Find last row with data in that column
    lastRow = ws.Cells(ws.Rows.Count, colNum).End(xlUp).Row
    
    ' Clear formatting
    ws.Range(ws.Cells(1, colNum), ws.Cells(lastRow, colNum)).ClearFormats
    
    MsgBox "Formatting cleared from column " & colNum, vbInformation
End Sub
