Attribute VB_Name = "FormatStructuredText"
Option Explicit

Sub CombineAndFormatText()
    ' This script combines the structured text components from a CSV import
    ' and formats each component with appropriate colors
    
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim targetCol As Integer
    Dim i As Long
    
    ' Use active sheet
    Set ws = ActiveSheet
    
    ' Find the last row with data
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    
    ' Ask for the target column where to place the formatted text
    targetCol = InputBox("Enter the column number where you want to place the formatted text (e.g., 11 for column K):", "Target Column")
    
    ' Validate input
    If targetCol < 1 Then
        MsgBox "Invalid column number. Operation cancelled.", vbExclamation
        Exit Sub
    End If
    
    ' Add a header for the new column
    ws.Cells(1, targetCol).Value = "Formatted Human Readable Text"
    ws.Cells(1, targetCol).Font.Bold = True
    ws.Cells(1, targetCol).Interior.Color = RGB(31, 78, 120)  ' Dark blue header
    ws.Cells(1, targetCol).Font.Color = RGB(255, 255, 255)     ' White
    ws.Cells(1, targetCol).Borders.LineStyle = xlContinuous
    
    ' Find column indices for each part (assuming they match the components we exported)
    Dim colPrefix As Integer       ' "When"
    Dim colMetricName As Integer   ' "metric_name"
    Dim colEntityPart As Integer   ' "entity_part"
    Dim colCondition As Integer    ' "condition"
    Dim colThreshold As Integer    ' "threshold"
    Dim colDuration As Integer     ' "duration"
    Dim colSeverity As Integer     ' "severity"
    
    ' Look for expected headers (case insensitive)
    For i = 1 To 20 ' Check up to column T
        Select Case LCase(ws.Cells(1, i).Value)
            Case "prefix"
                colPrefix = i
            Case "metric name"
                colMetricName = i
            Case "entity part"
                colEntityPart = i
            Case "condition"
                colCondition = i
            Case "threshold"
                colThreshold = i
            Case "duration"
                colDuration = i
            Case "severity"
                colSeverity = i
        End Select
    Next i
    
    ' If any column wasn't found, ask the user for it
    If colPrefix = 0 Then
        colPrefix = CInt(InputBox("Enter column number for 'Prefix' (When):", "Column Selection", "4"))
    End If
    If colMetricName = 0 Then
        colMetricName = CInt(InputBox("Enter column number for 'Metric Name':", "Column Selection", "5"))
    End If
    If colEntityPart = 0 Then
        colEntityPart = CInt(InputBox("Enter column number for 'Entity Part':", "Column Selection", "6"))
    End If
    If colCondition = 0 Then
        colCondition = CInt(InputBox("Enter column number for 'Condition':", "Column Selection", "7"))
    End If
    If colThreshold = 0 Then
        colThreshold = CInt(InputBox("Enter column number for 'Threshold':", "Column Selection", "8"))
    End If
    If colDuration = 0 Then
        colDuration = CInt(InputBox("Enter column number for 'Duration':", "Column Selection", "9"))
    End If
    If colSeverity = 0 Then
        colSeverity = CInt(InputBox("Enter column number for 'Severity':", "Column Selection", "10"))
    End If
    
    ' Turn off screen updating to improve performance
    Application.ScreenUpdating = False
    
    ' Process each row
    For i = 2 To lastRow
        ' Check first if we have all components
        If ws.Cells(i, colPrefix).Value <> "" Then
            ' Get component values
            Dim prefix As String
            Dim metricName As String
            Dim entityPart As String
            Dim condition As String
            Dim threshold As String
            Dim duration As String
            Dim severity As String
            
            prefix = ws.Cells(i, colPrefix).Value
            metricName = ws.Cells(i, colMetricName).Value
            entityPart = ws.Cells(i, colEntityPart).Value
            condition = ws.Cells(i, colCondition).Value
            threshold = ws.Cells(i, colThreshold).Value
            duration = ws.Cells(i, colDuration).Value
            severity = ws.Cells(i, colSeverity).Value
            
            ' Check the severity to determine row color
            Dim rowColor As Long
            If InStr(1, severity, "critical", vbTextCompare) > 0 Then
                rowColor = RGB(248, 203, 173)  ' Light red/peach for critical
            Else
                rowColor = RGB(255, 235, 156)  ' Light yellow for warning
            End If
            
            ' Set the cell background color and properties
            With ws.Cells(i, targetCol)
                .Interior.Color = rowColor
                .Borders.LineStyle = xlContinuous
                .WrapText = True
                .VerticalAlignment = xlCenter
                
                ' Clear any existing value
                .Value = ""
            End With
            
            ' Format and add each component with appropriate color
            ' Start with "When"
            ws.Cells(i, targetCol).Characters(1, 0).Insert prefix & " "
            ws.Cells(i, targetCol).Characters(1, Len(prefix)).Font.Bold = True
            
            ' Add metric name in blue
            Dim currentPos As Long
            currentPos = Len(ws.Cells(i, targetCol).Value) + 1
            ws.Cells(i, targetCol).Characters(currentPos, 0).Insert metricName & " "
            ws.Cells(i, targetCol).Characters(currentPos, Len(metricName)).Font.Color = RGB(0, 112, 192) ' Blue
            ws.Cells(i, targetCol).Characters(currentPos, Len(metricName)).Font.Bold = True
            
            ' Add entity part in italic
            currentPos = Len(ws.Cells(i, targetCol).Value) + 1
            ws.Cells(i, targetCol).Characters(currentPos, 0).Insert entityPart & " "
            ws.Cells(i, targetCol).Characters(currentPos, Len(entityPart)).Font.Italic = True
            
            ' Add condition part in bold
            currentPos = Len(ws.Cells(i, targetCol).Value) + 1
            ws.Cells(i, targetCol).Characters(currentPos, 0).Insert condition & " "
            ws.Cells(i, targetCol).Characters(currentPos, Len(condition)).Font.Bold = True
            
            ' Add threshold in red and bold
            currentPos = Len(ws.Cells(i, targetCol).Value) + 1
            ws.Cells(i, targetCol).Characters(currentPos, 0).Insert threshold & " "
            ws.Cells(i, targetCol).Characters(currentPos, Len(threshold)).Font.Color = RGB(192, 0, 0) ' Red
            ws.Cells(i, targetCol).Characters(currentPos, Len(threshold)).Font.Bold = True
            
            ' Add duration
            currentPos = Len(ws.Cells(i, targetCol).Value) + 1
            ws.Cells(i, targetCol).Characters(currentPos, 0).Insert duration & " "
            
            ' Add "raise a critical/warning alert" in red
            currentPos = Len(ws.Cells(i, targetCol).Value) + 1
            Dim alertText As String
            alertText = "raise a " & severity & " alert"
            ws.Cells(i, targetCol).Characters(currentPos, 0).Insert alertText
            ws.Cells(i, targetCol).Characters(currentPos, Len(alertText)).Font.Color = RGB(192, 0, 0) ' Red
            ws.Cells(i, targetCol).Characters(currentPos, Len(alertText)).Font.Bold = True
        End If
    Next i
    
    ' Set column width
    ws.Columns(targetCol).ColumnWidth = 60
    ws.Rows.AutoFit
    
    ' Turn screen updating back on
    Application.ScreenUpdating = True
    
    MsgBox "Formatting complete! Text components have been combined and formatted.", vbInformation
End Sub

Sub ApplyFormattingToFullText()
    ' This is an alternative approach that formats existing full text in a column
    ' by searching for patterns rather than combining separate components
    
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim textCol As Integer
    Dim i As Long
    
    ' Use active sheet
    Set ws = ActiveSheet
    
    ' Ask for the column containing the text to format
    textCol = InputBox("Enter the column number containing the text to format (e.g., 4 for column D):", "Column Selection")
    
    ' Validate input
    If textCol < 1 Then
        MsgBox "Invalid column number. Operation cancelled.", vbExclamation
        Exit Sub
    End If
    
    ' Find the last row with data
    lastRow = ws.Cells(ws.Rows.Count, textCol).End(xlUp).Row
    
    ' Format header
    With ws.Cells(1, textCol)
        .Interior.Color = RGB(31, 78, 120)  ' Dark blue header
        .Font.Color = RGB(255, 255, 255)     ' White
        .Font.Bold = True
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
    End With
    
    ' Turn off screen updating to improve performance
    Application.ScreenUpdating = False
    
    ' Process each row
    For i = 2 To lastRow
        Dim cellText As String
        cellText = ws.Cells(i, textCol).Value
        
        ' Skip empty cells
        If Len(cellText) = 0 Then GoTo NextCell
        
        ' Set row background color based on "critical alert" presence
        If InStr(1, cellText, "critical alert", vbTextCompare) > 0 Then
            ws.Cells(i, textCol).Interior.Color = RGB(248, 203, 173)  ' Light red/peach
        Else
            ws.Cells(i, textCol).Interior.Color = RGB(255, 235, 156)  ' Light yellow
        End If
        
        ' Apply borders, word wrap, and vertical alignment
        With ws.Cells(i, textCol)
            .Borders.LineStyle = xlContinuous
            .WrapText = True
            .VerticalAlignment = xlCenter
        End With
        
        ' Reset font formatting for the entire cell
        ws.Cells(i, textCol).Font.Color = RGB(0, 0, 0)  ' Black
        ws.Cells(i, textCol).Font.Bold = False
        ws.Cells(i, textCol).Font.Italic = False
        
        ' Apply character-level formatting for specific patterns
        
        ' Format "When" at the beginning
        If Left(cellText, 4) = "When" Then
            ws.Cells(i, textCol).Characters(1, 4).Font.Bold = True
        End If
        
        ' Find and format metric names - common patterns from the image
        Dim metricPatterns As Variant
        metricPatterns = Array("number of database connections", "free/totalSpace", _
                          "disk_read/sec", "network throughput", "memory consumption", _
                          "container restarts", "disk utilization", "CPU utilization", _
                          "percentage of unavailable pods", "CPU IOWait", "float_table_wasted_pages")
        
        Dim pattern As Variant
        For Each pattern In metricPatterns
            If InStr(1, cellText, pattern, vbTextCompare) > 0 Then
                Dim startPos As Long
                startPos = InStr(1, cellText, pattern, vbTextCompare)
                ws.Cells(i, textCol).Characters(startPos, Len(pattern)).Font.Color = RGB(0, 112, 192) ' Blue
                ws.Cells(i, textCol).Characters(startPos, Len(pattern)).Font.Bold = True
            End If
        Next pattern
        
        ' Format entity parts
        Dim entityPatterns As Variant
        entityPatterns = Array(" on RDS instance", " on M3", " on system", " on Pod", _
                           " on Kubernetes", " on cluster", " on K8s", " on NAT-")
        
        For Each pattern In entityPatterns
            If InStr(1, cellText, pattern, vbTextCompare) > 0 Then
                startPos = InStr(1, cellText, pattern, vbTextCompare)
                ws.Cells(i, textCol).Characters(startPos, Len(pattern)).Font.Italic = True
            End If
        Next pattern
        
        ' Format threshold conditions
        Dim conditionPatterns As Variant
        conditionPatterns = Array(" is above ", " is below ", " is over ", " exceeds ", " is not equal to ")
        
        For Each pattern In conditionPatterns
            If InStr(1, cellText, pattern, vbTextCompare) > 0 Then
                startPos = InStr(1, cellText, pattern, vbTextCompare)
                ws.Cells(i, textCol).Characters(startPos, Len(pattern)).Font.Bold = True
            End If
        Next pattern
        
        ' Format numeric thresholds with units
        Dim thresholdPatterns As Variant
        thresholdPatterns = Array(" 0 for ", " 10 for ", " 20% for ", " 25% for ", " 30 ", _
                              " 75 ", " 80 ", " 90% ", " 95% ", " 120k ", " 300 ", " 750kb ")
        
        For Each pattern In thresholdPatterns
            If InStr(1, cellText, pattern, vbTextCompare) > 0 Then
                startPos = InStr(1, cellText, pattern, vbTextCompare)
                ws.Cells(i, textCol).Characters(startPos, Len(pattern)).Font.Color = RGB(192, 0, 0) ' Red
                ws.Cells(i, textCol).Characters(startPos, Len(pattern)).Font.Bold = True
            End If
        Next pattern
        
        ' Format "raise a critical alert" and "raise a warning alert"
        Dim alertPatterns As Variant
        alertPatterns = Array("raise a critical alert", "raise a warning alert", "raise a virtual alert")
        
        For Each pattern In alertPatterns
            If InStr(1, cellText, pattern, vbTextCompare) > 0 Then
                startPos = InStr(1, cellText, pattern, vbTextCompare)
                ws.Cells(i, textCol).Characters(startPos, Len(pattern)).Font.Color = RGB(192, 0, 0) ' Red
                ws.Cells(i, textCol).Characters(startPos, Len(pattern)).Font.Bold = True
            End If
        Next pattern
        
NextCell:
    Next i
    
    ' Set column width
    ws.Columns(textCol).ColumnWidth = 60
    
    ' Auto-fit row heights
    ws.Rows.AutoFit
    
    ' Turn screen updating back on
    Application.ScreenUpdating = True
    
    MsgBox "Text formatting completed!", vbInformation
End Sub
