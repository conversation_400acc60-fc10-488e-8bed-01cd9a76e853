Attribute VB_Name = "FormatNewRelicAlerts"
Option Explicit

Sub FormatNewRelicAlerts()
    ' Format New Relic Alerts with proper colors
    Dim ws As Worksheet
    Dim lastRow As Long, lastCol As Long
    Dim headerRange As Range, dataRange As Range
    Dim cell As Range
    Dim i As Long, j <PERSON> Long
    
    ' Get the active worksheet
    Set ws = ActiveSheet
    
    ' Find the last used row and column
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    lastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column
    
    ' Define the header range and data range
    Set headerRange = ws.Range(ws.Cells(1, 1), ws.Cells(1, lastCol))
    Set dataRange = ws.Range(ws.Cells(2, 1), ws.Cells(lastRow, lastCol))
    
    ' Format header row - Blue with white text
    With headerRange
        .Interior.Color = RGB(68, 114, 196)  ' Blue header (4472C4)
        .Font.Color = RGB(255, 255, 255)     ' White text
        .Font.Bold = True
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
    End With
    
    ' Format data rows based on severity
    For i = 2 To lastRow
        ' Check if 'critical' appears in the Severity column (assumed to be column E or 5)
        Dim severity As String
        severity = LCase(ws.Cells(i, 5).Value)
        
        ' Set row color based on severity
        Dim rowRange As Range
        Set rowRange = ws.Range(ws.Cells(i, 1), ws.Cells(i, lastCol))
        
        If InStr(severity, "critical") > 0 Then
            ' Critical alerts - Light red/peach
            rowRange.Interior.Color = RGB(248, 203, 173)  ' F8CBAD
        Else
            ' Warning alerts - Light yellow
            rowRange.Interior.Color = RGB(255, 235, 156)  ' FFEB9C
        End If
        
        ' Apply borders and text alignment
        With rowRange
            .Borders.LineStyle = xlContinuous
            .Borders.Weight = xlThin
            .VerticalAlignment = xlCenter
        End With
        
        ' Enable text wrapping for the Human Readable Condition column (assumed to be column D or 4)
        ws.Cells(i, 4).WrapText = True
    Next i
    
    ' Adjust column widths
    ws.Columns("A").ColumnWidth = 25  ' Alert Name
    ws.Columns("B").ColumnWidth = 20  ' Entity
    ws.Columns("C").ColumnWidth = 15  ' Alert Type
    ws.Columns("D").ColumnWidth = 60  ' Human Readable Condition
    ws.Columns("E").ColumnWidth = 15  ' Severity
    
    ' Auto-fit row heights
    ws.Rows.AutoFit
    
    MsgBox "New Relic alerts formatting completed successfully!", vbInformation
End Sub

' Add a clean up formatting function that removes all formatting for testing
Sub ClearAllFormatting()
    ' Clear all formatting from the active worksheet
    Dim ws As Worksheet
    Set ws = ActiveSheet
    
    ' Clear formatting but keep data
    ws.Cells.ClearFormats
    
    MsgBox "All formatting has been cleared!", vbInformation
End Sub
