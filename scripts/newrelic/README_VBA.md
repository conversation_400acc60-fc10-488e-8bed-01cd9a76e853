# Excel VBA Formatting for New Relic Alerts

Since the Python formatting isn't working as expected, I've provided a VBA script that you can use to format the Excel file directly.

## Instructions for Using the VBA Script

1. First, run the Python script to generate the Excel file with the data:
   ```bash
   python scripts/newrelic/newrelic_alerts_formatter.py --input scripts/newrelic/policy_conditions.json --output scripts/newrelic/formatted_alerts.xlsx
   ```

2. Open the generated Excel file in Microsoft Excel

3. Access the VBA editor:
   - Press `Alt + F11` (Windows) or `Option + F11` (Mac)
   - Or go to Developer tab > Visual Basic

4. Import the VBA module:
   - In the VBA editor, go to File > Import File
   - Select the `format_alerts_excel.bas` file

5. Run the macro:
   - In the VBA editor, double-click the imported module
   - Place your cursor inside the `FormatNewRelicAlerts` subroutine
   - Press F5 or click the Run button (▶)
   - Or return to Excel and run from Developer tab > Macros

## What the VBA Script Does

The VBA script:
1. Formats the header row with a blue background and white text
2. Colors critical alert rows with light red/peach (RGB: 248, 203, 173)
3. Colors warning alert rows with light yellow (RGB: 255, 235, 156)
4. Adds borders to all cells
5. Sets appropriate column widths
6. Enables text wrapping for the "Human Readable Condition" column

## Troubleshooting

If you need to remove all formatting to try again:
1. Run the `ClearAllFormatting` subroutine in the VBA module
2. Then run the `FormatNewRelicAlerts` subroutine again

## Security Note

If you get a security warning about macros, you'll need to enable macros for this workbook to run the formatting script.
