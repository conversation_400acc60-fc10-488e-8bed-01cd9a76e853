Attribute VB_Name = "FormatHumanReadableColumn"
Option Explicit

Sub FormatHumanReadableColumn()
    ' This script formats only the Human Readable column based on keywords
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim cell As Range
    Dim humanReadableCol As Range
    Dim headerCell As Range
    Dim colNum As Integer
    Dim foundCol As Boolean
    
    ' Get the active worksheet
    Set ws = ActiveSheet
    
    ' Find the column with "Human Readable Condition" header
    foundCol = False
    For colNum = 1 To 20  ' Check first 20 columns
        If InStr(1, ws.Cells(1, colNum).Value, "Human Readable", vbTextCompare) > 0 Then
            foundCol = True
            Exit For
        End If
    Next colNum
    
    ' If not found, ask user to select the column
    If Not foundCol Then
        MsgBox "Column with 'Human Readable' not found automatically. Please select the column manually.", vbInformation
        On Error Resume Next
        Set headerCell = Application.InputBox("Click on the header cell of the Human Readable column", Type:=8)
        On Error GoTo 0
        
        If headerCell Is Nothing Then
            MsgBox "Operation cancelled", vbExclamation
            Exit Sub
        End If
        
        colNum = headerCell.Column
    End If
    
    ' Find the last row with data
    lastRow = ws.Cells(ws.Rows.Count, colNum).End(xlUp).Row
    
    ' Format the header cell
    Set headerCell = ws.Cells(1, colNum)
    With headerCell
        .Interior.Color = RGB(68, 114, 196)  ' Blue header (4472C4)
        .Font.Color = RGB(255, 255, 255)     ' White text
        .Font.Bold = True
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
    End With
    
    ' Get the range for all cells in the Human Readable column (excluding header)
    Set humanReadableCol = ws.Range(ws.Cells(2, colNum), ws.Cells(lastRow, colNum))
    
    ' Apply basic formatting to all cells in the column
    With humanReadableCol
        .WrapText = True
        .VerticalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
    End With
    
    ' Format individual cells based on content
    Dim criticalCount As Integer
    Dim warningCount As Integer
    
    criticalCount = 0
    warningCount = 0
    
    ' Process each cell in the column
    For Each cell In humanReadableCol
        ' Check for keywords in the text
        If InStr(1, cell.Value, "critical alert", vbTextCompare) > 0 Then
            cell.Interior.Color = RGB(248, 203, 173)  ' Light red/peach (F8CBAD)
            criticalCount = criticalCount + 1
        Else
            cell.Interior.Color = RGB(255, 235, 156)  ' Light yellow (FFEB9C)
            warningCount = warningCount + 1
        End If
    Next cell
    
    ' Set the column width to accommodate the content
    ws.Columns(colNum).ColumnWidth = 60
    
    ' Auto-fit row heights
    ws.Rows.AutoFit
    
    ' Show summary
    MsgBox "Formatting complete!" & vbNewLine & _
           "Critical alerts: " & criticalCount & vbNewLine & _
           "Warning alerts: " & warningCount, vbInformation
End Sub

Sub ClearHumanReadableFormatting()
    ' Clear formatting from just the Human Readable column
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim colNum As Integer
    Dim foundCol As Boolean
    Dim headerCell As Range
    
    ' Get the active worksheet
    Set ws = ActiveSheet
    
    ' Find the column with "Human Readable Condition" header
    foundCol = False
    For colNum = 1 To 20  ' Check first 20 columns
        If InStr(1, ws.Cells(1, colNum).Value, "Human Readable", vbTextCompare) > 0 Then
            foundCol = True
            Exit For
        End If
    Next colNum
    
    ' If not found, ask user to select the column
    If Not foundCol Then
        MsgBox "Column with 'Human Readable' not found automatically. Please select the column manually.", vbInformation
        On Error Resume Next
        Set headerCell = Application.InputBox("Click on the header cell of the Human Readable column", Type:=8)
        On Error GoTo 0
        
        If headerCell Is Nothing Then
            MsgBox "Operation cancelled", vbExclamation
            Exit Sub
        End If
        
        colNum = headerCell.Column
    End If
    
    ' Find the last row with data
    lastRow = ws.Cells(ws.Rows.Count, colNum).End(xlUp).Row
    
    ' Clear formatting from the column
    ws.Range(ws.Cells(1, colNum), ws.Cells(lastRow, colNum)).ClearFormats
    
    MsgBox "Formatting cleared from Human Readable column", vbInformation
End Sub
