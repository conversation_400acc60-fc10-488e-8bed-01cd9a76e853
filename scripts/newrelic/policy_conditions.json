{"policy": {"id": 4795537, "incident_preference": "PER_CONDITION_AND_TARGET", "name": "MI Alert - Infrastructure", "created_at": 1695465508601, "updated_at": 1695465508601}, "conditions": [{"id": 36670760, "entity_guid": "MTA5MzYyMHxBSU9QU3xDT05ESVRJT058MzY2NzA3NjA", "type": "static", "name": "Pod not Scheduled", "enabled": true, "value_function": "single_value", "violation_time_limit_seconds": 259200, "terms": [{"duration": "10", "operator": "above", "threshold": "0.0", "time_function": "all", "priority": "critical"}], "nrql": {"query": "SELECT count(*) FROM K8sPodSample FACET clusterName, podName  WHERE isScheduled IN (0, '0') AND product = 'polaris' and clusterName not like 'sre-staging-eks' AND clusterName not like 'sre-global-%' AND clusterName NOT LIKE '%mdm%-aks-instance%'"}, "signal": {"aggregation_window": "60", "aggregation_method": "EVENT_FLOW", "aggregation_delay": 120, "fill_option": "none"}, "description": "Pod not Scheduled for cluster {{ tags.clusterName }}, Pod {{ tags.podName }}", "data_account_id": 1093620}, {"id": ********, "entity_guid": "*******************************************", "type": "static", "name": "OOM triggered on Pod", "enabled": true, "value_function": "single_value", "violation_time_limit_seconds": 259200, "terms": [{"duration": "30", "operator": "above", "threshold": "0.0", "time_function": "all", "priority": "critical"}], "nrql": {"query": "SELECT count(*) FROM K8sContainerSample FACET clusterName, podName, containerName WHERE reason = 'OOMKilled' and product = 'polaris' AND  clusterName NOT LIKE '%mdm%-aks-instance%'"}, "signal": {"aggregation_window": "60", "aggregation_method": "EVENT_FLOW", "aggregation_delay": 120, "fill_option": "none"}, "description": "OOM triggered on Pod for cluster {{ tags.clusterName }}, Pod {{ tags.podName }}", "data_account_id": 1093620}, {"id": ********, "entity_guid": "*******************************************", "type": "static", "name": "Pod with CrashLoopBackOff", "enabled": true, "value_function": "single_value", "violation_time_limit_seconds": 259200, "terms": [{"duration": "45", "operator": "above", "threshold": "10.0", "time_function": "all", "priority": "critical"}], "nrql": {"query": "SELECT count(*) FROM K8sContainerSample FACET clusterName, podName, containerName  WHERE reason = 'CrashLoopBackOff' AND status = 'Waiting' AND clusterName NOT LIKE '%mdm%-aks-instance%' AND clusterName NOT LIKE '%staging%' AND product = 'polaris'"}, "signal": {"aggregation_window": "900", "aggregation_method": "EVENT_FLOW", "aggregation_delay": 120, "fill_option": "none"}, "description": "Pod with CrashLoopBackOff for cluster {{ tags.clusterName }}, Pod {{ tags.podName }}", "data_account_id": 1093620}, {"id": ********, "entity_guid": "*******************************************", "type": "static", "name": "Cluster Node in Disk Pressure State", "enabled": true, "value_function": "single_value", "violation_time_limit_seconds": 259200, "terms": [{"duration": "5", "operator": "not_equals", "threshold": "0.0", "time_function": "all", "priority": "critical"}], "nrql": {"query": "SELECT average(`condition.DiskPressure`) FROM K8sNodeSample WHERE product = 'polaris' AND clusterName NOT LIKE '%mdm%-aks-instance%' FACET clusterName, nodeName "}, "signal": {"aggregation_window": "60", "aggregation_method": "EVENT_FLOW", "aggregation_delay": 120, "fill_option": "none"}, "description": "Cluster Node in Disk Pressure State for cluster {{ tags.clusterName }}, Node {{ tags.nodeName }}", "data_account_id": 1093620}, {"id": ********, "entity_guid": "*******************************************", "type": "static", "name": "Disk Utilization is high for system", "enabled": true, "value_function": "single_value", "violation_time_limit_seconds": 259200, "terms": [{"duration": "30", "operator": "above", "threshold": "95.0", "time_function": "all", "priority": "critical"}], "nrql": {"query": "SELECT average(`host.diskUtilizationPercent`) FROM Metric WHERE product = 'polaris' AND clusterName NOT LIKE '%mdm%-aks-instance%'FACET `host.fullHostname`, product, clusterName"}, "signal": {"aggregation_window": "60", "aggregation_method": "EVENT_FLOW", "aggregation_delay": 120, "fill_option": "none"}, "description": "Disk Utilization is high for system {{ tags.host.fullHostname }} of {{ tags. product }}, clustername: {{ tags.clusterName }}", "data_account_id": 1093620}, {"id": ********, "entity_guid": "*******************************************", "type": "static", "name": "CPU Utilization is high for system", "enabled": true, "value_function": "single_value", "violation_time_limit_seconds": 259200, "terms": [{"duration": "5", "operator": "above", "threshold": "90.0", "time_function": "all", "priority": "critical"}], "nrql": {"query": "SELECT average(`host.cpuSystemPercent`) FROM Metric WHERE product = 'polaris' AND clusterName NOT LIKE '%mdm%-aks-instance%' FACET `host.fullHostname`,  product, clusterName"}, "signal": {"aggregation_window": "60", "aggregation_method": "EVENT_FLOW", "aggregation_delay": 120, "fill_option": "none"}, "description": "CPU Utilization is high for system {{ tags.host.fullHostname }} of {{ tags. product }}, clustername: {{ tags.clusterName }}", "data_account_id": 1093620}, {"id": ********, "entity_guid": "*******************************************", "type": "static", "name": "Percentage of Unavailable pods > 25%", "enabled": true, "value_function": "single_value", "violation_time_limit_seconds": 259200, "terms": [{"duration": "30", "operator": "above", "threshold": "25.0", "time_function": "all", "priority": "critical"}], "nrql": {"query": "SELECT average(podsUnavailable/podsDesired)*100 FROM K8sDeploymentSample WHERE product = 'polaris' AND  clusterName LIKE 'primary-%-eks'  FACET clusterName, deploymentName"}, "signal": {"aggregation_window": "60", "aggregation_method": "EVENT_FLOW", "aggregation_delay": 120, "fill_option": "none"}, "description": "Percentage of Unavailable pods > 25% for cluster {{ tags.clusterName }}, deployment {{ tags.deploymentName }}", "data_account_id": 1093620}, {"id": ********, "entity_guid": "*******************************************", "type": "static", "name": "CPU IOWait high for system", "enabled": true, "value_function": "single_value", "violation_time_limit_seconds": 259200, "terms": [{"duration": "15", "operator": "above", "threshold": "20.0", "time_function": "all", "priority": "critical"}], "nrql": {"query": "SELECT average(`host.cpuIoWaitPercent`) FROM Metric WHERE product = 'polaris' AND clusterName NOT LIKE '%mdm%-aks-instance%' FACET `host.fullHostname`, product, clusterName"}, "signal": {"aggregation_window": "60", "aggregation_method": "EVENT_FLOW", "aggregation_delay": 120, "fill_option": "none"}, "description": "CPU IOWait is high for system {{ tags.host.fullHostname }} of {{ tags. product }}, clustername: {{ tags.clusterName }}", "data_account_id": 1093620}, {"id": ********, "entity_guid": "*******************************************", "type": "static", "name": "Jasper memory consumption is High", "enabled": true, "value_function": "single_value", "violation_time_limit_seconds": 259200, "terms": [{"duration": "5", "operator": "above", "threshold": "1.2E10", "time_function": "all", "priority": "critical"}], "nrql": {"query": "SELECT average(`k8s.container.memoryUsedBytes`) FROM Metric where k8s.containerName = 'jasperreports' AND clusterName NOT LIKE '%mdm%-aks-instance%' FACET k8s.clusterName"}, "signal": {"aggregation_window": "60", "aggregation_method": "EVENT_FLOW", "aggregation_delay": 120, "fill_option": "none"}, "description": "Jasper memory consumption is Higher than *********** on {{ tags.k8s.clusterName }}", "data_account_id": 1093620}, {"id": ********, "entity_guid": "*******************************************", "type": "static", "name": "MI Container restarts > 10 in 15 minutes", "enabled": true, "value_function": "single_value", "violation_time_limit_seconds": 259200, "terms": [{"duration": "15", "operator": "above", "threshold": "10.0", "time_function": "all", "priority": "critical"}], "nrql": {"query": "from K8sContainerSample SELECT sum(restartCountDelta) WHERE clusterName IN ('primary-ap1-eks', 'staging-global-us-east-1-51-ControlPlane-EKSControlPlane', 'primary-ap2-eks-2', 'primary-na1-eks', 'primary-na2-eks') AND product = 'polaris' FACET clusterName, containerName "}, "signal": {"aggregation_window": "60", "aggregation_method": "EVENT_FLOW", "aggregation_delay": 120, "fill_option": "none"}, "description": "Container restarts greater than 10 in 15 minutes for the container: {{tags.containerName}}, cluster: {{tags.clusterName}}", "data_account_id": 1093620}]}