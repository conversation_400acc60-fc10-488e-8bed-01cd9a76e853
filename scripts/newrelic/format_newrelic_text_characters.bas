Attribute VB_Name = "FormatNewRelicTextCharacters"
Option Explicit

Sub FormatNewRelicHumanReadableText()
    ' This script formats the Human Readable text at the CHARACTER LEVEL
    ' using the Characters method to highlight specific parts of text
    
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim colNum As Integer
    Dim i As Long
    Dim cellText As String
    Dim startPos As Long, textLen As Long
    
    ' Use active sheet
    Set ws = ActiveSheet
    
    ' Ask for column number
    colNum = InputBox("Enter the column number where Human Readable text is located (e.g., 4 for column D):", "Column Selection")
    
    ' Validate input
    If colNum < 1 Then
        MsgBox "Invalid column number. Operation cancelled.", vbExclamation
        Exit Sub
    End If
    
    ' Find last row with data in that column
    lastRow = ws.Cells(ws.Rows.Count, colNum).End(xlUp).Row
    
    ' Format header (row 1)
    With ws.Cells(1, colNum)
        .Interior.Color = RGB(31, 78, 120)  ' Dark blue header
        .Font.Color = RGB(255, 255, 255)     ' White
        .Font.Bold = True
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
    End With
    
    ' Format cells and text within them
    Application.ScreenUpdating = False ' Speed up execution
    
    For i = 2 To lastRow
        cellText = ws.Cells(i, colNum).Value
        
        ' Skip if cell is empty
        If Len(cellText) = 0 Then GoTo NextCell
        
        ' Set base cell formatting
        With ws.Cells(i, colNum)
            ' Determine background color based on "critical alert" presence
            If InStr(1, cellText, "critical alert", vbTextCompare) > 0 Then
                .Interior.Color = RGB(248, 203, 173)  ' Light red/peach
            Else
                .Interior.Color = RGB(255, 235, 156)  ' Light yellow
            End If
            
            ' Apply borders and text formatting
            .Borders.LineStyle = xlContinuous
            .WrapText = True
            .VerticalAlignment = xlCenter
            
            ' Reset font color and style for the entire cell
            .Font.Color = RGB(0, 0, 0)  ' Black
            .Font.Bold = False
        End With
        
        ' Now apply character-level formatting to specific parts of text
        ' FORMAT "When"
        If InStr(1, cellText, "When", vbTextCompare) > 0 Then
            startPos = InStr(1, cellText, "When", vbTextCompare)
            textLen = 4 ' Length of "When"
            ws.Cells(i, colNum).Characters(startPos, textLen).Font.Bold = True
        End If
        
        ' FORMAT metric names (look for specific patterns seen in the image)
        Dim metricPatterns As Variant
        metricPatterns = Array("number of database connections", "free/totalSpace", _
                            "disk_read/sec", "network throughput", "memory consumption", _
                            "container restarts", "disk utilization", "CPU utilization", _
                            "percentage of unavailable pods", "CPU IOWait", _
                            "float_table_wasted_pages")
        
        Dim pattern As Variant
        For Each pattern In metricPatterns
            If InStr(1, cellText, pattern, vbTextCompare) > 0 Then
                startPos = InStr(1, cellText, pattern, vbTextCompare)
                textLen = Len(pattern)
                ws.Cells(i, colNum).Characters(startPos, textLen).Font.Color = RGB(0, 112, 192) ' Blue
                ws.Cells(i, colNum).Characters(startPos, textLen).Font.Bold = True
            End If
        Next pattern
        
        ' FORMAT "is above", "is below", "is over", "exceeds"
        Dim thresholdPatterns As Variant
        thresholdPatterns = Array("is above", "is below", "is over", "exceeds", "is not equal to")
        
        For Each pattern In thresholdPatterns
            If InStr(1, cellText, pattern, vbTextCompare) > 0 Then
                startPos = InStr(1, cellText, pattern, vbTextCompare)
                textLen = Len(pattern)
                ws.Cells(i, colNum).Characters(startPos, textLen).Font.Bold = True
            End If
        Next pattern
        
        ' FORMAT numeric values with units (numbers followed by units like gb, mb, % etc.)
        ' This uses a more complex approach to find patterns like "300 for", "80 gb", "90%", "750kb"
        Dim regexPattern As String
        regexPattern = "[0-9]+(\.[0-9]+)?\s*(gb|mb|kb|%|seconds|minutes|hours|hour)"
        
        ' Since VBA doesn't have built-in regex, we'll use a simpler approach
        ' to find number patterns with common units
        Dim unitPatterns As Variant
        unitPatterns = Array(" gb ", " mb ", " kb ", " % ", " minutes ", " seconds ", " hours ", " hour ")
        
        Dim numPos As Long, spacePos As Long
        Dim j As Long
        
        ' Look for numbers followed by units
        For j = 0 To UBound(unitPatterns)
            If InStr(1, cellText, unitPatterns(j), vbTextCompare) > 0 Then
                ' Find the unit
                startPos = InStr(1, cellText, unitPatterns(j), vbTextCompare)
                
                ' Look backwards to find the number before the unit
                spacePos = InStrRev(cellText, " ", startPos - 1)
                If spacePos = 0 Then spacePos = 1
                
                ' Calculate positions
                numPos = spacePos
                textLen = startPos + Len(unitPatterns(j)) - numPos
                
                ' Apply formatting to the number and unit
                ws.Cells(i, colNum).Characters(numPos, textLen).Font.Color = RGB(192, 0, 0) ' Red
                ws.Cells(i, colNum).Characters(numPos, textLen).Font.Bold = True
            End If
        Next j
        
        ' FORMAT "raise a critical alert"
        If InStr(1, cellText, "raise a critical alert", vbTextCompare) > 0 Then
            startPos = InStr(1, cellText, "raise a critical alert", vbTextCompare)
            textLen = Len("raise a critical alert")
            ws.Cells(i, colNum).Characters(startPos, textLen).Font.Color = RGB(192, 0, 0) ' Red
            ws.Cells(i, colNum).Characters(startPos, textLen).Font.Bold = True
        End If
        
        ' FORMAT entity names like "RDS instance", "M3-Polaris-NFT Instance"
        Dim entityPatterns As Variant
        entityPatterns = Array("on RDS instance", "on M3", "on system", "on Pod", _
                            "on Kubernetes", "on cluster", "on K8s", "on NAT-")
        
        For Each pattern In entityPatterns
            If InStr(1, cellText, pattern, vbTextCompare) > 0 Then
                startPos = InStr(1, cellText, pattern, vbTextCompare)
                textLen = Len(pattern)
                ws.Cells(i, colNum).Characters(startPos, textLen).Font.Italic = True
            End If
        Next pattern
        
NextCell:
    Next i
    
    ' Set column width to match the image
    ws.Columns(colNum).ColumnWidth = 60
    
    ' Auto-fit rows for better readability
    ws.Rows.AutoFit
    
    Application.ScreenUpdating = True
    
    MsgBox "Human Readable column formatting completed with character-level formatting!", vbInformation
End Sub

Sub ClearColumnFormatting()
    ' Simple script to clear formatting from a column
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim colNum As Integer
    
    ' Use active sheet
    Set ws = ActiveSheet
    
    ' Ask for column number 
    colNum = InputBox("Enter the column number to clear formatting (e.g., 4 for column D):", "Column Selection")
    
    ' Validate input
    If colNum < 1 Then
        MsgBox "Invalid column number. Operation cancelled.", vbExclamation
        Exit Sub
    End If
    
    ' Find last row with data in that column
    lastRow = ws.Cells(ws.Rows.Count, colNum).End(xlUp).Row
    
    ' Clear formatting
    ws.Range(ws.Cells(1, colNum), ws.Cells(lastRow, colNum)).ClearFormats
    
    MsgBox "Formatting cleared from column " & colNum, vbInformation
End Sub
