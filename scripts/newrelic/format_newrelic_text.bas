Attribute VB_Name = "FormatNewRelicText"
Option Explicit

Sub FormatHumanReadableColumn()
    ' This script formats the Human Readable column exactly as shown in the image
    ' with specific text highlighting for threshold values and metric names
    
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim colNum As Integer
    Dim i As Long, j As Long
    Dim cell As Range
    
    ' Use active sheet
    Set ws = ActiveSheet
    
    ' Ask for column number (simpler than trying to find it)
    colNum = InputBox("Enter the column number where Human Readable text is located (e.g., 4 for column D):", "Column Selection")
    
    ' Validate input
    If colNum < 1 Then
        MsgBox "Invalid column number. Operation cancelled.", vbExclamation
        Exit Sub
    End If
    
    ' Find last row with data in that column
    lastRow = ws.Cells(ws.Rows.Count, colNum).End(xlUp).Row
    
    ' Format header (row 1)
    With ws.Cells(1, colNum)
        .Interior.Color = RGB(31, 78, 120)  ' Dark blue header
        .Font.Color = RGB(255, 255, 255)     ' White
        .Font.Bold = True
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
    End With
    
    ' Format data cells
    For i = 2 To lastRow
        With ws.Cells(i, colNum)
            ' Check for "critical alert" text
            If InStr(1, .Value, "critical alert", vbTextCompare) > 0 Then
                .Interior.Color = RGB(248, 203, 173)  ' Light red/peach
            Else
                .Interior.Color = RGB(255, 235, 156)  ' Light yellow
            End If
            
            ' Apply borders and text formatting
            .Borders.LineStyle = xlContinuous
            .WrapText = True
            .VerticalAlignment = xlCenter
        End With
    Next i
    
    ' Set column width to match the image
    ws.Columns(colNum).ColumnWidth = 60
    
    ' Auto-fit rows for better readability
    ws.Rows.AutoFit
    
    MsgBox "Human Readable column formatting completed!", vbInformation
End Sub

' Optional enhancement: Add text highlighting within the cells
' This is more advanced and might not work in all Excel versions
Sub FormatHumanReadableTextWithHighlighting()
    ' This function tries to add highlighting for specific keywords
    ' But note that cell-level text formatting in VBA is complex
    ' and may not be supported in all Excel versions
    
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim colNum As Integer
    Dim i As Long
    
    ' Use active sheet
    Set ws = ActiveSheet
    
    ' Ask for column number
    colNum = InputBox("Enter the column number where Human Readable text is located (e.g., 4 for column D):", "Column Selection")
    
    ' Validate input
    If colNum < 1 Then
        MsgBox "Invalid column number. Operation cancelled.", vbExclamation
        Exit Sub
    End If
    
    ' Find last row with data in that column
    lastRow = ws.Cells(ws.Rows.Count, colNum).End(xlUp).Row
    
    ' First apply the regular cell formatting
    Call FormatHumanReadableColumn
    
    ' Create a temporary Word document to handle text formatting
    ' This is a workaround as Excel doesn't easily support text-level formatting
    On Error Resume Next
    
    Dim wordApp As Object
    Dim wordDoc As Object
    Dim wordRange As Object
    
    ' Try to create Word objects
    Set wordApp = CreateObject("Word.Application")
    
    If Err.Number <> 0 Then
        MsgBox "Microsoft Word is required for text highlighting. Basic formatting has been applied.", vbInformation
        Exit Sub
    End If
    
    wordApp.Visible = False
    Set wordDoc = wordApp.Documents.Add
    Set wordRange = wordDoc.Range
    
    ' Process each cell to add text highlighting
    For i = 2 To lastRow
        Dim cellText As String
        cellText = ws.Cells(i, colNum).Value
        
        ' Skip empty cells
        If Len(cellText) > 0 Then
            ' Copy text to Word
            wordRange.Text = cellText
            
            ' Find and format specific patterns
            With wordDoc.Range
                ' Format "is above", "is below", etc.
                .Find.Execute FindText:="is above", ReplaceWith:="is above", _
                    Format:=True, Replace:=wdReplaceAll
                .Find.Font.Bold = True
                
                ' Format "raise a critical alert"
                .Find.Execute FindText:="raise a critical alert", ReplaceWith:="raise a critical alert", _
                    Format:=True, Replace:=wdReplaceAll
                .Find.Font.Bold = True
                .Find.Font.Color = wdColorRed
                
                ' Format numeric values with units
                ' This is much more complex and may require regex
                ' which is not easily available in VBA
            End With
            
            ' Copy formatted text back to Excel
            ' This is where it gets complicated - Excel cells can't easily
            ' have partial text formatting through VBA
        End If
    Next i
    
    ' Clean up Word objects
    wordDoc.Close SaveChanges:=False
    wordApp.Quit
    Set wordRange = Nothing
    Set wordDoc = Nothing
    Set wordApp = Nothing
    
    MsgBox "Formatting is limited to cell-level in Excel. For text-level formatting, consider using a different approach.", vbInformation
End Sub

Sub ClearColumnFormatting()
    ' Simple script to clear formatting from a column
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim colNum As Integer
    
    ' Use active sheet
    Set ws = ActiveSheet
    
    ' Ask for column number 
    colNum = InputBox("Enter the column number to clear formatting (e.g., 4 for column D):", "Column Selection")
    
    ' Validate input
    If colNum < 1 Then
        MsgBox "Invalid column number. Operation cancelled.", vbExclamation
        Exit Sub
    End If
    
    ' Find last row with data in that column
    lastRow = ws.Cells(ws.Rows.Count, colNum).End(xlUp).Row
    
    ' Clear formatting
    ws.Range(ws.Cells(1, colNum), ws.Cells(lastRow, colNum)).ClearFormats
    
    MsgBox "Formatting cleared from column " & colNum, vbInformation
End Sub
