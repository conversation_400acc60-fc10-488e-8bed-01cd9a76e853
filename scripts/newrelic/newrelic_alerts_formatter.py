#!/usr/bin/env python3
"""
New Relic Alerts Formatter

This script takes raw New Relic alert conditions from API responses,
uses Azure OpenAI to convert them to human-readable format,
and saves the results to an Excel file with proper formatting.
"""

import os
import json
import argparse
import logging
from typing import Dict, List, Any, Optional
import pandas as pd
from pandas import DataFrame
import openai
from dotenv import load_dotenv
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class AzureOpenAIClient:
    """
    Client for interacting with Azure OpenAI API.
    """
    
    def __init__(self, api_key: str, endpoint: str, deployment_name: str):
        """
        Initialize the Azure OpenAI client.
        
        Args:
            api_key: Azure OpenAI API key
            endpoint: Azure OpenAI endpoint URL
            deployment_name: Azure OpenAI deployment name
        """
        self.api_key = api_key
        self.endpoint = endpoint
        self.deployment_name = deployment_name
        
        # Configure the OpenAI client for Azure
        self.client = openai.AzureOpenAI(
            api_key=api_key,
            api_version="2023-05-15",
            azure_endpoint=endpoint
        )
    
    def convert_alert_condition(self, condition: Dict[str, Any]) -> Dict[str, str]:
        """
        Convert a New Relic alert condition to human-readable format, split into parts.
        
        Args:
            condition: Dictionary containing alert condition details
            
        Returns:
            Dictionary with parts of the human-readable text for easy formatting
        """
        # Create a system prompt to guide the model
        system_prompt = """
        You are an expert at translating New Relic alert conditions into human-readable format.
        Convert the raw New Relic condition data into a clear, concise sentence following this pattern:
        "When [metric_name] on [entity] is [above/below] [threshold] for [duration] raise a [severity] alert"
        
        Examples:
        - "When number of database connections is over 300 for 30 minutes raise a critical alert"
        - "When free/totalSpace on M3-Polaris-NFT Instance is below 80 gb for 5 minutes raise a critical alert"
        - "When disk_read/sec on RDS instance is above 120k for 1 hour raise a critical alert"
        - "When network throughput on an RDS instance is above 750kb for 5 minutes raise a critical alert"
        
        IMPORTANT: Return the response as a JSON object with these fields:
        {
          "full_text": "The complete human-readable text",
          "prefix": "When",
          "metric_name": "the metric name", 
          "entity": "the entity type/name",
          "condition": "is above/below/etc",
          "threshold": "threshold with units",
          "duration": "duration with units",
          "severity": "critical/warning"
        }
        
        This will allow for better formatting of the parts. Ensure the JSON is valid.
        """
        
        # Convert condition to string for processing
        condition_str = json.dumps(condition, indent=2)
        
        try:
            # Call Azure OpenAI to process the condition
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Convert this New Relic alert condition to human-readable format with JSON structure: {condition_str}"}
                ],
                temperature=0.1,  # Low temperature for more deterministic results
                max_tokens=500,
                response_format={"type": "json_object"}
            )
            
            # Extract and parse the JSON response
            response_text = response.choices[0].message.content.strip()
            try:
                parsed_response = json.loads(response_text)
                # Ensure all expected fields are present
                required_fields = ["full_text", "prefix", "metric_name", "entity", 
                                 "condition", "threshold", "duration", "severity"]
                
                for field in required_fields:
                    if field not in parsed_response:
                        parsed_response[field] = ""
                        
                # Return the parsed response
                return parsed_response
                
            except json.JSONDecodeError:
                # If JSON parsing failed, return the full text in the first field
                logger.error(f"Failed to parse JSON response: {response_text}")
                return {"full_text": response_text, "prefix": "When", "metric_name": "", 
                        "entity": "", "condition": "", "threshold": "", 
                        "duration": "", "severity": ""}
                
        except Exception as e:
            logger.error(f"Error calling Azure OpenAI: {str(e)}")
            error_msg = f"Error converting condition: {str(e)}"
            return {"full_text": error_msg, "prefix": "", "metric_name": "", 
                    "entity": "", "condition": "", "threshold": "", 
                    "duration": "", "severity": ""}

class NewRelicAlertsFormatter:
    """
    Processes New Relic alert conditions and formats them into an Excel spreadsheet.
    """
    
    def __init__(self, openai_client: AzureOpenAIClient):
        """
        Initialize the formatter with an Azure OpenAI client.
        
        Args:
            openai_client: An initialized AzureOpenAIClient
        """
        self.openai_client = openai_client
    
    def process_alert_conditions(self, conditions: List[Dict[str, Any]]) -> DataFrame:
        """
        Process a list of New Relic alert conditions.
        
        Args:
            conditions: List of dictionaries containing alert condition details
            
        Returns:
            DataFrame with processed alert conditions and their parsed components
        """
        results = []
        
        for condition in conditions:
            try:
                # Extract basic condition info
                alert_name = condition.get("name", "Unknown Alert")
                alert_type = condition.get("type", "Unknown Type")
                entity_name = self._extract_entity_name(condition)
                
                # Process with AI to get structured components
                structured_text = self.openai_client.convert_alert_condition(condition)
                
                # Extract components
                full_text = structured_text.get("full_text", "")
                prefix = structured_text.get("prefix", "When")
                metric_name = structured_text.get("metric_name", "")
                entity_part = structured_text.get("entity", "")
                condition_part = structured_text.get("condition", "")
                threshold = structured_text.get("threshold", "")
                duration = structured_text.get("duration", "")
                severity = structured_text.get("severity", "")
                
                # Ensure severity is set
                if not severity:
                    severity = "critical" if "critical alert" in full_text.lower() else "warning"
                
                # Add to results with all components
                results.append({
                    "Alert Name": alert_name,
                    "Entity": entity_name,
                    "Alert Type": alert_type,
                    "Human Readable Text": full_text,
                    "Prefix": prefix,
                    "Metric Name": metric_name,
                    "Entity Part": entity_part,
                    "Condition": condition_part,
                    "Threshold": threshold,
                    "Duration": duration,
                    "Severity": severity
                })
                
                logger.info(f"Processed condition: {alert_name}")
                
            except Exception as e:
                logger.error(f"Error processing condition: {str(e)}")
        
        # Convert to DataFrame
        return pd.DataFrame(results)
    
    def _extract_entity_name(self, condition: Dict[str, Any]) -> str:
        """
        Extract entity name from condition data.
        
        Args:
            condition: Dictionary containing alert condition details
            
        Returns:
            Entity name string
        """
        # Attempt to extract entity name from different possible locations
        entities = condition.get("entities", [])
        if entities and len(entities) > 0:
            if isinstance(entities[0], dict) and "name" in entities[0]:
                return entities[0]["name"]
            return str(entities[0])
        
        # Try to extract from other possible fields
        for field in ["target", "entity", "entityName", "scope"]:
            if field in condition and condition[field]:
                return str(condition[field])
        
        return "Unknown Entity"
    
    def save_to_excel(self, df: DataFrame, output_path: str) -> None:
        """
        Save the processed alerts to an Excel file with formatting and separate columns for text components.
        
        Args:
            df: DataFrame with processed alerts
            output_path: Path to save the Excel file
        """
        try:
            # Create a simplified view for the main Excel file
            main_view = df[['Alert Name', 'Entity', 'Alert Type', 'Human Readable Text', 'Severity']].copy()
            
            # Save the components to a separate sheet for reference and formatting
            writer = pd.ExcelWriter(output_path, engine='openpyxl')
            
            # Save the main view to first sheet
            main_view.to_excel(writer, index=False, sheet_name='Alerts')
            
            # Save the full data with all components to second sheet
            df.to_excel(writer, index=False, sheet_name='Components')
            
            # Get the workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['Alerts']
            components_sheet = writer.sheets['Components']
            
            # Define explicit RGB colors that match the example
            header_color = 'FF4472C4'  # Blue header
            critical_color = 'FFF8CBAD'  # Light red/peach
            warning_color = 'FFFFEB9C'  # Light yellow
            
            # Format the header row on main sheet
            for col_num, col_name in enumerate(main_view.columns, 1):
                cell = worksheet.cell(row=1, column=col_num)
                cell.fill = PatternFill(start_color=header_color[2:], end_color=header_color[2:], fill_type="solid")
                cell.font = Font(color="FFFFFF", bold=True)
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(style='thin'), 
                    right=Side(style='thin'), 
                    top=Side(style='thin'), 
                    bottom=Side(style='thin')
                )
            
            # Format data rows based on severity on main sheet
            for row_num in range(2, len(main_view) + 2):
                severity = worksheet.cell(row=row_num, column=5).value
                fill_color = critical_color[2:] if severity and 'critical' in severity.lower() else warning_color[2:]
                
                # Apply formatting to all cells in the row
                for col_num in range(1, len(main_view.columns) + 1):
                    cell = worksheet.cell(row=row_num, column=col_num)
                    cell.fill = PatternFill(start_color=fill_color, end_color=fill_color, fill_type="solid")
                    cell.border = Border(
                        left=Side(style='thin'), 
                        right=Side(style='thin'), 
                        top=Side(style='thin'), 
                        bottom=Side(style='thin')
                    )
                    # Apply text wrap to the Human Readable Text column
                    if col_num == 4:
                        cell.alignment = Alignment(wrap_text=True, vertical='center')
            
            # Format the components sheet headers
            for col_num, col_name in enumerate(df.columns, 1):
                cell = components_sheet.cell(row=1, column=col_num)
                cell.fill = PatternFill(start_color=header_color[2:], end_color=header_color[2:], fill_type="solid")
                cell.font = Font(color="FFFFFF", bold=True)
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(left=Side(style='thin'), right=Side(style='thin'), 
                                    top=Side(style='thin'), bottom=Side(style='thin'))
            
            # Set column widths for main view
            column_widths = {
                1: 25,  # Alert Name
                2: 20,  # Entity
                3: 15,  # Alert Type
                4: 60,  # Human Readable Text
                5: 15,  # Severity
            }
            
            for col_idx, width in column_widths.items():
                worksheet.column_dimensions[chr(64 + col_idx)].width = width
            
            # Auto-fit columns for components sheet
            for col_num, col_name in enumerate(df.columns, 1):
                components_sheet.column_dimensions[chr(64 + col_num)].width = 20
            
            # Set component columns to wider for text
            components_sheet.column_dimensions['E'].width = 40  # Human Readable Text
            components_sheet.column_dimensions['G'].width = 25  # Metric Name
            components_sheet.column_dimensions['H'].width = 25  # Entity Part
            
            # Save the workbook
            writer.close()
            
            logger.info(f"Excel file saved with split text components: {output_path}")
            logger.info(f"Sheet 'Alerts': Main view with formatted cells")
            logger.info(f"Sheet 'Components': All components for text-level formatting")
            
        except Exception as e:
            logger.error(f"Error with Excel formatting: {str(e)}")
            # Fallback to simple pandas export without formatting
            df.to_excel(output_path, index=False, sheet_name='New Relic Alerts')
            logger.info(f"Saved Excel file without formatting as fallback")
    
    def save_to_csv(self, df: DataFrame, output_path: str) -> None:
        """
        Save the processed alerts to a CSV file with separate columns for text components.
        This makes it easier to import into Excel for custom formatting.
        
        Args:
            df: DataFrame with processed alerts
            output_path: Path to save the CSV file
        """
        try:
            # Create a new DataFrame with just the components we want for formatting
            format_columns = [
                'Alert Name',
                'Entity',
                'Alert Type',
                'Prefix',           # 'When'
                'Metric Name',      # e.g., 'number of database connections'
                'Entity Part',      # e.g., 'on RDS instance'
                'Condition',        # e.g., 'is above'
                'Threshold',        # e.g., '300'
                'Duration',         # e.g., 'for 30 minutes'
                'Severity'          # e.g., 'critical'
            ]
            
            # Create a view with just these columns
            csv_df = df[format_columns].copy() if all(col in df.columns for col in format_columns) else df
            
            # Save to CSV
            csv_df.to_csv(output_path, index=False)
            logger.info(f"CSV file saved with columns for text formatting: {output_path}")
            
        except Exception as e:
            logger.error(f"Error saving CSV file: {str(e)}")
            # Fallback to simple export
            df.to_csv(output_path, index=False)
            logger.info(f"Saved CSV with all available columns as fallback")

def load_new_relic_conditions(input_path: str) -> List[Dict[str, Any]]:
    """
    Load New Relic alert conditions from a JSON file.
    
    Args:
        input_path: Path to the JSON file containing alert conditions
    
    Returns:
        List of condition dictionaries
    """
    try:
        with open(input_path, 'r') as f:
            data = json.load(f)
        
        # Handle different possible structures in the JSON
        if isinstance(data, list):
            return data
        elif isinstance(data, dict):
            if "conditions" in data:
                return data["conditions"]
            elif "data" in data and isinstance(data["data"], list):
                return data["data"]
            else:
                # Return all keys in case the structure is unknown
                return [data]
        else:
            logger.error(f"Unexpected data structure in {input_path}")
            return []
    except Exception as e:
        logger.error(f"Error loading New Relic conditions: {str(e)}")
        return []

def main():
    """
    Main function to execute the script.
    """
    parser = argparse.ArgumentParser(description="Format New Relic alerts using Azure OpenAI")
    parser.add_argument("--input", "-i", required=True, help="Path to the JSON file with New Relic alert conditions")
    parser.add_argument("--output", "-o", required=True, help="Path for the output Excel file")
    parser.add_argument("--csv", "-c", help="Path for the output CSV file with component columns")
    parser.add_argument("--format", "-f", choices=["excel", "csv", "both"], default="excel",
                        help="Output format: excel, csv, or both (default: excel)")
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv(override=True)
    
    # Get Azure OpenAI credentials from environment variables
    api_key = os.getenv("AZURE_OPENAI_API_KEY")
    endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
    deployment_name = os.getenv("AZURE_OPENAI_DEPLOYMENT")
    
    if not all([api_key, endpoint, deployment_name]):
        logger.error("Azure OpenAI credentials not found in environment variables")
        print("Please ensure the following environment variables are set:")
        print("  - AZURE_OPENAI_API_KEY")
        print("  - AZURE_OPENAI_ENDPOINT")
        print("  - AZURE_OPENAI_DEPLOYMENT")
        return
    
    # Initialize Azure OpenAI client
    openai_client = AzureOpenAIClient(api_key, endpoint, deployment_name)
    
    # Initialize formatter
    formatter = NewRelicAlertsFormatter(openai_client)
    
    # Load New Relic conditions
    logger.info(f"Loading New Relic conditions from {args.input}")
    conditions = load_new_relic_conditions(args.input)
    
    if not conditions:
        logger.error("No alert conditions found or error loading file")
        return
    
    logger.info(f"Processing {len(conditions)} alert conditions")
    
    # Process conditions
    df = formatter.process_alert_conditions(conditions)
    
    # Determine output format
    if args.format == "excel" or args.format == "both":
        # Save to Excel
        formatter.save_to_excel(df, args.output)
        logger.info(f"Processing complete. Excel results saved to {args.output}")
    
    if args.format == "csv" or args.format == "both":
        # Determine CSV output path
        csv_path = args.csv if args.csv else os.path.splitext(args.output)[0] + ".csv"
        # Save to CSV
        formatter.save_to_csv(df, csv_path)
        logger.info(f"Processing complete. CSV components saved to {csv_path}")
    
    logger.info("Processing completed successfully.")

if __name__ == "__main__":
    main()
