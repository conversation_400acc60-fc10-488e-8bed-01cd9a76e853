Alert Name,Entity,Alert Type,Prefix,Metric Name,Entity Part,Condition,Threshold,Duration,Severity
Pod not Scheduled,Unknown Entity,static,When,count of unscheduled pods,Kubernetes clusters,is above,0,10 minutes,critical
OOM triggered on Pod,Unknown Entity,static,When,OOM triggered on Pod,Pod,is above,0.0,30 minutes,critical
Pod with Crash<PERSON>oopBackOff,Unknown Entity,static,When,count of pods with Crash<PERSON>oopBackOff,Kubernetes cluster,is above,10,45 minutes,critical
Cluster Node in Disk Pressure State,Unknown Entity,static,When,condition.DiskPressure,Cluster Node,is not equal to,0.0,5 minutes,critical
Disk Utilization is high for system,Unknown Entity,static,When,disk utilization percent,system,is above,95%,30 minutes,critical
CPU Utilization is high for system,Unknown Entity,static,When,CPU Utilization,system,is above,90%,5 minutes,critical
Percentage of Unavailable pods > 25%,Unknown Entity,static,When,percentage of unavailable pods,cluster primary-%-eks,is above,25%,30 minutes,critical
CPU IOWait high for system,Unknown Entity,static,When,CPU IOWait high for system,host.cpuIoWaitPercent,is above,20.0%,15 minutes,critical
Jasper memory consumption is High,Unknown Entity,static,When,memory consumption,JasperReports container,is above,12 GB,5 minutes,critical
MI Container restarts > 10 in 15 minutes,Unknown Entity,static,When,container restarts,MI Container,is above,10,15 minutes,critical
