'''
This script disables New Relic conditions based on policy ID.
'''
import requests
import json
import os
import argparse
import logging
import time
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress
from rich.logging import <PERSON><PERSON>and<PERSON>

from dotenv import load_dotenv

load_dotenv()

console = Console()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)]
)
log = logging.getLogger("rich")

class NewRelicConditionManager:
    def __init__(self, api_key, account_id, region='US', verbose=False):
        self.api_key = api_key
        self.account_id = account_id
        self.api_endpoint = "https://api.newrelic.com/graphql" if region == 'US' else "https://api.eu.newrelic.com/graphql"
        self.headers = {
            "Content-Type": "application/json",
            "API-Key": self.api_key
        }
        self.verbose = verbose

    def fetch_conditions_by_policy(self, policy_id):
        """Fetch all conditions for a specific policy ID with pagination support"""
        all_conditions = []
        cursor = None
        
        while True:
            query = '''
            query ($accountId: Int!, $policyId: ID!, $cursor: String) {
              actor {
                account(id: $accountId) {
                  alerts {
                    nrqlConditionsSearch(searchCriteria: { policyId: $policyId }, cursor: $cursor) {
                      nextCursor
                      totalCount
                      nrqlConditions {
                        id
                        name
                        description
                        enabled
                        nrql {
                          query
                        }
                        signal {
                          aggregationWindow
                          aggregationMethod
                          aggregationDelay
                          aggregationTimer
                        }
                        policyId
                        runbookUrl
                        terms {
                          operator
                          thresholdDuration
                          threshold
                          priority
                          thresholdOccurrences
                        }
                        type
                        violationTimeLimitSeconds
                      }
                    }
                  }
                }
              }
            }
            '''
            
            variables = {
                "accountId": self.account_id,
                "policyId": policy_id,
                "cursor": cursor
            }
            
            if self.verbose:
                log.info(f"Sending GraphQL request with cursor: {cursor}")
            
            try:
                response = requests.post(
                    self.api_endpoint, 
                    json={"query": query, "variables": variables}, 
                    headers=self.headers
                )
                response.raise_for_status()  # Raise exception for HTTP errors
            except requests.exceptions.RequestException as e:
                log.error(f"API request failed: {str(e)}")
                if self.verbose:
                    log.error(f"Request details: {variables}")
                break
            
            try:
                result = response.json()
                if 'errors' in result:
                    log.error(f"GraphQL errors: {result['errors']}")
                    break
                    
                search_results = result["data"]["actor"]["account"]["alerts"]["nrqlConditionsSearch"]
                page_conditions = search_results["nrqlConditions"]
                total_count = search_results["totalCount"]
                
                console.print(f"Fetched {len(page_conditions)} conditions (Progress: {len(all_conditions) + len(page_conditions)}/{total_count})", style="dim")
                
                all_conditions.extend(page_conditions)
                
                # Get next cursor
                cursor = search_results["nextCursor"]
                
                # Break the loop if no more pages
                if not cursor:
                    break
                    
                # Small delay to avoid overwhelming the API
                time.sleep(0.5)
                    
            except KeyError as e:
                log.error(f"Error parsing response: {e}")
                if self.verbose:
                    log.error(f"Response: {response.text}")
                break
        
        return {"data": {"actor": {"account": {"alerts": {"nrqlConditionsSearch": {"nrqlConditions": all_conditions}}}}}}

    def disable_condition(self, condition):
        """Disable a condition based on its type"""
        condition_id = condition['id']
        condition_type = condition['type']
        
        if condition_type == 'STATIC':
            mutation = self._get_static_update_mutation()
        elif condition_type == 'BASELINE':
            mutation = self._get_baseline_update_mutation()
        else:
            log.error(f"Unknown condition type: {condition_type} for condition {condition['name']}")
            return False
        
        # Create a copy of the condition with enabled=False for the update
        update_condition = self._prepare_condition_for_update(condition)
        
        variables = {
            "accountId": self.account_id,
            "id": condition_id,
            "condition": update_condition
        }
        
        if self.verbose:
            log.info(f"Disabling condition: {condition['name']} (ID: {condition_id}, Type: {condition_type})")
        
        try:
            response = requests.post(
                self.api_endpoint, 
                json={"query": mutation, "variables": variables}, 
                headers=self.headers
            )
            response.raise_for_status()  # Raise exception for HTTP errors
        except requests.exceptions.RequestException as e:
            log.error(f"API request failed for condition {condition['name']}: {str(e)}")
            return False
        
        result = response.json()
        if 'errors' in result:
            log.error(f"Error disabling condition {condition['name']}: {result['errors']}")
            return False
            
        if self.verbose:
            log.info(f"Successfully disabled condition: {condition['name']}")
        
        return True

    def _prepare_condition_for_update(self, condition):
        """Prepare condition object for update by setting enabled to False
        and transforming it into the format expected by the API"""
        update_condition = {}
        
        # Required fields for both STATIC and BASELINE conditions
        update_condition["enabled"] = False
        update_condition["name"] = condition["name"]
        
        # Copy description if it exists
        if "description" in condition and condition["description"]:
            update_condition["description"] = condition["description"]
        
        # Copy NRQL query
        update_condition["nrql"] = condition["nrql"]
        
        # Copy signal related fields if they exist
        if "signal" in condition and condition["signal"]:
            signal = {}
            signal_fields = ["aggregationWindow", "aggregationMethod", "aggregationDelay", "aggregationTimer"]
            for field in signal_fields:
                if field in condition["signal"] and condition["signal"][field] is not None:
                    signal[field] = condition["signal"][field]
            if signal:
                update_condition["signal"] = signal
        
        # Copy runbook URL if it exists
        if "runbookUrl" in condition and condition["runbookUrl"]:
            update_condition["runbookUrl"] = condition["runbookUrl"]
        
        # Copy terms
        if "terms" in condition and condition["terms"]:
            update_condition["terms"] = condition["terms"]
        
        # Copy violation time limit if it exists
        if "violationTimeLimitSeconds" in condition and condition["violationTimeLimitSeconds"]:
            update_condition["violationTimeLimitSeconds"] = condition["violationTimeLimitSeconds"]
        
        return update_condition

    def _get_static_update_mutation(self):
        return '''
        mutation ($accountId: Int!, $id: ID!, $condition: AlertsNrqlConditionUpdateStaticInput!) {
          alertsNrqlConditionStaticUpdate(
            accountId: $accountId,
            id: $id,
            condition: $condition
          ) {
            id
            name
            enabled
          }
        }
        '''

    def _get_baseline_update_mutation(self):
        return '''
        mutation ($accountId: Int!, $id: ID!, $condition: AlertsNrqlConditionUpdateBaselineInput!) {
          alertsNrqlConditionBaselineUpdate(
            accountId: $accountId,
            id: $id,
            condition: $condition
          ) {
            id
            name
            enabled
          }
        }
        '''

def display_conditions(conditions):
    """Display conditions in a rich table"""
    table = Table(title="New Relic Conditions")
    
    table.add_column("ID", style="cyan")
    table.add_column("Name", style="magenta")
    table.add_column("Type", style="green")
    table.add_column("Enabled", style="blue")
    table.add_column("Policy ID", style="yellow")
    
    for condition in conditions:
        table.add_row(
            condition['id'],
            condition['name'],
            condition['type'],
            str(condition['enabled']),
            condition['policyId']
        )
    
    console.print(Panel(table, expand=False))

def save_conditions_to_file(conditions, filename):
    """Save conditions to a JSON file for potential restoration later"""
    with open(filename, 'w') as f:
        json.dump(conditions, f, indent=2)
    log.info(f"Saved conditions to {filename}")

def main():
    parser = argparse.ArgumentParser(description="Disable New Relic conditions based on policy ID.")
    parser.add_argument("--account-id", type=int, required=True, help="New Relic account ID")
    parser.add_argument("--policy-id", required=True, help="Policy ID to disable conditions for")
    parser.add_argument("--region", choices=['US', 'EU'], default='US', help="New Relic region (US or EU)")
    parser.add_argument("--dry-run", action="store_true", help="Only list conditions without disabling them")
    parser.add_argument("--batch-size", type=int, default=10, help="Number of conditions to disable in parallel (default: 10)")
    parser.add_argument("--no-confirm", action="store_true", help="Skip confirmation prompt")
    parser.add_argument("--save-backup", action="store_true", help="Save a backup of conditions before disabling")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()

    # Set log level based on verbose flag
    if args.verbose:
        logging.getLogger("rich").setLevel(logging.DEBUG)
    
    api_key = os.getenv("NEWRELIC_API_KEY")
    if not api_key:
        log.error("NEWRELIC_API_KEY environment variable is not set.")
        exit(1)

    manager = NewRelicConditionManager(api_key, args.account_id, args.region, verbose=args.verbose)

    # Fetch conditions for the policy
    log.info(f"Fetching conditions for policy {args.policy_id}...")
    response = manager.fetch_conditions_by_policy(args.policy_id)
    
    try:
        conditions = response["data"]["actor"]["account"]["alerts"]["nrqlConditionsSearch"]["nrqlConditions"]
    except KeyError:
        log.error(f"Error fetching conditions: {response}")
        exit(1)
    
    # Filter to only show enabled conditions
    enabled_conditions = [c for c in conditions if c['enabled']]
    
    # Display fetched conditions
    log.info(f"Found {len(conditions)} total conditions in policy {args.policy_id}")
    log.info(f"{len(enabled_conditions)} conditions are currently enabled")
    
    if not enabled_conditions:
        log.warning("No enabled conditions found to disable.")
        return
    
    display_conditions(enabled_conditions)
    
    # Save backup if requested
    if args.save_backup:
        backup_file = f"new_relic_policy_{args.policy_id}_backup_{int(time.time())}.json"
        save_conditions_to_file(conditions, backup_file)
    
    if args.dry_run:
        log.warning("Dry run mode. No conditions will be disabled.")
        return

    # Confirm before disabling
    if not args.no_confirm:
        confirm = input(f"Do you want to disable these {len(enabled_conditions)} conditions? (y/n): ")
        if confirm.lower() != 'y':
            log.warning("Operation cancelled.")
            return

    # Disable conditions
    log.info(f"Disabling {len(enabled_conditions)} conditions...")
    successful = 0
    failed = 0
    
    with Progress() as progress:
        task = progress.add_task("[cyan]Disabling conditions...", total=len(enabled_conditions))
        
        for i, condition in enumerate(enabled_conditions):
            # Display progress information periodically
            if (i + 1) % 5 == 0 or i == 0 or i == len(enabled_conditions) - 1:
                progress.console.print(f"Processing {i+1}/{len(enabled_conditions)}: '{condition['name']}'", style="dim")
            
            result = manager.disable_condition(condition)
            
            if result:
                successful += 1
            else:
                failed += 1
            
            progress.update(task, advance=1)
            
            # Small delay to avoid overwhelming the API
            time.sleep(0.2)
    
    log.info(f"Successfully disabled {successful} out of {len(enabled_conditions)} conditions.")
    
    if failed > 0:
        log.error(f"Failed to disable {failed} conditions.")
        log.warning("Check the error logs above for details.")

if __name__ == "__main__":
    main() 