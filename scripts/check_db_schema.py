#!/usr/bin/env python3
"""
Database Schema Checker for AI Incident Manager

This script checks the existing database schema to understand
table structures and data types.
"""

import os
import sys
import logging
import dotenv
import psycopg2
from psycopg2.extras import DictCursor

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_connection():
    """Get a database connection using environment variables."""
    conn_params = {
        "dbname": os.environ.get("POSTGRES_DB", "incident_manager"),
        "user": os.environ.get("POSTGRES_USER", "postgres"),
        "password": os.environ.get("POSTGRES_PASSWORD", "postgres"),
        "host": os.environ.get("POSTGRES_HOST", "localhost"),
        "port": os.environ.get("POSTGRES_PORT", "5432")
    }
    
    logger.info(f"Connecting to PostgreSQL with parameters: {conn_params}")
    
    return psycopg2.connect(**conn_params)

def check_existing_tables():
    """Check what tables exist in the database and their structure."""
    try:
        with get_connection() as conn:
            with conn.cursor(cursor_factory=DictCursor) as cur:
                # List all tables in the database
                cur.execute("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name;
                """)
                
                tables = cur.fetchall()
                
                if not tables:
                    logger.info("No tables found in the database.")
                    return
                
                logger.info(f"Found {len(tables)} tables in the database:")
                for table in tables:
                    table_name = table['table_name']
                    logger.info(f"Table: {table_name}")
                    
                    # Get column information for each table
                    cur.execute("""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns
                    WHERE table_schema = 'public' AND table_name = %s
                    ORDER BY ordinal_position;
                    """, (table_name,))
                    
                    columns = cur.fetchall()
                    for column in columns:
                        nullable = "NULL" if column['is_nullable'] == 'YES' else "NOT NULL"
                        logger.info(f"  - {column['column_name']}: {column['data_type']} {nullable}")
                    
                    # Get primary key info
                    cur.execute("""
                    SELECT kcu.column_name
                    FROM information_schema.table_constraints tc
                    JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                    WHERE tc.constraint_type = 'PRIMARY KEY'
                    AND tc.table_schema = 'public'
                    AND tc.table_name = %s;
                    """, (table_name,))
                    
                    pkeys = cur.fetchall()
                    if pkeys:
                        logger.info(f"  Primary Key: {', '.join([pk['column_name'] for pk in pkeys])}")
                    
                    logger.info("  ---")
                
    except Exception as e:
        logger.error(f"Error checking database schema: {str(e)}")
        raise

if __name__ == "__main__":
    check_existing_tables()
