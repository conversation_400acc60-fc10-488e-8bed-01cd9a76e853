#!/usr/bin/env python3
"""
Database Initialization Script for AI Incident Manager

This script initializes the PostgreSQL database with all required tables
for the incident management system, including entity-related tables.
"""

import os
import logging
import uuid
import dotenv
import psycopg2

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the PostgresDB class
from ai_incident_manager.database.postgres import PostgresDB

def generate_uuid():
    """Generate a UUID string.
    
    Since we can't rely on the PostgreSQL uuid-ossp extension in Azure,
    we'll generate UUIDs in application code.
    
    Returns:
        str: UUID string
    """
    return str(uuid.uuid4())

def initialize_database():
    """Initialize the database with all required tables."""
    logger.info("Initializing PostgreSQL database...")
    
    try:
        # Create database instance
        db = PostgresDB()
        
        # Log connection parameters (without password)
        conn_params = db.conn_params.copy()
        conn_params.pop('password', None)
        logger.info(f"Connecting to PostgreSQL with parameters: {conn_params}")
        
        # Check for UUID extension
        with db.get_connection() as conn:
            with conn.cursor() as cur:
                # First, check if the uuid-ossp extension is already installed
                cur.execute("""
                SELECT 1 FROM pg_extension WHERE extname = 'uuid-ossp';
                """)
                extension_exists = cur.fetchone()
                
                if not extension_exists:
                    logger.info("Installing uuid-ossp extension...")
                    try:
                        cur.execute("""
                        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
                        """)
                        logger.info("uuid-ossp extension installed successfully.")
                    except psycopg2.Error as e:
                        logger.warning(f"Could not create uuid-ossp extension: {str(e)}")
                        logger.info("Using application-generated UUIDs instead.")
                else:
                    logger.info("uuid-ossp extension is already installed.")
        
        # Create all tables
        db.create_tables()
        
        logger.info("Database tables created successfully!")
        
        # Display connection information
        logger.info(f"Database: {db.conn_params['dbname']}")
        logger.info(f"Host: {db.conn_params['host']}")
        logger.info(f"Port: {db.conn_params['port']}")
        logger.info(f"User: {db.conn_params['user']}")
        
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise

if __name__ == "__main__":
    initialize_database()
