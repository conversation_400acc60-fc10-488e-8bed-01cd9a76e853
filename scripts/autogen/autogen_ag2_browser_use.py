import os
import nest_asyncio

from autogen import <PERSON><PERSON><PERSON>, UserProxyAgent, config_list_from_json
from autogen.tools.experimental import BrowserUseTool, Crawl4AITool

from dotenv import load_dotenv

load_dotenv(override=True)

nest_asyncio.apply()

# config_list = config_list_from_json(env_or_file="OAI_CONFIG_LIST_FILE")

config_list = config_list_from_json("OAI_CONFIG_LIST", filter_dict={"model": "gpt-4o"})

llm_config = {
    "config_list": config_list,
}

user_proxy = UserProxyAgent(name="user_proxy", human_input_mode="NEVER")
assistant = AssistantAgent(name="assistant", llm_config=llm_config)

browser_use_tool = BrowserUseTool(
    llm_config=llm_config,
    browser_config={"headless": False},
)

browser_use_tool.register_for_execution(user_proxy)
browser_use_tool.register_for_llm(assistant)


# crawlai_tool = Crawl4AITool() # without llm_config

crawlai_tool = Crawl4AITool(llm_config=llm_config)

crawlai_tool.register_for_execution(user_proxy)
crawlai_tool.register_for_llm(assistant)

# message = """Go to Reddit, search for 'ag2' in the search bar, click on the first post and return the first comment."""
message = """Get info from https://docs.ag2.ai/docs/Home"""

result = user_proxy.initiate_chat(
    recipient=assistant,
    message=message,
    max_turns=2,
)
