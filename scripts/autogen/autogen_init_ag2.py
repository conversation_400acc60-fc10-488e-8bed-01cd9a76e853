import os
import nest_asyncio

from autogen import ConversableAgent, GroupChat, GroupChatManager, config_list_from_json, UserProxyAgent
from autogen.agents import WebSurferAgent

from utils.agent_utils import get_end_intent

nest_asyncio.apply()

# Load configuration for the LLM
config_list4 = config_list_from_json(
    "OAI_CONFIG_LIST", filter_dict={"model": [os.environ.get("AZURE_GPT4_MODEL_NAME", "obv-gpt4t0125")]}
)

llm_config4 = {
    "config_list": config_list4,
    "temperature": 0.1,
}

# Create expert agents
websurfer = WebSurferAgent(name="WebSurfer", llm_config=llm_config4, web_tool="crawl4ai")

research_expert = ConversableAgent(
    name="ResearchExpert",
    system_message="You are a research expert who helps analyze and synthesize information from various sources. You excel at drawing insights and making connections.",
    llm_config=llm_config4
)

ai_expert = ConversableAgent(
    name="<PERSON>E<PERSON><PERSON>", 
    system_message="You are an AI technology expert who understands various AI models, architectures, and their applications. You provide technical insights about AI systems.",
    llm_config=llm_config4
)

integrator = ConversableAgent(
    name="Integrator",
    system_message="You are an integration expert who helps combine insights from different sources and experts into coherent conclusions and recommendations.",
    llm_config=llm_config4
)

user_proxy = UserProxyAgent(
    name="UserProxy",
    human_input_mode="NEVER",
    is_termination_msg=lambda x: get_end_intent(x) == "end",
    code_execution_config=False,
    llm_config=llm_config4,
)

# Define nested chats for research workflow
research_workflow = [
    {
        "recipient": websurfer,
        "summary_method": "reflection_with_llm",
        "summary_prompt": "Summarize the key information gathered from web sources."
    },
    {
        "recipient": research_expert,
        "message": "Analyze the gathered information and identify key insights.",
        "summary_method": "reflection_with_llm",
        "max_turns": 2
    },
    {
        "recipient": ai_expert,
        "message": "Provide technical analysis and implications.",
        "summary_method": "reflection_with_llm",
        "max_turns": 2
    }
]

# Define nested chats for integration workflow
integration_workflow = [
    {
        "recipient": integrator,
        "message": "Synthesize all findings and create final recommendations.",
        "summary_method": "reflection_with_llm",
        "max_turns": 2
    }
]

# Register nested chats with agents
research_expert.register_nested_chats(
    research_workflow,
    trigger=lambda sender: sender not in [websurfer, ai_expert, integrator]
)

integrator.register_nested_chats(
    integration_workflow,
    trigger=lambda sender: sender not in [research_expert, websurfer, ai_expert]
)

# Create GroupChat
groupchat = GroupChat(
    agents=[user_proxy, websurfer, research_expert, ai_expert, integrator],
    messages=[],
    max_round=12,
    send_introductions=True
)

# Create GroupChatManager
manager = GroupChatManager(
    groupchat=groupchat,
    llm_config=llm_config4
)

# Function to initiate research
def initiate_research(query: str):
    """
    Initiates a research workflow using nested chats
    """
    # Start with research expert who will trigger nested chats
    return research_expert.generate_reply(
        messages=[{"role": "user", "content": query}]
    )




