"""
This is an example of a modified group chat using some of the agents in the agents/agents.py file. Compare these results to the results from autogen_standard_group_chat.py.
"""

import logging
import os

from typing import Dict, Optional, Union
from dotenv import load_dotenv, find_dotenv

from langchain_community.document_loaders import <PERSON>yPDFLoader, TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.chains import ConversationalRetrievalChain

from autogen_mods.modified_group_chat import ModifiedGroupChat, ModifiedGroupChatManager
from autogen import config_list_from_json

from agents.agents import (
    user_proxy,
    code_reviewer,
    agent_awareness_expert,
    python_expert,
    function_calling_agent,
    agi_gestalt_agent,
    creative_solution_agent,
    first_principles_thinker_agent,
    out_of_the_box_thinker_agent,
    strategic_planning_agent,
    project_manager_agent,
    efficiency_optimizer_agent,
    emotional_intelligence_expert_agent,
    task_history_review_agent,
    task_comprehension_agent,
    topology_diagram_agent,
)


from dotenv import load_dotenv

load_dotenv(override=True)

logging.basicConfig(level=logging.INFO)

# here going to use azure openai gpt
config_list3 = config_list_from_json(
    "OAI_CONFIG_LIST", filter_dict={"model": os.environ.get("AZURE_GPT3_MODEL_NAME", "gpt-35-turbo")}
)

config_list4 = config_list_from_json(
    "OAI_CONFIG_LIST", filter_dict={"model": os.environ.get("AZURE_GPT4_MODEL_NAME", "obv-gpt4t0125")}
)

llm_config4 = {
    # "seed": 42,
    "config_list": config_list4,
    "temperature": 0.1,
    # "cache_seed": os.environ.get("CACHE_SEED", None),
}


AGENT_TEAM = [
    user_proxy,
    code_reviewer,
    agent_awareness_expert,
    python_expert,
    function_calling_agent,
    # agi_gestalt_agent,
    creative_solution_agent,
    first_principles_thinker_agent,
    # out_of_the_box_thinker_agent,
    # strategic_planning_agent,
    project_manager_agent,
    # efficiency_optimizer_agent,
    # emotional_intelligence_expert_agent,
    task_history_review_agent,
    task_comprehension_agent,
    # topology_diagram_agent,
]

groupchat = ModifiedGroupChat(
    agents=AGENT_TEAM,
    messages=[],
    max_round=100,
    use_agent_council=True,
    inject_agent_council=False,
    continue_chat=False,
)
manager = ModifiedGroupChatManager(groupchat=groupchat, llm_config=llm_config4)

# NOTE: If the agents succussfully run their own autogen script, you will have to give it some time to process then press enter to exit the nested script.

# message = """I'm interested in building autonomous agents using the autogen python library. Can you show me a complete example of how to do this? The example should show how to correctly configure and instantiate autogen automous agents. The request given to the agents will be: "Please write and then execute a python script that prints 10 dad jokes". I want the agents to run completely autonomously without any human intervention."""

message = """Need to collect the issue details with id "612f1d67-3f9a-4a74-abbe-960b9e42700e" from NewRelic using nerdgraph api and then fetch entity relationship for the issue. gather all the entity information from newrelic and gather more details from web provide a comphrehensive analysis of the issue"""

user_proxy.initiate_chat(
    manager,
    clear_history=False,
    message=message,
)
