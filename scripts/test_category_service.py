#!/usr/bin/env python3
"""
Test script for the AlertCategoryService.

This script demonstrates loading and using the AlertCategoryService.
"""

import os
import sys
import json
from pathlib import Path

# Add the project root to the Python path
script_dir = Path(__file__).resolve().parent
project_root = script_dir.parent
sys.path.append(str(project_root))

# Import the service
from ai_incident_manager.services.alert_category_service import get_alert_category_service

# Try to import the old manager for comparison (should show deprecation warning)
from ai_incident_manager.config.alert_categories import AlertCategoryManager

def main():
    """Main test function."""
    print("\n===== Testing AlertCategoryService =====\n")
    
    # Get the service instance
    service = get_alert_category_service()
    print(f"Successfully initialized AlertCategoryService")
    
    # Get all categories
    categories = service.get_all_categories()
    print(f"Found {len(categories)} categories")
    
    # Print category names
    print("\nAvailable categories:")
    for category in categories:
        print(f"- {category['category']}")
    
    # Test getting a category by condition ID
    test_condition_id = "25541032"  # Pod with CrashLoopBackOff
    category = service.get_category_by_condition_id(test_condition_id)
    if category:
        print(f"\nFound category by condition ID {test_condition_id}: {category['category']}")
    else:
        print(f"\nNo category found for condition ID {test_condition_id}")
    
    # Test getting a category by pattern
    test_title = "Test alert: Pod in CrashLoopBackOff state"
    test_condition = "Pod with CrashLoopBackOff"
    category = service.get_category_by_patterns(test_title, test_condition)
    if category:
        print(f"\nFound category by pattern matching: {category['category']}")
        
        # Show runbook
        print("\nRunbook:")
        print(category.get('runbook', 'No runbook available'))
        
        # Show metrics
        print("\nMetrics to check:")
        for metric in category.get('metrics', []):
            print(f"- {metric.get('name')}")
    else:
        print(f"\nNo category found for title '{test_title}' and condition '{test_condition}'")
    
    # Test getting a category with all available methods
    category = service.get_category(test_title, test_condition, condition_id=test_condition_id)
    print(f"\nFinal category: {category['category']}")
    
    # Test the old manager (should show deprecation warning)
    print("\n===== Testing deprecated AlertCategoryManager =====\n")
    manager = AlertCategoryManager()
    manager_categories = manager.get_all_categories()
    print(f"Manager found {len(manager_categories)} categories")
    
    # Compare count
    if len(categories) == len(manager_categories):
        print("\nService and Manager return the same number of categories ✅")
    else:
        print(f"\nService and Manager return different numbers of categories ❌")
        print(f"Service: {len(categories)}, Manager: {len(manager_categories)}")
    
    # Test deprecated add_category (should show warning that it's a no-op)
    manager.add_category({"category": "test_category"})
    
    print("\nTests completed")

if __name__ == "__main__":
    main() 