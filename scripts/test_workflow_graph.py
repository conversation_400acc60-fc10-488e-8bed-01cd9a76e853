#!/usr/bin/env python3
"""
Test script for incrementally testing the incident analysis workflow.

This script creates a simplified version of the incident analysis workflow
with only the analyze_alert node to start, using the alert parser agent.
"""

import os
import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, TypedDict, Annotated
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
import dotenv

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import necessary components
from ai_incident_manager.models.workflow_state import IncidentState, InvestigationNote
from ai_incident_manager.agents.alert_parser_agent import (
    alert_parser_agent, AlertParserDeps, AlertParserResponse
)
from ai_incident_manager.lib.new_relic.graphql_client import NewRelicGraphQLClient
from ai_incident_manager.lib.new_relic.query_client import NewRelicQueryClient
from ai_incident_manager.lib.new_relic.logs_client import NewRelicLogsClient
from ai_incident_manager.agents.entity_analyzer import Entity<PERSON>nalyzer

from openai import AsyncAzureOpenAI

# Initialize OpenAI client
azure_openai_enabled = "AZURE_OPENAI_ENDPOINT" in os.environ and "AZURE_OPENAI_API_KEY" in os.environ

if azure_openai_enabled:
    openai_client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT"),
        azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
        api_version=os.environ.get("AZURE_OPENAI_API_VERSION", "2023-05-15")
    )
else:
    raise ValueError("Azure OpenAI is required for this script")

async def analyze_alert(state: IncidentState) -> Dict:
    """
    Analyze the alert to understand the incident context using the alert parser agent.
    
    Args:
        state: Current workflow state
        
    Returns:
        Updated fields of workflow state
    """
    logger.info(f"Analyzing alert for incident {state['incident_id']}")
    
    # Extract alert information
    alert = state["raw_alert"]
    
    # Initialize the New Relic clients
    api_key = os.environ.get("NEWRELIC_API_KEY")
    account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        raise ValueError("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set in environment variables")
        
    graphql_client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
    query_client = NewRelicQueryClient(graphql_client)
    logs_client = NewRelicLogsClient(graphql_client)
    
    # Initialize the entity analyzer
    entity_analyzer = EntityAnalyzer(graphql_client, debug=True)
    
    # Load alert categories from service
    from ai_incident_manager.services.alert_category_service import get_alert_category_service
    alert_categories = get_alert_category_service().get_all_categories()
    
    # Set up dependencies for the agent
    deps = AlertParserDeps(
        openai_client=openai_client,
        nr_query_client=query_client,
        entity_analyzer=entity_analyzer,
        alert_categories=alert_categories
    )
    
    # Prepare the user prompt with the alert data
    user_prompt = f"""
    ```json
    {json.dumps(alert, indent=2)}
    ```
    """
    
    current_investigation_state = state.get("investigation_state", {
        "current_step": 0,
        "notes": [],
        "next_steps": []
    })
    
    # Run the agent
    try:
        result = await alert_parser_agent.run(user_prompt, deps=deps)
        analysis_result = result.data
        
        # Add an investigation note
        note: InvestigationNote = {
            "timestamp": datetime.utcnow().isoformat(),
            "agent": "alert_parser",
            "note": f"Alert analyzed. Category: {analysis_result.get('alert_category')}",
            "data": analysis_result
        }
        
        # Update the entities in the state
        entities = []
        for entity in analysis_result.get("entities", []):
            entities.append({
                "entity_guid": entity.get("entity_guid", ""),
                "entity_name": entity.get("entity_name", "Unknown"),
                "entity_type": entity.get("entity_type", "Unknown"),
                "cluster_id": entity.get("cluster_id"),
                "product": entity.get("product"),
                "region": entity.get("region"),
                "metrics": {},
                "logs": [],
                "events": [],
                "metadata": entity.get("metadata", {})
            })
        
        # Update the investigation state
        updated_investigation_state = current_investigation_state.copy()
        updated_investigation_state["notes"].append(note)
        updated_investigation_state["current_step"] += 1
        updated_investigation_state["next_steps"] = ["collect_metrics"]  # Future steps
        
        # Return the updated state fields
        return {
            "investigation_state": updated_investigation_state,
            "alert_category": analysis_result.get("alert_category", "unknown"),
            "alert_runbook": analysis_result.get("alert_runbook", ""),
            "entities": entities,
            "condition_name": analysis_result.get("condition_name", ""),
            "condition_id": analysis_result.get("condition_id"),
            "policy_name": analysis_result.get("policy_name"),
            "policy_id": analysis_result.get("policy_id"),
            "cluster_name": analysis_result.get("cluster_name"),
            "investigation_notes": state.get("investigation_notes", []) + [note]
        }
        
    except Exception as e:
        logger.error(f"Error analyzing alert: {str(e)}")
        # Add an error note
        error_note: InvestigationNote = {
            "timestamp": datetime.utcnow().isoformat(),
            "agent": "alert_parser",
            "note": f"Error occurred during alert analysis: {str(e)}",
            "data": {"error": str(e)}
        }
        
        updated_investigation_state = current_investigation_state.copy()
        updated_investigation_state["notes"].append(error_note)
        updated_investigation_state["current_step"] += 1
        updated_investigation_state["next_steps"] = ["collect_metrics"]  # Continue with next step despite error
        
        # Return only the updated fields with the error
        return {
            "investigation_state": updated_investigation_state,
            "error": str(e),
            "investigation_notes": state.get("investigation_notes", []) + [error_note]
        }

def create_test_workflow() -> StateGraph:
    """
    Create a simplified workflow with just the analyze_alert node.
    
    Returns:
        StateGraph for testing
    """
    # Define the workflow
    workflow = StateGraph(IncidentState)
    
    # Add only the analyze_alert node for now
    workflow.add_node("analyze_alert", analyze_alert)
    
    # Set the entry point
    workflow.set_entry_point("analyze_alert")
    
    # Set a simple edge to end after the analyze_alert node
    workflow.add_edge("analyze_alert", END)
    
    # Compile the workflow
    return workflow.compile()

async def test_workflow():
    """
    Test the simplified workflow with a sample alert.
    """
    # Create the workflow
    workflow = create_test_workflow()
    
    # Sample test alert
    test_alert = {
        "issueId": "afb1be25-3714-4115-9005-9929ef244089", 
        "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/afb1be25-3714-4115-9005-9929ef244089?notifier=WEBHOOK",
        "title": "castai-workload-autoscaler query result is > 10.0 on 'Pod with CrashLoopBackOff -- '", 
        "priority": "CRITICAL", 
        "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtODY3MTE3NTgxODg1MDAzMTg3NQ"], 
        "impactedEntities": ["castai-workload-autoscaler"], 
        "totalIncidents": "1",
        "state": "ACTIVATED",
        "trigger": "STATE_CHANGE",
        "isCorrelated": "false",
        "createdAt": *************,
        "updatedAt": *************,
        "sources": ["newrelic"],
        "alertPolicyNames": ["Neurons k8s Infra - Critical"],
        "alertConditionNames": ["Pod with CrashLoopBackOff -- "],
        "workflowName": "obv-ai-processing-neurons",
        "chartLink": "https://gorgon.nr-assets.net/image/5f52ac1f-eabc-4086-ba53-77a6ee9322f8?config.legend.enabled=false&width=400&height=210",
        "product": "neurons",
        "nr_region": "us"
    }
    
    # Initial state
    incident_id = f"INC-{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
    initial_state: IncidentState = {
        "incident_id": incident_id,
        "raw_alert": test_alert,
        "title": test_alert.get("title", "Unknown"),
        "description": f"Alert from {test_alert.get('alertPolicyNames', ['Unknown'])[0]}", 
        "severity": test_alert.get("priority", "UNKNOWN"),
        "customer": None,
        "start_time": datetime.utcnow().isoformat(),
        "current_phase": "initialize",
        "investigation_notes": [],
        "primary_entity_guid": test_alert.get("EntityId", [""])[0],
        "primary_entity_type": None,
        "entity_details": [],
        "related_entity_details": [],
        "metrics": [],
        "logs": [],
        "system_checks": [],
        "root_cause": None,
        "remediation_actions": [],
        "output_incident": None,
        "investigation_state": {
            "current_step": 0,
            "notes": [],
            "next_steps": []
        }
    }
    
    # Run the workflow
    logger.info(f"Starting workflow for incident {incident_id}")
    result = await workflow.ainvoke(initial_state)
    
    # Print the result
    logger.info("Workflow execution complete")
    logger.info(f"Alert category: {result.get('alert_category', 'Unknown')}")
    logger.info(f"Number of entities: {len(result.get('entities', []))}")
    
    if "error" in result:
        logger.error(f"Workflow error: {result['error']}")
    
    # Print investigation notes
    for note in result.get("investigation_notes", []):
        logger.info(f"Note ({note['timestamp']}): {note['agent']}")
        logger.info(f"Content: {note['note']}")
    
    return result

if __name__ == "__main__":
    asyncio.run(test_workflow()) 