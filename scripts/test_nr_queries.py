#!/usr/bin/env python3
"""
Test script for quickly testing New Relic query methods with custom data.
"""

import os
import json
import argparse
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional, Union
from pprint import pprint
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import New Relic client
from lib.new_relic import NewRelicGraphQLClient, NewRelicQueryClient, Region


def setup_client() -> NewRelicQueryClient:
    """Set up and return the New Relic query client."""
    # Get API key and account ID from environment variables
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
    
    if not api_key:
        raise ValueError("NEWRELIC_API_KEY environment variable is required")
    
    # Create the GraphQL client
    graphql_client = NewRelicGraphQLClient(
        api_key=api_key,
        region=Region.US,
        account_id=account_id,
        debug=True  # Enable debugging for better visibility of what's happening
    )
    
    # Create and return the query client
    return NewRelicQueryClient(graphql_client)


def test_get_all_policy_ids(client: NewRelicQueryClient, account_id: Optional[str] = None) -> None:
    """Test getting all policy IDs."""
    try:
        policy_ids = client.get_all_policy_ids(account_id=account_id)
        print(f"Found {len(policy_ids)} policies:")
        for policy_id in policy_ids:
            print(f"  - {policy_id}")
    except Exception as e:
        print(f"Error: {str(e)}")


def test_get_webhook_destination(
    client: NewRelicQueryClient, 
    name: str, 
    url: str, 
    account_id: Optional[str] = None
) -> None:
    """Test getting webhook destination."""
    try:
        destination_id = client.get_webhook_destination(name=name, url=url, account_id=account_id)
        print(f"Webhook destination: {destination_id}")
    except Exception as e:
        print(f"Error: {str(e)}")


def test_get_notification_channel(
    client: NewRelicQueryClient,
    destination_id: str,
    name: str,
    account_id: Optional[str] = None
) -> None:
    """Test getting notification channel."""
    try:
        channel_id = client.get_notification_channel(
            destination_id=destination_id, name=name, account_id=account_id
        )
        print(f"Notification channel: {channel_id}")
    except Exception as e:
        print(f"Error: {str(e)}")


def test_get_workflow(
    client: NewRelicQueryClient,
    name: str,
    channel_id: str,
    account_id: Optional[str] = None
) -> None:
    """Test getting workflow."""
    try:
        workflow_id = client.get_workflow(name=name, channel_id=channel_id, account_id=account_id)
        print(f"Workflow: {workflow_id}")
    except Exception as e:
        print(f"Error: {str(e)}")


def test_get_kubernetes_events(
    client: NewRelicQueryClient,
    object_name: str,
    object_kind: str = "Pod",
    cluster_name: Optional[str] = None,
    account_id: Optional[str] = None,
    since: Optional[Union[str, int, datetime]] = None,
    until: Optional[Union[str, int, datetime]] = None
) -> None:
    """Test getting Kubernetes events."""
    try:
        # Default to 1 hour ago if since is not specified
        if since is None:
            since = datetime.now() - timedelta(hours=1)
            
        # Default to now if until is not specified
        if until is None:
            until = datetime.now()
            
        events = client.get_kubernetes_events(
            object_name=object_name,
            object_kind=object_kind,
            cluster_name=cluster_name,
            account_id=account_id,
            since=since,
            until=until
        )
        
        print(f"Found {len(events)} Kubernetes events:")
        for event in events:
            print(f"  - {event.get('reason', 'Unknown')} ({event.get('type', 'Unknown')}): {event.get('message', 'No message')}")
            print(f"    Time: {event.get('timestamp', 'Unknown')}")
            print()
    except Exception as e:
        print(f"Error: {str(e)}")


def test_get_issue_details(
    client: NewRelicQueryClient,
    issue_id: str,
    account_id: Optional[str] = None,
    lookback: str = "1 month"
) -> None:
    """Test getting issue details."""
    try:
        issue = client.get_issue_details(
            issue_id=issue_id,
            account_id=account_id,
            lookback=lookback
        )
        
        print(f"Issue details for {issue_id}:")
        pprint(issue)
    except Exception as e:
        print(f"Error: {str(e)}")


def test_get_entity_metrics_by_type(
    client: NewRelicQueryClient,
    entity_guid: str,
    entity_type: str,
    metrics: Optional[List[str]] = None,
    since: Optional[datetime] = None,
    until: Optional[datetime] = None,
    period: str = "1 minute",
    account_id: Optional[str] = None
) -> None:
    """Test getting entity metrics by type."""
    try:
        # Default to 1 hour ago if since is not specified
        if since is None:
            since = datetime.now() - timedelta(hours=1)
            
        # Default to now if until is not specified
        if until is None:
            until = datetime.now()
            
        metric_results = client.get_entity_metrics_by_type(
            entity_guid=entity_guid,
            entity_type=entity_type,
            metrics=metrics,
            since=since,
            until=until,
            period=period,
            account_id=account_id
        )
        
        print(f"Entity metrics for {entity_guid} ({entity_type}):")
        for metric_name, results in metric_results.items():
            print(f"  - {metric_name}: {len(results)} data points")
            if results:
                print(f"    First value: {results[0].value}, timestamp: {results[0].timestamp}")
                print(f"    Last value: {results[-1].value}, timestamp: {results[-1].timestamp}")
            print()
    except Exception as e:
        print(f"Error: {str(e)}")


def test_get_alert_condition_details(
    client: NewRelicQueryClient,
    condition_id: str,
    account_id: Optional[str] = None
) -> None:
    """Test getting alert condition details."""
    try:
        condition = client.get_alert_condition_details(
            condition_id=condition_id,
            account_id=account_id
        )
        
        print(f"Alert condition details for {condition_id}:")
        pprint(condition)
    except Exception as e:
        print(f"Error: {str(e)}")


def main():
    """Parse command line arguments and run the specified test function."""
    parser = argparse.ArgumentParser(description="Test New Relic query methods")
    
    # Add common arguments
    parser.add_argument(
        "--account-id", 
        help=f"New Relic account ID (defaults to NEWRELIC_ACCOUNT_ID env var)"
    )
    
    # Create subparsers for each test function
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # get_all_policy_ids
    policy_parser = subparsers.add_parser("get_all_policy_ids", help="Test get_all_policy_ids method")
    
    # get_webhook_destination
    webhook_parser = subparsers.add_parser("get_webhook_destination", help="Test get_webhook_destination method")
    webhook_parser.add_argument("--name", required=True, help="Webhook destination name")
    webhook_parser.add_argument("--url", required=True, help="Webhook destination URL")
    
    # get_notification_channel
    channel_parser = subparsers.add_parser("get_notification_channel", help="Test get_notification_channel method")
    channel_parser.add_argument("--destination-id", required=True, help="Destination ID")
    channel_parser.add_argument("--name", required=True, help="Channel name")
    
    # get_workflow
    workflow_parser = subparsers.add_parser("get_workflow", help="Test get_workflow method")
    workflow_parser.add_argument("--name", required=True, help="Workflow name")
    workflow_parser.add_argument("--channel-id", required=True, help="Channel ID")
    
    # get_kubernetes_events
    k8s_parser = subparsers.add_parser("get_kubernetes_events", help="Test get_kubernetes_events method")
    k8s_parser.add_argument("--object-name", required=True, help="Object name")
    k8s_parser.add_argument("--object-kind", default="Pod", help="Object kind (default: Pod)")
    k8s_parser.add_argument("--cluster-name", help="Cluster name")
    
    # get_issue_details
    issue_parser = subparsers.add_parser("get_issue_details", help="Test get_issue_details method")
    issue_parser.add_argument("--issue-id", required=True, help="Issue ID")
    issue_parser.add_argument("--lookback", default="1 month", help="Lookback period (default: 1 month)")
    
    # get_entity_metrics_by_type
    metrics_parser = subparsers.add_parser("get_entity_metrics_by_type", help="Test get_entity_metrics_by_type method")
    metrics_parser.add_argument("--entity-guid", required=True, help="Entity GUID")
    metrics_parser.add_argument("--entity-type", required=True, help="Entity type")
    metrics_parser.add_argument("--metrics", nargs="+", help="List of metrics to fetch")
    metrics_parser.add_argument("--period", default="1 minute", help="Time bucket for the timeseries (default: 1 minute)")
    
    # get_alert_condition_details
    condition_parser = subparsers.add_parser("get_alert_condition_details", help="Test get_alert_condition_details method")
    condition_parser.add_argument("--condition-id", required=True, help="Alert condition ID")
    
    # Parse arguments
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Set up client
    client = setup_client()
    
    # Convert any command-line arguments to the expected types
    kwargs = {k: v for k, v in vars(args).items() if k != "command" and v is not None}
    
    # Run the specified test function
    if args.command == "get_all_policy_ids":
        test_get_all_policy_ids(client, **kwargs)
    elif args.command == "get_webhook_destination":
        test_get_webhook_destination(client, **kwargs)
    elif args.command == "get_notification_channel":
        test_get_notification_channel(client, **kwargs)
    elif args.command == "get_workflow":
        test_get_workflow(client, **kwargs)
    elif args.command == "get_kubernetes_events":
        test_get_kubernetes_events(client, **kwargs)
    elif args.command == "get_issue_details":
        test_get_issue_details(client, **kwargs)
    elif args.command == "get_entity_metrics_by_type":
        test_get_entity_metrics_by_type(client, **kwargs)
    elif args.command == "get_alert_condition_details":
        test_get_alert_condition_details(client, **kwargs)


if __name__ == "__main__":
    main() 