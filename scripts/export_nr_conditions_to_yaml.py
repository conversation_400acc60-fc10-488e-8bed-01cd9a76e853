'''
This script exports New Relic conditions based on policy ID to a YAML file.
By default, only enabled conditions are exported unless the --all-conditions flag is used.

Examples:
    # Export enabled conditions for a single policy using account ID from environment
    python export_nr_conditions_to_yaml.py --policy-ids 123456
    
    # Export all conditions (enabled and disabled) for multiple policies
    python export_nr_conditions_to_yaml.py --policy-ids 123456 789012 --all-conditions
    
    # Export enabled conditions for multiple policies with custom output directory
    python export_nr_conditions_to_yaml.py --policy-ids 123456 789012 --output-dir /path/to/output
    
    # Export enabled conditions with explicit account ID
    python export_nr_conditions_to_yaml.py --account-id 1234567 --policy-ids 123456
'''
import requests
import json
import os
import argparse
import logging
import time
import yaml
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress
from rich.logging import RichHandler

from dotenv import load_dotenv

load_dotenv()

console = Console()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)]
)
log = logging.getLogger("rich")

class NewRelicConditionExporter:
    def __init__(self, api_key, account_id, region='US', verbose=False, enabled_only=True):
        self.api_key = api_key
        self.account_id = account_id
        self.api_endpoint = "https://api.newrelic.com/graphql" if region == 'US' else "https://api.eu.newrelic.com/graphql"
        self.headers = {
            "Content-Type": "application/json",
            "API-Key": self.api_key
        }
        self.verbose = verbose
        self.enabled_only = enabled_only

    def fetch_conditions_by_policy(self, policy_id):
        """Fetch conditions for a specific policy ID with pagination support
        If self.enabled_only is True, only fetch enabled conditions"""
        all_conditions = []
        cursor = None
        
        while True:
            query = '''
            query ($accountId: Int!, $policyId: ID!, $cursor: String) {
              actor {
                account(id: $accountId) {
                  alerts {
                    nrqlConditionsSearch(searchCriteria: { policyId: $policyId }, cursor: $cursor) {
                      nextCursor
                      totalCount
                      nrqlConditions {
                        id
                        name
                        description
                        enabled
                        nrql {
                          query
                        }
                        signal {
                          aggregationWindow
                          aggregationMethod
                          aggregationDelay
                          aggregationTimer
                        }
                        policyId
                        runbookUrl
                        terms {
                          operator
                          thresholdDuration
                          threshold
                          priority
                          thresholdOccurrences
                        }
                        type
                        violationTimeLimitSeconds
                      }
                    }
                  }
                }
              }
            }
            '''
            
            variables = {
                "accountId": self.account_id,
                "policyId": policy_id,
                "cursor": cursor
            }
            
            if self.verbose:
                log.info(f"Sending GraphQL request with cursor: {cursor}")
            
            try:
                response = requests.post(
                    self.api_endpoint, 
                    json={"query": query, "variables": variables}, 
                    headers=self.headers
                )
                response.raise_for_status()  # Raise exception for HTTP errors
            except requests.exceptions.RequestException as e:
                log.error(f"API request failed: {str(e)}")
                if self.verbose:
                    log.error(f"Request details: {variables}")
                break
            
            try:
                result = response.json()
                if 'errors' in result:
                    log.error(f"GraphQL errors: {result['errors']}")
                    break
                    
                search_results = result["data"]["actor"]["account"]["alerts"]["nrqlConditionsSearch"]
                page_conditions = search_results["nrqlConditions"]
                total_count = search_results["totalCount"]
                
                console.print(f"Fetched {len(page_conditions)} conditions (Progress: {len(all_conditions) + len(page_conditions)}/{total_count})", style="dim")
                
                # Filter conditions if enabled_only is True
                if self.enabled_only:
                    filtered_conditions = [c for c in page_conditions if c['enabled']]
                    if self.verbose and len(filtered_conditions) != len(page_conditions):
                        log.info(f"Filtered out {len(page_conditions) - len(filtered_conditions)} disabled conditions")
                    all_conditions.extend(filtered_conditions)
                else:
                    all_conditions.extend(page_conditions)
                
                # Get next cursor
                cursor = search_results["nextCursor"]
                
                # Break the loop if no more pages
                if not cursor:
                    break
                    
                # Small delay to avoid overwhelming the API
                time.sleep(0.5)
                    
            except KeyError as e:
                log.error(f"Error parsing response: {e}")
                if self.verbose:
                    log.error(f"Response: {response.text}")
                break
        
        return all_conditions

def display_conditions(conditions):
    """Display conditions in a rich table"""
    table = Table(title="New Relic Conditions")
    
    table.add_column("ID", style="cyan")
    table.add_column("Name", style="magenta")
    table.add_column("Type", style="green")
    table.add_column("Enabled", style="blue")
    table.add_column("Policy ID", style="yellow")
    
    for condition in conditions:
        table.add_row(
            condition['id'],
            condition['name'],
            condition['type'],
            str(condition['enabled']),
            condition['policyId']
        )
    
    console.print(Panel(table, expand=False))

def save_conditions_to_yaml(conditions, filename):
    """Save conditions to a YAML file for later use"""
    with open(filename, 'w') as f:
        yaml.dump(conditions, f, default_flow_style=False, sort_keys=False)
    log.info(f"Saved conditions to {filename}")

def main():
    parser = argparse.ArgumentParser(
        description="Export New Relic conditions based on policy ID to a YAML file. By default, only enabled conditions are exported.")
    parser.add_argument("--account-id", type=int, required=False, help="New Relic account ID (defaults to NEWRELIC_ACCOUNT_ID env variable)")
    parser.add_argument("--policy-ids", nargs='+', required=True, help="One or more policy IDs to export conditions for")
    parser.add_argument("--region", choices=['US', 'EU'], default='US', help="New Relic region (US or EU)")
    parser.add_argument("--output-dir", default=".", help="Output directory for YAML files (default: current directory)")
    parser.add_argument("--output-prefix", default="new_relic_policy_", help="Prefix for output filenames")
    parser.add_argument("--all-conditions", action="store_true", help="Export all conditions (both enabled and disabled)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()

    # Set log level based on verbose flag
    if args.verbose:
        logging.getLogger("rich").setLevel(logging.DEBUG)
    
    # Get API key from environment
    api_key = os.getenv("NEWRELIC_API_KEY")
    if not api_key:
        log.error("NEWRELIC_API_KEY environment variable is not set.")
        exit(1)
    
    # Get account ID from args or environment
    account_id = args.account_id
    if not account_id:
        account_id_str = os.getenv("NEWRELIC_ACCOUNT_ID")
        if not account_id_str:
            log.error("Account ID not provided and NEWRELIC_ACCOUNT_ID environment variable is not set.")
            exit(1)
        try:
            account_id = int(account_id_str)
        except ValueError:
            log.error(f"Invalid account ID in environment variable: {account_id_str}")
            exit(1)

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Initialize exporter
    exporter = NewRelicConditionExporter(api_key, account_id, args.region, verbose=args.verbose, enabled_only=not args.all_conditions)
    
    all_conditions = {}
    
    # Process each policy ID
    for policy_id in args.policy_ids:
        condition_type = "enabled conditions" if not args.all_conditions else "all conditions"
        log.info(f"Fetching {condition_type} for policy {policy_id}...")
        conditions = exporter.fetch_conditions_by_policy(policy_id)
        
        if not conditions:
            log.warning(f"No conditions found for policy {policy_id}.")
            continue
        
        # Display fetched conditions
        log.info(f"Found {len(conditions)} total conditions in policy {policy_id}")
        
        # Display conditions in a table
        display_conditions(conditions)
        
        # Store conditions for this policy
        all_conditions[policy_id] = conditions
        
        # Determine output filename for individual policy
        output_file = os.path.join(args.output_dir, f"{args.output_prefix}{policy_id}_conditions.yaml")
        
        # Save conditions to YAML file
        save_conditions_to_yaml(conditions, output_file)
        log.info(f"Exported {len(conditions)} conditions for policy {policy_id} to {output_file}")
    
    # If multiple policies were processed, create a combined file
    if len(args.policy_ids) > 1:
        combined_file = os.path.join(args.output_dir, f"{args.output_prefix}combined_conditions.yaml")
        save_conditions_to_yaml(all_conditions, combined_file)
        log.info(f"Exported combined conditions for all policies to {combined_file}")
    
    total_conditions = sum(len(conditions) for conditions in all_conditions.values())
    log.info(f"Successfully exported a total of {total_conditions} conditions from {len(args.policy_ids)} policies")

if __name__ == "__main__":
    main()
