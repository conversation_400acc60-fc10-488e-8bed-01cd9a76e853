#!/usr/bin/env python
"""
Simple test script to verify the New Relic client retry functionality.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the parent directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.base import NewRelicGraphQLError

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("nr_retry_test")

def test_retry_functionality():
    """
    Test the retry functionality with a simple query.
    """
    # Load environment variables
    load_dotenv()
    
    # Get New Relic credentials
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        logger.error("Missing New Relic credentials. Set NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID environment variables.")
        return
    
    logger.info("Initializing New Relic client...")
    client = NewRelicGraphQLClient(
        api_key=api_key,
        account_id=account_id,
        debug=True  # Enable debug to see more information
    )
    
    query_client = NewRelicQueryClient(client)
    
    # Test: Simple query to get policy IDs
    logger.info("Testing simple query to get policy IDs...")
    try:
        policies = query_client.get_all_policy_ids()
        logger.info(f"Successfully retrieved {len(policies)} policies")
        logger.info(f"First few policy IDs: {policies[:5] if policies else 'None'}")
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")

if __name__ == "__main__":
    logger.info("Starting New Relic retry functionality test...")
    test_retry_functionality()
    logger.info("Test completed.") 