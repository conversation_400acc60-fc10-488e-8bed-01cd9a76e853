# VictorOps Incident Management Tools

This directory contains tools for collecting and analyzing incident reports from VictorOps.

## Prerequisites

The following environment variables need to be set before using these scripts:

```
VICTOROPS_API_ID=your_victorops_api_id
VICTOROPS_API_KEY=your_victorops_api_key
```

You can add these to your `.env` file in the project root.

## Script Descriptions

### 1. VictorOps Incident Collector (`victorops_incidents_collector.py`)

This script collects incident reports from VictorOps for a specified time period (default: last 30 days).

#### Features:
- Collects all incidents with automatic pagination
- Handles API rate limiting (VictorOps allows max 1 request per minute)
- Supports filtering by routing key and current phase
- Saves results to a JSON file

#### Usage:

```bash
python victorops_incidents_collector.py [options]
```

#### Options:
- `--output`, `-o`: Output file name (default: `victorops_incidents.json`)
- `--routing-key`, `-r`: Filter by routing key (optional)
- `--current-phase`, `-p`: Filter by current phase (e.g., 'resolved', 'triggered') (optional)
- `--days`, `-d`: Number of days to look back (default: 30)

#### Example:

```bash
# Collect all resolved incidents from the last 30 days
python victorops_incidents_collector.py

# Collect incidents from the last 60 days for TechOps
python victorops_incidents_collector.py --days 60 --routing-key TechOps

# Collect only acknowledged incidents for the last 7 days
python victorops_incidents_collector.py --days 7 --current-phase acknowledged
```

### 2. VictorOps Incident Analyzer (`victorops_incident_analyzer.py`)

This script analyzes VictorOps incident data collected by the collector script.

#### Features:
- Comprehensive incident analysis
- Calculation of resolution and response times
- Breakdown by routing key, service, entity type, paged team, and paged user
- Analysis of incident distribution by time of day and day of week
- Outputs analysis as both human-readable text and optional JSON file

#### Usage:

```bash
python victorops_incident_analyzer.py [options]
```

#### Options:
- `--input`, `-i`: Input file name (default: `victorops_incidents.json`)
- `--output`, `-o`: Output file for analysis results in JSON format (optional)

#### Example:

```bash
# Analyze incidents from the default file
python victorops_incident_analyzer.py

# Analyze incidents from a custom file and save results
python victorops_incident_analyzer.py -i my_incidents.json -o analysis_results.json
```

### 3. VictorOps Data Pipeline (`victorops_data_pipeline.py`)

This wrapper script runs the collector and analyzer in sequence for a streamlined workflow.

#### Features:
- Runs both the collector and analyzer scripts with a single command
- Generates timestamped filenames by default
- Options to skip collection if you already have the incident data
- Verifies that environment variables are properly set

#### Usage:

```bash
python victorops_data_pipeline.py [options]
```

#### Options:
- `--incident-file`: Output file for incidents (default: `victorops_incidents_YYYY-MM-DD.json`)
- `--analysis-file`: Output file for analysis (default: `victorops_analysis_YYYY-MM-DD.json`)
- `--routing-key`: Filter by routing key (optional)
- `--current-phase`: Filter by current phase (optional)
- `--days`: Number of days to look back (default: 30)
- `--skip-collect`: Skip collection and use existing incident file

#### Example:

```bash
# Run the complete pipeline with default settings
python victorops_data_pipeline.py

# Run the pipeline for a specific routing key and time period
python victorops_data_pipeline.py --routing-key TechOps --days 14

# Skip collection and analyze an existing incident file
python victorops_data_pipeline.py --skip-collect --incident-file existing_incidents.json
```

## Example Workflow

1. Set up the environment variables in your `.env` file:
```
VICTOROPS_API_ID=your_victorops_api_id
VICTOROPS_API_KEY=your_victorops_api_key
```

2. Navigate to the victorOps directory:
```bash
cd victorOps
```

3. Run the complete data pipeline:
```bash
python victorops_data_pipeline.py
```

4. View the analysis results in the console and check the generated JSON files for further processing.

## Notes

- The VictorOps API has a rate limit of 1 request per minute. The collector script automatically handles this by waiting between requests.
- For large date ranges, the collection process might take some time due to the API rate limits.
- These scripts require the `requests`, `python-dotenv`, and other standard Python libraries which are already included in the project dependencies. 