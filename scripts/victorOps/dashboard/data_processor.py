#!/usr/bin/env python3
"""
VictorOps Dashboard Data Processor

This module loads and processes VictorOps incident data for visualization.
"""

import sys
import os
import json
from typing import Dict, List, Any, Optional
from collections import defaultdict

# Add parent directory to path to import victorops_incident_analyzer
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from victorops_incident_analyzer import (
    load_incidents_from_file,
    analyze_incidents,
    extract_clean_entity_name,
    filter_incidents_by_routing_key,
    group_incidents_by_entity_name
)

class DashboardDataProcessor:
    """Class for processing VictorOps incident data for dashboard visualization."""
    
    def __init__(self, incident_file: str):
        """
        Initialize the data processor.
        
        Args:
            incident_file: Path to the VictorOps incident JSON file
        """
        self.incident_file = incident_file
        self.incidents = load_incidents_from_file(incident_file)
        self.filtered_incidents = self.incidents
        self.analysis = None
        
    def filter_by_routing_key(self, routing_keys: Optional[List[str]] = None) -> None:
        """
        Filter incidents by one or more routing keys.
        
        Args:
            routing_keys: List of routing keys to filter by (e.g., ['TechOps', 'DevOps'])
        """
        if routing_keys and len(routing_keys) > 0:
            # Filter incidents by multiple routing keys
            self.filtered_incidents = []
            for incident in self.incidents:
                incident_key = incident.get("routingKey")
                if incident_key in routing_keys:
                    self.filtered_incidents.append(incident)
        else:
            self.filtered_incidents = self.incidents
        
        # Update analysis with filtered data
        self.analyze()
        
    def analyze(self) -> Dict[str, Any]:
        """
        Analyze the filtered incidents.
        
        Returns:
            Dictionary with analysis results
        """
        self.analysis = analyze_incidents(self.filtered_incidents)
        return self.analysis
    
    def get_routing_keys(self) -> List[str]:
        """
        Get all unique routing keys from the incidents.
        
        Returns:
            List of routing keys
        """
        keys = set()
        for incident in self.incidents:
            key = incident.get("routingKey")
            if key:
                keys.add(key)
        return sorted(list(keys))
    
    def get_entity_groups(self) -> Dict[str, List[Dict]]:
        """
        Get incidents grouped by entity display name.
        
        Returns:
            Dictionary mapping entity names to lists of incidents
        """
        return group_incidents_by_entity_name(self.filtered_incidents)
    
    def get_time_series_data(self) -> Dict[str, Dict[str, int]]:
        """
        Prepare time series data for incidents over time.
        
        Returns:
            Dictionary with time series data
        """
        # Count incidents by date
        dates = defaultdict(int)
        for incident in self.filtered_incidents:
            start_time = incident.get("startTime", "")
            if start_time:
                # Extract date part (YYYY-MM-DD)
                date = start_time.split("T")[0]
                dates[date] += 1
        
        # Sort by date
        sorted_dates = sorted(dates.items())
        
        return {
            "dates": [date for date, _ in sorted_dates],
            "counts": [count for _, count in sorted_dates]
        }
    
    def get_resolution_time_histogram(self) -> Dict[str, List]:
        """
        Prepare resolution time data for histogram.
        
        Returns:
            Dictionary with resolution time bins and counts
        """
        resolution_times = []
        
        for incident in self.filtered_incidents:
            transitions = incident.get("transitions", [])
            triggered_time = None
            resolved_time = None
            
            for transition in transitions:
                if transition.get("name") == "triggered":
                    triggered_time = transition.get("at", "")
                elif transition.get("name") == "resolved":
                    resolved_time = transition.get("at", "")
            
            if triggered_time and resolved_time:
                # Convert to timestamps and calculate difference in minutes
                triggered = triggered_time.replace("Z", "+00:00")
                resolved = resolved_time.replace("Z", "+00:00")
                
                from datetime import datetime
                t1 = datetime.fromisoformat(triggered)
                t2 = datetime.fromisoformat(resolved)
                
                minutes = (t2 - t1).total_seconds() / 60
                resolution_times.append(minutes)
        
        # Create bins for histogram
        bins = []
        counts = []
        
        if resolution_times:
            # Define bin ranges in minutes
            bin_ranges = [
                (0, 15, "0-15 min"),
                (15, 30, "15-30 min"),
                (30, 60, "30-60 min"),
                (60, 120, "1-2 hours"),
                (120, 240, "2-4 hours"),
                (240, 480, "4-8 hours"),
                (480, 1440, "8-24 hours"),
                (1440, float('inf'), ">24 hours")
            ]
            
            # Count incidents in each bin
            for start, end, label in bin_ranges:
                count = sum(1 for t in resolution_times if start <= t < end)
                if count > 0:
                    bins.append(label)
                    counts.append(count)
        
        return {
            "bins": bins,
            "counts": counts
        }

if __name__ == "__main__":
    # Example usage
    processor = DashboardDataProcessor("victorops_incidents.json")
    analysis = processor.analyze()
    print(f"Loaded {len(processor.incidents)} incidents")
    print(f"Available routing keys: {processor.get_routing_keys()}")
