<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VictorOps Incident Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Plotly.js for charts -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <!-- Custom CSS for dashboard -->
    <style>
        body {
            background-color: #f0f2f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #2c3e50;
        }
        .dashboard-header {
            background: linear-gradient(135deg, #0062cc, #007bff);
            color: white;
            padding: 20px 0;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .dashboard-title {
            font-weight: 300;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
            transition: all 0.3s;
            border: none;
            overflow: hidden;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }
        .card-header {
            background: linear-gradient(to right, #007bff, #0099f7);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            font-weight: 500;
            border: none;
            padding: 12px 15px;
        }
        .metrics-card {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        .metric-value {
            font-size: 2.2rem;
            font-weight: 600;
            color: #007bff;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .filter-section {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #007bff;
        }
        .table-container {
            max-height: 400px;
            overflow-y: auto;
        }
        .sidebar {
            position: sticky;
            top: 20px;
        }
        /* Custom colors for status indicators */
        .status-resolved {
            color: #28a745;
        }
        .status-triggered {
            color: #dc3545;
        }
        .status-acknowledged {
            color: #ffc107;
        }
        /* Loading spinner */
        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Loading spinner -->
    <div class="loading" id="loading">
        <div class="loading-spinner"></div>
    </div>

    <!-- Dashboard Header -->
    <header class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="dashboard-title">
                        <i class="fas fa-chart-line me-2"></i>VictorOps Incident Analytics
                    </h1>
                    <p class="text-light mb-0">Comprehensive visualization of incident data</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex justify-content-md-end flex-column">
                        <div class="mb-2">
                            <span class="text-light">Total Incidents:</span>
                            <span class="badge bg-primary" id="total-incidents">-</span>
                        </div>
                        <div>
                            <span class="badge bg-info" id="current-filter">Showing all incidents</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard Content -->
    <div class="container">
        <!-- Filters Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="filter-section">
                    <h5><i class="fas fa-filter me-2"></i>Filter Incidents</h5>
                    <form id="filter-form" class="row g-3 align-items-end">
                        <div class="col-md-6">
                            <label for="routing-key" class="form-label">Routing Keys (Multiple Selection)</label>
                            <div class="d-flex mb-2">
                                <button type="button" class="btn btn-sm btn-outline-primary me-2" id="select-all-keys">Select All</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="deselect-all-keys">Deselect All</button>
                            </div>
                            <select class="form-select" id="routing-key" name="routing_keys[]" multiple size="5">
                                {% for key in routing_keys %}
                                <option value="{{ key }}">{{ key }}</option>
                                {% endfor %}
                            </select>
                            <small class="form-text text-muted">Hold Ctrl/Cmd to select multiple keys</small>
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Apply Filters
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metrics-card">
                    <div class="metric-value" id="avg-resolution-time">-</div>
                    <div class="metric-label">Avg. Resolution Time (min)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metrics-card">
                    <div class="metric-value" id="avg-response-time">-</div>
                    <div class="metric-label">Avg. Response Time (min)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metrics-card">
                    <div class="metric-value" id="top-routing-key">-</div>
                    <div class="metric-label">Top Routing Key</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metrics-card">
                    <div class="metric-value" id="top-entity-type">-</div>
                    <div class="metric-label">Top Entity Type</div>
                </div>
            </div>
        </div>

        <!-- Charts Row 1 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-calendar-alt me-2"></i>Incidents Over Time
                    </div>
                    <div class="card-body">
                        <div id="time-series-chart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-clock me-2"></i>Resolution Time Distribution
                    </div>
                    <div class="card-body">
                        <div id="resolution-histogram" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 2 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-tags me-2"></i>Incidents by Routing Key
                    </div>
                    <div class="card-body">
                        <div id="routing-key-chart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-server me-2"></i>Top 10 Services
                    </div>
                    <div class="card-body">
                        <div id="service-chart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 3 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-cubes me-2"></i>Incidents by Entity Type
                    </div>
                    <div class="card-body">
                        <div id="entity-type-chart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-users me-2"></i>Incidents by Paged Team
                    </div>
                    <div class="card-body">
                        <div id="paged-team-chart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 4 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-sun me-2"></i>Incidents by Time of Day
                    </div>
                    <div class="card-body">
                        <div id="time-of-day-chart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-calendar-week me-2"></i>Incidents by Day of Week
                    </div>
                    <div class="card-body">
                        <div id="day-of-week-chart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Entity Groups Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-layer-group me-2"></i>Entity Groups
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-light active" id="chart-view-btn">
                                <i class="fas fa-chart-bar me-1"></i>Chart
                            </button>
                            <button type="button" class="btn btn-sm btn-light" id="table-view-btn">
                                <i class="fas fa-table me-1"></i>Table
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Chart View -->
                        <div id="entity-group-chart-container">
                            <div id="entity-group-chart" style="height: 600px; width: 100%;"></div>
                        </div>
                        
                        <!-- Table View (Hidden by default) -->
                        <div id="entity-group-table-container" style="display: none;" class="p-3">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="entity-group-table">
                                    <thead class="table-primary">
                                        <tr>
                                            <th scope="col">Entity Name</th>
                                            <th scope="col">Incidents</th>
                                            <th scope="col">Avg Resolution (min)</th>
                                            <th scope="col">Latest Status</th>
                                            <th scope="col">Latest Incident</th>
                                            <th scope="col">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Table rows will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Entity Group Detail Modal -->
        <div class="modal fade" id="entity-detail-modal" tabindex="-1" aria-labelledby="entityDetailModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="entityDetailModalLabel">Entity Group Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <h6 id="entity-detail-name" class="mb-3 fw-bold"></h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered" id="entity-incidents-table">
                                <thead class="table-primary">
                                    <tr>
                                        <th>Incident #</th>
                                        <th>Status</th>
                                        <th>Start Time</th>
                                        <th>Last Updated</th>
                                        <th>Alert Count</th>
                                        <th>Routing Key</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Incident rows will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                        <h6 class="mt-4 mb-2">Timeline & Metrics</h6>
                        <div id="entity-timeline-chart" style="height: 200px;"></div>
                        <h6 class="mt-4 mb-2">Latest Root Cause Analysis</h6>
                        <div id="entity-rca-section" class="p-3 bg-light rounded">
                            <p class="text-muted">No RCA information available</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Show loading spinner
            function showLoading() {
                $('#loading').css('display', 'flex');
            }

            // Hide loading spinner
            function hideLoading() {
                $('#loading').css('display', 'none');
            }

            // Load data and update charts
            function loadData() {
                showLoading();
                $.getJSON('/api/data')
                    .done(function(data) {
                        updateDashboard(data);
                        updateEntityGroupTable(data.entity_group_data.labels, data.entity_group_data.values, data.entity_details);
                        hideLoading();
                    })
                    .fail(function(jqXHR, textStatus, errorThrown) {
                        console.error('Error loading data:', errorThrown);
                        hideLoading();
                        alert('Error loading data. Please check the console for details.');
                    });
            }

            // Update all dashboard elements
            function updateDashboard(data) {
                // Update metrics
                $('#total-incidents').text(data.total_incidents);
                $('#avg-resolution-time').text(data.resolution_times.avg ? data.resolution_times.avg.toFixed(1) : '-');
                $('#avg-response-time').text(data.response_times.avg ? data.response_times.avg.toFixed(1) : '-');
                
                // Update top routing key and entity type
                if (data.routing_key_data.labels.length > 0) {
                    $('#top-routing-key').text(data.routing_key_data.labels[0]);
                }
                
                if (data.entity_type_data.labels.length > 0) {
                    $('#top-entity-type').text(data.entity_type_data.labels[0]);
                }
                
                // Create charts
                createPieChart('routing-key-chart', 'Incidents by Routing Key', 
                               data.routing_key_data.labels, data.routing_key_data.values);
                
                createPieChart('entity-type-chart', 'Incidents by Entity Type', 
                               data.entity_type_data.labels, data.entity_type_data.values);
                
                createBarChart('service-chart', 'Top 10 Services', 
                              data.service_data.labels, data.service_data.values);
                
                createPieChart('paged-team-chart', 'Incidents by Paged Team', 
                               data.paged_team_data.labels, data.paged_team_data.values);
                
                createBarChart('time-of-day-chart', 'Incidents by Time of Day', 
                              data.time_of_day_data.labels, data.time_of_day_data.values);
                
                createBarChart('day-of-week-chart', 'Incidents by Day of Week', 
                              data.day_of_week_data.labels, data.day_of_week_data.values);
                
                createHorizontalBarChart('entity-group-chart', 'Top 20 Entity Groups', 
                                        data.entity_group_data.labels, data.entity_group_data.values);
                
                createLineChart('time-series-chart', 'Incidents Over Time', 
                               data.time_series_data.dates, data.time_series_data.counts);
                
                createBarChart('resolution-histogram', 'Resolution Time Distribution', 
                              data.resolution_histogram.bins, data.resolution_histogram.counts);
            }

            // Create a pie chart
            function createPieChart(elementId, title, labels, values) {
                var data = [{
                    type: 'pie',
                    labels: labels,
                    values: values,
                    hole: 0.4,
                    marker: {
                        colors: generateColors(labels.length)
                    },
                    textinfo: 'label+percent',
                    insidetextorientation: 'radial'
                }];

                var layout = {
                    title: title,
                    showlegend: false,
                    margin: {t: 30, b: 0, l: 0, r: 0},
                    height: 300
                };

                Plotly.newPlot(elementId, data, layout, {responsive: true});
            }

            // Create a bar chart
            function createBarChart(elementId, title, labels, values) {
                var data = [{
                    type: 'bar',
                    x: labels,
                    y: values,
                    marker: {
                        color: '#007bff'
                    }
                }];

                var layout = {
                    title: title,
                    margin: {t: 30, b: 50, l: 50, r: 20},
                    height: 300
                };

                Plotly.newPlot(elementId, data, layout, {responsive: true});
            }

            // Create a horizontal bar chart
            function createHorizontalBarChart(elementId, title, labels, values) {
                var data = [{
                    type: 'bar',
                    orientation: 'h',
                    y: labels,
                    x: values,
                    marker: {
                        color: '#007bff'
                    }
                }];

                var layout = {
                    title: title,
                    margin: {t: 30, b: 20, l: 300, r: 50},
                    height: 600,
                    font: {family: 'Segoe UI, sans-serif', size: 12},
                    autosize: true,
                    yaxis: {
                        automargin: true,
                        tickfont: {size: 11}
                    }
                };

                Plotly.newPlot(elementId, data, layout, {responsive: true});
            }

            // Create a line chart
            function createLineChart(elementId, title, labels, values) {
                var data = [{
                    type: 'scatter',
                    mode: 'lines+markers',
                    x: labels,
                    y: values,
                    line: {
                        color: '#007bff',
                        width: 2
                    },
                    marker: {
                        color: '#007bff',
                        size: 6
                    }
                }];

                var layout = {
                    title: title,
                    margin: {t: 30, b: 50, l: 50, r: 20},
                    height: 300,
                    xaxis: {
                        title: 'Date'
                    },
                    yaxis: {
                        title: 'Incident Count'
                    }
                };

                Plotly.newPlot(elementId, data, layout, {responsive: true});
            }

            // Generate colors for charts
            function generateColors(count) {
                var colors = [];
                var baseColors = [
                    '#3366cc', '#109618', '#ff9900', '#dc3912', '#990099',
                    '#0099c6', '#dd4477', '#66aa00', '#b82e2e', '#316395',
                    '#994499', '#22aa99', '#aaaa11', '#6633cc', '#e67300',
                    '#8b0707', '#651067', '#329262', '#5574a6', '#3b3eac'
                ];
                
                for (var i = 0; i < count; i++) {
                    colors.push(baseColors[i % baseColors.length]);
                }
                
                return colors;
            }

            // Handle routing key selection buttons
            $('#select-all-keys').on('click', function() {
                $('#routing-key option').prop('selected', true);
            });
            
            $('#deselect-all-keys').on('click', function() {
                $('#routing-key option').prop('selected', false);
            });
            
            // Handle filter form submission
            $('#filter-form').on('submit', function(e) {
                e.preventDefault();
                var formData = $(this).serialize();
                var selectedKeys = $('#routing-key').val() || [];
                
                // Show visual feedback about the selected filters
                var filterText = selectedKeys.length > 0 ? 
                    'Filtering by ' + selectedKeys.length + ' routing key' + (selectedKeys.length > 1 ? 's' : '') : 
                    'Showing all incidents';
                    
                showLoading();
                $.post('/api/filter', formData)
                    .done(function() {
                        loadData();
                        $('#current-filter').text(filterText);
                    })
                    .fail(function() {
                        hideLoading();
                        alert('Error applying filters.');
                    });
            });

            // Function to update the entity group table
            function updateEntityGroupTable(entities, counts, entityDetails) {
                const tableBody = $('#entity-group-table tbody');
                tableBody.empty();
                
                if (!entityDetails) {
                    entityDetails = {}; // Fallback if no details provided
                }
                
                // Get the indexes in sorted order (by count)
                let indexOrder = [];
                for (let i = 0; i < entities.length; i++) {
                    indexOrder.push(i);
                }
                indexOrder.sort((a, b) => counts[b] - counts[a]);
                
                // Create table rows
                for (let idx of indexOrder) {
                    const entityName = entities[idx];
                    const incidentCount = counts[idx];
                    const details = entityDetails[entityName] || {};
                    
                    // Format resolution time with fallback values
                    let avgRes = details.avg_resolution || '-';
                    if (avgRes !== '-') {
                        avgRes = parseFloat(avgRes).toFixed(1);
                    }
                    
                    // Get status with appropriate styling
                    let statusClass = '';
                    if (details.latest_status === 'resolved') {
                        statusClass = 'text-success';
                    } else if (details.latest_status === 'triggered') {
                        statusClass = 'text-danger';
                    } else if (details.latest_status === 'acknowledged') {
                        statusClass = 'text-warning';
                    }
                    
                    // Format the latest incident date if available
                    let latestDate = details.latest_time || '-';
                    if (latestDate !== '-') {
                        latestDate = new Date(latestDate).toLocaleString();
                    }
                    
                    // Create the row
                    const row = $(`
                        <tr>
                            <td>${entityName}</td>
                            <td>${incidentCount}</td>
                            <td>${avgRes}</td>
                            <td><span class="${statusClass}">${details.latest_status || '-'}</span></td>
                            <td>${latestDate}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary view-details-btn" data-entity="${entityName}">
                                    <i class="fas fa-info-circle"></i> Details
                                </button>
                            </td>
                        </tr>
                    `);
                    
                    tableBody.append(row);
                }
                
                // Add click handler for detail buttons
                $('.view-details-btn').on('click', function() {
                    const entityName = $(this).data('entity');
                    showEntityDetails(entityName);
                });
            }
            
            // Function to show entity details in a modal
            function showEntityDetails(entityName) {
                // Set the entity name in the modal
                $('#entity-detail-name').text(entityName);
                
                // Load entity details from the server
                $.getJSON(`/api/entity/${encodeURIComponent(entityName)}`)
                    .done(function(data) {
                        // Populate the incidents table
                        const tableBody = $('#entity-incidents-table tbody');
                        tableBody.empty();
                        
                        if (data.incidents && data.incidents.length > 0) {
                            data.incidents.forEach(function(incident) {
                                let statusClass = '';
                                if (incident.currentPhase === 'resolved') {
                                    statusClass = 'text-success';
                                } else if (incident.currentPhase === 'triggered') {
                                    statusClass = 'text-danger';
                                } else if (incident.currentPhase === 'acknowledged') {
                                    statusClass = 'text-warning';
                                }
                                
                                const row = $(`
                                    <tr>
                                        <td>${incident.incidentNumber || '-'}</td>
                                        <td><span class="${statusClass}">${incident.currentPhase || '-'}</span></td>
                                        <td>${incident.startTime ? new Date(incident.startTime).toLocaleString() : '-'}</td>
                                        <td>${incident.lastAlertTime ? new Date(incident.lastAlertTime).toLocaleString() : '-'}</td>
                                        <td>${incident.alertCount || '0'}</td>
                                        <td>${incident.routingKey || '-'}</td>
                                    </tr>
                                `);
                                
                                tableBody.append(row);
                            });
                            
                            // Create timeline chart if data available
                            if (data.timeline && data.timeline.dates && data.timeline.dates.length > 0) {
                                createLineChart('entity-timeline-chart', 'Incident Timeline', 
                                              data.timeline.dates, data.timeline.counts);
                            }
                            
                            // Display RCA if available
                            if (data.latest_rca) {
                                $('#entity-rca-section').html(`<p>${data.latest_rca}</p>`);
                            } else {
                                $('#entity-rca-section').html(`<p class="text-muted">No RCA information available</p>`);
                            }
                        } else {
                            tableBody.html(`<tr><td colspan="6" class="text-center">No incidents found</td></tr>`);
                        }
                        
                        // Show the modal
                        $('#entity-detail-modal').modal('show');
                    })
                    .fail(function(jqXHR, textStatus, errorThrown) {
                        console.error('Error loading entity details:', errorThrown);
                        alert('Error loading entity details. Please check the console for more information.');
                    });
            }
            
            // Toggle between chart and table views
            $('#chart-view-btn').on('click', function() {
                $(this).addClass('active');
                $('#table-view-btn').removeClass('active');
                $('#entity-group-chart-container').show();
                $('#entity-group-table-container').hide();
            });
            
            $('#table-view-btn').on('click', function() {
                $(this).addClass('active');
                $('#chart-view-btn').removeClass('active');
                $('#entity-group-chart-container').hide();
                $('#entity-group-table-container').show();
            });
            
            // Initial data load
            loadData();
        });
    </script>
</body>
</html>
