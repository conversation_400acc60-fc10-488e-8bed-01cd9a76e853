#!/usr/bin/env python3
"""
VictorOps Dashboard Web Application

This module provides a Flask web application to visualize Victor<PERSON><PERSON> incident data.
"""

import os
import json
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from data_processor import DashboardDataProcessor

app = Flask(__name__)

# Default incident file path
DEFAULT_INCIDENT_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                    "victorops_incidents.json")

# Initialize data processor
data_processor = None

@app.route('/')
def index():
    """Render the main dashboard page."""
    global data_processor
    
    # Get available routing keys
    routing_keys = []
    if data_processor:
        routing_keys = data_processor.get_routing_keys()
    
    return render_template('index.html', routing_keys=routing_keys)

@app.route('/api/filter', methods=['POST'])
def filter_data():
    """Filter incidents by multiple routing keys."""
    global data_processor
    
    if not data_processor:
        return jsonify({"error": "No data loaded"}), 400
    
    # Get routing keys from form (will be a list if multiple selected)
    routing_keys = request.form.getlist('routing_keys[]')
    
    # Filter data with the list of routing keys
    data_processor.filter_by_routing_key(routing_keys if routing_keys else None)
    
    # Return success
    return jsonify({"success": True})

@app.route('/api/data')
def get_data():
    """Get all data for dashboard."""
    global data_processor
    
    if not data_processor:
        return jsonify({"error": "No data loaded"}), 400
    
    # Get current analysis
    analysis = data_processor.analysis
    
    # Get data for charts
    routing_key_data = {
        "labels": list(analysis["by_routing_key"].keys()),
        "values": list(analysis["by_routing_key"].values())
    }
    
    entity_type_data = {
        "labels": list(analysis["by_entity_type"].keys()),
        "values": list(analysis["by_entity_type"].values())
    }
    
    paged_team_data = {
        "labels": list(analysis["by_paged_team"].keys()),
        "values": list(analysis["by_paged_team"].values())
    }
    
    # Get top services
    service_items = sorted(analysis["by_service"].items(), key=lambda x: x[1], reverse=True)[:10]
    service_data = {
        "labels": [item[0] for item in service_items],
        "values": [item[1] for item in service_items]
    }
    
    # Get time of day data
    time_of_day_data = {
        "labels": [f"{hour}:00" for hour in range(24)],
        "values": [analysis["by_time_of_day"].get(str(hour), 0) for hour in range(24)]
    }
    
    # Get day of week data
    days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
    day_of_week_data = {
        "labels": days,
        "values": [analysis["by_day_of_week"].get(day, 0) for day in days]
    }
    
    # Get entity group data (top 20)
    entity_groups = data_processor.get_entity_groups()
    entity_items = sorted([(name, len(incidents)) for name, incidents in entity_groups.items()], 
                         key=lambda x: x[1], reverse=True)[:20]
    entity_group_data = {
        "labels": [item[0] for item in entity_items],
        "values": [item[1] for item in entity_items]
    }
    
    # Get time series data
    time_series_data = data_processor.get_time_series_data()
    
    # Get resolution time histogram
    resolution_histogram = data_processor.get_resolution_time_histogram()
    
    # Calculate entity details for table view
    entity_details = {}
    for entity_name, incidents in entity_groups.items():
        if not incidents:
            continue
            
        # Find latest incident and its status
        latest_incident = max(incidents, key=lambda x: x.get("startTime", ""))
        latest_status = latest_incident.get("currentPhase", "-")
        latest_time = latest_incident.get("startTime", "-")
        
        # Calculate average resolution time
        resolution_times = []
        for incident in incidents:
            transitions = incident.get("transitions", [])
            triggered_time = None
            resolved_time = None
            
            for transition in transitions:
                if transition.get("name") == "triggered":
                    triggered_time = transition.get("at", "")
                elif transition.get("name") == "resolved":
                    resolved_time = transition.get("at", "")
            
            if triggered_time and resolved_time:
                # Convert to timestamps and calculate difference in minutes
                triggered = triggered_time.replace("Z", "+00:00")
                resolved = resolved_time.replace("Z", "+00:00")
                
                t1 = datetime.fromisoformat(triggered)
                t2 = datetime.fromisoformat(resolved)
                
                minutes = (t2 - t1).total_seconds() / 60
                resolution_times.append(minutes)
        
        avg_resolution = sum(resolution_times) / len(resolution_times) if resolution_times else None
        
        # Store entity details
        entity_details[entity_name] = {
            "latest_status": latest_status,
            "latest_time": latest_time,
            "avg_resolution": avg_resolution
        }
    
    # Combine all data
    return jsonify({
        "total_incidents": analysis["total_incidents"],
        "routing_key_data": routing_key_data,
        "entity_type_data": entity_type_data,
        "service_data": service_data,
        "paged_team_data": paged_team_data,
        "time_of_day_data": time_of_day_data,
        "day_of_week_data": day_of_week_data,
        "entity_group_data": entity_group_data,
        "time_series_data": time_series_data,
        "resolution_histogram": resolution_histogram,
        "resolution_times": analysis["resolution_times"],
        "response_times": analysis["response_times"],
        "entity_details": entity_details
    })

@app.route('/api/entity/<entity_name>')
def get_entity_details(entity_name):
    """Get detailed information for a specific entity."""
    global data_processor
    
    if not data_processor:
        return jsonify({"error": "No data loaded"}), 400
    
    # Get all incidents for this entity
    entity_groups = data_processor.get_entity_groups()
    incidents = entity_groups.get(entity_name, [])
    
    if not incidents:
        return jsonify({"error": "Entity not found"}), 404
    
    # Sort incidents by start time (newest first)
    sorted_incidents = sorted(incidents, key=lambda x: x.get("startTime", ""), reverse=True)
    
    # Create timeline data
    dates = {}
    for incident in incidents:
        start_time = incident.get("startTime", "")
        if start_time:
            # Extract date part (YYYY-MM-DD)
            date = start_time.split("T")[0]
            dates[date] = dates.get(date, 0) + 1
    
    # Sort dates for timeline chart
    timeline_data = {
        "dates": sorted(dates.keys()),
        "counts": [dates[date] for date in sorted(dates.keys())]
    }
    
    # Get the latest root cause analysis (placeholder) - in a real system, this would come from your RCA records
    # For now, we'll just create a sample based on the most recent incident
    latest_rca = None
    if sorted_incidents:
        latest = sorted_incidents[0]
        # Simulate having RCA data by creating a sample
        entity_type = latest.get("entityType", "unknown")
        service = latest.get("service", "unknown")
        start_time = latest.get("startTime", "")
        if start_time:
            start_time = datetime.fromisoformat(start_time.replace("Z", "+00:00")).strftime("%Y-%m-%d %H:%M:%S")
        
        latest_rca = f"<strong>Root Cause Analysis for incident #{latest.get('incidentNumber')}</strong><br><br>" \
                   f"<strong>Entity:</strong> {entity_name}<br>" \
                   f"<strong>Service:</strong> {service}<br>" \
                   f"<strong>Started at:</strong> {start_time}<br><br>" \
                   f"<strong>Issue Description:</strong> The {entity_type} experienced an alert condition that triggered this incident.<br><br>" \
                   f"<strong>Root Cause:</strong> Based on analysis of similar incidents, this was likely caused by resource constraints or connectivity issues.<br><br>" \
                   f"<strong>Recommended Actions:</strong><br>" \
                   f"1. Monitor resource usage patterns<br>" \
                   f"2. Check for related infrastructure events<br>" \
                   f"3. Consider proactive scaling if pattern continues"
    
    return jsonify({
        "entity_name": entity_name,
        "incidents": sorted_incidents,
        "timeline": timeline_data,
        "latest_rca": latest_rca,
        "incident_count": len(incidents)
    })

def init_app(incident_file=DEFAULT_INCIDENT_FILE):
    """Initialize the application with data."""
    global data_processor
    
    # Load incidents
    data_processor = DashboardDataProcessor(incident_file)
    data_processor.analyze()
    
    return app

if __name__ == '__main__':
    app = init_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
