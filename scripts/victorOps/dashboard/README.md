# VictorOps Incident Dashboard

This dashboard provides a beautiful, interactive visualization of VictorOps incident data, allowing for filtering by routing key and analyzing incidents grouped by entity display name.

## Features

- Interactive charts using Plotly.js
- Filtering by routing key (e.g., "TechOps")
- Clean entity name display (removing markdown hyperlinks)
- Multiple visualizations:
  - Incidents over time
  - Resolution time distribution
  - Incidents by routing key, service, entity type, and paged team
  - Time of day and day of week analysis
  - Top entity groups

## Prerequisites

- Python 3.10+
- Rye package manager

## Setup

1. Install dependencies using rye:

```bash
cd scripts/victorOps/dashboard
rye add flask plotly
```

2. Run the dashboard:

```bash
python app.py
```

3. Open your browser and navigate to: http://localhost:5000

## Usage

1. The dashboard will automatically load data from the default `victorops_incidents.json` file in the parent directory.
2. Use the filtering options to narrow down incident data:
   - Select a specific routing key (e.g., "TechOps")
3. Interactive charts will update to reflect the filtered data.
4. Hover over chart elements to see detailed information.

## Dashboard Sections

1. **Key Metrics**: Average resolution time, response time, top routing key, and entity type
2. **Incidents Over Time**: Timeline of incidents
3. **Resolution Time Distribution**: Histogram of resolution times
4. **Routing Key Analysis**: Distribution of incidents by routing key
5. **Top Services**: Most frequent services with incidents
6. **Entity Analysis**: Breakdown by entity type
7. **Team Analysis**: Incidents by paged team
8. **Time Pattern Analysis**: Incidents by time of day and day of week
9. **Entity Groups**: Top 20 entity groups with incident counts

## Custom Data File

To use a custom incident file:

```bash
python app.py --incident-file=/path/to/your/incidents.json
```
