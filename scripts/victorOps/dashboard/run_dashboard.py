#!/usr/bin/env python3
"""
VictorOps Dashboard Runner

This script provides a simple way to start the VictorOps Incident Dashboard.
"""

import os
import argparse
import logging
from app import init_app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def main():
    """Main function to run the dashboard."""
    parser = argparse.ArgumentParser(description="Run VictorOps Incident Dashboard")
    parser.add_argument("--incident-file", 
                      help="Path to the incident JSON file (default: ../victorops_incidents.json)")
    parser.add_argument("--port", type=int, default=5000,
                      help="Port to run the dashboard on (default: 5000)")
    parser.add_argument("--host", default="0.0.0.0",
                      help="Host to run the dashboard on (default: 0.0.0.0)")
    parser.add_argument("--debug", action="store_true",
                      help="Run in debug mode")
    args = parser.parse_args()
    
    # Default incident file path
    incident_file = args.incident_file
    if not incident_file:
        incident_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                     "victorops_incidents.json")
    
    logger.info(f"Starting dashboard with incident file: {incident_file}")
    app = init_app(incident_file)
    
    logger.info(f"Dashboard running at http://{args.host}:{args.port}")
    app.run(debug=args.debug, host=args.host, port=args.port)

if __name__ == "__main__":
    main()
