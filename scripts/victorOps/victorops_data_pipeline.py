#!/usr/bin/env python3
"""
VictorOps Data Pipeline

This script runs the VictorOps incident collector and analyzer in sequence.
"""

import os
import logging
import argparse
import subprocess
from datetime import datetime
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def check_environment_variables(check_openai=False):
    """
    Check if required environment variables are set.

    Args:
        check_openai: Whether to check for OpenAI environment variables

    Returns:
        Boolean indicating whether all required environment variables are set
    """
    load_dotenv()

    api_id = os.environ.get("VICTOROPS_API_ID")
    api_key = os.environ.get("VICTOROPS_API_KEY")

    if not api_id:
        logger.error("VICTOROPS_API_ID environment variable is not set")
        return False

    if not api_key:
        logger.error("VICTOROPS_API_KEY environment variable is not set")
        return False

    if check_openai:
        openai_api_key = os.environ.get("AZURE_OPENAI_API_KEY")
        openai_endpoint = os.environ.get("AZURE_OPENAI_ENDPOINT")
        openai_deployment = os.environ.get("AZURE_OPENAI_DEPLOYMENT")

        if not openai_api_key:
            logger.error("AZURE_OPENAI_API_KEY environment variable is not set")
            return False

        if not openai_endpoint:
            logger.error("AZURE_OPENAI_ENDPOINT environment variable is not set")
            return False

        if not openai_deployment:
            logger.error("AZURE_OPENAI_DEPLOYMENT environment variable is not set")
            return False

    return True

def run_collector(output_file, routing_key=None, current_phase=None, days=30):
    """Run the incident collector script."""
    script_path = os.path.join(os.path.dirname(__file__), "victorops_incidents_collector.py")
    cmd = ["python", script_path, "--output", output_file, "--days", str(days)]

    if routing_key:
        cmd.extend(["--routing-key", routing_key])

    if current_phase:
        cmd.extend(["--current-phase", current_phase])

    logger.info(f"Running collector: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Collector failed: {str(e)}")
        logger.error(f"Error output: {e.stderr}")
        return False

def run_enricher(input_file=None, output_file=None, routing_key=None, current_phase=None, days=30):
    """Run the incident enricher script."""
    script_path = os.path.join(os.path.dirname(__file__), "victorops_incident_enricher.py")
    cmd = ["python", script_path]

    if input_file:
        cmd.extend(["--input", input_file])

    if output_file:
        cmd.extend(["--output", output_file])

    if routing_key:
        cmd.extend(["--routing-key", routing_key])

    if current_phase:
        cmd.extend(["--current-phase", current_phase])

    cmd.extend(["--days", str(days)])

    logger.info(f"Running enricher: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Enricher failed: {str(e)}")
        logger.error(f"Error output: {e.stderr}")
        return False


def run_analyzer(input_file, output_file=None):
    """Run the incident analyzer script."""
    script_path = os.path.join(os.path.dirname(__file__), "victorops_incident_analyzer.py")
    cmd = ["python", script_path, "--input", input_file]

    if output_file:
        cmd.extend(["--output", output_file])

    logger.info(f"Running analyzer: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)  # Print analyzer results to console
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Analyzer failed: {str(e)}")
        logger.error(f"Error output: {e.stderr}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Run VictorOps incident collection, enrichment, and analysis pipeline"
    )
    parser.add_argument("--incident-file", default=None,
                      help="Output file for incidents (default: victorops_incidents_YYYY-MM-DD.json)")
    parser.add_argument("--enriched-file", default=None,
                      help="Output file for enriched incidents (default: victorops_incidents_enriched_YYYY-MM-DD.json)")
    parser.add_argument("--analysis-file", default=None,
                      help="Output file for analysis (default: victorops_analysis_YYYY-MM-DD.json)")
    parser.add_argument("--routing-key", help="Filter by routing key")
    parser.add_argument("--current-phase", help="Filter by current phase (e.g., 'resolved', 'triggered')")
    parser.add_argument("--days", type=int, default=30, help="Number of days to look back")
    parser.add_argument("--skip-collect", action="store_true", help="Skip collection and use existing incident file")
    parser.add_argument("--skip-enrich", action="store_true", help="Skip enrichment step")
    parser.add_argument("--skip-analyze", action="store_true", help="Skip analysis step")
    args = parser.parse_args()

    # Generate default filenames with date stamp if not provided
    date_str = datetime.now().strftime("%Y-%m-%d")

    incident_file = args.incident_file or f"victorops_incidents_{date_str}.json"
    enriched_file = args.enriched_file or f"victorops_incidents_enriched_{date_str}.json"
    analysis_file = args.analysis_file or f"victorops_analysis_{date_str}.json"

    # Step 1: Collect incidents
    if not args.skip_collect:
        # Check environment variables for collection
        if not check_environment_variables():
            return

        logger.info("Starting incident collection...")
        if not run_collector(
            output_file=incident_file,
            routing_key=args.routing_key,
            current_phase=args.current_phase,
            days=args.days
        ):
            logger.error("Data collection failed. Exiting.")
            return
    else:
        logger.info(f"Skipping collection, using existing file: {incident_file}")

    # Step 2: Enrich incidents with product and cluster information
    input_for_analysis = incident_file

    if not args.skip_enrich:
        # Check environment variables for enrichment (including OpenAI)
        if not check_environment_variables(check_openai=True):
            logger.error("Required environment variables for enrichment are missing. Skipping enrichment.")
        else:
            logger.info("Starting incident enrichment...")
            if not run_enricher(
                input_file=incident_file,
                output_file=enriched_file,
                routing_key=args.routing_key,
                current_phase=args.current_phase,
                days=args.days
            ):
                logger.error("Data enrichment failed. Continuing with original incidents for analysis.")
            else:
                # Use enriched file for analysis
                input_for_analysis = enriched_file
                logger.info(f"Enriched incident data saved to: {enriched_file}")
    else:
        logger.info("Skipping enrichment step.")

    # Step 3: Analyze incidents
    if not args.skip_analyze:
        logger.info("Starting incident analysis...")
        if not run_analyzer(input_file=input_for_analysis, output_file=analysis_file):
            logger.error("Data analysis failed. Exiting.")
            return
        logger.info(f"Analysis data saved to: {analysis_file}")
    else:
        logger.info("Skipping analysis step.")

    logger.info("Data pipeline completed successfully.")

if __name__ == "__main__":
    main()