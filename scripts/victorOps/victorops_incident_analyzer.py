#!/usr/bin/env python3
"""
VictorOps Incident Analyzer

This script provides utility functions to analyze VictorOps incident data.
"""

import json
import logging
import argparse
import re
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import Counter, defaultdict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def load_incidents_from_file(filename: str) -> List[dict]:
    """
    Load incidents from a JSON file.
    
    Args:
        filename: Path to the JSON file containing incidents
        
    Returns:
        List of incident dictionaries
    """
    try:
        with open(filename, 'r') as f:
            data = json.load(f)
            return data.get("incidents", [])
    except FileNotFoundError:
        logger.error(f"File not found: {filename}")
        return []
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in file: {filename}")
        return []

def count_incidents_by_routing_key(incidents: List[dict]) -> Dict[str, int]:
    """
    Count incidents by routing key.
    
    Args:
        incidents: List of incident dictionaries
        
    Returns:
        Dictionary mapping routing keys to incident counts
    """
    routing_keys = [incident.get("routingKey", "unknown") for incident in incidents]
    return dict(Counter(routing_keys))

def count_incidents_by_service(incidents: List[dict]) -> Dict[str, int]:
    """
    Count incidents by service.
    
    Args:
        incidents: List of incident dictionaries
        
    Returns:
        Dictionary mapping services to incident counts
    """
    services = [incident.get("service", "unknown") for incident in incidents]
    return dict(Counter(services))

def count_incidents_by_entity_type(incidents: List[dict]) -> Dict[str, int]:
    """
    Count incidents by entity type.
    
    Args:
        incidents: List of incident dictionaries
        
    Returns:
        Dictionary mapping entity types to incident counts
    """
    entity_types = [incident.get("entityType", "unknown") for incident in incidents]
    return dict(Counter(entity_types))

def count_incidents_by_paged_team(incidents: List[dict]) -> Dict[str, int]:
    """
    Count incidents by paged team.
    
    Args:
        incidents: List of incident dictionaries
        
    Returns:
        Dictionary mapping paged teams to incident counts
    """
    team_counts = defaultdict(int)
    
    for incident in incidents:
        paged_teams = incident.get("pagedTeams", [])
        for team in paged_teams:
            team_counts[team] += 1
    
    return dict(team_counts)

def count_incidents_by_paged_user(incidents: List[dict]) -> Dict[str, int]:
    """
    Count incidents by paged user.
    
    Args:
        incidents: List of incident dictionaries
        
    Returns:
        Dictionary mapping paged users to incident counts
    """
    user_counts = defaultdict(int)
    
    for incident in incidents:
        paged_users = incident.get("pagedUsers", [])
        for user in paged_users:
            user_counts[user] += 1
    
    return dict(user_counts)

def calculate_resolution_times(incidents: List[dict]) -> Dict[str, float]:
    """
    Calculate resolution time statistics (in minutes).
    
    Args:
        incidents: List of incident dictionaries
        
    Returns:
        Dictionary with resolution time statistics
    """
    resolution_times = []
    
    for incident in incidents:
        transitions = incident.get("transitions", [])
        triggered_time = None
        resolved_time = None
        
        for transition in transitions:
            if transition.get("name") == "triggered":
                triggered_time = datetime.strptime(
                    transition.get("at", ""), "%Y-%m-%dT%H:%M:%SZ"
                )
            elif transition.get("name") == "resolved":
                resolved_time = datetime.strptime(
                    transition.get("at", ""), "%Y-%m-%dT%H:%M:%SZ"
                )
        
        if triggered_time and resolved_time:
            resolution_time = (resolved_time - triggered_time).total_seconds() / 60
            resolution_times.append(resolution_time)
    
    if not resolution_times:
        return {"count": 0}
    
    return {
        "count": len(resolution_times),
        "min": min(resolution_times),
        "max": max(resolution_times),
        "avg": sum(resolution_times) / len(resolution_times),
        "median": sorted(resolution_times)[len(resolution_times) // 2]
    }

def calculate_response_times(incidents: List[dict]) -> Dict[str, float]:
    """
    Calculate response time statistics (time from triggered to acknowledged, in minutes).
    
    Args:
        incidents: List of incident dictionaries
        
    Returns:
        Dictionary with response time statistics
    """
    response_times = []
    
    for incident in incidents:
        transitions = incident.get("transitions", [])
        triggered_time = None
        ack_time = None
        
        for transition in transitions:
            if transition.get("name") == "triggered":
                triggered_time = datetime.strptime(
                    transition.get("at", ""), "%Y-%m-%dT%H:%M:%SZ"
                )
            elif transition.get("name") == "acknowledged":
                ack_time = datetime.strptime(
                    transition.get("at", ""), "%Y-%m-%dT%H:%M:%SZ"
                )
                break  # Take the first acknowledgment
        
        if triggered_time and ack_time:
            response_time = (ack_time - triggered_time).total_seconds() / 60
            response_times.append(response_time)
    
    if not response_times:
        return {"count": 0}
    
    return {
        "count": len(response_times),
        "min": min(response_times),
        "max": max(response_times),
        "avg": sum(response_times) / len(response_times),
        "median": sorted(response_times)[len(response_times) // 2]
    }

def count_incidents_by_time_of_day(incidents: List[dict]) -> Dict[str, int]:
    """
    Count incidents by time of day (hour).
    
    Args:
        incidents: List of incident dictionaries
        
    Returns:
        Dictionary mapping hour to incident counts
    """
    hour_counts = defaultdict(int)
    
    for incident in incidents:
        start_time = incident.get("startTime")
        if start_time:
            dt = datetime.strptime(start_time, "%Y-%m-%dT%H:%M:%SZ")
            hour = dt.hour
            hour_counts[hour] += 1
    
    return dict(sorted(hour_counts.items()))

def count_incidents_by_day_of_week(incidents: List[dict]) -> Dict[str, int]:
    """
    Count incidents by day of week.
    
    Args:
        incidents: List of incident dictionaries
        
    Returns:
        Dictionary mapping day of week to incident counts
    """
    day_counts = defaultdict(int)
    days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
    
    for incident in incidents:
        start_time = incident.get("startTime")
        if start_time:
            dt = datetime.strptime(start_time, "%Y-%m-%dT%H:%M:%SZ")
            day_of_week = days[dt.weekday()]
            day_counts[day_of_week] += 1
    
    return {day: day_counts.get(day, 0) for day in days}

def extract_clean_entity_name(entity_display_name: str) -> str:
    """
    Extract clean entity name by removing markdown hyperlinks.
    
    Args:
        entity_display_name: The entity display name with potential markdown hyperlinks
        
    Returns:
        Clean entity display name without markdown hyperlinks
    """
    # Pattern to match markdown hyperlinks: - [NewRelicIncidentURL](URL)
    pattern = r'\s+-\s+\[NewRelicIncidentURL\]\(.*?\)'
    clean_name = re.sub(pattern, '', entity_display_name)
    return clean_name

def group_incidents_by_entity_name(incidents: List[dict]) -> Dict[str, List[dict]]:
    """
    Group incidents by cleaned entity display name.
    
    Args:
        incidents: List of incident dictionaries
        
    Returns:
        Dictionary mapping cleaned entity names to lists of incidents
    """
    grouped_incidents = defaultdict(list)
    
    for incident in incidents:
        entity_display_name = incident.get("entityDisplayName", "unknown")
        clean_name = extract_clean_entity_name(entity_display_name)
        grouped_incidents[clean_name].append(incident)
    
    return dict(grouped_incidents)

def filter_incidents_by_routing_key(incidents: List[dict], routing_key: str) -> List[dict]:
    """
    Filter incidents by routing key.
    
    Args:
        incidents: List of incident dictionaries
        routing_key: Routing key to filter by
        
    Returns:
        Filtered list of incidents
    """
    return [inc for inc in incidents if inc.get("routingKey") == routing_key]

def analyze_incidents(incidents: List[dict]) -> Dict[str, Any]:
    """
    Perform comprehensive analysis on incidents.
    
    Args:
        incidents: List of incident dictionaries
        
    Returns:
        Dictionary with analysis results
    """
    if not incidents:
        logger.error("No incidents to analyze")
        return {"error": "No incidents to analyze"}
    
    total_incidents = len(incidents)
    
    return {
        "total_incidents": total_incidents,
        "by_routing_key": count_incidents_by_routing_key(incidents),
        "by_service": count_incidents_by_service(incidents),
        "by_entity_type": count_incidents_by_entity_type(incidents),
        "by_paged_team": count_incidents_by_paged_team(incidents),
        "by_paged_user": count_incidents_by_paged_user(incidents),
        "resolution_times": calculate_resolution_times(incidents),
        "response_times": calculate_response_times(incidents),
        "by_time_of_day": count_incidents_by_time_of_day(incidents),
        "by_day_of_week": count_incidents_by_day_of_week(incidents),
        "by_entity_name": group_incidents_by_entity_name(incidents)
    }

def print_analysis(analysis: Dict[str, Any]) -> None:
    """
    Print analysis results in a readable format.
    
    Args:
        analysis: Dictionary with analysis results
    """
    if "error" in analysis:
        logger.error(analysis["error"])
        return
    
    print("\n==== VictorOps Incident Analysis ====\n")
    
    print(f"Total Incidents: {analysis['total_incidents']}")
    
    print("\n--- Incidents by Routing Key ---")
    for key, count in sorted(analysis['by_routing_key'].items(), key=lambda x: x[1], reverse=True):
        print(f"{key}: {count}")
    
    print("\n--- Top 10 Services ---")
    for service, count in sorted(analysis['by_service'].items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"{service}: {count}")
        
    print("\n--- Top 20 Entity Names ---")
    entity_groups = analysis.get('by_entity_name', {})
    for entity_name, incidents in sorted(entity_groups.items(), key=lambda x: len(x[1]), reverse=True)[:20]:
        print(f"{entity_name}: {len(incidents)} incidents")
    
    print("\n--- Incidents by Entity Type ---")
    for entity_type, count in sorted(analysis['by_entity_type'].items(), key=lambda x: x[1], reverse=True):
        print(f"{entity_type}: {count}")
    
    print("\n--- Incidents by Paged Team ---")
    for team, count in sorted(analysis['by_paged_team'].items(), key=lambda x: x[1], reverse=True):
        print(f"{team}: {count}")
    
    print("\n--- Incidents by Paged User ---")
    for user, count in sorted(analysis['by_paged_user'].items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"{user}: {count}")
    
    print("\n--- Resolution Times (minutes) ---")
    rt = analysis['resolution_times']
    if rt['count'] > 0:
        print(f"Count: {rt['count']}")
        print(f"Min: {rt['min']:.2f}")
        print(f"Max: {rt['max']:.2f}")
        print(f"Avg: {rt['avg']:.2f}")
        print(f"Median: {rt['median']:.2f}")
    else:
        print("No resolution time data available")
    
    print("\n--- Response Times (minutes) ---")
    rt = analysis['response_times']
    if rt['count'] > 0:
        print(f"Count: {rt['count']}")
        print(f"Min: {rt['min']:.2f}")
        print(f"Max: {rt['max']:.2f}")
        print(f"Avg: {rt['avg']:.2f}")
        print(f"Median: {rt['median']:.2f}")
    else:
        print("No response time data available")
    
    print("\n--- Incidents by Time of Day ---")
    for hour, count in analysis['by_time_of_day'].items():
        print(f"{hour:02d}:00 - {hour:02d}:59: {count}")
    
    print("\n--- Incidents by Day of Week ---")
    for day, count in analysis['by_day_of_week'].items():
        print(f"{day}: {count}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Analyze VictorOps incidents")
    parser.add_argument("--input", "-i", default="victorops_incidents.json", 
                        help="Input file name (default: victorops_incidents.json)")
    parser.add_argument("--output", "-o", 
                        help="Output file for analysis results in JSON format (optional)")
    parser.add_argument("--routing-key", help="Filter incidents by routing key (e.g., 'TechOps')")
    parser.add_argument("--entity-groups", action="store_true", 
                        help="Show detailed incident grouping by entity display name")
    args = parser.parse_args()
    
    incidents = load_incidents_from_file(args.input)
    
    if not incidents:
        logger.error(f"No incidents found in {args.input}")
        return
        
    # Filter by routing key if specified
    if args.routing_key:
        filtered_incidents = filter_incidents_by_routing_key(incidents, args.routing_key)
        logger.info(f"Filtered to {len(filtered_incidents)} incidents with routing key '{args.routing_key}'")
        incidents = filtered_incidents
        
    if not incidents:
        logger.error("No incidents found after filtering")
        return
    
    logger.info(f"Analyzing {len(incidents)} incidents")
    
    analysis = analyze_incidents(incidents)
    
    print_analysis(analysis)
    
    # If entity groups flag is set, print detailed entity grouping
    if args.entity_groups:
        print("\n===== DETAILED ENTITY GROUPING =====")
        entity_groups = analysis.get('by_entity_name', {})
        for entity_name, group_incidents in sorted(entity_groups.items(), key=lambda x: len(x[1]), reverse=True):
            print(f"\n--- {entity_name} ({len(group_incidents)} incidents) ---")
            for inc in group_incidents:
                print(f"  Incident #{inc.get('incidentNumber')} - {inc.get('currentPhase')} - {inc.get('startTime')}")
    
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(analysis, f, indent=2)
        logger.info(f"Analysis saved to {args.output}")

if __name__ == "__main__":
    main() 