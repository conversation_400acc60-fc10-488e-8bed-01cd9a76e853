#!/usr/bin/env python3
"""
VictorOps Incident Collector

This script collects incident reports from VictorOps for the last 30 days.
It handles pagination, rate limiting, and stores the results in a JSON file.
"""

import os
import json
import time
import logging
import requests
import re
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple
from dotenv import load_dotenv
import argparse
import openai

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class VictorOpsClient:
    """
    Client for interacting with the VictorOps API.
    """
    BASE_URL = "https://api.victorops.com"
    
    def __init__(self, api_id: str, api_key: str):
        """
        Initialize the VictorOps client with API credentials.
        
        Args:
            api_id: VictorOps API ID
            api_key: VictorOps API Key
        """
        self.api_id = api_id
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            "Accept": "application/json",
            "X-VO-Api-Id": api_id,
            "X-VO-Api-Key": api_key
        })
        self.last_request_time = 0
    
    def _enforce_rate_limit(self) -> None:
        """
        Enforces the rate limit of one request per 10 seconds.
        """
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        # VictorOps API allows max one request per minute
        if time_since_last_request < 10:
            wait_time = 10 - time_since_last_request
            logger.info(f"Rate limiting: Waiting {wait_time:.2f} seconds before next request")
            time.sleep(wait_time)
        
        self.last_request_time = time.time()
    
    def get_incidents(
        self,
        started_after: str,
        routing_key: Optional[str] = None,
        current_phase: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> dict:
        """
        Get incidents from VictorOps.
        
        Args:
            started_after: ISO8601 timestamp to filter incidents that started after this time
            routing_key: Optional filter by routing key
            current_phase: Optional filter by current phase (e.g., "resolved", "triggered")
            limit: Number of incidents to return (max 100)
            offset: Offset for pagination
            
        Returns:
            Dictionary containing incident data
        """
        self._enforce_rate_limit()
        
        endpoint = f"{self.BASE_URL}/api-reporting/v2/incidents"
        params = {
            "limit": min(limit, 100),  # Ensure limit is not more than 100
            "offset": offset,
            "startedAfter": started_after
        }
        
        if routing_key:
            params["routingKey"] = routing_key
        
        if current_phase:
            params["currentPhase"] = current_phase
        
        logger.info(f"Fetching incidents with params: {params}")
        
        response = self.session.get(endpoint, params=params)
        
        if response.status_code != 200:
            logger.error(f"Error fetching incidents: {response.status_code} - {response.text}")
            response.raise_for_status()
        
        return response.json()
    
    def get_all_incidents(
        self,
        started_after: str,
        routing_key: Optional[str] = None,
        current_phase: Optional[str] = None
    ) -> List[dict]:
        """
        Get all incidents with pagination.
        
        Args:
            started_after: ISO8601 timestamp to filter incidents that started after this time
            routing_key: Optional filter by routing key
            current_phase: Optional filter by current phase
            
        Returns:
            List of all incidents
        """
        all_incidents = []
        offset = 0
        limit = 100
        total = None
        
        while total is None or offset < total:
            response = self.get_incidents(
                started_after=started_after,
                routing_key=routing_key,
                current_phase=current_phase,
                limit=limit,
                offset=offset
            )
            
            total = response.get("total", 0)
            incidents = response.get("incidents", [])
            all_incidents.extend(incidents)
            
            logger.info(f"Fetched {len(incidents)} incidents. Total: {total}, Offset: {offset}")
            
            if len(incidents) == 0 or len(incidents) < limit:
                break
                
            offset += limit
        
        return all_incidents
        
    def get_incident_details(self, incident_number: str) -> Dict[str, Any]:
        """
        Get detailed information for a specific incident.
        
        Args:
            incident_number: The VictorOps incident number
            
        Returns:
            Dictionary containing detailed incident information
        """
        self._enforce_rate_limit()
        
        endpoint = f"{self.BASE_URL}/api-public/v1/incidents/{incident_number}"
        
        logger.info(f"Fetching details for incident: {incident_number}")
        
        response = self.session.get(endpoint)
        
        if response.status_code != 200:
            logger.error(f"Error fetching incident details: {response.status_code} - {response.text}")
            response.raise_for_status()
        
        return response.json()

def get_thirty_days_ago_iso8601() -> str:
    """
    Get ISO8601 formatted timestamp for 30 days ago.
    
    Returns:
        ISO8601 formatted timestamp
    """
    thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
    return thirty_days_ago.strftime("%Y-%m-%dT%H:%M:%SZ")

def save_incidents_to_file(incidents: List[dict], filename: str) -> None:
    """
    Save incidents to a JSON file.
    
    Args:
        incidents: List of incident dictionaries
        filename: Output filename
    """
    with open(filename, 'w') as f:
        json.dump({"incidents": incidents, "count": len(incidents)}, f, indent=2)
    
    logger.info(f"Saved {len(incidents)} incidents to {filename}")


class AzureOpenAIClient:
    """
    Client for interacting with Azure OpenAI API.
    """
    
    def __init__(self, api_key: str, endpoint: str, deployment_name: str):
        """
        Initialize the Azure OpenAI client.
        
        Args:
            api_key: Azure OpenAI API key
            endpoint: Azure OpenAI endpoint URL
            deployment_name: Azure OpenAI deployment name
        """
        self.api_key = api_key
        self.endpoint = endpoint
        self.deployment_name = deployment_name
        
        # Configure the OpenAI client for Azure (new API style for 1.0.0+)
        self.client = openai.AzureOpenAI(
            api_key=api_key,
            api_version="2023-05-15",
            azure_endpoint=endpoint
        )
    
    def determine_product_name(self, incident_details: Dict[str, Any]) -> Tuple[str, float]:
        """
        Determine the product name using Azure OpenAI and pattern matching.
        
        Args:
            incident_details: Dictionary containing incident details
            
        Returns:
            Tuple containing (product_name, confidence_score)
        """
        # First, try to determine product name using pattern matching
        product_name, confidence = self._determine_product_by_patterns(incident_details)
        
        # If pattern matching didn't yield a high confidence result, use LLM
        if confidence < 0.8:
            return self._determine_product_by_llm(incident_details)
        
        return product_name, confidence
    
    def _determine_product_by_patterns(self, incident_details: Dict[str, Any]) -> Tuple[str, float]:
        """
        Determine product name using pattern matching rules.
        
        Args:
            incident_details: Dictionary containing incident details
            
        Returns:
            Tuple containing (product_name, confidence_score)
        """
        # Convert the incident details to a string for pattern matching
        incident_str = json.dumps(incident_details).lower()
        
        # Define patterns for each product
        mdm_patterns = ["na2", "na1", "ap1", "ap2", "eu1", "mdm", "nmdm"]
        neurons_patterns = ["nvu", "mlu", "uku", "fru", "ttu", "tku", "neurons"]
        pulse_patterns = ["pulse", "pulsesecure", "zdt"]
        incapptic_patterns = ["incapptic"]
        
        # Check for pattern matches and count occurrences
        mdm_matches = sum(1 for pattern in mdm_patterns if pattern in incident_str)
        neurons_matches = sum(1 for pattern in neurons_patterns if pattern in incident_str)
        pulse_matches = sum(1 for pattern in pulse_patterns if pattern in incident_str)
        incapptic_matches = sum(1 for pattern in incapptic_patterns if pattern in incident_str)
        
        # Determine the product with the most matches
        pattern_matches = [
            ("MDM", mdm_matches, len(mdm_patterns)),
            ("Neurons", neurons_matches, len(neurons_patterns)),
            ("Pulse", pulse_matches, len(pulse_patterns)),
            ("Incapptic", incapptic_matches, len(incapptic_patterns))
        ]
        
        # Sort by number of matches in descending order
        pattern_matches.sort(key=lambda x: x[1], reverse=True)
        
        # Calculate confidence based on the ratio of matches to total patterns for the product
        top_product, matches, total_patterns = pattern_matches[0]
        
        # If no matches, return Unknown with 0 confidence
        if matches == 0:
            return "Unknown", 0.0
        
        # Calculate confidence score (matches / total_patterns)
        confidence = matches / total_patterns
        
        return top_product, confidence
    
    def _determine_product_by_llm(self, incident_details: Dict[str, Any]) -> Tuple[str, float]:
        """
        Determine product name using Azure OpenAI LLM.
        
        Args:
            incident_details: Dictionary containing incident details
            
        Returns:
            Tuple containing (product_name, confidence_score)
        """
        # Prepare prompt for the LLM
        system_prompt = (
            "You are an AI assistant that analyzes incident reports and identifies the relevant product. "
            "Based on the incident details provided, determine the product name. "
            "The possible products are: MDM, Neurons, Pulse, and Incapptic. "
            "MDM indicators: na2, na1, ap1, ap2, eu1, mdm, nmdm. "
            "Neurons indicators: nvu, mlu, uku, fru, ttu, tku, neurons. "
            "Pulse indicators: pulse, pulsesecure, zdt. "
            "Incapptic indicators: incapptic. "
            "Provide your answer in the format: {{'product': '<product_name>', 'confidence': <confidence_score>}} "
            "where confidence is a float between 0 and 1."
        )
        
        user_prompt = f"Analyze the following incident report and determine the product: {json.dumps(incident_details)}"
        
        try:
            # Make API call to Azure OpenAI (new API style for 1.0.0+)
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3,
                max_tokens=100
            )
            
            # Extract result from response (new API style for 1.0.0+)
            result_text = response.choices[0].message.content.strip()
            
            try:
                # Try to parse the result as JSON
                result = json.loads(result_text)
                product = result.get("product", "Unknown")
                confidence = float(result.get("confidence", 0.5))
                return product, confidence
            except (json.JSONDecodeError, ValueError):
                # If parsing fails, extract product name using regex
                product_match = re.search(r"'product'\s*:\s*'([^']+)'|\"product\"\s*:\s*\"([^\"]+)\"", result_text)
                confidence_match = re.search(r"'confidence'\s*:\s*(\d+\.\d+)|\"confidence\"\s*:\s*(\d+\.\d+)", result_text)
                
                product = "Unknown"
                confidence = 0.5
                
                if product_match:
                    # Extract the product from either group 1 or group 2, whichever is not None
                    if product_match.group(1):
                        product = product_match.group(1)
                    elif product_match.group(2):
                        product = product_match.group(2)
                
                if confidence_match:
                    # Extract the confidence from either group 1 or group 2, whichever is not None
                    if confidence_match.group(1):
                        confidence = float(confidence_match.group(1))
                    elif confidence_match.group(2):
                        confidence = float(confidence_match.group(2))
                
                return product, confidence
                
        except Exception as e:
            logger.error(f"Error calling Azure OpenAI: {str(e)}")
            return "Unknown", 0.0

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Collect VictorOps incidents and analyze details")
    parser.add_argument("--output", "-o", default="victorops_incidents.json", 
                        help="Output file name (default: victorops_incidents.json)")
    parser.add_argument("--routing-key", "-r", help="Filter by routing key")
    parser.add_argument("--current-phase", "-p", help="Filter by current phase (e.g., 'resolved', 'triggered')")
    parser.add_argument("--days", "-d", type=int, default=30, 
                        help="Number of days to look back (default: 30)")
    parser.add_argument("--enrich", "-e", action="store_true",
                        help="Enrich incidents with detailed information and product identification")
    parser.add_argument("--input", "-i", help="Input file with incidents to enrich (skips collection step)")
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv()
    
    # Generate output filename with date if using default name
    today = datetime.now().strftime("%Y-%m-%d")
    output_file = f"victorops_incidents_{today}.json" if args.output == "victorops_incidents.json" else args.output
    
    incidents = []
    victorops_client = None
    
    # Step 1: Get incidents - either from file or by collecting from API
    if args.input:
        # Load incidents from input file
        try:
            with open(args.input, 'r') as f:
                data = json.load(f)
                incidents = data.get("incidents", [])
                logger.info(f"Loaded {len(incidents)} incidents from {args.input}")
        except Exception as e:
            logger.error(f"Error loading incidents from {args.input}: {str(e)}")
            exit(1)
    else:
        # Collect incidents from VictorOps API
        victorops_api_id = os.environ.get("VICTOROPS_API_ID")
        victorops_api_key = os.environ.get("VICTOROPS_API_KEY")
        
        if not victorops_api_id or not victorops_api_key:
            logger.error("VICTOROPS_API_ID and VICTOROPS_API_KEY environment variables must be set")
            exit(1)
        
        # Calculate the date range
        days_ago = datetime.now(timezone.utc) - timedelta(days=args.days)
        started_after = days_ago.strftime("%Y-%m-%dT%H:%M:%SZ")
        
        # Initialize VictorOps client
        logger.info(f"Collecting incidents from the last {args.days} days (since {started_after})")
        victorops_client = VictorOpsClient(api_id=victorops_api_id, api_key=victorops_api_key)
        
        # Get incidents
        incidents = victorops_client.get_all_incidents(
            started_after=started_after,
            routing_key=args.routing_key,
            current_phase=args.current_phase
        )
        logger.info(f"Collected {len(incidents)} incidents from VictorOps API")
    
    # Step 2: Enrich incidents with detailed information and product identification if requested
    if args.enrich:
        logger.info("Enriching incidents with detailed information and product identification")
        
        # Check for Azure OpenAI credentials
        openai_api_key = os.environ.get("AZURE_OPENAI_API_KEY")
        openai_endpoint = os.environ.get("AZURE_OPENAI_ENDPOINT")
        openai_deployment = os.environ.get("AZURE_OPENAI_DEPLOYMENT")
        
        if not openai_api_key or not openai_endpoint or not openai_deployment:
            logger.error("AZURE_OPENAI_API_KEY, AZURE_OPENAI_ENDPOINT, and AZURE_OPENAI_DEPLOYMENT environment variables must be set for enrichment")
            exit(1)
        
        # Initialize OpenAI client
        openai_client = AzureOpenAIClient(
            api_key=openai_api_key,
            endpoint=openai_endpoint,
            deployment_name=openai_deployment
        )
        
        # Initialize VictorOps client if not already initialized
        if victorops_client is None:
            victorops_api_id = os.environ.get("VICTOROPS_API_ID")
            victorops_api_key = os.environ.get("VICTOROPS_API_KEY")
            
            if not victorops_api_id or not victorops_api_key:
                logger.error("VICTOROPS_API_ID and VICTOROPS_API_KEY environment variables must be set for enrichment")
                exit(1)
                
            victorops_client = VictorOpsClient(api_id=victorops_api_id, api_key=victorops_api_key)
        
        # Enrich each incident with detailed information and product identification
        enriched_incidents = []
        total_incidents = len(incidents)
        
        for i, incident in enumerate(incidents):
            incident_number = incident.get("incidentNumber")
            logger.info(f"Processing incident {i+1}/{total_incidents}: {incident_number}")
            
            if not incident_number:
                logger.warning(f"Incident missing incident number: {incident}")
                enriched_incidents.append(incident)
                continue
            
            try:
                # Get detailed information for the incident
                incident_details = victorops_client.get_incident_details(incident_number)
                
                # Determine product name using pattern matching and/or Azure OpenAI
                product_name, confidence = openai_client.determine_product_name(incident_details)
                
                # Add product information to the incident
                incident_details["product"] = product_name
                incident_details["product_confidence"] = confidence
                
                logger.info(f"Incident {incident_number} identified as {product_name} with {confidence:.2f} confidence")
                
                # Add the enriched incident to the list
                enriched_incidents.append(incident_details)
                
            except Exception as e:
                logger.error(f"Error enriching incident {incident_number}: {str(e)}")
                # Add the original incident to the list
                enriched_incidents.append(incident)
        
        # Replace the original incidents with enriched ones
        incidents = enriched_incidents
        logger.info(f"Enriched {len(incidents)} incidents with detailed information and product identification")
    
    # Step 3: Save incidents to file
    save_incidents_to_file(incidents, output_file)
    
    logger.info(f"Done! Saved {len(incidents)} incidents to {output_file}.")

if __name__ == "__main__":
    main() 