import logging
import azure.functions as func
from azure.servicebus import ServiceBusClient, ServiceBusMessage

CONNECTION_STR = "your_service_bus_connection_string"
QUEUE_NAME = "your_queue_name"

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request.')
    
    try:
        # Parse JSON payload from request
        req_body = req.get_json()
    except ValueError:
        return func.HttpResponse("Invalid JSON received", status_code=400)
    
    # Connect to Service Bus and send message
    try:
        servicebus_client = ServiceBusClient.from_connection_string(conn_str=CONNECTION_STR, logging_enable=True)
        with servicebus_client:
            sender = servicebus_client.get_queue_sender(queue_name=QUEUE_NAME)
            with sender:
                message = ServiceBusMessage(str(req_body))
                sender.send_messages(message)
                logging.info("Message sent to Service Bus queue.")
                
        return func.HttpResponse("Message successfully sent to the queue.", status_code=200)
    except Exception as e:
        logging.error(f"Failed to send message: {str(e)}")
        return func.HttpResponse("Failed to send message to the queue.", status_code=500)
