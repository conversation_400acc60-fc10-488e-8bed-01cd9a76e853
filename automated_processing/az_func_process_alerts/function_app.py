import azure.functions as func
import datetime
import json
import logging
from azure.servicebus import ServiceBusClient, ServiceBusMessage

SERVICE_BUS_CONNECTION_STR = "Endpoint=sb://sb-obv-newrelic-v1.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=6PmrTFXIAPJi9bOxADj4Y9gTnRM9IsPcT+ASbO1+JZ4="
SERVICE_BUS_QUEUE_NAME = "nr-alert"

app = func.FunctionApp()

@app.route(route="automated_processing", auth_level=func.AuthLevel.ANONYMOUS)
def automated_processing(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request.')

    try:
        req_body = req.get_json()
    except ValueError:
        return func.HttpResponse(
            "Invalid JSON payload.",
            status_code=400
        )

    try:
        message = json.dumps(req_body)
        send_to_service_bus(message)
        return func.HttpResponse(
            "Message sent to Service Bus queue successfully.",
            status_code=200
        )
    except Exception as e:
        logging.error(f"Error sending message to Service Bus queue: {e}")
        return func.HttpResponse(
            "Error sending message to Service Bus queue.",
            status_code=500
        )

def send_to_service_bus(message):
    servicebus_client = ServiceBusClient.from_connection_string(conn_str=SERVICE_BUS_CONNECTION_STR, logging_enable=True)
    with servicebus_client:
        sender = servicebus_client.get_queue_sender(queue_name=SERVICE_BUS_QUEUE_NAME)
        with sender:
            servicebus_message = ServiceBusMessage(message)
            sender.send_messages(servicebus_message)