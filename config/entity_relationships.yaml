# Unified Entity Relationships Configuration
# This single configuration file defines all entity relationships and alert mappings
# Enhanced with graph-based cascading failure analysis support

# Graph Analysis Configuration
graph_analysis:
  default_criticality_score: 0.5
  default_failure_probability: 0.1
  default_propagation_probability: 0.5
  critical_path_threshold: 0.7
  failure_threshold: 0.1
  propagation_decay: 0.8
  max_hops: 5
  max_graph_depth: 3
  cache_timeout_seconds: 300

# Architectural Relationships - Business Logic Dependencies Not Visible in Telemetry
# These relationships represent intended architecture that may not be observable through monitoring
architectural_relationships:
  
  # Mobile Identity (MI) Architecture Business Dependencies
  mi_production_architecture:
    description: "Mobile Identity microservices architecture with business logic dependencies"
    
    services:
      # Core MI Services
      connector-service:
        entity_type: "APPLICATION"
        entity_guid_pattern: "mi-connector-service-*"
        entity_name_pattern: "connector-service"
        business_dependencies:
          - target_service: identity-service
            relationship_type: "DEPENDS_ON"
            criticality: "critical"
            failure_propagation_probability: 0.85
            typical_response_time_ms: 200
            failure_indicators:
              error_rate: "> 0.05"
              response_time: "> 500ms" 
              availability: "< 0.95"
            metrics_to_validate: ["error_rate", "response_time", "availability"]
            
          - target_service: auth-service
            relationship_type: "DEPENDS_ON" 
            criticality: "critical"
            failure_propagation_probability: 0.80
            typical_response_time_ms: 150
            failure_indicators:
              auth_failures: "> 0.10"
              response_time: "> 400ms"
            metrics_to_validate: ["auth_failure_rate", "response_time"]
            
          - target_service: kafka-cluster
            relationship_type: "PUBLISHES_TO"
            criticality: "medium"
            failure_propagation_probability: 0.40
            failure_indicators:
              publish_errors: "> 0.05"
              lag: "> 1000"
            metrics_to_validate: ["publish_error_rate", "producer_lag"]
            
          - target_service: redis-cluster
            relationship_type: "CACHES_TO"
            criticality: "medium"
            failure_propagation_probability: 0.35
            failure_indicators:
              cache_miss_rate: "> 0.20"
              connection_errors: "> 0.02"
            metrics_to_validate: ["cache_miss_rate", "connection_error_rate"]
            
          - target_service: payment-gateway
            relationship_type: "INTEGRATES_WITH"
            criticality: "high"
            failure_propagation_probability: 0.60
            failure_indicators:
              external_api_errors: "> 0.08"
              timeout_rate: "> 0.05"
            metrics_to_validate: ["external_api_error_rate", "timeout_rate"]
      
      identity-service:
        entity_type: "APPLICATION"
        entity_guid_pattern: "mi-identity-service-*"
        entity_name_pattern: "identity-service"
        business_dependencies:
          - target_service: postgresql-primary
            relationship_type: "STORES_DATA"
            criticality: "critical"
            failure_propagation_probability: 0.95
            failure_indicators:
              db_connection_errors: "> 0.02"
              query_time: "> 1000ms"
            metrics_to_validate: ["db_connection_error_rate", "avg_query_time"]
            
          - target_service: redis-cluster  
            relationship_type: "CACHES_TO"
            criticality: "medium"
            failure_propagation_probability: 0.40
            failure_indicators:
              cache_errors: "> 0.05"
            metrics_to_validate: ["cache_error_rate"]
            
          - target_service: profile-service
            relationship_type: "COMMUNICATES_WITH"
            criticality: "medium"
            failure_propagation_probability: 0.50
            failure_indicators:
              service_errors: "> 0.08"
            metrics_to_validate: ["service_call_error_rate"]
      
      auth-service:
        entity_type: "APPLICATION"
        entity_guid_pattern: "mi-auth-service-*"
        entity_name_pattern: "auth-service"
        business_dependencies:
          - target_service: postgresql-primary
            relationship_type: "STORES_DATA"
            criticality: "critical"
            failure_propagation_probability: 0.90
            failure_indicators:
              db_errors: "> 0.03"
            metrics_to_validate: ["db_error_rate"]
            
          - target_service: redis-cluster
            relationship_type: "CACHES_TO"
            criticality: "medium"
            failure_propagation_probability: 0.45
            failure_indicators:
              session_errors: "> 0.05"
            metrics_to_validate: ["session_error_rate"]
      
      profile-service:
        entity_type: "APPLICATION"
        entity_guid_pattern: "mi-profile-service-*"
        entity_name_pattern: "profile-service"
        business_dependencies:
          - target_service: postgresql-primary
            relationship_type: "STORES_DATA"
            criticality: "critical"
            failure_propagation_probability: 0.88
            failure_indicators:
              db_errors: "> 0.03"
            metrics_to_validate: ["db_error_rate"]
            
          - target_service: elasticsearch-cluster
            relationship_type: "INDEXES_TO"
            criticality: "medium"
            failure_propagation_probability: 0.35
            failure_indicators:
              index_errors: "> 0.08"
            metrics_to_validate: ["elasticsearch_error_rate"]
      
      # Infrastructure Dependencies
      api-gateway:
        entity_type: "APPLICATION"
        entity_guid_pattern: "api-gateway-*"
        entity_name_pattern: "api-gateway"
        business_dependencies:
          - target_service: connector-service
            relationship_type: "ROUTES_TO"
            criticality: "high"
            failure_propagation_probability: 0.75
            failure_indicators:
              routing_errors: "> 0.05"
            metrics_to_validate: ["routing_error_rate"]
            
          - target_service: identity-service
            relationship_type: "ROUTES_TO"
            criticality: "high"
            failure_propagation_probability: 0.70
            failure_indicators:
              routing_errors: "> 0.05"
            metrics_to_validate: ["routing_error_rate"]
            
          - target_service: auth-service
            relationship_type: "ROUTES_TO"
            criticality: "high"
            failure_propagation_probability: 0.72
            failure_indicators:
              routing_errors: "> 0.05"
            metrics_to_validate: ["routing_error_rate"]
  
  # Generic Business Logic Patterns (for other services)
  generic_service_patterns:
    web_service_to_database:
      relationship_type: "DEPENDS_ON"
      criticality: "critical"
      failure_propagation_probability: 0.90
      failure_indicators:
        connection_errors: "> 0.02"
        query_time: "> 1000ms"
      
    service_to_cache:
      relationship_type: "CACHES_TO" 
      criticality: "medium"
      failure_propagation_probability: 0.40
      failure_indicators:
        cache_miss_rate: "> 0.25"
        
    service_to_message_queue:
      relationship_type: "PUBLISHES_TO"
      criticality: "medium"
      failure_propagation_probability: 0.45
      failure_indicators:
        publish_errors: "> 0.05"
        
    api_gateway_to_service:
      relationship_type: "ROUTES_TO"
      criticality: "high" 
      failure_propagation_probability: 0.75
      failure_indicators:
        routing_errors: "> 0.03"

# Enhanced Alert Category Mappings with Architectural Augmentation
alert_categories:

# Entity Types with their metadata and relationships
entity_types:
  # Kubernetes Pod
  KUBERNETES_POD:
    aliases: ["K8S_POD"]
    default_criticality: 0.6
    default_failure_probability: 0.2
    icon: "kubernetes-pod"
    color: "#326ce5"
    metadata:
      - podName
      - namespaceName
      - clusterName
      - status
      - createdAt
      - containerCount
      - labels
      - restartCount
      - phase
    relationships:
      - target: KUBERNETES_NODE
        relation: runs_on
        importance: high
        weight: 1.5
        propagation_probability: 0.8
        metrics: ["cpu_usage", "memory_usage", "restart_count"]
        query: >
          SELECT latest(nodeName) FROM K8sPodSample
          WHERE podName = '{entity_name}' AND clusterName like '%{cluster_name}%'
          SINCE {since_time_ms} UNTIL {until_time_ms}
      - target: KUBERNETES_DEPLOYMENT
        relation: managed_by
        importance: medium
        weight: 1.2
        propagation_probability: 0.6
        metrics: ["available_replicas", "desired_replicas", "updated_replicas"]
        query: >
          SELECT latest(deploymentName) FROM K8sPodSample
          WHERE podName = '{entity_name}' AND clusterName like '%{cluster_name}%'
          SINCE {since_time_ms} UNTIL {until_time_ms}
      - target: KUBERNETES_NAMESPACE
        relation: belongs_to
        importance: medium
        weight: 1.0
        propagation_probability: 0.3
        query: >
          SELECT latest(namespaceName) FROM K8sPodSample
          WHERE podName = '{entity_name}' AND clusterName like '%{cluster_name}%'
          SINCE {since_time_ms} UNTIL {until_time_ms}

  # Kubernetes Node
  KUBERNETES_NODE:
    aliases: ["K8S_NODE"]
    default_criticality: 0.8
    default_failure_probability: 0.05
    icon: "kubernetes-node"
    color: "#ff6b6b"
    metadata:
      - nodeName
      - clusterName
      - condition.Ready
      - unschedulable
      - kubeletVersion
      - osImage
      - regionName
      - instanceType
      - allocatableCapacity
      - allocatableCpu
      - allocatableMemory
    relationships:
      - target: KUBERNETES_CLUSTER
        relation: belongs_to
        importance: medium
        weight: 1.0
        propagation_probability: 0.4
        query: >
          SELECT latest(clusterName) FROM K8sNodeSample
          WHERE nodeName = '{entity_name}'
          SINCE {since_time_ms} UNTIL {until_time_ms}
      - target: KUBERNETES_POD
        relation: hosts
        importance: high
        weight: 2.0
        propagation_probability: 0.9
        metrics: ["cpu_usage", "memory_usage", "pod_count"]
        query: >
          SELECT uniqueCount(podName) as podCount, uniques(podName) as podNames
          FROM K8sPodSample
          WHERE nodeName = '{entity_name}' AND clusterName like '%{cluster_name}%'
          SINCE {since_time_ms} UNTIL {until_time_ms}

  # Kubernetes Deployment
  KUBERNETES_DEPLOYMENT:
    aliases: ["K8S_DEPLOYMENT"]
    metadata:
      - deploymentName
      - namespaceName
      - clusterName
      - createdAt
      - availableReplicas
      - desiredReplicas
      - updatedReplicas
    relationships:
      - target: KUBERNETES_POD
        relation: manages
        importance: high
        metrics: ["pod_status", "restart_count", "cpu_usage", "memory_usage"]
        query: >
          SELECT uniques(podName) as podNames
          FROM K8sPodSample
          WHERE deploymentName = '{entity_name}' AND clusterName like '%{cluster_name}%'
          SINCE {since_time_ms} UNTIL {until_time_ms}

  # Container
  CONTAINER:
    default_criticality: 0.4
    default_failure_probability: 0.3
    icon: "container"
    color: "#4ecdc4"
    metadata:
      - containerName
      - podName
      - namespaceName
      - clusterName
      - image
      - state
      - restartCount
      - cpuCoresUtilization
      - memoryUtilization
      - exitCode
    relationships:
      - target: KUBERNETES_POD
        relation: contained_by
        importance: high
        weight: 1.8
        propagation_probability: 0.7
        query: >
          SELECT latest(podName) FROM K8sContainerSample
          WHERE containerName = '{entity_name}' AND clusterName like '%{cluster_name}%'
          AND entityGuid = '{entity_guid}'
          SINCE {since_time_ms} UNTIL {until_time_ms}
      - target: KUBERNETES_NODE
        relation: runs_on
        importance: medium
        weight: 1.3
        propagation_probability: 0.5
        query: >
          SELECT latest(nodeName) FROM K8sContainerSample
          WHERE containerName = '{entity_name}' AND clusterName like '%{cluster_name}%'
          AND entityGuid = '{entity_guid}'
          SINCE {since_time_ms} UNTIL {until_time_ms}
      - target: KUBERNETES_DEPLOYMENT
        relation: managed_by
        importance: medium
        weight: 1.1
        propagation_probability: 0.4
        query: >
          SELECT latest(deploymentName) FROM K8sContainerSample
          WHERE containerName = '{entity_name}' AND clusterName like '%{cluster_name}%'
          AND entityGuid = '{entity_guid}'
          SINCE {since_time_ms} UNTIL {until_time_ms}

  # Host / Infrastructure
  HOST:
    aliases: ["INFRASTRUCTURE"]
    metadata:
      - hostname
      - operatingSystem
      - awsRegion
      - awsInstanceType
      - awsInstanceId
      - coreCount
      - memoryTotalBytes
    relationships:
      - target: INFRASTRUCTURE
        relation: belongs_to
        importance: medium
        query: >
          SELECT latest(awsRegion) as region, latest(awsAvailabilityZone) as availabilityZone
          FROM SystemSample
          WHERE hostname = '{entity_name}'
          SINCE {since_time_ms} UNTIL {until_time_ms}

  # Application
  APPLICATION:
    metadata:
      - name
      - language
      - appId
      - healthStatus
      - entityType
      - alertSeverity
    relationships:
      - target: INSTANCE
        relation: runs_on
        importance: medium
        query: >
          SELECT uniques(hostname) as hostnames
          FROM Transaction
          WHERE appName = '{entity_name}'
          SINCE {since_time_ms} UNTIL {until_time_ms}
      - target: APM_SERVICE
        relation: exposes
        importance: medium
        query: >
          SELECT uniques(name) as serviceNames
          FROM Metric
          WHERE appName = '{entity_name}' AND entityType = 'SERVICE'
          SINCE {since_time_ms} UNTIL {until_time_ms}

  # Kafka
  KAFKA:
    metadata:
      - name
      - version
      - broker_count
      - topic_count
      - partition_count
      - consumer_groups
      - replication_factor
    relationships:
      - target: KUBERNETES_POD
        relation: runs_on
        importance: high
        pattern: "^kafka-.*"
        parameters:
          cluster_name:
            pattern: "(\\w+)-kafka-\\d+"
            source: "entity_name"
      - target: KUBERNETES_POD
        relation: communicates_with
        importance: medium
        pattern: "^debezium-.*"
        parameters:
          cluster_name:
            source: "context"
            field: "cluster_name"

  # Debezium
  DEBEZIUM:
    metadata:
      - name
      - version
      - connector_type
      - source_database
      - destination_topic
      - lag
      - status
    relationships:
      - target: KAFKA
        relation: writes_to
        importance: high
        pattern: "^debezium-.*"
        parameters:
          topic:
            pattern: "debezium-(\\w+)"
            source: "entity_name"
      - target: DATABASE
        relation: reads_from
        importance: high
        pattern: "^debezium-.*"
        parameters:
          database_name:
            pattern: "debezium-(\\w+)"
            source: "entity_name"

# Alert Category Mappings
# For each alert category, define the entity relationship mapping and fallback strategies
alert_categories:
  # CrashLoopBackOff
  kubernetes_crashloopbackoff:
    primary_entity_type: "CONTAINER"
    # Simplified relationship traversal - no source_type filtering
    traverse_relationships:
      - target_type: "KUBERNETES_POD" 
        relationship: "CONTAINED_BY"
        metrics_to_collect: ["cpu_usage", "memory_usage", "restart_count", "container_state"]
      - target_type: "KUBERNETES_NODE"
        relationship: "RUNS_ON" 
        metrics_to_collect: ["cpu_pressure", "memory_pressure", "disk_pressure", "pod_count"]
      - target_type: "KUBERNETES_DEPLOYMENT"
        relationship: "MANAGED_BY"
        metrics_to_collect: ["available_replicas", "desired_replicas", "updated_replicas"]
    
    # Cascading failure characteristics
    failure_characteristics:
      initial_failure_probability: 0.9
      time_to_propagate: 300  # seconds
      blast_radius_multiplier: 1.2
      critical_threshold: 0.3
      recovery_time_estimate: 900  # seconds
      impact_severity: "high"
      propagation_pattern: "hierarchical"  # hierarchical, lateral, radial
      failure_triggers:
        - condition: "restart_count > 10"
          probability_increase: 0.2
        - condition: "cpu_usage > 90"
          probability_increase: 0.15
        - condition: "memory_usage > 85"
          probability_increase: 0.15
      mitigation_strategies:
        - "increase_resource_limits"
        - "check_dependent_services"
        - "review_application_logs"
        - "validate_configuration"
    
    # Fallback queries for missing entities
    fallback_queries:
      - type: "container_related"
        relationship: "CONTAINED_BY"
      - type: "custom_nrql"
        target_type: "KUBERNETES_POD"
        relationship: "MANAGES"
        query: >
          SELECT entityName, entityGuid, podName as name, namespaceName, clusterName, nodeName
          FROM K8sPodSample 
          WHERE entity.guid = '{entity_guid}' OR podName LIKE 'container-%'
          SINCE {since} UNTIL {until}
        result_mapping:
          guid: "entityGuid"
          name: "podName"
          cluster_name: "clusterName"
          namespace: "namespaceName"
    
    # Entity discovery for alerts without entity_guid
    entity_discovery:
      nrql_queries:
        - query: >
            SELECT entityName, entityGuid, containerName, podName, clusterName, namespaceName 
            FROM ContainerSample 
            WHERE containerName LIKE '%{service_name}%' OR containerName LIKE '%{component_name}%'
            AND clusterName like '%{cluster_name}%'
            SINCE {since} UNTIL {until}
            LIMIT 10
          entity_type: "CONTAINER"
          is_primary: true
          result_mapping:
            guid: "entityGuid"
            name: "containerName"
            pod_name: "podName"
            cluster_name: "clusterName"
            namespace: "namespaceName"

  # Kubernetes Evicted Pod
  kubernetes_evicted_pod:
    primary_entity_type: "KUBERNETES_POD"
    traverse_relationships:
      - target_type: "KUBERNETES_NODE"
        relationship: "RUNS_ON"
        metrics_to_collect: ["cpu_pressure", "memory_pressure", "disk_pressure", "pod_count"]
      - target_type: "KUBERNETES_DEPLOYMENT"
        relationship: "MANAGED_BY"
        metrics_to_collect: ["available_replicas", "desired_replicas", "updated_replicas"]
    
    # Cascading failure characteristics
    failure_characteristics:
      initial_failure_probability: 0.7
      time_to_propagate: 180  # seconds
      blast_radius_multiplier: 1.8
      critical_threshold: 0.4
      recovery_time_estimate: 600  # seconds
      impact_severity: "medium"
      propagation_pattern: "lateral"  # affects sibling pods
      failure_triggers:
        - condition: "node_memory_pressure"
          probability_increase: 0.3
        - condition: "node_disk_pressure"
          probability_increase: 0.25
        - condition: "node_cpu_pressure"
          probability_increase: 0.2
      mitigation_strategies:
        - "scale_down_non_critical_pods"
        - "increase_node_resources"
        - "check_resource_quotas"
        - "review_pod_resource_requests"
    
    fallback_queries:
      - type: "pod_related"
        relationship: "RUNS_ON"
      - type: "custom_nrql"
        target_type: "KUBERNETES_NODE"
        relationship: "RUNS_ON"
        query: >
          SELECT entityName, entityGuid, nodeName as name, clusterName
          FROM K8sNodeSample
          WHERE clusterName like '%{cluster_name}%'
          AND (cpuCoresUtilization > 80 OR memoryUtilization > 80)
          SINCE {since} UNTIL {until}
          LIMIT 5
        result_mapping:
          guid: "entityGuid"
          name: "nodeName"
          cluster_name: "clusterName"
    
    entity_discovery:
      nrql_queries:
        - query: >
            SELECT entityName, entityGuid, podName, clusterName, namespaceName, phase 
            FROM K8sPodSample 
            WHERE phase = 'Evicted' AND clusterName like '%{cluster_name}%'
            SINCE {since} UNTIL {until}
            LIMIT 10
          entity_type: "KUBERNETES_POD"
          is_primary: true
          result_mapping:
            guid: "entityGuid"
            name: "podName"
            cluster_name: "clusterName"
            namespace: "namespaceName"

  # Kubernetes Deployment Unavailable Pods
  kubernetes_deployment_unavailable_pods:
    primary_entity_type: "KUBERNETES_DEPLOYMENT"
    
    # Enable architectural relationship augmentation
    architectural_augmentation: true
    architecture_scope: "mi_production_architecture"
    
    traverse_relationships:
      # Standard telemetry-based relationships
      - source_type: "KUBERNETES_DEPLOYMENT"
        target_type: "KUBERNETES_POD"
        relationship: "MANAGES"
        importance: "high"
        discovery_method: "telemetry"
        metrics_to_collect: ["pod_status", "container_status", "restart_count", "cpu_usage", "memory_usage"]
        continue_traversal_if:
          - "restart_count > 5"
          - "cpu_usage > 0.8"
          - "memory_usage > 0.8" 
          - "pod_status != 'Running'"
          
      - source_type: "KUBERNETES_POD"
        target_type: "KUBERNETES_NODE"
        relationship: "RUNS_ON"
        importance: "medium"
        discovery_method: "telemetry"
        metrics_to_collect: ["cpu_pressure", "memory_pressure", "disk_pressure", "pod_count"]
        continue_traversal_if:
          - "cpu_pressure == true"
          - "memory_pressure == true"
          - "disk_pressure == true"
          
      - source_type: "KUBERNETES_POD"
        target_type: "CONTAINER"
        relationship: "CONTAINS"
        importance: "high"
        discovery_method: "telemetry"
        metrics_to_collect: ["container_status", "restart_count", "cpu_usage", "memory_usage"]
        continue_traversal_if:
          - "restart_count > 3"
          - "container_status != 'running'"
          
      # Architectural relationships for business logic
      - source_type: "APPLICATION"
        target_type: "APPLICATION"
        relationship: "DEPENDS_ON"
        importance: "critical"
        discovery_method: "architectural_config"
        validation_required: true
        metrics_to_collect: ["error_rate", "response_time", "availability"]
        continue_traversal_if:
          - "error_rate > 0.05"
          - "response_time > 500"
          - "availability < 0.95"
          
      - source_type: "APPLICATION"
        target_type: "DATABASE"
        relationship: "STORES_DATA"
        importance: "critical"
        discovery_method: "architectural_config"
        validation_required: true
        metrics_to_collect: ["db_connection_error_rate", "avg_query_time", "connection_pool_usage"]
        continue_traversal_if:
          - "db_connection_error_rate > 0.02"
          - "avg_query_time > 1000"
          - "connection_pool_usage > 0.9"
          
      - source_type: "APPLICATION"
        target_type: "CACHE"
        relationship: "CACHES_TO"
        importance: "medium"
        discovery_method: "architectural_config"
        validation_required: true
        metrics_to_collect: ["cache_miss_rate", "connection_error_rate"]
        continue_traversal_if:
          - "cache_miss_rate > 0.2"
          - "connection_error_rate > 0.02"
    
    fallback_queries:
      - type: "deployment_related"
        relationship: "MANAGES"
      - type: "custom_nrql"
        target_type: "KUBERNETES_POD"
        relationship: "MANAGED_BY"
        query: >
          SELECT entityName, entityGuid, podName as name, namespaceName, clusterName, status
          FROM K8sPodSample
          WHERE deploymentName = '{deployment_name}' AND clusterName like '%{cluster_name}%'
          AND (status != 'Running' OR restartCount > 5)
          SINCE {since} UNTIL {until}
          LIMIT 10
        result_mapping:
          guid: "entityGuid"
          name: "podName"
          status: "status"
          cluster_name: "clusterName"
          namespace: "namespaceName"
    
    entity_discovery:
      nrql_queries:
        - query: >
            SELECT entityName, entityGuid, deploymentName, clusterName, namespaceName, 
            unavailableReplicas, desiredReplicas 
            FROM K8sDeploymentSample 
            WHERE (unavailableReplicas / desiredReplicas) > 0.25
            AND clusterName like '%{cluster_name}%' AND deploymentName = '{deployment_name}'
            SINCE {since} UNTIL {until}
            LIMIT 10
          entity_type: "KUBERNETES_DEPLOYMENT"
          is_primary: true
          result_mapping:
            guid: "entityGuid" 
            name: "deploymentName"
            cluster_name: "clusterName"
            namespace: "namespaceName"
            unavailable_replicas: "unavailableReplicas"
            desired_replicas: "desiredReplicas"

  # Debezium Lag
  debezium_lag:
    primary_entity_type: "DEBEZIUM"
    traverse_relationships:
      - target_type: "KAFKA"
        relationship: "WRITES_TO"
        metrics_to_collect: ["throughput", "consumer_lag", "broker_count", "topic_partition_count"]
      - target_type: "DATABASE"
        relationship: "READS_FROM"
        metrics_to_collect: ["connection_count", "query_rate", "locks", "replication_lag"]
    
    fallback_queries:
      - type: "custom_nrql"
        target_type: "KUBERNETES_POD"
        relationship: "RUNS_AS"
        query: >
          SELECT entityName, entityGuid, podName as name, namespaceName, clusterName
          FROM K8sPodSample
          WHERE podName LIKE 'debezium-%' AND clusterName like '%{cluster_name}%'
          SINCE {since} UNTIL {until}
          LIMIT 5
        result_mapping:
          guid: "entityGuid"
          name: "podName"
          cluster_name: "clusterName"
          namespace: "namespaceName"
    
    entity_discovery:
      nrql_queries:
        - query: >
            SELECT entityName, entityGuid, podName, clusterName, namespaceName
            FROM K8sPodSample
            WHERE podName LIKE 'debezium-%' AND clusterName like '%{cluster_name}%'
            SINCE {since} UNTIL {until}
            LIMIT 5
          entity_type: "KUBERNETES_POD"
          is_primary: true
          result_mapping:
            guid: "entityGuid"
            name: "podName"
            cluster_name: "clusterName"
            namespace: "namespaceName"

# Alert Pattern Mappings for when entity GUID is not available in the alert
alert_patterns:
  - name: "debezium_lag_alert"
    title_pattern: ".*debezium.*lag.*"
    condition_pattern: ".*debezium.*lag.*"
    primary_entity_type: "DEBEZIUM"
    target_entities:
      - type: "KUBERNETES_POD"
        pattern: "^debezium-.*"
        metrics: ["cpu", "memory", "restarts", "network_received", "network_transmitted"]
      - type: "APPLICATION"
        pattern: "kafka"
        metrics: ["throughput", "consumer_lag", "broker_count", "topic_partition_count"]

  - name: "evicted_pod_alert"
    title_pattern: ".*evicted.*pod.*"
    condition_pattern: ".*evicted.*pod.*"
    primary_entity_type: "KUBERNETES_POD"
    target_entities:
      - type: "KUBERNETES_NODE"
        pattern: ".*"
        metrics: ["cpu", "memory", "disk", "network_received", "network_transmitted"]
      - type: "KUBERNETES_POD"
        pattern: ".*"
        metrics: ["status", "restarts", "cpu", "memory"]

  - name: "device_logs_alert"
    title_pattern: "device_logs_workers.*query result.*"
    condition_pattern: "device_logs_workers.*query result.*"
    primary_entity_type: "KUBERNETES_POD"
    target_entities:
      - type: "KUBERNETES_POD"
        pattern: "^device-log.*"
        metrics: ["cpu", "memory", "restarts", "network_received", "network_transmitted"]
      - type: "KUBERNETES_POD"
        pattern: "^kafka-.*"
        metrics: ["cpu", "memory", "restarts", "throughput"]

# Metadata fields to extract for each entity type
metadata_fields:
  K8S_POD:
    - podName
    - namespaceName
    - clusterName
    - status
    - createdAt
    - containerCount
    - labels

  K8S_NODE:
    - nodeName
    - clusterName
    - condition.Ready
    - unschedulable
    - kubeletVersion
    - osImage
    - regionName
    - instanceType

  K8S_CLUSTER:
    - clusterName
    - provider
    - regionName
    - version
    - nodeCount

  HOST:
    - hostname
    - operatingSystem
    - awsRegion
    - awsInstanceType
    - awsInstanceId
    - coreCount
    - memoryTotalBytes

  APPLICATION:
    - name
    - language
    - appId
    - healthStatus
    - entityType
    - alertSeverity

  KAFKA:
    - name
    - version
    - broker_count
    - topic_count
    - partition_count
    - consumer_groups
    - replication_factor

  DEBEZIUM:
    - name
    - version
    - connector_type
    - source_database
    - destination_topic
    - lag
    - status

# Alias mappings for entity types
aliases:
  KUBERNETES_POD: K8S_POD
  KUBERNETES_NODE: K8S_NODE
  KUBERNETES_CLUSTER: K8S_CLUSTER
  AWS_EC2_INSTANCE: HOST
  AZURE_VM: HOST
  KAFKA_BROKER: KAFKA
  KAFKA_TOPIC: KAFKA_TOPIC

# Graph-based Analysis Rules
graph_analysis_rules:
  # Critical Path Detection Rules
  critical_path_detection:
    path_criticality_threshold: 0.6
    max_path_length: 8
    path_scoring_weights:
      node_criticality: 0.4
      edge_weight: 0.3
      relationship_type: 0.2
      path_length: 0.1
    high_priority_relationships:
      - "DEPENDS_ON"
      - "RUNS_ON"
      - "MANAGES"
      - "CONTAINED_BY"
  
  # Cascading Failure Prediction
  cascading_failure_prediction:
    simulation_steps: 15
    convergence_threshold: 0.01
    uncertainty_factor: 0.05
    time_decay_factor: 0.85
    relationship_type_weights:
      DEPENDS_ON: 1.0
      RUNS_ON: 0.9
      MANAGES: 0.7
      CONTAINED_BY: 0.8
      COMMUNICATES_WITH: 0.5
      BELONGS_TO: 0.3
      WRITES_TO: 0.6
      READS_FROM: 0.6
  
  # Bottleneck Detection
  bottleneck_detection:
    flow_centrality_threshold: 0.7
    betweenness_centrality_threshold: 0.6
    degree_centrality_threshold: 0.8
    bottleneck_scoring_weights:
      betweenness: 0.6
      flow: 0.4
    critical_bottleneck_types:
      - "KUBERNETES_NODE"
      - "DATABASE"
      - "KAFKA"
      - "APPLICATION"
  
  # Resilience Assessment
  resilience_assessment:
    removal_strategies:
      - "random"
      - "targeted"
      - "degree_based"
    removal_percentage: 0.1
    fragmentation_threshold: 0.3
    connectivity_threshold: 0.5
    resilience_scoring_weights:
      connectivity: 0.5
      largest_component: 0.3
      average_path_length: 0.2
  
  # Graph Health Metrics
  graph_health_metrics:
    density_healthy_range: [0.1, 0.5]
    clustering_healthy_range: [0.2, 0.8]
    diameter_max_healthy: 8
    component_count_max_healthy: 3
    centralization_max_healthy: 0.7
    
  # Alert Enrichment Rules
  alert_enrichment:
    max_related_entities: 10
    relationship_depth: 2
    include_metrics: true
    include_historical_data: true
    urgency_scoring_factors:
      criticality_score: 0.3
      failure_probability: 0.2
      blast_radius: 0.2
      recovery_time: 0.15
      relationship_strength: 0.15
    
  # Monitoring Recommendations
  monitoring_recommendations:
    critical_entity_threshold: 0.7
    bottleneck_monitoring_priority: "high"
    cascade_risk_threshold: 0.5
    recommended_metrics_by_type:
      KUBERNETES_POD:
        - "cpu_usage"
        - "memory_usage"
        - "restart_count"
        - "network_throughput"
      KUBERNETES_NODE:
        - "cpu_pressure"
        - "memory_pressure"
        - "disk_pressure"
        - "pod_count"
        - "allocatable_resources"
      CONTAINER:
        - "cpu_cores_utilization"
        - "memory_utilization"
        - "restart_count"
        - "exit_code"
      APPLICATION:
        - "response_time"
        - "error_rate"
        - "throughput"
        - "availability"
      DATABASE:
        - "connection_count"
        - "query_rate"
        - "lock_waits"
        - "replication_lag"
