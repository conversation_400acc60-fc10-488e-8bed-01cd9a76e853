# Entity Metrics Configuration
# Defines metrics to collect for different entity types in New Relic

entities:
  KUBERNETES_POD:
    metrics:
      cpu:
        name: "CPU Utilization"
        nrql: "SELECT average(cpuCoresUtilization) AS 'CPU Utilization' FROM K8sPodSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: true
        unit: "percent"
      memory:
        name: "Memory Utilization" 
        nrql: "SELECT average(memoryUtilization) AS 'Memory Utilization' FROM K8sPodSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: true
        unit: "percent"
      restarts:
        name: "Container Restarts"
        nrql: "SELECT sum(containerRestarts) AS 'Container Restarts' FROM K8sPodSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: true
        unit: "count"
      network_received:
        name: "Network Received"
        nrql: "SELECT average(netReceivedBytesPerSecond) AS 'Network Received' FROM K8sPodSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: false
        unit: "bytes/sec"
      network_transmitted:
        name: "Network Transmitted"
        nrql: "SELECT average(netTransmittedBytesPerSecond) AS 'Network Transmitted' FROM K8sPodSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: false
        unit: "bytes/sec"
        
  KUBERNETES_NODE:
    metrics:
      cpu:
        name: "CPU Utilization"
        nrql: "SELECT average(cpuPercent) AS 'CPU Utilization' FROM K8sNodeSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: true
        unit: "percent"
      memory:
        name: "Memory Utilization"
        nrql: "SELECT average(memoryUsedPercent) AS 'Memory Utilization' FROM K8sNodeSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: true
        unit: "percent"
      disk:
        name: "Disk Utilization"
        nrql: "SELECT average(fsUsedPercent) AS 'Disk Utilization' FROM K8sNodeSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: true
        unit: "percent"
      network_received:
        name: "Network Received"
        nrql: "SELECT average(receivedBytesPerSecond) AS 'Network Received' FROM K8sNodeSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: false
        unit: "bytes/sec"
      network_transmitted:
        name: "Network Transmitted"
        nrql: "SELECT average(transmittedBytesPerSecond) AS 'Network Transmitted' FROM K8sNodeSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: false
        unit: "bytes/sec"
        
  CONTAINER:
    metrics:
      cpu:
        name: "CPU Utilization"
        nrql: "SELECT average(cpuPercent) AS 'CPU Utilization' FROM ContainerSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: true
        unit: "percent"
      memory:
        name: "Memory Usage"
        nrql: "SELECT average(memoryUsageBytes) AS 'Memory Usage' FROM ContainerSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: true
        unit: "bytes"
        
  HOST:
    metrics:
      cpu:
        name: "CPU Utilization"
        nrql: "SELECT average(cpuPercent) AS 'CPU Utilization' FROM SystemSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: true
        unit: "percent"
      memory:
        name: "Memory Utilization"
        nrql: "SELECT average(memoryUsedPercent) AS 'Memory Utilization' FROM SystemSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: true
        unit: "percent"
      disk:
        name: "Disk Utilization"
        nrql: "SELECT average(diskUsedPercent) AS 'Disk Utilization' FROM SystemSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: true
        unit: "percent"
      network_io:
        name: "Network IO"
        nrql: "SELECT average(networkIOBytesPerSecond) AS 'Network IO' FROM SystemSample WHERE entityGuid = '{entity_guid}' TIMESERIES {period} SINCE '{since}' UNTIL '{until}'"
        default: false
        unit: "bytes/sec"

  # Alias mappings for entity types
  aliases:
    K8S_POD: KUBERNETES_POD
    KUBERNETES_POD_ENTITY: KUBERNETES_POD
    K8S_NODE: KUBERNETES_NODE
    KUBERNETES_NODE_ENTITY: KUBERNETES_NODE 