# NRQL Queries Configuration
# Organized by entity type and metric name for centralized management

# Kubernetes Pod Queries
kubernetes_pod:
  cpu_usage: >
    SELECT average(cpuCoresUtilization) FROM K8sContainerSample
    WHERE podName = '{pod_name}' AND clusterName = '{cluster_name}'
    FACET containerName
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  memory_usage: >
    SELECT average(memoryUtilization) FROM K8sContainerSample
    WHERE podName = '{pod_name}' AND clusterName = '{cluster_name}'
    FACET containerName
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  restart_count: >
    SELECT max(restartCount) FROM K8sContainerSample
    WHERE podName = '{pod_name}' AND clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  container_status: >
    SELECT latest(status) FROM K8sContainerSample
    WHERE podName = '{pod_name}' AND clusterName = '{cluster_name}'
    FACET containerName
    SINCE {since_time_ms} UNTIL {until_time_ms}
    
  network_io: >
    SELECT average(net.rxBytesPerSecond) as 'receiveBytesPerSecond', 
           average(net.txBytesPerSecond) as 'transmitBytesPerSecond',
           average(net.errorsPerSecond) as 'networkErrorsPerSecond'
    FROM K8sPodSample
    WHERE podName = '{pod_name}' AND clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES
    
  volume_usage: >
    SELECT latest(fsUsedPercent) as 'volumeUsedPercent',
           latest(fsUsedBytes) as 'volumeUsedBytes',
           latest(fsCapacityBytes) as 'volumeCapacityBytes'
    FROM K8sVolumeSample
    WHERE podName = '{pod_name}' AND clusterName = '{cluster_name}'
    FACET volumeName, pvcName
    SINCE {since_time_ms} UNTIL {until_time_ms}

# Kubernetes Node Queries
kubernetes_node:
  cpu_usage: >
    SELECT average(cpuUsedCores), average(cpuUsedCoresPercentage) FROM K8sNodeSample
    WHERE nodeName = '{node_name}' AND clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  memory_usage: >
    SELECT average(memoryUsedBytes), average(memoryUsedPercent) FROM K8sNodeSample
    WHERE nodeName = '{node_name}' AND clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  pod_count: >
    SELECT latest(podCount) FROM K8sNodeSample
    WHERE nodeName = '{node_name}' AND clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  condition: >
    SELECT latest(condition.Ready) FROM K8sNodeSample
    WHERE nodeName = '{node_name}' AND clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms}
    
  disk_usage: >
    SELECT average(fsUsedPercent) as 'diskUsedPercent',
           average(fsUsedBytes) as 'diskUsedBytes',
           average(fsCapacityBytes) as 'diskCapacityBytes'
    FROM K8sNodeSample
    WHERE nodeName = '{node_name}' AND clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES
    
  network_io: >
    SELECT average(net.rxBytesPerSecond) as 'receiveBytesPerSecond', 
           average(net.txBytesPerSecond) as 'transmitBytesPerSecond'
    FROM K8sNodeSample
    WHERE nodeName = '{node_name}' AND clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES
    
  allocatable_resources: >
    SELECT latest(allocatableCpuCores) as 'allocatableCpuCores',
           latest(allocatableMemoryBytes) as 'allocatableMemoryBytes',
           latest(allocatablePods) as 'allocatablePods'
    FROM K8sNodeSample
    WHERE nodeName = '{node_name}' AND clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms}

# Kubernetes Cluster Queries
kubernetes_cluster:
  resource_usage: >
    SELECT average(cpuUsedCores) as 'cpuUsedCores',
           average(allocatableCpuCores) as 'allocatableCpuCores',
           average(cpuUsedCores/allocatableCpuCores)*100 as 'cpuUtilizationPercent',
           average(memoryWorkingSetBytes) as 'memoryUsedBytes',
           average(allocatableMemoryBytes) as 'allocatableMemoryBytes',
           average(memoryWorkingSetBytes/allocatableMemoryBytes)*100 as 'memoryUtilizationPercent'
    FROM K8sNodeSample
    WHERE clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms}
    TIMESERIES AUTO
    
  pod_counts: >
    SELECT uniqueCount(podName) as 'totalPods',
           filter(uniqueCount(podName), WHERE status = 'Running') as 'runningPods',
           filter(uniqueCount(podName), WHERE status = 'Pending') as 'pendingPods',
           filter(uniqueCount(podName), WHERE status = 'Failed') as 'failedPods',
           filter(uniqueCount(podName), WHERE status = 'Unknown') as 'unknownPods'
    FROM K8sPodSample
    WHERE clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms}
    
  nodes: >
    SELECT uniqueCount(nodeName) as 'nodeCount',
           sum(allocatableCpuCores) as 'totalAllocatableCpuCores',
           sum(allocatableMemoryBytes) as 'totalAllocatableMemoryBytes'
    FROM K8sNodeSample
    WHERE clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms}
    
  deployments: >
    SELECT uniqueCount(deploymentName) as 'deploymentCount'
    FROM K8sDeploymentSample
    WHERE clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms}
    
  events: >
    SELECT timestamp,
           event.type as 'eventType',
           event.reason as 'reason',
           event.message as 'message',
           event.count as 'count',
           event.involvedObject.kind as 'kind',
           event.involvedObject.name as 'name',
           event.involvedObject.namespace as 'namespace'
    FROM InfrastructureEvent
    WHERE category = 'kubernetes'
    AND clusterName = '{cluster_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms}
    LIMIT {limit}

# Host Queries
host:
  cpu_usage: >
    SELECT average(cpuPercent) FROM SystemSample
    WHERE hostname = '{hostname}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  memory_usage: >
    SELECT average(memoryUsedPercent) FROM SystemSample
    WHERE hostname = '{hostname}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  disk_usage: >
    SELECT average(diskUsedPercent) FROM SystemSample
    WHERE hostname = '{hostname}'
    FACET mountPoint
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  network_io: >
    SELECT average(networkReceiveBytes), average(networkTransmitBytes) FROM SystemSample
    WHERE hostname = '{hostname}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

# Application Queries
application:
  response_time: >
    SELECT average(duration) FROM Transaction
    WHERE appName = '{app_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  throughput: >
    SELECT count(*) FROM Transaction
    WHERE appName = '{app_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  error_rate: >
    SELECT percentage(count(*), WHERE error is true) FROM Transaction
    WHERE appName = '{app_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES

  apdex: >
    SELECT apdex(duration, t: {apdex_t}) FROM Transaction
    WHERE appName = '{app_name}'
    SINCE {since_time_ms} UNTIL {until_time_ms} TIMESERIES 