# Database Queries Configuration
# Organized by product type and context purpose for retrieving 
# additional information during incident analysis

# MDM Product Queries
mdm:
  # General context queries for MDM incidents
  general_context:
    application_status: >
      SELECT status, last_status_change, error_count 
      FROM mdm_application_status 
      WHERE application_name = '{application_name}' 
      AND timestamp BETWEEN '{since_time}' AND '{until_time}'
      ORDER BY timestamp DESC
      LIMIT 1
    
    deployment_history: >
      SELECT deployment_id, version, deployment_time, status, deployed_by
      FROM mdm_deployments 
      WHERE application_name = '{application_name}'
      AND deployment_time BETWEEN 
        ('{since_time}'::timestamp - INTERVAL '24 hours') AND '{until_time}'::timestamp
      ORDER BY deployment_time DESC
      LIMIT 5
    
    configuration_changes: >
      SELECT change_id, config_key, old_value, new_value, changed_by, change_time
      FROM mdm_configuration_changes
      WHERE application_name = '{application_name}'
      AND change_time BETWEEN 
        ('{since_time}'::timestamp - INTERVAL '12 hours') AND '{until_time}'::timestamp
      ORDER BY change_time DESC
      LIMIT 10
  
  # RCA specific queries to help with root cause analysis
  rca:
    recent_failures: >
      SELECT incident_id, title, start_time, end_time, severity, root_cause
      FROM incident_history
      WHERE related_application = '{application_name}'
      AND landscape = '{landscape}'
      AND start_time BETWEEN 
        ('{since_time}'::timestamp - INTERVAL '7 days') AND '{until_time}'::timestamp
      ORDER BY start_time DESC
      LIMIT 5
    
    system_health_metrics: >
      SELECT metric_name, avg_value, max_value, min_value, collection_time
      FROM mdm_system_metrics
      WHERE application = '{application_name}'
      AND metric_name IN ('response_time', 'error_rate', 'throughput', 'cpu_usage', 'memory_usage')
      AND collection_time BETWEEN '{since_time}' AND '{until_time}'
      GROUP BY metric_name, collection_time
      ORDER BY collection_time DESC

    dependency_health: >
      SELECT dependency_name, status, last_check_time, response_time_ms
      FROM mdm_dependency_health
      WHERE application = '{application_name}'
      AND last_check_time BETWEEN '{since_time}' AND '{until_time}'
      ORDER BY last_check_time DESC

# Neurons Product Queries
neurons:
  # General context queries for Neurons incidents
  general_context:
    pod_restarts: >
      SELECT pod_name, namespace, container_name, restart_count, last_restart_time
      FROM neurons_pod_events
      WHERE cluster_name = '{cluster_name}'
      AND pod_name LIKE '%{pod_name_pattern}%'
      AND last_restart_time BETWEEN 
        ('{since_time}'::timestamp - INTERVAL '6 hours') AND '{until_time}'::timestamp
      ORDER BY last_restart_time DESC
      LIMIT 10
    
    deployment_history: >
      SELECT deployment_id, deployment_name, version, namespace, deployment_time, status
      FROM neurons_deployments
      WHERE cluster_name = '{cluster_name}'
      AND namespace = '{namespace}'
      AND deployment_time BETWEEN 
        ('{since_time}'::timestamp - INTERVAL '24 hours') AND '{until_time}'::timestamp
      ORDER BY deployment_time DESC
      LIMIT 5
    
    autoscale_events: >
      SELECT event_time, resource_name, namespace, old_replicas, new_replicas, reason
      FROM neurons_autoscale_events
      WHERE cluster_name = '{cluster_name}'
      AND namespace = '{namespace}'
      AND event_time BETWEEN 
        ('{since_time}'::timestamp - INTERVAL '6 hours') AND '{until_time}'::timestamp
      ORDER BY event_time DESC
      LIMIT 10
  
  # RCA specific queries to help with root cause analysis
  rca:
    resource_utilization: >
      SELECT pod_name, avg(cpu_usage) as avg_cpu, max(cpu_usage) as max_cpu,
             avg(memory_usage) as avg_memory, max(memory_usage) as max_memory,
             measurement_time
      FROM neurons_resource_metrics
      WHERE cluster_name = '{cluster_name}'
      AND namespace = '{namespace}'
      AND pod_name LIKE '%{pod_name_pattern}%'
      AND measurement_time BETWEEN '{since_time}' AND '{until_time}'
      GROUP BY pod_name, measurement_time
      ORDER BY measurement_time DESC
    
    recent_incidents: >
      SELECT incident_id, title, start_time, severity, root_cause_summary
      FROM incident_history
      WHERE cluster_name = '{cluster_name}'
      AND namespace = '{namespace}'
      AND start_time BETWEEN 
        ('{since_time}'::timestamp - INTERVAL '7 days') AND '{until_time}'::timestamp
      ORDER BY start_time DESC
      LIMIT 5
    
    container_terminations: >
      SELECT pod_name, container_name, exit_code, reason, termination_time
      FROM neurons_container_terminations
      WHERE cluster_name = '{cluster_name}'
      AND namespace = '{namespace}'
      AND termination_time BETWEEN '{since_time}' AND '{until_time}'
      ORDER BY termination_time DESC
      LIMIT 15

# General Queries for Any Product
general:
  # Maintenance window information
  maintenance_windows: >
    SELECT mw_id, description, start_time, end_time, affected_systems, status
    FROM maintenance_windows
    WHERE (
      (start_time BETWEEN '{since_time}' AND '{until_time}') OR
      (end_time BETWEEN '{since_time}' AND '{until_time}') OR
      (start_time <= '{since_time}' AND end_time >= '{until_time}')
    )
    AND affected_systems LIKE '%{product}%'
    ORDER BY start_time DESC
  
  # Historical incidents for pattern recognition
  historical_incidents: >
    SELECT incident_id, title, severity, start_time, end_time, root_cause, resolution
    FROM incident_history
    WHERE product = '{product}'
    AND (
      LOWER(title) SIMILAR TO '%(error|failure|outage|degradation|latency|{alert_keyword})%' OR
      LOWER(description) SIMILAR TO '%(error|failure|outage|degradation|latency|{alert_keyword})%'
    )
    AND start_time BETWEEN 
      ('{since_time}'::timestamp - INTERVAL '30 days') AND '{until_time}'::timestamp
    ORDER BY similarity_score(title, '{alert_title}') DESC
    LIMIT 5
  
  # Scheduled jobs that might affect the system
  scheduled_jobs: >
    SELECT job_id, job_name, description, schedule, last_run_time, next_run_time, status
    FROM scheduled_jobs
    WHERE product = '{product}'
    AND (
      (last_run_time BETWEEN 
        ('{since_time}'::timestamp - INTERVAL '1 hour') AND '{until_time}'::timestamp) OR
      (next_run_time BETWEEN '{since_time}' AND 
        ('{until_time}'::timestamp + INTERVAL '1 hour'))
    )
    ORDER BY last_run_time DESC
    LIMIT 10 