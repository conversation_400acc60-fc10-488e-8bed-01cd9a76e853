# Graph-Based Cascading Failure Detection Implementation

## Overview

This document provides a comprehensive technical overview of the graph-based cascading failure detection system implemented for the AI incident management platform. The system uses NetworkX graphs to model entity relationships and predict cascading failures across microservices architectures.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Core Components](#core-components)
3. [Graph Service Implementation](#graph-service-implementation)
4. [Cascading Failure Analysis](#cascading-failure-analysis)
5. [Data Models](#data-models)
6. [Workflow Integration](#workflow-integration)
7. [Configuration](#configuration)
8. [Usage Examples](#usage-examples)
9. [Performance Considerations](#performance-considerations)
10. [Testing](#testing)

## Architecture Overview

The graph-based system extends the existing incident management workflow by adding entity relationship modeling and cascading failure prediction capabilities. It integrates seamlessly with the existing LangGraph workflow and Pydantic AI agents.

```mermaid
graph TB
    Alert[Alert Input] --> Parser[Alert Parser Agent]
    Parser --> EntityAnalyzer[Entity Analyzer Agent]
    EntityAnalyzer --> EntityRelationships[Entity Relationships Agent]
    EntityRelationships --> GraphAnalysis[Graph Analysis Node]
    GraphAnalysis --> GraphService[Graph Service]
    GraphService --> CascadingAnalysis[Cascading Failure Analysis]
    CascadingAnalysis --> GraphEnrichment[Graph Enrichment Node]
    GraphEnrichment --> NextSteps[Continue Workflow]
    
    GraphService --> NetworkX[NetworkX Graph]
    GraphService --> NewRelic[New Relic API]
    GraphService --> Config[Entity Relationships Config]
```

## Core Components

### 1. Graph Service (`ai_incident_manager/services/graph_service.py`)

The `GraphService` is the central component responsible for:
- Building entity relationship graphs from topology data
- Performing cascading failure analysis
- Finding critical paths between entities
- Calculating graph metrics and centrality measures

#### Key Methods:

```python
class GraphService:
    async def build_entity_graph(
        self,
        primary_entity_guid: str,
        entity_type: str,
        entity_name: str,
        alert_category: str,
        cluster_name: Optional[str] = None,
        max_depth: int = 3,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None
    ) -> nx.DiGraph
    
    def analyze_cascading_failures(
        self,
        graph: nx.DiGraph,
        primary_entity_guid: str,
        failure_threshold: float = 0.1,
        propagation_decay: float = 0.8,
        max_hops: int = 5
    ) -> CascadingFailureAnalysis
    
    def find_critical_paths(
        self,
        graph: nx.DiGraph,
        source: str,
        target: str
    ) -> List[List[str]]
```

### 2. Graph Data Models (`ai_incident_manager/models/graph_models.py`)

Comprehensive Pydantic models for type safety and validation:

```python
class GraphNodeModel(BaseModel):
    id: str
    name: str
    node_type: NodeTypeEnum
    entity_guid: Optional[str] = None
    properties: Dict[str, Any] = Field(default_factory=dict)
    metrics: Dict[str, Any] = Field(default_factory=dict)
    health_status: HealthStatusEnum = HealthStatusEnum.UNKNOWN
    criticality_score: float = Field(default=0.0, ge=0.0, le=1.0)
    failure_probability: float = Field(default=0.0, ge=0.0, le=1.0)
    cluster_name: Optional[str] = None
    namespace: Optional[str] = None

class GraphEdgeModel(BaseModel):
    source: str
    target: str
    relationship_type: RelationshipTypeEnum
    weight: float = Field(default=1.0, ge=0.0)
    strength: float = Field(default=1.0, ge=0.0, le=1.0)
    failure_propagation_probability: float = Field(default=0.5, ge=0.0, le=1.0)
    properties: Dict[str, Any] = Field(default_factory=dict)
```

### 3. Cascading Failure Agent (`ai_incident_manager/agents/cascading_failure_agent.py`)

Specialized Pydantic AI agent for intelligent failure analysis:

```python
@cascading_failure_agent.tool
async def build_entity_graph_tool(
    ctx: RunContext[CascadingFailureAgentDeps],
    primary_entity_guid: str,
    entity_type: str,
    max_depth: int = 3
) -> Dict[str, Any]:
    """Build entity relationship graph for cascading failure analysis."""
    
@cascading_failure_agent.tool
async def analyze_cascading_failures_tool(
    ctx: RunContext[CascadingFailureAgentDeps],
    graph_data: Dict[str, Any],
    failure_threshold: float = 0.1
) -> Dict[str, Any]:
    """Analyze potential cascading failures in the entity graph."""
```

## Graph Service Implementation

### Graph Construction Process

1. **Primary Entity Initialization**: The service starts with the primary failing entity from the alert
2. **Relationship Discovery**: Uses the entity relationship service to discover connected entities
3. **Graph Building**: Constructs a NetworkX directed graph with entities as nodes and relationships as edges
4. **Enrichment**: Adds metrics, health status, and failure probabilities to nodes and edges

```python
def build_entity_graph(self, primary_entity_guid: str, entity_type: str, ...):
    # Create new graph
    G = nx.DiGraph()
    
    # Add primary entity with failure indicators
    primary_node = GraphNode(
        id=primary_entity_guid,
        name=entity_name,
        node_type=NodeType(entity_type),
        entity_guid=primary_entity_guid,
        health_status="issue",
        criticality_score=1.0,
        failure_probability=0.9
    )
    
    G.add_node(primary_entity_guid, **primary_node.__dict__)
    
    # Discover and add related entities
    try:
        relationships = await self.entity_relationship_service.traverse_relationships(
            primary_entity_guid=primary_entity_guid,
            entity_type=entity_type,
            alert_category=alert_category,
            max_depth=max_depth,
            cluster_name=cluster_name,
            since_time_ms=since_time_ms,
            until_time_ms=until_time_ms
        )
        
        # Add related entities and relationships to graph
        self._add_relationships_to_graph(G, relationships)
        
    except Exception as e:
        self.logger.warning(f"Could not traverse relationships: {str(e)}")
    
    return G
```

### Graph Structure

The graph uses the following structure:
- **Nodes**: Represent entities (services, databases, infrastructure components)
- **Edges**: Represent dependencies and relationships between entities
- **Node Attributes**: Health status, criticality score, failure probability, metrics
- **Edge Attributes**: Relationship type, weight, failure propagation probability

## Cascading Failure Analysis

### Algorithm Overview

The cascading failure analysis uses a **breadth-first search (BFS)** algorithm with **probabilistic failure propagation**:

```python
def analyze_cascading_failures(
    self,
    graph: nx.DiGraph,
    primary_entity_guid: str,
    failure_threshold: float = 0.1,
    propagation_decay: float = 0.8,
    max_hops: int = 5
) -> CascadingFailureAnalysis:
    
    # Initialize failure probabilities
    failure_probabilities = {
        node: graph.nodes[node].get('failure_probability', 0.0) 
        for node in graph.nodes()
    }
    failure_probabilities[primary_entity_guid] = 1.0  # Primary entity has failed
    
    # Simulate failure propagation using BFS
    queue = deque([(primary_entity_guid, 1.0, [primary_entity_guid])])
    visited_edges = set()
    
    while queue:
        current_entity, current_prob, path = queue.popleft()
        
        if len(path) > max_hops:
            continue
            
        # Propagate failure to dependent entities
        for neighbor in graph.successors(current_entity):
            edge_data = graph.get_edge_data(current_entity, neighbor)
            propagation_prob = edge_data.get('failure_propagation_probability', 0.5)
            
            # Calculate cascading failure probability
            propagated_prob = current_prob * propagation_prob * (propagation_decay ** (len(path) - 1))
            
            # Update neighbor's failure probability
            neighbor_prob = failure_probabilities.get(neighbor, 0.0)
            new_prob = min(1.0, neighbor_prob + propagated_prob)
            failure_probabilities[neighbor] = new_prob
            
            # If probability exceeds threshold, mark as affected
            if new_prob >= failure_threshold:
                affected_entities.add(neighbor)
                queue.append((neighbor, new_prob, path + [neighbor]))
```

### Failure Propagation Model

The system uses a sophisticated failure propagation model:

1. **Initial State**: Primary entity has 100% failure probability
2. **Propagation**: Failure probability propagates through edges based on:
   - Edge failure propagation probability (relationship strength)
   - Propagation decay factor (distance-based reduction)
   - Edge weight (relationship importance)
3. **Accumulation**: Multiple failure paths to the same entity accumulate probability
4. **Threshold**: Entities exceeding the failure threshold are considered affected

### Risk Assessment

The system calculates several risk metrics:

```python
# Calculate impact score based on affected entities and their criticality
total_criticality = sum(
    graph.nodes[entity].get('criticality_score', 0.5) 
    for entity in affected_entities
)
impact_score = min(1.0, total_criticality / len(graph.nodes()))

# Calculate blast radius (number of potentially affected entities)
blast_radius = len(affected_entities)

# Identify critical dependencies
critical_dependencies = [
    entity for entity, prob in failure_probabilities.items()
    if prob >= failure_threshold and entity != primary_entity_guid
]
```

## Data Models

### Node Types

```python
class NodeTypeEnum(str, Enum):
    KUBERNETES_POD = "KUBERNETES_POD"
    KUBERNETES_NODE = "KUBERNETES_NODE"
    KUBERNETES_DEPLOYMENT = "KUBERNETES_DEPLOYMENT"
    KUBERNETES_NAMESPACE = "KUBERNETES_NAMESPACE"
    KUBERNETES_CLUSTER = "KUBERNETES_CLUSTER"
    KUBERNETES_CONTAINER = "KUBERNETES_CONTAINER"
    CONTAINER = "CONTAINER"
    APPLICATION = "APPLICATION"
    HOST = "HOST"
    DATABASE = "DATABASE"
    KAFKA = "KAFKA"
    DEBEZIUM = "DEBEZIUM"
    UNKNOWN = "UNKNOWN"
```

### Relationship Types

```python
class RelationshipTypeEnum(str, Enum):
    RUNS_ON = "RUNS_ON"
    CONTAINS = "CONTAINS"
    DEPENDS_ON = "DEPENDS_ON"
    COMMUNICATES_WITH = "COMMUNICATES_WITH"
    MANAGES = "MANAGES"
    BELONGS_TO = "BELONGS_TO"
    WRITES_TO = "WRITES_TO"
    READS_FROM = "READS_FROM"
    RELATED_TO = "RELATED_TO"
    CONTAINED_BY = "CONTAINED_BY"
    MANAGED_BY = "MANAGED_BY"
```

### Analysis Results

```python
@dataclass
class CascadingFailureAnalysis:
    primary_entity: str
    affected_entities: List[str]
    failure_paths: List[List[str]]
    risk_scores: Dict[str, float]
    critical_dependencies: List[str]
    potential_blast_radius: int
    estimated_impact_score: float
    recommended_actions: List[str]
```

## Workflow Integration

### Graph Analysis Nodes

The system integrates with the LangGraph workflow through two specialized nodes:

1. **Graph Analysis Node** (`graph_analysis_node`):
   - Performs comprehensive graph analysis
   - Runs cascading failure detection
   - Calculates risk assessments

2. **Graph Enrichment Node** (`graph_enrichment_node`):
   - Enriches incident with graph-based insights
   - Generates monitoring recommendations
   - Creates escalation criteria

### Enhanced Workflow

```python
def create_enhanced_graph_workflow() -> StateGraph:
    workflow = StateGraph(IncidentState)
    
    # Standard workflow nodes
    workflow.add_edge(START, "analyze_alert")
    workflow.add_edge("analyze_alert", "analyze_entity")
    workflow.add_edge("analyze_entity", "analyze_entity_relationships")
    
    # NEW: Graph analysis integration
    workflow.add_edge("analyze_entity_relationships", "graph_analysis")
    workflow.add_edge("graph_analysis", "graph_enrichment")
    
    # Continue with existing workflow
    workflow.add_edge("graph_enrichment", "execute_runbooks")
    # ... rest of workflow
```

## Configuration

### Entity Relationships Configuration

Enhanced `config/entity_relationships.yaml` with graph analysis parameters:

```yaml
graph_analysis:
  default_parameters:
    max_depth: 3
    failure_threshold: 0.1
    propagation_decay: 0.8
    max_hops: 5
    critical_path_threshold: 0.7
  
  failure_characteristics:
    kubernetes_deployment:
      base_failure_probability: 0.05
      propagation_probability: 0.3
      criticality_score: 0.8
    
    database:
      base_failure_probability: 0.02
      propagation_probability: 0.6
      criticality_score: 0.95
```

### Monitoring Recommendations

```yaml
monitoring_recommendations:
  high_criticality_entities:
    metrics: ["cpu_usage", "memory_usage", "error_rate", "availability"]
    thresholds:
      cpu_usage: 0.8
      memory_usage: 0.85
      error_rate: 0.05
      availability: 0.95
```

## Usage Examples

### Basic Graph Analysis

```python
from ai_incident_manager.services.graph_service import get_graph_service

# Initialize graph service
graph_service = get_graph_service()

# Build entity graph
graph = await graph_service.build_entity_graph(
    primary_entity_guid="failing-service-001",
    entity_type="KUBERNETES_DEPLOYMENT",
    entity_name="connector-service",
    alert_category="kubernetes_availability",
    cluster_name="production-cluster",
    max_depth=3
)

# Analyze cascading failures
analysis = graph_service.analyze_cascading_failures(
    graph=graph,
    primary_entity_guid="failing-service-001",
    failure_threshold=0.25,
    max_hops=3
)

print(f"Blast radius: {analysis.potential_blast_radius} entities")
print(f"Impact score: {analysis.estimated_impact_score:.2f}")
print(f"Affected entities: {analysis.affected_entities}")
```

### Agent-Based Analysis

```python
from ai_incident_manager.agents.cascading_failure_agent import cascading_failure_agent

# Create analysis prompt
prompt = """
Analyze cascading failure risk for connector-service failure in production cluster.
Primary entity: connector-service (GUID: failing-service-001)
Alert: Pod availability below 75%
"""

# Run agent analysis
result = await cascading_failure_agent.run(prompt, deps=agent_deps)

# Extract insights
analysis_summary = result.data.analysis_summary
risk_assessment = result.data.risk_assessment
recommended_actions = result.data.recommended_actions
```

## Performance Considerations

### Scalability

1. **Graph Size Limits**: 
   - Recommended maximum: 500 nodes, 2000 edges
   - Uses depth-limited traversal to control graph size
   - Implements caching for frequently accessed graphs

2. **Computation Complexity**:
   - Graph construction: O(V + E) where V = nodes, E = edges
   - Cascading failure analysis: O(V × E × max_hops)
   - Critical path finding: O(V!)

3. **Memory Usage**:
   - NetworkX graph: ~1KB per node + edge metadata
   - Analysis results: ~100B per affected entity
   - Caching: Configurable TTL (default 5 minutes)

### Optimization Strategies

```python
class GraphService:
    def __init__(self, cache_timeout_seconds: int = 300):
        self.cache_timeout = cache_timeout_seconds
        self.graph_cache = {}  # In-memory caching
        self.cache_timestamps = {}
        
    def _is_cache_valid(self, cache_key: str) -> bool:
        if cache_key not in self.cache_timestamps:
            return False
        
        age = time.time() - self.cache_timestamps[cache_key]
        return age < self.cache_timeout
```

## Testing

### Test Scenarios

1. **Unit Tests** (`tests/test_graph_integration.py`):
   - Graph service functionality
   - Cascading failure algorithms
   - Data model validation

2. **Integration Tests**:
   - End-to-end workflow integration
   - Agent interaction testing
   - Configuration validation

3. **Scenario Tests** (`test_graph_scenario.py`):
   - MI architecture modeling
   - Real-world failure simulation
   - Performance benchmarking

### MI Architecture Test Case

The implementation includes a comprehensive test case modeling the Mobile Identity (MI) microservices architecture:

```python
# 12 interconnected services
services = [
    "connector-service",      # Primary failing service
    "identity-service",       # Core identity management
    "auth-service",          # Authentication
    "profile-service",       # User profiles
    "kafka-cluster",         # Event streaming
    "redis-cluster",         # Caching layer
    "postgresql-primary",    # Primary database
    "elasticsearch-cluster", # Search and indexing
    "api-gateway",          # API routing
    "nginx-ingress",        # Load balancing
    "payment-gateway",      # External payment
    "notification-service"  # External notifications
]

# 20+ dependency relationships
relationships = [
    ("api-gateway", "connector-service", "routes_to"),
    ("connector-service", "identity-service", "depends_on"),
    ("identity-service", "postgresql-primary", "stores_data"),
    # ... more relationships
]
```

### Running Tests

```bash
# Run graph scenario test
python -m ai_incident_manager.workflow.test_graph_scenario

# Run enhanced workflow test
python -m ai_incident_manager.workflow.test_incremental_graph_enhanced

# Run unit tests
pytest tests/test_graph_integration.py -v
```

## Key Benefits

1. **Proactive Failure Detection**: Identifies potential cascading failures before they occur
2. **Risk Quantification**: Provides numerical risk assessments and blast radius calculations
3. **Actionable Insights**: Generates specific recommendations for monitoring and mitigation
4. **Scalable Architecture**: Handles complex microservices topologies efficiently
5. **Intelligent Analysis**: Uses AI agents for context-aware failure analysis
6. **Workflow Integration**: Seamlessly integrates with existing incident management processes

## Future Enhancements

1. **Machine Learning Integration**: Train models on historical failure patterns
2. **Real-time Monitoring**: Continuous graph updates based on telemetry data
3. **Simulation Capabilities**: What-if analysis for different failure scenarios
4. **Visual Dashboard**: Interactive graph visualization for operators
5. **Multi-cluster Support**: Cross-cluster dependency analysis
6. **Temporal Analysis**: Time-series analysis of cascading failure patterns

## Conclusion

The graph-based cascading failure detection system provides a powerful foundation for proactive incident management. By modeling entity relationships and simulating failure propagation, it enables teams to understand the potential impact of failures and take preventive action before cascading failures occur.

The implementation successfully balances sophistication with practicality, providing actionable insights while maintaining performance and scalability for production environments.