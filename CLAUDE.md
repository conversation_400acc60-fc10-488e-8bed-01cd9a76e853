# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Package Management
- Package manager: `rye` (not pip) - Python dependency management tool
- Add dependency: `rye add <package>`
- Install dependencies: `rye sync`
- Build: `rye build`
- Run test: `pytest tests/test_file.py::TestClass::test_method -v`
- Run all tests: `pytest`
- Format code: `black .`
- Sort imports: `isort .`
- Lint: `flake8`

## Code Style Guidelines
- **Formatting**: black with line length 100
- **Typing**: Use strict typing annotations (Dict, List, Optional, Union)
- **Imports**: Organized with isort (black profile)
- **Classes**: PascalCase, descriptive nouns
- **Functions/Variables**: snake_case, descriptive verbs for functions
- **Docstrings**: Required for all modules, classes, functions with Args/Returns
- **Error Handling**: Use specific exceptions, handle explicitly
- **Models**: Use Pydantic for data validation and structured schemas

## Project Architecture

This is an AI-powered incident management system using **LangGraph workflows** and **Pydantic AI Agents** to orchestrate comprehensive alert analysis, root cause analysis (RCA), remediation, and structured record-keeping.

### Core Components

1. **LangGraph Workflow Engine** (`ai_incident_manager/workflow/flow.py`)
   - Orchestrates incident analysis through state-based transitions
   - Modular nodes for each analysis step: alert analysis, metrics collection, logs analysis, system checks, root cause analysis, remediation
   - Uses MemorySaver for maintaining state across workflow steps

2. **Pydantic AI Agents** (`ai_incident_manager/agents/`)
   - Specialized agents for different tasks using structured prompts
   - `incident_agents.py`: Core agents (alert_analyzer, metrics_analyzer, logs_analyzer, system_checks_agent, root_cause_analyzer, remediation_advisor)
   - Each agent has typed dependencies and uses structured data models

3. **Data Models** (`ai_incident_manager/models/`)
   - `incident.py`: Main incident data structures using Pydantic
   - `workflow_state.py`: State management for LangGraph workflows
   - Strict typing and validation for all data flowing through the system

4. **Service Integration Layer** (`ai_incident_manager/services/`)
   - `metrics_collector.py`: New Relic metrics collection
   - `*_service.py` files: Integration with external services (ADO, Teams, MongoDB, etc.)
   - Abstracted clients for external API interactions

5. **Configuration-Driven Design**
   - `ai_incident_manager/config/`: YAML-based configuration for alert categories, entity types, runbooks
   - Runbook Manager for matching alerts with predefined response procedures
   - Configurable mappings for alerts lacking direct entity associations

### Workflow Architecture

The system follows a **state machine pattern** with these phases:
1. Alert ingestion (Azure Service Bus) → Alert parsing
2. Entity analysis and relationship mapping
3. Metrics and logs collection from New Relic
4. System health checks execution
5. Root cause analysis using AI agents
6. Remediation recommendations
7. Reporting (Azure DevOps tickets, Teams notifications)

### Key Design Patterns

- **Agent-Based Pipeline**: Each analysis step handled by specialized AI agents
- **State-Based Transitions**: LangGraph manages workflow state and conditional routing
- **Modular Architecture**: Clear separation between data collection, analysis, and reporting
- **Configuration-Driven**: Extensible through YAML configuration files
- **Multi-Region Support**: Handles US and EU environments with proper context

### Development Workflow

- Main entry point: `ai_incident_manager/main.py` (FastAPI application)
- Workflow testing: `ai_incident_manager/workflow/test_incremental_graph_refactored.py`
- Docker support with `docker-compose.yml` for PostgreSQL
- Environment variables configured via `.env` file

## Important Implementation Notes

### New Relic Integration
- When sending NRQL queries using NerdGraph, **always use `since` and `until` time parameters**
- Never use hours timewindow as parameters - calculations are based on exact alert window timing
- All methods/functions handling NRQL should expect exact since/until timestamps

### Data Flow
- Raw alerts → Structured incident data → AI analysis → Actionable insights
- State preservation through LangGraph checkpointing
- MongoDB for enhanced data storage and retrieval

### Extensibility Points
- Add new alert sources: Implement parsers in `agents/alert_parser_agent.py`
- Add entity types: Update `config/entity_types.yaml`
- Add runbooks: Update `config/runbooks.yaml`
- Add integrations: Create new services in `services/` directory

## Environment Setup

Required environment variables:
- New Relic: `NEWRELIC_API_KEY`, `NEWRELIC_ACCOUNT_ID`
- Azure OpenAI: `AZURE_OPENAI_ENDPOINT`, `AZURE_OPENAI_API_KEY`, `AZURE_OPENAI_DEPLOYMENT`
- Azure DevOps: `ADO_PERSONAL_ACCESS_TOKEN`, `ADO_ORGANIZATION`, `ADO_PROJECT`
- Teams: `TEAMS_WEBHOOK_URL`
- Optional MongoDB: `MONGODB_CONNECTION_STRING`

## Testing and Development

- Run workflow test: `python -m ai_incident_manager.workflow.test_incremental_graph_refactored`
- Start API server: `python -m ai_incident_manager.main`
- Docker development: `docker-compose up postgres` for database
- Tests located in `tests/` directory with comprehensive integration tests

## Modular Workflow Structure

The workflow has been modularized for maintainability:
- `workflow/nodes/`: Individual step implementations
- `workflow/utils/`: Shared utilities (decorators, serialization, MongoDB operations)
- `workflow/config.py`: Centralized configuration and client initialization
- Migration from monolithic to modular design documented in `workflow/README.md`