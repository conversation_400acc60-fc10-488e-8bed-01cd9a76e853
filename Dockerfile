FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install rye
RUN curl -sSf https://rye-up.com/get | RYE_INSTALL_OPTION="--yes" bash
ENV PATH="/root/.rye/shims:${PATH}"

# Copy pyproject.toml and other rye config files
COPY pyproject.toml .
COPY README.md .
COPY .python-version .
COPY rye.toml .

# Copy application code
COPY ai_incident_manager/ ai_incident_manager/
COPY lib/ lib/ 
COPY utils/ utils/

# Install dependencies
RUN rye sync --no-dev

# Set environment variables
ENV PYTHONPATH=/app
ENV NEWRELIC_API_KEY=""
ENV NEWRELIC_ACCOUNT_ID=""
ENV AZURE_OPENAI_ENDPOINT=""
ENV AZURE_OPENAI_API_KEY=""
ENV AZURE_OPENAI_DEPLOYMENT="gpt-4o"
ENV AZURE_OPENAI_API_VERSION="2023-05-15"
ENV ADO_PERSONAL_ACCESS_TOKEN=""
ENV ADO_ORGANIZATION="Ivanti"
ENV ADO_PROJECT="AI Automation and Observability"
ENV ADO_API_VERSION="7.0"
ENV ADO_AREA_PATH="AI Automation and Observability"
ENV TEAMS_WEBHOOK_URL=""
ENV MONGODB_CONNECTION_STRING=""

# Set the default command
CMD ["python", "-m", "ai_incident_manager.workflow.test_incremental_graph"] 