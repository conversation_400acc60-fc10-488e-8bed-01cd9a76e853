from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from azure.identity import DefaultAzureCredential
from azure.monitor.query import MetricsQueryClient, TimeSeriesElement, MetricAggregationType

class AzureAppInsights:
    """
    A class to interact with Azure Application Insights API for fetching various application metrics.
    This class provides methods to monitor application performance, errors, availability, and other key metrics.
    """

    def __init__(self, resource_id: str):
        """
        Initialize the Azure Application Insights client.
        
        Args:
            resource_id (str): The resource ID of your Application Insights resource
        """
        self.resource_id = resource_id
        self.credential = DefaultAzureCredential()
        self.client = MetricsQueryClient(credential=self.credential)

    def get_metric(
        self,
        metric_name: str,
        aggregation: str,
        timespan: Optional[Tuple[datetime, datetime]] = None,
        filter: Optional[str] = None,
    ) -> List[TimeSeriesElement]:
        """
        Generic method to fetch any metric from Application Insights.
        
        Args:
            metric_name (str): Name of the metric to fetch
            aggregation (str): Type of aggregation (sum, avg, count, min, max)
            timespan (Tuple[datetime, datetime], optional): Time interval for the query as (start_time, end_time)
            filter (str, optional): Filter to apply on the metric
            
        Returns:
            List[TimeSeriesElement]: List of time series data points
        """
        if timespan is None:
            # Default to last 24 hours
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=24)
            timespan = (start_time, end_time)

        response = self.client.query_resource(
            self.resource_id,
            metric_names=[metric_name],
            timespan=timespan,
            aggregations=[aggregation],
            filter=filter
        )
        return response.metrics

    def get_request_performance(
        self,
        timespan: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, float]:
        """
        Get application request performance metrics including response time and request count.
        
        Returns:
            Dict[str, float]: Dictionary containing average response time and request count
        """
        response_time = self.get_metric(
            "requests/duration",
            "avg",
            timespan
        )
        request_count = self.get_metric(
            "requests/count",
            "sum",
            timespan
        )

        # Safely extract values with null checks
        avg_response_time = 0.0
        if response_time and response_time[0].data and response_time[0].data[0].average is not None:
            avg_response_time = float(response_time[0].data[0].average)

        req_count = 0.0
        if request_count and request_count[0].data and request_count[0].data[0].total is not None:
            req_count = float(request_count[0].data[0].total)

        return {
            "avg_response_time": avg_response_time,
            "request_count": req_count
        }

    def get_failure_metrics(
        self,
        timespan: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, float]:
        """
        Get application failure metrics including failed requests and exceptions.
        
        Returns:
            Dict[str, float]: Dictionary containing failure rate and exception count
        """
        failed_requests = self.get_metric(
            "requests/failed",
            "sum",
            timespan
        )
        exceptions = self.get_metric(
            "exceptions/count",
            "sum",
            timespan
        )

        # Safely extract values with null checks
        failed_req = 0.0
        if failed_requests and failed_requests[0].data and failed_requests[0].data[0].total is not None:
            failed_req = float(failed_requests[0].data[0].total)

        exception_count = 0.0
        if exceptions and exceptions[0].data and exceptions[0].data[0].total is not None:
            exception_count = float(exceptions[0].data[0].total)

        return {
            "failed_requests": failed_req,
            "exceptions": exception_count
        }

    def get_dependency_metrics(
        self,
        timespan: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, float]:
        """
        Get dependency call metrics including latency and success rate.
        
        Returns:
            Dict[str, float]: Dictionary containing dependency metrics
        """
        dependency_duration = self.get_metric(
            "dependencies/duration",
            "avg",
            timespan
        )
        dependency_failures = self.get_metric(
            "dependencies/failed",
            "sum",
            timespan
        )

        # Safely extract values with null checks
        avg_duration = 0.0
        if dependency_duration and dependency_duration[0].data and dependency_duration[0].data[0].average is not None:
            avg_duration = float(dependency_duration[0].data[0].average)

        failures = 0.0
        if dependency_failures and dependency_failures[0].data and dependency_failures[0].data[0].total is not None:
            failures = float(dependency_failures[0].data[0].total)

        return {
            "avg_dependency_duration": avg_duration,
            "failed_dependencies": failures
        }

    def get_performance_metrics(
        self,
        timespan: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, float]:
        """
        Get server-side performance metrics including CPU and memory usage.
        
        Returns:
            Dict[str, float]: Dictionary containing performance metrics
        """
        cpu_usage = self.get_metric(
            "performanceCounters/processorTimePercentage",
            "avg",
            timespan
        )
        memory_usage = self.get_metric(
            "performanceCounters/memoryAvailableBytes",
            "avg",
            timespan
        )

        # Safely extract values with null checks
        cpu = 0.0
        if cpu_usage and cpu_usage[0].data and cpu_usage[0].data[0].average is not None:
            cpu = float(cpu_usage[0].data[0].average)

        memory = 0.0
        if memory_usage and memory_usage[0].data and memory_usage[0].data[0].average is not None:
            memory = float(memory_usage[0].data[0].average)

        return {
            "cpu_usage": cpu,
            "available_memory": memory
        }

    def get_availability_metrics(
        self,
        timespan: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, float]:
        """
        Get application availability metrics including availability tests results.
        
        Returns:
            Dict[str, float]: Dictionary containing availability metrics
        """
        availability = self.get_metric(
            "availabilityResults/availabilityPercentage",
            "avg",
            timespan
        )
        response_time = self.get_metric(
            "availabilityResults/duration",
            "avg",
            timespan
        )

        # Safely extract values with null checks
        avail_pct = 0.0
        if availability and availability[0].data and availability[0].data[0].average is not None:
            avail_pct = float(availability[0].data[0].average)

        resp_time = 0.0
        if response_time and response_time[0].data and response_time[0].data[0].average is not None:
            resp_time = float(response_time[0].data[0].average)

        return {
            "availability_percentage": avail_pct,
            "availability_response_time": resp_time
        }

    def get_user_metrics(
        self,
        timespan: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, float]:
        """
        Get user-related metrics including user sessions and page views.
        
        Returns:
            Dict[str, float]: Dictionary containing user metrics
        """
        users = self.get_metric(
            "users/count",
            "sum",
            timespan
        )
        page_views = self.get_metric(
            "pageViews/count",
            "sum",
            timespan
        )

        # Safely extract values with null checks
        user_count = 0.0
        if users and users[0].data and users[0].data[0].total is not None:
            user_count = float(users[0].data[0].total)

        views = 0.0
        if page_views and page_views[0].data and page_views[0].data[0].total is not None:
            views = float(page_views[0].data[0].total)

        return {
            "user_count": user_count,
            "page_views": views
        }