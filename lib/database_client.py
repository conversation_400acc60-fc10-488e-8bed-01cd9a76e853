"""
PostgreSQL Database Client for Context Retrieval

This module provides a client interface for retrieving additional context
from a PostgreSQL database to assist with incident analysis.
"""

import os
import yaml
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from typing import Dict, List, Any, Optional, Union
import asyncio
from datetime import datetime
import dotenv
from loguru import logger
from contextlib import contextmanager
import functools

# Load environment variables
dotenv.load_dotenv()

# Configure logger
logger = logger.bind(name="database_client")

class DatabaseClient:
    """Client for retrieving context data from PostgreSQL database."""
    
    def __init__(self, config_path: str = None, host: str = None, port: str = None, 
                 dbname: str = None, user: str = None, password: str = None, 
                 sslmode: str = None):
        """
        Initialize the database client with connection parameters.
        
        Args:
            config_path: Path to the YAML configuration file containing SQL queries
            host: Database host (defaults to POSTGRES_HOST environment variable)
            port: Database port (defaults to POSTGRES_PORT environment variable)
            dbname: Database name (defaults to POSTGRES_DB environment variable)
            user: Database user (defaults to POSTGRES_USER environment variable)
            password: Database password (defaults to POSTGRES_PASSWORD environment variable)
            sslmode: SSL mode (defaults to POSTGRES_SSL_MODE environment variable)
        """
        # Set connection parameters, prioritizing passed arguments over environment variables
        self.connection_params = {
            'host': host or os.getenv('POSTGRES_HOST', 'localhost'),
            'port': port or os.getenv('POSTGRES_PORT', '5432'),
            'dbname': dbname or os.getenv('POSTGRES_DB', 'incident_management'),
            'user': user or os.getenv('POSTGRES_USER', 'postgres'),
            'password': password or os.getenv('POSTGRES_PASSWORD', ''),
            'sslmode': sslmode or os.getenv('POSTGRES_SSL_MODE', 'require')
        }
        
        # Load query templates from YAML file
        self.config_path = config_path or os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
            'config', 
            'database_queries.yaml'
        )
        self.queries = self._load_queries()
        
        # Test connection on initialization
        try:
            with self.get_connection():
                logger.info("Successfully connected to PostgreSQL database")
        except Exception as e:
            logger.error(f"Failed to connect to PostgreSQL database: {str(e)}")
    
    def _load_queries(self) -> Dict:
        """Load SQL query templates from YAML configuration file."""
        try:
            with open(self.config_path, 'r') as f:
                queries = yaml.safe_load(f)
            logger.info(f"Loaded query templates from {self.config_path}")
            return queries
        except Exception as e:
            logger.error(f"Failed to load query templates: {str(e)}")
            return {}
    
    @contextmanager
    def get_connection(self):
        """Create and yield a database connection within a context manager."""
        conn = None
        try:
            conn = psycopg2.connect(**self.connection_params)
            yield conn
        except Exception as e:
            logger.error(f"Database connection error: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, query: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Execute a SQL query and return results as a list of dictionaries.
        
        Args:
            query: SQL query to execute
            params: Parameters to substitute into the query
            
        Returns:
            List of dictionaries representing result rows
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                    cur.execute(query, params or {})
                    results = cur.fetchall()
                    return [dict(row) for row in results]
        except Exception as e:
            logger.error(f"Query execution error: {str(e)}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            return []
    
    async def execute_query_async(self, query: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Execute a SQL query asynchronously.
        
        Args:
            query: SQL query to execute
            params: Parameters to substitute into the query
            
        Returns:
            List of dictionaries representing result rows
        """
        return await asyncio.to_thread(self.execute_query, query, params)
    
    def get_query_template(self, product: str, category: str, query_name: str) -> Optional[str]:
        """
        Retrieve a query template from the loaded configuration.
        
        Args:
            product: Product name (e.g., 'mdm', 'neurons', 'general')
            category: Category name (e.g., 'general_context', 'rca')
            query_name: Name of the specific query
            
        Returns:
            Query template string or None if not found
        """
        try:
            return self.queries.get(product, {}).get(category, {}).get(query_name)
        except (KeyError, AttributeError):
            logger.error(f"Query template not found: {product}.{category}.{query_name}")
            return None
    
    def format_query(self, query_template: str, **kwargs) -> str:
        """
        Format a query template with the provided parameters.
        
        Args:
            query_template: SQL query template with placeholders
            **kwargs: Parameters to substitute into the query
            
        Returns:
            Formatted query string
        """
        return query_template.format(**kwargs)
    
    def get_context_data(self, product: str, context_type: str, 
                         query_params: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get context data for an incident based on product and context type.
        
        Args:
            product: Product name (e.g., 'mdm', 'neurons', 'general')
            context_type: Type of context to retrieve (e.g., 'general_context', 'rca')
            query_params: Parameters to substitute into the query templates
            
        Returns:
            Dictionary of query names and their results
        """
        results = {}
        
        if product not in self.queries:
            logger.warning(f"No queries defined for product: {product}")
            return results
        
        if context_type not in self.queries[product]:
            logger.warning(f"No {context_type} queries defined for product: {product}")
            return results
        
        # Execute all queries for the given product and context type
        for query_name, query_template in self.queries[product][context_type].items():
            try:
                # Format query with parameters
                formatted_query = self.format_query(query_template, **query_params)
                
                # Execute query
                query_results = self.execute_query(formatted_query)
                
                # Add results to output dictionary
                results[query_name] = query_results
                
                logger.info(f"Successfully executed {product}.{context_type}.{query_name}")
            except KeyError as e:
                # Missing parameter for query formatting
                logger.warning(f"Missing parameter for query {query_name}: {str(e)}")
            except Exception as e:
                # Other query execution errors
                logger.error(f"Error executing {query_name}: {str(e)}")
        
        return results
    
    async def get_context_data_async(self, product: str, context_type: str, 
                                     query_params: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get context data asynchronously for an incident based on product and context type.
        
        Args:
            product: Product name (e.g., 'mdm', 'neurons', 'general')
            context_type: Type of context to retrieve (e.g., 'general_context', 'rca')
            query_params: Parameters to substitute into the query templates
            
        Returns:
            Dictionary of query names and their results
        """
        return await asyncio.to_thread(self.get_context_data, product, context_type, query_params)
    
    def get_general_context(self, product: str, query_params: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get general context data applicable to all products.
        
        Args:
            product: Product name to include in query parameters
            query_params: Parameters to substitute into the query templates
            
        Returns:
            Dictionary of query names and their results
        """
        # Make sure product is included in query parameters
        params = query_params.copy()
        params['product'] = product
        
        return self.get_context_data('general', 'general_context', params)
    
    async def get_general_context_async(self, product: str, 
                                        query_params: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get general context data asynchronously.
        
        Args:
            product: Product name to include in query parameters
            query_params: Parameters to substitute into the query templates
            
        Returns:
            Dictionary of query names and their results
        """
        return await asyncio.to_thread(self.get_general_context, product, query_params)
    
    def get_product_context(self, product: str, query_params: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get product-specific context data.
        
        Args:
            product: Product name (e.g., 'mdm', 'neurons')
            query_params: Parameters to substitute into the query templates
            
        Returns:
            Dictionary of query names and their results
        """
        return self.get_context_data(product, 'general_context', query_params)
    
    async def get_product_context_async(self, product: str, 
                                        query_params: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get product-specific context data asynchronously.
        
        Args:
            product: Product name (e.g., 'mdm', 'neurons')
            query_params: Parameters to substitute into the query templates
            
        Returns:
            Dictionary of query names and their results
        """
        return await asyncio.to_thread(self.get_product_context, product, query_params)
    
    def get_rca_context(self, product: str, query_params: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get RCA-specific context data.
        
        Args:
            product: Product name (e.g., 'mdm', 'neurons')
            query_params: Parameters to substitute into the query templates
            
        Returns:
            Dictionary of query names and their results
        """
        return self.get_context_data(product, 'rca', query_params)
    
    async def get_rca_context_async(self, product: str, 
                                    query_params: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get RCA-specific context data asynchronously.
        
        Args:
            product: Product name (e.g., 'mdm', 'neurons')
            query_params: Parameters to substitute into the query templates
            
        Returns:
            Dictionary of query names and their results
        """
        return await asyncio.to_thread(self.get_rca_context, product, query_params)
    
    def get_comprehensive_context(self, product: str, 
                                  query_params: Dict[str, Any]) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """
        Get comprehensive context data including general, product-specific, and RCA contexts.
        
        Args:
            product: Product name (e.g., 'mdm', 'neurons')
            query_params: Parameters to substitute into the query templates
            
        Returns:
            Nested dictionary with context types, query names, and results
        """
        results = {
            'general': self.get_general_context(product, query_params),
            'product_specific': self.get_product_context(product, query_params),
            'rca': self.get_rca_context(product, query_params)
        }
        return results
    
    async def get_comprehensive_context_async(self, product: str, 
                                             query_params: Dict[str, Any]) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """
        Get comprehensive context data asynchronously.
        
        Args:
            product: Product name (e.g., 'mdm', 'neurons')
            query_params: Parameters to substitute into the query templates
            
        Returns:
            Nested dictionary with context types, query names, and results
        """
        return await asyncio.to_thread(self.get_comprehensive_context, product, query_params)


# Initialize a singleton instance
_instance = None

def get_database_client() -> DatabaseClient:
    """Get or create the singleton DatabaseClient instance."""
    global _instance
    if _instance is None:
        _instance = DatabaseClient()
    return _instance


# Example usage
async def main():
    """Test function for the database client."""
    # Initialize the client
    db_client = get_database_client()
    
    # Example query parameters
    query_params = {
        'since_time': '2023-01-01 00:00:00',
        'until_time': '2023-01-02 00:00:00',
        'application_name': 'test-app',
        'landscape': 'na1',
        'alert_title': 'High CPU usage',
        'alert_keyword': 'cpu'
    }
    
    # Get context data for MDM product
    mdm_context = await db_client.get_product_context_async('mdm', query_params)
    print("MDM Context:")
    print(mdm_context)
    
    # Get RCA context data for MDM product
    mdm_rca = await db_client.get_rca_context_async('mdm', query_params)
    print("MDM RCA Context:")
    print(mdm_rca)
    
    # Get comprehensive context
    comprehensive = await db_client.get_comprehensive_context_async('mdm', query_params)
    print("Comprehensive Context:")
    print(comprehensive)


if __name__ == "__main__":
    asyncio.run(main()) 