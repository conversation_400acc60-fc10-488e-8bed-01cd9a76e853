import json
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Tuple
import requests
from requests.exceptions import RequestException
from datetime import datetime, timedelta, timezone


class Region(Enum):
    """New Relic regions for NerdGraph API"""
    US = "https://api.newrelic.com/graphql"
    EU = "https://api.eu.newrelic.com/graphql"


class AlertStatus(Enum):
    """Alert status values"""
    FIRING = "open"
    RESOLVED = "closed"
    ACKNOWLEDGED = "acknowledged"


class AlertSeverity(Enum):
    """Alert severity values"""
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"


@dataclass
class GraphQLResponse:
    """Structured response from NerdGraph API"""
    data: Optional[Dict[str, Any]] = None
    errors: Optional[List[Dict[str, Any]]] = None


@dataclass
class TimeseriesDataPoint:
    """Represents a single timeseries data point"""
    timestamp: datetime
    value: float
    

@dataclass
class MetricResult:
    """Represents metric query results"""
    name: str
    timeseries: List[TimeseriesDataPoint]
    metadata: Dict[str, Any]


@dataclass
class Alert:
    """Represents a New Relic alert/issue"""
    id: str
    name: str
    status: AlertStatus
    severity: AlertSeverity
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    closed_by: Optional[str] = None
    policy_name: Optional[str] = None
    condition_name: Optional[str] = None
    entity_guids: Optional[List[str]] = None
    deep_link_url: Optional[str] = None
    total_incidents: Optional[int] = None


class NewRelicGraphQLError(Exception):
    """Custom exception for New Relic GraphQL errors"""
    pass


class NewRelicGraphQLClient:
    """
    Client for interacting with New Relic's NerdGraph GraphQL API.
    Supports both US and EU endpoints.
    """
    
    def __init__(
        self, 
        api_key: str, 
        region: Region = Region.US, 
        account_id: Optional[str] = None, 
        timeout: int = 30
    ):
        """
        Args:
            api_key (str): New Relic personal API key.
            region (Region): Target region (US or EU). Defaults to US.
            account_id (str, optional): Default account ID for queries.
            timeout (int): HTTP request timeout in seconds. Defaults to 30.
        """
        self.api_key = api_key
        self.region = region
        self.account_id = account_id
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'API-Key': self.api_key
        })
        
    def query(self, query: str, variables: Optional[Dict] = None) -> GraphQLResponse:
        """
        Execute a GraphQL query against New Relic's NerdGraph API.
        
        Args:
            query (str): The GraphQL query string.
            variables (dict, optional): Variables for the GraphQL query.
        
        Returns:
            GraphQLResponse: The parsed GraphQL response.
        
        Raises:
            NewRelicGraphQLError: If the request fails or errors are returned.
        """
        payload = {
            'query': query,
            'variables': variables or {}
        }
        
        try:
            response = self.session.post(
                self.region.value,
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            graphql_response = GraphQLResponse(
                data=data.get('data'),
                errors=data.get('errors')
            )
            
            if graphql_response.errors:
                raise NewRelicGraphQLError(
                    f"GraphQL query failed: {json.dumps(graphql_response.errors, indent=2)}"
                )
                
            return graphql_response
            
        except RequestException as e:
            raise NewRelicGraphQLError(f"Failed to execute query: {str(e)}") from e

    def get_metric_timeseries(
        self,
        metric_name: str,
        account_id: Optional[str] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        where: Optional[str] = None,
        facet: Optional[Union[str, List[str]]] = None,
        timeseries_period: str = "1 minute"
    ) -> List[MetricResult]:
        """
        Fetch timeseries metric data using NRQL.

        Args:
            metric_name: Name of the metric to query.
            account_id: Account ID (overrides default if set).
            since: Start time for the query (defaults to 30 minutes ago).
            until: End time for the query (defaults to now).
            where: Optional NRQL WHERE clause.
            facet: Optional facet clause (string or list of strings).
            timeseries_period: Time bucket for the timeseries (default: 1 minute).

        Returns:
            List[MetricResult]: The parsed timeseries results.
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError(
                "account_id must be provided either at client initialization or method call."
            )

        # Default time range if not provided
        if since is None:
            since = datetime.now(timezone.utc) - timedelta(minutes=30)
        if until is None:
            until = datetime.now(timezone.utc)

        # Format dates in YYYY-MM-DD HH:MM:SS format as required by New Relic
        since_str = since.strftime('%Y-%m-%d %H:%M:%S')
        until_str = until.strftime('%Y-%m-%d %H:%M:%S')

        # Build the NRQL query
        query_parts = [
            f"SELECT latest({metric_name})",
            "FROM Metric",
            f"SINCE '{since_str}'",
            f"UNTIL '{until_str}'",
            f"TIMESERIES {timeseries_period}"
        ]

        if where:
            query_parts.insert(2, f"WHERE {where}")

        if facet:
            if isinstance(facet, list):
                facet_clause = ", ".join(facet)
            else:
                facet_clause = facet
            query_parts.insert(-1, f"FACET {facet_clause}")

        nrql = " ".join(query_parts)

        # Construct GraphQL query
        gql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.query(gql_query, variables)
        results = response.data["actor"]["account"]["nrql"]["results"]
        metadata = response.data["actor"]["account"]["nrql"]["metadata"]

        metric_results = []
        for result in results:
            timeseries_data = []
            # The results for timeseries queries often use epoch timestamps as keys,
            # e.g. {"*************": 12.3, "facet": "something"}, so we parse them accordingly.
            for timestamp_key, value in result.items():
                if timestamp_key == "facet":
                    continue
                try:
                    timestamp = datetime.fromtimestamp(int(timestamp_key) / 1000, tz=timezone.utc)
                    timeseries_data.append(
                        TimeseriesDataPoint(timestamp=timestamp, value=float(value))
                    )
                except ValueError:
                    # Not a numeric timestamp key; ignore
                    continue

            metric_results.append(
                MetricResult(
                    name=metric_name,
                    timeseries=timeseries_data,
                    metadata={
                        "facet": result.get("facet"),
                        **metadata
                    }
                )
            )

        return metric_results

    def fetch_pod_details(
        self, 
        pod_name: str, 
        start_time: Optional[datetime] = None, 
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Fetch pod details from New Relic K8sPodSample.

        Args:
            pod_name: Name of the pod to query.
            start_time: Start time for the query (defaults to 30 minutes ago).
            end_time: End time for the query (defaults to now).

        Returns:
            dict: A dictionary containing pod details and metrics.
        """
        if start_time is None:
            start_time = datetime.now(timezone.utc) - timedelta(minutes=30)
        if end_time is None:
            end_time = datetime.now(timezone.utc)

        gql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            timeWindow {
                                start
                                end
                            }
                        }
                    }
                }
            }
        }
        """

        nrql = f"""
        SELECT *
        FROM K8sPodSample
        WHERE podName = '{pod_name}'
        SINCE '{start_time.isoformat()}'
        UNTIL '{end_time.isoformat()}'
        """

        variables = {
            "accountId": int(self.account_id),
            "nrql": nrql
        }

        response = self.query(gql_query, variables)
        return {
            "pod_details": response.data["actor"]["account"]["nrql"]["results"],
            "metadata": response.data["actor"]["account"]["nrql"]["metadata"]
        }

    def fetch_infrastructure_events(
        self,
        node_name: str,
        cluster_name: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        account_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Fetch infrastructure events for a specific node in a cluster.

        Example NRQL:
            SELECT * 
            FROM SystemSample 
            WHERE hostname = '<node_name>' AND clusterName = '<cluster_name>'
            SINCE ...
            UNTIL ...

        Args:
            node_name (str): Name of the node.
            cluster_name (str): Name of the cluster.
            start_time (datetime, optional): Start time for the query (defaults to 24 hours ago).
            end_time (datetime, optional): End time for the query (defaults to now).
            account_id (str, optional): Account ID (overrides default if set).

        Returns:
            List[Dict[str, Any]]: The raw event data.
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError(
                "account_id must be provided either at client initialization or method call."
            )

        if start_time is None:
            start_time = datetime.now(timezone.utc) - timedelta(days=1)
        if end_time is None:
            end_time = datetime.now(timezone.utc)

        # Format dates in YYYY-MM-DD HH:MM:SS format as required by New Relic
        start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')

        nrql = (
            f"SELECT * FROM SystemSample "
            f"WHERE hostname = '{node_name}' "
            f"AND clusterName = '{cluster_name}' "
            f"SINCE '{start_time_str}' "
            f"UNTIL '{end_time_str}'"
        )

        gql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.query(gql_query, variables)
        return response.data["actor"]["account"]["nrql"]["results"]

    def get_kubernetes_events(
        self,
        pod_name: str,
        cluster_name: Optional[str] = None,
        namespace: Optional[str] = None,
        account_id: Optional[int] = None,
        since: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        Fetch Kubernetes events for a specific pod.

        Args:
            pod_name (str): Name of the pod.
            cluster_name (str, optional): Optional cluster name filter.
            namespace (str, optional): Optional namespace filter.
            account_id (int, optional): Account ID (overrides default if set).
            since (datetime, optional): Start time for the query (defaults to 1 hour ago).

        Returns:
            List of Kubernetes events.
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError(
                "account_id must be provided either at client initialization or method call."
            )

        if since is None:
            since = datetime.now(timezone.utc) - timedelta(hours=1)

        where_conditions = [f"podName = '{pod_name}'"]
        if cluster_name:
            where_conditions.append(f"clusterName = '{cluster_name}'")
        if namespace:
            where_conditions.append(f"namespaceName = '{namespace}'")

        where_clause = " AND ".join(where_conditions)

        gql_query = """
        query($accountId: Int!, $since: String!, $where: String!) {
            actor {
                account(id: $accountId) {
                    nrql(query: "SELECT * FROM K8sEvent WHERE " + $where + " SINCE '" + $since + "'") {
                        results
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "since": since.isoformat(),
            "where": where_clause
        }

        # Execute the GraphQL query
        response = self.query(gql_query, variables)
        return response.data["actor"]["account"]["nrql"]["results"]

    def get_issues(
        self,
        account_id: Optional[int] = None,
        state: Optional[str] = None,
        priority: Optional[str] = None,
        entity_guid: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Fetch New Relic issues/incidents.

        Args:
            account_id: Account ID (overrides default if set).
            state: Optional filter by issue state (e.g., "ACTIVATED", "CLOSED").
            priority: Optional filter by priority.
            entity_guid: Optional filter by entity GUID.

        Returns:
            List[Dict[str, Any]]: List of issues/incidents (raw).
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError(
                "account_id must be provided either at client initialization or method call."
            )

        gql_query = """
        query($accountId: Int!, $state: [IssueState], $priority: [IssuePriority], $entityGuid: EntityGuid) {
            actor {
                account(id: $accountId) {
                    issues(filters: {
                        states: $state
                        priority: $priority
                        entityGuids: [$entityGuid]
                    }) {
                        issues {
                            title
                            state
                            priority
                            createdAt
                            entityGuids
                            description
                            alertPolicyNames
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "state": [state] if state else None,
            "priority": [priority] if priority else None,
            "entityGuid": entity_guid
        }

        response = self.query(gql_query, variables)
        return response.data["actor"]["account"]["issues"]["issues"]

    def get_entity_metrics(
        self,
        entity_guid: str,
        metrics: List[str],
        account_id: Optional[int] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        period: str = "1 minute"
    ) -> Dict[str, List[MetricResult]]:
        """
        Fetch multiple metrics for a specific entity.

        Args:
            entity_guid: Entity GUID to query.
            metrics: List of metric names to fetch.
            account_id: Account ID (overrides default if set).
            since: Start time for the query (defaults to 30 minutes ago).
            until: End time for the query (defaults to now).
            period: Time bucket for the timeseries.

        Returns:
            Dict[str, List[MetricResult]]: Dictionary mapping metric names to their timeseries data.
        """
        results = {}
        for metric in metrics:
            results[metric] = self.get_metric_timeseries(
                metric_name=metric,
                account_id=account_id,
                since=since,
                until=until,
                where=f"entity.guid = '{entity_guid}'",
                timeseries_period=period
            )
        return results

    def close(self):
        """Close the underlying session."""
        self.session.close()

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()

    # ---------------------
    # Internal Build Queries
    # ---------------------
    def _build_policy_search_query(self) -> Tuple[str, Dict]:
        query = """
        query($accountId: Int!) {
            actor {
                account(id: $accountId) {
                    alerts {
                        policiesSearch {
                            policies {
                                id
                            }
                        }
                    }
                }
            }
        }
        """
        variables = {"accountId": int(self.account_id)}
        return query, variables

    def _build_get_webhook_destination_query(self, name: str, url: str) -> Tuple[str, Dict]:
        query = """
        query($accountId: Int!, $name: String!, $url: String!) {
            actor {
                account(id: $accountId) {
                    aiNotifications {
                        destinations(filters: {
                            name: $name,
                            type: WEBHOOK,
                            property: { key: "url", value: $url }
                        }) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """
        variables = {
            "accountId": int(self.account_id),
            "name": name,
            "url": url
        }
        return query, variables

    def _build_create_webhook_destination_mutation(self, name: str, url: str) -> Tuple[str, Dict]:
        query = """
        mutation($accountId: Int!, $name: String!, $url: String!) {
            aiNotificationsCreateDestination(
                accountId: $accountId,
                destination: {
                    type: WEBHOOK,
                    name: $name,
                    properties: [{key: "url", value: $url}]
                }
            ) {
                destination {
                    id
                }
            }
        }
        """
        variables = {
            "accountId": int(self.account_id),
            "name": name,
            "url": url
        }
        return query, variables

    def _build_get_notification_channel_query(self, destination_id: str, name: str) -> Tuple[str, Dict]:
        query = """
        query($accountId: Int!, $destinationId: ID!, $name: String!) {
            actor {
                account(id: $accountId) {
                    aiNotifications {
                        channels(filters: {
                            destinationId: $destinationId,
                            name: $name
                        }) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """
        variables = {
            "accountId": int(self.account_id),
            "destinationId": destination_id,
            "name": name
        }
        return query, variables

    def _build_create_notification_channel_mutation(self, destination_id: str, name: str, api_key: str) -> Tuple[str, Dict]:
        query = """
        mutation($accountId: Int!, $destinationId: ID!, $name: String!, $apiKey: String!) {
            aiNotificationsCreateChannel(
                accountId: $accountId,
                channel: {
                    name: $name,
                    product: IINT,
                    type: WEBHOOK,
                    destinationId: $destinationId,
                    properties: [
                        {
                            key: "headers",
                            value: "{ \\"X-API-KEY\\":\\"$apiKey\\"}"
                        },
                        {
                            key: "payload",
                            value: "{\\"id\\": {{ json issueId }}, \\"issueUrl\\": {{ json issuePageUrl }}, \\"name\\": {{ json annotations.title.[0] }}, \\"severity\\": {{ json priority }}, \\"impactedEntities\\": {{ json entitiesData.names }}, \\"totalIncidents\\": {{ json totalIncidents }}, \\"status\\": {{ json state }}, \\"trigger\\": {{ json triggerEvent }}, \\"isCorrelated\\": {{ json isCorrelated }}, \\"createdAt\\": {{ createdAt }}, \\"updatedAt\\": {{ updatedAt }}, \\"lastReceived\\": {{ updatedAt }}, \\"source\\": {{ json accumulations.source }}, \\"alertPolicyNames\\": {{ json accumulations.policyName }}, \\"alertConditionNames\\": {{ json accumulations.conditionName }}, \\"workflowName\\": {{ json workflowName }}}"
                        }
                    ]
                }
            ) {
                channel {
                    id
                }
            }
        }
        """
        variables = {
            "accountId": int(self.account_id),
            "destinationId": destination_id,
            "name": name,
            "apiKey": api_key
        }
        return query, variables

    def _build_get_workflow_query(self, name: str, channel_id: str) -> Tuple[str, Dict]:
        query = """
        query($accountId: Int!, $name: String!, $channelId: ID!) {
            actor {
                account(id: $accountId) {
                    aiWorkflows {
                        workflows(
                            filters: {name: $name, channelId: $channelId}
                        ) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """
        variables = {
            "accountId": int(self.account_id),
            "name": name,
            "channelId": channel_id
        }
        return query, variables

    def _build_create_workflow_mutation(self, name: str, channel_id: str, policy_ids: List[str]) -> Tuple[str, Dict]:
        query = """
        mutation($accountId: Int!, $name: String!, $channelId: ID!, $policyIds: [String!]!) {
            aiWorkflowsCreateWorkflow(
                accountId: $accountId
                createWorkflowData: {
                    destinationConfigurations: {
                        channelId: $channelId,
                        notificationTriggers: [ACTIVATED, ACKNOWLEDGED, CLOSED, PRIORITY_CHANGED, OTHER_UPDATES]
                    },
                    issuesFilter: {
                        predicates: [
                            {
                                attribute: "labels.policyIds",
                                operator: EXACTLY_MATCHES,
                                values: $policyIds
                            }
                        ],
                        type: FILTER
                    },
                    workflowEnabled: true,
                    destinationsEnabled: true,
                    mutingRulesHandling: DONT_NOTIFY_FULLY_MUTED_ISSUES,
                    name: $name
                }
            ) {
                workflow {
                    id
                }
            }
        }
        """
        variables = {
            "accountId": int(self.account_id),
            "name": name,
            "channelId": channel_id,
            "policyIds": policy_ids
        }
        return query, variables

    # ---------------------
    # Higher-level API
    # ---------------------
    def _parse_alert(self, issue: Dict[str, Any]) -> Alert:
        """
        Helper to parse raw issue data into an Alert object.
        """
        def safe_timestamp(val: Optional[int]) -> Optional[datetime]:
            return datetime.fromtimestamp(val / 1000, tz=timezone.utc) if val else None

        # Some fields might be lists (title, description), handle gracefully
        def first_or_str(val: Any) -> str:
            if isinstance(val, list):
                return val[0] if val else ""
            return val if val else ""

        return Alert(
            id=str(issue.get("issueId", "")),
            name=first_or_str(issue.get("title")),
            status=AlertStatus(issue["state"].lower()) if "state" in issue else AlertStatus.FIRING,
            severity=AlertSeverity(issue["priority"].lower()) if "priority" in issue else AlertSeverity.INFO,
            description=first_or_str(issue.get("description")),
            created_at=safe_timestamp(issue.get("createdAt")),
            updated_at=safe_timestamp(issue.get("updatedAt")),
            closed_at=safe_timestamp(issue.get("closedAt")),
            acknowledged_at=safe_timestamp(issue.get("acknowledgedAt")),
            acknowledged_by=issue.get("acknowledgedBy"),
            closed_by=issue.get("closedBy"),
            policy_name=issue.get("policyName"),
            condition_name=issue.get("conditionName"),
            entity_guids=issue.get("entityGuids"),
            deep_link_url=issue.get("deepLinkUrl"),
            total_incidents=issue.get("totalIncidents")
        )

    def get_all_policy_ids(self) -> List[str]:
        """Retrieve all New Relic policy IDs."""
        query, variables = self._build_policy_search_query()
        response = self.query(query, variables)
        try:
            policies = response.data["actor"]["account"]["alerts"]["policiesSearch"]["policies"]
            return [policy["id"] for policy in policies]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to fetch policy IDs")

    def get_webhook_destination(self, name: str, url: str) -> Optional[str]:
        """
        Return the webhook destination ID if it exists, otherwise None.
        """
        query, variables = self._build_get_webhook_destination_query(name, url)
        response = self.query(query, variables)
        try:
            destinations = response.data["actor"]["account"]["aiNotifications"]["destinations"]["entities"]
            return destinations[0]["id"] if destinations else None
        except (KeyError, IndexError):
            return None

    def create_webhook_destination(self, name: str, url: str) -> str:
        """
        Create a new webhook destination and return its ID.
        """
        query, variables = self._build_create_webhook_destination_mutation(name, url)
        response = self.query(query, variables)
        try:
            return response.data["aiNotificationsCreateDestination"]["destination"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create webhook destination")

    def get_notification_channel(self, destination_id: str, name: str) -> Optional[str]:
        """
        Return the notification channel ID if it exists, otherwise None.
        """
        query, variables = self._build_get_notification_channel_query(destination_id, name)
        response = self.query(query, variables)
        try:
            channels = response.data["actor"]["account"]["aiNotifications"]["channels"]["entities"]
            return channels[0]["id"] if channels else None
        except (KeyError, IndexError):
            return None

    def create_notification_channel(self, destination_id: str, name: str, api_key: str) -> str:
        """
        Create a new notification channel tied to the given destination and return its ID.
        """
        query, variables = self._build_create_notification_channel_mutation(destination_id, name, api_key)
        response = self.query(query, variables)
        try:
            return response.data["aiNotificationsCreateChannel"]["channel"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create notification channel")

    def get_workflow(self, name: str, channel_id: str) -> Optional[str]:
        """
        Return the workflow ID if it exists, otherwise None.
        """
        query, variables = self._build_get_workflow_query(name, channel_id)
        response = self.query(query, variables)
        try:
            workflows = response.data["actor"]["account"]["aiWorkflows"]["workflows"]["entities"]
            return workflows[0]["id"] if workflows else None
        except (KeyError, IndexError):
            return None

    def create_workflow(self, name: str, channel_id: str, policy_ids: List[str]) -> str:
        """
        Create a new workflow linking specified policies to a notification channel.
        Return the new workflow ID.
        """
        query, variables = self._build_create_workflow_mutation(name, channel_id, policy_ids)
        response = self.query(query, variables)
        try:
            return response.data["aiWorkflowsCreateWorkflow"]["workflow"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create workflow")

    def get_alerts(self) -> List[Alert]:
        """
        Fetch a list of all active or recent AI Alerts from New Relic.
        
        Returns:
            List[Alert]: Parsed list of Alert objects.
        """
        gql_query = """
        query($accountId: Int!) {
            actor {
                account(id: $accountId) {
                    aiIssues {
                        issues {
                            issues {
                                account {
                                    id
                                    name
                                }
                                acknowledgedAt
                                acknowledgedBy
                                activatedAt
                                closedAt
                                closedBy
                                conditionName
                                createdAt
                                deepLinkUrl
                                description
                                entityGuids
                                entityNames
                                issueId
                                policyName
                                priority
                                state
                                title
                                totalIncidents
                                updatedAt
                            }
                        }
                    }
                }
            }
        }
        """
        variables = {"accountId": int(self.account_id)}
        response = self.query(gql_query, variables)

        alerts = []
        try:
            issues = response.data["actor"]["account"]["aiIssues"]["issues"]["issues"]
            for issue in issues:
                alerts.append(self._parse_alert(issue))
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to parse alerts: {str(e)}")
        return alerts

    def parse_generic_timeseries(
        self, 
        timeseries_data: Dict[str, Any], 
        metric_name: str
    ) -> List[MetricResult]:
        """
        Parse a generic timeseries response and convert it to a list of MetricResult objects.

        Args:
            timeseries_data (dict): The raw timeseries response from a NRQL query.
            metric_name (str): The default metric name if facets are not available.

        Returns:
            List[MetricResult]: The parsed timeseries results.
        """
        results: List[MetricResult] = []

        data_points = timeseries_data.get("results", [])
        if not data_points:
            return results

        # Group data points by facet
        facet_groups: Dict[str, List[Dict]] = {}
        for point in data_points:
            facet = point.get("facet")
            if facet:
                # Convert facet list to string key
                if isinstance(facet, list):
                    facet_key = " | ".join(str(f) for f in facet)
                else:
                    facet_key = str(facet)
            else:
                facet_key = metric_name
            
            if facet_key not in facet_groups:
                facet_groups[facet_key] = []
            facet_groups[facet_key].append(point)

        # Process each facet group
        for facet_key, points in facet_groups.items():
            timeseries: List[TimeseriesDataPoint] = []
            
            for point in points:
                if "beginTimeSeconds" not in point:
                    continue
                
                timestamp = datetime.fromtimestamp(point["beginTimeSeconds"], tz=timezone.utc)
                
                # Find the metric value - look for keys matching or containing the metric name.
                metric_value = None
                for key, value in point.items():
                    # Heuristic: if the key references the metric
                    if key.endswith(metric_name) or key.startswith("average.") or key.startswith("latest.") or key.startswith("max."):
                        metric_value = value
                        break
                
                if metric_value is None:
                    continue
                
                # Convert to float (treat None as 0.0)
                try:
                    val = float(metric_value)
                except (ValueError, TypeError):
                    val = 0.0
                
                timeseries.append(
                    TimeseriesDataPoint(timestamp=timestamp, value=val)
                )
            
            if timeseries:
                metadata = {
                    "facet": facet_key,
                    "total_points": len(timeseries)
                }
                # Sort timeseries by ascending timestamp
                timeseries.sort(key=lambda x: x.timestamp)
                results.append(
                    MetricResult(
                        name=facet_key,
                        timeseries=timeseries,
                        metadata=metadata
                    )
                )

        return results

    def get_evicted_pods_metrics(
        self,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        cluster_pattern: str = '%prd%',
        exclude_patterns: Optional[List[str]] = None,
        timeseries_period: str = "10 minutes"
    ) -> Dict[str, Any]:
        """
        Get metrics for evicted pods across clusters.

        Args:
            since: Start time for the query (defaults to 24 hours ago).
            until: End time for the query (defaults to now).
            cluster_pattern: Pattern to match cluster names (defaults to '%prd%').
            exclude_patterns: List of patterns to exclude from pod names.
            timeseries_period: Time bucket for the timeseries (default: 10 minutes).

        Returns:
            Dict[str, Any]: Raw results from New Relic NRQL query.
        """
        if since is None:
            since = datetime.now(timezone.utc) - timedelta(hours=24)
        if until is None:
            until = datetime.now(timezone.utc)
        if exclude_patterns is None:
            exclude_patterns = []

        # Format dates in YYYY-MM-DD HH:MM:SS format as required by New Relic
        since_str = since.strftime('%Y-%m-%d %H:%M:%S')
        until_str = until.strftime('%Y-%m-%d %H:%M:%S')

        # Build WHERE clause for exclusions
        exclusion_clauses = [f"podName NOT LIKE '{pattern}'" for pattern in exclude_patterns]
        exclusion_str = " AND ".join(exclusion_clauses)
        if exclusion_str:
            exclusion_str = f" AND {exclusion_str}"

        nrql = f"""
        SELECT count(*) FROM K8sPodSample
        WHERE reason = 'Evicted'
        AND status = 'Failed'
        AND clusterName LIKE '{cluster_pattern}'
        {exclusion_str}
        FACET podName, clusterName
        TIMESERIES {timeseries_period}
        SINCE '{since_str}'
        UNTIL '{until_str}'
        """

        gql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(self.account_id),
            "nrql": nrql
        }

        response = self.query(gql_query, variables)
        try:
            return response.data["actor"]["account"]["nrql"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch evicted pods metrics: {str(e)}")

    def get_generic_timeseries_metrics(
        self,
        nrql_query: str,
        metric_name: str,
        account_id: Optional[int] = None
    ) -> List[MetricResult]:
        """
        Execute a NRQL query and parse its timeseries results.

        Args:
            nrql_query: The NRQL query to execute.
            metric_name: Default name for the metric if no facets are present.
            account_id: Optional account ID to override the default.

        Returns:
            List[MetricResult]: The parsed timeseries results.
        """
        gql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id or self.account_id),
            "nrql": nrql_query
        }

        response = self.query(gql_query, variables)
        try:
            results = response.data["actor"]["account"]["nrql"]
            # Uncomment for debugging:
            # print("Raw Results:")
            # print(json.dumps(results, indent=2))
            return self.parse_generic_timeseries(results, metric_name)
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch timeseries metrics: {str(e)}")


def format_metric_results(results: List[MetricResult]) -> None:
    """Helper function to format and print MetricResult data to stdout."""
    for result in results:
        print(f"\nMetric: {result.name}")
        print("=" * 50)
        print("Metadata:", json.dumps(result.metadata, indent=2))
        print("\nTimeseries Data:")
        for point in result.timeseries:
            print(f"  {point.timestamp.isoformat()}: {point.value}")


# Example usage when run as a script
if __name__ == "__main__":
    import os
    from datetime import datetime, timedelta, timezone

    # Get API key from environment variables (or set them manually)
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")

    if not api_key or not account_id:
        print("Error: NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID environment variables must be set.")
        exit(1)

    try:
        # Initialize the client
        client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)

        # Example 1: Get evicted pods metrics
        print("\n=== Example 1: Evicted Pods Metrics ===")
        since = datetime.now(timezone.utc) - timedelta(hours=24)
        evicted_pods = client.get_evicted_pods_metrics(
            since=since,
            cluster_pattern='%prd%',
            exclude_patterns=['%assetprocessor%', '%fru-prd%'],
            timeseries_period='30 minutes'
        )
        print("Raw Evicted Pods Results:")
        print(json.dumps(evicted_pods, indent=2))

        # Example 2: Generic timeseries metrics for CPU usage
        print("\n=== Example 2: Generic CPU Usage Metrics ===")
        cpu_query = """
        SELECT average(cpuUsedCores)
        FROM K8sContainerSample
        WHERE clusterName LIKE '%prd%'
        FACET containerName, clusterName
        TIMESERIES 30 minutes
        SINCE 24 hours ago
        LIMIT 5
        """
        cpu_metrics = client.get_generic_timeseries_metrics(nrql_query=cpu_query, metric_name="container_cpu_usage")
        format_metric_results(cpu_metrics)

        # Example 3: Memory usage metrics
        print("\n=== Example 3: Memory Usage Metrics ===")
        memory_query = """
        SELECT average(memoryUsedBytes)
        FROM K8sContainerSample
        WHERE clusterName LIKE '%prd%'
        FACET containerName, clusterName
        TIMESERIES 30 minutes
        SINCE 24 hours ago
        LIMIT 5
        """
        memory_metrics = client.get_generic_timeseries_metrics(nrql_query=memory_query, metric_name="container_memory_usage")
        format_metric_results(memory_metrics)

        # Example 4: Fetch Infrastructure Events
        print("\n=== Example 4: Fetch Infrastructure Events ===")
        infra_events = client.fetch_infrastructure_events(
            node_name="my-node",
            cluster_name="my-cluster"
        )
        print("Infrastructure Events:")
        print(json.dumps(infra_events, indent=2))

    except NewRelicGraphQLError as e:
        print(f"Error querying New Relic: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
    finally:
        client.close()