from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from enum import Enum
import json
import logging
from .new_relic import NewRelicGraphQLClient, MetricResult, TimeseriesDataPoint

logger = logging.getLogger(__name__)

class EntityType(Enum):
    """Types of entities that can be affected in an incident"""
    KUBERNETES_POD = "KUBERNETES_POD"
    KUBERNETES_DEPLOYMENT = "KUBERNETES_DEPLOYMENT"
    KUBERNETES_NODE = "KUBERNETES_NODE"
    KUBERNETES_CLUSTER = "KUBERNETES_CLUSTER"
    APPLICATION = "APPLICATION"
    DATABASE = "DATABASE"
    UNKNOWN = "UNKNOWN"

@dataclass
class InvestigationStep:
    """Represents a single step in the incident investigation"""
    step_number: int
    description: str
    timestamp: datetime
    findings: Dict[str, Any]
    artifacts: List[str] = field(default_factory=list)

@dataclass
class Issue:
    """
    Represents an incident/issue with methods to analyze and gather context
    from New Relic data sources.
    """
    id: str
    title: str
    description: str
    start_time: datetime
    end_time: Optional[datetime]
    severity: str
    status: str
    nr_client: NewRelicGraphQLClient
    account_id: int
    entity_guids: List[str] = field(default_factory=list)
    entity_types: List[EntityType] = field(default_factory=list)
    affected_clusters: List[str] = field(default_factory=list)
    affected_regions: List[str] = field(default_factory=list)
    affected_products: List[str] = field(default_factory=list)
    investigation_steps: List[InvestigationStep] = field(default_factory=list)
    metrics_data: Dict[str, List[MetricResult]] = field(default_factory=dict)
    logs_data: List[Dict[str, Any]] = field(default_factory=list)
    rca_summary: Optional[str] = None

    def __post_init__(self):
        """Initialize investigation by gathering basic entity information"""
        self._gather_entity_info()

    def _gather_entity_info(self) -> None:
        """Gather information about affected entities from New Relic"""
        try:
            # Query entity details using GraphQL
            query = """
            query($guids: [EntityGuid!]!) {
                actor {
                    entities(guids: $guids) {
                        guid
                        name
                        type
                        tags {
                            key
                            values
                        }
                    }
                }
            }
            """
            response = self.nr_client.execute(query, {"guids": self.entity_guids})
            
            if response.data and "actor" in response.data:
                entities = response.data["actor"]["entities"]
                for entity in entities:
                    # Determine entity type
                    entity_type = self._determine_entity_type(entity["type"])
                    if entity_type not in self.entity_types:
                        self.entity_types.append(entity_type)
                    
                    # Extract cluster, region, and product info from tags
                    for tag in entity["tags"]:
                        if tag["key"] == "clusterName" and tag["values"]:
                            cluster = tag["values"][0]
                            if cluster not in self.affected_clusters:
                                self.affected_clusters.append(cluster)
                        elif tag["key"] == "region" and tag["values"]:
                            region = tag["values"][0]
                            if region not in self.affected_regions:
                                self.affected_regions.append(region)
                        elif tag["key"] == "product" and tag["values"]:
                            product = tag["values"][0]
                            if product not in self.affected_products:
                                self.affected_products.append(product)

        except Exception as e:
            logger.error(f"Error gathering entity info: {str(e)}")

    def _determine_entity_type(self, nr_type: str) -> EntityType:
        """Map New Relic entity type to our EntityType enum"""
        type_mapping = {
            "KUBERNETES_POD": EntityType.KUBERNETES_POD,
            "KUBERNETES_DEPLOYMENT": EntityType.KUBERNETES_DEPLOYMENT,
            "KUBERNETES_NODE": EntityType.KUBERNETES_NODE,
            "KUBERNETES_CLUSTER": EntityType.KUBERNETES_CLUSTER,
            "APM_APPLICATION_ENTITY": EntityType.APPLICATION,
            "MYSQL_NODE_ENTITY": EntityType.DATABASE,
            "POSTGRES_NODE_ENTITY": EntityType.DATABASE
        }
        return type_mapping.get(nr_type, EntityType.UNKNOWN)

    def gather_metrics(self, metric_names: List[str], lookback_minutes: int = 60) -> None:
        """Gather relevant metrics for the affected entities"""
        try:
            since = self.start_time - timedelta(minutes=lookback_minutes)
            until = self.end_time or datetime.utcnow()

            for metric_name in metric_names:
                results = self.nr_client.get_metric_timeseries(
                    metric_name=metric_name,
                    account_id=self.account_id,
                    since=since,
                    until=until,
                    where=f"entity.guid IN ({','.join(self.entity_guids)})"
                )
                self.metrics_data[metric_name] = results

        except Exception as e:
            logger.error(f"Error gathering metrics: {str(e)}")

    def gather_logs(self, lookback_minutes: int = 60) -> None:
        """Gather relevant logs for the affected entities"""
        try:
            query = """
            query($accountId: Int!, $since: String!, $guids: String!) {
                actor {
                    account(id: $accountId) {
                        nrql(query: "SELECT * FROM Log WHERE entity.guid IN (" + $guids + ") SINCE '" + $since + "'") {
                            results
                        }
                    }
                }
            }
            """

            since = (self.start_time - timedelta(minutes=lookback_minutes)).isoformat() + "Z"
            variables = {
                "accountId": self.account_id,
                "since": since,
                "guids": ",".join(self.entity_guids)
            }

            response = self.nr_client.execute(query, variables)
            if response.data and "actor" in response.data:
                self.logs_data = response.data["actor"]["account"]["nrql"]["results"]

        except Exception as e:
            logger.error(f"Error gathering logs: {str(e)}")

    def gather_kubernetes_events(self) -> None:
        """Gather Kubernetes events if this is a Kubernetes-related incident"""
        if not any(et in [EntityType.KUBERNETES_POD, EntityType.KUBERNETES_DEPLOYMENT, 
                         EntityType.KUBERNETES_NODE, EntityType.KUBERNETES_CLUSTER] 
                  for et in self.entity_types):
            return

        try:
            # Get pod names from entity information
            pod_names = []
            query = """
            query($guids: [EntityGuid!]!) {
                actor {
                    entities(guids: $guids) {
                        ... on KubernetesPodEntity {
                            podName
                        }
                    }
                }
            }
            """
            response = self.nr_client.execute(query, {"guids": self.entity_guids})
            if response.data and "actor" in response.data:
                entities = response.data["actor"]["entities"]
                for entity in entities:
                    if "podName" in entity:
                        pod_names.append(entity["podName"])

            # Gather events for each pod
            for pod_name in pod_names:
                events = self.nr_client.get_kubernetes_events(
                    pod_name=pod_name,
                    cluster_name=self.affected_clusters[0] if self.affected_clusters else None,
                    account_id=self.account_id,
                    since=self.start_time
                )
                self._add_investigation_step(
                    description=f"Gathered Kubernetes events for pod {pod_name}",
                    findings={"events": events}
                )

        except Exception as e:
            logger.error(f"Error gathering Kubernetes events: {str(e)}")

    def _add_investigation_step(self, description: str, findings: Dict[str, Any]) -> None:
        """Add a new investigation step"""
        step = InvestigationStep(
            step_number=len(self.investigation_steps) + 1,
            description=description,
            timestamp=datetime.utcnow(),
            findings=findings
        )
        self.investigation_steps.append(step)

    def analyze_with_llm(self, llm_client: Any) -> None:
        """
        Analyze the incident using an LLM to generate RCA.
        The llm_client is expected to be an Azure OpenAI client.
        """
        # Prepare context for LLM
        context = {
            "incident": {
                "title": self.title,
                "description": self.description,
                "start_time": self.start_time.isoformat(),
                "end_time": self.end_time.isoformat() if self.end_time else None,
                "severity": self.severity,
                "status": self.status,
                "entity_types": [et.value for et in self.entity_types],
                "affected_clusters": self.affected_clusters,
                "affected_regions": self.affected_regions,
                "affected_products": self.affected_products
            },
            "investigation_steps": [
                {
                    "step_number": step.step_number,
                    "description": step.description,
                    "timestamp": step.timestamp.isoformat(),
                    "findings": step.findings
                }
                for step in self.investigation_steps
            ],
            "metrics_data": self.metrics_data,
            "logs_data": self.logs_data
        }

        # TODO: Implement the actual LLM call once we have the Azure OpenAI client
        # For now, we'll just store the context as a placeholder
        self._add_investigation_step(
            description="Analyzed incident data with LLM",
            findings={"analysis_context": context}
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert the issue to a dictionary for serialization"""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "severity": self.severity,
            "status": self.status,
            "entity_types": [et.value for et in self.entity_types],
            "affected_clusters": self.affected_clusters,
            "affected_regions": self.affected_regions,
            "affected_products": self.affected_products,
            "investigation_steps": [
                {
                    "step_number": step.step_number,
                    "description": step.description,
                    "timestamp": step.timestamp.isoformat(),
                    "findings": step.findings,
                    "artifacts": step.artifacts
                }
                for step in self.investigation_steps
            ],
            "metrics_data": self.metrics_data,
            "logs_data": self.logs_data,
            "rca_summary": self.rca_summary
        }
