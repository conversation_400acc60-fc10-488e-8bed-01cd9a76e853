"""
New Relic GraphQL client for log query operations.
"""

from typing import Any, Dict, List, Optional, Union, Set
from datetime import datetime, timedelta

from .base import UTC, GraphQLResponse, format_datetime
from .client import NewRelicGraphQLClient, NewRelicGraphQLError
from .utils import retry_on_error


class NewRelicLogsClient:
    """
    Client for New Relic log query operations.
    Provides methods for fetching logs from various partitions.
    """
    
    # Common log partitions - can be expanded as needed
    KNOWN_PARTITIONS = {
        "default": ["Log"],
        "all": [
            "Log", "Log_Access_AZ_CA1", "Log_Access_AZ_UK1", "Log_CNS", "Log_GlobalProd",
            "Log_ISM", "Log_Incapptic", "Log_Landesk", "Log_MDM_Sandbox", "Log_MI_AP1",
            "Log_MI_AP2", "Log_MI_AP2_Access", "Log_MI_AP_GS_Azure", "Log_MI_NA1", "Log_MI_NA2",
            "Log_MI_NA2_Access", "Log_MI_NA_GS_Azure", "Log_MI_SB_GS_Azure", "Log_MI_Sandbox_Access",
            "Log_NMDM_AZ_CA1", "Log_NMDM_AZ_CA1_Managed", "Log_NMDM_AZ_SB", "Log_NMDM_AZ_SB_Managed",
            "Log_NMDM_AZ_UK1", "Log_NMDM_AZ_UK1_Managed", "Log_Neurons_MLU", "Log_Neurons_NVU",
            "Log_Neurons_TKU", "Log_Neurons_TTU", "Log_Neurons_UKU", "Log_RiskSense", "Log_VNS",
            "Log_GlobalStaging", "Log_MI_AP_GS", "Log_MI_NA_GS", "Log_MiProdCluster", "Log_MI_SB_GS",
            "Log_MiSbStaging", "Log_MI_STAGING", "Log_MI_STAGING_GS", "Log_nmdm_az_sc_eu2"
        ],
        "neurons_nvu": ["Log_Neurons_NVU"],
        "neurons_mlu": ["Log_Neurons_MLU"],
        "neurons_uku": ["Log_Neurons_UKU"],
        "neurons_ttu": ["Log_Neurons_TTU"],
        "neurons_tku": ["Log_Neurons_TKU"],
        "mdm_na1": ["Log_MI_NA1"],
        "mdm_na2": ["Log_MI_NA2"],
        "mdm_ap1": ["Log_MI_AP1"],
        "mdm_ap2": ["Log_MI_AP2"],
        "mdm_all": ["Log_MI_NA1", "Log_MI_NA2", "Log_MI_AP1", "Log_MI_AP2"],
        "neurons_all": ["Log_Neurons_MLU", "Log_Neurons_NVU", "Log_Neurons_TKU", "Log_Neurons_TTU", "Log_Neurons_UKU"]
    }
    
    def __init__(self, client: NewRelicGraphQLClient, debug: bool = False):
        """
        Initialize the New Relic Logs client.
        
        Args:
            client: An initialized NewRelicGraphQLClient
            debug: Enable debug mode for verbose output
        """
        self.client = client
        self.debug = debug
    
    def _format_log_sources(self, partitions: Union[List[str], str]) -> str:
        """
        Format the FROM clause for log queries.
        
        Args:
            partitions: List of partition names or a predefined partition set key
            
        Returns:
            Formatted FROM clause string
        """
        if isinstance(partitions, str):
            # Check if it's a predefined partition set
            if partitions in self.KNOWN_PARTITIONS:
                partition_list = self.KNOWN_PARTITIONS[partitions]
            else:
                # Single partition name
                partition_list = [partitions]
        else:
            partition_list = partitions
            
        return ", ".join(partition_list)
    
    @retry_on_error(max_attempts=3, initial_delay=1, backoff_factor=2)
    def query_logs(
        self,
        query: str,
        limit: int = 100,
        account_id: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Execute a logs query against New Relic.

        Args:
            query: NRQL query for logs
            limit: Maximum number of logs to return
            account_id: Account ID (optional, overrides client default)

        Returns:
            List of log entries
        """
        account_id = account_id or self.client._verify_account_id()
        
        graphql_query = """
        query($accountId: Int!, $query: Nrql!) {
          actor {
            account(id: $accountId) {
              nrql(query: $query) {
                results
              }
            }
          }
        }
        """
        
        # Ensure query doesn't exceed the limit
        if "LIMIT" not in query.upper():
            query += f" LIMIT {limit}"
            
        variables = {
            "accountId": int(account_id),
            "query": query
        }
        
        if self.debug:
            print(f"Executing GraphQL logs query with NRQL: {query}")
            
        try:
            response = self.client.execute_query(graphql_query, variables)
            if self.debug and response.data and "actor" in response.data:
                results = response.data["actor"]["account"]["nrql"]["results"]
                print(f"Query returned {len(results)} results")
            return response.data["actor"]["account"]["nrql"]["results"]
        except NewRelicGraphQLError as e:
            if self.debug:
                print(f"Error querying logs: {str(e)}")
            # Convert to standard exception to prevent retry issues
            raise Exception(f"GraphQL query failed: {str(e)}")
        except Exception as e:
            if self.debug:
                print(f"Error querying logs: {str(e)}")
            return []
    
    def get_pod_logs(
        self,
        pod_name: str,
        cluster_partition: Optional[str] = None,
        log_level: Optional[str] = None,
        since: Optional[Union[str, int, datetime]] = None,
        until: Optional[Union[str, int, datetime]] = None,
        limit: int = 1000,
        account_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get logs for a specific Kubernetes pod.
        
        Args:
            pod_name: Name of the pod
            cluster_partition: Specific cluster partition to query (e.g., "neurons_nvu")
            log_level: Optional filter for log level (e.g., "error", "warn")
            since: Start time for the query
            until: End time for the query
            limit: Maximum number of log entries to return
            account_id: Account ID (overrides default if set)
            
        Returns:
            List of log entries for the pod
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        # Determine which partitions to query
        partitions = cluster_partition or "all"
        
        # Build where conditions
        where_conditions = [
            f"pod_name = '{pod_name}'"
        ]
        
        # Add log level filter if specified
        if log_level:
            where_conditions.append(f"level = '{log_level}' OR severity = '{log_level}'")
        
        # Add time range conditions if specified
        time_conditions = []
        if since:
            if isinstance(since, datetime):
                since_str = since.strftime('%Y-%m-%d %H:%M:%S')
            else:
                since_str = str(since)
            time_conditions.append(f"SINCE '{since_str}'")
        
        if until:
            if isinstance(until, datetime):
                until_str = until.strftime('%Y-%m-%d %H:%M:%S')
            else:
                until_str = str(until)
            time_conditions.append(f"UNTIL '{until_str}'")
        
        time_clause = " ".join(time_conditions)
        
        # Select relevant fields
        select_fields = ["timestamp", "level", "message", "pod_name", "container_name"]
        
        query = "SELECT " + ", ".join(select_fields) + " FROM " + self._format_log_sources(partitions) + " WHERE " + " AND ".join(where_conditions)
        
        if time_clause:
            query += " " + time_clause
            
        try:
            return self.query_logs(
                query=query,
                limit=limit,
                account_id=account_id
            )
        except Exception as e:
            if self.debug:
                print(f"Error in get_pod_logs: {str(e)}")
            return []
    
    # def get_entity_logs(
    #     self,
    #     entity_guid: str,
    #     since: datetime,
    #     until: datetime,
    #     entity_type: str = None,
    #     logs_config: Dict[str, Any] = None,
    #     limit: int = 100,
    #     account_id: Optional[str] = None,
    # ) -> List[Dict[str, Any]]:
    #     """
    #     Get logs for a specific entity.

    #     Args:
    #         entity_guid: Entity GUID
    #         since: Start time for logs
    #         until: End time for logs
    #         entity_type: Entity type (optional)
    #         logs_config: Configuration for log collection (optional)
    #         limit: Maximum number of logs to return
    #         account_id: Account ID (optional, overrides client default)

    #     Returns:
    #         List of log entries
    #     """
    #     # Build log query using NRQL log syntax
    #     base_query = f"FROM Log WHERE entity.guid = '{entity_guid}'"
        
    #     # Add date range
    #     time_query = (
    #         f"SINCE '{since.strftime('%Y-%m-%d %H:%M:%S')}' " +
    #         f"UNTIL '{until.strftime('%Y-%m-%d %H:%M:%S')}'"
    #     )
        
    #     # Add filtering based on logs_config if provided
    #     filter_conditions = []
    #     if logs_config and "filters" in logs_config:
    #         for filter_term in logs_config["filters"]:
    #             if ":" in filter_term:
    #                 # This is a field:value filter, add it directly
    #                 filter_conditions.append(filter_term)
    #             else:
    #                 # This is a search term, wrap it in message LIKE
    #                 filter_conditions.append(f"message LIKE '%{filter_term}%'")
        
    #     # Default to error logs if no filters provided
    #     if not filter_conditions:
    #         filter_conditions = ["level IN ('error', 'warning', 'critical', 'fatal')"]
            
    #     # Combine all filter conditions with OR
    #     filters = " OR ".join(filter_conditions)
    #     if filters:
    #         base_query += f" AND ({filters})"
            
    #     # Specify attributes to select
    #     attributes = "*"
    #     if logs_config and "attributes" in logs_config:
    #         attributes = ", ".join(logs_config["attributes"])
            
    #     # Specify partition if provided
    #     partition = ""
    #     if logs_config and "partition" in logs_config and logs_config["partition"] != "all":
    #         # New Relic no longer supports WITH PARTITIONS directly in NRQL
    #         # Instead, we include it in the FROM clause
    #         base_query = f"FROM Log, Partition '{logs_config['partition']}' WHERE entity.guid = '{entity_guid}'"
            
    #     # Build the full query
    #     query = f"SELECT {attributes} {base_query} {time_query} LIMIT {limit}"
        
    #     if self.debug:
    #         print(f"Executing logs query: {query}")

    #     # Execute query using client
    #     return self.query_logs(
    #         query=query,
    #         limit=limit,
    #         account_id=account_id
    #     )
    
    # def get_logs_config_for_entity_type(self, entity_type: str, cluster_id: str = "unknown") -> Dict[str, Any]:
    #     """
    #     Get the logs configuration for a specific entity type and cluster.
    #     This identifies what logs should be collected based on entity type and cluster.
        
    #     Args:
    #         entity_type: The entity type to get logs for
    #         cluster_id: The cluster ID (for Kubernetes entities)
            
    #     Returns:
    #         Dictionary of logs configuration appropriate for this entity type
    #     """
    #     # Determine log partition based on cluster ID
    #     partition = "all"
    #     for product, regions in self._get_cluster_patterns().items():
    #         for region, patterns in regions.items():
    #             if any(pattern in cluster_id for pattern in patterns):
    #                 partition = self._get_log_partition(region)
    #                 break
        
    #     # Create logs configurations grouped by entity type
    #     logs_config = {
    #         "K8S_POD": {
    #             "filters": [
    #                 f"kubernetes.pod.name:'{cluster_id}'",
    #                 "pod",
    #                 "error",
    #                 "exception",
    #                 "fail",
    #                 "critical"
    #             ],
    #             "attributes": [
    #                 "kubernetes.pod.name",
    #                 "kubernetes.namespace.name",
    #                 "kubernetes.container.name",
    #                 "message",
    #                 "level",
    #                 "timestamp"
    #             ],
    #             "partition": partition
    #         },
    #         "K8S_NODE": {
    #             "filters": [
    #                 f"kubernetes.node.name:'{cluster_id}'",
    #                 "node",
    #                 "error",
    #                 "warning",
    #                 "fail",
    #                 "critical"
    #             ],
    #             "attributes": [
    #                 "kubernetes.node.name",
    #                 "message",
    #                 "level",
    #                 "timestamp",
    #                 "component"
    #             ],
    #             "partition": partition
    #         },
    #         "HOST": {
    #             "filters": [
    #                 "error",
    #                 "warning",
    #                 "critical",
    #                 "fail",
    #                 "exception"
    #             ],
    #             "attributes": [
    #                 "hostname",
    #                 "service",
    #                 "message",
    #                 "level",
    #                 "timestamp"
    #             ],
    #             "partition": "all"
    #         },
    #         "APPLICATION": {
    #             "filters": [
    #                 "error",
    #                 "exception",
    #                 "fatal",
    #                 "warning",
    #                 "critical"
    #             ],
    #             "attributes": [
    #                 "service.name", 
    #                 "message",
    #                 "level",
    #                 "timestamp",
    #                 "trace.id"
    #             ],
    #             "partition": "all"
    #         },
    #         "CONTAINER": {
    #             "filters": [
    #                 "error",
    #                 "exception",
    #                 "fatal",
    #                 "warning",
    #                 "critical"
    #             ],
    #             "attributes": [
    #                 "container.name",
    #                 "container.id",
    #                 "message",
    #                 "level",
    #                 "timestamp"
    #             ],
    #             "partition": partition
    #         },
    #         "AWSRDSDBINSTANCE": {
    #             "filters": [
    #                 "error",
    #                 "warning",
    #                 "fatal",
    #                 "critical",
    #                 "timeout",
    #                 "deadlock",
    #                 "connection"
    #             ],
    #             "attributes": [
    #                 "provider.instanceIdentifier",
    #                 "message",
    #                 "level",
    #                 "timestamp"
    #             ],
    #             "partition": "all"
    #         }
    #     }
        
    #     # Return configuration for the entity type, or an empty dict if not found
    #     return logs_config.get(entity_type, {})
        
    def _get_cluster_patterns(self) -> Dict[str, Dict[str, List[str]]]:
        """
        Get patterns for identifying cluster region from cluster ID.
        
        Returns:
            Dictionary of cluster patterns by product and region
        """
        return {
            "neurons": {
                "nvu": ["aks-edge-rg-nvu-prd-neurons", "aks-rg-nvu-prd-neurons"],
                "uku": ["aks-rg-uku-prd-neurons", "aks-edge-rg-uku-prd-neurons"],
                "mlu": ["aks-edge-rg-mlu-prd-neurons", "aks-rg-mlu-prd-neurons"],
                "ttu": ["aks-edge-rg-ttu-prd-neurons", "aks-rg-ttu-prd-neurons"],
                "tku": ["aks-edge-rg-tku-prd-neurons", "aks-rg-tku-prd-neurons"]
            },
            "mdm": {
                "na1": ["na1", "primary-na1", "na1-eks", "north-america-1"],
                "na2": ["na2", "primary-na2", "na2-eks", "north-america-2"],
                "ap1": ["ap1", "primary-ap1", "ap1-eks", "asia-pacific-1"],
                "ap2": ["ap2", "primary-ap2", "ap2-eks", "asia-pacific-2"]
            }
        }
        
    def _get_log_partition(self, region: str) -> str:
        """
        Get the log partition to use for a given region.
        
        Args:
            region: Region code
            
        Returns:
            Log partition name
        """
        partition_map = {
            "nvu": "neurons_nvu",
            "uku": "neurons_all",
            "mlu": "neurons_all",
            "ttu": "neurons_all",
            "tku": "neurons_all",
            "na1": "default",
            "na2": "default",
            "ap1": "default",
            "ap2": "default"
        }
        return partition_map.get(region, "all") 