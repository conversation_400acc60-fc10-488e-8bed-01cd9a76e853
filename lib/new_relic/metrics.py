"""
New Relic GraphQL client for metrics and timeseries operations.
"""

from typing import Any, Dict, List, Optional, Union, cast
from datetime import datetime, timedelta
import os
import yaml

from .base import UTC, MetricResult, TimeseriesDataPoint, format_datetime
from .client import NewRelicGraphQLClient, NewRelicGraphQLError
from .config import MetricsConfigManager


class NewRelicMetricsClient:
    """
    Client for New Relic metrics operations.
    Provides methods for fetching and analyzing metric data.
    """
    
    def __init__(self, client: NewRelicGraphQLClient):
        """
        Initialize the New Relic metrics client.
        
        Args:
            client: The core New Relic GraphQL client instance
        """
        self.client = client
        self.config_manager = MetricsConfigManager()
    
    def get_metric_timeseries(
        self,
        metric_name: str,
        account_id: Optional[str] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        where: Optional[str] = None,
        facet: Optional[Union[str, List[str]]] = None,
        timeseries_period: str = "1 minute"
    ) -> List[MetricResult]:
        """
        Fetch timeseries metric data using NRQL.

        Args:
            metric_name: Name of the metric to query
            account_id: Account ID (overrides default if set)
            since: Start time for the query (defaults to 30 minutes ago)
            until: End time for the query (defaults to now)
            where: Optional NRQL WHERE clause
            facet: Optional facet clause (string or list of strings)
            timeseries_period: Time bucket for the timeseries (default: 1 minute)

        Returns:
            List of MetricResult objects containing the timeseries data
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        # Set default time values if not provided
        since = since or (datetime.now(UTC) - timedelta(minutes=30))
        until = until or datetime.now(UTC)

        # Build the NRQL query
        query_parts = [
            f"SELECT latest({metric_name})",
            f"FROM Metric",
            f"SINCE '{format_datetime(since)}'",
            f"UNTIL '{format_datetime(until)}'",
            f"TIMESERIES {timeseries_period}"
        ]

        if where:
            query_parts.insert(2, f"WHERE {where}")

        if facet:
            if isinstance(facet, list):
                facet_clause = ", ".join(facet)
            else:
                facet_clause = facet
            query_parts.insert(-1, f"FACET {facet_clause}")

        nrql = " ".join(query_parts)

        # Construct GraphQL query
        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.client.execute_query(graphql_query, variables)
        
        try:
            nrql_data = response.data["actor"]["account"]["nrql"]
            results = nrql_data["results"]
            metadata = nrql_data["metadata"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to parse metric results: {str(e)}")

        metric_results = []
        for result in results:
            timeseries_data = []
            for timestamp, value in result.items():
                if timestamp != "facet":
                    try:
                        ts = datetime.fromtimestamp(int(timestamp) / 1000, UTC)
                        val = float(value) if value is not None else 0.0
                        timeseries_data.append(
                            TimeseriesDataPoint(timestamp=ts, value=val)
                        )
                    except (ValueError, TypeError):
                        # Skip invalid data points
                        continue

            metric_results.append(
                MetricResult(
                    name=metric_name,
                    timeseries=sorted(timeseries_data, key=lambda x: x.timestamp),
                    metadata={
                        "facet": result.get("facet"),
                        **metadata
                    }
                )
            )

        return metric_results
    
    def parse_generic_timeseries(
        self, 
        timeseries_data: Dict[str, Any], 
        metric_name: str
    ) -> List[MetricResult]:
        """
        Parse a generic timeseries response and convert it to a list of MetricResult objects.

        Args:
            timeseries_data: The raw timeseries response from a NRQL query
            metric_name: The default metric name if facets are not available

        Returns:
            List of parsed MetricResult objects
        """
        results: List[MetricResult] = []
        
        # Get the results array from the response
        data_points = timeseries_data.get("results", [])
        if not data_points:
            return results

        # Group data points by facet
        facet_groups: Dict[str, List[Dict]] = {}
        for point in data_points:
            facet = point.get("facet")
            if facet:
                # Convert facet list to string key
                if isinstance(facet, list):
                    facet_key = " | ".join(str(f) for f in facet)
                else:
                    facet_key = str(facet)
            else:
                facet_key = metric_name
            
            if facet_key not in facet_groups:
                facet_groups[facet_key] = []
            facet_groups[facet_key].append(point)

        # Process each facet group
        for facet_key, points in facet_groups.items():
            timeseries: List[TimeseriesDataPoint] = []
            
            for point in points:
                # Get the timestamp from beginTimeSeconds
                timestamp_value = point.get("beginTimeSeconds")
                if timestamp_value is None:
                    # Try alternative formats
                    for key in point:
                        if isinstance(key, str) and key.isdigit():
                            timestamp_value = int(key) / 1000  # Convert ms to seconds
                            break
                
                if timestamp_value is None:
                    continue
                    
                try:
                    timestamp = datetime.fromtimestamp(timestamp_value, UTC)
                except (ValueError, TypeError):
                    continue
                
                # Find the metric value - look for keys ending with the metric name
                # or for common metrics pattern like average.*
                metric_value = None
                for key, value in point.items():
                    if (key.endswith(metric_name) or 
                        key.startswith("average.") or 
                        key == "count" or 
                        key == "sum" or
                        key == "latest"):
                        metric_value = value
                        break
                
                # Skip if no valid value found
                if metric_value is None:
                    continue
                
                # Convert to float, use 0.0 for null values
                try:
                    value = float(metric_value) if metric_value is not None else 0.0
                except (ValueError, TypeError):
                    value = 0.0
                
                timeseries.append(TimeseriesDataPoint(
                    timestamp=timestamp,
                    value=value
                ))
            
            # Only create a MetricResult if we have data points
            if timeseries:
                metadata = {
                    "facet": facet_key,
                    "total_points": len(timeseries)
                }
                results.append(MetricResult(
                    name=facet_key,
                    timeseries=sorted(timeseries, key=lambda x: x.timestamp),
                    metadata=metadata
                ))

        return results

    def get_generic_timeseries_metrics(
        self,
        nrql_query: str,
        metric_name: str,
        account_id: Optional[str] = None
    ) -> List[MetricResult]:
        """
        Execute a NRQL query and parse its timeseries results.

        Args:
            nrql_query: The NRQL query to execute
            metric_name: Default name for the metric if no facets are present
            account_id: Optional account ID to override the default

        Returns:
            List of parsed MetricResult objects
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql_query
        }

        response = self.client.execute_query(graphql_query, variables)
        try:
            results = response.data["actor"]["account"]["nrql"]
            return self.parse_generic_timeseries(results, metric_name)
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch timeseries metrics: {str(e)}")

    def get_entity_metrics_by_type(
        self,
        entity_guid: str,
        entity_type: str,
        metrics: Optional[List[str]] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        period: str = "1 minute",
        account_id: Optional[str] = None,
        metrics_config: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, List[MetricResult]]:
        """
        Gets metrics for a specific entity based on its type.
        Uses predefined metric sets for known entity types.

        Args:
            entity_guid: Entity GUID
            entity_type: Entity type
            metrics: Optional list of specific metrics to fetch
            since: Start time for metrics
            until: End time for metrics
            period: Aggregation period ("1 minute", "5 minutes", etc.)
            account_id: Account ID (overrides default if set)
            metrics_config: Configuration for metrics collection

        Returns:
            Dictionary of metrics data with metric names as keys
        """
        # If metrics_config is provided, use its basic metrics
        if metrics is None and metrics_config and "basic" in metrics_config:
            metrics = metrics_config["basic"]

        account_id = self.client._verify_account_id(account_id)
        since = since or (datetime.now(UTC) - timedelta(minutes=30))
        until = until or datetime.now(UTC)
        
        # If no metrics specified, use default metrics for this entity type
        if not metrics:
            metrics = self.config_manager.get_default_metrics(entity_type)
            
        # If still no metrics (entity type not configured), use empty dict
        if not metrics:
            return {}
        
        results = {}
        
        # Get each requested metric
        for metric_name in metrics:
            try:
                # Get the NRQL template from configuration
                nrql_template = self.config_manager.get_nrql_for_metric(entity_type, metric_name)
                
                if not nrql_template:
                    print(f"Warning: No NRQL template found for metric '{metric_name}' on entity type '{entity_type}'")
                    continue
                    
                # Format the NRQL template with parameters
                nrql = self.config_manager.format_nrql(
                    nrql_template,
                    entity_guid=entity_guid,
                    since=format_datetime(since),
                    until=format_datetime(until),
                    period=period
                )
                
                # Get the metric metadata
                metric_metadata = self.config_manager.get_metric_metadata(entity_type, metric_name)
                
                # Execute the query and parse results
                metric_results = self.get_generic_timeseries_metrics(
                    nrql_query=nrql,
                    metric_name=metric_metadata.get('name', metric_name),
                    account_id=account_id
                )
                
                # Add metadata to results
                for result in metric_results:
                    result.metadata.update({
                        "unit": metric_metadata.get("unit", "unknown"),
                        "entity_type": entity_type,
                        "entity_guid": entity_guid
                    })
                
                results[metric_name] = metric_results
                
            except Exception as e:
                print(f"Error fetching metric '{metric_name}' for entity {entity_guid}: {str(e)}")
        
        return results

    def get_evicted_pods_metrics(
        self,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        cluster_pattern: str = '%prd%',
        exclude_patterns: Optional[List[str]] = None,
        timeseries_period: str = "10 minutes",
        account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get metrics for evicted pods across clusters.

        Args:
            since: Start time for the query (defaults to 24 hours ago)
            until: End time for the query (defaults to now)
            cluster_pattern: Pattern to match cluster names (defaults to '%prd%')
            exclude_patterns: List of patterns to exclude from pod names
            timeseries_period: Time bucket for the timeseries (default: 10 minutes)
            account_id: Account ID (overrides default if set)

        Returns:
            Raw results from New Relic NRQL query
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)
        since = since or (datetime.now(UTC) - timedelta(hours=24))
        until = until or datetime.now(UTC)
        exclude_patterns = exclude_patterns or []

        # Build WHERE clause for exclusions
        exclusion_clauses = [f"podName NOT LIKE '{pattern}'" for pattern in exclude_patterns]
        exclusion_str = " AND ".join(exclusion_clauses)
        if exclusion_str:
            exclusion_str = f" AND {exclusion_str}"

        nrql = f"""
        SELECT count(*) FROM K8sPodSample 
        WHERE reason = 'Evicted' 
        AND status = 'Failed' 
        AND clusterName LIKE '{cluster_pattern}'{exclusion_str}
        FACET podName, clusterName 
        TIMESERIES {timeseries_period} 
        SINCE '{format_datetime(since)}' 
        UNTIL '{format_datetime(until)}'
        """

        # Build the GraphQL query
        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.client.execute_query(graphql_query, variables)
        try:
            return response.data["actor"]["account"]["nrql"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch evicted pods metrics: {str(e)}")

    def get_node_metrics(
        self,
        entity_guid: str,
        metric_type: str = 'cpu',  # 'cpu', 'memory', or 'disk'
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get node metrics (CPU, Memory, or Disk usage).
        
        Note: This method is kept for backwards compatibility.
        Consider using get_entity_metrics_by_type for new code.

        Args:
            entity_guid: Entity GUID of the node
            metric_type: Type of metric to fetch ('cpu', 'memory', or 'disk')
            since: Start time for the query
            until: End time for the query
            account_id: Optional account ID override

        Returns:
            Dictionary with metric results and metadata
            
        Raises:
            ValueError: If no account_id is available or invalid metric type
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)
        since = since or (datetime.now(UTC) - timedelta(days=1))
        until = until or datetime.now(UTC)

        metric_queries = {
            'cpu': "SELECT average(cpuPercent) AS 'CPU used %' FROM SystemSample",
            'memory': "SELECT average(memoryUsedPercent) AS 'Memory used %' FROM SystemSample",
            'disk': "SELECT max(diskUsedPercent) AS 'Storage used %' FROM StorageSample"
        }

        if metric_type not in metric_queries:
            raise ValueError(f"Invalid metric type. Must be one of: {', '.join(metric_queries.keys())}")

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        nrql = f"""
        {metric_queries[metric_type]}
        WHERE entityGuid = '{entity_guid}'
        TIMESERIES AUTO
        SINCE '{format_datetime(since)}'
        UNTIL '{format_datetime(until)}'
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.client.execute_query(graphql_query, variables)
        try:
            return response.data["actor"]["account"]["nrql"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch node metrics: {str(e)}")

    def get_entity_metrics(
        self,
        entity_guid: str,
        metrics: List[str],
        account_id: Optional[str] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        period: str = "1 minute"
    ) -> Dict[str, List[MetricResult]]:
        """
        Fetch multiple metrics for a specific entity.
        
        Note: This method is kept for backwards compatibility.
        Consider using get_entity_metrics_by_type for new code.

        Args:
            entity_guid: Entity GUID to query
            metrics: List of metric names to fetch
            account_id: Account ID (overrides default if set)
            since: Start time for the query (defaults to 30 minutes ago)
            until: End time for the query (defaults to now)
            period: Time bucket for the timeseries

        Returns:
            Dictionary mapping metric names to their MetricResult lists
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If any query fails
        """
        results = {}
        for metric in metrics:
            results[metric] = self.get_metric_timeseries(
                metric_name=metric,
                account_id=account_id,
                since=since,
                until=until,
                where=f"entity.guid = '{entity_guid}'",
                timeseries_period=period
            )
        return results

    def get_metrics_config_for_entity_type(self, entity_type: str) -> Dict[str, Any]:
        """
        Get the metrics configuration for a specific entity type.
        This identifies what metrics should be collected based on entity type.
        
        Args:
            entity_type: The entity type to get metrics for
            
        Returns:
            Dictionary of metric configurations appropriate for this entity type
        """
        # Create metrics configurations grouped by entity type
        metrics_config = {
            "K8S_POD": {
                "basic": [
                    "k8s.pod.cpu.usage",
                    "k8s.pod.memory.usage",
                    "k8s.pod.restarts",
                    "k8s.pod.status.phase",
                ],
                "advanced": [
                    "k8s.pod.cpu.limit",
                    "k8s.pod.memory.limit",
                    "k8s.pod.network.receive.bytes",
                    "k8s.pod.network.transmit.bytes",
                ]
            },
            "K8S_NODE": {
                "basic": [
                    "k8s.node.cpu.usage",
                    "k8s.node.memory.usage",
                    "k8s.node.filesystem.usage", 
                    "k8s.node.status.condition",
                ],
                "advanced": [
                    "k8s.node.cpu.capacity",
                    "k8s.node.memory.capacity",
                    "k8s.node.pod.capacity",
                    "k8s.node.network.receive.bytes",
                    "k8s.node.network.transmit.bytes",
                ]
            },
            "HOST": {
                "basic": [
                    "host.cpuPercent",
                    "host.memoryUsedPercent",
                    "host.diskUsedPercent",
                    "host.loadAverageOneMinute",
                ],
                "advanced": [
                    "host.swapUsedPercent",
                    "host.ioUtilizationPercent",
                    "host.processCount",
                    "host.networkThroughput",
                ]
            },
            "APPLICATION": {
                "basic": [
                    "apm.service.transaction.duration",
                    "apm.service.transaction.error_count",
                    "apm.service.transaction.request_count",
                    "apm.service.memory.physical",
                ],
                "advanced": [
                    "apm.service.datastore.operation.duration",
                    "apm.service.external.host.duration",
                    "apm.service.error.count",
                    "apm.service.cpu.usertime.utilization",
                ]
            },
            "CONTAINER": {
                "basic": [
                    "container.cpu.usage",
                    "container.memory.usage",
                    "container.status",
                    "container.restarts",
                ],
                "advanced": [
                    "container.cpu.limit",
                    "container.memory.limit",
                    "container.network.receive.bytes",
                    "container.network.transmit.bytes",
                ]
            },
            "AWSRDSDBINSTANCE": {
                "basic": [
                    "provider.cpuUtilization.Average",
                    "provider.freeableMemory.Average",
                    "provider.freeStorageSpace.Average",
                    "provider.databaseConnections.Average",
                ],
                "advanced": [
                    "provider.readIOPS.Average",
                    "provider.writeIOPS.Average",
                    "provider.readLatency.Average",
                    "provider.writeLatency.Average",
                ]
            }
        }
        
        # Return configuration for the entity type, or an empty dict if not found
        return metrics_config.get(entity_type, {}) 