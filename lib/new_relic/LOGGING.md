# New Relic Integration Logging Guide

This document explains how to use the enhanced logging system for the New Relic integration, which uses Loguru for better formatting, colors, and context.

## Overview

We've implemented a better logging system using [Loguru](https://github.com/Delgan/loguru) that provides:

- Color-coded logs by severity level (DEBUG, INFO, WARNING, ERROR, CRIT<PERSON>AL)
- Structured logs with timestamps, severity, and module context
- Easy customization of log formats
- Context binding for tracing operations through related logs
- Function execution tracking with timing information
- Properly formatted stack traces for easier debugging
- Visually distinct node execution boundaries in workflow logs
- Formatted NRQL queries and results in code blocks with syntax highlighting

## ANSI Colors in Logs

The logging system now uses direct ANSI escape codes for coloring rather than Loguru's tag-based format (`<red>text</red>`). This ensures better compatibility with terminals and avoids parsing issues.

### Using Colors in Logs

```python
# Import the Colors class from the logger
from lib.new_relic.logger import Colors

# Use the colors in your log messages
logger.info(f"{Colors.RED}This is red text{Colors.RESET}")
logger.error(f"{Colors.YELLOW}Warning:{Colors.RESET} {Colors.RED}Something went wrong{Colors.RESET}")
```

### Available Colors

The following color constants are available in the `Colors` class:

- Basic formatting: `RESET`, `BOLD`, `UNDERLINE`
- Foreground colors: `BLACK`, `RED`, `GREEN`, `YELLOW`, `BLUE`, `MAGENTA`, `CYAN`, `WHITE`
- Background colors: `BG_BLACK`, `BG_RED`, `BG_GREEN`, `BG_YELLOW`, `BG_BLUE`, `BG_MAGENTA`, `BG_CYAN`, `BG_WHITE`
- Bright colors: `BRIGHT_RED`, `BRIGHT_GREEN`, `BRIGHT_YELLOW`, `BRIGHT_BLUE`, `BRIGHT_MAGENTA`, `BRIGHT_CYAN`, `BRIGHT_WHITE`

## Basic Usage

### Importing and Setting Up Loggers

```python
# Import the logger tools
from lib.new_relic.logger import get_logger, log_execution, enrich_log_context

# Get a module-specific logger instance
logger = get_logger(__name__)

# Now you can use logger methods
logger.info("Processing started")
logger.debug("Detailed information")
logger.warning("Something might be wrong")
logger.error("An error occurred")
logger.critical("System failure")
```

### Logging with Context

```python
# Bind contextual information to logs
incident_logger = logger.bind(incident_id="123456", entity="server-xyz")
incident_logger.info("Processing incident")

# Or use the context manager for temporary context
with enrich_log_context(incident_id="123456", entity="server-xyz") as log:
    log.info("This log will have incident and entity context")
    log.warning("So will this one")
# Context is removed after exiting the with block
```

### Tracking Function Execution

```python
# Use the decorator to log function entry, exit, and execution time
@log_execution("custom_operation_name")  # Name is optional
async def my_async_function(arg1, arg2):
    # Function body
    return result

# Works with sync functions too
@log_execution()
def another_function():
    # Function body
    pass
```

## Enhanced Workflow Node Logging

For workflow nodes, we've added special formatting to make node boundaries more visible in the logs:

```python
from lib.new_relic.logger import format_node_start, format_node_end, format_node_error

# Use in the log_node_execution decorator:
def log_node_execution(node_name):
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Get incident_id from state...
            
            # Log node start with special formatting
            logger.info(format_node_start(node_name, incident_id))
            
            start_time = datetime.now(timezone.utc)
            try:
                result = await func(*args, **kwargs)
                
                # Calculate duration
                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()
                
                # Log node completion with special formatting
                logger.success(format_node_end(node_name, incident_id, duration))
                return result
            except Exception as e:
                # Calculate duration on error
                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()
                
                # Handle errors with special formatting
                logger.error(format_node_error(node_name, incident_id, str(e), duration))
                raise
        return wrapper
    return decorator
```

## NRQL Query Formatting

For NRQL queries, we provide special formatting functions to make queries and results more readable:

```python
from lib.new_relic.logger import format_nrql_query, format_nrql_results

# Format a query as a code block with syntax highlighting
logger.info(format_nrql_query("""
SELECT count(*) FROM Transaction 
WHERE appName = 'My Application' 
SINCE 1 hour ago
"""))

# Format query results with proper structure
results = query_client.execute_nrql(query)
logger.success(format_nrql_results(results))
```

## Configuration

The logger is configured in `lib.new_relic.logger.py`. You can customize:

- Log level via the `LOG_LEVEL` environment variable (default: INFO)
- Log format and colors
- File logging by setting the `LOG_FILE` environment variable
- Maximum number of NRQL results to display in logs

## Best Practices

1. **Use Module-Specific Loggers**: Always get a logger instance for your module
   ```python
   logger = get_logger(__name__)
   ```

2. **Add Context for Complex Operations**: Use `bind()` or `enrich_log_context()` 
   ```python
   query_logger = logger.bind(query_id="12345", query_type="NRQL")
   ```

3. **Track Function Execution**: Add the `@log_execution()` decorator to important functions to track timing

4. **Use Node Formatting in Workflows**: Use the formatting functions in workflow nodes for better visibility

5. **Format NRQL Queries**: Use the NRQL formatting functions when logging queries and results

6. **Use Appropriate Log Levels**:
   - `DEBUG`: Detailed information useful for debugging
   - `INFO`: Normal operations and status updates
   - `WARNING`: Something unexpected but not an error
   - `ERROR`: An operation failed
   - `CRITICAL`: System-level failures

7. **Use ANSI Colors Properly**:
   - Always reset colors after using them: `f"{Colors.RED}red text{Colors.RESET}"`
   - For complex multi-line output, consider using the formatting functions
   - Avoid directly using Loguru's tag-based color format (`<red>text</red>`)

## Example

Here's a complete example showing various logging features:

```python
from lib.new_relic.logger import (
    get_logger, log_execution, format_nrql_query, format_nrql_results, 
    enrich_log_context, Colors
)

logger = get_logger(__name__)

@log_execution()
async def fetch_data(query_id, parameters):
    # Use context manager to add structured context to logs
    with enrich_log_context(query_id=query_id, operation="fetch_data") as log:
        # Format parameters for display
        param_str = f"{Colors.CYAN}{parameters}{Colors.RESET}"
        log.info(f"Fetching data with parameters: {param_str}")
        
        # Log the NRQL query with special formatting
        query = f"""
        SELECT * FROM Transaction 
        WHERE appName = '{parameters["app_name"]}' 
        SINCE {parameters["since"]} 
        UNTIL {parameters["until"]}
        """
        log.info(format_nrql_query(query))
        
        try:
            # Execute query
            results = await query_client.execute_nrql(query)
            
            # Log results with special formatting
            log.success(format_nrql_results(results))
            return results
        except Exception as e:
            error_msg = f"{Colors.RED}Query failed:{Colors.RESET} {str(e)}"
            log.error(error_msg)
            raise
```

## Troubleshooting

If you need to see more detailed logs, set the `LOG_LEVEL` environment variable to `DEBUG` in your `.env` file:

```
LOG_LEVEL=DEBUG
```

You can also run the included example script to see the logging features in action:

```
python lib/new_relic/logging_example.py
```

To test if ANSI colors are working in your terminal, run:

```
python test_colors.py
```

### Common Issues

1. **Color codes showing as text**: Some terminals or environments may not process ANSI color codes correctly. 
   In this case, you'll see the raw color codes like `<red>` in the output instead of actual colored text.
   
   Solution: Ensure your terminal supports ANSI colors or set `export TERM=xterm-color` in your shell.

2. **Complex formatting issues with Loguru**: When sending complex strings with ANSI colors to Loguru,
   you might encounter parsing errors.
   
   Solution: For complex multi-line colored output, use the specialized formatting functions or 
   print directly to stderr:
   
   ```python
   import sys
   print(complex_colored_string, file=sys.stderr)
   ``` 