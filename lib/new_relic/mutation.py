"""
New Relic GraphQL client for mutations (write operations).
"""

import json
from typing import Any, Dict, List, Optional

from .base import GraphQLResponse, NewRelicGraphQLError
from .client import NewRelicGraphQLClient


class NewRelicMutationClient:
    """
    Client for New Relic mutation (write) operations.
    Provides methods for creating and updating resources in New Relic.
    """
    
    def __init__(self, client: NewRelicGraphQLClient):
        """
        Initialize the New Relic mutation client.
        
        Args:
            client: The core New Relic GraphQL client instance
        """
        self.client = client
    
    def create_webhook_destination(
        self, 
        name: str, 
        url: str, 
        account_id: Optional[str] = None
    ) -> str:
        """
        Create a webhook destination.
        
        Args:
            name: Webhook name
            url: Webhook URL
            account_id: Account ID (overrides default if set)
            
        Returns:
            Webhook destination ID
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $name: String!, $url: String!) {
            aiNotificationsCreateDestination(
                accountId: $accountId,
                destination: {
                    type: WEBHOOK,
                    name: $name,
                    properties: [{key: "url", value: $url}]
                }
            ) {
                destination {
                    id
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "name": name,
            "url": url
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiNotificationsCreateDestination"]["destination"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create webhook destination")

    def create_notification_channel(
        self, 
        destination_id: str, 
        name: str, 
        api_key: str, 
        account_id: Optional[str] = None
    ) -> str:
        """
        Create a notification channel.
        
        Args:
            destination_id: Destination ID
            name: Channel name
            api_key: API key for the channel
            account_id: Account ID (overrides default if set)
            
        Returns:
            Channel ID
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        # Define the payload template with proper JSON escaping
        payload_template = (
            '{"id": {{ json issueId }},'
            '"issueUrl": {{ json issuePageUrl }},'
            '"name": {{ json annotations.title.[0] }},'
            '"severity": {{ json priority }},'
            '"impactedEntities": {{ json entitiesData.names }},'
            '"totalIncidents": {{ json totalIncidents }},'
            '"status": {{ json state }},'
            '"trigger": {{ json triggerEvent }},'
            '"isCorrelated": {{ json isCorrelated }},'
            '"createdAt": {{ createdAt }},'
            '"updatedAt": {{ updatedAt }},'
            '"lastReceived": {{ updatedAt }},'
            '"source": {{ json accumulations.source }},'
            '"alertPolicyNames": {{ json accumulations.policyName }},'
            '"alertConditionNames": {{ json accumulations.conditionName }},'
            '"workflowName": {{ json workflowName }}}'
        )
        
        graphql_query = """
        mutation($accountId: Int!, $destinationId: ID!, $name: String!, $headers: String!, $payload: String!) {
            aiNotificationsCreateChannel(
                accountId: $accountId,
                channel: {
                    name: $name,
                    product: IINT,
                    type: WEBHOOK,
                    destinationId: $destinationId,
                    properties: [
                        {
                            key: "headers",
                            value: $headers
                        },
                        {
                            key: "payload",
                            value: $payload
                        }
                    ]
                }
            ) {
                channel {
                    id
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "destinationId": destination_id,
            "name": name,
            "headers": json.dumps({"X-API-KEY": api_key}),
            "payload": payload_template
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiNotificationsCreateChannel"]["channel"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create notification channel")

    def create_workflow(
        self, 
        name: str, 
        channel_id: str, 
        policy_ids: List[str], 
        account_id: Optional[str] = None
    ) -> str:
        """
        Create a workflow.
        
        Args:
            name: Workflow name
            channel_id: Channel ID
            policy_ids: List of policy IDs
            account_id: Account ID (overrides default if set)
            
        Returns:
            Workflow ID
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $name: String!, $channelId: ID!, $policyIds: [String!]!) {
            aiWorkflowsCreateWorkflow(
                accountId: $accountId
                createWorkflowData: {
                    destinationConfigurations: {
                        channelId: $channelId,
                        notificationTriggers: [ACTIVATED, ACKNOWLEDGED, CLOSED, PRIORITY_CHANGED, OTHER_UPDATES]
                    },
                    issuesFilter: {
                        predicates: [
                            {
                                attribute: "labels.policyIds",
                                operator: EXACTLY_MATCHES,
                                values: $policyIds
                            }
                        ],
                        type: FILTER
                    },
                    workflowEnabled: true,
                    destinationsEnabled: true,
                    mutingRulesHandling: DONT_NOTIFY_FULLY_MUTED_ISSUES,
                    name: $name
                }
            ) {
                workflow {
                    id
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "name": name,
            "channelId": channel_id,
            "policyIds": policy_ids
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiWorkflowsCreateWorkflow"]["workflow"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create workflow")
    
    def update_workflow(
        self,
        workflow_id: str,
        enabled: bool,
        account_id: Optional[str] = None
    ) -> bool:
        """
        Update a workflow's enabled status.
        
        Args:
            workflow_id: ID of the workflow to update
            enabled: Whether the workflow should be enabled
            account_id: Account ID (overrides default if set)
            
        Returns:
            True if successful
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $workflowId: ID!, $enabled: Boolean!) {
            aiWorkflowsUpdateWorkflow(
                accountId: $accountId,
                updateWorkflowData: {
                    id: $workflowId,
                    workflowEnabled: $enabled
                }
            ) {
                workflow {
                    id
                    workflowEnabled
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "workflowId": workflow_id,
            "enabled": enabled
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiWorkflowsUpdateWorkflow"]["workflow"]["workflowEnabled"] == enabled
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to update workflow")
    
    def delete_workflow(
        self,
        workflow_id: str,
        account_id: Optional[str] = None
    ) -> bool:
        """
        Delete a workflow.
        
        Args:
            workflow_id: ID of the workflow to delete
            account_id: Account ID (overrides default if set)
            
        Returns:
            True if successful
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $workflowId: ID!) {
            aiWorkflowsDeleteWorkflow(
                accountId: $accountId,
                id: $workflowId
            ) {
                id
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "workflowId": workflow_id
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiWorkflowsDeleteWorkflow"]["id"] == workflow_id
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to delete workflow") 