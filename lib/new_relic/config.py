"""
Configuration manager for New Relic metric queries.
"""

import os
import yaml
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

class MetricsConfigManager:
    """Manages metric configuration for different entity types"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the metrics configuration manager
        
        Args:
            config_path: Path to the metrics configuration YAML file.
                         If None, uses the default config/entity_metrics.yaml.
        """
        if config_path is None:
            # Default location relative to the package
            module_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(module_dir, '..', '..', 'config', 'entity_metrics.yaml')
            
        self.config = self._load_config(config_path)
        self._aliases = self._load_aliases()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load the metrics configuration from YAML file
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dictionary containing the configuration
        """
        if not os.path.exists(config_path):
            print(f"Warning: Metrics configuration file not found at {config_path}")
            return {"entities": {}}
            
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"Error loading metrics configuration: {str(e)}")
            return {"entities": {}}
    
    def _load_aliases(self) -> Dict[str, str]:
        """
        Load entity type aliases from the configuration
        
        Returns:
            Dictionary mapping alias names to canonical entity types
        """
        aliases = {}
        try:
            alias_config = self.config.get("entities", {}).get("aliases", {})
            if alias_config:
                aliases.update(alias_config)
        except Exception as e:
            print(f"Error loading entity aliases: {str(e)}")
        return aliases
    
    def _resolve_entity_type(self, entity_type: str) -> str:
        """
        Resolve entity type aliases to their canonical names
        
        Args:
            entity_type: Entity type or alias
            
        Returns:
            Canonical entity type name
        """
        return self._aliases.get(entity_type, entity_type)
    
    def get_entity_metrics(self, entity_type: str) -> Dict[str, Dict[str, Any]]:
        """
        Get all metrics configuration for a specific entity type
        
        Args:
            entity_type: Type of entity (e.g., KUBERNETES_POD)
            
        Returns:
            Dictionary of metric configurations
        """
        resolved_type = self._resolve_entity_type(entity_type)
        return self.config.get("entities", {}).get(resolved_type, {}).get("metrics", {})
    
    def get_default_metrics(self, entity_type: str) -> List[str]:
        """
        Get list of default metrics to collect for an entity type
        
        Args:
            entity_type: Type of entity
            
        Returns:
            List of metric names that are marked as default
        """
        metrics = self.get_entity_metrics(entity_type)
        return [name for name, config in metrics.items() 
                if config.get("default", False)]
                
    def get_nrql_for_metric(self, entity_type: str, metric_name: str) -> Optional[str]:
        """
        Get the NRQL query for a specific metric and entity type
        
        Args:
            entity_type: Type of entity
            metric_name: Name of the metric
            
        Returns:
            NRQL query string or None if not found
        """
        metrics = self.get_entity_metrics(entity_type)
        return metrics.get(metric_name, {}).get("nrql")
    
    def get_metric_metadata(self, entity_type: str, metric_name: str) -> Dict[str, Any]:
        """
        Get metadata for a specific metric (name, unit, etc.)
        
        Args:
            entity_type: Type of entity
            metric_name: Name of the metric
            
        Returns:
            Dictionary of metric metadata
        """
        metrics = self.get_entity_metrics(entity_type)
        metric_config = metrics.get(metric_name, {})
        
        # Remove the NRQL query from the metadata
        metadata = {k: v for k, v in metric_config.items() if k != 'nrql'}
        return metadata
        
    def format_nrql(self, nrql: str, **kwargs) -> str:
        """
        Format the NRQL query with the given parameters
        
        Args:
            nrql: The NRQL query template
            **kwargs: Parameters to substitute in the query
            
        Returns:
            Formatted NRQL query
        """
        return nrql.format(**kwargs)
    
    def get_all_entity_types(self) -> List[str]:
        """
        Get all entity types defined in the configuration
        
        Returns:
            List of entity type names
        """
        entities = self.config.get("entities", {})
        return [name for name in entities.keys() if name != "aliases"] 