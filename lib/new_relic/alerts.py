"""
New Relic GraphQL client for alert operations.
"""

from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

from .base import UTC, Alert, AlertStatus, AlertSeverity, safe_enum_parse, parse_timestamp
from .client import NewRelicGraphQLClient


class NewRelicAlertsClient:
    """
    Client for New Relic alerts operations.
    Provides methods for fetching and processing alert data.
    """
    
    def __init__(self, client: NewRelicGraphQLClient):
        """
        Initialize the New Relic alerts client.
        
        Args:
            client: The core New Relic GraphQL client instance
        """
        self.client = client
    
    def get_alerts(
        self,
        account_id: Optional[str] = None,
        status: Optional[AlertStatus] = None,
        entity_guid: Optional[str] = None,
        since: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Alert]:
        """
        Fetch New Relic issues/incidents.

        Args:
            account_id: Account ID (overrides default if set)
            status: Optional filter by alert status
            entity_guid: Optional filter by entity GUID
            since: Optional filter for alerts since a timestamp
            limit: Maximum number of alerts to return

        Returns:
            List of Alert objects
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)
        since_filter = f", createdAt: {int(since.timestamp() * 1000)}" if since else ""
        status_value = f", states: [{status.name}]" if status else ""
        entity_filter = f", entityGuids: [\"{entity_guid}\"]" if entity_guid else ""
        
        graphql_query = f"""
        query($accountId: Int!) {{
            actor {{
                account(id: $accountId) {{
                    aiIssues {{
                        issues {{
                            issues {{
                                account {{
                                    id
                                    name
                                }}
                                acknowledgedAt
                                acknowledgedBy
                                activatedAt
                                closedAt
                                closedBy
                                conditionName
                                createdAt
                                deepLinkUrl
                                description
                                entityGuids
                                entityNames
                                issueId
                                policyName
                                priority
                                state
                                title
                                totalIncidents
                                updatedAt
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """

        variables = {"accountId": int(account_id)}
        response = self.client.execute_query(graphql_query, variables)
        
        alerts = []
        try:
            issues = response.data["actor"]["account"]["aiIssues"]["issues"]["issues"]
            for issue in issues:
                alerts.append(self._parse_alert(issue))
        except (KeyError, TypeError) as e:
            raise Exception(f"Failed to parse alerts: {str(e)}")
        
        return alerts

    def _parse_alert(self, issue: Dict[str, Any]) -> Alert:
        """
        Parse an issue from the API response into an Alert object.
        
        Args:
            issue: Raw issue data from API
            
        Returns:
            Parsed Alert object
        """
        title = issue.get("title", "")
        description = issue.get("description", "")
        
        # Handle both string and list formats for title and description
        if isinstance(title, list) and title:
            title = title[0]
        if isinstance(description, list) and description:
            description = description[0]
            
        # Parse timestamps with proper error handling
        def parse_timestamp(ts_value):
            if not ts_value:
                return None
            try:
                return datetime.fromtimestamp(ts_value / 1000, UTC)
            except (ValueError, TypeError):
                return None
        
        # Parse status and severity with fallbacks
        try:
            status = AlertStatus(issue.get("state", "").lower())
        except (ValueError, AttributeError):
            status = AlertStatus.FIRING  # Default
            
        try:
            severity = AlertSeverity(issue.get("priority", "").lower())
        except (ValueError, AttributeError):
            severity = AlertSeverity.INFO  # Default
        
        return Alert(
            id=issue.get("issueId", ""),
            name=title,
            status=safe_enum_parse(AlertStatus, issue.get("state"), AlertStatus.FIRING),
            severity=safe_enum_parse(AlertSeverity, issue.get("priority"), AlertSeverity.INFO),
            description=description,
            created_at=parse_timestamp(issue.get("createdAt")),
            updated_at=parse_timestamp(issue.get("updatedAt")),
            closed_at=parse_timestamp(issue.get("closedAt")),
            acknowledged_at=parse_timestamp(issue.get("acknowledgedAt")),
            acknowledged_by=issue.get("acknowledgedBy"),
            closed_by=issue.get("closedBy"),
            policy_name=issue.get("policyName"),
            condition_name=issue.get("conditionName"),
            entity_guids=issue.get("entityGuids"),
            deep_link_url=issue.get("deepLinkUrl"),
            total_incidents=issue.get("totalIncidents")
        )

    def acknowledge_alert(
        self,
        issue_id: str,
        account_id: Optional[str] = None
    ) -> bool:
        """
        Acknowledge a New Relic alert.
        
        Args:
            issue_id: ID of the issue to acknowledge
            account_id: Account ID (overrides default if set)
            
        Returns:
            True if successful
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $issueId: ID!) {
            aiIssuesAcknowledgeIssue(
                accountId: $accountId,
                id: $issueId
            ) {
                issue {
                    issueId
                    state
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "issueId": issue_id
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            result = response.data["aiIssuesAcknowledgeIssue"]["issue"]
            return result["state"].lower() == "acknowledged"
        except (KeyError, TypeError):
            raise Exception("Failed to acknowledge alert")

    def close_alert(
        self,
        issue_id: str,
        account_id: Optional[str] = None
    ) -> bool:
        """
        Close a New Relic alert.
        
        Args:
            issue_id: ID of the issue to close
            account_id: Account ID (overrides default if set)
            
        Returns:
            True if successful
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $issueId: ID!) {
            aiIssuesCloseIssue(
                accountId: $accountId,
                id: $issueId
            ) {
                issue {
                    issueId
                    state
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "issueId": issue_id
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            result = response.data["aiIssuesCloseIssue"]["issue"]
            return result["state"].lower() == "closed"
        except (KeyError, TypeError):
            raise Exception("Failed to close alert") 