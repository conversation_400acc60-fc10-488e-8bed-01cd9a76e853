# Entity Relationships and Metadata

This module provides a structured approach to manage entity relationships and metadata in New Relic.

## Overview

The Entity Relationships system allows you to:

- Define relationships between different types of entities
- Specify NRQL queries to discover relationships dynamically
- Configure metadata fields to extract for each entity type
- Handle entity type aliases for consistency

## Configuration

Relationships and metadata fields are defined in the `config/entity_relationships.yaml` file.

### Example Configuration

```yaml
relationships:
  K8S_POD:
    - type: K8S_NODE
      relation: runs_on
      query: >
        SELECT latest(nodeName) FROM K8sPodSample
        WHERE podName = '{entity_name}' AND clusterName = '{cluster_name}'
        SINCE 10 minutes ago

  K8S_NODE:
    - type: K8S_CLUSTER
      relation: belongs_to
      query: >
        SELECT latest(clusterName) FROM K8sNodeSample
        WHERE nodeName = '{entity_name}'
        SINCE 10 minutes ago

metadata_fields:
  K8S_POD:
    - podName
    - namespaceName
    - clusterName
    - status

aliases:
  KUBERNETES_POD: K8S_POD
  KUBERNETES_NODE: K8S_NODE
```

## Basic Usage

### Mapping Entity Relationships

```python
from lib.new_relic.analyzer import EntityRelationshipMapper
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient

# Initialize clients
client = NewRelicGraphQLClient(api_key="your-key", account_id="your-account")
query_client = NewRelicQueryClient(client)

# Initialize relationship mapper
mapper = EntityRelationshipMapper(query_client)

# Map relationships for an entity
relationships = mapper.map_relationships(
    entity_type="K8S_POD",
    entity_name="my-pod-name",
    cluster_name="my-cluster",
    since_minutes=30
)

# Print relationships
for rel in relationships:
    print(f"Related Type: {rel['related_type']}, Relation: {rel['relation']}")
    for result in rel['query_results']:
        print(result)
```

### Extracting Entity Metadata

```python
from lib.new_relic.analyzer import EntityAnalyzer
from lib.new_relic.client import NewRelicGraphQLClient

# Initialize clients
client = NewRelicGraphQLClient(api_key="your-key", account_id="your-account")

# Initialize entity analyzer
analyzer = EntityAnalyzer(client)

# Get entity details
entity_details = analyzer._get_entity_details("entity-guid")

# Extract metadata
metadata = analyzer._get_entity_metadata(entity_details)

# Print metadata
print(metadata)
```

## Entity Types

The system supports various entity types including:

- **K8S_POD**: Kubernetes pods
- **K8S_NODE**: Kubernetes nodes
- **K8S_CLUSTER**: Kubernetes clusters
- **HOST**: Host systems (including AWS and Azure instances)
- **APPLICATION**: New Relic APM applications

## Relationship Types

Relationships are defined by:

- **type**: The type of related entity
- **relation**: The nature of the relationship (e.g., "runs_on", "belongs_to")
- **query**: NRQL query to discover the relationship

## Metadata Fields

Metadata fields define which attributes to extract from entity details. The system supports:

- Simple field names (e.g., "podName")
- Nested fields using dot notation (e.g., "condition.Ready")

## Working with Entity Types

Entity type names can vary in New Relic. The `aliases` configuration allows mapping
different entity type strings to a standard name.

For example, both "KUBERNETES_POD" and "K8S_POD" will be treated as the same entity type.

## Advanced Usage

### Custom Parameters

You can pass additional parameters to the mapping function:

```python
relationships = mapper.map_relationships(
    entity_type="K8S_POD",
    entity_name="my-pod-name",
    cluster_name="my-cluster",
    additional_params={
        "custom_param": "custom_value"
    }
)
```

### Adding New Entity Types

To add support for a new entity type:

1. Add the entity type to the `relationships` section in the YAML file
2. Define relationships using NRQL queries
3. Add metadata fields to the `metadata_fields` section
4. Add any aliases to the `aliases` section 