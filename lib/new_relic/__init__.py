"""
New Relic NerdGraph GraphQL client library.
Supports both US and EU endpoints with proper authentication and error handling.
"""

from .base import (
    Region, 
    AlertStatus,
    AlertSeverity,
    GraphQLResponse,
    NewRelicGraphQLError,
    TimeseriesDataPoint,
    MetricResult,
    Alert
)

from .client import NewRelicGraphQLClient
from .query import NewRelicQueryClient
from .mutation import NewRelicMutationClient
from .metrics import NewRelicMetricsClient
from .alerts import NewRelicAlertsClient

__all__ = [
    # Base types
    'Region',
    'AlertStatus',
    'AlertSeverity',
    'GraphQLResponse',
    'NewRelicGraphQLError',
    'TimeseriesDataPoint',
    'MetricResult',
    'Alert',
    
    # Client classes
    'NewRelicGraphQLClient',
    'NewRelicQueryClient',
    'NewRelicMutationClient',
    'NewRelicMetricsClient',
    'NewRelicAlertsClient'
] 