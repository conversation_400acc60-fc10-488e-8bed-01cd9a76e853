"""
Utility functions for New Relic API clients.
"""

import functools
import time
import random
import logging
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union, cast

from .base import NewRelicGraphQLError
from .retry_utils import nr_retry, is_retriable_error

# Re-export the retry utilities for backward compatibility
__all__ = ['retry_on_error', 'nr_retry', 'is_retriable_error']

T = TypeVar('T')

logger = logging.getLogger(__name__)

def retry_on_error(max_attempts=3, initial_delay=1, backoff_factor=2):
    """
    Decorator to retry a function call on failure with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        initial_delay: Initial delay in seconds before retrying
        backoff_factor: Factor to increase delay on each retry
    
    Returns:
        Decorated function that will retry on exception
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            delay = initial_delay
            
            while attempt < max_attempts:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    attempt += 1
                    if attempt >= max_attempts:
                        # Re-raise the exception if we've exhausted all attempts
                        raise
                    
                    # Calculate delay with exponential backoff
                    sleep_time = delay * (backoff_factor ** (attempt - 1))
                    
                    # Log the retry attempt
                    logging.warning(
                        f"Error in {func.__name__}, retrying in {sleep_time} seconds "
                        f"(attempt {attempt}/{max_attempts-1}): {str(e)}"
                    )
                    
                    # Sleep before retrying
                    time.sleep(sleep_time)
        
        return wrapper
    
    return decorator 