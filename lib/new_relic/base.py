"""
Base types and utilities for New Relic GraphQL client.
"""

import json
from enum import Enum
from typing import Any, Dict, List, Optional, TypeVar, Union, Generic
from datetime import datetime, timezone
from pydantic import BaseModel, Field

# Use timezone consistently instead of UTC constant
UTC = timezone.utc

T = TypeVar('T')


class Region(Enum):
    """New Relic regions for NerdGraph API"""
    US = "https://api.newrelic.com/graphql"
    EU = "https://api.eu.newrelic.com/graphql"


class AlertStatus(Enum):
    """Alert status values"""
    FIRING = "open"
    RESOLVED = "closed"
    ACKNOWLEDGED = "acknowledged"


class AlertSeverity(Enum):
    """Alert severity values"""
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"


class NewRelicGraphQLError(Exception):
    """Custom exception for New Relic GraphQL errors"""
    
    def __init__(self, message: str, errors: Optional[List[Dict[str, Any]]] = None):
        self.errors = errors or []
        error_details = f": {json.dumps(errors)}" if errors else ""
        super().__init__(f"{message}{error_details}")


class GraphQLResponse(BaseModel, Generic[T]):
    """Structured response from NerdGraph API with generic data type"""
    data: Optional[T] = None
    errors: Optional[List[Dict[str, Any]]] = None

    @property
    def has_errors(self) -> bool:
        """Check if the response contains errors"""
        return self.errors is not None and len(self.errors) > 0


class TimeseriesDataPoint(BaseModel):
    """Represents a single timeseries data point"""
    timestamp: datetime
    value: float


class MetricResult(BaseModel):
    """Represents metric query results"""
    name: str
    timeseries: List[TimeseriesDataPoint]
    metadata: Dict[str, Any]


class Alert(BaseModel):
    """Represents a New Relic alert/issue"""
    id: str
    name: str
    status: AlertStatus
    severity: AlertSeverity
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    closed_by: Optional[str] = None
    policy_name: Optional[str] = None
    condition_name: Optional[str] = None
    entity_guids: Optional[List[str]] = None
    deep_link_url: Optional[str] = None
    total_incidents: Optional[int] = None


def format_datetime(dt: datetime) -> str:
    """
    Format a datetime object for use in New Relic queries.
    
    Args:
        dt: The datetime to format
        
    Returns:
        Formatted datetime string in YYYY-MM-DD HH:MM:SS format as required by New Relic
    """
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def parse_timestamp(ts_value: Optional[int]) -> Optional[datetime]:
    """
    Parse a timestamp value from New Relic API into a datetime object.
    
    Args:
        ts_value: Timestamp in milliseconds since epoch or None
        
    Returns:
        Parsed datetime or None if invalid
    """
    if not ts_value:
        return None
    try:
        return datetime.fromtimestamp(ts_value / 1000, UTC)
    except (ValueError, TypeError):
        return None


def safe_enum_parse(enum_class, value: Optional[str], default):
    """
    Safely parse an enum value with a fallback.
    
    Args:
        enum_class: The Enum class to use
        value: The string value to parse
        default: The default enum value if parsing fails
        
    Returns:
        Parsed enum value or default
    """
    if not value:
        return default
    try:
        return enum_class(value.lower())
    except (ValueError, AttributeError):
        return default 