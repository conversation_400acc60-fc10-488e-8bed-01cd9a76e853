"""
Kubernetes Cluster entity for New Relic queries.
"""

from typing import Dict, List, Any, Optional
from ..nrql_manager import NRQLManager
from ..query import NewRelicQueryClient

class ClusterEntity:
    """
    Entity class for Kubernetes Cluster-related queries.
    Provides methods for retrieving cluster metrics and information using the NRQL Manager.
    """
    
    def __init__(self, query_client: NewRelicQueryClient, nrql_manager: Optional[NRQLManager] = None):
        """
        Initialize the Cluster entity.
        
        Args:
            query_client: The New Relic query client for executing NRQL
            nrql_manager: Optional NRQL Manager instance (creates a new one if not provided)
        """
        self.query_client = query_client
        self.nrql_manager = nrql_manager or NRQLManager()
    
    def get_cluster_nodes(
        self, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get information about nodes in a Kubernetes cluster.
        
        Args:
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing node information
        """
        params = {
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        query = f"""
        SELECT uniqueCount(nodeName) as 'nodeCount',
               latest(capacityCpuCores) as 'totalCpuCores',
               latest(capacityMemoryBytes) as 'totalMemoryBytes',
               latest(allocatableCpuCores) as 'allocatableCpuCores',
               latest(allocatableMemoryBytes) as 'allocatableMemoryBytes'
        FROM K8sNodeSample 
        WHERE clusterName = '{cluster_name}'
        SINCE {since_time_ms} UNTIL {until_time_ms}
        FACET nodeName
        """
        
        return self.query_client.execute_nrql_query(query)
    
    def get_resource_usage(
        self, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get resource usage across a Kubernetes cluster.
        
        Args:
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing resource usage data
        """
        params = {
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
        
        query = f"""
        SELECT 
            average(cpuUsedCores) as 'cpuUsedCores', 
            average(allocatableCpuCores) as 'allocatableCpuCores',
            average(cpuUsedCores/allocatableCpuCores)*100 as 'cpuUtilizationPercent',
            average(memoryWorkingSetBytes) as 'memoryUsedBytes',
            average(allocatableMemoryBytes) as 'allocatableMemoryBytes',
            average(memoryWorkingSetBytes/allocatableMemoryBytes)*100 as 'memoryUtilizationPercent'
        FROM K8sNodeSample 
        WHERE clusterName = '{cluster_name}'
        SINCE {since_time_ms} UNTIL {until_time_ms}
        TIMESERIES AUTO
        """
        
        return self.query_client.execute_nrql_query(query)
    
    def get_namespace_count(
        self, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get count of namespaces in a Kubernetes cluster.
        
        Args:
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing namespace count data
        """
        params = {
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        query = f"""
        SELECT uniqueCount(namespaceName) as 'namespaceCount'
        FROM K8sNamespaceSample 
        WHERE clusterName = '{cluster_name}'
        SINCE {since_time_ms} UNTIL {until_time_ms}
        """
        
        return self.query_client.execute_nrql_query(query)
    
    def get_pod_counts(
        self, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get count of pods by status in a Kubernetes cluster.
        
        Args:
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing pod count data
        """
        params = {
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        query = f"""
        SELECT 
            uniqueCount(podName) as 'totalPods',
            filter(uniqueCount(podName), WHERE status = 'Running') as 'runningPods',
            filter(uniqueCount(podName), WHERE status = 'Pending') as 'pendingPods',
            filter(uniqueCount(podName), WHERE status = 'Failed') as 'failedPods',
            filter(uniqueCount(podName), WHERE status = 'Unknown') as 'unknownPods'
        FROM K8sPodSample 
        WHERE clusterName = '{cluster_name}'
        SINCE {since_time_ms} UNTIL {until_time_ms}
        """
        
        return self.query_client.execute_nrql_query(query)
    
    def get_container_restarts(
        self, 
        cluster_name: str, 
        namespace: Optional[str] = None,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get top containers by restart count in a Kubernetes cluster.
        
        Args:
            cluster_name: Name of the Kubernetes cluster
            namespace: Optional namespace to filter by
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            limit: Maximum number of results to return
            
        Returns:
            List of query result objects containing container restart data
        """
        params = {
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        namespace_filter = f"AND namespaceName = '{namespace}'" if namespace else ""
        
        query = f"""
        SELECT max(restartCount) as 'restartCount'
        FROM K8sContainerSample 
        WHERE clusterName = '{cluster_name}' {namespace_filter}
        SINCE {since_time_ms} UNTIL {until_time_ms}
        FACET podName, containerName
        LIMIT {limit}
        """
        
        return self.query_client.execute_nrql_query(query)
    
    def get_deployment_status(
        self, 
        cluster_name: str, 
        namespace: Optional[str] = None,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get deployment status in a Kubernetes cluster.
        
        Args:
            cluster_name: Name of the Kubernetes cluster
            namespace: Optional namespace to filter by
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            limit: Maximum number of results to return
            
        Returns:
            List of query result objects containing deployment status data
        """
        params = {
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        namespace_filter = f"AND namespaceName = '{namespace}'" if namespace else ""
        
        query = f"""
        SELECT 
            latest(podsDesired) as 'podsDesired',
            latest(podsReady) as 'podsReady',
            latest(podsAvailable) as 'podsAvailable',
            latest(podsUnavailable) as 'podsUnavailable'
        FROM K8sDeploymentSample 
        WHERE clusterName = '{cluster_name}' {namespace_filter}
        SINCE {since_time_ms} UNTIL {until_time_ms}
        FACET deploymentName, namespaceName
        LIMIT {limit}
        """
        
        return self.query_client.execute_nrql_query(query)
    
    def get_events(
        self, 
        cluster_name: str, 
        event_types: Optional[List[str]] = None,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get Kubernetes events for a cluster.
        
        Args:
            cluster_name: Name of the Kubernetes cluster
            event_types: Optional list of event types to filter by (e.g., ['Warning', 'Normal'])
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            limit: Maximum number of results to return
            
        Returns:
            List of query result objects containing Kubernetes events
        """
        params = {
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        event_type_filter = ""
        if event_types:
            event_type_list = ", ".join([f"'{event_type}'" for event_type in event_types])
            event_type_filter = f"AND event.type IN ({event_type_list})"
            
        query = f"""
        SELECT 
            timestamp,
            event.type as 'eventType',
            event.reason as 'reason',
            event.message as 'message',
            event.count as 'count',
            event.involvedObject.kind as 'kind',
            event.involvedObject.name as 'name',
            event.involvedObject.namespace as 'namespace'
        FROM InfrastructureEvent 
        WHERE category = 'kubernetes' 
        AND clusterName = '{cluster_name}' 
        {event_type_filter}
        SINCE {since_time_ms} UNTIL {until_time_ms}
        LIMIT {limit}
        """
        
        return self.query_client.execute_nrql_query(query)