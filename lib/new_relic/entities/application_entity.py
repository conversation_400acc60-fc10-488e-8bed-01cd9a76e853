"""
Application entity for New Relic queries.
"""

from typing import Dict, List, Any, Optional
from ..nrql_manager import NRQLManager
from ..query import NewRelicQueryClient

class ApplicationEntity:
    """
    Entity class for Application-related queries.
    Provides methods for retrieving application metrics and information using the NRQL Manager.
    """
    
    def __init__(self, query_client: NewRelicQueryClient, nrql_manager: Optional[NRQLManager] = None):
        """
        Initialize the Application entity.
        
        Args:
            query_client: The New Relic query client for executing NRQL
            nrql_manager: Optional NRQL Manager instance (creates a new one if not provided)
        """
        self.query_client = query_client
        self.nrql_manager = nrql_manager or NRQLManager()
    
    def get_error_rate(self, app_name: str, since_minutes: int = 30, period_minutes: int = 1) -> List[Dict[str, Any]]:
        """
        Get error rate for an application.
        
        Args:
            app_name: Name of the application
            since_minutes: Time window in minutes to query data for
            period_minutes: Time period for TIMESERIES grouping
            
        Returns:
            List of query result objects containing error rate data
        """
        nrql = self.nrql_manager.get_query(
            "application",
            "error_rate",
            app_name=app_name,
            since_minutes=since_minutes,
            period_minutes=period_minutes
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_response_time(self, app_name: str, since_minutes: int = 30, period_minutes: int = 1) -> List[Dict[str, Any]]:
        """
        Get response time for an application.
        
        Args:
            app_name: Name of the application
            since_minutes: Time window in minutes to query data for
            period_minutes: Time period for TIMESERIES grouping
            
        Returns:
            List of query result objects containing response time data
        """
        nrql = self.nrql_manager.get_query(
            "application",
            "response_time",
            app_name=app_name,
            since_minutes=since_minutes,
            period_minutes=period_minutes
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_throughput(self, app_name: str, since_minutes: int = 30, period_minutes: int = 1) -> List[Dict[str, Any]]:
        """
        Get throughput for an application.
        
        Args:
            app_name: Name of the application
            since_minutes: Time window in minutes to query data for
            period_minutes: Time period for TIMESERIES grouping
            
        Returns:
            List of query result objects containing throughput data
        """
        nrql = self.nrql_manager.get_query(
            "application",
            "throughput",
            app_name=app_name,
            since_minutes=since_minutes,
            period_minutes=period_minutes
        )
        return self.query_client.execute_nrql(nrql) 