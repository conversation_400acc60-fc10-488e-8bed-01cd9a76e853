"""
Kubernetes Pod entity for New Relic queries.
"""

from typing import Dict, List, Any, Optional
from ..nrql_manager import NRQLManager
from ..query import NewRelicQueryClient

class PodEntity:
    """
    Entity class for Kubernetes Pod-related queries.
    Provides methods for retrieving pod metrics and information using the NRQL Manager.
    """
    
    def __init__(self, query_client: NewRelicQueryClient, nrql_manager: Optional[NRQLManager] = None):
        """
        Initialize the Pod entity.
        
        Args:
            query_client: The New Relic query client for executing NRQL
            nrql_manager: Optional NRQL Manager instance (creates a new one if not provided)
        """
        self.query_client = query_client
        self.nrql_manager = nrql_manager or NRQLManager()
        
    def get_network_io(
        self, 
        pod_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get network I/O metrics for a Kubernetes pod.
        
        Args:
            pod_name: Name of the pod
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing network I/O data
        """
        params = {
            "pod_name": pod_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_pod",
            "network_io",
            **params
        )
        return self.query_client.execute_nrql(nrql)
        
    def get_volume_usage(
        self, 
        pod_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get volume usage metrics for a Kubernetes pod.
        
        Args:
            pod_name: Name of the pod
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing volume usage data
        """
        params = {
            "pod_name": pod_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_pod",
            "volume_usage",
            **params
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_cpu_usage(
        self, 
        pod_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get CPU usage for a Kubernetes pod.
        
        Args:
            pod_name: Name of the pod
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing CPU usage data
        """
        params = {
            "pod_name": pod_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_pod",
            "cpu_usage",
            **params
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_restart_count(
        self, 
        pod_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get container restart count for a Kubernetes pod.
        
        Args:
            pod_name: Name of the pod
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing restart count data
        """
        params = {
            "pod_name": pod_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_pod",
            "restart_count",
            **params
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_memory_usage(
        self, 
        pod_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get memory usage for a Kubernetes pod.
        
        Args:
            pod_name: Name of the pod
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing memory usage data
        """
        params = {
            "pod_name": pod_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_pod",
            "memory_usage",
            **params
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_container_status(
        self, 
        pod_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get container status for all containers in a Kubernetes pod.
        
        Args:
            pod_name: Name of the pod
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing container status data
        """
        params = {
            "pod_name": pod_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_pod",
            "container_status",
            **params
        )
        return self.query_client.execute_nrql(nrql) 