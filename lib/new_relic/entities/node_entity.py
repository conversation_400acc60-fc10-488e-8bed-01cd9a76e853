"""
Kubernetes Node entity for New Relic queries.
"""

from typing import Dict, List, Any, Optional
from ..nrql_manager import NRQLManager
from ..query import NewRelicQueryClient

class NodeEntity:
    """
    Entity class for Kubernetes Node-related queries.
    Provides methods for retrieving node metrics and information using the NRQL Manager.
    """
    
    def __init__(self, query_client: NewRelicQueryClient, nrql_manager: Optional[NRQLManager] = None):
        """
        Initialize the Node entity.
        
        Args:
            query_client: The New Relic query client for executing NRQL
            nrql_manager: Optional NRQL Manager instance (creates a new one if not provided)
        """
        self.query_client = query_client
        self.nrql_manager = nrql_manager or NRQLManager()
        
    def get_network_io(
        self, 
        node_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get network I/O metrics for a Kubernetes node.
        
        Args:
            node_name: Name of the node
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing network I/O data
        """
        params = {
            "node_name": node_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_node",
            "network_io",
            **params
        )
        return self.query_client.execute_nrql(nrql)
        
    def get_disk_usage(
        self, 
        node_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get disk usage metrics for a Kubernetes node.
        
        Args:
            node_name: Name of the node
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing disk usage data
        """
        params = {
            "node_name": node_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_node",
            "disk_usage",
            **params
        )
        return self.query_client.execute_nrql(nrql)
        
    def get_allocatable_resources(
        self, 
        node_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get allocatable resources for a Kubernetes node.
        
        Args:
            node_name: Name of the node
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing allocatable resources data
        """
        params = {
            "node_name": node_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_node",
            "allocatable_resources",
            **params
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_disk_usage(self, node_name: str, since_time_ms: Optional[int] = None, until_time_ms: Optional[int] = None, since_minutes: int = 5) -> List[Dict[str, Any]]:
        """
        Get disk usage for a Kubernetes node.
        
        Args:
            node_name: Name of the node
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing disk usage data
        """
        params = {
            "node_name": node_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_node",
            "disk_usage",
            **params
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_cpu_usage(
        self, 
        node_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get CPU usage for a Kubernetes node.
        
        Args:
            node_name: Name of the node
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing CPU usage data
        """
        params = {
            "node_name": node_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_node",
            "cpu_usage",
            **params
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_memory_usage(
        self, 
        node_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get memory usage for a Kubernetes node.
        
        Args:
            node_name: Name of the node
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing memory usage data
        """
        params = {
            "node_name": node_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_node",
            "memory_usage",
            **params
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_pods_on_node(self, node_name: str, since_minutes: int = 5) -> List[Dict[str, Any]]:
        """
        Get list of pods running on a Kubernetes node.
        
        Args:
            node_name: Name of the node
            since_minutes: Time window in minutes to query data for
            
        Returns:
            List of query result objects containing pod names
        """
        nrql = self.nrql_manager.get_query(
            "kubernetes_node",
            "pods_on_node",
            node_name=node_name,
            since_minutes=since_minutes
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_pod_count(
        self, 
        node_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get pod count for a Kubernetes node.
        
        Args:
            node_name: Name of the node
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing pod count data
        """
        params = {
            "node_name": node_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_node",
            "pod_count",
            **params
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_condition(
        self, 
        node_name: str, 
        cluster_name: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get condition information for a Kubernetes node.
        
        Args:
            node_name: Name of the node
            cluster_name: Name of the Kubernetes cluster
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing node condition data
        """
        params = {
            "node_name": node_name,
            "cluster_name": cluster_name,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "kubernetes_node",
            "condition",
            **params
        )
        return self.query_client.execute_nrql(nrql) 