"""
Host entity for New Relic queries.
"""

from typing import Dict, List, Any, Optional
from ..nrql_manager import NRQLManager
from ..query import NewRelicQueryClient

class HostEntity:
    """
    Entity class for Host-related queries.
    Provides methods for retrieving host metrics and information using the NRQL Manager.
    """
    
    def __init__(self, query_client: NewRelicQueryClient, nrql_manager: Optional[NRQLManager] = None):
        """
        Initialize the Host entity.
        
        Args:
            query_client: The New Relic query client for executing NRQL
            nrql_manager: Optional NRQL Manager instance (creates a new one if not provided)
        """
        self.query_client = query_client
        self.nrql_manager = nrql_manager or NRQLManager()
    
    def get_cpu_memory_disk(
        self, 
        hostname: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get combined CPU, memory, and disk usage for a host.
        
        Args:
            hostname: Name of the host
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing usage data
        """
        params = {
            "hostname": hostname,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "host",
            "cpu_usage",  # Changed from cpu_memory_disk to cpu_usage
            **params
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_processes(
        self, 
        hostname: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get process count for a host.
        
        Args:
            hostname: Name of the host
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing process count data
        """
        params = {
            "hostname": hostname,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "host",
            "processes",
            **params
        )
        return self.query_client.execute_nrql(nrql)
    
    def get_network_io(
        self, 
        hostname: str, 
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        since_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get network I/O metrics for a host.
        
        Args:
            hostname: Name of the host
            since_time_ms: Start time in epoch milliseconds (preferred)
            until_time_ms: End time in epoch milliseconds (preferred)
            since_minutes: Time window in minutes to query data for (deprecated)
            
        Returns:
            List of query result objects containing network I/O data
        """
        params = {
            "hostname": hostname,
        }
        
        # Prefer epoch millisecond timestamps if provided
        if since_time_ms is not None and until_time_ms is not None:
            params["since_time_ms"] = since_time_ms
            params["until_time_ms"] = until_time_ms
        else:
            params["since_minutes"] = since_minutes
            
        nrql = self.nrql_manager.get_query(
            "host",
            "network_io",
            **params
        )
        return self.query_client.execute_nrql(nrql) 