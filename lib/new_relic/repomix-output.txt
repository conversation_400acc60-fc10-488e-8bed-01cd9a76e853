This file is a merged representation of the entire codebase, combined into a single document by Repomix.

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded

Additional Info:
----------------

================================================================
Directory Structure
================================================================
__init__.py
alerts.py
analyzer.py
base.py
client.py
config.py
logs.py
main.py
metrics.py
mutation.py
query.py
README.md
retry_utils.py
utils.py

================================================================
Files
================================================================

================
File: __init__.py
================
"""
New Relic NerdGraph GraphQL client library.
Supports both US and EU endpoints with proper authentication and error handling.
"""

from .base import (
    Region, 
    AlertStatus,
    AlertSeverity,
    GraphQLResponse,
    NewRelicGraphQLError,
    TimeseriesDataPoint,
    MetricResult,
    Alert
)

from .client import NewRelicGraphQLClient
from .query import NewRelicQueryClient
from .mutation import NewRelicMutationClient
from .metrics import NewRelicMetricsClient
from .alerts import NewRelicAlertsClient

__all__ = [
    # Base types
    'Region',
    'AlertStatus',
    'AlertSeverity',
    'GraphQLResponse',
    'NewRelicGraphQLError',
    'TimeseriesDataPoint',
    'MetricResult',
    'Alert',
    
    # Client classes
    'NewRelicGraphQLClient',
    'NewRelicQueryClient',
    'NewRelicMutationClient',
    'NewRelicMetricsClient',
    'NewRelicAlertsClient'
]

================
File: alerts.py
================
"""
New Relic GraphQL client for alert operations.
"""

from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

from .base import UTC, Alert, AlertStatus, AlertSeverity, safe_enum_parse, parse_timestamp
from .client import NewRelicGraphQLClient


class NewRelicAlertsClient:
    """
    Client for New Relic alerts operations.
    Provides methods for fetching and processing alert data.
    """
    
    def __init__(self, client: NewRelicGraphQLClient):
        """
        Initialize the New Relic alerts client.
        
        Args:
            client: The core New Relic GraphQL client instance
        """
        self.client = client
    
    def get_alerts(
        self,
        account_id: Optional[str] = None,
        status: Optional[AlertStatus] = None,
        entity_guid: Optional[str] = None,
        since: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Alert]:
        """
        Fetch New Relic issues/incidents.

        Args:
            account_id: Account ID (overrides default if set)
            status: Optional filter by alert status
            entity_guid: Optional filter by entity GUID
            since: Optional filter for alerts since a timestamp
            limit: Maximum number of alerts to return

        Returns:
            List of Alert objects
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)
        since_filter = f", createdAt: {int(since.timestamp() * 1000)}" if since else ""
        status_value = f", states: [{status.name}]" if status else ""
        entity_filter = f", entityGuids: [\"{entity_guid}\"]" if entity_guid else ""
        
        graphql_query = f"""
        query($accountId: Int!) {{
            actor {{
                account(id: $accountId) {{
                    aiIssues {{
                        issues {{
                            issues {{
                                account {{
                                    id
                                    name
                                }}
                                acknowledgedAt
                                acknowledgedBy
                                activatedAt
                                closedAt
                                closedBy
                                conditionName
                                createdAt
                                deepLinkUrl
                                description
                                entityGuids
                                entityNames
                                issueId
                                policyName
                                priority
                                state
                                title
                                totalIncidents
                                updatedAt
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """

        variables = {"accountId": int(account_id)}
        response = self.client.execute_query(graphql_query, variables)
        
        alerts = []
        try:
            issues = response.data["actor"]["account"]["aiIssues"]["issues"]["issues"]
            for issue in issues:
                alerts.append(self._parse_alert(issue))
        except (KeyError, TypeError) as e:
            raise Exception(f"Failed to parse alerts: {str(e)}")
        
        return alerts

    def _parse_alert(self, issue: Dict[str, Any]) -> Alert:
        """
        Parse an issue from the API response into an Alert object.
        
        Args:
            issue: Raw issue data from API
            
        Returns:
            Parsed Alert object
        """
        title = issue.get("title", "")
        description = issue.get("description", "")
        
        # Handle both string and list formats for title and description
        if isinstance(title, list) and title:
            title = title[0]
        if isinstance(description, list) and description:
            description = description[0]
            
        # Parse timestamps with proper error handling
        def parse_timestamp(ts_value):
            if not ts_value:
                return None
            try:
                return datetime.fromtimestamp(ts_value / 1000, UTC)
            except (ValueError, TypeError):
                return None
        
        # Parse status and severity with fallbacks
        try:
            status = AlertStatus(issue.get("state", "").lower())
        except (ValueError, AttributeError):
            status = AlertStatus.FIRING  # Default
            
        try:
            severity = AlertSeverity(issue.get("priority", "").lower())
        except (ValueError, AttributeError):
            severity = AlertSeverity.INFO  # Default
        
        return Alert(
            id=issue.get("issueId", ""),
            name=title,
            status=safe_enum_parse(AlertStatus, issue.get("state"), AlertStatus.FIRING),
            severity=safe_enum_parse(AlertSeverity, issue.get("priority"), AlertSeverity.INFO),
            description=description,
            created_at=parse_timestamp(issue.get("createdAt")),
            updated_at=parse_timestamp(issue.get("updatedAt")),
            closed_at=parse_timestamp(issue.get("closedAt")),
            acknowledged_at=parse_timestamp(issue.get("acknowledgedAt")),
            acknowledged_by=issue.get("acknowledgedBy"),
            closed_by=issue.get("closedBy"),
            policy_name=issue.get("policyName"),
            condition_name=issue.get("conditionName"),
            entity_guids=issue.get("entityGuids"),
            deep_link_url=issue.get("deepLinkUrl"),
            total_incidents=issue.get("totalIncidents")
        )

    def acknowledge_alert(
        self,
        issue_id: str,
        account_id: Optional[str] = None
    ) -> bool:
        """
        Acknowledge a New Relic alert.
        
        Args:
            issue_id: ID of the issue to acknowledge
            account_id: Account ID (overrides default if set)
            
        Returns:
            True if successful
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $issueId: ID!) {
            aiIssuesAcknowledgeIssue(
                accountId: $accountId,
                id: $issueId
            ) {
                issue {
                    issueId
                    state
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "issueId": issue_id
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            result = response.data["aiIssuesAcknowledgeIssue"]["issue"]
            return result["state"].lower() == "acknowledged"
        except (KeyError, TypeError):
            raise Exception("Failed to acknowledge alert")

    def close_alert(
        self,
        issue_id: str,
        account_id: Optional[str] = None
    ) -> bool:
        """
        Close a New Relic alert.
        
        Args:
            issue_id: ID of the issue to close
            account_id: Account ID (overrides default if set)
            
        Returns:
            True if successful
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $issueId: ID!) {
            aiIssuesCloseIssue(
                accountId: $accountId,
                id: $issueId
            ) {
                issue {
                    issueId
                    state
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "issueId": issue_id
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            result = response.data["aiIssuesCloseIssue"]["issue"]
            return result["state"].lower() == "closed"
        except (KeyError, TypeError):
            raise Exception("Failed to close alert")

================
File: analyzer.py
================
"""
New Relic entity analyzer for comprehensive information gathering about affected entities.
"""

import os
import re
import yaml
from typing import Any, Dict, List, Optional, Union, Set, Tuple, Pattern
from datetime import datetime, timedelta
import logging

from .base import UTC, Alert, NewRelicGraphQLError
from .client import NewRelicGraphQLClient
from .query import NewRelicQueryClient
from .metrics import NewRelicMetricsClient
from .logs import NewRelicLogsClient

logger = logging.getLogger(__name__)


class EntityDetails:
    """Container for entity details and associated data"""

    def __init__(
        self,
        entity_guid: str,
        entity_name: str,
        entity_type: str,
        cluster_id: Optional[str] = None,
        product: Optional[str] = None,
        region: Optional[str] = None,
    ):
        self.entity_guid = entity_guid
        self.entity_name = entity_name
        self.entity_type = entity_type
        self.cluster_id = cluster_id
        self.product = product
        self.region = region
        self.metrics = {}
        self.logs = []
        self.events = []
        self.related_entities = []
        self.metadata = {}
        self.relationships = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert the entity details to a dictionary"""
        return {
            "entity_guid": self.entity_guid,
            "entity_name": self.entity_name,
            "entity_type": self.entity_type,
            "cluster_id": self.cluster_id,
            "product": self.product,
            "region": self.region,
            "metrics": self.metrics,
            "logs": self.logs,
            "events": self.events,
            "related_entities": self.related_entities,
            "metadata": self.metadata,
            "relationships": self.relationships,
        }


class EntityRelationshipMapper:
    """
    Maps relationships between different New Relic entities.
    Uses a configuration file to define relationship patterns.
    """

    # Common entity types and their corresponding New Relic entity type values
    ENTITY_TYPES = {
        "pod": ["KUBERNETES_POD", "K8S_POD"],
        "node": ["KUBERNETES_NODE", "K8S_NODE", "HOST"],
        "cronjob": ["KUBERNETES_CRONJOB", "K8S_CRONJOB"],
        "deployment": ["KUBERNETES_DEPLOYMENT", "K8S_DEPLOYMENT"],
        "service": ["KUBERNETES_SERVICE", "K8S_SERVICE"],
        "container": ["CONTAINER"],
        "application": ["APPLICATION"],
    }

    # Common cluster prefixes for different products
    CLUSTER_PATTERNS = {
        "neurons": {
            "nvu": ["aks-edge-rg-nvu-prd-neurons", "aks-rg-nvu-prd-neurons"],
            "uku": ["aks-rg-uku-prd-neurons", "aks-edge-rg-uku-prd-neurons"],
            "mlu": ["aks-edge-rg-mlu-prd-neurons", "aks-rg-mlu-prd-neurons"],
            "ttu": ["aks-edge-rg-ttu-prd-neurons", "aks-rg-ttu-prd-neurons"],
            "tku": ["aks-edge-rg-tku-prd-neurons", "aks-rg-tku-prd-neurons"],
        },
        "mdm": {
            "na1": ["na1", "primary-na1", "na1-eks", "north-america-1"],
            "na2": ["na2", "primary-na2", "na2-eks", "north-america-2"],
            "ap1": ["ap1", "primary-ap1", "ap1-eks", "asia-pacific-1"],
            "ap2": ["ap2", "primary-ap2", "ap2-eks", "asia-pacific-2"],
        },
    }

    def __init__(
        self,
        query_client: NewRelicQueryClient,
        config_path: Optional[str] = None,
        debug: bool = False,
    ):
        """
        Initialize the entity relationship mapper.

        Args:
            query_client: An initialized NewRelicQueryClient
            config_path: Path to the entity relationships config file
            debug: Enable debug mode for verbose output
        """
        self.query = query_client
        self.debug = debug

        # Load relationship configuration
        if config_path is None:
            module_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(
                module_dir, "..", "..", "config", "entity_relationships.yaml"
            )

        self.relationships_config = self._load_relationships_config(config_path)

    def _load_relationships_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load entity relationships configuration from YAML file.

        Args:
            config_path: Path to the configuration file

        Returns:
            Parsed configuration dictionary
        """
        if not os.path.exists(config_path):
            logger.warning(
                f"Entity relationships config file not found at {config_path}"
            )
            return {"relationships": []}

        try:
            with open(config_path, "r") as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"Error loading entity relationships config: {str(e)}")
            return {"relationships": []}

    def is_entity_type(self, entity_type: str, generic_type: str) -> bool:
        """
        Check if an entity is of a specific generic type.

        Args:
            entity_type: Entity type string from New Relic
            generic_type: Generic type to check ("pod", "node", etc.)

        Returns:
            True if the entity matches the generic type, False otherwise
        """
        generic_type = generic_type.lower()
        if generic_type in self.ENTITY_TYPES:
            return entity_type in self.ENTITY_TYPES[generic_type]
        return False

    def map_entity_relationships(
        self,
        entity_details: Dict[str, Any],
        query_client: NewRelicQueryClient,
        since_time: datetime,
        until_time: datetime,
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Map relationships for a specific entity.

        Args:
            entity_details: Entity details dictionary
            query_client: NewRelicQueryClient for querying related entities
            since_time: Start time for relationship mapping
            until_time: End time for relationship mapping

        Returns:
            Dictionary of relationships by type
        """
        entity_type = entity_details.get("type", "")
        entity_name = entity_details.get("name", "")
        relationships = {}

        # Handle Kubernetes pod -> node relationship
        if self.is_entity_type(entity_type, "pod"):
            # Find the node this pod is running on
            node = self._find_pod_node_relationship(
                entity_name,
                entity_details.get("cluster_id", "unknown"),
                query_client,
                since_time,
                until_time,
            )

            if node:
                relationships["node"] = [node]

                # Find the cluster this node belongs to
                cluster = self._find_node_cluster_relationship(
                    node["name"], query_client, since_time, until_time
                )

                if cluster:
                    relationships["cluster"] = [cluster]

        # Handle Kubernetes node -> cluster relationship
        elif self.is_entity_type(entity_type, "node"):
            # Find the cluster this node belongs to
            cluster = self._find_node_cluster_relationship(
                entity_name, query_client, since_time, until_time
            )

            if cluster:
                relationships["cluster"] = [cluster]

            # Find pods running on this node
            pods = self._find_node_pods_relationship(
                entity_name, query_client, since_time, until_time
            )

            if pods:
                relationships["pods"] = pods

        return relationships

    def _find_pod_node_relationship(
        self,
        pod_name: str,
        cluster_id: str,
        query_client: NewRelicQueryClient,
        since_time: datetime,
        until_time: datetime,
    ) -> Optional[Dict[str, Any]]:
        """
        Find the node relationship for a pod.

        Args:
            pod_name: Name of the pod
            cluster_id: Cluster ID
            query_client: Query client for executing NRQL
            since_time: Start time for the query
            until_time: End time for the query

        Returns:
            Node relationship details or None if not found
        """
        # Query New Relic for the node name using the query client
        query = f"""
        SELECT nodeName, entityGuid as nodeEntityGuid
        FROM K8sPodSample
        WHERE podName = '{pod_name}'
        {"AND clusterName LIKE '%" + cluster_id + "%'" if cluster_id and cluster_id != "unknown" else ""}
        SINCE '{since_time.isoformat()}'
        UNTIL '{until_time.isoformat()}'
        LIMIT 1
        """

        try:
            # Use query_client to execute the query instead of direct execution
            results = query_client.execute_nrql(query)

            if results and "nodeName" in results[0]:
                return {
                    "type": "node",
                    "name": results[0]["nodeName"],
                    "entity_guid": results[0].get("nodeEntityGuid"),
                    "relationship": "runs_on",
                }
        except Exception as e:
            if self.debug:
                logger.error(f"Error finding pod node relationship: {str(e)}")

        return None

    def _find_node_cluster_relationship(
        self,
        node_name: str,
        query_client: NewRelicQueryClient,
        since_time: datetime,
        until_time: datetime,
    ) -> Optional[Dict[str, Any]]:
        """
        Find the cluster relationship for a node.

        Args:
            node_name: Name of the node
            query_client: Query client for executing NRQL
            since_time: Start time for the query
            until_time: End time for the query

        Returns:
            Cluster relationship details or None if not found
        """
        query = f"""
        SELECT clusterName
        FROM K8sNodeSample
        WHERE nodeName = '{node_name}'
        SINCE '{since_time.isoformat()}'
        UNTIL '{until_time.isoformat()}'
        LIMIT 1
        """

        try:
            # Use query_client to execute the query
            results = query_client.execute_nrql(query)

            if results and "clusterName" in results[0]:
                return {
                    "type": "cluster",
                    "name": results[0]["clusterName"],
                    "relationship": "belongs_to",
                }
        except Exception as e:
            if self.debug:
                logger.error(f"Error finding node cluster relationship: {str(e)}")

        return None

    def _find_node_pods_relationship(
        self,
        node_name: str,
        query_client: NewRelicQueryClient,
        since_time: datetime,
        until_time: datetime,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        Find pods relationship for a node.

        Args:
            node_name: Name of the node
            query_client: Query client for executing NRQL
            since_time: Start time for the query
            until_time: End time for the query
            limit: Maximum number of pods to return

        Returns:
            List of pod relationship details
        """
        query = f"""
        SELECT podName, entityGuid
        FROM K8sPodSample
        WHERE nodeName = '{node_name}'
        SINCE '{since_time.isoformat()}'
        UNTIL '{until_time.isoformat()}'
        LIMIT {limit}
        """

        try:
            # Use query_client to execute the query
            results = query_client.execute_nrql(query)

            pods = []
            for pod in results:
                if "podName" in pod:
                    pods.append(
                        {
                            "type": "pod",
                            "name": pod["podName"],
                            "entity_guid": pod.get("entityGuid"),
                            "relationship": "runs_on",
                        }
                    )

            return pods
        except Exception as e:
            if self.debug:
                logger.error(f"Error finding node pods relationship: {str(e)}")

        return []

    def get_entity_relationships_for_alert(
        self,
        alert_condition: str,
        entity_name: Optional[str] = None,
        entity_type: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Get potential related entities for an alert based on configuration.

        Args:
            alert_condition: Alert condition name or description
            entity_name: Optional entity name to filter relationships
            entity_type: Optional entity type to filter relationships

        Returns:
            List of potential related entities
        """
        potential_entities = []

        # Go through the relationships defined in config
        for relationship in self.relationships_config.get("relationships", []):
            source = relationship.get("source", {})

            # Check if this relationship applies to the alert condition
            condition_pattern = source.get("condition")
            if condition_pattern and re.search(
                condition_pattern, alert_condition, re.IGNORECASE
            ):
                # This relationship applies to the alert
                for target in relationship.get("target_entities", []):
                    target_type = target.get("type")
                    target_pattern = target.get("pattern")
                    target_metrics = target.get("metrics", [])

                    # If entity_type specified, check if it matches
                    if entity_type and target_type != entity_type:
                        continue

                    # If entity_name specified, check if it matches pattern
                    if entity_name and target_pattern:
                        if not re.search(target_pattern, entity_name, re.IGNORECASE):
                            continue

                    # Add this target entity to the list
                    potential_entities.append(
                        {
                            "type": target_type,
                            "pattern": target_pattern,
                            "metrics": target_metrics,
                        }
                    )

        return potential_entities


class EntityAnalyzer:
    """
    Performs comprehensive analysis of New Relic entities.
    Gets entity details and relationship mappings, but delegates metrics
    and logs collection to specialized classes.
    """

    def __init__(self, client: NewRelicGraphQLClient, debug: bool = False):
        """
        Initialize the entity analyzer.

        Args:
            client: An initialized NewRelicGraphQLClient
            debug: Enable debug mode for verbose output
        """
        self.client = client
        self.query = NewRelicQueryClient(client)
        self.metrics = NewRelicMetricsClient(client)
        self.logs = NewRelicLogsClient(client)
        self.relationship_mapper = EntityRelationshipMapper(self.query, debug=debug)
        self.debug = debug

    async def get_entity_details(self, entity_guid: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about an entity from New Relic.
        Public async method that delegates to the private implementation.

        Args:
            entity_guid: New Relic entity GUID

        Returns:
            Entity details dictionary or None if not found
        """
        # Simply call the non-async version for now
        # In a real async implementation, we'd make the GraphQL client truly async
        return self._get_entity_details(entity_guid)
        
    async def get_related_entities(self, entity_guid: str) -> List[Dict[str, Any]]:
        """
        Get entities related to the specified entity.
        Public async method that delegates to the non-async implementation.

        Args:
            entity_guid: New Relic entity GUID

        Returns:
            List of related entities with their details
        """
        # First get the entity details
        entity_details = await self.get_entity_details(entity_guid)
        
        if not entity_details:
            return []
            
        # Then map relationships - use current time for window
        now = datetime.now(UTC)
        since_time = now - timedelta(hours=1)  # 1 hour window
        until_time = now
        
        # Use the relationship mapper to find related entities
        related = self.relationship_mapper.map_entity_relationships(
            entity_details, self.query, since_time, until_time
        )
        
        # Format the results as a flat list of entities
        result = []
        for rel_type, entities in related.items():
            for entity in entities:
                if "entity_guid" in entity and entity["entity_guid"]:
                    # Add relationship type to each entity
                    entity["relationship_type"] = rel_type
                    result.append(entity)
        
        return result

    def analyze_entity(
        self,
        entity_guid: str,
        since_time: datetime,
        until_time: datetime,
        product: Optional[str] = None,
        region: Optional[str] = None,
        collect_metrics: bool = False,
        collect_logs: bool = False,
        collect_events: bool = False,
    ) -> Dict[str, Any]:
        """
        Analyze an entity to get comprehensive information.

        Args:
            entity_guid: Entity GUID
            since_time: Start time for data collection
            until_time: End time for data collection
            product: Product (optional)
            region: Region (optional)
            collect_metrics: Whether to collect metrics (optional, default False)
            collect_logs: Whether to collect logs (optional, default False)
            collect_events: Whether to collect events (optional, default False)

        Returns:
            Dictionary with entity details and associated data
        """
        # Get entity details using the query client
        entity_details = self._get_entity_details(entity_guid)

        if not entity_details:
            raise ValueError(f"Entity with GUID {entity_guid} not found")

        # Extract entity details
        entity_name = entity_details.get("name", "unknown")
        entity_type = entity_details.get("type", "unknown")

        # Extract cluster ID from tags if it's a Kubernetes entity
        cluster_id = "unknown"
        if is_pod_entity(entity_type) or is_node_entity(entity_type):
            for tag in entity_details.get("tags", []):
                if tag.get("key") == "clusterName":
                    cluster_id = (
                        tag.get("values", [])[0] if tag.get("values") else "unknown"
                    )
                    break

        # Create entity data object
        entity_data = EntityDetails(
            entity_guid=entity_guid,
            entity_name=entity_name,
            entity_type=entity_type,
            cluster_id=cluster_id,
            product=product,
            region=region,
        )

        # Get metadata
        entity_data.metadata = self._get_entity_metadata(entity_details)

        # Map relationships - this is the core responsibility of EntityAnalyzer
        entity_data.relationships = self.relationship_mapper.map_entity_relationships(
            entity_details, self.query, since_time, until_time
        )

        # Identify metrics to collect (but don't collect them unless requested)
        metrics_config = self._identify_metrics_to_collect(entity_type)
        entity_data.metrics_to_collect = metrics_config

        # Identify logs to collect (but don't collect them unless requested)
        logs_config = self._identify_logs_to_collect(entity_type, cluster_id)
        entity_data.logs_to_collect = logs_config

        # Optionally collect metrics
        if collect_metrics:
            entity_data.metrics = self._collect_entity_metrics(
                entity_guid, entity_type, since_time, until_time, metrics_config
            )

        # Optionally collect logs
        if collect_logs:
            entity_data.logs = self._collect_entity_logs(
                entity_guid, entity_type, since_time, until_time, logs_config, limit=100
            )

        # Optionally collect Kubernetes events if applicable
        if collect_events and (
            is_pod_entity(entity_type) or is_node_entity(entity_type)
        ):
            entity_data.events = self._collect_entity_events(
                entity_name, entity_type, cluster_id, since_time, until_time
            )

        return entity_data.to_dict()

    def _analyze_entity_sync(
        self,
        entity_guid: str,
        since_time: datetime,
        until_time: datetime,
        product: Optional[str] = None,
        region: Optional[str] = None,
        collect_metrics: bool = False,
        collect_logs: bool = False,
        collect_events: bool = False,
    ) -> Dict[str, Any]:
        """
        Analyze an entity to get comprehensive information (sync version).
        
        Args:
            entity_guid: Entity GUID
            since_time: Start time for data collection
            until_time: End time for data collection
            product: Product (optional)
            region: Region (optional)
            collect_metrics: Whether to collect metrics (optional, default False)
            collect_logs: Whether to collect logs (optional, default False)
            collect_events: Whether to collect events (optional, default False)

        Returns:
            Dictionary with entity details and associated data
        """
        # Get entity details using the query client
        entity_details = self._get_entity_details(entity_guid)

        if not entity_details:
            raise ValueError(f"Entity with GUID {entity_guid} not found")

        # Extract entity details
        entity_name = entity_details.get("name", "unknown")
        entity_type = entity_details.get("type", "unknown")

        # Extract cluster ID from tags if it's a Kubernetes entity
        cluster_id = "unknown"
        if is_pod_entity(entity_type) or is_node_entity(entity_type):
            for tag in entity_details.get("tags", []):
                if tag.get("key") == "clusterName":
                    cluster_id = (
                        tag.get("values", [])[0] if tag.get("values") else "unknown"
                    )
                    break

        # Create entity data object
        entity_data = EntityDetails(
            entity_guid=entity_guid,
            entity_name=entity_name,
            entity_type=entity_type,
            cluster_id=cluster_id,
            product=product,
            region=region,
        )

        # Get metadata
        entity_data.metadata = self._get_entity_metadata(entity_details)

        # Map relationships - this is the core responsibility of EntityAnalyzer
        entity_data.relationships = self.relationship_mapper.map_entity_relationships(
            entity_details, self.query, since_time, until_time
        )

        # Identify metrics to collect (but don't collect them unless requested)
        metrics_config = self._identify_metrics_to_collect(entity_type)
        entity_data.metrics_to_collect = metrics_config

        # Identify logs to collect (but don't collect them unless requested)
        logs_config = self._identify_logs_to_collect(entity_type, cluster_id)
        entity_data.logs_to_collect = logs_config

        # Optionally collect metrics
        if collect_metrics:
            entity_data.metrics = self._collect_entity_metrics(
                entity_guid, entity_type, since_time, until_time, metrics_config
            )

        # Optionally collect logs
        if collect_logs:
            entity_data.logs = self._collect_entity_logs(
                entity_guid, entity_type, since_time, until_time, logs_config, limit=100
            )

        # Optionally collect Kubernetes events if applicable
        if collect_events and (
            is_pod_entity(entity_type) or is_node_entity(entity_type)
        ):
            entity_data.events = self._collect_entity_events(
                entity_name, entity_type, cluster_id, since_time, until_time
            )

        return entity_data.to_dict()

    async def analyze_entity(
        self,
        entity_guid: str,
        since_time: datetime,
        until_time: datetime,
        product: Optional[str] = None,
        region: Optional[str] = None,
        collect_metrics: bool = False,
        collect_logs: bool = False,
        collect_events: bool = False,
    ) -> Dict[str, Any]:
        """
        Analyze an entity to get comprehensive information (async version).

        Args:
            entity_guid: Entity GUID
            since_time: Start time for data collection
            until_time: End time for data collection
            product: Product (optional)
            region: Region (optional)
            collect_metrics: Whether to collect metrics (optional, default False)
            collect_logs: Whether to collect logs (optional, default False)
            collect_events: Whether to collect events (optional, default False)

        Returns:
            Dictionary with entity details and associated data
        """
        # Simply call the non-async version for now
        # In a real async implementation, we'd make the underlying calls truly async
        return self._analyze_entity_sync(
            entity_guid=entity_guid,
            since_time=since_time,
            until_time=until_time,
            product=product,
            region=region,
            collect_metrics=collect_metrics,
            collect_logs=collect_logs,
            collect_events=collect_events
        )

    def _get_entity_details(self, entity_guid: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about an entity from New Relic.

        Args:
            entity_guid: New Relic entity GUID

        Returns:
            Entity details dictionary or None if not found
        """
        try:
            # Use query client's method to get entity details
            result = self.query.get_entity_details(entity_guid)
            return result
        except Exception as e:
            if self.debug:
                logger.error(
                    f"Error fetching entity details for {entity_guid}: {str(e)}"
                )
            return None

    def _get_entity_metadata(self, entity_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract metadata from entity details based on the entity type.

        Args:
            entity_details: Entity details dictionary

        Returns:
            Metadata dictionary
        """
        metadata = {}

        # Extract tags
        tags = {}
        for tag in entity_details.get("tags", []):
            tags[tag.get("key")] = tag.get("values")
        metadata["tags"] = tags

        # Extract type-specific metadata
        entity_type = entity_details.get("type")

        if is_pod_entity(entity_type):
            metadata.update(
                {
                    "pod_name": entity_details.get("podName"),
                    "namespace": entity_details.get("namespaceName"),
                    "cluster_name": entity_details.get("clusterName"),
                }
            )
        elif is_node_entity(entity_type):
            metadata.update(
                {
                    "node_name": entity_details.get("nodeName"),
                    "cluster_name": entity_details.get("clusterName"),
                }
            )
        elif entity_type == "CONTAINER":
            metadata.update(
                {
                    "container_id": entity_details.get("containerId"),
                    "image_name": entity_details.get("imageName"),
                }
            )

        return metadata
        
    def _identify_metrics_to_collect(self, entity_type: str) -> Dict[str, Any]:
        """
        Identify which metrics should be collected for this entity type.
        
        Args:
            entity_type: Entity type
            
        Returns:
            Dictionary of metric configurations
        """
        # Delegate to metrics client for configuration
        return self.metrics.get_metrics_config_for_entity_type(entity_type)
        
    def _identify_logs_to_collect(self, entity_type: str, cluster_id: str) -> Dict[str, Any]:
        """
        Identify which logs should be collected for this entity type.
        
        Args:
            entity_type: Entity type
            cluster_id: Cluster ID
            
        Returns:
            Dictionary of log configurations
        """
        # Delegate to logs client for configuration
        return self.logs.get_logs_config_for_entity_type(entity_type, cluster_id)

    def _collect_entity_metrics(
        self,
        entity_guid: str,
        entity_type: str,
        since_time: datetime,
        until_time: datetime,
        metrics_config: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Collect metrics for an entity using metrics client.

        Args:
            entity_guid: New Relic entity GUID
            entity_type: Entity type
            since_time: Start time for metrics
            until_time: End time for metrics
            metrics_config: Optional metrics configuration

        Returns:
            Dictionary of metrics data
        """
        try:
            # Use the metrics client to get entity metrics
            return self.metrics.get_entity_metrics_by_type(
                entity_guid=entity_guid,
                entity_type=entity_type,
                since=since_time,
                until=until_time,
                metrics_config=metrics_config,
            )
        except Exception as e:
            if self.debug:
                logger.error(f"Error collecting metrics for {entity_guid}: {str(e)}")
            return {}

    def _collect_entity_logs(
        self,
        entity_guid: str,
        entity_type: str,
        since_time: datetime,
        until_time: datetime,
        logs_config: Dict[str, Any] = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        Collect logs for an entity using logs client.

        Args:
            entity_guid: Entity GUID
            entity_type: Entity type
            since_time: Start time for logs
            until_time: End time for logs
            logs_config: Optional logs configuration
            limit: Maximum number of logs to return

        Returns:
            List of log entries
        """
        try:
            # Use the logs client to get entity logs
            return self.logs.get_entity_logs(
                entity_guid=entity_guid,
                entity_type=entity_type,
                since=since_time,
                until=until_time,
                logs_config=logs_config,
                limit=limit,
            )
        except Exception as e:
            if self.debug:
                logger.error(f"Error collecting logs for {entity_guid}: {str(e)}")
            return []

    def _collect_entity_events(
        self,
        entity_name: str,
        entity_type: str,
        cluster_id: str,
        since_time: datetime,
        until_time: datetime,
    ) -> List[Dict[str, Any]]:
        """
        Collect Kubernetes events for an entity.

        Args:
            entity_name: Entity name
            entity_type: Entity type
            cluster_id: Cluster ID
            since_time: Start time for events
            until_time: End time for events

        Returns:
            List of Kubernetes events
        """
        events = []
        object_kind = "Pod" if is_pod_entity(entity_type) else "Node"

        try:
            # Use query client to get Kubernetes events
            entity_events = self.query.get_kubernetes_events(
                object_name=entity_name,
                object_kind=object_kind,
                cluster_name=cluster_id,
                since=since_time,
                until=until_time,
            )

            # Add source field to entity events
            for event in entity_events:
                event["source"] = object_kind.lower()

            events.extend(entity_events)

            # For pods, also get events for the underlying node
            if is_pod_entity(entity_type):
                # Find the node for this pod using query client
                node_name = self.query.find_pod_node(
                    pod_name=entity_name,
                    cluster_id=cluster_id,
                    since_time=since_time,
                    until_time=until_time,
                )

                if node_name:
                    # Get events for this node
                    node_events = self.query.get_kubernetes_events(
                        object_name=node_name,
                        object_kind="Node",
                        cluster_name=cluster_id,
                        since=since_time,
                        until=until_time,
                    )

                    # Add source field to node events
                    for event in node_events:
                        event["source"] = "node"

                    events.extend(node_events)

            return events

        except Exception as e:
            if self.debug:
                logger.error(f"Error collecting events for {entity_name}: {str(e)}")
            return []


# Utility functions to avoid duplicated code
def is_pod_entity(entity_type: str) -> bool:
    """Check if an entity is a Kubernetes pod."""
    return entity_type in ["KUBERNETES_POD", "K8S_POD"]


def is_node_entity(entity_type: str) -> bool:
    """Check if an entity is a Kubernetes node."""
    return entity_type in ["KUBERNETES_NODE", "K8S_NODE"]

================
File: base.py
================
"""
Base types and utilities for New Relic GraphQL client.
"""

import json
from enum import Enum
from typing import Any, Dict, List, Optional, TypeVar, Union, Generic
from datetime import datetime, timezone
from pydantic import BaseModel, Field

# Use timezone consistently instead of UTC constant
UTC = timezone.utc

T = TypeVar('T')


class Region(Enum):
    """New Relic regions for NerdGraph API"""
    US = "https://api.newrelic.com/graphql"
    EU = "https://api.eu.newrelic.com/graphql"


class AlertStatus(Enum):
    """Alert status values"""
    FIRING = "open"
    RESOLVED = "closed"
    ACKNOWLEDGED = "acknowledged"


class AlertSeverity(Enum):
    """Alert severity values"""
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"


class NewRelicGraphQLError(Exception):
    """Custom exception for New Relic GraphQL errors"""
    
    def __init__(self, message: str, errors: Optional[List[Dict[str, Any]]] = None):
        self.errors = errors or []
        error_details = f": {json.dumps(errors)}" if errors else ""
        super().__init__(f"{message}{error_details}")


class GraphQLResponse(BaseModel, Generic[T]):
    """Structured response from NerdGraph API with generic data type"""
    data: Optional[T] = None
    errors: Optional[List[Dict[str, Any]]] = None

    @property
    def has_errors(self) -> bool:
        """Check if the response contains errors"""
        return self.errors is not None and len(self.errors) > 0


class TimeseriesDataPoint(BaseModel):
    """Represents a single timeseries data point"""
    timestamp: datetime
    value: float


class MetricResult(BaseModel):
    """Represents metric query results"""
    name: str
    timeseries: List[TimeseriesDataPoint]
    metadata: Dict[str, Any]


class Alert(BaseModel):
    """Represents a New Relic alert/issue"""
    id: str
    name: str
    status: AlertStatus
    severity: AlertSeverity
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    closed_by: Optional[str] = None
    policy_name: Optional[str] = None
    condition_name: Optional[str] = None
    entity_guids: Optional[List[str]] = None
    deep_link_url: Optional[str] = None
    total_incidents: Optional[int] = None


def format_datetime(dt: datetime) -> str:
    """
    Format a datetime object for use in New Relic queries.
    
    Args:
        dt: The datetime to format
        
    Returns:
        Formatted datetime string in YYYY-MM-DD HH:MM:SS format as required by New Relic
    """
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def parse_timestamp(ts_value: Optional[int]) -> Optional[datetime]:
    """
    Parse a timestamp value from New Relic API into a datetime object.
    
    Args:
        ts_value: Timestamp in milliseconds since epoch or None
        
    Returns:
        Parsed datetime or None if invalid
    """
    if not ts_value:
        return None
    try:
        return datetime.fromtimestamp(ts_value / 1000, UTC)
    except (ValueError, TypeError):
        return None


def safe_enum_parse(enum_class, value: Optional[str], default):
    """
    Safely parse an enum value with a fallback.
    
    Args:
        enum_class: The Enum class to use
        value: The string value to parse
        default: The default enum value if parsing fails
        
    Returns:
        Parsed enum value or default
    """
    if not value:
        return default
    try:
        return enum_class(value.lower())
    except (ValueError, AttributeError):
        return default

================
File: client.py
================
"""
Core New Relic GraphQL client implementation.
"""

import requests
import json
import pprint
from typing import Any, Dict, List, Optional, Union, Callable, cast

from .base import Region, GraphQLResponse, NewRelicGraphQLError, T, format_datetime
from .retry_utils import nr_retry


class NewRelicGraphQLClient:
    """
    Client for interacting with New Relic's NerdGraph GraphQL API.
    Provides core functionality for executing GraphQL queries.
    """
    
    def __init__(
        self, 
        api_key: str, 
        region: Region = Region.US, 
        account_id: Optional[str] = None, 
        timeout: int = 30,
        debug: bool = False,
        debug_request: bool = False,
        debug_response: bool = False,
        debug_errors: bool = False,
        debug_callback: Optional[Callable[[str, Any], bool]] = None
    ):
        """
        Initialize the New Relic GraphQL client.
        
        Args:
            api_key: New Relic API key
            region: API region (US or EU)
            account_id: Default account ID to use for queries
            timeout: Request timeout in seconds
            debug: Enable all debug logging
            debug_request: Debug only request information
            debug_response: Debug only response information
            debug_errors: Debug only error information
            debug_callback: Custom callback function for debug filtering
        """
        self.api_key = api_key
        self.region = region
        self.account_id = account_id
        self.timeout = timeout
        self.debug = debug
        self.debug_request = debug_request or debug
        self.debug_response = debug_response or debug
        self.debug_errors = debug_errors or debug
        self.debug_callback = debug_callback
        self.headers = {
            'Content-Type': 'application/json',
            'API-Key': self.api_key
        }
    
    def _should_debug(self, section: str, data: Any = None) -> bool:
        """
        Determine if debugging should be enabled for a specific section.
        
        Args:
            section: The section being debugged ('request', 'response', or 'error')
            data: Optional data for the debug callback
            
        Returns:
            True if debugging should be enabled for this section
        """
        if self.debug_callback:
            return self.debug_callback(section, data)
        
        if section == 'request':
            return self.debug_request
        elif section == 'response':
            return self.debug_response
        elif section == 'error':
            return self.debug_errors
        
        return self.debug
        
    @nr_retry
    def execute_query(
        self, 
        query: str, 
        variables: Optional[Dict[str, Any]] = None, 
        debug: Optional[bool] = None,
        debug_options: Optional[Dict[str, bool]] = None
    ) -> GraphQLResponse:
        """
        Execute a GraphQL query against the New Relic NerdGraph API.
        
        This method is decorated with @nr_retry to automatically retry on transient errors
        (such as timeouts or server errors) using exponential backoff with jitter.
        
        Args:
            query: The GraphQL query string to execute
            variables: Optional dictionary of variables for the query
            debug: If True, enable debug output for this query
            debug_options: Optional dictionary of specific debug options for this query
            
        Returns:
            A GraphQLResponse object containing the query results
            
        Raises:
            NewRelicGraphQLError: If the query fails with an error
        """
        payload = {
            'query': query,
            'variables': variables or {}
        }
        
        # Apply debug options for this specific query if provided
        local_debug = debug if debug is not None else self.debug
        local_debug_request = debug_options.get('request', self.debug_request) if debug_options else self.debug_request
        local_debug_response = debug_options.get('response', self.debug_response) if debug_options else self.debug_response
        local_debug_errors = debug_options.get('errors', self.debug_errors) if debug_options else self.debug_errors
        
        # If main debug is enabled, enable all sections
        if local_debug:
            local_debug_request = local_debug_response = local_debug_errors = True
        
        # Log request details if debug is enabled
        if local_debug_request and self._should_debug('request', payload):
            self._debug_request(query, variables, self.region.value)
        
        try:
            response = requests.post(
                self.region.value,
                json=payload,
                headers=self.headers,
                timeout=self.timeout
            )
            
            # Handle HTTP errors
            try:
                response.raise_for_status()
            except requests.exceptions.HTTPError as e:
                # Try to extract more detailed error information if available
                error_detail = ""
                error_data = None
                
                try:
                    error_json = response.json()
                    if error_json and "errors" in error_json:
                        error_detail = f": {error_json['errors']}"
                        error_data = error_json
                except Exception:
                    pass
                
                if local_debug_errors and self._should_debug('error', error_data):
                    self._debug_error_response(response, e)
                
                raise NewRelicGraphQLError(f"HTTP error: {e}{error_detail}")
            
            # Parse response JSON
            try:
                data = response.json()
            except ValueError as e:
                if local_debug_errors and self._should_debug('error', response.text):
                    self._debug_invalid_json(response, e)
                raise NewRelicGraphQLError(f"Invalid JSON response: {str(e)}")
            
            # Log response if debug is enabled
            if local_debug_response and self._should_debug('response', data):
                self._debug_response(response, data)
            
            graphql_response = GraphQLResponse(
                data=data.get('data'),
                errors=data.get('errors')
            )
            
            if graphql_response.has_errors:
                if local_debug_errors and self._should_debug('error', graphql_response.errors):
                    self._debug_graphql_errors(graphql_response.errors)
                    
                raise NewRelicGraphQLError(
                    "GraphQL query failed",
                    graphql_response.errors
                )
                
            return graphql_response
            
        except requests.exceptions.RequestException as e:
            if local_debug_errors and self._should_debug('error', str(e)):
                self._debug_request_exception(e)
            raise NewRelicGraphQLError(f"Failed to execute query: {str(e)}")

    def _debug_request(self, query: str, variables: Optional[Dict[str, Any]], url: str) -> None:
        """Print formatted debug information for a request"""
        print("\n" + "="*80)
        print("NEW RELIC GRAPHQL REQUEST")
        print("="*80)
        print(f"URL: {url}")
        
        # Print headers (but mask the API key)
        headers_display = self.headers.copy()
        if 'API-Key' in headers_display:
            api_key = headers_display['API-Key']
            if len(api_key) > 8:
                headers_display['API-Key'] = f"{api_key[:4]}...{api_key[-4:]}"
            else:
                headers_display['API-Key'] = "****"
        print("\nHeaders:")
        pprint.pprint(headers_display)
        
        # Format and print the GraphQL query
        print("\nQuery:")
        formatted_query = query.strip()
        print(formatted_query)
        
        # Print variables if any
        if variables:
            print("\nVariables:")
            pprint.pprint(variables)
        
        print("-"*80)
    
    def _debug_response(self, response: requests.Response, data: Dict[str, Any]) -> None:
        """Print formatted debug information for a response"""
        print("\nRESPONSE:")
        print(f"Status Code: {response.status_code}")
        print("\nContent:")
        pprint.pprint(data)
        print("="*80 + "\n")
    
    def _debug_error_response(self, response: requests.Response, exception: Exception) -> None:
        """Print formatted debug information for an error response"""
        print("\nERROR RESPONSE:")
        print(f"Status Code: {response.status_code}")
        print(f"Exception: {str(exception)}")
        try:
            error_data = response.json()
            print("\nError Data:")
            pprint.pprint(error_data)
        except:
            print("\nResponse Text:")
            print(response.text)
        print("="*80 + "\n")
    
    def _debug_invalid_json(self, response: requests.Response, exception: Exception) -> None:
        """Print formatted debug information for an invalid JSON response"""
        print("\nERROR: Invalid JSON response")
        print(f"Exception: {str(exception)}")
        print("\nResponse Text:")
        print(response.text)
        print("="*80 + "\n")
    
    def _debug_graphql_errors(self, errors: List[Dict[str, Any]]) -> None:
        """Print formatted debug information for GraphQL errors"""
        print("\nGRAPHQL ERRORS:")
        pprint.pprint(errors)
        print("="*80 + "\n")
    
    def _debug_request_exception(self, exception: Exception) -> None:
        """Print formatted debug information for a request exception"""
        print(f"\nREQUEST EXCEPTION: {str(exception)}")
        print("="*80 + "\n")

    def _verify_account_id(self, account_id: Optional[str] = None) -> str:
        """
        Verify that an account ID is available, either from the method parameter or client instance.
        
        Args:
            account_id: Optional account ID to use
            
        Returns:
            The verified account ID
            
        Raises:
            ValueError: If no account ID is available
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("account_id must be provided either at client initialization or method call")
        return account_id

================
File: config.py
================
"""
Configuration manager for New Relic metric queries.
"""

import os
import yaml
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

class MetricsConfigManager:
    """Manages metric configuration for different entity types"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the metrics configuration manager
        
        Args:
            config_path: Path to the metrics configuration YAML file.
                         If None, uses the default config/entity_metrics.yaml.
        """
        if config_path is None:
            # Default location relative to the package
            module_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(module_dir, '..', '..', 'config', 'entity_metrics.yaml')
            
        self.config = self._load_config(config_path)
        self._aliases = self._load_aliases()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load the metrics configuration from YAML file
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dictionary containing the configuration
        """
        if not os.path.exists(config_path):
            print(f"Warning: Metrics configuration file not found at {config_path}")
            return {"entities": {}}
            
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"Error loading metrics configuration: {str(e)}")
            return {"entities": {}}
    
    def _load_aliases(self) -> Dict[str, str]:
        """
        Load entity type aliases from the configuration
        
        Returns:
            Dictionary mapping alias names to canonical entity types
        """
        aliases = {}
        try:
            alias_config = self.config.get("entities", {}).get("aliases", {})
            if alias_config:
                aliases.update(alias_config)
        except Exception as e:
            print(f"Error loading entity aliases: {str(e)}")
        return aliases
    
    def _resolve_entity_type(self, entity_type: str) -> str:
        """
        Resolve entity type aliases to their canonical names
        
        Args:
            entity_type: Entity type or alias
            
        Returns:
            Canonical entity type name
        """
        return self._aliases.get(entity_type, entity_type)
    
    def get_entity_metrics(self, entity_type: str) -> Dict[str, Dict[str, Any]]:
        """
        Get all metrics configuration for a specific entity type
        
        Args:
            entity_type: Type of entity (e.g., KUBERNETES_POD)
            
        Returns:
            Dictionary of metric configurations
        """
        resolved_type = self._resolve_entity_type(entity_type)
        return self.config.get("entities", {}).get(resolved_type, {}).get("metrics", {})
    
    def get_default_metrics(self, entity_type: str) -> List[str]:
        """
        Get list of default metrics to collect for an entity type
        
        Args:
            entity_type: Type of entity
            
        Returns:
            List of metric names that are marked as default
        """
        metrics = self.get_entity_metrics(entity_type)
        return [name for name, config in metrics.items() 
                if config.get("default", False)]
                
    def get_nrql_for_metric(self, entity_type: str, metric_name: str) -> Optional[str]:
        """
        Get the NRQL query for a specific metric and entity type
        
        Args:
            entity_type: Type of entity
            metric_name: Name of the metric
            
        Returns:
            NRQL query string or None if not found
        """
        metrics = self.get_entity_metrics(entity_type)
        return metrics.get(metric_name, {}).get("nrql")
    
    def get_metric_metadata(self, entity_type: str, metric_name: str) -> Dict[str, Any]:
        """
        Get metadata for a specific metric (name, unit, etc.)
        
        Args:
            entity_type: Type of entity
            metric_name: Name of the metric
            
        Returns:
            Dictionary of metric metadata
        """
        metrics = self.get_entity_metrics(entity_type)
        metric_config = metrics.get(metric_name, {})
        
        # Remove the NRQL query from the metadata
        metadata = {k: v for k, v in metric_config.items() if k != 'nrql'}
        return metadata
        
    def format_nrql(self, nrql: str, **kwargs) -> str:
        """
        Format the NRQL query with the given parameters
        
        Args:
            nrql: The NRQL query template
            **kwargs: Parameters to substitute in the query
            
        Returns:
            Formatted NRQL query
        """
        return nrql.format(**kwargs)
    
    def get_all_entity_types(self) -> List[str]:
        """
        Get all entity types defined in the configuration
        
        Returns:
            List of entity type names
        """
        entities = self.config.get("entities", {})
        return [name for name in entities.keys() if name != "aliases"]

================
File: logs.py
================
"""
New Relic GraphQL client for log query operations.
"""

from typing import Any, Dict, List, Optional, Union, Set
from datetime import datetime, timedelta

from .base import UTC, GraphQLResponse, format_datetime
from .client import NewRelicGraphQLClient, NewRelicGraphQLError
from .utils import retry_on_error


class NewRelicLogsClient:
    """
    Client for New Relic log query operations.
    Provides methods for fetching logs from various partitions.
    """
    
    # Common log partitions - can be expanded as needed
    KNOWN_PARTITIONS = {
        "default": ["Log"],
        "all": [
            "Log", "Log_Access_AZ_CA1", "Log_Access_AZ_UK1", "Log_CNS", "Log_GlobalProd",
            "Log_ISM", "Log_Incapptic", "Log_Landesk", "Log_MDM_Sandbox", "Log_MI_AP1",
            "Log_MI_AP2", "Log_MI_AP2_Access", "Log_MI_AP_GS_Azure", "Log_MI_NA1", "Log_MI_NA2",
            "Log_MI_NA2_Access", "Log_MI_NA_GS_Azure", "Log_MI_SB_GS_Azure", "Log_MI_Sandbox_Access",
            "Log_NMDM_AZ_CA1", "Log_NMDM_AZ_CA1_Managed", "Log_NMDM_AZ_SB", "Log_NMDM_AZ_SB_Managed",
            "Log_NMDM_AZ_UK1", "Log_NMDM_AZ_UK1_Managed", "Log_Neurons_MLU", "Log_Neurons_NVU",
            "Log_Neurons_TKU", "Log_Neurons_TTU", "Log_Neurons_UKU", "Log_RiskSense", "Log_VNS",
            "Log_GlobalStaging", "Log_MI_AP_GS", "Log_MI_NA_GS", "Log_MiProdCluster", "Log_MI_SB_GS",
            "Log_MiSbStaging", "Log_MI_STAGING", "Log_MI_STAGING_GS", "Log_nmdm_az_sc_eu2"
        ],
        "neurons_nvu": ["Log_Neurons_NVU"],
        "neurons_all": ["Log_Neurons_MLU", "Log_Neurons_NVU", "Log_Neurons_TKU", "Log_Neurons_TTU", "Log_Neurons_UKU"]
    }
    
    def __init__(self, client: NewRelicGraphQLClient, debug: bool = False):
        """
        Initialize the New Relic Logs client.
        
        Args:
            client: An initialized NewRelicGraphQLClient
            debug: Enable debug mode for verbose output
        """
        self.client = client
        self.debug = debug
    
    def _format_log_sources(self, partitions: Union[List[str], str]) -> str:
        """
        Format the FROM clause for log queries.
        
        Args:
            partitions: List of partition names or a predefined partition set key
            
        Returns:
            Formatted FROM clause string
        """
        if isinstance(partitions, str):
            # Check if it's a predefined partition set
            if partitions in self.KNOWN_PARTITIONS:
                partition_list = self.KNOWN_PARTITIONS[partitions]
            else:
                # Single partition name
                partition_list = [partitions]
        else:
            partition_list = partitions
            
        return ", ".join(partition_list)
    
    @retry_on_error(max_attempts=3, initial_delay=1, backoff_factor=2)
    def query_logs(
        self,
        query: str,
        limit: int = 100,
        account_id: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Execute a logs query against New Relic.

        Args:
            query: NRQL query for logs
            limit: Maximum number of logs to return
            account_id: Account ID (optional, overrides client default)

        Returns:
            List of log entries
        """
        account_id = account_id or self.client._verify_account_id()
        
        graphql_query = """
        query($accountId: Int!, $query: Nrql!) {
          actor {
            account(id: $accountId) {
              nrql(query: $query) {
                results
              }
            }
          }
        }
        """
        
        # Ensure query doesn't exceed the limit
        if "LIMIT" not in query.upper():
            query += f" LIMIT {limit}"
            
        variables = {
            "accountId": int(account_id),
            "query": query
        }
        
        if self.debug:
            print(f"Executing GraphQL logs query with NRQL: {query}")
            
        try:
            response = self.client.execute_query(graphql_query, variables)
            return response.data["actor"]["account"]["nrql"]["results"]
        except Exception as e:
            if self.debug:
                print(f"Error querying logs: {str(e)}")
            return []
    
    def get_pod_logs(
        self,
        pod_name: str,
        cluster_partition: Optional[str] = None,
        log_level: Optional[str] = None,
        since: Optional[Union[str, int, datetime]] = None,
        until: Optional[Union[str, int, datetime]] = None,
        limit: int = 1000,
        account_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get logs for a specific Kubernetes pod.
        
        Args:
            pod_name: Name of the pod
            cluster_partition: Specific cluster partition to query (e.g., "neurons_nvu")
            log_level: Optional filter for log level (e.g., "error", "warn")
            since: Start time for the query
            until: End time for the query
            limit: Maximum number of log entries to return
            account_id: Account ID (overrides default if set)
            
        Returns:
            List of log entries for the pod
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        # Determine which partitions to query
        partitions = cluster_partition or "default"
        
        # Build where conditions
        where_conditions = [
            f"pod_name = '{pod_name}' OR k8s.pod.name = '{pod_name}'"
        ]
        
        # Add log level filter if specified
        if log_level:
            where_conditions.append(f"level = '{log_level}' OR severity = '{log_level}'")
        
        # Select relevant fields
        select_fields = ["timestamp", "level", "message", "pod_name", "k8s.pod.name", "container_name", "k8s.container.name"]
        
        return self.query_logs(
            query="SELECT " + ", ".join(select_fields) + " FROM " + self._format_log_sources(partitions) + " WHERE " + " AND ".join(where_conditions),
            limit=limit,
            account_id=account_id
        )
    
    def get_entity_logs(
        self,
        entity_guid: str,
        since: datetime,
        until: datetime,
        entity_type: str = None,
        logs_config: Dict[str, Any] = None,
        limit: int = 100,
        account_id: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Get logs for a specific entity.

        Args:
            entity_guid: Entity GUID
            since: Start time for logs
            until: End time for logs
            entity_type: Entity type (optional)
            logs_config: Configuration for log collection (optional)
            limit: Maximum number of logs to return
            account_id: Account ID (optional, overrides client default)

        Returns:
            List of log entries
        """
        # Build log query using NRQL log syntax
        base_query = f"FROM Log WHERE entity.guid = '{entity_guid}'"
        
        # Add date range
        time_query = (
            f"SINCE '{since.strftime('%Y-%m-%d %H:%M:%S')}' " +
            f"UNTIL '{until.strftime('%Y-%m-%d %H:%M:%S')}'"
        )
        
        # Add filtering based on logs_config if provided
        filter_conditions = []
        if logs_config and "filters" in logs_config:
            for filter_term in logs_config["filters"]:
                if ":" in filter_term:
                    # This is a field:value filter, add it directly
                    filter_conditions.append(filter_term)
                else:
                    # This is a search term, wrap it in message LIKE
                    filter_conditions.append(f"message LIKE '%{filter_term}%'")
        
        # Default to error logs if no filters provided
        if not filter_conditions:
            filter_conditions = ["level IN ('error', 'warning', 'critical', 'fatal')"]
            
        # Combine all filter conditions with OR
        filters = " OR ".join(filter_conditions)
        if filters:
            base_query += f" AND ({filters})"
            
        # Specify attributes to select
        attributes = "*"
        if logs_config and "attributes" in logs_config:
            attributes = ", ".join(logs_config["attributes"])
            
        # Specify partition if provided
        partition = ""
        if logs_config and "partition" in logs_config and logs_config["partition"] != "all":
            # New Relic no longer supports WITH PARTITIONS directly in NRQL
            # Instead, we include it in the FROM clause
            base_query = f"FROM Log, Partition '{logs_config['partition']}' WHERE entity.guid = '{entity_guid}'"
            
        # Build the full query
        query = f"SELECT {attributes} {base_query} {time_query} LIMIT {limit}"
        
        if self.debug:
            print(f"Executing logs query: {query}")

        # Execute query using client
        return self.query_logs(
            query=query,
            limit=limit,
            account_id=account_id
        )
    
    def get_logs_config_for_entity_type(self, entity_type: str, cluster_id: str = "unknown") -> Dict[str, Any]:
        """
        Get the logs configuration for a specific entity type and cluster.
        This identifies what logs should be collected based on entity type and cluster.
        
        Args:
            entity_type: The entity type to get logs for
            cluster_id: The cluster ID (for Kubernetes entities)
            
        Returns:
            Dictionary of logs configuration appropriate for this entity type
        """
        # Determine log partition based on cluster ID
        partition = "all"
        for product, regions in self._get_cluster_patterns().items():
            for region, patterns in regions.items():
                if any(pattern in cluster_id for pattern in patterns):
                    partition = self._get_log_partition(region)
                    break
        
        # Create logs configurations grouped by entity type
        logs_config = {
            "K8S_POD": {
                "filters": [
                    f"kubernetes.pod.name:'{cluster_id}'",
                    "pod",
                    "error",
                    "exception",
                    "fail",
                    "critical"
                ],
                "attributes": [
                    "kubernetes.pod.name",
                    "kubernetes.namespace.name",
                    "kubernetes.container.name",
                    "message",
                    "level",
                    "timestamp"
                ],
                "partition": partition
            },
            "K8S_NODE": {
                "filters": [
                    f"kubernetes.node.name:'{cluster_id}'",
                    "node",
                    "error",
                    "warning",
                    "fail",
                    "critical"
                ],
                "attributes": [
                    "kubernetes.node.name",
                    "message",
                    "level",
                    "timestamp",
                    "component"
                ],
                "partition": partition
            },
            "HOST": {
                "filters": [
                    "error",
                    "warning",
                    "critical",
                    "fail",
                    "exception"
                ],
                "attributes": [
                    "hostname",
                    "service",
                    "message",
                    "level",
                    "timestamp"
                ],
                "partition": "all"
            },
            "APPLICATION": {
                "filters": [
                    "error",
                    "exception",
                    "fatal",
                    "warning",
                    "critical"
                ],
                "attributes": [
                    "service.name", 
                    "message",
                    "level",
                    "timestamp",
                    "trace.id"
                ],
                "partition": "all"
            },
            "CONTAINER": {
                "filters": [
                    "error",
                    "exception",
                    "fatal",
                    "warning",
                    "critical"
                ],
                "attributes": [
                    "container.name",
                    "container.id",
                    "message",
                    "level",
                    "timestamp"
                ],
                "partition": partition
            },
            "AWSRDSDBINSTANCE": {
                "filters": [
                    "error",
                    "warning",
                    "fatal",
                    "critical",
                    "timeout",
                    "deadlock",
                    "connection"
                ],
                "attributes": [
                    "provider.instanceIdentifier",
                    "message",
                    "level",
                    "timestamp"
                ],
                "partition": "all"
            }
        }
        
        # Return configuration for the entity type, or an empty dict if not found
        return logs_config.get(entity_type, {})
        
    def _get_cluster_patterns(self) -> Dict[str, Dict[str, List[str]]]:
        """
        Get patterns for identifying cluster region from cluster ID.
        
        Returns:
            Dictionary of cluster patterns by product and region
        """
        return {
            "neurons": {
                "nvu": ["aks-edge-rg-nvu-prd-neurons", "aks-rg-nvu-prd-neurons"],
                "uku": ["aks-rg-uku-prd-neurons", "aks-edge-rg-uku-prd-neurons"],
                "mlu": ["aks-edge-rg-mlu-prd-neurons", "aks-rg-mlu-prd-neurons"],
                "ttu": ["aks-edge-rg-ttu-prd-neurons", "aks-rg-ttu-prd-neurons"],
                "tku": ["aks-edge-rg-tku-prd-neurons", "aks-rg-tku-prd-neurons"]
            },
            "mdm": {
                "na1": ["na1", "primary-na1", "na1-eks", "north-america-1"],
                "na2": ["na2", "primary-na2", "na2-eks", "north-america-2"],
                "ap1": ["ap1", "primary-ap1", "ap1-eks", "asia-pacific-1"],
                "ap2": ["ap2", "primary-ap2", "ap2-eks", "asia-pacific-2"]
            }
        }
        
    def _get_log_partition(self, region: str) -> str:
        """
        Get the log partition to use for a given region.
        
        Args:
            region: Region code
            
        Returns:
            Log partition name
        """
        partition_map = {
            "nvu": "neurons_nvu",
            "uku": "neurons_all",
            "mlu": "neurons_all",
            "ttu": "neurons_all",
            "tku": "neurons_all",
            "na1": "default",
            "na2": "default",
            "ap1": "default",
            "ap2": "default"
        }
        return partition_map.get(region, "all")

================
File: main.py
================
"""
Example usage of the New Relic GraphQL client library.
"""

import os
import json
from datetime import datetime, timedelta

from lib.new_relic.base import Region, UTC, AlertStatus, format_datetime
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.metrics import NewRelicMetricsClient
from lib.new_relic.alerts import NewRelicAlertsClient
from lib.new_relic.mutation import NewRelicMutationClient


def format_metric_results(client, results):
    """Helper function to format and print metric results."""
    for result in results:
        print(f"\nMetric: {result.name}")
        print("=" * 50)
        print("Metadata:", json.dumps(result.metadata, indent=2))
        print("\nTimeseries Data:")
        for point in result.timeseries:
            print(f"  {point.timestamp.isoformat()}: {point.value}")


def main():
    # Get API key from environment variable
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        print("Error: NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID environment variables must be set")
        exit(1)
    
    # Initialize the core client
    core_client = NewRelicGraphQLClient(
        api_key=api_key,
        region=Region.US,
        account_id=account_id
    )
    
    # Initialize specialized clients
    query_client = NewRelicQueryClient(core_client)
    metrics_client = NewRelicMetricsClient(core_client)
    alerts_client = NewRelicAlertsClient(core_client)
    mutation_client = NewRelicMutationClient(core_client)
    
    try:
        # Example 1: Get pod details
        print("\n=== Example 1: Pod Details ===")
        pod_name = "example-pod-name"
        pod_details = query_client.get_pod_details(
            pod_name=pod_name,
            since=datetime.now(UTC) - timedelta(hours=1)
        )
        print(f"Found {len(pod_details)} pod details records")
        
        # Example 2: Get evicted pods metrics
        print("\n=== Example 2: Evicted Pods Metrics ===")
        since = datetime.now(UTC) - timedelta(hours=24)
        evicted_pods = metrics_client.get_evicted_pods_metrics(
            since=since,
            cluster_pattern='%prd%',
            exclude_patterns=['%assetprocessor%', '%fru-prd%'],
            timeseries_period='30 minutes'
        )
        print(f"Found metrics for evicted pods: {len(evicted_pods.get('results', []))} results")
        
        # Example 3: Get alerts
        print("\n=== Example 3: Recent Alerts ===")
        alerts = alerts_client.get_alerts(
            status=AlertStatus.FIRING,
            since=datetime.now(UTC) - timedelta(days=1),
            limit=5
        )
        print(f"Found {len(alerts)} recent alerts:")
        for alert in alerts:
            print(f"  - {alert.name} ({alert.severity.value}) - {alert.status.value}")
        
        # Example 4: Get CPU metrics for a container
        print("\n=== Example 4: CPU Metrics ===")
        cpu_query = """
        SELECT average(cpuUsedCores)
        FROM K8sContainerSample
        WHERE clusterName LIKE '%prd%'
        FACET containerName, clusterName
        TIMESERIES 30 minutes
        SINCE 2 hours ago
        LIMIT 2
        """
        cpu_metrics = metrics_client.get_generic_timeseries_metrics(
            nrql_query=cpu_query,
            metric_name="container_cpu_usage"
        )
        format_metric_results(metrics_client, cpu_metrics)
        
    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        # Clean up resources
        core_client.close()


if __name__ == "__main__":
    main()

================
File: metrics.py
================
"""
New Relic GraphQL client for metrics and timeseries operations.
"""

from typing import Any, Dict, List, Optional, Union, cast
from datetime import datetime, timedelta
import os
import yaml

from .base import UTC, MetricResult, TimeseriesDataPoint, format_datetime
from .client import NewRelicGraphQLClient, NewRelicGraphQLError
from .config import MetricsConfigManager


class NewRelicMetricsClient:
    """
    Client for New Relic metrics operations.
    Provides methods for fetching and analyzing metric data.
    """
    
    def __init__(self, client: NewRelicGraphQLClient):
        """
        Initialize the New Relic metrics client.
        
        Args:
            client: The core New Relic GraphQL client instance
        """
        self.client = client
        self.config_manager = MetricsConfigManager()
    
    def get_metric_timeseries(
        self,
        metric_name: str,
        account_id: Optional[str] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        where: Optional[str] = None,
        facet: Optional[Union[str, List[str]]] = None,
        timeseries_period: str = "1 minute"
    ) -> List[MetricResult]:
        """
        Fetch timeseries metric data using NRQL.

        Args:
            metric_name: Name of the metric to query
            account_id: Account ID (overrides default if set)
            since: Start time for the query (defaults to 30 minutes ago)
            until: End time for the query (defaults to now)
            where: Optional NRQL WHERE clause
            facet: Optional facet clause (string or list of strings)
            timeseries_period: Time bucket for the timeseries (default: 1 minute)

        Returns:
            List of MetricResult objects containing the timeseries data
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        # Set default time values if not provided
        since = since or (datetime.now(UTC) - timedelta(minutes=30))
        until = until or datetime.now(UTC)

        # Build the NRQL query
        query_parts = [
            f"SELECT latest({metric_name})",
            f"FROM Metric",
            f"SINCE '{format_datetime(since)}'",
            f"UNTIL '{format_datetime(until)}'",
            f"TIMESERIES {timeseries_period}"
        ]

        if where:
            query_parts.insert(2, f"WHERE {where}")

        if facet:
            if isinstance(facet, list):
                facet_clause = ", ".join(facet)
            else:
                facet_clause = facet
            query_parts.insert(-1, f"FACET {facet_clause}")

        nrql = " ".join(query_parts)

        # Construct GraphQL query
        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.client.execute_query(graphql_query, variables)
        
        try:
            nrql_data = response.data["actor"]["account"]["nrql"]
            results = nrql_data["results"]
            metadata = nrql_data["metadata"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to parse metric results: {str(e)}")

        metric_results = []
        for result in results:
            timeseries_data = []
            for timestamp, value in result.items():
                if timestamp != "facet":
                    try:
                        ts = datetime.fromtimestamp(int(timestamp) / 1000, UTC)
                        val = float(value) if value is not None else 0.0
                        timeseries_data.append(
                            TimeseriesDataPoint(timestamp=ts, value=val)
                        )
                    except (ValueError, TypeError):
                        # Skip invalid data points
                        continue

            metric_results.append(
                MetricResult(
                    name=metric_name,
                    timeseries=sorted(timeseries_data, key=lambda x: x.timestamp),
                    metadata={
                        "facet": result.get("facet"),
                        **metadata
                    }
                )
            )

        return metric_results
    
    def parse_generic_timeseries(
        self, 
        timeseries_data: Dict[str, Any], 
        metric_name: str
    ) -> List[MetricResult]:
        """
        Parse a generic timeseries response and convert it to a list of MetricResult objects.

        Args:
            timeseries_data: The raw timeseries response from a NRQL query
            metric_name: The default metric name if facets are not available

        Returns:
            List of parsed MetricResult objects
        """
        results: List[MetricResult] = []
        
        # Get the results array from the response
        data_points = timeseries_data.get("results", [])
        if not data_points:
            return results

        # Group data points by facet
        facet_groups: Dict[str, List[Dict]] = {}
        for point in data_points:
            facet = point.get("facet")
            if facet:
                # Convert facet list to string key
                if isinstance(facet, list):
                    facet_key = " | ".join(str(f) for f in facet)
                else:
                    facet_key = str(facet)
            else:
                facet_key = metric_name
            
            if facet_key not in facet_groups:
                facet_groups[facet_key] = []
            facet_groups[facet_key].append(point)

        # Process each facet group
        for facet_key, points in facet_groups.items():
            timeseries: List[TimeseriesDataPoint] = []
            
            for point in points:
                # Get the timestamp from beginTimeSeconds
                timestamp_value = point.get("beginTimeSeconds")
                if timestamp_value is None:
                    # Try alternative formats
                    for key in point:
                        if isinstance(key, str) and key.isdigit():
                            timestamp_value = int(key) / 1000  # Convert ms to seconds
                            break
                
                if timestamp_value is None:
                    continue
                    
                try:
                    timestamp = datetime.fromtimestamp(timestamp_value, UTC)
                except (ValueError, TypeError):
                    continue
                
                # Find the metric value - look for keys ending with the metric name
                # or for common metrics pattern like average.*
                metric_value = None
                for key, value in point.items():
                    if (key.endswith(metric_name) or 
                        key.startswith("average.") or 
                        key == "count" or 
                        key == "sum" or
                        key == "latest"):
                        metric_value = value
                        break
                
                # Skip if no valid value found
                if metric_value is None:
                    continue
                
                # Convert to float, use 0.0 for null values
                try:
                    value = float(metric_value) if metric_value is not None else 0.0
                except (ValueError, TypeError):
                    value = 0.0
                
                timeseries.append(TimeseriesDataPoint(
                    timestamp=timestamp,
                    value=value
                ))
            
            # Only create a MetricResult if we have data points
            if timeseries:
                metadata = {
                    "facet": facet_key,
                    "total_points": len(timeseries)
                }
                results.append(MetricResult(
                    name=facet_key,
                    timeseries=sorted(timeseries, key=lambda x: x.timestamp),
                    metadata=metadata
                ))

        return results

    def get_generic_timeseries_metrics(
        self,
        nrql_query: str,
        metric_name: str,
        account_id: Optional[str] = None
    ) -> List[MetricResult]:
        """
        Execute a NRQL query and parse its timeseries results.

        Args:
            nrql_query: The NRQL query to execute
            metric_name: Default name for the metric if no facets are present
            account_id: Optional account ID to override the default

        Returns:
            List of parsed MetricResult objects
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql_query
        }

        response = self.client.execute_query(graphql_query, variables)
        try:
            results = response.data["actor"]["account"]["nrql"]
            return self.parse_generic_timeseries(results, metric_name)
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch timeseries metrics: {str(e)}")

    def get_entity_metrics_by_type(
        self,
        entity_guid: str,
        entity_type: str,
        metrics: Optional[List[str]] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        period: str = "1 minute",
        account_id: Optional[str] = None,
        metrics_config: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, List[MetricResult]]:
        """
        Gets metrics for a specific entity based on its type.
        Uses predefined metric sets for known entity types.

        Args:
            entity_guid: Entity GUID
            entity_type: Entity type
            metrics: Optional list of specific metrics to fetch
            since: Start time for metrics
            until: End time for metrics
            period: Aggregation period ("1 minute", "5 minutes", etc.)
            account_id: Account ID (overrides default if set)
            metrics_config: Configuration for metrics collection

        Returns:
            Dictionary of metrics data with metric names as keys
        """
        # If metrics_config is provided, use its basic metrics
        if metrics is None and metrics_config and "basic" in metrics_config:
            metrics = metrics_config["basic"]

        account_id = self.client._verify_account_id(account_id)
        since = since or (datetime.now(UTC) - timedelta(minutes=30))
        until = until or datetime.now(UTC)
        
        # If no metrics specified, use default metrics for this entity type
        if not metrics:
            metrics = self.config_manager.get_default_metrics(entity_type)
            
        # If still no metrics (entity type not configured), use empty dict
        if not metrics:
            return {}
        
        results = {}
        
        # Get each requested metric
        for metric_name in metrics:
            try:
                # Get the NRQL template from configuration
                nrql_template = self.config_manager.get_nrql_for_metric(entity_type, metric_name)
                
                if not nrql_template:
                    print(f"Warning: No NRQL template found for metric '{metric_name}' on entity type '{entity_type}'")
                    continue
                    
                # Format the NRQL template with parameters
                nrql = self.config_manager.format_nrql(
                    nrql_template,
                    entity_guid=entity_guid,
                    since=format_datetime(since),
                    until=format_datetime(until),
                    period=period
                )
                
                # Get the metric metadata
                metric_metadata = self.config_manager.get_metric_metadata(entity_type, metric_name)
                
                # Execute the query and parse results
                metric_results = self.get_generic_timeseries_metrics(
                    nrql_query=nrql,
                    metric_name=metric_metadata.get('name', metric_name),
                    account_id=account_id
                )
                
                # Add metadata to results
                for result in metric_results:
                    result.metadata.update({
                        "unit": metric_metadata.get("unit", "unknown"),
                        "entity_type": entity_type,
                        "entity_guid": entity_guid
                    })
                
                results[metric_name] = metric_results
                
            except Exception as e:
                print(f"Error fetching metric '{metric_name}' for entity {entity_guid}: {str(e)}")
        
        return results

    def get_evicted_pods_metrics(
        self,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        cluster_pattern: str = '%prd%',
        exclude_patterns: Optional[List[str]] = None,
        timeseries_period: str = "10 minutes",
        account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get metrics for evicted pods across clusters.

        Args:
            since: Start time for the query (defaults to 24 hours ago)
            until: End time for the query (defaults to now)
            cluster_pattern: Pattern to match cluster names (defaults to '%prd%')
            exclude_patterns: List of patterns to exclude from pod names
            timeseries_period: Time bucket for the timeseries (default: 10 minutes)
            account_id: Account ID (overrides default if set)

        Returns:
            Raw results from New Relic NRQL query
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)
        since = since or (datetime.now(UTC) - timedelta(hours=24))
        until = until or datetime.now(UTC)
        exclude_patterns = exclude_patterns or []

        # Build WHERE clause for exclusions
        exclusion_clauses = [f"podName NOT LIKE '{pattern}'" for pattern in exclude_patterns]
        exclusion_str = " AND ".join(exclusion_clauses)
        if exclusion_str:
            exclusion_str = f" AND {exclusion_str}"

        nrql = f"""
        SELECT count(*) FROM K8sPodSample 
        WHERE reason = 'Evicted' 
        AND status = 'Failed' 
        AND clusterName LIKE '{cluster_pattern}'{exclusion_str}
        FACET podName, clusterName 
        TIMESERIES {timeseries_period} 
        SINCE '{format_datetime(since)}' 
        UNTIL '{format_datetime(until)}'
        """

        # Build the GraphQL query
        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.client.execute_query(graphql_query, variables)
        try:
            return response.data["actor"]["account"]["nrql"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch evicted pods metrics: {str(e)}")

    def get_node_metrics(
        self,
        entity_guid: str,
        metric_type: str = 'cpu',  # 'cpu', 'memory', or 'disk'
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get node metrics (CPU, Memory, or Disk usage).
        
        Note: This method is kept for backwards compatibility.
        Consider using get_entity_metrics_by_type for new code.

        Args:
            entity_guid: Entity GUID of the node
            metric_type: Type of metric to fetch ('cpu', 'memory', or 'disk')
            since: Start time for the query
            until: End time for the query
            account_id: Optional account ID override

        Returns:
            Dictionary with metric results and metadata
            
        Raises:
            ValueError: If no account_id is available or invalid metric type
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)
        since = since or (datetime.now(UTC) - timedelta(days=1))
        until = until or datetime.now(UTC)

        metric_queries = {
            'cpu': "SELECT average(cpuPercent) AS 'CPU used %' FROM SystemSample",
            'memory': "SELECT average(memoryUsedPercent) AS 'Memory used %' FROM SystemSample",
            'disk': "SELECT max(diskUsedPercent) AS 'Storage used %' FROM StorageSample"
        }

        if metric_type not in metric_queries:
            raise ValueError(f"Invalid metric type. Must be one of: {', '.join(metric_queries.keys())}")

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        nrql = f"""
        {metric_queries[metric_type]}
        WHERE entityGuid = '{entity_guid}'
        TIMESERIES AUTO
        SINCE '{format_datetime(since)}'
        UNTIL '{format_datetime(until)}'
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.client.execute_query(graphql_query, variables)
        try:
            return response.data["actor"]["account"]["nrql"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch node metrics: {str(e)}")

    def get_entity_metrics(
        self,
        entity_guid: str,
        metrics: List[str],
        account_id: Optional[str] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        period: str = "1 minute"
    ) -> Dict[str, List[MetricResult]]:
        """
        Fetch multiple metrics for a specific entity.
        
        Note: This method is kept for backwards compatibility.
        Consider using get_entity_metrics_by_type for new code.

        Args:
            entity_guid: Entity GUID to query
            metrics: List of metric names to fetch
            account_id: Account ID (overrides default if set)
            since: Start time for the query (defaults to 30 minutes ago)
            until: End time for the query (defaults to now)
            period: Time bucket for the timeseries

        Returns:
            Dictionary mapping metric names to their MetricResult lists
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If any query fails
        """
        results = {}
        for metric in metrics:
            results[metric] = self.get_metric_timeseries(
                metric_name=metric,
                account_id=account_id,
                since=since,
                until=until,
                where=f"entity.guid = '{entity_guid}'",
                timeseries_period=period
            )
        return results

    def get_metrics_config_for_entity_type(self, entity_type: str) -> Dict[str, Any]:
        """
        Get the metrics configuration for a specific entity type.
        This identifies what metrics should be collected based on entity type.
        
        Args:
            entity_type: The entity type to get metrics for
            
        Returns:
            Dictionary of metric configurations appropriate for this entity type
        """
        # Create metrics configurations grouped by entity type
        metrics_config = {
            "K8S_POD": {
                "basic": [
                    "k8s.pod.cpu.usage",
                    "k8s.pod.memory.usage",
                    "k8s.pod.restarts",
                    "k8s.pod.status.phase",
                ],
                "advanced": [
                    "k8s.pod.cpu.limit",
                    "k8s.pod.memory.limit",
                    "k8s.pod.network.receive.bytes",
                    "k8s.pod.network.transmit.bytes",
                ]
            },
            "K8S_NODE": {
                "basic": [
                    "k8s.node.cpu.usage",
                    "k8s.node.memory.usage",
                    "k8s.node.filesystem.usage", 
                    "k8s.node.status.condition",
                ],
                "advanced": [
                    "k8s.node.cpu.capacity",
                    "k8s.node.memory.capacity",
                    "k8s.node.pod.capacity",
                    "k8s.node.network.receive.bytes",
                    "k8s.node.network.transmit.bytes",
                ]
            },
            "HOST": {
                "basic": [
                    "host.cpuPercent",
                    "host.memoryUsedPercent",
                    "host.diskUsedPercent",
                    "host.loadAverageOneMinute",
                ],
                "advanced": [
                    "host.swapUsedPercent",
                    "host.ioUtilizationPercent",
                    "host.processCount",
                    "host.networkThroughput",
                ]
            },
            "APPLICATION": {
                "basic": [
                    "apm.service.transaction.duration",
                    "apm.service.transaction.error_count",
                    "apm.service.transaction.request_count",
                    "apm.service.memory.physical",
                ],
                "advanced": [
                    "apm.service.datastore.operation.duration",
                    "apm.service.external.host.duration",
                    "apm.service.error.count",
                    "apm.service.cpu.usertime.utilization",
                ]
            },
            "CONTAINER": {
                "basic": [
                    "container.cpu.usage",
                    "container.memory.usage",
                    "container.status",
                    "container.restarts",
                ],
                "advanced": [
                    "container.cpu.limit",
                    "container.memory.limit",
                    "container.network.receive.bytes",
                    "container.network.transmit.bytes",
                ]
            },
            "AWSRDSDBINSTANCE": {
                "basic": [
                    "provider.cpuUtilization.Average",
                    "provider.freeableMemory.Average",
                    "provider.freeStorageSpace.Average",
                    "provider.databaseConnections.Average",
                ],
                "advanced": [
                    "provider.readIOPS.Average",
                    "provider.writeIOPS.Average",
                    "provider.readLatency.Average",
                    "provider.writeLatency.Average",
                ]
            }
        }
        
        # Return configuration for the entity type, or an empty dict if not found
        return metrics_config.get(entity_type, {})

================
File: mutation.py
================
"""
New Relic GraphQL client for mutations (write operations).
"""

import json
from typing import Any, Dict, List, Optional

from .base import GraphQLResponse, NewRelicGraphQLError
from .client import NewRelicGraphQLClient


class NewRelicMutationClient:
    """
    Client for New Relic mutation (write) operations.
    Provides methods for creating and updating resources in New Relic.
    """
    
    def __init__(self, client: NewRelicGraphQLClient):
        """
        Initialize the New Relic mutation client.
        
        Args:
            client: The core New Relic GraphQL client instance
        """
        self.client = client
    
    def create_webhook_destination(
        self, 
        name: str, 
        url: str, 
        account_id: Optional[str] = None
    ) -> str:
        """
        Create a webhook destination.
        
        Args:
            name: Webhook name
            url: Webhook URL
            account_id: Account ID (overrides default if set)
            
        Returns:
            Webhook destination ID
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $name: String!, $url: String!) {
            aiNotificationsCreateDestination(
                accountId: $accountId,
                destination: {
                    type: WEBHOOK,
                    name: $name,
                    properties: [{key: "url", value: $url}]
                }
            ) {
                destination {
                    id
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "name": name,
            "url": url
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiNotificationsCreateDestination"]["destination"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create webhook destination")

    def create_notification_channel(
        self, 
        destination_id: str, 
        name: str, 
        api_key: str, 
        account_id: Optional[str] = None
    ) -> str:
        """
        Create a notification channel.
        
        Args:
            destination_id: Destination ID
            name: Channel name
            api_key: API key for the channel
            account_id: Account ID (overrides default if set)
            
        Returns:
            Channel ID
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        # Define the payload template with proper JSON escaping
        payload_template = (
            '{"id": {{ json issueId }},'
            '"issueUrl": {{ json issuePageUrl }},'
            '"name": {{ json annotations.title.[0] }},'
            '"severity": {{ json priority }},'
            '"impactedEntities": {{ json entitiesData.names }},'
            '"totalIncidents": {{ json totalIncidents }},'
            '"status": {{ json state }},'
            '"trigger": {{ json triggerEvent }},'
            '"isCorrelated": {{ json isCorrelated }},'
            '"createdAt": {{ createdAt }},'
            '"updatedAt": {{ updatedAt }},'
            '"lastReceived": {{ updatedAt }},'
            '"source": {{ json accumulations.source }},'
            '"alertPolicyNames": {{ json accumulations.policyName }},'
            '"alertConditionNames": {{ json accumulations.conditionName }},'
            '"workflowName": {{ json workflowName }}}'
        )
        
        graphql_query = """
        mutation($accountId: Int!, $destinationId: ID!, $name: String!, $headers: String!, $payload: String!) {
            aiNotificationsCreateChannel(
                accountId: $accountId,
                channel: {
                    name: $name,
                    product: IINT,
                    type: WEBHOOK,
                    destinationId: $destinationId,
                    properties: [
                        {
                            key: "headers",
                            value: $headers
                        },
                        {
                            key: "payload",
                            value: $payload
                        }
                    ]
                }
            ) {
                channel {
                    id
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "destinationId": destination_id,
            "name": name,
            "headers": json.dumps({"X-API-KEY": api_key}),
            "payload": payload_template
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiNotificationsCreateChannel"]["channel"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create notification channel")

    def create_workflow(
        self, 
        name: str, 
        channel_id: str, 
        policy_ids: List[str], 
        account_id: Optional[str] = None
    ) -> str:
        """
        Create a workflow.
        
        Args:
            name: Workflow name
            channel_id: Channel ID
            policy_ids: List of policy IDs
            account_id: Account ID (overrides default if set)
            
        Returns:
            Workflow ID
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $name: String!, $channelId: ID!, $policyIds: [String!]!) {
            aiWorkflowsCreateWorkflow(
                accountId: $accountId
                createWorkflowData: {
                    destinationConfigurations: {
                        channelId: $channelId,
                        notificationTriggers: [ACTIVATED, ACKNOWLEDGED, CLOSED, PRIORITY_CHANGED, OTHER_UPDATES]
                    },
                    issuesFilter: {
                        predicates: [
                            {
                                attribute: "labels.policyIds",
                                operator: EXACTLY_MATCHES,
                                values: $policyIds
                            }
                        ],
                        type: FILTER
                    },
                    workflowEnabled: true,
                    destinationsEnabled: true,
                    mutingRulesHandling: DONT_NOTIFY_FULLY_MUTED_ISSUES,
                    name: $name
                }
            ) {
                workflow {
                    id
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "name": name,
            "channelId": channel_id,
            "policyIds": policy_ids
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiWorkflowsCreateWorkflow"]["workflow"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create workflow")
    
    def update_workflow(
        self,
        workflow_id: str,
        enabled: bool,
        account_id: Optional[str] = None
    ) -> bool:
        """
        Update a workflow's enabled status.
        
        Args:
            workflow_id: ID of the workflow to update
            enabled: Whether the workflow should be enabled
            account_id: Account ID (overrides default if set)
            
        Returns:
            True if successful
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $workflowId: ID!, $enabled: Boolean!) {
            aiWorkflowsUpdateWorkflow(
                accountId: $accountId,
                updateWorkflowData: {
                    id: $workflowId,
                    workflowEnabled: $enabled
                }
            ) {
                workflow {
                    id
                    workflowEnabled
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "workflowId": workflow_id,
            "enabled": enabled
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiWorkflowsUpdateWorkflow"]["workflow"]["workflowEnabled"] == enabled
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to update workflow")
    
    def delete_workflow(
        self,
        workflow_id: str,
        account_id: Optional[str] = None
    ) -> bool:
        """
        Delete a workflow.
        
        Args:
            workflow_id: ID of the workflow to delete
            account_id: Account ID (overrides default if set)
            
        Returns:
            True if successful
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $workflowId: ID!) {
            aiWorkflowsDeleteWorkflow(
                accountId: $accountId,
                id: $workflowId
            ) {
                id
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "workflowId": workflow_id
        }
        
        response = self.client.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiWorkflowsDeleteWorkflow"]["id"] == workflow_id
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to delete workflow")

================
File: query.py
================
"""
New Relic GraphQL client for query (read) operations.
"""

import logging
from typing import Any, Dict, List, Optional, Union, cast
from datetime import datetime, timedelta

from .base import UTC, GraphQLResponse, MetricResult, Alert, format_datetime
from .client import NewRelicGraphQLClient, NewRelicGraphQLError
from .metrics import NewRelicMetricsClient

import logfire

# Configure logger
logger = logging.getLogger(__name__)

# Configure logfire
logfire.configure(token="pylf_v1_us_VKtN68FRL2VBWqwLpv0K5JmJbJtrXTQBrFW99tNfFbLN")
logfire.instrument_openai()


class NewRelicQueryClient:
    """
    Client for New Relic query (read) operations.
    Provides methods for fetching data from New Relic.
    """

    def __init__(self, client: NewRelicGraphQLClient):
        """
        Initialize the New Relic query client.

        Args:
            client: The core New Relic GraphQL client instance
        """
        self.client = client
        self.metrics = NewRelicMetricsClient(client)  # Initialize metrics client

    def get_all_policy_ids(self, account_id: Optional[str] = None) -> List[str]:
        """
        Get all alert policy IDs for an account.

        Args:
            account_id: Account ID (overrides default if set)

        Returns:
            List of policy IDs

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)

        graphql_query = """
        query($accountId: Int!) {
            actor {
                account(id: $accountId) {
                    alerts {
                        policiesSearch {
                            policies {
                                id
                            }
                        }
                    }
                }
            }
        }
        """

        variables = {"accountId": int(account_id)}
        response = self.client.execute_query(graphql_query, variables)

        try:
            policies = response.data["actor"]["account"]["alerts"]["policiesSearch"][
                "policies"
            ]
            return [policy["id"] for policy in policies]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to fetch policy IDs")

    def get_webhook_destination(
        self, name: str, url: str, account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get a webhook destination ID by name and URL.

        Args:
            name: Webhook name
            url: Webhook URL
            account_id: Account ID (overrides default if set)

        Returns:
            Webhook destination ID if found, None otherwise

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)

        graphql_query = """
        query($accountId: Int!, $name: String!, $url: String!) {
            actor {
                account(id: $accountId) {
                    aiNotifications {
                        destinations(filters: {
                            name: $name,
                            type: WEBHOOK,
                            property: { key: "url", value: $url }
                        }) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """

        variables = {"accountId": int(account_id), "name": name, "url": url}

        response = self.client.execute_query(graphql_query, variables)

        try:
            destinations = response.data["actor"]["account"]["aiNotifications"][
                "destinations"
            ]["entities"]
            return destinations[0]["id"] if destinations else None
        except (KeyError, IndexError):
            return None

    def get_notification_channel(
        self, destination_id: str, name: str, account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get a notification channel ID by destination ID and name.

        Args:
            destination_id: Destination ID
            name: Channel name
            account_id: Account ID (overrides default if set)

        Returns:
            Channel ID if found, None otherwise

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)

        graphql_query = """
        query($accountId: Int!, $destinationId: ID!, $name: String!) {
            actor {
                account(id: $accountId) {
                    aiNotifications {
                        channels(filters: {
                            destinationId: $destinationId,
                            name: $name
                        }) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "destinationId": destination_id,
            "name": name,
        }

        response = self.client.execute_query(graphql_query, variables)

        try:
            channels = response.data["actor"]["account"]["aiNotifications"]["channels"][
                "entities"
            ]
            return channels[0]["id"] if channels else None
        except (KeyError, IndexError):
            return None

    def get_workflow(
        self, name: str, channel_id: str, account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get a workflow ID by name and channel ID.

        Args:
            name: Workflow name
            channel_id: Channel ID
            account_id: Account ID (overrides default if set)

        Returns:
            Workflow ID if found, None otherwise

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)

        graphql_query = """
        query($accountId: Int!, $name: String!, $channelId: ID!) {
            actor {
                account(id: $accountId) {
                    aiWorkflows {
                        workflows(
                            filters: {name: $name, channelId: $channelId}
                        ) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "name": name,
            "channelId": channel_id,
        }

        response = self.client.execute_query(graphql_query, variables)

        try:
            workflows = response.data["actor"]["account"]["aiWorkflows"]["workflows"][
                "entities"
            ]
            return workflows[0]["id"] if workflows else None
        except (KeyError, IndexError):
            return None

    # def get_pod_details(
    #     self,
    #     pod_name: str,
    #     since: Optional[datetime] = None,
    #     until: Optional[datetime] = None,
    #     account_id: Optional[str] = None
    # ) -> List[Dict[str, Any]]:
    #     """
    #     Get detailed information about a specific pod.

    #     Args:
    #         pod_name: Name of the pod
    #         since: Start time for the query
    #         until: End time for the query
    #         account_id: Optional account ID override

    #     Returns:
    #         List containing pod details

    #     Raises:
    #         ValueError: If no account_id is available
    #         NewRelicGraphQLError: If the query fails
    #     """
    #     account_id = self.client._verify_account_id(account_id)
    #     since = since or (datetime.now(UTC) - timedelta(minutes=30))
    #     until = until or datetime.now(UTC)

    #     graphql_query = """
    #     query($accountId: Int!, $nrql: Nrql!) {
    #         actor {
    #             account(id: $accountId) {
    #                 nrql(query: $nrql) {
    #                     results
    #                 }
    #             }
    #         }
    #     }
    #     """

    #     nrql = f"""
    #     SELECT *
    #     FROM K8sPodSample
    #     WHERE podName = '{pod_name}'
    #     SINCE '{format_datetime(since)}'
    #     UNTIL '{format_datetime(until)}'
    #     LIMIT 100
    #     """

    #     variables = {
    #         "accountId": int(account_id),
    #         "nrql": nrql
    #     }

    #     response = self.client.execute_query(graphql_query, variables)
    #     try:
    #         return response.data["actor"]["account"]["nrql"]["results"]
    #     except (KeyError, TypeError) as e:
    #         raise NewRelicGraphQLError(f"Failed to fetch pod details: {str(e)}")

    # def get_infrastructure_events(
    #     self,
    #     node_name: str,
    #     cluster_name: Optional[str] = None,
    #     start_time: Optional[datetime] = None,
    #     end_time: Optional[datetime] = None,
    #     account_id: Optional[str] = None,
    #     event_type: str = "Warning"
    # ) -> List[Dict[str, Any]]:
    #     """
    #     Fetch infrastructure events for a specific node.

    #     Args:
    #         node_name: Name of the node
    #         cluster_name: Optional cluster name for filtering
    #         start_time: Start time for the query (defaults to 24 hours ago)
    #         end_time: End time for the query (defaults to now)
    #         account_id: Account ID (overrides default if set)
    #         event_type: Type of events to query (default: "Warning")

    #     Returns:
    #         List of infrastructure events

    #     Raises:
    #         ValueError: If no account_id is available
    #         NewRelicGraphQLError: If the query fails
    #     """
    #     account_id = self.client._verify_account_id(account_id)
    #     start_time = start_time or (datetime.now(UTC) - timedelta(days=1))
    #     end_time = end_time or datetime.now(UTC)

    #     # Build cluster filter
    #     cluster_filter = f"AND clusterName = '{cluster_name}'" if cluster_name else ""

    #     graphql_query = """
    #     query($accountId: Int!, $nrql: Nrql!) {
    #         actor {
    #             account(id: $accountId) {
    #                 nrql(query: $nrql) {
    #                     results
    #                 }
    #             }
    #         }
    #     }
    #     """

    #     nrql = f"""
    #     SELECT *
    #     FROM InfrastructureEvent
    #     WHERE category = 'kubernetes'
    #     {cluster_filter}
    #     AND event.involvedObject.name = '{node_name}'
    #     AND event.type = '{event_type}'
    #     SINCE '{format_datetime(start_time)}'
    #     UNTIL '{format_datetime(end_time)}'
    #     LIMIT MAX
    #     """

    #     variables = {
    #         "accountId": int(account_id),
    #         "nrql": nrql
    #     }

    #     response = self.client.execute_query(graphql_query, variables)

    #     try:
    #         return response.data["actor"]["account"]["nrql"]["results"]
    #     except (KeyError, TypeError) as e:
    #         raise NewRelicGraphQLError(f"Failed to parse infrastructure events: {str(e)}")

    def get_kubernetes_events(
        self,
        object_name: str,
        object_kind: str = "Pod",
        cluster_name: Optional[str] = None,
        account_id: Optional[str] = None,
        since: Optional[Union[str, int, datetime]] = None,
        until: Optional[Union[str, int, datetime]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Fetch Kubernetes events for a specific object (Pod or Node).

        Example usage:
        # For a specific node
        node_events = client.query.get_kubernetes_events("aks-default-12345-vmss001", object_kind="Node")

        # For nodes with wildcard pattern
        node_events = client.query.get_kubernetes_events("%vmss%", object_kind="Node")

        # For a specific pod
        pod_events = client.query.get_kubernetes_events("my-pod-name")

        # With specific timestamps (epoch milliseconds)
        events = client.query.get_kubernetes_events("my-pod", since=*************, until=*************)

        # With formatted datetime strings
        events = client.query.get_kubernetes_events("my-pod", since="2025-03-28 10:00:00", until="2025-03-28 11:00:00")

        Args:
            object_name: Name of the object (pod or node)
            object_kind: Kind of object ('Pod' or 'Node')
            cluster_name: Optional cluster name filter
            account_id: Account ID (overrides default if set)
            since: Start time for the query (as epoch milliseconds, formatted string, or datetime object)
            until: End time for the query (as epoch milliseconds, formatted string, or datetime object)

        Returns:
            List of Kubernetes events

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)

        # Format the since timestamp
        if since is None:
            since_timestamp = f"SINCE 30 MINUTES AGO"
        elif isinstance(since, datetime):
            since_timestamp = f"SINCE '{format_datetime(since)}'"
        elif isinstance(since, int):
            # Treat as epoch milliseconds
            since_timestamp = f"SINCE {since}"
        else:
            # Treat as formatted string
            since_timestamp = f"SINCE '{since}'"

        # Format the until timestamp
        if until is None:
            until_timestamp = f"UNTIL 1 MINUTE AGO"
        elif isinstance(until, datetime):
            until_timestamp = f"UNTIL '{format_datetime(until)}'"
        elif isinstance(until, int):
            # Treat as epoch milliseconds
            until_timestamp = f"UNTIL {until}"
        else:
            # Treat as formatted string
            until_timestamp = f"UNTIL '{until}'"

        # Build name filter
        name_filter = f"event.involvedObject.name = '{object_name}'"
        if "*" in object_name or "%" in object_name:
            name_filter = f"event.involvedObject.name LIKE '{object_name}'"

        # Add cluster filter if provided
        cluster_filter = f"AND clusterName = '{cluster_name}'" if cluster_name else ""

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        nrql = f"""
        SELECT event.involvedObject.name, event.involvedObject.kind, event.reason, event.message, timestamp
        FROM InfrastructureEvent
        WHERE category = 'kubernetes'
        AND event.involvedObject.kind = '{object_kind}'
        AND {name_filter}
        {cluster_filter}
        {since_timestamp}
        {until_timestamp}
        """

        variables = {"accountId": int(account_id), "nrql": nrql}

        response = self.client.execute_query(graphql_query, variables)

        try:
            return response.data["actor"]["account"]["nrql"]["results"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to parse Kubernetes events: {str(e)}")

    def get_issue_details(
        self, issue_id: str, account_id: Optional[str] = None, lookback: str = "1 month"
    ) -> Dict[str, Any]:
        """
        Fetch issue details from New Relic including related incidents.

        Args:
            issue_id: The New Relic issue ID
            account_id: Account ID (overrides default if set)
            lookback: Time period to look back for incidents (default: "1 month")

        Returns:
            Dictionary containing issue details including related incidents

        Raises:
            ValueError: If no account_id is available or issue_id is empty
            NewRelicGraphQLError: If the query fails
        """
        if not issue_id:
            raise ValueError("Issue ID cannot be None or empty")

        account_id = self.client._verify_account_id(account_id)

        # Query to get incidents related to the issue
        nrql_query = f"""
        FROM NrAiIncident
        SELECT *
        WHERE incidentId in (
            FROM NrAiIssue
            SELECT getfield(incidentIds,0)
            WHERE issueId = '{issue_id}'
            SINCE {lookback} AGO
        )
        SINCE {lookback} AGO
        """

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                    }
                }
            }
        }
        """

        variables = {"accountId": int(account_id), "nrql": nrql_query}

        response = self.client.execute_query(graphql_query, variables)

        try:
            incidents = response.data["actor"]["account"]["nrql"]["results"]

            # Now get the issue itself
            issue_query = f"""
            FROM NrAiIssue
            SELECT *
            WHERE issueId = '{issue_id}'
            SINCE {lookback} AGO
            LIMIT 1
            """

            issue_variables = {"accountId": int(account_id), "nrql": issue_query}

            issue_response = self.client.execute_query(graphql_query, issue_variables)
            issue_data = issue_response.data["actor"]["account"]["nrql"]["results"]

            if not issue_data:
                raise NewRelicGraphQLError(f"No issue found with ID: {issue_id}")

            # Combine the data
            result = {
                "issue": issue_data[0] if issue_data else None,
                "incidents": incidents,
            }

            return result
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to parse issue details: {str(e)}")

    def get_entity_metrics_by_type(
        self,
        entity_guid: str,
        entity_type: str,
        metrics: Optional[List[str]] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        period: str = "1 minute",
        account_id: Optional[str] = None,
    ) -> Dict[str, List[MetricResult]]:
        """
        Get metrics for an entity based on its type using the configured metrics.

        Args:
            entity_guid: Entity GUID to query
            entity_type: Type of entity (e.g., KUBERNETES_POD, KUBERNETES_NODE)
            metrics: List of specific metrics to fetch (defaults to all default metrics)
            since: Start time for the query
            until: End time for the query
            period: Time bucket for the timeseries
            account_id: Optional account ID to override client default

        Returns:
            Dictionary of metrics with their timeseries data
        """
        return self.metrics.get_entity_metrics_by_type(
            entity_guid=entity_guid,
            entity_type=entity_type,
            metrics=metrics,
            since=since,
            until=until,
            period=period,
            account_id=account_id,
        )

    def execute_nrql(
        self, query: str, account_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Execute a NRQL query and return the results.

        Args:
            query: NRQL query string
            account_id: Optional account ID to override client default

        Returns:
            List of query result rows

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                    }
                }
            }
        }
        """

        variables = {"accountId": int(account_id), "nrql": query}

        try:
            response = self.client.execute_query(graphql_query, variables)
            return response.data["actor"]["account"]["nrql"]["results"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to execute NRQL query: {str(e)}")

    def get_entity_details(
        self, entity_guid: str, account_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about an entity.

        Args:
            entity_guid: Entity GUID
            account_id: Account ID (overrides default if set)

        Returns:
            Entity details dictionary or None if not found
        """
        # Get GraphQL query for entity details
        graphql_query = """
        query($entityGuid: EntityGuid!) {
          actor {
            entity(guid: $entityGuid) {
              domain
              entityType
              guid
              name
              type
              ... on InfrastructureHostEntity {
                name
                hostSummary {
                  cpuUtilizationPercent
                  diskUsedPercent
                  memoryUsedPercent
                }
              }
              ... on InfrastructureIntegrationEntity {
                integrationTypeCode
              }
              tags {
                key
                values
              }
              recentAlertViolations {
                alertSeverity
                label
                closedAt
                openedAt
              }
            }
          }
        }
        """

        # Set up variables for the query
        variables = {"entityGuid": entity_guid}

        try:
            # Execute the query
            response = self.client.execute_query(graphql_query, variables)
            entity = response.data["actor"]["entity"]
            
            # Validate and sanitize tags to prevent errors
            if "tags" in entity and isinstance(entity["tags"], list):
                sanitized_tags = []
                for tag in entity["tags"]:
                    if isinstance(tag, dict) and "key" in tag:
                        # Ensure values is a list
                        if "values" not in tag or not isinstance(tag["values"], list):
                            tag["values"] = []
                        sanitized_tags.append(tag)
                entity["tags"] = sanitized_tags
                
            return entity
        except Exception as e:
            logger.error(f"Error fetching entity details for {entity_guid}: {str(e)}")
            return None

    def find_pod_node(
        self, 
        pod_name: str, 
        cluster_id: str, 
        since_time: datetime, 
        until_time: datetime,
        account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Find the node that a pod is running on.

        Args:
            pod_name: Name of the pod
            cluster_id: Cluster ID
            since_time: Start time for the query
            until_time: End time for the query
            account_id: Account ID (overrides default if set)

        Returns:
            Name of the node if found, None otherwise
        """
        try:
            # Build NRQL query to find the node name for a pod
            query = f"""
            SELECT nodeName
            FROM K8sPodSample
            WHERE podName = '{pod_name}'
            {"AND clusterName LIKE '%" + cluster_id + "%'" if cluster_id and cluster_id != "unknown" else ""}
            SINCE '{since_time.isoformat()}'
            UNTIL '{until_time.isoformat()}'
            LIMIT 1
            """
            
            # Execute the query
            results = self.execute_nrql(query, account_id)
            if results and "nodeName" in results[0]:
                return results[0]["nodeName"]
                
        except Exception as e:
            logger.error(f"Error finding pod node: {str(e)}")
            
        return None

    def get_alert_condition_details(
        self, condition_id: str, account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get detailed information about a NRQL alert condition.

        Args:
            condition_id: The ID of the NRQL alert condition
            account_id: Optional account ID to override client default

        Returns:
            Dictionary containing alert condition details

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails or condition not found
        """
        account_id = self.client._verify_account_id(account_id)

        try:
            # GraphQL query to fetch condition details
            graphql_query = """
            query($accountId: Int!, $conditionId: ID!) {
              actor {
                account(id: $accountId) {
                  alerts {
                    nrqlCondition(id: $conditionId) {
                      description
                      id
                      enabled
                      name
                      nrql {
                        query
                      }
                      signal {
                        aggregationWindow
                        aggregationMethod
                        aggregationDelay
                        aggregationTimer
                      }
                      policyId
                      runbookUrl
                      terms {
                        operator
                        priority
                        threshold
                        thresholdDuration
                        thresholdOccurrences
                      }
                      type
                      violationTimeLimitSeconds
                    }
                  }
                }
              }
            }
            """

            variables = {"accountId": int(account_id), "conditionId": condition_id}

            response = self.client.execute_query(graphql_query, variables)

            # Extract condition from response
            condition = (
                response.data.get("actor", {})
                .get("account", {})
                .get("alerts", {})
                .get("nrqlCondition")
            )

            if not condition:
                raise NewRelicGraphQLError(
                    f"No alert condition found with ID: {condition_id}"
                )

            # Get policy name for the condition
            policy_id = condition.get("policyId")
            if policy_id:
                try:
                    policy_query = """
                    query($accountId: Int!, $policyId: ID!) {
                      actor {
                        account(id: $accountId) {
                          alerts {
                            policy(id: $policyId) {
                              id
                              name
                            }
                          }
                        }
                      }
                    }
                    """

                    policy_variables = {
                        "accountId": int(account_id),
                        "policyId": policy_id,
                    }

                    policy_response = self.client.execute_query(
                        policy_query, policy_variables
                    )
                    policy = (
                        policy_response.data.get("actor", {})
                        .get("account", {})
                        .get("alerts", {})
                        .get("policy", {})
                    )

                    if policy:
                        condition["policyName"] = policy.get("name")
                except Exception as e:
                    logger.warning(
                        f"Failed to fetch policy name for policy {policy_id}: {str(e)}"
                    )

            # Format and return the condition data
            return condition

        except Exception as e:
            raise NewRelicGraphQLError(
                f"Error fetching alert condition details: {str(e)}"
            )

================
File: README.md
================
# New Relic Library

A Python library for interacting with New Relic's NerdGraph GraphQL API. This library provides a robust interface for querying metrics, logs, events, and entity data from New Relic.

## Features

- **GraphQL Client**: Core client for making GraphQL requests to New Relic's API
- **Query Client**: Specialized client for NRQL queries and data retrieval
- **Logs Client**: Client for retrieving logs from various partitions
- **Metrics Client**: Client for fetching and analyzing metrics data
- **Entity Analyzer**: Comprehensive analysis of entities for incident investigation
- **Robust Error Handling**: Centralized retry logic for transient errors

## Components

- `client.py`: Core GraphQL client implementation
- `query.py`: Client for New Relic query operations
- `logs.py`: Client for log query operations
- `metrics.py`: Client for metrics operations
- `analyzer.py`: Entity analyzer for comprehensive information gathering
- `base.py`: Common base classes and types
- `retry_utils.py`: Centralized retry utilities
- `utils.py`: General utility functions

## Retry Functionality

The library implements a robust retry mechanism for handling transient errors in New Relic API calls:

### Centralized Retry Logic

All GraphQL queries now benefit from centralized retry functionality implemented at the `execute_query` level. This ensures consistent retry behavior across all client operations.

```python
# The execute_query method is decorated with @nr_retry
@nr_retry
def execute_query(self, query, variables=None, debug=None, debug_options=None):
    # Method implementation
```

### Retry Configuration

The retry logic is configured with:

- Exponential backoff with jitter to prevent thundering herd problems
- Specific error detection for NRDB timeouts and server errors
- Configurable retry attempts (default: 4 attempts)
- Detailed logging of retry operations

### Retry Utilities

Available in `retry_utils.py`:

- `nr_retry`: Decorator for retrying operations with configurable parameters
- `is_retriable_error`: Function to determine if an exception should trigger a retry

## Usage

```python
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient

# Initialize the core client
client = NewRelicGraphQLClient(
    api_key="YOUR_API_KEY",
    account_id="YOUR_ACCOUNT_ID"
)

# Create a query client
query_client = NewRelicQueryClient(client)

# Execute NRQL queries
results = query_client.execute_nrql(
    "SELECT count(*) FROM Transaction",
    since="1 hour ago"
)

# The client automatically handles retries for transient errors
```

## Implementation Details

The retry functionality uses the Tenacity library, which provides comprehensive retry capabilities:

- The retry decorator is applied at the `execute_query` level, ensuring all GraphQL requests benefit from retry functionality
- Retry conditions are carefully defined to only retry on known transient error patterns
- Debug logging is available to trace retry operations

## Backward Compatibility

For backward compatibility, the library maintains the legacy `retry_on_error` decorator, which now wraps the more powerful `nr_retry` functionality.

## Future Improvements

- Add support for distributed tracing
- Implement batched query operations
- Add more specific entity type metrics
- Enhance log querying capabilities

================
File: retry_utils.py
================
"""
Retry utilities for New Relic API clients.
"""

import logging
import tenacity
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from tenacity.wait import wait_random

from .base import NewRelicGraphQLError

logger = logging.getLogger(__name__)

def is_retriable_error(exception):
    """
    Determines if an exception should trigger a retry.
    Specifically looks for NRDB timeout errors and server errors.
    
    Args:
        exception: The exception to check
        
    Returns:
        True if the exception should trigger a retry, False otherwise
    """
    if isinstance(exception, NewRelicGraphQLError):
        error_message = str(exception).lower()
        
        # Check for specific NRDB error codes
        nrdb_timeout_errors = any(code in error_message for code in 
                                ["nrdb:1107", "nrdb:1109", "timeout", "query duration exceeded"])
        
        # Check for other server errors
        server_errors = any(indicator in error_message for indicator in 
                          ["server_error", "rate limit", "an error occurred resolving this field"])
        
        return nrdb_timeout_errors or server_errors
    
    return False

# Create a custom retry condition for checking retriable errors
def retry_if_retriable_error(exception):
    """Custom retry condition for checking if an exception is retriable."""
    return is_retriable_error(exception)

# Define our retry decorator with Tenacity
nr_retry = retry(
    retry=(retry_if_exception_type(NewRelicGraphQLError) & 
           tenacity.retry_if_exception(retry_if_retriable_error)),
    stop=stop_after_attempt(4),  # Try 4 times (initial + 3 retries)
    wait=wait_exponential(multiplier=1, min=1, max=10) + wait_random(0, 1),  # Exponential backoff with jitter
    before_sleep=lambda retry_state: logger.warning(
        f"New Relic API call failed with error: {retry_state.outcome.exception()}. "
        f"Retrying in {retry_state.next_action.sleep} seconds... "
        f"(Attempt {retry_state.attempt_number}/{retry_state.stop.max_attempt_number})"
    ),
    reraise=True
)

================
File: utils.py
================
"""
Utility functions for New Relic API clients.
"""

import functools
import time
import random
import logging
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union, cast

from .base import NewRelicGraphQLError
from .retry_utils import nr_retry, is_retriable_error

# Re-export the retry utilities for backward compatibility
__all__ = ['retry_on_error', 'nr_retry', 'is_retriable_error']

T = TypeVar('T')

logger = logging.getLogger(__name__)

def retry_on_error(max_attempts: int = 3, initial_delay: float = 1, backoff_factor: float = 2):
    """
    Decorator for retrying a function on specific exceptions.
    
    This is a legacy decorator that has been replaced by the more powerful
    nr_retry decorator in retry_utils.py. This is maintained for backward
    compatibility.
    
    Args:
        max_attempts: Maximum number of attempts
        initial_delay: Initial delay in seconds
        backoff_factor: Factor to increase delay by for each retry
        
    Returns:
        Decorated function
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            delay = initial_delay
            
            while attempt < max_attempts:
                try:
                    return func(*args, **kwargs)
                except NewRelicGraphQLError as e:
                    attempt += 1
                    
                    # If this was the last attempt, raise the exception
                    if attempt >= max_attempts:
                        logger.warning(f"Final retry attempt failed: {str(e)}")
                        raise
                    
                    # Check if we should retry based on the error message
                    error_message = str(e).lower()
                    
                    # Retry on specific error conditions
                    if (
                        "timeout" in error_message or
                        "nrdb:1109" in error_message or  # Common NRQL timeout error
                        "nrdb:1107" in error_message or  # Another common NRQL timeout error
                        "query duration exceeded" in error_message or
                        "server_error" in error_message or 
                        "rate limit" in error_message or
                        "an error occurred resolving this field" in error_message
                    ):
                        # Calculate delay with jitter
                        jitter = random.uniform(0, 0.5 * delay)
                        actual_delay = delay + jitter
                        
                        logger.warning(
                            f"New Relic API call failed with error: {str(e)}. "
                            f"Retrying in {actual_delay:.2f} seconds... "
                            f"(Attempt {attempt}/{max_attempts-1})"
                        )
                        
                        time.sleep(actual_delay)
                        delay *= backoff_factor
                    else:
                        # Don't retry other errors
                        logger.warning(f"Error not eligible for retry: {str(e)}")
                        raise
                except Exception as e:
                    # Don't retry other exceptions
                    logger.warning(f"Unexpected error (not retrying): {str(e)}")
                    raise
            
            return None  # This should never be reached
        
        return wrapper
    
    return decorator



================================================================
End of Codebase
================================================================
