"""
Retry utilities for New Relic API clients.
"""

import logging
import tenacity
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from tenacity.wait import wait_random

from .base import NewRelicGraphQLError

logger = logging.getLogger(__name__)

def is_retriable_error(exception):
    """
    Determines if an exception should trigger a retry.
    Specifically looks for NRDB timeout errors and server errors.
    
    Args:
        exception: The exception to check
        
    Returns:
        True if the exception should trigger a retry, False otherwise
    """
    if isinstance(exception, NewRelicGraphQLError):
        error_message = str(exception).lower()
        
        # Check for specific NRDB error codes
        nrdb_timeout_errors = any(code in error_message for code in 
                                ["nrdb:1107", "nrdb:1109", "timeout", "query duration exceeded"])
        
        # Check for other server errors
        server_errors = any(indicator in error_message for indicator in 
                          ["server_error", "rate limit", "an error occurred resolving this field"])
        
        return nrdb_timeout_errors or server_errors
    
    return False

# Define our direct retry decorator, avoiding the custom retry condition
nr_retry = retry(
    retry=retry_if_exception_type(NewRelicGraphQLError),
    stop=stop_after_attempt(4),  # Try 4 times (initial + 3 retries)
    wait=wait_exponential(multiplier=1, min=1, max=10) + wait_random(0, 1),  # Exponential backoff with jitter
    before_sleep=lambda retry_state: logger.warning(
        f"New Relic API call failed. Retrying in {retry_state.next_action.sleep} seconds... "
        f"(Attempt {retry_state.attempt_number}/{4})"
    ),
    reraise=True
) 