"""
New Relic entity analyzer for comprehensive information gathering about affected entities.
"""

import os
import re
import yaml
from typing import Any, Dict, List, Optional, Union, Set, Tuple, Pattern
from datetime import datetime, timedelta

# Remove the old logging setup
# import logging
# logger = logging.getLogger(__name__)

# Import the new logger
from .logger import get_logger, log_execution, enrich_log_context

# Set up logger for this module
logger = get_logger(__name__)

from .base import UTC, Alert, NewRelicGraphQLError
from .client import NewRelicGraphQLClient
from .query import NewRelicQueryClient
from .metrics import NewRelicMetricsClient
from .logs import NewRelicLogsClient
from .nrql_manager import NRQLManager

try:
    # Try to import the new EntityRelationshipService
    from ai_incident_manager.services.entity_relationship_service import (
        get_entity_relationship_service,
    )
    from ai_incident_manager.services.alert_to_entity_resolver import (
        get_alert_to_entity_resolver,
    )

    USE_NEW_RELATIONSHIP_SERVICE = True
    logger.info("Using new EntityRelationshipService for entity analysis")
except ImportError:
    USE_NEW_RELATIONSHIP_SERVICE = False
    logger.info("Falling back to built-in EntityRelationshipMapper for entity analysis")


class EntityDetails:
    """Container for entity details and associated data"""

    def __init__(
        self,
        entity_guid: str,
        entity_name: str,
        entity_type: str,
        cluster_id: Optional[str] = None,
        product: Optional[str] = None,
        region: Optional[str] = None,
    ):
        self.entity_guid = entity_guid
        self.entity_name = entity_name
        self.entity_type = entity_type
        self.cluster_id = cluster_id
        self.product = product
        self.region = region
        self.metrics = {}
        self.logs = []
        self.events = []
        self.related_entities = []
        self.metadata = {}
        self.relationships = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert the entity details to a dictionary"""
        return {
            "entity_guid": self.entity_guid,
            "entity_name": self.entity_name,
            "entity_type": self.entity_type,
            "cluster_id": self.cluster_id,
            "product": self.product,
            "region": self.region,
            "metrics": self.metrics,
            "logs": self.logs,
            "events": self.events,
            "related_entities": self.related_entities,
            "metadata": self.metadata,
            "relationships": self.relationships,
        }


class EntityRelationshipMapper:
    """
    Maps relationships between different New Relic entities.
    Uses a configuration file to define relationship patterns and queries.
    """

    # Common entity types and their corresponding New Relic entity type values
    ENTITY_TYPES = {
        "pod": ["KUBERNETES_POD", "K8S_POD"],
        "node": ["KUBERNETES_NODE", "K8S_NODE", "HOST"],
        "cronjob": ["KUBERNETES_CRONJOB", "K8S_CRONJOB"],
        "deployment": ["KUBERNETES_DEPLOYMENT", "K8S_DEPLOYMENT"],
        "service": ["KUBERNETES_SERVICE", "K8S_SERVICE"],
        "container": ["CONTAINER"],
        "application": ["APPLICATION"],
    }

    # Common cluster prefixes for different products
    CLUSTER_PATTERNS = {
        "neurons": {
            "nvu": ["aks-edge-rg-nvu-prd-neurons", "aks-rg-nvu-prd-neurons"],
            "uku": ["aks-rg-uku-prd-neurons", "aks-edge-rg-uku-prd-neurons"],
            "mlu": ["aks-edge-rg-mlu-prd-neurons", "aks-rg-mlu-prd-neurons"],
            "ttu": ["aks-edge-rg-ttu-prd-neurons", "aks-rg-ttu-prd-neurons"],
            "tku": ["aks-edge-rg-tku-prd-neurons", "aks-rg-tku-prd-neurons"],
        },
        "mdm": {
            "na1": ["na1", "primary-na1", "na1-eks", "north-america-1"],
            "na2": ["na2", "primary-na2", "na2-eks", "north-america-2"],
            "ap1": ["ap1", "primary-ap1", "ap1-eks", "asia-pacific-1"],
            "ap2": ["ap2", "primary-ap2", "ap2-eks", "asia-pacific-2"],
        },
    }

    def __init__(
        self,
        query_client: NewRelicQueryClient,
        config_path: Optional[str] = None,
        debug: bool = False,
    ):
        """
        Initialize the entity relationship mapper.

        Args:
            query_client: An initialized NewRelicQueryClient
            config_path: Path to the entity relationships config file
            debug: Enable debug mode for verbose output
        """
        self.query_client = query_client
        self.debug = debug

        # Load relationship configuration
        if config_path is None:
            module_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(
                module_dir, "..", "..", "config", "entity_relationships.yaml"
            )

        self.config = self._load_config(config_path)

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load entity relationships configuration from YAML file.

        Args:
            config_path: Path to the configuration file

        Returns:
            Parsed configuration dictionary
        """
        if not os.path.exists(config_path):
            logger.warning(
                f"Entity relationships config file not found at {config_path}"
            )
            return {"relationships": {}, "metadata_fields": {}, "aliases": {}}

        try:
            with open(config_path, "r") as f:
                config = yaml.safe_load(f)
                if self.debug:
                    logger.debug(f"Loaded entity relationships config: {config}")
                return config
        except Exception as e:
            logger.error(f"Error loading entity relationships config: {str(e)}")
            return {"relationships": {}, "metadata_fields": {}, "aliases": {}}

    def normalize_entity_type(self, entity_type: str) -> str:
        """
        Normalize an entity type using the alias mapping from config.

        Args:
            entity_type: The entity type to normalize

        Returns:
            Normalized entity type
        """
        aliases = self.config.get("aliases", {})
        return aliases.get(entity_type, entity_type)

    def is_entity_type(self, entity_type: str, generic_type: str) -> bool:
        """
        Check if an entity is of a specific generic type.

        Args:
            entity_type: Entity type string from New Relic
            generic_type: Generic type to check ("pod", "node", etc.)

        Returns:
            True if the entity matches the generic type, False otherwise
        """
        generic_type = generic_type.lower()
        if generic_type in self.ENTITY_TYPES:
            return entity_type in self.ENTITY_TYPES[generic_type]
        return False

    def map_relationships(
        self,
        entity_type: str,
        entity_name: str,
        cluster_name: Optional[str] = None,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
        additional_params: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Map relationships for a specific entity based on configuration.

        Args:
            entity_type: Type of the entity
            entity_name: Name of the entity
            cluster_name: Optional cluster name for Kubernetes entities
            since_time_ms: Start time in epoch milliseconds
            until_time_ms: End time in epoch milliseconds
            additional_params: Additional parameters to format into queries

        Returns:
            List of relationship dictionaries
        """
        # Normalize entity type
        entity_type = self.normalize_entity_type(entity_type)

        # Get relationships from config
        relationships_config = self.config.get("relationships", {}).get(entity_type, [])
        if not relationships_config and self.debug:
            logger.debug(f"No relationships defined for entity type: {entity_type}")

        results = []

        # Prepare parameters for query formatting
        params = {"entity_name": entity_name}

        # Set time range parameters
        if since_time_ms is not None:
            params["since_time_ms"] = since_time_ms
        if until_time_ms is not None:
            params["until_time_ms"] = until_time_ms

        if cluster_name:
            params["cluster_name"] = cluster_name
        if additional_params:
            params.update(additional_params)

        # Process each relationship
        for rel_config in relationships_config:
            try:
                rel_type = rel_config.get("type")
                relation = rel_config.get("relation")
                query_template = rel_config.get("query")

                if not rel_type or not relation or not query_template:
                    logger.warning(f"Invalid relationship config: {rel_config}")
                    continue

                # Format the query with parameters
                try:
                    query = query_template.format(**params)
                except KeyError as e:
                    logger.error(f"Missing parameter for relationship query: {e}")
                    continue

                # Execute the query
                try:
                    query_results = self.query_client.execute_nrql(query)

                    if query_results:
                        results.append(
                            {
                                "related_type": rel_type,
                                "relation": relation,
                                "query_results": query_results,
                            }
                        )
                    elif self.debug:
                        logger.debug(f"No results for relationship query: {query}")

                except Exception as e:
                    logger.error(f"Error executing relationship query: {str(e)}")
                    if self.debug:
                        logger.debug(f"Failed query: {query}")

            except Exception as e:
                logger.error(f"Error processing relationship: {str(e)}")

        return results

    def get_metadata_fields(self, entity_type: str) -> List[str]:
        """
        Get the metadata fields to extract for a specific entity type.

        Args:
            entity_type: The entity type

        Returns:
            List of metadata field names
        """
        # Normalize entity type
        entity_type = self.normalize_entity_type(entity_type)

        # Get metadata fields from config
        return self.config.get("metadata_fields", {}).get(entity_type, [])


class EntityAnalyzer:
    """
    Performs comprehensive analysis of New Relic entities.
    Gets entity details and relationship mappings, but delegates metrics
    and logs collection to specialized classes.
    """

    def __init__(self, client: NewRelicGraphQLClient, debug: bool = False):
        """
        Initialize the entity analyzer.

        Args:
            client: An initialized NewRelicGraphQLClient
            debug: Enable debug mode for verbose output
        """
        self.client = client
        self.query = NewRelicQueryClient(client)
        self.metrics = NewRelicMetricsClient(client)
        self.logs = NewRelicLogsClient(client)
        self.debug = debug

        # Use new relationship service if available, otherwise use built-in mapper
        if USE_NEW_RELATIONSHIP_SERVICE:
            # We'll use the entity relationship service through its singleton
            self.relationship_service = get_entity_relationship_service()
            # Inject query client into relationship service for queries
            # This is a temporary solution until the relationship service is updated
            # to take the query client in its constructor
            self.relationship_service.query_client = self.query
            self.alert_resolver = get_alert_to_entity_resolver()
            logger.info("Successfully initialized relationship service")
        else:
            # Fall back to the built-in mapper
            self.relationship_mapper = EntityRelationshipMapper(self.query, debug=debug)
            logger.info("Using built-in relationship mapper")

        # Initialize entity classes
        self.nrql_manager = NRQLManager()
        from .entities import PodEntity, NodeEntity, HostEntity, ApplicationEntity

        self.pod_entity = PodEntity(self.query, self.nrql_manager)
        self.node_entity = NodeEntity(self.query, self.nrql_manager)
        self.host_entity = HostEntity(self.query, self.nrql_manager)
        self.app_entity = ApplicationEntity(self.query, self.nrql_manager)

    @log_execution("get_entity_details")
    async def get_entity_details(self, entity_guid: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about an entity from New Relic.
        Public async method that delegates to the private implementation.

        Args:
            entity_guid: New Relic entity GUID

        Returns:
            Entity details dictionary or None if not found
        """
        # Create a contextual logger with entity information
        entity_logger = logger.bind(entity_guid=entity_guid, action="get_details")
        entity_logger.info(f"Retrieving entity details")
        
        try:
            # Simply call the non-async version for now
            # In a real async implementation, we'd make the GraphQL client truly async
            details = self._get_entity_details(entity_guid)
            
            if details:
                entity_type = details.get("type", "Unknown")
                entity_name = details.get("name", "Unknown")
                entity_logger.success(f"Retrieved details for {entity_type} entity: {entity_name}")
            else:
                entity_logger.warning(f"No details found for entity")
                
            return details
        except Exception as e:
            entity_logger.error(f"Error retrieving entity details: {str(e)}")
            return None

    @log_execution("get_related_entities")
    async def get_related_entities(self, entity_guid: str) -> List[Dict[str, Any]]:
        """
        Get entities related to the specified entity.
        Public async method that delegates to the non-async implementation.

        Args:
            entity_guid: New Relic entity GUID

        Returns:
            List of related entities with their details
        """
        # Create a contextual logger with entity information
        entity_logger = logger.bind(entity_guid=entity_guid, action="get_related")
        entity_logger.info(f"Retrieving related entities")
        
        try:
            # First get the entity details
            details = await self.get_entity_details(entity_guid)
            if not details:
                entity_logger.warning("Cannot retrieve related entities - primary entity details not found")
                return []
                
            entity_name = details.get("name", "Unknown")
            entity_type = details.get("type", "Unknown")
            
            # Use directly related entities from the API if available
            if "nr_related_entities" in details:
                entity_logger.info(
                    f"Using {len(details['nr_related_entities'])} related entities from New Relic API"
                )
                # Transform into expected format if needed
                return details["nr_related_entities"]

            # Otherwise, use the relationship services to find related entities
            # Map relationships - use current time for window
            now = datetime.now(UTC)
            since_time = now - timedelta(hours=1)  # 1 hour window
            until_time = now

            # Convert to epoch milliseconds
            since_time_ms = int(since_time.timestamp() * 1000)
            until_time_ms = int(until_time.timestamp() * 1000)
            
            # Get cluster name from tags
            cluster_name = None
            if "tags" in details:
                for tag in details.get("tags", []):
                    if tag.get("key") in ["clusterName", "k8s.clusterName"]:
                        cluster_name = tag.get("values", [])[0] if tag.get("values") else None
                        break
            
            # Now use the appropriate method to get related entities
            if USE_NEW_RELATIONSHIP_SERVICE:
                entity_logger.info(f"Using relationship service to find entities related to {entity_name}")
                # Get NR relationships using the relationship service
                related_entities = self.relationship_service.get_related_entities(
                    entity_guid,
                    entity_type,
                    entity_name,
                    cluster_name=cluster_name,
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )
                entity_logger.success(f"Found {len(related_entities)} related entities for {entity_name}")
                return related_entities
            else:
                # For the built-in mapper, we need to extract the cluster ID if available
                cluster_id = cluster_name
                        
                entity_logger.info(f"Using built-in mapper to find entities related to {entity_name}")
                related = self.relationship_mapper.map_relationships(
                    entity_type=entity_type,
                    entity_name=entity_name,
                    cluster_name=cluster_id,
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms
                )

                # Format the results as a flat list of entities
                result = []
                for rel in related:
                    for entity in rel["query_results"]:
                        if "entityGuid" in entity:
                            # Add relationship type to each entity
                            entity["relationship_type"] = rel["related_type"]
                            entity["relation"] = rel["relation"]
                            result.append(entity)

                entity_logger.success(f"Found {len(related)} related entities for {entity_name}")
                return result
        except Exception as e:
            entity_logger.error(f"Error retrieving related entities: {str(e)}")
            return []

    def analyze_entity(
        self,
        entity_guid: str,
        since_time: datetime,
        until_time: datetime,
        product: Optional[str] = None,
        region: Optional[str] = None,
        collect_metrics: bool = False,
        collect_logs: bool = False,
        collect_events: bool = False,
    ) -> Dict[str, Any]:
        """
        Analyze an entity to get comprehensive information.

        Args:
            entity_guid: Entity GUID
            since_time: Start time for data collection
            until_time: End time for data collection
            product: Product (optional)
            region: Region (optional)
            collect_metrics: Whether to collect metrics (optional, default False)
            collect_logs: Whether to collect logs (optional, default False)
            collect_events: Whether to collect events (optional, default False)

        Returns:
            Dictionary with entity details and associated data
        """
        # Get entity details using the query client
        entity_details = self._get_entity_details(entity_guid)

        if not entity_details:
            raise ValueError(f"Entity with GUID {entity_guid} not found")

        # Extract entity details
        entity_name = entity_details.get("name", "unknown")
        entity_type = entity_details.get("type", "unknown")

        # Extract cluster ID from tags if it's a Kubernetes entity
        cluster_id = "unknown"
        if is_pod_entity(entity_type) or is_node_entity(entity_type):
            for tag in entity_details.get("tags", []):
                if tag.get("key") == "clusterName":
                    cluster_id = (
                        tag.get("values", [])[0] if tag.get("values") else "unknown"
                    )
                    break

        # Create entity data object
        entity_data = EntityDetails(
            entity_guid=entity_guid,
            entity_name=entity_name,
            entity_type=entity_type,
            cluster_id=cluster_id,
            product=product,
            region=region,
        )

        # Get metadata
        entity_data.metadata = self._get_entity_metadata(entity_details)

        # Map relationships - this is the core responsibility of EntityAnalyzer
        # Convert to epoch milliseconds
        since_time_ms = int(since_time.timestamp() * 1000)
        until_time_ms = int(until_time.timestamp() * 1000)

        # Use the appropriate method for relationship mapping
        if USE_NEW_RELATIONSHIP_SERVICE:
            # Get both NR relationships and custom relationships
            nr_relationships = self.relationship_service.get_nr_relationships(
                entity_type,
                entity_name,
                cluster_id,
                since_time_ms=since_time_ms,
                until_time_ms=until_time_ms,
            )

            # Get custom relationships with additional context
            additional_context = {
                "cluster_name": cluster_id,
                "product": product,
                "region": region,
            }
            custom_relationships = self.relationship_service.get_custom_relationships(
                entity_type, entity_name, additional_context=additional_context
            )

            # Include any related entities from the entity details
            if "nr_related_entities" in entity_details:
                nr_relationships.extend(entity_details["nr_related_entities"])
                logger.debug(
                    f"Added {len(entity_details['nr_related_entities'])} related entities from New Relic API"
                )

            # Combine both types of relationships
            entity_data.relationships = nr_relationships + custom_relationships
            logger.debug(
                f"Mapped {len(nr_relationships)} NR relationships and {len(custom_relationships)} custom relationships"
            )
        else:
            # Use the legacy mapper
            relationships = self.relationship_mapper.map_relationships(
                entity_type,
                entity_name,
                cluster_id,
                since_time_ms=since_time_ms,
                until_time_ms=until_time_ms,
            )
            
            # Include any related entities from the entity details
            if "nr_related_entities" in entity_details:
                # We need to transform the format to match what relationship_mapper returns
                nr_relationships = []
                for rel in entity_details["nr_related_entities"]:
                    # Format for compatibility with the legacy mapper format
                    nr_rel = {
                        "related_type": rel["target"]["type"],
                        "relation": rel["relationship_type"],
                        "query_results": [{
                            "entityGuid": rel["target"]["guid"],
                            "entityName": rel["target"]["name"],
                            "entityType": rel["target"]["type"],
                        }]
                    }
                    nr_relationships.append(nr_rel)
                
                relationships.extend(nr_relationships)
                logger.debug(
                    f"Added {len(nr_relationships)} related entities from New Relic API"
                )
            
            entity_data.relationships = relationships

        # Identify metrics to collect (but don't collect them unless requested)
        metrics_config = self._identify_metrics_to_collect(entity_type)
        entity_data.metrics_to_collect = metrics_config

        # Identify logs to collect (but don't collect them unless requested)
        logs_config = self._identify_logs_to_collect(entity_type, cluster_id)
        entity_data.logs_to_collect = logs_config

        # Optionally collect metrics using appropriate entity class
        if collect_metrics:
            entity_data.metrics = self._collect_entity_metrics_using_entities(
                entity_name, entity_type, cluster_id, since_time_ms, until_time_ms
            )

        # Optionally collect logs
        if collect_logs:
            entity_data.logs = self._collect_entity_logs(
                entity_guid, entity_type, since_time, until_time, logs_config, limit=100
            )

        # Optionally collect Kubernetes events if applicable
        if collect_events and (
            is_pod_entity(entity_type) or is_node_entity(entity_type)
        ):
            entity_data.events = self._collect_entity_events(
                entity_name, entity_type, cluster_id, since_time, until_time
            )

        return entity_data.to_dict()

    def _analyze_entity_sync(
        self,
        entity_guid: str,
        since_time: datetime,
        until_time: datetime,
        product: Optional[str] = None,
        region: Optional[str] = None,
        collect_metrics: bool = False,
        collect_logs: bool = False,
        collect_events: bool = False,
    ) -> Dict[str, Any]:
        """
        Analyze an entity to get comprehensive information (sync version).

        Args:
            entity_guid: Entity GUID
            since_time: Start time for data collection
            until_time: End time for data collection
            product: Product (optional)
            region: Region (optional)
            collect_metrics: Whether to collect metrics (optional, default False)
            collect_logs: Whether to collect logs (optional, default False)
            collect_events: Whether to collect events (optional, default False)

        Returns:
            Dictionary with entity details and associated data

        Raises:
            ValueError: If the entity is not found
        """
        try:
            # Get entity details using the query client
            entity_details = self._get_entity_details(entity_guid)

            if not entity_details:
                # Return a minimal error response instead of raising an exception
                logger.error(f"Entity with GUID {entity_guid} not found")
                return {
                    "error": f"Entity with GUID {entity_guid} not found",
                    "entity_guid": entity_guid,
                    "found": False
                }

            # Extract entity details
            entity_name = entity_details.get("name", "unknown")
            entity_type = entity_details.get("type", "unknown")

            # Extract cluster ID from tags if it's a Kubernetes entity
            cluster_id = "unknown"
            if is_pod_entity(entity_type) or is_node_entity(entity_type):
                for tag in entity_details.get("tags", []):
                    # Check for both standard clusterName and k8s.clusterName formats
                    if tag.get("key") in ["clusterName", "k8s.clusterName"]:
                        cluster_id = (
                            tag.get("values", [])[0] if tag.get("values") else "unknown"
                        )
                        break

            # Create entity data object
            entity_data = EntityDetails(
                entity_guid=entity_guid,
                entity_name=entity_name,
                entity_type=entity_type,
                cluster_id=cluster_id,
                product=product,
                region=region,
            )

            # Get metadata
            entity_data.metadata = self._get_entity_metadata(entity_details)

            # Map relationships - this is the core responsibility of EntityAnalyzer
            # Convert to epoch milliseconds
            since_time_ms = int(since_time.timestamp() * 1000)
            until_time_ms = int(until_time.timestamp() * 1000)

            # Use the appropriate method for relationship mapping
            if USE_NEW_RELATIONSHIP_SERVICE:
                # Get both NR relationships and custom relationships
                nr_relationships = self.relationship_service.get_nr_relationships(
                    entity_type,
                    entity_name,
                    cluster_id,
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )

                # Get custom relationships with additional context
                additional_context = {
                    "cluster_name": cluster_id,
                    "product": product,
                    "region": region,
                }
                custom_relationships = self.relationship_service.get_custom_relationships(
                    entity_type, entity_name, additional_context=additional_context
                )

                # Include any related entities from the entity details
                if "nr_related_entities" in entity_details:
                    nr_relationships.extend(entity_details["nr_related_entities"])
                    logger.debug(
                        f"Added {len(entity_details['nr_related_entities'])} related entities from New Relic API"
                    )

                # Combine both types of relationships
                entity_data.relationships = nr_relationships + custom_relationships
                logger.debug(
                    f"Mapped {len(nr_relationships)} NR relationships and {len(custom_relationships)} custom relationships"
                )
            else:
                # Use the legacy mapper
                relationships = self.relationship_mapper.map_relationships(
                    entity_type,
                    entity_name,
                    cluster_id,
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )
                
                # Include any related entities from the entity details
                if "nr_related_entities" in entity_details:
                    # We need to transform the format to match what relationship_mapper returns
                    nr_relationships = []
                    for rel in entity_details["nr_related_entities"]:
                        # Format for compatibility with the legacy mapper format
                        nr_rel = {
                            "related_type": rel["target"]["type"],
                            "relation": rel["relationship_type"],
                            "query_results": [{
                                "entityGuid": rel["target"]["guid"],
                                "entityName": rel["target"]["name"],
                                "entityType": rel["target"]["type"],
                            }]
                        }
                        nr_relationships.append(nr_rel)
                    
                    relationships.extend(nr_relationships)
                    logger.debug(
                        f"Added {len(nr_relationships)} related entities from New Relic API"
                    )
                
                entity_data.relationships = relationships

            # Identify metrics to collect (but don't collect them unless requested)
            metrics_config = self._identify_metrics_to_collect(entity_type)
            entity_data.metrics_to_collect = metrics_config

            # Identify logs to collect (but don't collect them unless requested)
            logs_config = self._identify_logs_to_collect(entity_type, cluster_id)
            entity_data.logs_to_collect = logs_config

            # Optionally collect metrics using appropriate entity class
            if collect_metrics:
                entity_data.metrics = self._collect_entity_metrics_using_entities(
                    entity_name, entity_type, cluster_id, since_time_ms, until_time_ms
                )

            # Optionally collect logs
            if collect_logs:
                entity_data.logs = self._collect_entity_logs(
                    entity_guid, entity_type, since_time, until_time, logs_config, limit=100
                )

            # Optionally collect Kubernetes events if applicable
            if collect_events and (
                is_pod_entity(entity_type) or is_node_entity(entity_type)
            ):
                entity_data.events = self._collect_entity_events(
                    entity_name, entity_type, cluster_id, since_time, until_time
                )

            return entity_data.to_dict()
        except Exception as e:
            logger.error(f"Error analyzing entity {entity_guid}: {str(e)}")
            return {
                "error": f"Error analyzing entity: {str(e)}",
                "entity_guid": entity_guid,
                "found": False
            }

    async def analyze_entity(
        self,
        entity_guid: str,
        since_time: datetime,
        until_time: datetime,
        product: Optional[str] = None,
        region: Optional[str] = None,
        collect_metrics: bool = False,
        collect_logs: bool = False,
        collect_events: bool = False,
    ) -> Dict[str, Any]:
        """
        Analyze an entity to get comprehensive information (async version).

        Args:
            entity_guid: Entity GUID
            since_time: Start time for data collection
            until_time: End time for data collection
            product: Product (optional)
            region: Region (optional)
            collect_metrics: Whether to collect metrics (optional, default False)
            collect_logs: Whether to collect logs (optional, default False)
            collect_events: Whether to collect events (optional, default False)

        Returns:
            Dictionary with entity details and associated data
        """
        try:
            # Simply call the non-async version for now
            # In a real async implementation, we'd make the underlying calls truly async
            return self._analyze_entity_sync(
                entity_guid=entity_guid,
                since_time=since_time,
                until_time=until_time,
                product=product,
                region=region,
                collect_metrics=collect_metrics,
                collect_logs=collect_logs,
                collect_events=collect_events,
            )
        except Exception as e:
            logger.error(f"Error in async analyze_entity for {entity_guid}: {str(e)}")
            return {
                "error": f"Error analyzing entity: {str(e)}",
                "entity_guid": entity_guid,
                "found": False
            }

    def _get_entity_details(self, entity_guid: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about an entity from New Relic.

        Args:
            entity_guid: New Relic entity GUID

        Returns:
            Entity details dictionary or None if not found
        """
        try:
            # Use query client's method to get entity details
            result = self.query.get_entity_details(entity_guid)
            
            # Check if we got a valid result
            if not result:
                logger.warning(f"Entity with GUID {entity_guid} not found or returned empty")
                return None
            
            # Extract and process related entities if present
            if "relatedEntities" in result and result["relatedEntities"] is not None and "results" in result["relatedEntities"]:
                related_entities = []
                for relationship in result["relatedEntities"]["results"]:
                    # Extract source entity
                    source = relationship.get("source", {})
                    source_entity = source.get("entity", {})
                    
                    # Extract target entity
                    target = relationship.get("target", {})
                    target_entity = target.get("entity", {})
                    
                    # Extract relationship type
                    relationship_type = relationship.get("type", "UNKNOWN")
                    
                    # Format the relationship
                    formatted_relationship = {
                        "source": {
                            "guid": source_entity.get("guid"),
                            "name": source_entity.get("name"),
                            "type": source_entity.get("type")
                        },
                        "target": {
                            "guid": target_entity.get("guid"),
                            "name": target_entity.get("name"),
                            "type": target_entity.get("type")
                        },
                        "relationship_type": relationship_type
                    }
                    
                    related_entities.append(formatted_relationship)
                
                # Store the related entities in the result
                result["nr_related_entities"] = related_entities
            
            return result
        except Exception as e:
            if self.debug:
                logger.error(
                    f"Error fetching entity details for {entity_guid}: {str(e)}"
                )
            return None

    def _get_entity_metadata(self, entity_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract metadata from entity details based on the entity type using configuration.

        Args:
            entity_details: Entity details dictionary

        Returns:
            Metadata dictionary
        """
        metadata = {}
        
        # Check if entity_details is None or empty
        if not entity_details:
            logger.warning("Cannot extract metadata from empty entity details")
            return metadata
        
        entity_type = entity_details.get("type")

        if not entity_type:
            logger.warning("Entity type not found in entity details")
            return metadata

        # Get metadata fields from configuration
        try:
            if USE_NEW_RELATIONSHIP_SERVICE:
                metadata_fields = self.relationship_service.get_metadata_fields(entity_type)
            else:
                metadata_fields = self.relationship_mapper.get_metadata_fields(entity_type)
        except Exception as e:
            logger.warning(f"Error getting metadata fields for entity type {entity_type}: {str(e)}")
            metadata_fields = []

        # Extract tags as a separate field (always included)
        tags = {}
        if "tags" in entity_details and isinstance(entity_details["tags"], list):
            for tag in entity_details["tags"]:
                if isinstance(tag, dict) and "key" in tag:
                    tags[tag.get("key")] = tag.get("values", [])
        metadata["tags"] = tags

        # Process nested fields using dot notation (e.g., "condition.Ready")
        for field in metadata_fields:
            try:
                if "." in field:
                    # Handle nested fields
                    parts = field.split(".")
                    value = entity_details
                    for part in parts:
                        if isinstance(value, dict) and part in value:
                            value = value[part]
                        else:
                            value = None
                            break
                    if value is not None:
                        metadata[field] = value
                else:
                    # Handle regular fields
                    if field in entity_details:
                        metadata[field] = entity_details[field]
            except Exception as e:
                logger.warning(f"Error extracting metadata field {field}: {str(e)}")
                continue

        return metadata

    def _identify_metrics_to_collect(self, entity_type: str) -> Dict[str, Any]:
        """
        Identify which metrics should be collected for this entity type.

        Args:
            entity_type: Entity type

        Returns:
            Dictionary of metric configurations
        """
        # Delegate to metrics client for configuration
        return self.metrics.get_metrics_config_for_entity_type(entity_type)

    def _identify_logs_to_collect(
        self, entity_type: str, cluster_id: str
    ) -> Dict[str, Any]:
        """
        Identify which logs should be collected for this entity type.

        Args:
            entity_type: Entity type
            cluster_id: Cluster ID

        Returns:
            Dictionary of log configurations
        """
        # Delegate to logs client for configuration
        return self.logs.get_logs_config_for_entity_type(entity_type, cluster_id)

    def _collect_entity_metrics_using_entities(
        self,
        entity_name: str,
        entity_type: str,
        cluster_name: Optional[str] = None,
        since_time_ms: Optional[int] = None,
        until_time_ms: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Collect metrics for an entity using appropriate entity class.

        Args:
            entity_name: Name of the entity
            entity_type: Type of the entity
            cluster_name: Optional cluster name for Kubernetes entities
            since_time_ms: Start time in epoch milliseconds
            until_time_ms: End time in epoch milliseconds

        Returns:
            Dictionary of metrics data
        """
        try:
            metrics = {}
            normalized_type = self.relationship_mapper.normalize_entity_type(
                entity_type
            )

            # Use the appropriate entity class based on entity type
            if is_pod_entity(normalized_type):
                logger.debug(f"Using PodEntity to collect metrics for {entity_name}")
                metrics["cpu"] = self.pod_entity.get_cpu_usage(
                    pod_name=entity_name,
                    cluster_name=cluster_name or "unknown",
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )
                metrics["memory"] = self.pod_entity.get_memory_usage(
                    pod_name=entity_name,
                    cluster_name=cluster_name or "unknown",
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )
                metrics["restarts"] = self.pod_entity.get_restart_count(
                    pod_name=entity_name,
                    cluster_name=cluster_name or "unknown",
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )
                metrics["containers"] = self.pod_entity.get_container_status(
                    pod_name=entity_name,
                    cluster_name=cluster_name or "unknown",
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )

            elif is_node_entity(normalized_type):
                logger.debug(f"Using NodeEntity to collect metrics for {entity_name}")
                metrics["cpu"] = self.node_entity.get_cpu_usage(
                    node_name=entity_name,
                    cluster_name=cluster_name or "unknown",
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )
                metrics["memory"] = self.node_entity.get_memory_usage(
                    node_name=entity_name,
                    cluster_name=cluster_name or "unknown",
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )
                metrics["pod_count"] = self.node_entity.get_pod_count(
                    node_name=entity_name,
                    cluster_name=cluster_name or "unknown",
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )

            elif normalized_type == "HOST":
                logger.debug(f"Using HostEntity to collect metrics for {entity_name}")
                metrics["system"] = self.host_entity.get_cpu_memory_disk(
                    hostname=entity_name,
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )
                metrics["network"] = self.host_entity.get_network_io(
                    hostname=entity_name,
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )

            elif normalized_type == "APPLICATION":
                logger.debug(
                    f"Using ApplicationEntity to collect metrics for {entity_name}"
                )
                metrics["response_time"] = self.app_entity.get_response_time(
                    app_name=entity_name,
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )
                metrics["throughput"] = self.app_entity.get_throughput(
                    app_name=entity_name,
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )
                metrics["error_rate"] = self.app_entity.get_error_rate(
                    app_name=entity_name,
                    since_time_ms=since_time_ms,
                    until_time_ms=until_time_ms,
                )

            else:
                logger.warning(
                    f"No specialized entity class available for type: {entity_type}"
                )
                # Fall back to generic metrics collection method
                return self._collect_entity_metrics(
                    "unknown",
                    entity_type,
                    datetime.fromtimestamp(since_time_ms / 1000, UTC),
                    datetime.fromtimestamp(until_time_ms / 1000, UTC),
                    self._identify_metrics_to_collect(entity_type),
                )

            return metrics

        except Exception as e:
            if self.debug:
                logger.error(
                    f"Error collecting metrics for {entity_name} ({entity_type}): {str(e)}"
                )
            return {}

    def _collect_entity_metrics(
        self,
        entity_guid: str,
        entity_type: str,
        since_time: datetime,
        until_time: datetime,
        metrics_config: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Collect metrics for an entity using metrics client.
        Used as a fallback when entity-specific methods are not available.

        Args:
            entity_guid: New Relic entity GUID
            entity_type: Entity type
            since_time: Start time for metrics
            until_time: End time for metrics
            metrics_config: Optional metrics configuration

        Returns:
            Dictionary of metrics data
        """
        try:
            # Use the metrics client to get entity metrics
            return self.metrics.get_entity_metrics_by_type(
                entity_guid=entity_guid,
                entity_type=entity_type,
                since=since_time,
                until=until_time,
                metrics_config=metrics_config,
            )
        except Exception as e:
            if self.debug:
                logger.error(f"Error collecting metrics for {entity_guid}: {str(e)}")
            return {}

    def _collect_entity_logs(
        self,
        entity_guid: str,
        entity_type: str,
        since_time: datetime,
        until_time: datetime,
        logs_config: Dict[str, Any] = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        Collect logs for an entity using logs client.

        Args:
            entity_guid: Entity GUID
            entity_type: Entity type
            since_time: Start time for logs
            until_time: End time for logs
            logs_config: Optional logs configuration
            limit: Maximum number of logs to return

        Returns:
            List of log entries
        """
        try:
            # Use the logs client to get entity logs
            return self.logs.get_entity_logs(
                entity_guid=entity_guid,
                entity_type=entity_type,
                since=since_time,
                until=until_time,
                logs_config=logs_config,
                limit=limit,
            )
        except Exception as e:
            if self.debug:
                logger.error(f"Error collecting logs for {entity_guid}: {str(e)}")
            return []

    def _collect_entity_events(
        self,
        entity_name: str,
        entity_type: str,
        cluster_id: str,
        since_time: datetime,
        until_time: datetime,
    ) -> List[Dict[str, Any]]:
        """
        Collect Kubernetes events for an entity.

        Args:
            entity_name: Entity name
            entity_type: Entity type
            cluster_id: Cluster ID
            since_time: Start time for events
            until_time: End time for events

        Returns:
            List of Kubernetes events
        """
        events = []
        object_kind = "Pod" if is_pod_entity(entity_type) else "Node"

        try:
            # Use query client to get Kubernetes events
            entity_events = self.query.get_kubernetes_events(
                object_name=entity_name,
                object_kind=object_kind,
                cluster_name=cluster_id,
                since=since_time,
                until=until_time,
            )

            # Add source field to entity events
            for event in entity_events:
                event["source"] = object_kind.lower()

            events.extend(entity_events)

            # For pods, also get events for the underlying node
            if is_pod_entity(entity_type):
                # Find the node for this pod using query client
                node_name = self.query.find_pod_node(
                    pod_name=entity_name,
                    cluster_id=cluster_id,
                    since_time=since_time,
                    until_time=until_time,
                )

                if node_name:
                    # Get events for this node
                    node_events = self.query.get_kubernetes_events(
                        object_name=node_name,
                        object_kind="Node",
                        cluster_name=cluster_id,
                        since=since_time,
                        until=until_time,
                    )

                    # Add source field to node events
                    for event in node_events:
                        event["source"] = "node"

                    events.extend(node_events)

            return events

        except Exception as e:
            if self.debug:
                logger.error(f"Error collecting events for {entity_name}: {str(e)}")
            return []


# Utility functions to avoid duplicated code
def is_pod_entity(entity_type: str) -> bool:
    """Check if an entity is a Kubernetes pod."""
    return entity_type in ["KUBERNETES_POD", "K8S_POD"]


def is_node_entity(entity_type: str) -> bool:
    """Check if an entity is a Kubernetes node."""
    return entity_type in ["KUBERNETES_NODE", "K8S_NODE"]
