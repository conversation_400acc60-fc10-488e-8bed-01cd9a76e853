"""
New Relic GraphQL client for query (read) operations.
"""

from typing import Any, Dict, List, Optional, Union, cast
from datetime import datetime, timedelta

from .base import UTC, GraphQLResponse, MetricResult, Alert, format_datetime
from .client import NewRelicGraphQLClient, NewRelicGraphQLError
from .metrics import NewRelicMetricsClient
from .logger import get_logger, log_execution, format_nrql_query, format_nrql_results, safe_format_for_logging

# Remove the old logging setup
# import logging
# logger = logging.getLogger(__name__)

# Get a logger specific to this module
logger = get_logger(__name__)

class NewRelicQueryClient:
    """
    Client for New Relic query (read) operations.
    Provides methods for fetching data from New Relic.
    """

    def __init__(self, client: NewRelicGraphQLClient):
        """
        Initialize the New Relic query client.

        Args:
            client: The core New Relic GraphQL client instance
        """
        self.client = client
        self.metrics = NewRelicMetricsClient(client)  # Initialize metrics client
        logger.debug(f"Initialized NewRelicQueryClient with account ID: {client.account_id}")

    @log_execution("get_all_policy_ids")
    def get_all_policy_ids(self, account_id: Optional[str] = None) -> List[str]:
        """
        Get all alert policy IDs for an account.

        Args:
            account_id: Account ID (overrides default if set)

        Returns:
            List of policy IDs

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)
        
        graphql_query = """
        query($accountId: Int!) {
            actor {
                account(id: $accountId) {
                    alerts {
                        policiesSearch {
                            policies {
                                id
                            }
                        }
                    }
                }
            }
        }
        """

        variables = {"accountId": int(account_id)}
        logger.debug(f"Executing get_all_policy_ids query for account: {account_id}")
        response = self.client.execute_query(graphql_query, variables)

        try:
            policies = response.data["actor"]["account"]["alerts"]["policiesSearch"][
                "policies"
            ]
            policy_ids = [policy["id"] for policy in policies]
            logger.debug(f"Found {len(policy_ids)} policy IDs for account {account_id}")
            return policy_ids
        except (KeyError, TypeError) as e:
            logger.error(f"Failed to extract policy IDs from response: {e}")
            return []

    def get_webhook_destination(
        self, name: str, url: str, account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get a webhook destination ID by name and URL.

        Args:
            name: Webhook name
            url: Webhook URL
            account_id: Account ID (overrides default if set)

        Returns:
            Webhook destination ID if found, None otherwise

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)

        graphql_query = """
        query($accountId: Int!, $name: String!, $url: String!) {
            actor {
                account(id: $accountId) {
                    aiNotifications {
                        destinations(filters: {
                            name: $name,
                            type: WEBHOOK,
                            property: { key: "url", value: $url }
                        }) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """

        variables = {"accountId": int(account_id), "name": name, "url": url}

        response = self.client.execute_query(graphql_query, variables)

        try:
            destinations = response.data["actor"]["account"]["aiNotifications"][
                "destinations"
            ]["entities"]
            return destinations[0]["id"] if destinations else None
        except (KeyError, IndexError):
            return None

    def get_notification_channel(
        self, destination_id: str, name: str, account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get a notification channel ID by destination ID and name.

        Args:
            destination_id: Destination ID
            name: Channel name
            account_id: Account ID (overrides default if set)

        Returns:
            Channel ID if found, None otherwise

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)

        graphql_query = """
        query($accountId: Int!, $destinationId: ID!, $name: String!) {
            actor {
                account(id: $accountId) {
                    aiNotifications {
                        channels(filters: {
                            destinationId: $destinationId,
                            name: $name
                        }) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "destinationId": destination_id,
            "name": name,
        }

        response = self.client.execute_query(graphql_query, variables)

        try:
            channels = response.data["actor"]["account"]["aiNotifications"]["channels"][
                "entities"
            ]
            return channels[0]["id"] if channels else None
        except (KeyError, IndexError):
            return None

    def get_workflow(
        self, name: str, channel_id: str, account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get a workflow ID by name and channel ID.

        Args:
            name: Workflow name
            channel_id: Channel ID
            account_id: Account ID (overrides default if set)

        Returns:
            Workflow ID if found, None otherwise

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)

        graphql_query = """
        query($accountId: Int!, $name: String!, $channelId: ID!) {
            actor {
                account(id: $accountId) {
                    aiWorkflows {
                        workflows(
                            filters: {name: $name, channelId: $channelId}
                        ) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "name": name,
            "channelId": channel_id,
        }

        response = self.client.execute_query(graphql_query, variables)

        try:
            workflows = response.data["actor"]["account"]["aiWorkflows"]["workflows"][
                "entities"
            ]
            return workflows[0]["id"] if workflows else None
        except (KeyError, IndexError):
            return None

    # def get_pod_details(
    #     self,
    #     pod_name: str,
    #     since: Optional[datetime] = None,
    #     until: Optional[datetime] = None,
    #     account_id: Optional[str] = None
    # ) -> List[Dict[str, Any]]:
    #     """
    #     Get detailed information about a specific pod.

    #     Args:
    #         pod_name: Name of the pod
    #         since: Start time for the query
    #         until: End time for the query
    #         account_id: Optional account ID override

    #     Returns:
    #         List containing pod details

    #     Raises:
    #         ValueError: If no account_id is available
    #         NewRelicGraphQLError: If the query fails
    #     """
    #     account_id = self.client._verify_account_id(account_id)
    #     since = since or (datetime.now(UTC) - timedelta(minutes=30))
    #     until = until or datetime.now(UTC)

    #     graphql_query = """
    #     query($accountId: Int!, $nrql: Nrql!) {
    #         actor {
    #             account(id: $accountId) {
    #                 nrql(query: $nrql) {
    #                     results
    #                 }
    #             }
    #         }
    #     }
    #     """

    #     nrql = f"""
    #     SELECT *
    #     FROM K8sPodSample
    #     WHERE podName = '{pod_name}'
    #     SINCE '{format_datetime(since)}'
    #     UNTIL '{format_datetime(until)}'
    #     LIMIT 100
    #     """

    #     variables = {
    #         "accountId": int(account_id),
    #         "nrql": nrql
    #     }

    #     response = self.client.execute_query(graphql_query, variables)
    #     try:
    #         return response.data["actor"]["account"]["nrql"]["results"]
    #     except (KeyError, TypeError) as e:
    #         raise NewRelicGraphQLError(f"Failed to fetch pod details: {str(e)}")

    # def get_infrastructure_events(
    #     self,
    #     node_name: str,
    #     cluster_name: Optional[str] = None,
    #     start_time: Optional[datetime] = None,
    #     end_time: Optional[datetime] = None,
    #     account_id: Optional[str] = None,
    #     event_type: str = "Warning"
    # ) -> List[Dict[str, Any]]:
    #     """
    #     Fetch infrastructure events for a specific node.

    #     Args:
    #         node_name: Name of the node
    #         cluster_name: Optional cluster name for filtering
    #         start_time: Start time for the query (defaults to 24 hours ago)
    #         end_time: End time for the query (defaults to now)
    #         account_id: Account ID (overrides default if set)
    #         event_type: Type of events to query (default: "Warning")

    #     Returns:
    #         List of infrastructure events

    #     Raises:
    #         ValueError: If no account_id is available
    #         NewRelicGraphQLError: If the query fails
    #     """
    #     account_id = self.client._verify_account_id(account_id)
    #     start_time = start_time or (datetime.now(UTC) - timedelta(days=1))
    #     end_time = end_time or datetime.now(UTC)

    #     # Build cluster filter
    #     cluster_filter = f"AND clusterName = '{cluster_name}'" if cluster_name else ""

    #     graphql_query = """
    #     query($accountId: Int!, $nrql: Nrql!) {
    #         actor {
    #             account(id: $accountId) {
    #                 nrql(query: $nrql) {
    #                     results
    #                 }
    #             }
    #         }
    #     }
    #     """

    #     nrql = f"""
    #     SELECT *
    #     FROM InfrastructureEvent
    #     WHERE category = 'kubernetes'
    #     {cluster_filter}
    #     AND event.involvedObject.name = '{node_name}'
    #     AND event.type = '{event_type}'
    #     SINCE '{format_datetime(start_time)}'
    #     UNTIL '{format_datetime(end_time)}'
    #     LIMIT MAX
    #     """

    #     variables = {
    #         "accountId": int(account_id),
    #         "nrql": nrql
    #     }

    #     response = self.client.execute_query(graphql_query, variables)

    #     try:
    #         return response.data["actor"]["account"]["nrql"]["results"]
    #     except (KeyError, TypeError) as e:
    #         raise NewRelicGraphQLError(f"Failed to parse infrastructure events: {str(e)}")

    def get_kubernetes_events(
        self,
        object_name: str,
        object_kind: str = "Pod",
        cluster_name: Optional[str] = None,
        account_id: Optional[str] = None,
        since: Optional[Union[str, int, datetime]] = None,
        until: Optional[Union[str, int, datetime]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Fetch Kubernetes events for a specific object (Pod or Node).

        Example usage:
        # For a specific node
        node_events = client.query.get_kubernetes_events("aks-default-12345-vmss001", object_kind="Node")

        # For nodes with wildcard pattern
        node_events = client.query.get_kubernetes_events("%vmss%", object_kind="Node")

        # For a specific pod
        pod_events = client.query.get_kubernetes_events("my-pod-name")

        # With specific timestamps (epoch milliseconds) - RECOMMENDED APPROACH
        events = client.query.get_kubernetes_events("my-pod", since=*************, until=*************)

        # With formatted datetime strings
        events = client.query.get_kubernetes_events("my-pod", since="2025-03-28 10:00:00", until="2025-03-28 11:00:00")

        Args:
            object_name: Name of the object (pod or node)
            object_kind: Kind of object ('Pod' or 'Node')
            cluster_name: Optional cluster name filter
            account_id: Account ID (overrides default if set)
            since: Start time for the query (as epoch milliseconds, formatted string, or datetime object)
            until: End time for the query (as epoch milliseconds, formatted string, or datetime object)

        Returns:
            List of Kubernetes events

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)

        # Format the since timestamp - prioritize epoch milliseconds
        if since is None:
            logger.info("No 'since' value provided, using default (30 minutes ago)")
            since_timestamp = "SINCE 30 MINUTES AGO"
        elif isinstance(since, int):
            # Treat as epoch milliseconds - prioritized approach
            logger.info(f"Using 'since' as epoch milliseconds: {since}")
            since_timestamp = f"SINCE {since}"
        elif isinstance(since, str) and since.isdigit():
            # If string is numeric, convert to int and treat as epoch milliseconds
            since_int = int(since)
            logger.info(f"Converting 'since' string to epoch milliseconds: {since_int}")
            since_timestamp = f"SINCE {since_int}"
        elif isinstance(since, datetime):
            logger.info(f"Converting 'since' datetime to formatted string: {format_datetime(since)}")
            since_timestamp = f"SINCE '{format_datetime(since)}'"
        else:
            # Treat as formatted string
            logger.info(f"Using 'since' as formatted string: {since}")
            since_timestamp = f"SINCE '{since}'"

        # Format the until timestamp - prioritize epoch milliseconds
        if until is None:
            logger.info("No 'until' value provided, using default (1 minute ago)")
            until_timestamp = "UNTIL 1 MINUTE AGO"
        elif isinstance(until, int):
            # Treat as epoch milliseconds - prioritized approach
            logger.info(f"Using 'until' as epoch milliseconds: {until}")
            until_timestamp = f"UNTIL {until}"
        elif isinstance(until, str) and until.isdigit():
            # If string is numeric, convert to int and treat as epoch milliseconds
            until_int = int(until)
            logger.info(f"Converting 'until' string to epoch milliseconds: {until_int}")
            until_timestamp = f"UNTIL {until_int}"
        elif isinstance(until, datetime):
            logger.info(f"Converting 'until' datetime to formatted string: {format_datetime(until)}")
            until_timestamp = f"UNTIL '{format_datetime(until)}'"
        else:
            # Treat as formatted string
            logger.info(f"Using 'until' as formatted string: {until}")
            until_timestamp = f"UNTIL '{until}'"

        # Validate time window relationship if both are explicit values
        if isinstance(since, (int, datetime)) and isinstance(until, (int, datetime)):
            since_val = since if isinstance(since, int) else int(since.timestamp() * 1000)
            until_val = until if isinstance(until, int) else int(until.timestamp() * 1000)
            
            if until_val <= since_val:
                logger.warning(f"Invalid time window: until ({until_val}) <= since ({since_val})")

        # Build name filter
        name_filter = f"event.involvedObject.name LIKE '%{object_name}%'"

        # Add cluster filter if provided
        cluster_filter = f"AND clusterName LIKE '%{cluster_name}%'" if cluster_name else ""

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        nrql = f"""
        SELECT event.involvedObject.name, event.involvedObject.kind, event.reason, event.message, timestamp
        FROM InfrastructureEvent
        WHERE category = 'kubernetes'
        AND event.involvedObject.kind LIKE '%{object_kind}%'
        AND {name_filter}
        {cluster_filter}
        {since_timestamp}
        {until_timestamp}
        """

        logger.info(f"Executing NRQL query for {object_kind} events with time window: {since_timestamp} to {until_timestamp}")
        
        variables = {"accountId": int(account_id), "nrql": nrql}

        logger.info(f"NRQL query: {nrql}")
        response = self.client.execute_query(graphql_query, variables)

        try:
            results = response.data["actor"]["account"]["nrql"]["results"]
            # Use safe formatting for the results to avoid Loguru formatting errors
            logger.info(f"NRQL query results: {safe_format_for_logging(str(results))}")
            logger.info(f"Found {len(results)} Kubernetes events for {object_kind} {object_name}")
            return results
        except (KeyError, TypeError) as e:
            error_msg = f"Failed to parse Kubernetes events: {str(e)}"
            logger.error(error_msg)
            raise NewRelicGraphQLError(error_msg)

    def get_issue_details(
        self, issue_id: str, account_id: Optional[str] = None, lookback: str = "1 month"
    ) -> Dict[str, Any]:
        """
        Fetch issue details from New Relic including related incidents.

        Args:
            issue_id: The New Relic issue ID
            account_id: Account ID (overrides default if set)
            lookback: Time period to look back for incidents (default: "1 month")

        Returns:
            Dictionary containing issue details including related incidents

        Raises:
            ValueError: If no account_id is available or issue_id is empty
            NewRelicGraphQLError: If the query fails
        """
        if not issue_id:
            raise ValueError("Issue ID cannot be None or empty")

        account_id = self.client._verify_account_id(account_id)

        # Query to get incidents related to the issue
        nrql_query = f"""
        FROM NrAiIncident
        SELECT *
        WHERE incidentId in (
            FROM NrAiIssue
            SELECT getfield(incidentIds,0)
            WHERE issueId = '{issue_id}'
            SINCE {lookback} AGO
        )
        SINCE {lookback} AGO
        """

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                    }
                }
            }
        }
        """

        variables = {"accountId": int(account_id), "nrql": nrql_query}

        response = self.client.execute_query(graphql_query, variables)

        try:
            incidents = response.data["actor"]["account"]["nrql"]["results"]

            # Now get the issue itself
            issue_query = f"""
            FROM NrAiIssue
            SELECT *
            WHERE issueId = '{issue_id}'
            SINCE {lookback} AGO
            LIMIT 1
            """

            issue_variables = {"accountId": int(account_id), "nrql": issue_query}

            issue_response = self.client.execute_query(graphql_query, issue_variables)
            issue_data = issue_response.data["actor"]["account"]["nrql"]["results"]

            if not issue_data:
                raise NewRelicGraphQLError(f"No issue found with ID: {issue_id}")

            # Combine the data
            result = {
                "issue": issue_data[0] if issue_data else None,
                "incidents": incidents,
            }

            return result
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to parse issue details: {str(e)}")

    def get_entity_metrics_by_type(
        self,
        entity_guid: str,
        entity_type: str,
        metrics: Optional[List[str]] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        period: str = "1 minute",
        account_id: Optional[str] = None,
    ) -> Dict[str, List[MetricResult]]:
        """
        Get metrics for an entity based on its type using the configured metrics.

        Args:
            entity_guid: Entity GUID to query
            entity_type: Type of entity (e.g., KUBERNETES_POD, KUBERNETES_NODE)
            metrics: List of specific metrics to fetch (defaults to all default metrics)
            since: Start time for the query
            until: End time for the query
            period: Time bucket for the timeseries
            account_id: Optional account ID to override client default

        Returns:
            Dictionary of metrics with their timeseries data
        """
        return self.metrics.get_entity_metrics_by_type(
            entity_guid=entity_guid,
            entity_type=entity_type,
            metrics=metrics,
            since=since,
            until=until,
            period=period,
            account_id=account_id,
        )

    @log_execution("execute_nrql")
    def execute_nrql(
        self, query: str, account_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Execute a NRQL query and return the results.

        Args:
            query: NRQL query string
            account_id: Optional account ID to override client default

        Returns:
            List of query result rows

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self.client._verify_account_id(account_id)

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                    }
                }
            }
        }
        """

        variables = {"accountId": int(account_id), "nrql": query}

        try:
            # Create a contextual logger with query information
            query_logger = logger.bind(
                account_id=account_id,
                query_type="NRQL"
            )
            
            # Log the formatted NRQL query
            query_logger.info(format_nrql_query(query))
            
            response = self.client.execute_query(graphql_query, variables)
            
            results = response.data["actor"]["account"]["nrql"]["results"]
            
            try:
                # Log the formatted results using our custom formatter
                # This should already be safe against Loguru formatting issues
                # logger.info(results) # for temp testing # REMOVE LATER 
                query_logger.success(format_nrql_results(results))
            except Exception as format_error:
                # If there's an issue formatting the results, log safely
                query_logger.warning(f"Could not format query results properly: {format_error}")
                query_logger.info(f"Raw results count: {len(results)}")
            
            return results
        except (KeyError, TypeError) as e:
            logger.error(f"Failed to execute NRQL query: {safe_format_for_logging(str(e))}")
            return []
        except Exception as e:
            logger.error(f"Error executing NRQL query: {safe_format_for_logging(str(e))}")
            raise

    def get_entity_details(
        self, entity_guid: str, account_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about an entity.

        Args:
            entity_guid: Entity GUID
            account_id: Account ID (overrides default if set)

        Returns:
            Entity details dictionary or None if not found
        """
        # Get GraphQL query for entity details
        graphql_query = """
        query($entityGuid: EntityGuid!) {
          actor {
            entity(guid: $entityGuid) {
              domain
              entityType
              guid
              name
              type
              ... on InfrastructureHostEntity {
                name
                hostSummary {
                  cpuUtilizationPercent
                  diskUsedPercent
                  memoryUsedPercent
                }
              }
              ... on InfrastructureIntegrationEntity {
                integrationTypeCode
              }
              tags {
                key
                values
              }
              recentAlertViolations {
                alertSeverity
                label
                closedAt
                openedAt
              }
              relatedEntities {
                results {
                    type
                    source {
                        guid
                        entity {
                            guid
                            name
                            type
                        }
                    }
                    target {
                        entity {
                            guid
                            type
                            name
                        }
                    }
                }
              }
            }
          }
        }
        """

        # Set up variables for the query
        variables = {"entityGuid": entity_guid}

        try:
            # Execute the query
            response = self.client.execute_query(graphql_query, variables)
            entity = response.data["actor"]["entity"]
            
            # Check if entity is None (entity not found)
            if entity is None:
                logger.warning(f"Entity with GUID {entity_guid} not found in New Relic")
                return None
            
            if "tags" in entity and isinstance(entity["tags"], list):
                tag_dict = {}
                for tag in entity["tags"]:
                    if isinstance(tag, dict) and "key" in tag:
                        values = tag.get("values", [])
                        tag_dict[tag["key"]] = values[0] if values else None
                entity["tags"] = tag_dict
                
            return entity
        except Exception as e:
            logger.error(f"Error fetching entity details for {entity_guid}: {str(e)}")
            return None

    def find_pod_node(
        self, 
        pod_name: str, 
        cluster_id: str, 
        since_time: datetime, 
        until_time: datetime,
        account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Find the node that a pod is running on.

        Args:
            pod_name: Name of the pod
            cluster_id: Cluster ID
            since_time: Start time for the query
            until_time: End time for the query
            account_id: Account ID (overrides default if set)

        Returns:
            Name of the node if found, None otherwise
        """
        try:
            # Build NRQL query to find the node name for a pod
            query = f"""
            SELECT nodeName
            FROM K8sPodSample
            WHERE podName = '{pod_name}'
            {"AND clusterName LIKE '%" + cluster_id + "%'" if cluster_id and cluster_id != "unknown" else ""}
            SINCE '{since_time.isoformat()}'
            UNTIL '{until_time.isoformat()}'
            LIMIT 1
            """
            
            # Execute the query
            results = self.execute_nrql(query, account_id)
            if results and "nodeName" in results[0]:
                return results[0]["nodeName"]
                
        except Exception as e:
            logger.error(f"Error finding pod node: {str(e)}")
            
        return None

    def get_alert_condition_details(
        self, condition_id: str, account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get detailed information about a NRQL alert condition.

        Args:
            condition_id: The ID of the NRQL alert condition
            account_id: Optional account ID to override client default

        Returns:
            Dictionary containing alert condition details

        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails or condition not found
        """
        account_id = self.client._verify_account_id(account_id)

        try:
            # GraphQL query to fetch condition details
            graphql_query = """
            query($accountId: Int!, $conditionId: ID!) {
              actor {
                account(id: $accountId) {
                  alerts {
                    nrqlCondition(id: $conditionId) {
                      description
                      id
                      enabled
                      name
                      nrql {
                        query
                      }
                      signal {
                        aggregationWindow
                        aggregationMethod
                        aggregationDelay
                        aggregationTimer
                      }
                      policyId
                      runbookUrl
                      terms {
                        operator
                        priority
                        threshold
                        thresholdDuration
                        thresholdOccurrences
                      }
                      type
                      violationTimeLimitSeconds
                    }
                  }
                }
              }
            }
            """

            variables = {"accountId": int(account_id), "conditionId": condition_id}

            response = self.client.execute_query(graphql_query, variables)

            # Extract condition from response
            condition = (
                response.data.get("actor", {})
                .get("account", {})
                .get("alerts", {})
                .get("nrqlCondition")
            )

            if not condition:
                raise NewRelicGraphQLError(
                    f"No alert condition found with ID: {condition_id}"
                )

            # Get policy name for the condition
            policy_id = condition.get("policyId")
            if policy_id:
                try:
                    policy_query = """
                    query($accountId: Int!, $policyId: ID!) {
                      actor {
                        account(id: $accountId) {
                          alerts {
                            policy(id: $policyId) {
                              id
                              name
                            }
                          }
                        }
                      }
                    }
                    """

                    policy_variables = {
                        "accountId": int(account_id),
                        "policyId": policy_id,
                    }

                    policy_response = self.client.execute_query(
                        policy_query, policy_variables
                    )
                    policy = (
                        policy_response.data.get("actor", {})
                        .get("account", {})
                        .get("alerts", {})
                        .get("policy", {})
                    )

                    if policy:
                        condition["policyName"] = policy.get("name")
                except Exception as e:
                    logger.warning(
                        f"Failed to fetch policy name for policy {policy_id}: {str(e)}"
                    )

            # Format and return the condition data
            return condition

        except Exception as e:
            raise NewRelicGraphQLError(
                f"Error fetching alert condition details: {str(e)}"
            )

    def get_container_related_entities(
        self, 
        entity_guid: str, 
        since_days: int = 7,
        account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get related entities (pod, deployment, node, cluster) from a container entity GUID.

        Args:
            entity_guid: The container entity GUID
            since_days: Number of days to look back in history
            account_id: Optional account ID to override client default

        Returns:
            Dictionary with podName, deploymentName, nodeName, clusterName, etc.

        Raises:
            NewRelicGraphQLError: If the query fails
        """
        try:
            # Build NRQL query to find related entities for a container
            query = f"""
            FROM K8sContainerSample 
            SELECT latest(podName), latest(deploymentName), latest(nodeName), latest(clusterName), latest(namespaceName), latest(containerName)
            WHERE entityGuid = '{entity_guid}' 
            SINCE {since_days} days ago
            LIMIT 1
            """
            
            # Execute the query
            results = self.execute_nrql(query, account_id)
            if not results:
                logger.warning(f"No related entities found for container {entity_guid}")
                return {}
                
            # Transform the results to a cleaner format
            related = {
                "podName": results[0].get("latest.podName"),
                "deploymentName": results[0].get("latest.deploymentName"),
                "nodeName": results[0].get("latest.nodeName"),
                "clusterName": results[0].get("latest.clusterName"),
                "namespaceName": results[0].get("latest.namespaceName"),
                "containerName": results[0].get("latest.containerName"),
            }
            
            return related
            
        except Exception as e:
            logger.error(f"Error getting container related entities: {str(e)}")
            raise NewRelicGraphQLError(f"Failed to get container related entities: {str(e)}")

    def get_pod_guid(
        self, 
        pod_name: str, 
        cluster_name: Optional[str] = None,
        namespace_name: Optional[str] = None,
        since_days: int = 7,
        account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get entity GUID for a pod by name.

        Args:
            pod_name: The name of the pod
            cluster_name: Optional cluster name for filtering
            namespace_name: Optional namespace name for filtering
            since_days: Number of days to look back in history
            account_id: Optional account ID to override client default

        Returns:
            Entity GUID if found, None otherwise
        """
        try:
            # Build filters
            filters = []
            if cluster_name:
                filters.append(f"clusterName = '{cluster_name}'")
            if namespace_name:
                filters.append(f"namespaceName = '{namespace_name}'")
                
            filters_str = " AND ".join(filters)
            if filters_str:
                filters_str = f" AND {filters_str}"
                
            # Build NRQL query to find pod GUID
            query = f"""
            FROM K8sPodSample 
            SELECT latest(entityGuid)
            WHERE podName = '{pod_name}'{filters_str}
            SINCE {since_days} days ago
            LIMIT 1
            """
            
            # Execute the query
            results = self.execute_nrql(query, account_id)
            if not results:
                logger.warning(f"No entity GUID found for pod {pod_name}")
                return None
                
            return results[0].get("latest.entityGuid")
            
        except Exception as e:
            logger.error(f"Error getting pod GUID: {str(e)}")
            return None

    def get_node_guid(
        self, 
        node_name: str, 
        cluster_name: Optional[str] = None,
        since_days: int = 7,
        account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get entity GUID for a node by name.

        Args:
            node_name: The name of the node
            cluster_name: Optional cluster name for filtering
            since_days: Number of days to look back in history
            account_id: Optional account ID to override client default

        Returns:
            Entity GUID if found, None otherwise
        """
        try:
            # Build filters
            cluster_filter = f" AND clusterName = '{cluster_name}'" if cluster_name else ""
                
            # Build NRQL query to find node GUID
            query = f"""
            FROM K8sNodeSample 
            SELECT latest(entityGuid)
            WHERE nodeName = '{node_name}'{cluster_filter}
            SINCE {since_days} days ago
            LIMIT 1
            """
            
            # Execute the query
            results = self.execute_nrql(query, account_id)
            if not results:
                logger.warning(f"No entity GUID found for node {node_name}")
                return None
                
            return results[0].get("latest.entityGuid")
            
        except Exception as e:
            logger.error(f"Error getting node GUID: {str(e)}")
            return None

    def get_deployment_guid(
        self, 
        deployment_name: str, 
        cluster_name: Optional[str] = None,
        namespace_name: Optional[str] = None,
        since_days: int = 7,
        account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get entity GUID for a deployment by name.

        Args:
            deployment_name: The name of the deployment
            cluster_name: Optional cluster name for filtering
            namespace_name: Optional namespace name for filtering
            since_days: Number of days to look back in history
            account_id: Optional account ID to override client default

        Returns:
            Entity GUID if found, None otherwise
        """
        try:
            # Build filters
            filters = []
            if cluster_name:
                filters.append(f"clusterName = '{cluster_name}'")
            if namespace_name:
                filters.append(f"namespaceName = '{namespace_name}'")
                
            filters_str = " AND ".join(filters)
            if filters_str:
                filters_str = f" AND {filters_str}"
                
            # Build NRQL query to find deployment GUID
            query = f"""
            FROM K8sDeploymentSample 
            SELECT latest(entityGuid)
            WHERE deploymentName = '{deployment_name}'{filters_str}
            SINCE {since_days} days ago
            LIMIT 1
            """
            
            # Execute the query
            results = self.execute_nrql(query, account_id)
            if not results:
                logger.warning(f"No entity GUID found for deployment {deployment_name}")
                return None
                
            return results[0].get("latest.entityGuid")
            
        except Exception as e:
            logger.error(f"Error getting deployment GUID: {str(e)}")
            return None

    def get_container_guid(
        self, 
        container_name: str, 
        cluster_name: Optional[str] = None,
        namespace_name: Optional[str] = None,
        since_days: int = 7,
        account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get entity GUID for a container by name.

        Args:
            container_name: The name of the container
            cluster_name: Optional cluster name for filtering
            namespace_name: Optional namespace name for filtering
            since_days: Number of days to look back in history
            account_id: Optional account ID to override client default

        Returns:
            Entity GUID if found, None otherwise
        """
        try:
            # Build filters
            filters = []
            if cluster_name:
                filters.append(f"clusterName = '{cluster_name}'")
            if namespace_name:
                filters.append(f"namespaceName = '{namespace_name}'")
                
            filters_str = " AND ".join(filters)
            if filters_str:
                filters_str = f" AND {filters_str}"
                
            # Build NRQL query to find container GUID
            query = f"""
            FROM K8sContainerSample 
            SELECT latest(entityGuid)
            WHERE containerName = '{container_name}'{filters_str}
            SINCE {since_days} days ago
            LIMIT 1
            """
            
            # Execute the query
            results = self.execute_nrql(query, account_id)
            if not results:
                logger.warning(f"No entity GUID found for container {container_name}")
                return None
                
            return results[0].get("latest.entityGuid")
            
        except Exception as e:
            logger.error(f"Error getting container GUID: {str(e)}")
            return None
