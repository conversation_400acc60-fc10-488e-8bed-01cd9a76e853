# New Relic Library

A Python library for interacting with New Relic's NerdGraph GraphQL API. This library provides a robust interface for querying metrics, logs, events, and entity data from New Relic.

## Features

- **GraphQL Client**: Core client for making GraphQL requests to New Relic's API
- **Query Client**: Specialized client for NRQL queries and data retrieval
- **Logs Client**: Client for retrieving logs from various partitions
- **Metrics Client**: Client for fetching and analyzing metrics data
- **Entity Analyzer**: Comprehensive analysis of entities for incident investigation
- **NRQL Manager**: Centralized management of NRQL queries via configuration
- **Entity Relationships**: Dynamic mapping of relationships between entities
- **Robust Error Handling**: Centralized retry logic for transient errors

## Components

- `client.py`: Core GraphQL client implementation
- `query.py`: Client for New Relic query operations
- `logs.py`: Client for log query operations
- `metrics.py`: Client for metrics operations
- `analyzer.py`: Entity analyzer for comprehensive information gathering
- `nrql_manager.py`: Manager for centralized NRQL query configuration
- `entities/`: Entity-specific query classes using the NRQL Manager
- `base.py`: Common base classes and types
- `retry_utils.py`: Centralized retry utilities
- `utils.py`: General utility functions

## New Features

### NRQL Manager

The NRQL Manager provides a centralized approach to managing New Relic NRQL queries:

- Store queries in a structured YAML configuration file
- Separate queries from code for easier maintenance
- Parameterize queries for flexible usage
- Organize queries by entity type and metric name

For detailed information, see [NRQL Manager Documentation](README_NRQL_MANAGER.md).

```python
from lib.new_relic.nrql_manager import NRQLManager
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.entities import PodEntity

# Initialize clients
client = NewRelicGraphQLClient(api_key="your-key", account_id="your-account")
query_client = NewRelicQueryClient(client)

# Use entity classes with NRQL Manager
pod_entity = PodEntity(query_client)
cpu_usage = pod_entity.get_cpu_usage("my-pod", "my-cluster", since_minutes=10)
```

### Entity Relationships

The Entity Relationships system enables dynamic mapping of relationships between entities:

- Configure relationship queries in YAML
- Extract standardized metadata from entities
- Handle entity type aliases for consistency
- Support for complex relationship chains

For detailed information, see [Entity Relationships Documentation](README_ENTITY_RELATIONSHIPS.md).

```python
from lib.new_relic.analyzer import EntityRelationshipMapper, EntityAnalyzer
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient

# Initialize clients
client = NewRelicGraphQLClient(api_key="your-key", account_id="your-account")
query_client = NewRelicQueryClient(client)

# Map relationships
mapper = EntityRelationshipMapper(query_client)
relationships = mapper.map_relationships("K8S_POD", "my-pod", "my-cluster")
```

## Retry Functionality

The library implements a robust retry mechanism for handling transient errors in New Relic API calls:

### Centralized Retry Logic

All GraphQL queries now benefit from centralized retry functionality implemented at the `execute_query` level. This ensures consistent retry behavior across all client operations.

```python
# The execute_query method is decorated with @nr_retry
@nr_retry
def execute_query(self, query, variables=None, debug=None, debug_options=None):
    # Method implementation
```

### Retry Configuration

The retry logic is configured with:

- Exponential backoff with jitter to prevent thundering herd problems
- Specific error detection for NRDB timeouts and server errors
- Configurable retry attempts (default: 4 attempts)
- Detailed logging of retry operations

### Retry Utilities

Available in `retry_utils.py`:

- `nr_retry`: Decorator for retrying operations with configurable parameters
- `is_retriable_error`: Function to determine if an exception should trigger a retry

## Usage

```python
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient

# Initialize the core client
client = NewRelicGraphQLClient(
    api_key="YOUR_API_KEY",
    account_id="YOUR_ACCOUNT_ID"
)

# Create a query client
query_client = NewRelicQueryClient(client)

# Execute NRQL queries
results = query_client.execute_nrql(
    "SELECT count(*) FROM Transaction",
    since="1 hour ago"
)

# The client automatically handles retries for transient errors
```

## Implementation Details

The retry functionality uses the Tenacity library, which provides comprehensive retry capabilities:

- The retry decorator is applied at the `execute_query` level, ensuring all GraphQL requests benefit from retry functionality
- Retry conditions are carefully defined to only retry on known transient error patterns
- Debug logging is available to trace retry operations

## Backward Compatibility

For backward compatibility, the library maintains the legacy `retry_on_error` decorator, which now wraps the more powerful `nr_retry` functionality.

## Future Improvements

- Add support for distributed tracing
- Implement batched query operations
- Add more specific entity type metrics
- Enhance log querying capabilities
- Expand entity relationship mapping to more entity types 