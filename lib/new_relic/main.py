"""
Example usage of the New Relic GraphQL client library.
"""

import os
import json
from datetime import datetime, timedelta

from lib.new_relic.base import Region, UTC, AlertStatus, format_datetime
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.metrics import NewRelicMetricsClient
from lib.new_relic.alerts import NewRelicAlertsClient
from lib.new_relic.mutation import NewRelicMutationClient


def format_metric_results(client, results):
    """Helper function to format and print metric results."""
    for result in results:
        print(f"\nMetric: {result.name}")
        print("=" * 50)
        print("Metadata:", json.dumps(result.metadata, indent=2))
        print("\nTimeseries Data:")
        for point in result.timeseries:
            print(f"  {point.timestamp.isoformat()}: {point.value}")


def main():
    # Get API key from environment variable
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        print("Error: NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID environment variables must be set")
        exit(1)
    
    # Initialize the core client
    core_client = NewRelicGraphQLClient(
        api_key=api_key,
        region=Region.US,
        account_id=account_id
    )
    
    # Initialize specialized clients
    query_client = NewRelicQueryClient(core_client)
    metrics_client = NewRelicMetricsClient(core_client)
    alerts_client = NewRelicAlertsClient(core_client)
    mutation_client = NewRelicMutationClient(core_client)
    
    try:
        # Example 1: Get pod details
        print("\n=== Example 1: Pod Details ===")
        pod_name = "example-pod-name"
        pod_details = query_client.get_pod_details(
            pod_name=pod_name,
            since=datetime.now(UTC) - timedelta(hours=1)
        )
        print(f"Found {len(pod_details)} pod details records")
        
        # Example 2: Get evicted pods metrics
        print("\n=== Example 2: Evicted Pods Metrics ===")
        since = datetime.now(UTC) - timedelta(hours=24)
        evicted_pods = metrics_client.get_evicted_pods_metrics(
            since=since,
            cluster_pattern='%prd%',
            exclude_patterns=['%assetprocessor%', '%fru-prd%'],
            timeseries_period='30 minutes'
        )
        print(f"Found metrics for evicted pods: {len(evicted_pods.get('results', []))} results")
        
        # Example 3: Get alerts
        print("\n=== Example 3: Recent Alerts ===")
        alerts = alerts_client.get_alerts(
            status=AlertStatus.FIRING,
            since=datetime.now(UTC) - timedelta(days=1),
            limit=5
        )
        print(f"Found {len(alerts)} recent alerts:")
        for alert in alerts:
            print(f"  - {alert.name} ({alert.severity.value}) - {alert.status.value}")
        
        # Example 4: Get CPU metrics for a container
        print("\n=== Example 4: CPU Metrics ===")
        cpu_query = """
        SELECT average(cpuUsedCores)
        FROM K8sContainerSample
        WHERE clusterName LIKE '%prd%'
        FACET containerName, clusterName
        TIMESERIES 30 minutes
        SINCE 2 hours ago
        LIMIT 2
        """
        cpu_metrics = metrics_client.get_generic_timeseries_metrics(
            nrql_query=cpu_query,
            metric_name="container_cpu_usage"
        )
        format_metric_results(metrics_client, cpu_metrics)
        
    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        # Clean up resources
        core_client.close()


if __name__ == "__main__":
    main() 