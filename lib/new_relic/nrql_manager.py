"""
NRQL Query Manager for centralized management of New Relic NRQL queries.
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logger
logger = logging.getLogger(__name__)

class NRQLManager:
    """
    Manager for centralized NRQL query management.
    Loads queries from a YAML configuration file and provides methods to retrieve and format them.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the NRQL Manager.
        
        Args:
            config_path: Path to the NRQL queries configuration YAML file.
                         If None, uses the default config/nrql_queries.yaml.
        """
        if not config_path:
            # Default location relative to this module
            module_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(module_dir, "..", "..", "config", "nrql_queries.yaml")
            
        self.queries = self._load_queries(config_path)
        
    def _load_queries(self, path: str) -> Dict[str, Any]:
        """
        Load NRQL queries from the configuration file.
        
        Args:
            path: Path to the configuration file
            
        Returns:
            Dictionary containing the queries configuration
            
        Raises:
            FileNotFoundError: If the configuration file does not exist
            yaml.YAMLError: If the YAML file is invalid
        """
        try:
            if not os.path.exists(path):
                logger.error(f"NRQL queries configuration file not found at {path}")
                return {}
                
            with open(path, 'r') as f:
                config = yaml.safe_load(f)
                logger.debug(f"Loaded {len(config)} entity types with NRQL queries")
                return config
        except Exception as e:
            logger.error(f"Error loading NRQL queries configuration: {str(e)}")
            raise
    
    def get_query(self, entity_type: str, metric_name: str, **params) -> str:
        """
        Get and format a specific NRQL query.
        
        Args:
            entity_type: The type of entity (e.g., 'kubernetes_pod', 'host')
            metric_name: The name of the metric or query (e.g., 'cpu_usage')
            **params: Parameters to format into the query template
            
        Returns:
            Formatted NRQL query string
            
        Raises:
            ValueError: If the requested query is not found
            KeyError: If a required parameter is missing
        """
        try:
            if entity_type not in self.queries:
                logger.error(f"Entity type '{entity_type}' not found in NRQL queries configuration")
                raise ValueError(f"NRQL query for entity type '{entity_type}' not found")
                
            if metric_name not in self.queries[entity_type]:
                logger.error(f"Metric '{metric_name}' not found for entity type '{entity_type}'")
                raise ValueError(f"NRQL query for {entity_type}.{metric_name} not found")
                
            template = self.queries[entity_type][metric_name]
            query = template.format(**params)
            logger.debug(f"Generated NRQL query: {query}")
            return query
        except KeyError as e:
            logger.error(f"Missing parameter for NRQL query {entity_type}.{metric_name}: {e}")
            raise ValueError(f"Missing parameter for NRQL query {entity_type}.{metric_name}: {e}")
    
    def get_available_queries(self, entity_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Get available queries for an entity type or all entity types.
        
        Args:
            entity_type: Optional entity type to filter by
            
        Returns:
            Dictionary containing available queries
        """
        if entity_type:
            return self.queries.get(entity_type, {})
        return self.queries
    
    def get_entity_types(self) -> list:
        """
        Get all available entity types.
        
        Returns:
            List of entity types
        """
        return list(self.queries.keys()) 