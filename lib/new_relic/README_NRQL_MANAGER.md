# NRQL Manager

The NRQL Manager provides a centralized approach to managing New Relic NRQL queries in a structured, maintainable way.

## Overview

NRQL Manager allows you to:

- Maintain all your NRQL queries in a single YAML file
- Reuse query templates across your codebase 
- Modify queries without changing Python code
- Organize queries by entity type and metric name
- Parameterize queries for easy customization

## Usage

### Basic Usage

```python
from lib.new_relic.nrql_manager import NRQLManager

# Initialize the manager
nrql_manager = NRQLManager()

# Get a formatted query with parameters
query = nrql_manager.get_query(
    "kubernetes_pod",           # Entity type
    "cpu_usage",                # Metric name
    pod_name="my-pod",          # Query parameter
    cluster_name="my-cluster",  # Query parameter
    since_minutes=10            # Query parameter
)

print(query)
# Output:
# SELECT sum(cpuUsedCores) FROM K8sContainerSample 
# WHERE podName = 'my-pod' AND clusterName = 'my-cluster' 
# SINCE 10 minutes ago TIMESERIES
```

### Using Entity Classes

We provide pre-built entity classes that use the NRQL Manager:

```python
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.entities import PodEntity, NodeEntity, HostEntity

# Set up client
client = NewRelicGraphQLClient(api_key="your-key", account_id="your-account")
query_client = NewRelicQueryClient(client)

# Create entity objects
pod_entity = PodEntity(query_client)
node_entity = NodeEntity(query_client)
host_entity = HostEntity(query_client)

# Get Pod CPU Usage
cpu_usage = pod_entity.get_cpu_usage("my-pod", "my-cluster", since_minutes=10)
print(cpu_usage)

# Get Node Disk Usage
disk_usage = node_entity.get_disk_usage("my-node", since_minutes=5)
print(disk_usage)

# Get Host CPU, Memory, and Disk Usage
host_metrics = host_entity.get_cpu_memory_disk("my-host", since_minutes=30)
print(host_metrics)
```

## Query Configuration

NRQL queries are defined in `config/nrql_queries.yaml`. The structure is:

```yaml
entity_type:
  metric_name: >
    NRQL query with {parameter} placeholders
  another_metric: >
    Another NRQL query
    
another_entity_type:
  metric_name: >
    NRQL query for a different entity type
```

### Adding a New Query

To add a new query:

1. Open `config/nrql_queries.yaml`
2. Add your query under the appropriate entity type or create a new entity type
3. Use the `>` YAML operator for multi-line strings
4. Use `{parameter_name}` placeholders for dynamic parts of the query

Example:

```yaml
kubernetes_pod:
  custom_metric: >
    SELECT average(myMetric) FROM K8sContainerSample
    WHERE podName = '{pod_name}' AND clusterName = '{cluster_name}'
    SINCE {since_minutes} minutes ago TIMESERIES
```

To use this new query:

```python
query = nrql_manager.get_query(
    "kubernetes_pod",
    "custom_metric",
    pod_name="my-pod",
    cluster_name="my-cluster",
    since_minutes=10
)
```

## Adding a New Entity Type

To create a new entity type class:

1. Create a new file in `lib/new_relic/entities/` (e.g., `my_entity.py`)
2. Define your entity class using the NRQLManager
3. Add your new class to `__init__.py`

Example implementation for a new entity:

```python
from typing import Dict, List, Any, Optional
from ..nrql_manager import NRQLManager
from ..query import NewRelicQueryClient

class MyEntity:
    def __init__(self, query_client, nrql_manager=None):
        self.query_client = query_client
        self.nrql_manager = nrql_manager or NRQLManager()
    
    def get_custom_metric(self, entity_id, since_minutes=30):
        nrql = self.nrql_manager.get_query(
            "my_entity_type",
            "custom_metric",
            entity_id=entity_id,
            since_minutes=since_minutes
        )
        return self.query_client.execute_nrql(nrql)
```

## Error Handling

The NRQL Manager will raise the following exceptions:

- `ValueError`: When a query for the given entity type or metric name doesn't exist
- `KeyError`: When a required parameter for a query is missing
- `FileNotFoundError`: When the NRQL queries configuration file is not found
- `yaml.YAMLError`: When the YAML file has invalid syntax

Always use try/except blocks to handle these exceptions in your code. 