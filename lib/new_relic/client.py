"""
Core New Relic GraphQL client implementation.
"""

import requests
import json
import pprint
from typing import Any, Dict, List, Optional, Union, Callable, cast

from .base import Region, GraphQLResponse, NewRelicGraphQLError, T, format_datetime
from .retry_utils import nr_retry


class NewRelicGraphQLClient:
    """
    Client for interacting with New Relic's NerdGraph GraphQL API.
    Provides core functionality for executing GraphQL queries.
    """
    
    def __init__(
        self, 
        api_key: str, 
        region: Region = Region.US, 
        account_id: Optional[str] = None, 
        timeout: int = 30,
        debug: bool = False,
        debug_request: bool = False,
        debug_response: bool = False,
        debug_errors: bool = False,
        debug_callback: Optional[Callable[[str, Any], bool]] = None
    ):
        """
        Initialize the New Relic GraphQL client.
        
        Args:
            api_key: New Relic API key
            region: API region (US or EU)
            account_id: Default account ID to use for queries
            timeout: Request timeout in seconds
            debug: Enable all debug logging
            debug_request: Debug only request information
            debug_response: Debug only response information
            debug_errors: Debug only error information
            debug_callback: Custom callback function for debug filtering
        """
        self.api_key = api_key
        self.region = region
        self.account_id = account_id
        self.timeout = timeout
        self.debug = debug
        self.debug_request = debug_request or debug
        self.debug_response = debug_response or debug
        self.debug_errors = debug_errors or debug
        self.debug_callback = debug_callback
        self.headers = {
            'Content-Type': 'application/json',
            'API-Key': self.api_key
        }
    
    def _should_debug(self, section: str, data: Any = None) -> bool:
        """
        Determine if debugging should be enabled for a specific section.
        
        Args:
            section: The section being debugged ('request', 'response', or 'error')
            data: Optional data for the debug callback
            
        Returns:
            True if debugging should be enabled for this section
        """
        if self.debug_callback:
            return self.debug_callback(section, data)
        
        if section == 'request':
            return self.debug_request
        elif section == 'response':
            return self.debug_response
        elif section == 'error':
            return self.debug_errors
        
        return self.debug
        
    @nr_retry
    def execute_query(
        self, 
        query: str, 
        variables: Optional[Dict[str, Any]] = None, 
        debug: Optional[bool] = None,
        debug_options: Optional[Dict[str, bool]] = None
    ) -> GraphQLResponse:
        """
        Execute a GraphQL query against the New Relic NerdGraph API.
        
        This method is decorated with @nr_retry to automatically retry on transient errors
        (such as timeouts or server errors) using exponential backoff with jitter.
        
        Args:
            query: The GraphQL query string to execute
            variables: Optional dictionary of variables for the query
            debug: If True, enable debug output for this query
            debug_options: Optional dictionary of specific debug options for this query
            
        Returns:
            A GraphQLResponse object containing the query results
            
        Raises:
            NewRelicGraphQLError: If the query fails with an error
        """
        payload = {
            'query': query,
            'variables': variables or {}
        }
        
        # Apply debug options for this specific query if provided
        local_debug = debug if debug is not None else self.debug
        local_debug_request = debug_options.get('request', self.debug_request) if debug_options else self.debug_request
        local_debug_response = debug_options.get('response', self.debug_response) if debug_options else self.debug_response
        local_debug_errors = debug_options.get('errors', self.debug_errors) if debug_options else self.debug_errors
        
        # If main debug is enabled, enable all sections
        if local_debug:
            local_debug_request = local_debug_response = local_debug_errors = True
        
        # Log request details if debug is enabled
        if local_debug_request and self._should_debug('request', payload):
            self._debug_request(query, variables, self.region.value)
        
        try:
            response = requests.post(
                self.region.value,
                json=payload,
                headers=self.headers,
                timeout=self.timeout
            )
            
            # Handle HTTP errors
            try:
                response.raise_for_status()
            except requests.exceptions.HTTPError as e:
                # Try to extract more detailed error information if available
                error_detail = ""
                error_data = None
                
                try:
                    error_json = response.json()
                    if error_json and "errors" in error_json:
                        error_detail = f": {error_json['errors']}"
                        error_data = error_json
                except Exception:
                    pass
                
                if local_debug_errors and self._should_debug('error', error_data):
                    self._debug_error_response(response, e)
                
                raise NewRelicGraphQLError(f"HTTP error: {e}{error_detail}")
            
            # Parse response JSON
            try:
                data = response.json()
            except ValueError as e:
                if local_debug_errors and self._should_debug('error', response.text):
                    self._debug_invalid_json(response, e)
                raise NewRelicGraphQLError(f"Invalid JSON response: {str(e)}")
            
            # Log response if debug is enabled
            if local_debug_response and self._should_debug('response', data):
                self._debug_response(response, data)
            
            graphql_response = GraphQLResponse(
                data=data.get('data'),
                errors=data.get('errors')
            )
            
            if graphql_response.has_errors:
                if local_debug_errors and self._should_debug('error', graphql_response.errors):
                    self._debug_graphql_errors(graphql_response.errors)
                    
                raise NewRelicGraphQLError(
                    "GraphQL query failed",
                    graphql_response.errors
                )
                
            return graphql_response
            
        except requests.exceptions.RequestException as e:
            if local_debug_errors and self._should_debug('error', str(e)):
                self._debug_request_exception(e)
            raise NewRelicGraphQLError(f"Failed to execute query: {str(e)}")

    def _debug_request(self, query: str, variables: Optional[Dict[str, Any]], url: str) -> None:
        """Print formatted debug information for a request"""
        print("\n" + "="*80)
        print("NEW RELIC GRAPHQL REQUEST")
        print("="*80)
        print(f"URL: {url}")
        
        # Print headers (but mask the API key)
        headers_display = self.headers.copy()
        if 'API-Key' in headers_display:
            api_key = headers_display['API-Key']
            if len(api_key) > 8:
                headers_display['API-Key'] = f"{api_key[:4]}...{api_key[-4:]}"
            else:
                headers_display['API-Key'] = "****"
        print("\nHeaders:")
        pprint.pprint(headers_display)
        
        # Format and print the GraphQL query
        print("\nQuery:")
        formatted_query = query.strip()
        print(formatted_query)
        
        # Print variables if any
        if variables:
            print("\nVariables:")
            pprint.pprint(variables)
        
        print("-"*80)
    
    def _debug_response(self, response: requests.Response, data: Dict[str, Any]) -> None:
        """Print formatted debug information for a response"""
        print("\nRESPONSE:")
        print(f"Status Code: {response.status_code}")
        print("\nContent:")
        pprint.pprint(data)
        print("="*80 + "\n")
    
    def _debug_error_response(self, response: requests.Response, exception: Exception) -> None:
        """Print formatted debug information for an error response"""
        print("\nERROR RESPONSE:")
        print(f"Status Code: {response.status_code}")
        print(f"Exception: {str(exception)}")
        try:
            error_data = response.json()
            print("\nError Data:")
            pprint.pprint(error_data)
        except:
            print("\nResponse Text:")
            print(response.text)
        print("="*80 + "\n")
    
    def _debug_invalid_json(self, response: requests.Response, exception: Exception) -> None:
        """Print formatted debug information for an invalid JSON response"""
        print("\nERROR: Invalid JSON response")
        print(f"Exception: {str(exception)}")
        print("\nResponse Text:")
        print(response.text)
        print("="*80 + "\n")
    
    def _debug_graphql_errors(self, errors: List[Dict[str, Any]]) -> None:
        """Print formatted debug information for GraphQL errors"""
        print("\nGRAPHQL ERRORS:")
        pprint.pprint(errors)
        print("="*80 + "\n")
    
    def _debug_request_exception(self, exception: Exception) -> None:
        """Print formatted debug information for a request exception"""
        print(f"\nREQUEST EXCEPTION: {str(exception)}")
        print("="*80 + "\n")

    def _verify_account_id(self, account_id: Optional[str] = None) -> str:
        """
        Verify that an account ID is available, either from the method parameter or client instance.
        
        Args:
            account_id: Optional account ID to use
            
        Returns:
            The verified account ID
            
        Raises:
            ValueError: If no account ID is available
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("account_id must be provided either at client initialization or method call")
        return account_id

