"""
New Relic logging utility module for standardized logging across the application.

This module configures Loguru with better formatting, colors, and structure to make
debugging easier and logs more readable.
"""

import os
import sys
import json
import inspect
import asyncio
import warnings
import re
import traceback
from datetime import datetime
from functools import wraps
from typing import Any, Callable, Dict, Optional, List, Union

import dotenv
from loguru import logger

# Filter out Pydantic JSON schema warnings
warnings.filterwarnings("ignore", category=UserWarning, message=".*is not JSON serializable.*")
warnings.filterwarnings("ignore", message=".*PydanticJsonSchemaWarning.*")

# Load environment variables
dotenv.load_dotenv()

# ANSI color codes
class Colors:
    RESET = "\033[0m"
    BOLD = "\033[1m"
    UNDERLINE = "\033[4m"
    ITALIC = "\033[3m"  # Adding ITALIC style
    
    # Foreground colors
    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"
    
    # Background colors
    BG_BLACK = "\033[40m"
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"
    BG_MAGENTA = "\033[45m"
    BG_CYAN = "\033[46m"
    BG_WHITE = "\033[47m"
    
    # Bright foreground colors
    BRIGHT_BLACK = "\033[90m"
    BRIGHT_RED = "\033[91m"
    BRIGHT_GREEN = "\033[92m"
    BRIGHT_YELLOW = "\033[93m"
    BRIGHT_BLUE = "\033[94m"
    BRIGHT_MAGENTA = "\033[95m"
    BRIGHT_CYAN = "\033[96m"
    BRIGHT_WHITE = "\033[97m"

# Function to escape angle brackets and other special characters that loguru might misinterpret
def escape_loguru_special_chars(text: str) -> str:
    """
    Escape special characters that Loguru might misinterpret as formatting directives.
    This is particularly important for strings containing angle brackets like "<none>"
    that appear in Kubernetes resource descriptions.
    
    Args:
        text: The string to escape
        
    Returns:
        Escaped string safe for Loguru formatting
    """
    if not isinstance(text, str):
        return text
    
    # Escape angle brackets by replacing them with their literal representation
    # This prevents loguru from interpreting them as color tags
    text = text.replace("<", r"\<").replace(">", r"\>")
    
    # Also escape curly braces for f-string safety
    text = text.replace("{", "{{").replace("}", "}}")
    
    # Escape quotes that might appear in API error messages
    text = text.replace("'code'", r"\'code\'")
    text = text.replace("'message'", r"\'message\'")
    
    return text

# Remove default handler
logger.remove()

# Add custom handler with better formatting
LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")

# Define log colors using ANSI escape codes
LEVEL_COLORS = {
    "TRACE": Colors.CYAN,
    "DEBUG": Colors.BLUE,
    "INFO": Colors.GREEN,
    "SUCCESS": Colors.GREEN,
    "WARNING": Colors.YELLOW,
    "ERROR": Colors.RED,
    "CRITICAL": f"{Colors.RED}{Colors.BOLD}",
}

# Define format for console logs (colorized, structured)
def colorize_level(level):
    level_color = LEVEL_COLORS.get(level, Colors.WHITE)
    # Pad level name to 8 characters for better alignment
    padded_level = f"{level:<8}"
    return f"{level_color}{padded_level}{Colors.RESET}"

# Custom message processor to escape special characters before Loguru processes them
def process_message(record):
    # Special handling for exceptions, which might contain API error responses
    if record.get("exception") is not None:
        # Make sure we don't lose the original message
        original_message = record["message"]
        
        # Try to safely process the message with extra escaping for API errors
        try:
            # Look for common API error patterns and escape them
            if "status_code" in original_message and "body" in original_message:
                # This looks like an API error, so be extra careful
                record["message"] = escape_api_error_message(original_message)
            else:
                # Regular escaping for normal messages
                record["message"] = escape_loguru_special_chars(original_message)
        except Exception:
            # If anything goes wrong during escaping, use a sanitized version
            record["message"] = f"Error message (sanitized): {sanitize_message(original_message)}"
    else:
        # Normal message processing for non-exception logs
        record["message"] = escape_loguru_special_chars(record["message"])
        
    return record["message"]

# Function to specially handle API error messages
def escape_api_error_message(message: str) -> str:
    """
    Special escaping for API error messages that might contain JSON or quoted fields.
    
    Args:
        message: API error message string
        
    Returns:
        Safely escaped message
    """
    # Replace any instances of 'code' or 'message' in quotes that might be interpreted as format keys
    message = re.sub(r"'(\w+)':", r"'\\\1':", message)
    
    # Double-escape curly braces in the message
    message = message.replace("{", "{{").replace("}", "}}")
    
    # Escape angle brackets
    message = message.replace("<", r"\<").replace(">", r"\>")
    
    return message

# Function to completely sanitize a message when all else fails
def sanitize_message(message: str) -> str:
    """
    Completely sanitize a message by removing all special characters.
    Used as a last resort when other escaping methods fail.
    
    Args:
        message: The message to sanitize
        
    Returns:
        Sanitized string
    """
    if not isinstance(message, str):
        try:
            message = str(message)
        except Exception:
            return "[Unprintable message]"
    
    # Remove characters that might cause formatting issues
    sanitized = ""
    for char in message:
        if char in "<>{}'\"":
            sanitized += "_"
        else:
            sanitized += char
            
    return sanitized

# Add custom error handler to catch and safely format exception logs
def safe_exception_formatter(record):
    """
    Safely format exception information to prevent Loguru from crashing.
    
    Args:
        record: Loguru record containing exception information
        
    Returns:
        Safely formatted exception information
    """
    if record["exception"] is None:
        return ""
        
    try:
        # Get the exception information from the record
        exception_type, exception_value, tb = record["exception"]
        
        # Format a simple traceback string
        tb_str = "".join(traceback.format_exception(exception_type, exception_value, tb))
        
        # Escape special characters in the traceback
        tb_str = escape_loguru_special_chars(tb_str)
        
        return f"\n{Colors.RED}Exception:\n{tb_str}{Colors.RESET}"
    except Exception:
        # If anything goes wrong, return a simple error message
        return f"\n{Colors.RED}[Error formatting exception]{Colors.RESET}"

# Add console handler with colors and custom format
logger.configure(
    handlers=[
        {
            "sink": sys.stderr,
            "format": lambda record: (
                f"{Colors.GREEN}{record['time'].strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}{Colors.RESET} | "
                f"{colorize_level(record['level'].name)} | "
                f"{Colors.CYAN}{record['name']}:{record['line']}{Colors.RESET} - "
                f"{process_message(record)}"
                f"{safe_exception_formatter(record) if record['exception'] else ''}\n"
            ),
            "level": LOG_LEVEL,
            "colorize": False,  # We're handling colorization manually
            "backtrace": True,
            "diagnose": True,
            "catch": True,  # Add catch=True to prevent Loguru from crashing on formatting errors
        }
    ]
)

# Optional: Configure file handler if needed
LOG_FILE = os.environ.get("LOG_FILE")
if LOG_FILE:
    logger.add(
        LOG_FILE,
        rotation="10 MB",
        retention="1 week",
        level=LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{line} - {message}\n",  # Add newline
        backtrace=True,
        diagnose=True,
        catch=True,  # Make sure we have catch=True here too
    )


def get_logger(name: str):
    """Get a logger instance with the given name for proper module context."""
    return logger.bind(name=name)


def log_execution(name: Optional[str] = None):
    """
    Decorator to log function entry and exit with execution time.
    
    Args:
        name: Optional override for the log prefix 
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper_async(*args: Any, **kwargs: Any) -> Any:
            function_name = name or func.__name__
            log = logger.bind(name=func.__module__)
            
            start_time = datetime.now()
            log.info(f"Starting {function_name}")
            
            try:
                result = await func(*args, **kwargs)
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                log.success(f"Completed {function_name} in {duration:.2f}s")
                return result
            except Exception as e:
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                log.error(f"Failed {function_name} after {duration:.2f}s: {str(e)}")
                raise
                
        @wraps(func)
        def wrapper_sync(*args: Any, **kwargs: Any) -> Any:
            function_name = name or func.__name__
            log = logger.bind(name=func.__module__)
            
            start_time = datetime.now()
            log.info(f"Starting {function_name}")
            
            try:
                result = func(*args, **kwargs)
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                log.success(f"Completed {function_name} in {duration:.2f}s")
                return result
            except Exception as e:
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                log.error(f"Failed {function_name} after {duration:.2f}s: {str(e)}")
                raise
        
        # Return appropriate wrapper based on whether the function is async
        if asyncio_is_coroutine_function(func):
            return wrapper_async
        return wrapper_sync
    
    return decorator


def asyncio_is_coroutine_function(func: Callable) -> bool:
    """Check if a function is a coroutine function."""
    return inspect.iscoroutinefunction(func)


# Common context enrichment function
class LogContextManager:
    """
    Context manager for temporarily enriching log context.
    """
    def __init__(self, **context):
        self.context = context
        self.bound_logger = logger.bind(**context)
        
    def __enter__(self):
        return self.bound_logger
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Nothing to clean up since bound_logger is temporary
        pass


def enrich_log_context(**context) -> LogContextManager:
    """
    Add context to logger for all subsequent log calls within the context block.
    
    Example:
        with enrich_log_context(incident_id="12345", entity="server-123") as log:
            log.info("Processing data")
    """
    return LogContextManager(**context)


def format_node_start(node_name: str, incident_id: str) -> str:
    """
    Format a node start message with prominent styling using ANSI colors.

    Args:
        node_name: Name of the workflow node
        incident_id: ID of the incident being processed

    Returns:
        Formatted message string
    """
    border = "═" * 80
    return (
        f"\n{Colors.YELLOW}{border}{Colors.RESET}\n"
        f"{Colors.YELLOW}▶{Colors.RESET} {Colors.BLUE}{Colors.BOLD}NODE START:{Colors.RESET} "
        f"{Colors.MAGENTA}{node_name}{Colors.RESET} "
        f"{Colors.CYAN}(incident: {incident_id}){Colors.RESET}\n"
        f"{Colors.YELLOW}{border}{Colors.RESET}\n"  # Extra newline at the end
    )


def format_node_end(node_name: str, incident_id: str, duration: float) -> str:
    """
    Format a node completion message with prominent styling using ANSI colors.

    Args:
        node_name: Name of the workflow node
        incident_id: ID of the incident being processed
        duration: Execution duration in seconds

    Returns:
        Formatted message string
    """
    border = "═" * 80
    return (
        f"\n{Colors.YELLOW}{border}{Colors.RESET}\n"
        f"{Colors.YELLOW}✓{Colors.RESET} {Colors.GREEN}{Colors.BOLD}NODE COMPLETE:{Colors.RESET} "
        f"{Colors.MAGENTA}{node_name}{Colors.RESET} "
        f"{Colors.CYAN}(incident: {incident_id}){Colors.RESET} "
        f"{Colors.GREEN}in {duration:.2f}s{Colors.RESET}\n"
        f"{Colors.YELLOW}{border}{Colors.RESET}\n"  # Extra newline at the end
    )


def format_node_error(node_name: str, incident_id: str, error: str, duration: float) -> str:
    """
    Format a node error message with prominent styling using ANSI colors.

    Args:
        node_name: Name of the workflow node
        incident_id: ID of the incident being processed
        error: Error message
        duration: Execution duration in seconds

    Returns:
        Formatted message string
    """
    border = "═" * 80
    return (
        f"\n{Colors.YELLOW}{border}{Colors.RESET}\n"
        f"{Colors.YELLOW}✗{Colors.RESET} {Colors.RED}{Colors.BOLD}NODE FAILED:{Colors.RESET} "
        f"{Colors.MAGENTA}{node_name}{Colors.RESET} "
        f"{Colors.CYAN}(incident: {incident_id}){Colors.RESET} "
        f"{Colors.RED}after {duration:.2f}s{Colors.RESET}\n"
        f"{Colors.RED}Error: {error}{Colors.RESET}\n"
        f"{Colors.YELLOW}{border}{Colors.RESET}\n"  # Extra newline at the end
    )


def format_nrql_query(query: str) -> str:
    """
    Format an NRQL query with syntax highlighting using ANSI colors, without boxes to allow easy copying.

    Args:
        query: The NRQL query string

    Returns:
        Formatted message string
    """
    # Clean up and format the query
    query = query.strip()
    lines = query.split('\n')
    # Remove excess whitespace while keeping indentation structure
    cleaned_lines = [line.rstrip() for line in lines]
    cleaned_query = '\n'.join(cleaned_lines)
    
    return (
        f"\n{Colors.CYAN}{Colors.BOLD}NRQL QUERY:{Colors.RESET}\n\n"
        f"{Colors.YELLOW}{cleaned_query}{Colors.RESET}\n"
    )


def format_nrql_results(results: List[Dict[str, Any]], max_items: int = 3) -> str:
    """
    Format NRQL query results with syntax highlighting using ANSI colors, without boxes to allow easy copying.

    Args:
        results: List of result records
        max_items: Maximum number of items to display (to avoid excessive logging)

    Returns:
        Formatted message string
    """
    if not results:
        return f"\n{Colors.CYAN}{Colors.BOLD}NRQL RESULTS:{Colors.RESET} {Colors.ITALIC}No results returned{Colors.RESET}\n"
    
    # Truncate results if there are too many
    display_results = results[:max_items]
    truncated = len(results) > max_items
    
    try:
        # First, create a safer representation of results for logging
        safe_results = []
        for item in display_results:
            safe_item = {}
            for key, value in item.items():
                # Replace dots with underscores in keys to avoid Loguru formatting issues
                safe_key = key.replace(".", "_")
                
                # If value is a string, escape angle brackets to prevent Loguru misinterpretation
                if isinstance(value, str):
                    value = escape_loguru_special_chars(value)
                    
                safe_item[safe_key] = value
            safe_results.append(safe_item)
        
        # Format results as JSON for display
        formatted_json = json.dumps(safe_results, indent=2)
    except Exception:
        # Fall back to just showing a summary if JSON formatting fails
        return f"\n{Colors.CYAN}{Colors.BOLD}NRQL RESULTS:{Colors.RESET} {Colors.GREEN}{len(results)} records returned (unable to display content){Colors.RESET}\n"
    
    # IMPORTANT: Escape all curly braces to prevent Loguru formatting conflicts
    # Double each curly brace to escape it in f-strings
    formatted_json = formatted_json.replace("{", "{{").replace("}", "}}")
    
    result = (
        f"\n{Colors.CYAN}{Colors.BOLD}NRQL RESULTS:{Colors.RESET} {Colors.GREEN}{len(results)} records returned{Colors.RESET}\n\n"
        f"{Colors.GREEN}{formatted_json}{Colors.RESET}\n"
    )
    
    # Add a note if results were truncated
    if truncated:
        result += f"\n{Colors.YELLOW}Note: Only showing {max_items} of {len(results)} records{Colors.RESET}"
    
    # Add a note about key transformations if dots were replaced
    if any("." in key for result in display_results for key in result.keys()):
        result += f"\n{Colors.YELLOW}Note: Dots in field names have been replaced with underscores for display purposes{Colors.RESET}"
    
    return result 


def safe_format_for_logging(message: str) -> str:
    """
    Safely format a string for logging with Loguru by escaping special characters.
    
    This utility function helps prevent formatting errors when logging messages
    containing curly braces, dots in field names, etc.
    
    Args:
        message: The string message to format
        
    Returns:
        Safely formatted string for Loguru
    """
    # Use the complete escape function that handles both angle brackets and curly braces
    return escape_loguru_special_chars(message) 