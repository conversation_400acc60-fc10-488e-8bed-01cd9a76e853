import psycopg2
from psycopg2.extras import RealDict<PERSON>ursor
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import logging
from contextlib import contextmanager

from lib.database_config import get_db_config

logger = logging.getLogger(__name__)

@contextmanager
def get_db_connection():
    """Context manager for database connections."""
    conn = None
    try:
        conn = psycopg2.connect(**get_db_config())
        yield conn
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        raise
    finally:
        if conn is not None:
            conn.close()

def init_db():
    """Initialize database tables."""
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            # Create investigation_steps table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS investigation_steps (
                    id SERIAL PRIMARY KEY,
                    issue_id VARCHAR(100) NOT NULL,
                    step_type VARCHAR(50) NOT NULL,
                    step_data JSONB NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    metadata JSONB
                )
            """)
            
            # Create investigation_results table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS investigation_results (
                    id SERIAL PRIMARY KEY,
                    issue_id VARCHAR(100) NOT NULL,
                    workflow_type VARCHAR(50) NOT NULL,
                    result_data JSONB NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    severity VARCHAR(20),
                    status VARCHAR(20),
                    metadata JSONB
                )
            """)
            
            conn.commit()

def save_investigation_step(
    issue_id: str,
    step_type: str,
    step_data: Dict[str, Any],
    metadata: Optional[Dict[str, Any]] = None
) -> int:
    """Save an investigation step to the database."""
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute(
                """
                INSERT INTO investigation_steps 
                (issue_id, step_type, step_data, metadata)
                VALUES (%s, %s, %s, %s)
                RETURNING id
                """,
                (issue_id, step_type, json.dumps(step_data), json.dumps(metadata) if metadata else None)
            )
            step_id = cur.fetchone()[0]
            conn.commit()
            return step_id

def save_investigation_result(
    issue_id: str,
    workflow_type: str,
    result_data: Dict[str, Any],
    severity: str = None,
    status: str = None,
    metadata: Optional[Dict[str, Any]] = None
) -> int:
    """Save investigation results to the database."""
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute(
                """
                INSERT INTO investigation_results 
                (issue_id, workflow_type, result_data, severity, status, metadata)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id
                """,
                (
                    issue_id,
                    workflow_type,
                    json.dumps(result_data),
                    severity,
                    status,
                    json.dumps(metadata) if metadata else None
                )
            )
            result_id = cur.fetchone()[0]
            conn.commit()
            return result_id

def get_investigation_steps(issue_id: str) -> List[Dict[str, Any]]:
    """Retrieve all investigation steps for an issue."""
    with get_db_connection() as conn:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute(
                """
                SELECT * FROM investigation_steps 
                WHERE issue_id = %s 
                ORDER BY created_at ASC
                """,
                (issue_id,)
            )
            return cur.fetchall()

def get_investigation_results(issue_id: str) -> List[Dict[str, Any]]:
    """Retrieve all investigation results for an issue."""
    with get_db_connection() as conn:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute(
                """
                SELECT * FROM investigation_results 
                WHERE issue_id = %s 
                ORDER BY created_at DESC
                """,
                (issue_id,)
            )
            return cur.fetchall() 