"""
New Relic NerdGraph GraphQL client library.
Supports both US and EU endpoints with proper authentication and error handling.
"""

import json
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Tuple, cast, TypeVar, Generic
import requests
from requests.exceptions import RequestException
from datetime import datetime, timedelta, timezone

# Use timezone consistently instead of UTC constant
UTC = timezone.utc

T = TypeVar('T')


class Region(Enum):
    """New Relic regions for NerdGraph API"""
    US = "https://api.newrelic.com/graphql"
    EU = "https://api.eu.newrelic.com/graphql"


class AlertStatus(Enum):
    """Alert status values"""
    FIRING = "open"
    RESOLVED = "closed"
    ACKNOWLEDGED = "acknowledged"


class AlertSeverity(Enum):
    """Alert severity values"""
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"


@dataclass
class GraphQLResponse(Generic[T]):
    """Structured response from NerdGraph API with generic data type"""
    data: Optional[T] = None
    errors: Optional[List[Dict[str, Any]]] = None

    @property
    def has_errors(self) -> bool:
        """Check if the response contains errors"""
        return self.errors is not None and len(self.errors) > 0


@dataclass
class TimeseriesDataPoint:
    """Represents a single timeseries data point"""
    timestamp: datetime
    value: float


@dataclass
class MetricResult:
    """Represents metric query results"""
    name: str
    timeseries: List[TimeseriesDataPoint]
    metadata: Dict[str, Any]


@dataclass
class Alert:
    """Represents a New Relic alert/issue"""
    id: str
    name: str
    status: AlertStatus
    severity: AlertSeverity
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    closed_by: Optional[str] = None
    policy_name: Optional[str] = None
    condition_name: Optional[str] = None
    entity_guids: Optional[List[str]] = None
    deep_link_url: Optional[str] = None
    total_incidents: Optional[int] = None


class NewRelicGraphQLError(Exception):
    """Custom exception for New Relic GraphQL errors"""
    
    def __init__(self, message: str, errors: Optional[List[Dict[str, Any]]] = None):
        self.errors = errors or []
        error_details = f": {json.dumps(errors)}" if errors else ""
        super().__init__(f"{message}{error_details}")


class NewRelicGraphQLClient:
    """
    Client for interacting with New Relic's NerdGraph GraphQL API.
    Supports both US and EU endpoints with comprehensive error handling.
    """
    
    def __init__(
        self, 
        api_key: str, 
        region: Region = Region.US, 
        account_id: Optional[str] = None, 
        timeout: int = 30
    ):
        """
        Initialize the New Relic GraphQL client.
        
        Args:
            api_key: New Relic API key
            region: API region (US or EU)
            account_id: Default account ID to use for queries
            timeout: Request timeout in seconds
        """
        self.api_key = api_key
        self.region = region
        self.account_id = account_id
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'API-Key': self.api_key
        })
        
    def execute_query(self, query: str, variables: Optional[Dict[str, Any]] = None) -> GraphQLResponse:
        """
        Execute a GraphQL query against New Relic's NerdGraph API.
        
        Args:
            query: GraphQL query string
            variables: Variables for the GraphQL query
            
        Returns:
            GraphQLResponse object with data and errors
            
        Raises:
            NewRelicGraphQLError: When API request fails or returns GraphQL errors
        """
        payload = {
            'query': query,
            'variables': variables or {}
        }
        
        try:
            response = self.session.post(
                self.region.value,
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            graphql_response = GraphQLResponse(
                data=data.get('data'),
                errors=data.get('errors')
            )
            
            if graphql_response.has_errors:
                raise NewRelicGraphQLError(
                    "GraphQL query failed",
                    graphql_response.errors
                )
                
            return graphql_response
            
        except requests.exceptions.RequestException as e:
            raise NewRelicGraphQLError(f"Failed to execute query: {str(e)}")

    def _verify_account_id(self, account_id: Optional[str] = None) -> str:
        """
        Verify that an account ID is available, either from the method parameter or client instance.
        
        Args:
            account_id: Optional account ID to use
            
        Returns:
            The verified account ID
            
        Raises:
            ValueError: If no account ID is available
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("account_id must be provided either at client initialization or method call")
        return account_id

    def _format_datetime(self, dt: datetime) -> str:
        """
        Format a datetime object for use in New Relic queries.
        
        Args:
            dt: The datetime to format
            
        Returns:
            Formatted datetime string with Z suffix for UTC
        """
        return dt.isoformat() + "Z"

    def get_metric_timeseries(
        self,
        metric_name: str,
        account_id: Optional[str] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        where: Optional[str] = None,
        facet: Optional[Union[str, List[str]]] = None,
        timeseries_period: str = "1 minute"
    ) -> List[MetricResult]:
        """
        Fetch timeseries metric data using NRQL.

        Args:
            metric_name: Name of the metric to query
            account_id: Account ID (overrides default if set)
            since: Start time for the query (defaults to 30 minutes ago)
            until: End time for the query (defaults to now)
            where: Optional NRQL WHERE clause
            facet: Optional facet clause (string or list of strings)
            timeseries_period: Time bucket for the timeseries (default: 1 minute)

        Returns:
            List of MetricResult objects containing the timeseries data
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        
        # Set default time values if not provided
        since = since or (datetime.now(UTC) - timedelta(minutes=30))
        until = until or datetime.now(UTC)

        # Build the NRQL query
        query_parts = [
            f"SELECT latest({metric_name})",
            f"FROM Metric",
            f"SINCE '{self._format_datetime(since)}'",
            f"UNTIL '{self._format_datetime(until)}'",
            f"TIMESERIES {timeseries_period}"
        ]

        if where:
            query_parts.insert(2, f"WHERE {where}")

        if facet:
            if isinstance(facet, list):
                facet_clause = ", ".join(facet)
            else:
                facet_clause = facet
            query_parts.insert(-1, f"FACET {facet_clause}")

        nrql = " ".join(query_parts)

        # Construct GraphQL query
        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.execute_query(graphql_query, variables)
        
        try:
            nrql_data = response.data["actor"]["account"]["nrql"]
            results = nrql_data["results"]
            metadata = nrql_data["metadata"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to parse metric results: {str(e)}")

        metric_results = []
        for result in results:
            timeseries_data = []
            for timestamp, value in result.items():
                if timestamp != "facet":
                    try:
                        ts = datetime.fromtimestamp(int(timestamp) / 1000, UTC)
                        val = float(value) if value is not None else 0.0
                        timeseries_data.append(
                            TimeseriesDataPoint(timestamp=ts, value=val)
                        )
                    except (ValueError, TypeError):
                        # Skip invalid data points
                        continue

            metric_results.append(
                MetricResult(
                    name=metric_name,
                    timeseries=sorted(timeseries_data, key=lambda x: x.timestamp),
                    metadata={
                        "facet": result.get("facet"),
                        **metadata
                    }
                )
            )

        return metric_results

    def fetch_pod_details(
        self, 
        pod_name: str, 
        start_time: Optional[datetime] = None, 
        end_time: Optional[datetime] = None,
        account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Fetch pod details from New Relic K8sPodSample.

        Args:
            pod_name: Name of the pod to query
            start_time: Start time for the query (defaults to 30 minutes ago)
            end_time: End time for the query (defaults to now)
            account_id: Account ID (overrides default if set)

        Returns:
            Dictionary containing pod details and metrics
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        start_time = start_time or (datetime.now(UTC) - timedelta(minutes=30))
        end_time = end_time or datetime.now(UTC)

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            timeWindow {
                                start
                                end
                            }
                        }
                    }
                }
            }
        }
        """

        nrql = f"""
        SELECT *
        FROM K8sPodSample
        WHERE podName = '{pod_name}'
        SINCE '{self._format_datetime(start_time)}'
        UNTIL '{self._format_datetime(end_time)}'
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.execute_query(graphql_query, variables)
        
        try:
            return {
                "pod_details": response.data["actor"]["account"]["nrql"]["results"],
                "metadata": response.data["actor"]["account"]["nrql"]["metadata"]
            }
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to parse pod details: {str(e)}")

    def get_infrastructure_events(
        self,
        node_name: str,
        cluster_name: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        account_id: Optional[str] = None,
        event_type: str = "Warning"
    ) -> List[Dict[str, Any]]:
        """
        Fetch infrastructure events for a specific node.

        Args:
            node_name: Name of the node
            cluster_name: Optional cluster name for filtering
            start_time: Start time for the query (defaults to 24 hours ago)
            end_time: End time for the query (defaults to now)
            account_id: Account ID (overrides default if set)
            event_type: Type of events to query (default: "Warning")

        Returns:
            List of infrastructure events
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        start_time = start_time or (datetime.now(UTC) - timedelta(days=1))
        end_time = end_time or datetime.now(UTC)

        # Build cluster filter
        cluster_filter = f"AND clusterName = '{cluster_name}'" if cluster_name else ""

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                    }
                }
            }
        }
        """

        nrql = f"""
        SELECT *
        FROM InfrastructureEvent
        WHERE category = 'kubernetes'
        {cluster_filter}
        AND event.involvedObject.name = '{node_name}'
        AND event.type = '{event_type}'
        SINCE '{self._format_datetime(start_time)}'
        UNTIL '{self._format_datetime(end_time)}'
        LIMIT MAX
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.execute_query(graphql_query, variables)
        
        try:
            return response.data["actor"]["account"]["nrql"]["results"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to parse infrastructure events: {str(e)}")

    def get_kubernetes_events(
        self,
        pod_name: str,
        cluster_name: Optional[str] = None,
        namespace: Optional[str] = None,
        account_id: Optional[str] = None,
        since: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        Fetch Kubernetes events for a specific pod.

        Args:
            pod_name: Name of the pod
            cluster_name: Optional cluster name filter
            namespace: Optional namespace filter
            account_id: Account ID (overrides default if set)
            since: Start time for the query (defaults to 1 hour ago)

        Returns:
            List of Kubernetes events
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        since = since or (datetime.now(UTC) - timedelta(hours=1))

        # Build WHERE clause
        where_conditions = [f"podName = '{pod_name}'"]
        if cluster_name:
            where_conditions.append(f"clusterName = '{cluster_name}'")
        if namespace:
            where_conditions.append(f"namespaceName = '{namespace}'")

        where_clause = " AND ".join(where_conditions)

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                    }
                }
            }
        }
        """

        nrql = f"""
        SELECT *
        FROM K8sEvent
        WHERE {where_clause}
        SINCE '{self._format_datetime(since)}'
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.execute_query(graphql_query, variables)
        
        try:
            return response.data["actor"]["account"]["nrql"]["results"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to parse Kubernetes events: {str(e)}")

    def get_alerts(
        self,
        account_id: Optional[str] = None,
        status: Optional[AlertStatus] = None,
        entity_guid: Optional[str] = None,
        since: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Alert]:
        """
        Fetch New Relic issues/incidents.

        Args:
            account_id: Account ID (overrides default if set)
            status: Optional filter by alert status
            entity_guid: Optional filter by entity GUID
            since: Optional filter for alerts since a timestamp
            limit: Maximum number of alerts to return

        Returns:
            List of Alert objects
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        since_filter = f", createdAt: {int(since.timestamp() * 1000)}" if since else ""
        status_value = f", states: [{status.name}]" if status else ""
        entity_filter = f", entityGuids: [\"{entity_guid}\"]" if entity_guid else ""
        
        graphql_query = f"""
        query($accountId: Int!) {{
            actor {{
                account(id: $accountId) {{
                    aiIssues {{
                        issues(filters: {{
                            _manyfilterscombinenwiththis_: true
                            {status_value}
                            {entity_filter}
                            {since_filter}
                        }}, limit: {limit}) {{
                            issues {{
                                account {{
                                    id
                                    name
                                }}
                                acknowledgedAt
                                acknowledgedBy
                                activatedAt
                                closedAt
                                closedBy
                                conditionName
                                createdAt
                                deepLinkUrl
                                description
                                entityGuids
                                entityNames
                                issueId
                                policyName
                                priority
                                state
                                title
                                totalIncidents
                                updatedAt
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """

        variables = {"accountId": int(account_id)}
        response = self.execute_query(graphql_query, variables)
        
        alerts = []
        try:
            issues = response.data["actor"]["account"]["aiIssues"]["issues"]["issues"]
            for issue in issues:
                alerts.append(self._parse_alert(issue))
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to parse alerts: {str(e)}")
        
        return alerts

    def get_all_policy_ids(self, account_id: Optional[str] = None) -> List[str]:
        """
        Get all alert policy IDs for an account.
        
        Args:
            account_id: Account ID (overrides default if set)
            
        Returns:
            List of policy IDs
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        
        graphql_query = """
        query($accountId: Int!) {
            actor {
                account(id: $accountId) {
                    alerts {
                        policiesSearch {
                            policies {
                                id
                            }
                        }
                    }
                }
            }
        }
        """
        
        variables = {"accountId": int(account_id)}
        response = self.execute_query(graphql_query, variables)
        
        try:
            policies = response.data["actor"]["account"]["alerts"]["policiesSearch"]["policies"]
            return [policy["id"] for policy in policies]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to fetch policy IDs")

    def get_webhook_destination(
        self, 
        name: str, 
        url: str, 
        account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get a webhook destination ID by name and URL.
        
        Args:
            name: Webhook name
            url: Webhook URL
            account_id: Account ID (overrides default if set)
            
        Returns:
            Webhook destination ID if found, None otherwise
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        
        graphql_query = """
        query($accountId: Int!, $name: String!, $url: String!) {
            actor {
                account(id: $accountId) {
                    aiNotifications {
                        destinations(filters: {
                            name: $name,
                            type: WEBHOOK,
                            property: { key: "url", value: $url }
                        }) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "name": name,
            "url": url
        }
        
        response = self.execute_query(graphql_query, variables)
        
        try:
            destinations = response.data["actor"]["account"]["aiNotifications"]["destinations"]["entities"]
            return destinations[0]["id"] if destinations else None
        except (KeyError, IndexError):
            return None

    def create_webhook_destination(
        self, 
        name: str, 
        url: str, 
        account_id: Optional[str] = None
    ) -> str:
        """
        Create a webhook destination.
        
        Args:
            name: Webhook name
            url: Webhook URL
            account_id: Account ID (overrides default if set)
            
        Returns:
            Webhook destination ID
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $name: String!, $url: String!) {
            aiNotificationsCreateDestination(
                accountId: $accountId,
                destination: {
                    type: WEBHOOK,
                    name: $name,
                    properties: [{key: "url", value: $url}]
                }
            ) {
                destination {
                    id
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "name": name,
            "url": url
        }
        
        response = self.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiNotificationsCreateDestination"]["destination"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create webhook destination")

    def get_notification_channel(
        self, 
        destination_id: str, 
        name: str, 
        account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get a notification channel ID by destination ID and name.
        
        Args:
            destination_id: Destination ID
            name: Channel name
            account_id: Account ID (overrides default if set)
            
        Returns:
            Channel ID if found, None otherwise
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        
        graphql_query = """
        query($accountId: Int!, $destinationId: ID!, $name: String!) {
            actor {
                account(id: $accountId) {
                    aiNotifications {
                        channels(filters: {
                            destinationId: $destinationId,
                            name: $name
                        }) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "destinationId": destination_id,
            "name": name
        }
        
        response = self.execute_query(graphql_query, variables)
        
        try:
            channels = response.data["actor"]["account"]["aiNotifications"]["channels"]["entities"]
            return channels[0]["id"] if channels else None
        except (KeyError, IndexError):
            return None

    def create_notification_channel(
        self, 
        destination_id: str, 
        name: str, 
        api_key: str, 
        account_id: Optional[str] = None
    ) -> str:
        """
        Create a notification channel.
        
        Args:
            destination_id: Destination ID
            name: Channel name
            api_key: API key for the channel
            account_id: Account ID (overrides default if set)
            
        Returns:
            Channel ID
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self._verify_account_id(account_id)
        
        # Define the payload template with proper JSON escaping
        payload_template = (
            '{"id": {{ json issueId }},'
            '"issueUrl": {{ json issuePageUrl }},'
            '"name": {{ json annotations.title.[0] }},'
            '"severity": {{ json priority }},'
            '"impactedEntities": {{ json entitiesData.names }},'
            '"totalIncidents": {{ json totalIncidents }},'
            '"status": {{ json state }},'
            '"trigger": {{ json triggerEvent }},'
            '"isCorrelated": {{ json isCorrelated }},'
            '"createdAt": {{ createdAt }},'
            '"updatedAt": {{ updatedAt }},'
            '"lastReceived": {{ updatedAt }},'
            '"source": {{ json accumulations.source }},'
            '"alertPolicyNames": {{ json accumulations.policyName }},'
            '"alertConditionNames": {{ json accumulations.conditionName }},'
            '"workflowName": {{ json workflowName }}}'
        )
        
        graphql_query = """
        mutation($accountId: Int!, $destinationId: ID!, $name: String!, $headers: String!, $payload: String!) {
            aiNotificationsCreateChannel(
                accountId: $accountId,
                channel: {
                    name: $name,
                    product: IINT,
                    type: WEBHOOK,
                    destinationId: $destinationId,
                    properties: [
                        {
                            key: "headers",
                            value: $headers
                        },
                        {
                            key: "payload",
                            value: $payload
                        }
                    ]
                }
            ) {
                channel {
                    id
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "destinationId": destination_id,
            "name": name,
            "headers": json.dumps({"X-API-KEY": api_key}),
            "payload": payload_template
        }
        
        response = self.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiNotificationsCreateChannel"]["channel"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create notification channel")

    def get_workflow(
        self, 
        name: str, 
        channel_id: str, 
        account_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Get a workflow ID by name and channel ID.
        
        Args:
            name: Workflow name
            channel_id: Channel ID
            account_id: Account ID (overrides default if set)
            
        Returns:
            Workflow ID if found, None otherwise
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        
        graphql_query = """
        query($accountId: Int!, $name: String!, $channelId: ID!) {
            actor {
                account(id: $accountId) {
                    aiWorkflows {
                        workflows(
                            filters: {name: $name, channelId: $channelId}
                        ) {
                            entities {
                                id
                            }
                        }
                    }
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "name": name,
            "channelId": channel_id
        }
        
        response = self.execute_query(graphql_query, variables)
        
        try:
            workflows = response.data["actor"]["account"]["aiWorkflows"]["workflows"]["entities"]
            return workflows[0]["id"] if workflows else None
        except (KeyError, IndexError):
            return None

    def create_workflow(
        self, 
        name: str, 
        channel_id: str, 
        policy_ids: List[str], 
        account_id: Optional[str] = None
    ) -> str:
        """
        Create a workflow.
        
        Args:
            name: Workflow name
            channel_id: Channel ID
            policy_ids: List of policy IDs
            account_id: Account ID (overrides default if set)
            
        Returns:
            Workflow ID
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the mutation fails
        """
        account_id = self._verify_account_id(account_id)
        
        graphql_query = """
        mutation($accountId: Int!, $name: String!, $channelId: ID!, $policyIds: [String!]!) {
            aiWorkflowsCreateWorkflow(
                accountId: $accountId
                createWorkflowData: {
                    destinationConfigurations: {
                        channelId: $channelId,
                        notificationTriggers: [ACTIVATED, ACKNOWLEDGED, CLOSED, PRIORITY_CHANGED, OTHER_UPDATES]
                    },
                    issuesFilter: {
                        predicates: [
                            {
                                attribute: "labels.policyIds",
                                operator: EXACTLY_MATCHES,
                                values: $policyIds
                            }
                        ],
                        type: FILTER
                    },
                    workflowEnabled: true,
                    destinationsEnabled: true,
                    mutingRulesHandling: DONT_NOTIFY_FULLY_MUTED_ISSUES,
                    name: $name
                }
            ) {
                workflow {
                    id
                }
            }
        }
        """
        
        variables = {
            "accountId": int(account_id),
            "name": name,
            "channelId": channel_id,
            "policyIds": policy_ids
        }
        
        response = self.execute_query(graphql_query, variables)
        
        try:
            return response.data["aiWorkflowsCreateWorkflow"]["workflow"]["id"]
        except (KeyError, TypeError):
            raise NewRelicGraphQLError("Failed to create workflow")

    def _parse_alert(self, issue: Dict[str, Any]) -> Alert:
        """
        Parse an issue from the API response into an Alert object.
        
        Args:
            issue: Raw issue data from API
            
        Returns:
            Parsed Alert object
        """
        title = issue.get("title", "")
        description = issue.get("description", "")
        
        # Handle both string and list formats for title and description
        if isinstance(title, list) and title:
            title = title[0]
        if isinstance(description, list) and description:
            description = description[0]
            
        # Parse timestamps with proper error handling
        def parse_timestamp(ts_value):
            if not ts_value:
                return None
            try:
                return datetime.fromtimestamp(ts_value / 1000, UTC)
            except (ValueError, TypeError):
                return None
        
        # Parse status and severity with fallbacks
        try:
            status = AlertStatus(issue.get("state", "").lower())
        except (ValueError, AttributeError):
            status = AlertStatus.FIRING  # Default
            
        try:
            severity = AlertSeverity(issue.get("priority", "").lower())
        except (ValueError, AttributeError):
            severity = AlertSeverity.INFO  # Default
        
        return Alert(
            id=issue.get("issueId", ""),
            name=title,
            status=status,
            severity=severity,
            description=description,
            created_at=parse_timestamp(issue.get("createdAt")),
            updated_at=parse_timestamp(issue.get("updatedAt")),
            closed_at=parse_timestamp(issue.get("closedAt")),
            acknowledged_at=parse_timestamp(issue.get("acknowledgedAt")),
            acknowledged_by=issue.get("acknowledgedBy"),
            closed_by=issue.get("closedBy"),
            policy_name=issue.get("policyName"),
            condition_name=issue.get("conditionName"),
            entity_guids=issue.get("entityGuids"),
            deep_link_url=issue.get("deepLinkUrl"),
            total_incidents=issue.get("totalIncidents")
        )

    def parse_generic_timeseries(
        self, 
        timeseries_data: Dict[str, Any], 
        metric_name: str
    ) -> List[MetricResult]:
        """
        Parse a generic timeseries response and convert it to a list of MetricResult objects.

        Args:
            timeseries_data: The raw timeseries response from a NRQL query
            metric_name: The default metric name if facets are not available

        Returns:
            List of parsed MetricResult objects
        """
        results: List[MetricResult] = []
        
        # Get the results array from the response
        data_points = timeseries_data.get("results", [])
        if not data_points:
            return results

        # Group data points by facet
        facet_groups: Dict[str, List[Dict]] = {}
        for point in data_points:
            facet = point.get("facet")
            if facet:
                # Convert facet list to string key
                if isinstance(facet, list):
                    facet_key = " | ".join(str(f) for f in facet)
                else:
                    facet_key = str(facet)
            else:
                facet_key = metric_name
            
            if facet_key not in facet_groups:
                facet_groups[facet_key] = []
            facet_groups[facet_key].append(point)

        # Process each facet group
        for facet_key, points in facet_groups.items():
            timeseries: List[TimeseriesDataPoint] = []
            
            for point in points:
                # Get the timestamp from beginTimeSeconds
                timestamp_value = point.get("beginTimeSeconds")
                if timestamp_value is None:
                    # Try alternative formats
                    for key in point:
                        if isinstance(key, str) and key.isdigit():
                            timestamp_value = int(key) / 1000  # Convert ms to seconds
                            break
                
                if timestamp_value is None:
                    continue
                    
                try:
                    timestamp = datetime.fromtimestamp(timestamp_value, UTC)
                except (ValueError, TypeError):
                    continue
                
                # Find the metric value - look for keys ending with the metric name
                # or for common metrics pattern like average.*
                metric_value = None
                for key, value in point.items():
                    if (key.endswith(metric_name) or 
                        key.startswith("average.") or 
                        key == "count" or 
                        key == "sum" or
                        key == "latest"):
                        metric_value = value
                        break
                
                # Skip if no valid value found
                if metric_value is None:
                    continue
                
                # Convert to float, use 0.0 for null values
                try:
                    value = float(metric_value) if metric_value is not None else 0.0
                except (ValueError, TypeError):
                    value = 0.0
                
                timeseries.append(TimeseriesDataPoint(
                    timestamp=timestamp,
                    value=value
                ))
            
            # Only create a MetricResult if we have data points
            if timeseries:
                metadata = {
                    "facet": facet_key,
                    "total_points": len(timeseries)
                }
                results.append(MetricResult(
                    name=facet_key,
                    timeseries=sorted(timeseries, key=lambda x: x.timestamp),
                    metadata=metadata
                ))

        return results

    def get_evicted_pods_metrics(
        self,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        cluster_pattern: str = '%prd%',
        exclude_patterns: Optional[List[str]] = None,
        timeseries_period: str = "10 minutes",
        account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get metrics for evicted pods across clusters.

        Args:
            since: Start time for the query (defaults to 24 hours ago)
            until: End time for the query (defaults to now)
            cluster_pattern: Pattern to match cluster names (defaults to '%prd%')
            exclude_patterns: List of patterns to exclude from pod names
            timeseries_period: Time bucket for the timeseries (default: 10 minutes)
            account_id: Account ID (overrides default if set)

        Returns:
            Raw results from New Relic NRQL query
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        since = since or (datetime.now(UTC) - timedelta(hours=24))
        until = until or datetime.now(UTC)
        exclude_patterns = exclude_patterns or []

        # Build WHERE clause for exclusions
        exclusion_clauses = [f"podName NOT LIKE '{pattern}'" for pattern in exclude_patterns]
        exclusion_str = " AND ".join(exclusion_clauses)
        if exclusion_str:
            exclusion_str = f" AND {exclusion_str}"

        nrql = f"""
        SELECT count(*) FROM K8sPodSample 
        WHERE reason = 'Evicted' 
        AND status = 'Failed' 
        AND clusterName LIKE '{cluster_pattern}'{exclusion_str}
        FACET podName, clusterName 
        TIMESERIES {timeseries_period} 
        SINCE '{self._format_datetime(since)}' 
        UNTIL '{self._format_datetime(until)}'
        """

        # Build the GraphQL query
        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.execute_query(graphql_query, variables)
        try:
            return response.data["actor"]["account"]["nrql"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch evicted pods metrics: {str(e)}")

    def get_generic_timeseries_metrics(
        self,
        nrql_query: str,
        metric_name: str,
        account_id: Optional[str] = None
    ) -> List[MetricResult]:
        """
        Execute a NRQL query and parse its timeseries results.

        Args:
            nrql_query: The NRQL query to execute
            metric_name: Default name for the metric if no facets are present
            account_id: Optional account ID to override the default

        Returns:
            List of parsed MetricResult objects
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        
        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql_query
        }

        response = self.execute_query(graphql_query, variables)
        try:
            results = response.data["actor"]["account"]["nrql"]
            return self.parse_generic_timeseries(results, metric_name)
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch timeseries metrics: {str(e)}")

    def get_pod_details(
        self,
        pod_name: str,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        account_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get detailed information about a specific pod.

        Args:
            pod_name: Name of the pod
            since: Start time for the query
            until: End time for the query
            account_id: Optional account ID override

        Returns:
            List containing pod details
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        since = since or (datetime.now(UTC) - timedelta(minutes=30))
        until = until or datetime.now(UTC)

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                    }
                }
            }
        }
        """

        nrql = f"""
        SELECT *
        FROM K8sPodSample
        WHERE podName = '{pod_name}'
        SINCE '{self._format_datetime(since)}'
        UNTIL '{self._format_datetime(until)}'
        LIMIT 100
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.execute_query(graphql_query, variables)
        try:
            return response.data["actor"]["account"]["nrql"]["results"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch pod details: {str(e)}")

    def get_node_metrics(
        self,
        entity_guid: str,
        metric_type: str = 'cpu',  # 'cpu', 'memory', or 'disk'
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get node metrics (CPU, Memory, or Disk usage).

        Args:
            entity_guid: Entity GUID of the node
            metric_type: Type of metric to fetch ('cpu', 'memory', or 'disk')
            since: Start time for the query
            until: End time for the query
            account_id: Optional account ID override

        Returns:
            Dictionary with metric results and metadata
            
        Raises:
            ValueError: If no account_id is available or invalid metric type
            NewRelicGraphQLError: If the query fails
        """
        account_id = self._verify_account_id(account_id)
        since = since or (datetime.now(UTC) - timedelta(days=1))
        until = until or datetime.now(UTC)

        metric_queries = {
            'cpu': "SELECT average(cpuPercent) AS 'CPU used %' FROM SystemSample",
            'memory': "SELECT average(memoryUsedPercent) AS 'Memory used %' FROM SystemSample",
            'disk': "SELECT max(diskUsedPercent) AS 'Storage used %' FROM StorageSample"
        }

        if metric_type not in metric_queries:
            raise ValueError(f"Invalid metric type. Must be one of: {', '.join(metric_queries.keys())}")

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
            actor {
                account(id: $accountId) {
                    nrql(query: $nrql) {
                        results
                        metadata {
                            facets
                        }
                    }
                }
            }
        }
        """

        nrql = f"""
        {metric_queries[metric_type]}
        WHERE entityGuid = '{entity_guid}'
        TIMESERIES AUTO
        SINCE '{self._format_datetime(since)}'
        UNTIL '{self._format_datetime(until)}'
        """

        variables = {
            "accountId": int(account_id),
            "nrql": nrql
        }

        response = self.execute_query(graphql_query, variables)
        try:
            return response.data["actor"]["account"]["nrql"]
        except (KeyError, TypeError) as e:
            raise NewRelicGraphQLError(f"Failed to fetch node metrics: {str(e)}")

    def get_entity_metrics(
        self,
        entity_guid: str,
        metrics: List[str],
        account_id: Optional[str] = None,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        period: str = "1 minute"
    ) -> Dict[str, List[MetricResult]]:
        """
        Fetch multiple metrics for a specific entity.

        Args:
            entity_guid: Entity GUID to query
            metrics: List of metric names to fetch
            account_id: Account ID (overrides default if set)
            since: Start time for the query (defaults to 30 minutes ago)
            until: End time for the query (defaults to now)
            period: Time bucket for the timeseries

        Returns:
            Dictionary mapping metric names to their MetricResult lists
            
        Raises:
            ValueError: If no account_id is available
            NewRelicGraphQLError: If any query fails
        """
        results = {}
        for metric in metrics:
            results[metric] = self.get_metric_timeseries(
                metric_name=metric,
                account_id=account_id,
                since=since,
                until=until,
                where=f"entity.guid = '{entity_guid}'",
                timeseries_period=period
            )
        return results

    def close(self) -> None:
        """Close the underlying session"""
        self.session.close()

    def __enter__(self) -> 'NewRelicGraphQLClient':
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Context manager exit"""
        self.close()


def format_metric_results(results: List[MetricResult]) -> None:
    """
    Helper function to format and print metric results.
    
    Args:
        results: List of MetricResult objects to format and print
    """
    for result in results:
        print(f"\nMetric: {result.name}")
        print("=" * 50)
        print("Metadata:", json.dumps(result.metadata, indent=2))
        print("\nTimeseries Data:")
        for point in result.timeseries:
            print(f"  {point.timestamp.isoformat()}: {point.value}")


if __name__ == "__main__":
    import os
    from datetime import datetime, timedelta

    # Get API key from environment variable
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")

    if not api_key or not account_id:
        print("Error: NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID environment variables must be set")
        exit(1)

    try:
        # Initialize the client
        with NewRelicGraphQLClient(api_key=api_key, account_id=account_id) as client:
            # Example 1: Get evicted pods metrics
            print("\n=== Example 1: Evicted Pods Metrics ===")
            since = datetime.now(UTC) - timedelta(hours=24)
            evicted_pods = client.get_evicted_pods_metrics(
                since=since,
                cluster_pattern='%prd%',
                exclude_patterns=['%assetprocessor%', '%fru-prd%'],
                timeseries_period='30 minutes'
            )
            print("Raw Results:")
            print(json.dumps(evicted_pods, indent=2))

            # Example 2: Generic timeseries metrics for CPU usage
            print("\n=== Example 2: Generic CPU Usage Metrics ===")
            cpu_query = """
            SELECT average(cpuUsedCores)
            FROM K8sContainerSample
            WHERE clusterName LIKE '%prd%'
            FACET containerName, clusterName
            TIMESERIES 30 minutes
            SINCE 24 hours ago
            LIMIT 5
            """
            cpu_metrics = client.get_generic_timeseries_metrics(
                nrql_query=cpu_query,
                metric_name="container_cpu_usage"
            )
            format_metric_results(cpu_metrics)

            # Example 3: Memory usage metrics
            print("\n=== Example 3: Memory Usage Metrics ===")
            memory_query = """
            SELECT average(memoryUsedBytes)
            FROM K8sContainerSample
            WHERE clusterName LIKE '%prd%'
            FACET containerName, clusterName
            TIMESERIES 30 minutes
            SINCE 24 hours ago
            LIMIT 5
            """
            memory_metrics = client.get_generic_timeseries_metrics(
                nrql_query=memory_query,
                metric_name="container_memory_usage"
            )
            format_metric_results(memory_metrics)

    except NewRelicGraphQLError as e:
        print(f"Error querying New Relic: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")


