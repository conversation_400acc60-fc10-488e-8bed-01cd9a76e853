from kubernetes import client, config, watch
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class KubernetesClient:
    """A client class to interact with Kubernetes cluster and fetch various metrics and details."""
    
    def __init__(self, kubeconfig_path: Optional[str] = None):
        """
        Initialize the Kubernetes client.
        
        Args:
            kubeconfig_path (str, optional): Path to kubeconfig file. If None, uses default config.
        """
        try:
            if kubeconfig_path:
                config.load_kube_config(config_file=kubeconfig_path)
            else:
                config.load_kube_config()
                
            self.core_v1 = client.CoreV1Api()
            self.apps_v1 = client.AppsV1Api()
            self.batch_v1 = client.BatchV1Api()
            self.events_v1 = client.EventsV1Api()
        except Exception as e:
            logger.error(f"Failed to initialize Kubernetes client: {str(e)}")
            raise
    
    def get_pod_details(self, namespace: str, pod_name: str) -> Dict:
        """
        Get detailed information about a specific pod.
        
        Args:
            namespace (str): Kubernetes namespace
            pod_name (str): Name of the pod
            
        Returns:
            Dict: Pod details including status, state, containers, etc.
        """
        try:
            pod = self.core_v1.read_namespaced_pod(name=pod_name, namespace=namespace)
            return {
                'name': pod.metadata.name,
                'namespace': pod.metadata.namespace,
                'status': pod.status.phase,
                'pod_ip': pod.status.pod_ip,
                'host_ip': pod.status.host_ip,
                'start_time': pod.status.start_time,
                'containers': [
                    {
                        'name': container.name,
                        'image': container.image,
                        'ready': container.ready,
                        'restart_count': container.restart_count,
                        'state': self._get_container_state(container.state)
                    } for container in pod.status.container_statuses
                ] if pod.status.container_statuses else []
            }
        except client.exceptions.ApiException as e:
            logger.error(f"Failed to get pod details: {str(e)}")
            raise

    def get_deployment_details(self, namespace: str, deployment_name: str) -> Dict:
        """
        Get detailed information about a specific deployment.
        
        Args:
            namespace (str): Kubernetes namespace
            deployment_name (str): Name of the deployment
            
        Returns:
            Dict: Deployment details including replicas, status, etc.
        """
        try:
            deployment = self.apps_v1.read_namespaced_deployment(
                name=deployment_name, namespace=namespace
            )
            return {
                'name': deployment.metadata.name,
                'namespace': deployment.metadata.namespace,
                'replicas': deployment.spec.replicas,
                'available_replicas': deployment.status.available_replicas,
                'ready_replicas': deployment.status.ready_replicas,
                'updated_replicas': deployment.status.updated_replicas,
                'strategy': deployment.spec.strategy.type,
                'selector': deployment.spec.selector.match_labels,
                'containers': [
                    {
                        'name': container.name,
                        'image': container.image,
                    } for container in deployment.spec.template.spec.containers
                ]
            }
        except client.exceptions.ApiException as e:
            logger.error(f"Failed to get deployment details: {str(e)}")
            raise

    def get_node_details(self, node_name: str) -> Dict:
        """
        Get detailed information about a specific node.
        
        Args:
            node_name (str): Name of the node
            
        Returns:
            Dict: Node details including capacity, allocatable resources, etc.
        """
        try:
            node = self.core_v1.read_node(name=node_name)
            return {
                'name': node.metadata.name,
                'status': self._get_node_status(node),
                'capacity': node.status.capacity,
                'allocatable': node.status.allocatable,
                'conditions': [
                    {
                        'type': condition.type,
                        'status': condition.status,
                        'message': condition.message
                    } for condition in node.status.conditions
                ],
                'addresses': [
                    {
                        'type': address.type,
                        'address': address.address
                    } for address in node.status.addresses
                ]
            }
        except client.exceptions.ApiException as e:
            logger.error(f"Failed to get node details: {str(e)}")
            raise

    def get_events(self, namespace: str, resource_name: Optional[str] = None) -> List[Dict]:
        """
        Get Kubernetes events for a namespace or specific resource.
        
        Args:
            namespace (str): Kubernetes namespace
            resource_name (str, optional): Name of the specific resource
            
        Returns:
            List[Dict]: List of events with details
        """
        try:
            field_selector = f"metadata.namespace={namespace}"
            if resource_name:
                field_selector += f",involvedObject.name={resource_name}"
                
            events = self.core_v1.list_namespaced_event(
                namespace=namespace,
                field_selector=field_selector
            )
            
            return [
                {
                    'type': event.type,
                    'reason': event.reason,
                    'message': event.message,
                    'count': event.count,
                    'first_timestamp': event.first_timestamp,
                    'last_timestamp': event.last_timestamp,
                    'involved_object': {
                        'kind': event.involved_object.kind,
                        'name': event.involved_object.name
                    }
                } for event in events.items
            ]
        except client.exceptions.ApiException as e:
            logger.error(f"Failed to get events: {str(e)}")
            raise

    def get_service_details(self, namespace: str, service_name: str) -> Dict:
        """
        Get detailed information about a specific service.
        
        Args:
            namespace (str): Kubernetes namespace
            service_name (str): Name of the service
            
        Returns:
            Dict: Service details including type, ports, selectors, etc.
        """
        try:
            service = self.core_v1.read_namespaced_service(
                name=service_name, namespace=namespace
            )
            return {
                'name': service.metadata.name,
                'namespace': service.metadata.namespace,
                'type': service.spec.type,
                'cluster_ip': service.spec.cluster_ip,
                'external_ips': service.spec.external_i_ps if hasattr(service.spec, 'external_i_ps') else None,
                'ports': [
                    {
                        'name': port.name,
                        'port': port.port,
                        'target_port': port.target_port,
                        'protocol': port.protocol
                    } for port in service.spec.ports
                ],
                'selector': service.spec.selector
            }
        except client.exceptions.ApiException as e:
            logger.error(f"Failed to get service details: {str(e)}")
            raise

    @staticmethod
    def _get_container_state(state) -> Dict:
        """Helper method to extract container state information."""
        if state.running:
            return {'status': 'running', 'started_at': state.running.started_at}
        elif state.waiting:
            return {
                'status': 'waiting',
                'reason': state.waiting.reason,
                'message': state.waiting.message
            }
        elif state.terminated:
            return {
                'status': 'terminated',
                'exit_code': state.terminated.exit_code,
                'reason': state.terminated.reason,
                'message': state.terminated.message
            }
        return {'status': 'unknown'}

    @staticmethod
    def _get_node_status(node) -> str:
        """Helper method to determine node status from conditions."""
        for condition in node.status.conditions:
            if condition.type == 'Ready':
                return 'Ready' if condition.status == 'True' else 'NotReady'
        return 'Unknown'