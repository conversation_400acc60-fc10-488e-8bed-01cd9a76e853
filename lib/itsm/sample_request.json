{
"IssueId": {{ json issueId }},
"IssueUrl": {{ json issuePageUrl }},
"Title": {{ json annotations.title.[0] }},
"Severity": "CRITICAL",
"EntityId": {{ json entitiesData.ids }},
"ImpactedEntities": {{json entitiesData.names}},
"TotalIncidents": {{json totalIncidents}},
"State": {{ json state }},
"Trigger": {{ json triggerEvent }},
"IsCorrelated": {{ json isCorrelated }},
"CreatedAt": {{ createdAt }},
"UpdatedAt": {{ updatedAt }},
"Sources": {{ json accumulations.source }},
"AlertPolicyNames": {{ json accumulations.policyName }},
"AlertConditionNames": {{ json accumulations.conditionName }},
"WorkflowName": {{ json workflowName }},
"ChartLink": {{ json violationChartUrl }},
"Product": "neurons",
"NrRegion": "us",
"AlertConditionId": {{ json accumulations.conditionFamilyId.[0] }},
"MonitoringTool": "NewRelic"
}