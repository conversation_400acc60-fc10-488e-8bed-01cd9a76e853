import os
from typing import Dict

def get_db_config() -> Dict[str, str]:
    """Get PostgreSQL database configuration from environment variables."""
    return {
        'host': os.environ.get('POSTGRES_HOST', 'localhost'),
        'port': os.environ.get('POSTGRES_PORT', '5432'),
        'database': os.environ.get('POSTGRES_DB', 'incident_management'),
        'user': os.environ.get('POSTGRES_USER', 'postgres'),
        'password': os.environ.get('POSTGRES_PASSWORD', ''),
        'sslmode': os.environ.get('POSTGRES_SSL_MODE', 'require')
    } 