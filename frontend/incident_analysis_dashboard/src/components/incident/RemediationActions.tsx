
import { RemediationAction } from "@/types/incident";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { WrenchIcon } from "lucide-react";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";

interface RemediationActionsProps {
  actions: RemediationAction[];
}

export const RemediationActions = ({ actions }: RemediationActionsProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center">
          <WrenchIcon className="mr-2 h-5 w-5" />
          Remediation Actions
          <span className="ml-auto text-sm text-muted-foreground font-normal">
            {actions.length} Actions
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {actions.map((action, index) => (
            <div key={index} className="border rounded-md overflow-hidden">
              <div className="bg-secondary p-4">
                <h3 className="font-semibold text-lg">{action.title}</h3>
                <p className="text-sm text-muted-foreground mt-1">{action.description}</p>
              </div>
              
              <div className="p-4">
                <h4 className="text-sm font-medium mb-2">Steps</h4>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">#</TableHead>
                      <TableHead>Command / Action</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {action.steps.map((step, stepIndex) => (
                      <TableRow key={stepIndex}>
                        <TableCell className="font-medium">{stepIndex + 1}</TableCell>
                        <TableCell>
                          <code className="bg-muted px-1.5 py-0.5 rounded text-sm font-mono">
                            {step}
                          </code>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
