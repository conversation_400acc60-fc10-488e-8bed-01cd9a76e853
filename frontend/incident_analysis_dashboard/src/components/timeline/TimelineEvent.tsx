
import { TimelineEvent as TimelineEventType } from '@/types/incident';
import { format } from 'date-fns';
import { 
  AlertTriangle, 
  WrenchIcon, 
  RefreshCw, 
  Search 
} from 'lucide-react';

interface TimelineEventProps {
  event: TimelineEventType;
}

export const TimelineEventItem = ({ event }: TimelineEventProps) => {
  const { type, timestamp, title, description } = event;
  
  const getIcon = () => {
    switch (type) {
      case 'alert':
        return <AlertTriangle className="h-4 w-4 text-alert-critical" />;
      case 'action':
        return <WrenchIcon className="h-4 w-4 text-alert-info" />;
      case 'update':
        return <RefreshCw className="h-4 w-4 text-alert-warning" />;
      case 'investigation':
        return <Search className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getTypeLabel = () => {
    switch (type) {
      case 'alert':
        return 'Alert';
      case 'action':
        return 'Action';
      case 'update':
        return 'Update';
      case 'investigation':
        return 'Investigation';
    }
  };

  const formattedTime = format(new Date(timestamp), 'h:mm a');
  const formattedDate = format(new Date(timestamp), 'MMM d, yyyy');

  return (
    <div className="relative pl-6 pb-8">
      <div className="timeline-marker flex items-center justify-center">
        {getIcon()}
      </div>
      <div className="timeline-line" />

      <div className="ml-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 mb-1">
          <span className="text-sm font-medium">{title}</span>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <span className="inline-flex items-center rounded-full px-2 py-1 bg-secondary">
              {getTypeLabel()}
            </span>
            <span>{formattedTime}</span>
            <span>{formattedDate}</span>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
    </div>
  );
};

interface TimelineProps {
  events: TimelineEventType[];
}

export const Timeline = ({ events }: TimelineProps) => {
  // Sort events by timestamp (newest first)
  const sortedEvents = [...events].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  return (
    <div className="relative mt-4">
      {sortedEvents.map((event) => (
        <TimelineEventItem key={event.id} event={event} />
      ))}
    </div>
  );
};
