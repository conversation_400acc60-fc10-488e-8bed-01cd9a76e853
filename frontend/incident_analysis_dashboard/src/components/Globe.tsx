import { useState, useEffect, useRef } from 'react';
import { useTheme } from '@/hooks/use-theme';
import { AlertCircle } from 'lucide-react';

interface IncidentLocation {
  id: string;
  lat: number;
  lng: number;
  size: number;
}

const mockIncidentLocations: IncidentLocation[] = [
  { id: '1', lat: 40.7128, lng: -74.006, size: 8 }, // New York
  { id: '2', lat: 51.5074, lng: -0.1278, size: 10 }, // London
  { id: '3', lat: 35.6762, lng: 139.6503, size: 6 }, // Tokyo
  { id: '4', lat: -33.8688, lng: 151.2093, size: 8 }, // Sydney
  { id: '5', lat: 52.5200, lng: 13.4050, size: 7 }, // Berlin
  { id: '6', lat: 19.4326, lng: -99.1332, size: 9 }, // Mexico City
  { id: '7', lat: -23.5505, lng: -46.6333, size: 8 }, // São Paulo
  { id: '8', lat: 28.6139, lng: 77.2090, size: 10 }, // New Delhi
  { id: '9', lat: 39.9042, lng: 116.4074, size: 9 }, // Beijing
  { id: '10', lat: 55.7558, lng: 37.6173, size: 7 }, // Moscow
];

export const Globe = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [rotation, setRotation] = useState(0);
  const rotationRef = useRef(0);
  const animationRef = useRef<number>();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // Function to draw the network visualization
  const drawNetwork = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Set background color based on theme
    const bgColor = isDark ? 'rgba(15, 23, 42, 0.3)' : 'rgba(248, 250, 252, 0.6)';
    ctx.fillStyle = bgColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Center point coordinates
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    
    // Create central node
    const centralNode = {
      x: centerX,
      y: centerY,
      radius: 6,
      color: isDark ? 'rgba(100, 116, 139, 0.6)' : 'rgba(148, 163, 184, 0.8)'
    };
    
    // Network properties
    const networkRadius = Math.min(canvas.width, canvas.height * 2) * 0.4;
    
    // Create nodes based on incident locations
    const nodes = mockIncidentLocations.map((location, index) => {
      // Convert lat/lng to positions radiating from center
      const angle = (index / mockIncidentLocations.length) * Math.PI * 2 + rotationRef.current;
      const distance = networkRadius * (0.4 + (location.id.length % 5) * 0.1);
      
      return {
        x: centerX + Math.cos(angle) * distance,
        y: centerY + Math.sin(angle) * distance,
        radius: location.size / 2 + 3,
        color: isDark ? 'rgba(239, 68, 68, 0.8)' : 'rgba(239, 68, 68, 0.7)',
        pulseSize: location.size / 2 + 3,
        id: location.id
      };
    });

    // Draw background grid
    const drawGrid = () => {
      // Draw light grid lines
      const gridColor = isDark ? 'rgba(100, 116, 139, 0.2)' : 'rgba(203, 213, 225, 0.5)';
      ctx.strokeStyle = gridColor;
      ctx.lineWidth = 0.5;
      
      // Horizontal lines
      const gridSpacing = 30;
      const numLines = Math.ceil(canvas.height / gridSpacing);
      
      for (let i = 0; i < numLines; i++) {
        const y = i * gridSpacing;
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
      }
      
      // Vertical lines
      const numColumns = Math.ceil(canvas.width / gridSpacing);
      for (let i = 0; i < numColumns; i++) {
        const x = i * gridSpacing;
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
      }
    };
    
    // Draw connection lines from central node to all other nodes
    const drawConnections = () => {
      nodes.forEach((node) => {
        // Draw connection line
        ctx.beginPath();
        ctx.strokeStyle = isDark ? 'rgba(148, 163, 184, 0.3)' : 'rgba(148, 163, 184, 0.5)';
        ctx.lineWidth = 0.8;
        ctx.moveTo(centralNode.x, centralNode.y);
        ctx.lineTo(node.x, node.y);
        ctx.stroke();
        
        // Add some secondary connections between nodes
        if (parseInt(node.id) % 3 === 0) {
          const connectedNode = nodes[(parseInt(node.id) + 2) % nodes.length];
          ctx.beginPath();
          ctx.strokeStyle = isDark ? 'rgba(148, 163, 184, 0.15)' : 'rgba(148, 163, 184, 0.3)';
          ctx.lineWidth = 0.5;
          ctx.moveTo(node.x, node.y);
          ctx.lineTo(connectedNode.x, connectedNode.y);
          ctx.stroke();
        }
      });
    };
    
    // Draw all nodes
    const drawNodes = () => {
      // Draw central node
      ctx.beginPath();
      ctx.fillStyle = centralNode.color;
      ctx.arc(centralNode.x, centralNode.y, centralNode.radius, 0, Math.PI * 2);
      ctx.fill();
      
      // Draw pulse effect for central node
      const centralPulseSize = centralNode.radius * (1.2 + Math.sin(Date.now() * 0.002) * 0.3);
      ctx.beginPath();
      ctx.strokeStyle = isDark ? 'rgba(100, 116, 139, 0.4)' : 'rgba(148, 163, 184, 0.5)';
      ctx.lineWidth = 0.8;
      ctx.arc(centralNode.x, centralNode.y, centralPulseSize, 0, Math.PI * 2);
      ctx.stroke();
      
      // Draw other nodes
      nodes.forEach((node) => {
        // Glow effect
        const glowRadius = node.radius * 2;
        const glowGradient = ctx.createRadialGradient(node.x, node.y, 0, node.x, node.y, glowRadius);
        
        if (isDark) {
          glowGradient.addColorStop(0, 'rgba(239, 68, 68, 0.6)');
          glowGradient.addColorStop(1, 'rgba(239, 68, 68, 0)');
        } else {
          glowGradient.addColorStop(0, 'rgba(239, 68, 68, 0.4)');
          glowGradient.addColorStop(1, 'rgba(239, 68, 68, 0)');
        }
        
        ctx.beginPath();
        ctx.fillStyle = glowGradient;
        ctx.arc(node.x, node.y, glowRadius, 0, Math.PI * 2);
        ctx.fill();
        
        // Node center
        ctx.beginPath();
        ctx.fillStyle = node.color;
        ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
        ctx.fill();
        
        // Highlight rim
        ctx.beginPath();
        ctx.strokeStyle = isDark ? 'rgba(255, 255, 255, 0.5)' : 'rgba(255, 255, 255, 0.7)';
        ctx.lineWidth = 0.7;
        ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
        ctx.stroke();
        
        // Pulse effect
        const pulseSize = node.radius * (1.2 + Math.sin(Date.now() * 0.004 + parseInt(node.id)) * 0.3);
        ctx.beginPath();
        ctx.strokeStyle = isDark ? 'rgba(239, 68, 68, 0.3)' : 'rgba(239, 68, 68, 0.2)';
        ctx.lineWidth = 0.8;
        ctx.arc(node.x, node.y, pulseSize, 0, Math.PI * 2);
        ctx.stroke();
        
        // Small data packets traveling along the lines (only for some nodes)
        if (parseInt(node.id) % 2 === 0) {
          const time = Date.now() * 0.001;
          const packetPosition = (time % 2) / 2; // 0 to 1
          
          if (packetPosition < 0.9) { // Only show packet for part of the animation
            const packetX = centralNode.x + (node.x - centralNode.x) * packetPosition;
            const packetY = centralNode.y + (node.y - centralNode.y) * packetPosition;
            
            ctx.beginPath();
            ctx.fillStyle = isDark ? 'rgba(255, 255, 255, 0.8)' : 'rgba(30, 41, 59, 0.7)';
            ctx.arc(packetX, packetY, 1.5, 0, Math.PI * 2);
            ctx.fill();
          }
        }
      });
    };
    
    // Draw additional floating points in the background
    const drawBackgroundPoints = () => {
      const numPoints = 40;
      const time = Date.now() * 0.0005;
      
      for (let i = 0; i < numPoints; i++) {
        const angle = (i / numPoints) * Math.PI * 2;
        const distance = networkRadius * 0.8 * (0.8 + Math.sin(time + i) * 0.2);
        
        const x = centerX + Math.cos(angle + time) * distance;
        const y = centerY + Math.sin(angle + time) * distance;
        
        if (i % 4 !== 0) continue; // Only draw some points
        
        const pointSize = 0.8 + Math.sin(time * 2 + i) * 0.3;
        ctx.beginPath();
        ctx.fillStyle = isDark ? 'rgba(148, 163, 184, 0.2)' : 'rgba(148, 163, 184, 0.3)';
        ctx.arc(x, y, pointSize, 0, Math.PI * 2);
        ctx.fill();
      }
    };
    
    // Execute drawing functions in order
    drawGrid();
    drawBackgroundPoints();
    drawConnections();
    drawNodes();
  };
  
  useEffect(() => {
    // Set canvas dimensions
    const canvas = canvasRef.current;
    if (!canvas) return;

    const displayWidth = canvas.clientWidth;
    const displayHeight = canvas.clientHeight;
    
    if (canvas.width !== displayWidth || canvas.height !== displayHeight) {
      canvas.width = displayWidth;
      canvas.height = displayHeight;
    }
    
    // Animate the network - fixed to prevent infinite updates
    const animate = () => {
      rotationRef.current = (rotationRef.current + 0.0002) % (Math.PI * 2);
      drawNetwork();
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animate();
    
    // Cleanup
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isDark]); // Only re-run when theme changes
  
  useEffect(() => {
    // Resize canvas to match its display size
    const resizeCanvas = () => {
      const canvas = canvasRef.current;
      if (!canvas) return;
      
      const displayWidth = canvas.clientWidth;
      const displayHeight = canvas.clientHeight;
      
      if (canvas.width !== displayWidth || canvas.height !== displayHeight) {
        canvas.width = displayWidth;
        canvas.height = displayHeight;
      }
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    return () => {
      window.removeEventListener('resize', resizeCanvas);
    };
  }, []);
  
  return (
    <div className="relative mx-auto w-full max-w-4xl aspect-[16/5] h-auto">
      <div className="absolute inset-0 overflow-hidden rounded-md">
        <canvas 
          ref={canvasRef} 
          className="w-full h-full"
        />
      </div>
      <div className="absolute bottom-3 right-3 flex items-center gap-1.5 backdrop-blur-sm px-2.5 py-1 rounded-full text-xs border shadow-sm 
                     dark:bg-gray-800/80 dark:border-gray-700 dark:text-gray-200
                     bg-white/80 border-gray-200 text-gray-700">
        <AlertCircle className="h-3 w-3 text-red-500" />
        <span className="font-medium">Live Incidents</span>
      </div>
    </div>
  );
};
