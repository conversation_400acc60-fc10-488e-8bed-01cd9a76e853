
import { Metric } from '@/types/incident';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';
import { format, parseISO } from 'date-fns';
import { AlertTriangle } from 'lucide-react';

interface MetricsChartProps {
  metric: Metric;
}

export const MetricsChart = ({ metric }: MetricsChartProps) => {
  const { name, data, threshold, unit } = metric;
  
  // Process data for the chart
  const chartData = data.map(point => ({
    ...point,
    formattedTime: format(parseISO(point.timestamp), 'HH:mm'),
    // For the chart tooltip
    formattedTimestamp: format(parseISO(point.timestamp), 'MMM d, yyyy HH:mm'),
  }));

  // Check if any value is above threshold
  const isAboveThreshold = threshold && data.some(point => point.value > threshold);

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{name}</CardTitle>
          {isAboveThreshold && (
            <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-alert-criticalBg text-alert-critical">
              <AlertTriangle className="mr-1 h-3 w-3" />
              Above Threshold
            </div>
          )}
        </div>
        {threshold && (
          <div className="text-sm text-muted-foreground">
            Threshold: {threshold} {unit}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#eaeaea" />
              <XAxis 
                dataKey="formattedTime" 
                padding={{ left: 10, right: 10 }}
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                domain={[0, 'auto']}
                tickFormatter={(value) => `${value}${unit === '%' ? '%' : ''}`}
              />
              <Tooltip
                contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.8)', borderRadius: '6px', border: '1px solid #eaeaea' }}
                formatter={(value: number) => [`${value}${unit === '%' ? '%' : ''}`, name]}
                labelFormatter={(time) => time}
                animationDuration={300}
              />
              {threshold && (
                <ReferenceLine
                  y={threshold}
                  stroke="#dc2626"
                  strokeDasharray="3 3"
                  label={{ 
                    value: `Threshold (${threshold}${unit === '%' ? '%' : ''})`, 
                    position: 'insideBottomRight',
                    fill: '#dc2626',
                    fontSize: 12
                  }}
                />
              )}
              <Line
                type="monotone"
                dataKey="value"
                stroke={isAboveThreshold ? '#dc2626' : '#3b82f6'}
                strokeWidth={2}
                dot={{ r: 3 }}
                activeDot={{ r: 6, strokeWidth: 1 }}
                animationDuration={1500}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

interface MetricsGridProps {
  metrics: Metric[];
}

export const MetricsGrid = ({ metrics }: MetricsGridProps) => {
  return (
    <div className="grid grid-cols-1 gap-6">
      {metrics.map((metric) => (
        <MetricsChart key={metric.id} metric={metric} />
      ))}
    </div>
  );
};
