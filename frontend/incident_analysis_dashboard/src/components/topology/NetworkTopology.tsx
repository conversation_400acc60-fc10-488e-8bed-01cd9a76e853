
import { useState, useCallback, useRef, useEffect } from 'react';
import ForceGraph2D from 'react-force-graph-2d';
import { Card, CardContent } from '@/components/ui/card';
import { Server, Database, Globe, Box, AlertTriangle } from 'lucide-react';

interface TopologyNode {
  id: string;
  name: string;
  issue?: boolean;
  group?: number;
  icon?: string;
  properties?: Record<string, any>;
  alt_names?: string[];
  entity_id?: string;
  type?: string;
  entityType?: string;
  domain?: string;
  data_source?: string;
  color?: string;
  x?: number;
  y?: number;
}

interface TopologyLink {
  id?: string;
  source: string;
  target: string;
  link_type?: string;
  properties?: Record<string, any>;
  data_source?: string;
  value?: number;
  curvature?: number;
}

interface TopologyData {
  nodes: TopologyNode[];
  links: TopologyLink[];
}

interface NetworkTopologyProps {
  data: TopologyData;
}

export const NetworkTopology = ({ data }: NetworkTopologyProps) => {
  const [graphData, setGraphData] = useState<TopologyData>({ nodes: [], links: [] });
  const graphRef = useRef<any>();
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });
  const initialized = useRef(false);
  const nodePositionsRef = useRef<Record<string, {x: number, y: number}>>({});
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const isVisible = useRef(true);
  
  // Cache SVG icons for different node types
  const nodeIcons = useRef<Record<string, HTMLImageElement>>({});
  
  // Preload icons for different node types
  useEffect(() => {
    const loadIcon = (type: string, svg: string) => {
      const blob = new Blob([svg], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      const img = new Image();
      img.src = url;
      
      return new Promise<void>((resolve) => {
        img.onload = () => {
          nodeIcons.current[type] = img;
          resolve();
        };
      });
    };
    
    // Create SVG icons for different node types
    const serverSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-server"><rect width="20" height="8" x="2" y="2" rx="2" ry="2"></rect><rect width="20" height="8" x="2" y="14" rx="2" ry="2"></rect><line x1="6" x2="6" y1="6" y2="6"></line><line x1="6" x2="6" y1="18" y2="18"></line></svg>`;
    
    const databaseSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path></svg>`;
    
    const networkSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe"><circle cx="12" cy="12" r="10"></circle><line x1="2" x2="22" y1="12" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path></svg>`;
    
    const containerSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-box"><path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"></path><path d="m3.3 7 8.7 5 8.7-5"></path><path d="M12 22V12"></path></svg>`;
    
    const serviceSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>`;
    
    const alertSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="red" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg>`;
    
    // Load all icons
    Promise.all([
      loadIcon('HOST', serverSvg),
      loadIcon('DATABASE', databaseSvg),
      loadIcon('NETWORK', networkSvg),
      loadIcon('CONTAINER', containerSvg),
      loadIcon('SERVICE', serviceSvg),
      loadIcon('ALERT', alertSvg)
    ]).then(() => {
      console.log('All icons loaded successfully');
    });
    
    return () => {
      // Clean up created object URLs
      Object.values(nodeIcons.current).forEach(img => {
        if (img.src.startsWith('blob:')) {
          URL.revokeObjectURL(img.src);
        }
      });
    };
  }, []);

  // Process data and assign colors based on groups or issues
  useEffect(() => {
    if (!data.nodes.length) return;
    
    console.log('Processing graph data with', data.nodes.length, 'nodes');
    
    // Process data and assign colors based on groups or issues
    const processedData = {
      nodes: data.nodes.map(node => {
        // Check if we have saved positions for this node
        const savedPosition = nodePositionsRef.current[node.id];
        
        return {
          ...node,
          // Apply saved positions if available
          ...(savedPosition ? savedPosition : {}),
          // Assign color based on issue status or group
          color: node.issue 
            ? '#dc2626' // Red for issues
            : node.group === 1 
              ? '#3b82f6' // Blue for group 1
              : node.group === 2 
                ? '#10b981' // Green for group 2
                : '#f59e0b'  // Default amber
        };
      }),
      links: data.links.map(link => ({
        ...link,
        // Add some curvature to links for better visualization
        curvature: link.curvature || 0.2
      }))
    };
    
    setGraphData(processedData);
    initialized.current = true;
    console.log('Graph data processed and initialized');
  }, [data]);

  // Handle visibility changes when switching tabs
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        isVisible.current = false;
      } else {
        isVisible.current = true;
        
        // When becoming visible again, re-initialize the graph if needed
        if (graphRef.current && initialized.current) {
          console.log('Document became visible, reinitializing graph');
          
          // Use a short timeout to ensure DOM is ready
          setTimeout(() => {
            if (!graphRef.current || !graphRef.current._graphData) return;
            
            // Get the current graph data from the ref
            const currentGraphData = graphRef.current._graphData;
            
            // Apply stored node positions
            if (currentGraphData && currentGraphData.nodes) {
              currentGraphData.nodes.forEach((node: TopologyNode) => {
                const savedPos = nodePositionsRef.current[node.id];
                if (savedPos) {
                  node.x = savedPos.x;
                  node.y = savedPos.y;
                }
              });
              
              // Re-render with saved positions
              graphRef.current.graphData(currentGraphData);
            }
          }, 100);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  // Save node positions when graph is updated
  const handleNodeDragEnd = useCallback((node: TopologyNode) => {
    // Update our position cache when a node is dragged
    nodePositionsRef.current[node.id] = { x: node.x || 0, y: node.y || 0 };
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const container = document.getElementById('topology-container');
      if (container) {
        setDimensions({
          width: container.clientWidth,
          height: 600
        });
      }
    };

    handleResize(); // Initial call
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fix disappearing graph issue with a timer to ensure DOM is ready
  useEffect(() => {
    // This ensures the graph is rendered after the tab content is fully visible
    const timer = setTimeout(() => {
      if (graphRef.current && graphData.nodes.length > 0) {
        console.log('Applying force graph settings');
        
        // Fix: Check for existence of d3Force methods before calling them
        if (graphRef.current.d3Force) {
          const linkForce = graphRef.current.d3Force('link');
          if (linkForce && linkForce.distance) {
            linkForce.distance(70);
          }
          
          const chargeForce = graphRef.current.d3Force('charge');
          if (chargeForce && chargeForce.strength) {
            chargeForce.strength(-120);
          }
        }
        
        // If we have saved positions, use them
        if (Object.keys(nodePositionsRef.current).length > 0) {
          const updatedNodes = graphData.nodes.map(node => ({
            ...node,
            ...(nodePositionsRef.current[node.id] || {})
          }));
          
          // Fix: Set the internal _graphData property instead of calling graphData()
          if (graphRef.current.graphData) {
            graphRef.current.graphData({
              nodes: updatedNodes,
              links: graphData.links
            });
          }
        }
      }
    }, 300);
    
    return () => clearTimeout(timer);
  }, [graphData]);

  // Handle tab changes to preserve graph state
  useEffect(() => {
    // Function to call when the tab becomes visible
    const handleTopologyTabVisible = () => {
      console.log('Topology tab visible');
      
      // Force a re-render after a short delay
      setTimeout(() => {
        if (graphRef.current && graphData.nodes.length > 0) {
          console.log('Re-rendering graph with stored positions');
          
          // Create a new reference to trigger re-render
          const newData = {
            nodes: graphData.nodes.map(node => ({
              ...node,
              ...(nodePositionsRef.current[node.id] || {})
            })),
            links: [...graphData.links]
          };
          
          // Fix: Set data directly if graphData function doesn't exist
          if (graphRef.current.graphData) {
            graphRef.current.graphData(newData);
          } else if (graphRef.current._graphData) {
            // As a backup, try to set _graphData directly
            graphRef.current._graphData = newData;
            if (graphRef.current.restart) {
              graphRef.current.restart();
            }
          }
        }
      }, 250);
    };
    
    // Check if we're on the topology tab by looking at the URL
    const isTopologyTab = window.location.pathname.includes('/topology');
    console.log('URL check:', window.location.pathname, 'Is topology tab:', isTopologyTab);
    
    if (isTopologyTab) {
      handleTopologyTabVisible();
    }
    
    // Use URL change listener to detect tab changes
    const handleUrlChange = () => {
      const newIsTopologyTab = window.location.pathname.includes('/topology');
      console.log('URL changed to:', window.location.pathname, 'Is topology tab:', newIsTopologyTab);
      
      if (newIsTopologyTab) {
        handleTopologyTabVisible();
      }
    };
    
    window.addEventListener('popstate', handleUrlChange);
    return () => window.removeEventListener('popstate', handleUrlChange);
  }, [graphData]);

  // Link label popup on hover
  const handleLinkClick = useCallback((link: TopologyLink) => {
    if (graphRef.current) {
      const { nodes } = graphData;
      const sourceNode = nodes.find(node => node.id === link.source);
      const targetNode = nodes.find(node => node.id === link.target);
      
      if (sourceNode && targetNode) {
        // Calculate center point between source and target
        const x = ((sourceNode.x || 0) + (targetNode.x || 0)) / 2;
        const y = ((sourceNode.y || 0) + (targetNode.y || 0)) / 2;
        
        // Zoom to link
        if (graphRef.current.centerAt) {
          graphRef.current.centerAt(x, y, 1000);
          if (graphRef.current.zoom) {
            graphRef.current.zoom(2, 1000);
          }
        }
      }
    }
  }, [graphData]);

  // Node label popup on hover
  const handleNodeHover = useCallback((node: TopologyNode | null) => {
    // Change cursor on hover
    document.body.style.cursor = node ? 'pointer' : 'default';
  }, []);

  // Save node positions after simulation ends
  const handleEngineStop = useCallback(() => {
    if (graphRef.current) {
      // Fix: Check if _graphData exists instead of calling graphData()
      const currentNodes = graphRef.current._graphData?.nodes || [];
      currentNodes.forEach((node: TopologyNode) => {
        if (node.x !== undefined && node.y !== undefined) {
          nodePositionsRef.current[node.id] = { x: node.x, y: node.y };
        }
      });
      console.log('Saved node positions on engine stop');
    }
  }, []);

  // Custom node canvases to display different node types with different colors and icons
  const paintNode = useCallback((node: TopologyNode, ctx: CanvasRenderingContext2D) => {
    const { x = 0, y = 0 } = node;
    const isIssue = node.issue;
    const nodeType = node.type || 'UNKNOWN';
    
    // Save canvas state
    ctx.save();
    
    // Node background circle
    const size = isIssue ? 12 : 10;
    ctx.beginPath();
    ctx.fillStyle = node.color || '#3b82f6';
    ctx.arc(x, y, size, 0, 2 * Math.PI);
    ctx.fill();
    
    // Add pulsating effect for issues
    if (isIssue) {
      // Add pulsing effect for issue nodes
      const pulseSize = size + 4 + Math.sin(Date.now() / 200) * 2;
      ctx.beginPath();
      ctx.arc(x, y, pulseSize, 0, 2 * Math.PI);
      ctx.fillStyle = 'rgba(220, 38, 38, 0.2)';
      ctx.fill();
      
      ctx.strokeStyle = '#fee2e2';
      ctx.lineWidth = 2;
      ctx.stroke();
    }
    
    // Draw icon based on node type
    const iconImg = isIssue ? 
      nodeIcons.current['ALERT'] : 
      nodeIcons.current[nodeType] || nodeIcons.current['SERVICE'];
    
    if (iconImg) {
      const iconSize = size * 1.2;
      ctx.drawImage(
        iconImg,
        x - iconSize/2,
        y - iconSize/2,
        iconSize,
        iconSize
      );
    }
    
    // Node label background for better readability
    const name = node.name || 'Unknown';
    const nodeType2 = node.type || 'Unknown';
    ctx.font = '6px Arial';
    const textWidth = ctx.measureText(name).width;
    const typeWidth = ctx.measureText(nodeType2).width;
    const maxWidth = Math.max(textWidth, typeWidth);
    const textHeight = 6;
    const padding = 3;
    
    // Draw label background
    ctx.fillStyle = 'rgba(255, 255, 255, 0.85)';
    ctx.fillRect(
      x - maxWidth/2 - padding, 
      y + size + 2, 
      maxWidth + padding * 2, 
      textHeight * 2 + padding * 2
    );
    
    // Draw name label
    ctx.fillStyle = '#000';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(name, x, y + size + textHeight);
    
    // Draw type label
    ctx.font = '5px Arial';
    ctx.fillStyle = '#666';
    ctx.fillText(`(${nodeType2})`, x, y + size + textHeight * 2);
    
    // Restore canvas state
    ctx.restore();
  }, []);
  
  return (
    <Card className="w-full">
      <CardContent className="p-4">
        <div id="topology-container" className="w-full dot-pattern">
          {initialized.current && graphData.nodes.length > 0 && (
            <ForceGraph2D
              ref={graphRef}
              graphData={graphData}
              nodeLabel={node => {
                const props = node.properties ? 
                  Object.entries(node.properties)
                    .filter(([_, value]) => value !== undefined)
                    .map(([key, value]) => {
                      if (Array.isArray(value)) {
                        return `${key}: ${value.join(', ')}`;
                      }
                      return `${key}: ${value}`;
                    })
                    .slice(0, 5)
                    .join('\n') : '';
                
                return `${node.name} (${node.type || 'Unknown'})\n${props}`;
              }}
              linkLabel={link => `${link.link_type || 'Connected to'}`}
              nodeCanvasObject={paintNode}
              linkColor={() => 'rgba(120, 120, 120, 0.8)'}
              linkDirectionalParticles={2}
              linkDirectionalParticleWidth={1.4}
              linkCurvature={link => (link as TopologyLink).curvature || 0.2}
              linkWidth={1.5}
              width={dimensions.width}
              height={dimensions.height}
              onLinkClick={handleLinkClick}
              onNodeHover={handleNodeHover}
              onNodeDragEnd={handleNodeDragEnd}
              onEngineStop={handleEngineStop}
              cooldownTime={3000}
              nodeRelSize={8}
              d3AlphaDecay={0.02} // Slower decay for more stable layout
              d3VelocityDecay={0.3} // Adjust for smoother movement
              warmupTicks={100} // More initial ticks to reach a stable state
              cooldownTicks={100} // More cooldown ticks for stability
            />
          )}
        </div>
      </CardContent>
    </Card>
  );
};
