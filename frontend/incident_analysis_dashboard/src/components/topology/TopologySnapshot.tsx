
import { useState, useEffect, useRef } from 'react';
import ForceGraph2D from 'react-force-graph-2d';
import { Card, CardContent } from '@/components/ui/card';
import { TopologyData } from '@/types/topology';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';
import { Link } from 'react-router-dom';

interface TopologySnapshotProps {
  data: TopologyData;
  incidentId: string;
  height?: number;
}

export const TopologySnapshot = ({ data, incidentId, height = 300 }: TopologySnapshotProps) => {
  const [graphData, setGraphData] = useState<TopologyData>({ nodes: [], links: [] });
  const graphRef = useRef<any>();
  const [dimensions, setDimensions] = useState({ width: 0, height });
  const containerRef = useRef<HTMLDivElement>(null);
  const nodeIcons = useRef<Record<string, HTMLImageElement>>({});
  
  // Preload icons
  useEffect(() => {
    const loadIcon = (type: string, svg: string) => {
      const blob = new Blob([svg], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      const img = new Image();
      img.src = url;
      
      return new Promise<void>((resolve) => {
        img.onload = () => {
          nodeIcons.current[type] = img;
          resolve();
        };
      });
    };
    
    // Create SVG icons
    const serverSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="8" x="2" y="2" rx="2" ry="2"></rect><rect width="20" height="8" x="2" y="14" rx="2" ry="2"></rect><line x1="6" x2="6" y1="6" y2="6"></line><line x1="6" x2="6" y1="18" y2="18"></line></svg>`;
    
    const databaseSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path></svg>`;
    
    const serviceSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>`;
    
    const containerSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"></path><path d="m3.3 7 8.7 5 8.7-5"></path><path d="M12 22V12"></path></svg>`;
    
    const podSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 5 5.5 7.5"></path><path d="M13 5l5.5 2.5"></path><path d="M8 10h8"></path><path d="M16 10v4.5s0 2-2 2-2-2-2-2V10"></path><path d="M8 10v4.5s0 2 2 2 2-2 2-2"></path><path d="M3 5.5C3 4.1 7 3 12 3s9 1.1 9 2.5S17 8 12 8 3 6.9 3 5.5z"></path><path d="M12 16a15.5 15.5 0 0 1-9-3v5c0 1.5 4 3 9 3s9-1.5 9-3v-5a15.5 15.5 0 0 1-9 3z"></path></svg>`;
    
    const nodeSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect><rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect><line x1="6" y1="6" x2="6.01" y2="6"></line><line x1="6" y1="18" x2="6.01" y2="18"></line></svg>`;
    
    const alertSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="red" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg>`;
    
    // Load all icons
    Promise.all([
      loadIcon('HOST', serverSvg),
      loadIcon('SERVER', serverSvg),
      loadIcon('DATABASE', databaseSvg),
      loadIcon('SERVICE', serviceSvg),
      loadIcon('CONTAINER', containerSvg),
      loadIcon('POD', podSvg),
      loadIcon('NODE', nodeSvg),
      loadIcon('ALERT', alertSvg)
    ]).then(() => {
      console.log('Snapshot icons loaded');
    });
    
    return () => {
      Object.values(nodeIcons.current).forEach(img => {
        if (img.src.startsWith('blob:')) {
          URL.revokeObjectURL(img.src);
        }
      });
    };
  }, []);

  // Handle container size
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        setDimensions({
          width: containerRef.current.clientWidth, 
          height
        });
      }
    };
    
    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    
    return () => window.removeEventListener('resize', updateDimensions);
  }, [height]);

  // Process data and set up graph
  useEffect(() => {
    if (!data?.nodes?.length) return;
    
    const processedData = {
      nodes: data.nodes.map(node => ({
        ...node,
        color: node.issue 
          ? '#dc2626' // Red for issues
          : node.group === 1 
            ? '#3b82f6' // Blue for group 1
            : node.group === 2 
              ? '#10b981' // Green for group 2
              : '#f59e0b'  // Default amber
      })),
      links: data.links.map(link => ({
        ...link,
        curvature: link.curvature || 0.2
      }))
    };
    
    setGraphData(processedData);
  }, [data]);

  // Custom node paint function
  const paintNode = (node: any, ctx: CanvasRenderingContext2D) => {
    const { x = 0, y = 0 } = node;
    const isIssue = node.issue;
    const nodeType = node.type || 'SERVICE';
    
    ctx.save();
    
    // Node background circle
    const size = isIssue ? 10 : 8;
    ctx.beginPath();
    ctx.fillStyle = node.color || '#3b82f6';
    ctx.arc(x, y, size, 0, 2 * Math.PI);
    ctx.fill();
    
    // Add pulsating effect for issues
    if (isIssue) {
      const pulseSize = size + 3 + Math.sin(Date.now() / 200) * 2;
      ctx.beginPath();
      ctx.arc(x, y, pulseSize, 0, 2 * Math.PI);
      ctx.fillStyle = 'rgba(220, 38, 38, 0.2)';
      ctx.fill();
      
      ctx.strokeStyle = '#fee2e2';
      ctx.lineWidth = 2;
      ctx.stroke();
    }
    
    // Draw icon based on node type
    const iconImg = isIssue ? 
      nodeIcons.current['ALERT'] : 
      nodeIcons.current[nodeType] || nodeIcons.current['SERVICE'];
    
    if (iconImg) {
      const iconSize = size * 1.2;
      ctx.drawImage(
        iconImg,
        x - iconSize/2,
        y - iconSize/2,
        iconSize,
        iconSize
      );
    }
    
    // Simplified labels for snapshot view
    ctx.font = '6px Arial';
    ctx.fillStyle = '#000';
    ctx.textAlign = 'center';
    ctx.fillText(node.name, x, y + size + 6);
    
    ctx.restore();
  };
  
  return (
    <Card className="w-full overflow-hidden">
      <CardContent className="p-4 pb-0 flex flex-col">
        <div ref={containerRef} className="flex-1 dot-pattern mb-4">
          {graphData.nodes.length > 0 && dimensions.width > 0 && (
            <ForceGraph2D
              ref={graphRef}
              graphData={graphData}
              width={dimensions.width}
              height={dimensions.height}
              nodeCanvasObject={paintNode}
              linkColor={() => 'rgba(120, 120, 120, 0.7)'}
              linkDirectionalParticles={1}
              linkDirectionalParticleWidth={1}
              linkWidth={1}
              cooldownTime={1000}
              nodeRelSize={6}
              onEngineStop={() => {
                // Center the graph once it stabilizes
                if (graphRef.current?.zoomToFit) {
                  graphRef.current.zoomToFit(400, 40);
                }
              }}
            />
          )}
        </div>
        <div className="border-t pt-2 pb-2 flex justify-center">
          <Link to={`/incident/${incidentId}/topology`}>
            <Button variant="ghost" size="sm" className="text-xs">
              <ExternalLink className="h-3.5 w-3.5 mr-1" />
              View full topology
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
};
