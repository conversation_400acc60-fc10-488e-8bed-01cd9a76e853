
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { memo, useEffect } from 'react';

interface TabItem {
  label: string;
  value: string;
  href: string;
}

interface TabNavigationProps {
  tabs: TabItem[];
  className?: string;
}

// Using memo to prevent unnecessary re-renders
export const TabNavigation = memo(({ tabs, className }: TabNavigationProps) => {
  const location = useLocation();
  const currentPath = location.pathname;

  // Add a useEffect to potentially help with topology tab initialization
  useEffect(() => {
    const isTopologyTab = currentPath.includes('/topology');
    if (isTopologyTab) {
      // Force a reflow to help with topology graph initialization
      const container = document.getElementById('topology-container');
      if (container) {
        // This is a hack to force a reflow
        void container.offsetHeight;
      }
    }
  }, [currentPath]);

  return (
    <div className={cn("border-b", className)}>
      <nav className="flex overflow-x-auto">
        {tabs.map((tab) => {
          const isActive = currentPath.endsWith(tab.href);
          return (
            <Link
              key={tab.value}
              to={tab.href}
              className={cn(
                "tab-item py-3 px-4 text-sm font-medium whitespace-nowrap",
                isActive 
                  ? "text-primary border-b-2 border-primary" 
                  : "text-muted-foreground hover:text-foreground"
              )}
            >
              {tab.label}
            </Link>
          );
        })}
      </nav>
    </div>
  );
});

TabNavigation.displayName = 'TabNavigation';
