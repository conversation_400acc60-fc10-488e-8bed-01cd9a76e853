
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Search, Bell, Sun, Moon, Activity } from 'lucide-react';
import { useTheme } from '@/hooks/use-theme';

export const Header = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <header className="border-b sticky top-0 z-10 bg-background/80 backdrop-blur-sm">
      <div className="container flex h-16 items-center justify-between py-4">
        <div className="flex items-center gap-3">
          <Link to="/" className="flex items-center gap-2 text-xl font-bold bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent">
            <div className="relative flex h-9 w-9 items-center justify-center rounded-full bg-gradient-to-r from-red-500 to-purple-600 text-white shadow-md">
              <Activity className="h-5 w-5" />
            </div>
            <span className="hidden md:inline">Ivanti Incident Management</span>
          </Link>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative rounded-md hidden md:block">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <input
              type="search"
              placeholder="Search incidents..."
              className="w-[250px] rounded-md border border-input bg-background py-2 pl-8 pr-3 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
            />
          </div>
          
          <Button variant="ghost" size="icon" className="rounded-full relative">
            <Bell className="h-[1.2rem] w-[1.2rem]" />
            <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500 ring-2 ring-background"></span>
          </Button>
          
          <Button variant="ghost" size="icon" className="rounded-full" onClick={toggleTheme}>
            {theme === 'light' ? (
              <Moon className="h-[1.2rem] w-[1.2rem]" />
            ) : (
              <Sun className="h-[1.2rem] w-[1.2rem]" />
            )}
          </Button>
        </div>
      </div>
    </header>
  );
};
