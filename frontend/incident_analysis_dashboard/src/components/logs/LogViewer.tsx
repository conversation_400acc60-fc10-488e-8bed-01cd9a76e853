import React, { useState, useEffect, useMemo } from 'react';
import { Search, Download, Filter, ArrowUpDown } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { LogEntry } from '@/types/incident';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { format } from 'date-fns';

interface LogViewerProps {
  logs: LogEntry[];
}

export const LogViewer: React.FC<LogViewerProps> = ({ logs }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevels, setSelectedLevels] = useState<string[]>([]);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  
  const logLevels = useMemo(() => {
    const levels = new Set<string>();
    logs.forEach(log => levels.add(log.level));
    return Array.from(levels);
  }, [logs]);

  const filteredLogs = useMemo(() => {
    return logs
      .filter(log => {
        // Apply text search
        const matchesSearch = 
          searchTerm === '' || 
          log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
          log.service.toLowerCase().includes(searchTerm.toLowerCase());
        
        // Apply level filter
        const matchesLevel = 
          selectedLevels.length === 0 || 
          selectedLevels.includes(log.level);
        
        return matchesSearch && matchesLevel;
      })
      .sort((a, b) => {
        const dateA = new Date(a.timestamp).getTime();
        const dateB = new Date(b.timestamp).getTime();
        return sortDirection === 'desc' ? dateB - dateA : dateA - dateB;
      });
  }, [logs, searchTerm, selectedLevels, sortDirection]);

  const toggleLevelFilter = (level: string) => {
    setSelectedLevels(prev => 
      prev.includes(level) 
        ? prev.filter(l => l !== level) 
        : [...prev, level]
    );
  };

  const toggleSortDirection = () => {
    setSortDirection(prev => prev === 'desc' ? 'asc' : 'desc');
  };

  const handleExportLogs = () => {
    const exportData = filteredLogs.map(log => ({
      timestamp: log.timestamp,
      level: log.level,
      service: log.service,
      message: log.message
    }));
    
    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `logs-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'error': return 'bg-red-500';
      case 'warning': return 'bg-yellow-500';
      case 'info': return 'bg-blue-500';
      case 'debug': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
          <CardTitle>System Logs</CardTitle>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleExportLogs}
              className="text-xs"
            >
              <Download className="h-3.5 w-3.5 mr-1" />
              Export Logs
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search logs..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={toggleSortDirection}
                className="text-xs"
              >
                <ArrowUpDown className="h-3.5 w-3.5 mr-1" />
                {sortDirection === 'desc' ? 'Newest First' : 'Oldest First'}
              </Button>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2 items-center">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm mr-2">Filter:</span>
            {logLevels.map(level => (
              <Badge
                key={level}
                variant={selectedLevels.includes(level) ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => toggleLevelFilter(level)}
              >
                {level}
              </Badge>
            ))}
          </div>
          
          <Separator />
          
          <div className="overflow-x-auto">
            <div className="space-y-2">
              {filteredLogs.length > 0 ? (
                filteredLogs.map((log, index) => (
                  <div key={index} className="flex flex-col md:flex-row bg-card border rounded-md p-2 text-sm">
                    <div className="flex-none md:w-48 text-muted-foreground">
                      {format(new Date(log.timestamp), 'yyyy-MM-dd HH:mm:ss')}
                    </div>
                    <div className="flex-none md:w-20">
                      <div className={`inline-block px-2 py-0.5 rounded-full text-white text-xs ${getLevelColor(log.level)}`}>
                        {log.level}
                      </div>
                    </div>
                    <div className="flex-none md:w-32 font-medium">
                      {log.service}
                    </div>
                    <div className="flex-1 mt-1 md:mt-0 break-words">
                      {log.message}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No logs match your current filters
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
