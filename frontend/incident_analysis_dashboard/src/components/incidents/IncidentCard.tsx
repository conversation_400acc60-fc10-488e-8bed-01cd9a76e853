
import { Incident } from '@/types/incident';
import { StatusBadge, SeverityBadge, TeamBadge } from './IncidentBadge';
import { formatDistanceToNow } from 'date-fns';
import { AlertCircle, ExternalLink, Clock } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Link } from 'react-router-dom';

interface IncidentCardProps {
  incident: Incident;
}

export const IncidentCard = ({ incident }: IncidentCardProps) => {
  const {
    id,
    title,
    description,
    status,
    severity,
    createdAt,
    teams,
    source
  } = incident;

  // Format the date to relative time (e.g., "2 months ago")
  const formattedDate = formatDistanceToNow(new Date(createdAt), { addSuffix: true });

  // Get appropriate border color based on severity
  const getBorderColor = () => {
    switch (severity) {
      case 'critical':
        return 'border-l-red-500';
      case 'warning':
        return 'border-l-amber-500';
      case 'info':
        return 'border-l-blue-500';
      case 'low':
        return 'border-l-green-500';
      default:
        return 'border-l-gray-500';
    }
  };

  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-md animate-fade-in border-l-4 hover:translate-x-1 hover:-translate-y-1 group" 
      style={{ borderLeftColor: severity === 'critical' ? 'rgb(239, 68, 68)' : 
                               severity === 'warning' ? 'rgb(245, 158, 11)' :
                               severity === 'info' ? 'rgb(59, 130, 246)' : 'rgb(34, 197, 94)' }}>
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="mt-1 p-1 rounded-full" 
              style={{ 
                background: severity === 'critical' ? 'rgba(239, 68, 68, 0.1)' : 
                            severity === 'warning' ? 'rgba(245, 158, 11, 0.1)' :
                            severity === 'info' ? 'rgba(59, 130, 246, 0.1)' : 'rgba(34, 197, 94, 0.1)',
                color: severity === 'critical' ? 'rgb(239, 68, 68)' : 
                       severity === 'warning' ? 'rgb(245, 158, 11)' :
                       severity === 'info' ? 'rgb(59, 130, 246)' : 'rgb(34, 197, 94)'
              }}>
              <AlertCircle className="h-5 w-5" />
            </div>
            <div>
              <Link to={`/incident/${id}`} className="group relative">
                <h3 className="font-medium text-lg group-hover:text-primary transition-colors">
                  {title}
                  <span className="absolute left-0 bottom-0 w-0 h-0.5 bg-gradient-to-r from-red-500 to-purple-600 transition-all duration-300 group-hover:w-full"></span>
                </h3>
              </Link>
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">{description}</p>
            </div>
          </div>
          <div className="text-right flex items-center gap-1 text-sm text-muted-foreground">
            <Clock className="h-3.5 w-3.5" />
            {formattedDate}
          </div>
        </div>

        <div className="mt-4 flex flex-wrap gap-2 items-center">
          <StatusBadge status={status} />
          <SeverityBadge severity={severity} />
          {teams.map((team) => (
            <TeamBadge key={team.id} name={team.name} />
          ))}
          <div className="ml-auto flex items-center text-sm text-muted-foreground">
            <span>Source: {source.name}</span>
            <button className="ml-2 inline-flex items-center text-primary hover:underline">
              <span>View in {source.name}</span>
              <ExternalLink className="ml-1 h-3 w-3" />
            </button>
          </div>
        </div>
      </div>
    </Card>
  );
};
