
import { Card, CardContent } from '@/components/ui/card';
import { 
  AlertTriangle, 
  Activity, 
  CheckCircle, 
  AlertCircle 
} from 'lucide-react';

interface StatsProps {
  total: number;
  active: number;
  resolved: number;
  critical: number;
}

export const IncidentStats = ({ total, active, resolved, critical }: StatsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <StatCard
        title="Total Incidents"
        value={total}
        icon={<Activity className="h-5 w-5" />}
        color="#8E9196"
        bgColor="bg-gray-50"
        borderColor="border-gray-200"
      />
      <StatCard
        title="Active"
        value={active}
        icon={<AlertCircle className="h-5 w-5" />}
        color="#F97316"
        bgColor="bg-gray-50"
        borderColor="border-gray-200"
        highlight={active > 0}
      />
      <StatCard
        title="Resolved"
        value={resolved}
        icon={<CheckCircle className="h-5 w-5" />}
        color="#8A898C"
        bgColor="bg-gray-50"
        borderColor="border-gray-200"
      />
      <StatCard
        title="Critical"
        value={critical}
        icon={<AlertTriangle className="h-5 w-5" />}
        color="#dc2626"
        bgColor="bg-gray-50"
        borderColor="border-gray-200"
        highlight={critical > 0}
      />
    </div>
  );
};

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  borderColor: string;
  highlight?: boolean;
}

const StatCard = ({ title, value, icon, color, bgColor, borderColor, highlight = false }: StatCardProps) => {
  return (
    <Card className={`overflow-hidden border ${borderColor} shadow-sm ${bgColor} ${highlight ? 'ring-1 ring-offset-1' : ''}`}>
      <CardContent className="p-6 relative">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium text-gray-600">{title}</h3>
          <div className={`flex items-center justify-center h-10 w-10 rounded-full`} 
               style={{ 
                 backgroundColor: highlight ? `${color}10` : '#F1F0FB', 
                 color: color
               }}>
            {icon}
          </div>
        </div>
        <p className="text-3xl font-bold" style={{ color: highlight ? color : '#403E43' }}>{value}</p>
        
        {/* Animated pulse dot */}
        <div className="absolute bottom-2 right-2 flex items-center gap-1.5 opacity-70">
          <span className="relative flex h-2 w-2">
            <span className={`animate-ping absolute inline-flex h-full w-full rounded-full opacity-75`} 
                  style={{ backgroundColor: color }}></span>
            <span className={`relative inline-flex rounded-full h-2 w-2`}
                  style={{ backgroundColor: color }}></span>
          </span>
          <span className="text-xs text-gray-500">Live</span>
        </div>
      </CardContent>
    </Card>
  );
};
