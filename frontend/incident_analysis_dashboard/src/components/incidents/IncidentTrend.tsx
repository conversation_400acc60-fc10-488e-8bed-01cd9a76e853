
import { useRef, useEffect } from 'react';
import * as Plot from '@observablehq/plot';
import * as d3 from 'd3';
import { useTheme } from '@/hooks/use-theme';

interface DailyIncident {
  date: Date;
  total: number;
  critical: number;
  resolved: number;
}

interface IncidentTrendProps {
  data: DailyIncident[];
}

export const IncidentTrend = ({ data }: IncidentTrendProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  useEffect(() => {
    if (!containerRef.current || !data.length) return;
    
    // Clear previous plot
    while (containerRef.current.firstChild) {
      containerRef.current.firstChild.remove();
    }
    
    // Theme colors
    const colors = {
      background: isDark ? '#0f172a' : '#ffffff',
      text: isDark ? '#e2e8f0' : '#334155',
      grid: isDark ? '#1e293b' : '#e2e8f0',
      axis: isDark ? '#475569' : '#94a3b8',
      totalLine: isDark ? '#38bdf8' : '#0284c7',
      criticalLine: isDark ? '#f87171' : '#ef4444',
      resolvedLine: isDark ? '#4ade80' : '#22c55e',
      tooltip: isDark ? '#0f172a' : '#ffffff',
      tooltipBorder: isDark ? '#334155' : '#e2e8f0',
      areaOpacity: isDark ? 0.3 : 0.2
    };
    
    const dateExtent = d3.extent(data, d => d.date) as [Date, Date];
    const maxValue = d3.max(data, d => Math.max(d.total, d.critical, d.resolved)) || 0;
    
    // Get 7-day moving averages
    const movingAverage = (array: number[], windowSize: number) => {
      return array.map((_, i) => {
        const start = Math.max(0, i - windowSize + 1);
        const windowValues = array.slice(start, i + 1);
        return d3.mean(windowValues) || 0;
      });
    };
    
    const totalMovingAvg = movingAverage(data.map(d => d.total), 7);
    const criticalMovingAvg = movingAverage(data.map(d => d.critical), 7);
    const resolvedMovingAvg = movingAverage(data.map(d => d.resolved), 7);
    
    const dataWithAvg = data.map((d, i) => ({
      ...d,
      totalAvg: totalMovingAvg[i],
      criticalAvg: criticalMovingAvg[i],
      resolvedAvg: resolvedMovingAvg[i]
    }));
    
    const plot = Plot.plot({
      width: containerRef.current.clientWidth,
      height: 340,
      style: {
        background: colors.background,
        color: colors.text,
        fontSize: '12px',
        fontFamily: 'Inter, system-ui, sans-serif',
      },
      x: {
        type: "time",
        tickFormat: "%b %d",
        label: null,
        labelAnchor: "center",
        grid: true,
        line: true,
        domain: dateExtent,
      },
      y: {
        grid: true,
        label: "Incidents",
        labelAnchor: "center",
        nice: true,
        zero: true,
      },
      marks: [
        Plot.ruleY([0], { stroke: colors.axis }),
        
        // Area charts
        Plot.areaY(
          dataWithAvg,
          {
            x: "date",
            y: "total",
            fillOpacity: colors.areaOpacity,
            fill: colors.totalLine,
            stroke: null
          }
        ),
        
        Plot.areaY(
          dataWithAvg,
          {
            x: "date",
            y: "critical",
            fillOpacity: colors.areaOpacity,
            fill: colors.criticalLine,
            stroke: null
          }
        ),
        
        Plot.areaY(
          dataWithAvg,
          {
            x: "date",
            y: "resolved",
            fillOpacity: colors.areaOpacity,
            fill: colors.resolvedLine,
            stroke: null
          }
        ),
        
        // Moving average lines
        Plot.line(
          dataWithAvg, 
          {
            x: "date",
            y: "totalAvg",
            stroke: colors.totalLine,
            strokeWidth: 2.5,
            tip: true,
            z: "Total (7d avg)"
          }
        ),
        
        Plot.line(
          dataWithAvg, 
          {
            x: "date",
            y: "criticalAvg",
            stroke: colors.criticalLine,
            strokeWidth: 2.5,
            tip: true,
            z: "Critical (7d avg)"
          }
        ),
        
        Plot.line(
          dataWithAvg, 
          {
            x: "date",
            y: "resolvedAvg",
            stroke: colors.resolvedLine,
            strokeWidth: 2.5,
            tip: true,
            z: "Resolved (7d avg)"
          }
        ),
        
        // Daily data points as dots instead of bars
        Plot.dot(data, {
          x: "date",
          y: "total",
          fill: colors.totalLine,
          fillOpacity: 0.4,
          r: 3,
          tip: "xy",
          title: d => `Total: ${d.total} on ${d3.timeFormat("%B %d")(d.date)}`,
        }),
        
        Plot.dot(data, {
          x: "date",
          y: "critical",
          fill: colors.criticalLine,
          fillOpacity: 0.4,
          r: 3,
          tip: "xy",
          title: d => `Critical: ${d.critical} on ${d3.timeFormat("%B %d")(d.date)}`,
        }),
        
        Plot.dot(data, {
          x: "date",
          y: "resolved",
          fill: colors.resolvedLine,
          fillOpacity: 0.4,
          r: 3,
          tip: "xy",
          title: d => `Resolved: ${d.resolved} on ${d3.timeFormat("%B %d")(d.date)}`,
        }),
        
        // Add crosshair
        Plot.crosshair(data, {
          x: "date",
          y: "total", 
          color: isDark ? "rgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.1)",
        }),
        
        // Add critical incident markers
        Plot.text(data.filter((d, i) => i % 7 === 0 && d.critical > d.resolved), {
          x: "date",
          y: d => d.critical,
          text: "⚠️",
          dy: -10,
          fontSize: 16
        })
      ],
      
      // Configure legend and margins
      marginTop: 25,
      marginRight: 40,
      marginBottom: 30,
      marginLeft: 50,
    });
    
    containerRef.current.appendChild(plot);
    
    // Cleanup
    return () => {
      if (plot && containerRef.current?.contains(plot)) {
        plot.remove();
      }
    };
  }, [data, isDark]);
  
  return (
    <div className={`w-full rounded-lg overflow-hidden ${
      isDark 
        ? 'bg-gray-900 border border-gray-800' 
        : 'bg-white border border-gray-200'
    }`}>
      <div className="p-4 pb-2">
        <h3 className={`text-lg font-semibold ${isDark ? 'text-gray-100' : 'text-gray-800'}`}>
          Incident Trends - Last 30 Days
        </h3>
        <div className="flex flex-col md:flex-row justify-between">
          <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Daily incidents with 7-day moving average
          </p>
          
          <div className="flex items-center gap-4 mt-2 md:mt-0">
            <div className="flex items-center gap-1">
              <span className="inline-block w-3 h-3 rounded-full bg-blue-500"></span>
              <span className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>Total</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="inline-block w-3 h-3 rounded-full bg-red-500"></span>
              <span className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>Critical</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="inline-block w-3 h-3 rounded-full bg-green-500"></span>
              <span className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>Resolved</span>
            </div>
          </div>
        </div>
      </div>
      <div 
        ref={containerRef} 
        className="w-full overflow-hidden"
      />
    </div>
  );
};
