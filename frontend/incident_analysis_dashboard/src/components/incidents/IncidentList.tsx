
import { useState } from 'react';
import { IncidentCard } from './IncidentCard';
import { Incident } from '@/types/incident';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Filter, ArrowUpDown } from 'lucide-react';

interface IncidentListProps {
  incidents: Incident[];
}

export const IncidentList = ({ incidents }: IncidentListProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  
  // Filter incidents based on search term
  const filteredIncidents = incidents.filter(incident => 
    incident.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    incident.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search incidents..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="icon" className="h-10 w-10">
            <Filter className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" className="h-10 w-10">
            <ArrowUpDown className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {filteredIncidents.length > 0 ? (
          filteredIncidents.map(incident => (
            <IncidentCard key={incident.id} incident={incident} />
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No incidents found</p>
          </div>
        )}
      </div>
    </div>
  );
};
