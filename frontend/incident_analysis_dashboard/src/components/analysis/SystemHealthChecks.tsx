
import { HealthCheck } from '@/types/incident';
import { AlertCircle, CheckCircle, ChevronRight } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface SystemHealthChecksProps {
  checks: HealthCheck[];
}

export const SystemHealthChecks = ({ checks }: SystemHealthChecksProps) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center">
          <AlertCircle className="mr-2 h-5 w-5" />
          System Health Checks
          <span className="ml-auto text-sm text-muted-foreground font-normal">
            {checks.length} Checks
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {checks.map((check) => (
            <HealthCheckItem key={check.id} check={check} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

interface HealthCheckItemProps {
  check: HealthCheck;
}

const HealthCheckItem = ({ check }: HealthCheckItemProps) => {
  const { type, status, message } = check;
  
  const isIssue = status === 'issues_found';

  return (
    <div className="flex items-start justify-between py-2 transition-colors cursor-pointer hover:bg-muted/50 px-2 rounded-md">
      <div className="flex items-start">
        {isIssue ? (
          <AlertCircle className="mr-2 mt-0.5 h-5 w-5 text-alert-critical" />
        ) : (
          <CheckCircle className="mr-2 mt-0.5 h-5 w-5 text-alert-success" />
        )}
        <div>
          <div className="flex items-center">
            <p className="font-medium">
              {type}
            </p>
            {isIssue && (
              <span className="ml-2 text-xs font-medium text-alert-critical">
                • ISSUES_FOUND
              </span>
            )}
            {!isIssue && (
              <span className="ml-2 text-xs font-medium text-alert-success">
                • ALL GOOD
              </span>
            )}
          </div>
          <p className="text-sm text-muted-foreground">
            {message}
          </p>
        </div>
      </div>
      <ChevronRight className="h-5 w-5 text-muted-foreground" />
    </div>
  );
};
