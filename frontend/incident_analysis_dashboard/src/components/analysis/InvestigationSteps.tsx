
import { InvestigationStep } from '@/types/incident';
import { Search, ChevronDown } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useState } from 'react';
import { formatDistanceToNow } from 'date-fns';

interface InvestigationStepsProps {
  steps: InvestigationStep[];
}

export const InvestigationSteps = ({ steps }: InvestigationStepsProps) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center">
          <Search className="mr-2 h-5 w-5" />
          Investigation Steps
          <span className="ml-auto text-sm text-muted-foreground font-normal">
            {steps.length} Steps
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {steps.map((step) => (
            <InvestigationStepItem key={step.id} step={step} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

interface InvestigationStepItemProps {
  step: InvestigationStep;
}

const InvestigationStepItem = ({ step }: InvestigationStepItemProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { id, title, description, timestamp } = step;
  
  const formattedDate = formatDistanceToNow(new Date(timestamp), { addSuffix: true });

  return (
    <div className="border rounded-md overflow-hidden">
      <div 
        className="flex items-center justify-between p-3 cursor-pointer bg-secondary hover:bg-secondary/70 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-background text-foreground text-sm font-medium">
            {id}
          </div>
          <h3 className="font-medium">{title}</h3>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-muted-foreground">{formattedDate}</span>
          <ChevronDown className={`h-5 w-5 transition-transform ${isExpanded ? 'transform rotate-180' : ''}`} />
        </div>
      </div>
      {isExpanded && (
        <div className="p-3 border-t">
          <p className="text-sm">{description}</p>
        </div>
      )}
    </div>
  );
};
