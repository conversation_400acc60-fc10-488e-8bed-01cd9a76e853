
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://wrlhncdvzswfqavovugs.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndybGhuY2R2enN3ZnFhdm92dWdzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDA1NTgyNzAsImV4cCI6MjA1NjEzNDI3MH0.qzONBk0WiTwb50JbfH4UTkc7g5Sm_9GT2z017CTZTDw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
