export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      health_checks: {
        Row: {
          category: string
          description: string | null
          details: Json | null
          id: string
          incident_id: string | null
          metrics: Json | null
          status: string
        }
        Insert: {
          category: string
          description?: string | null
          details?: Json | null
          id?: string
          incident_id?: string | null
          metrics?: Json | null
          status: string
        }
        Update: {
          category?: string
          description?: string | null
          details?: Json | null
          id?: string
          incident_id?: string | null
          metrics?: Json | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "health_checks_incident_id_fkey"
            columns: ["incident_id"]
            isOneToOne: false
            referencedRelation: "incidents"
            referencedColumns: ["id"]
          },
        ]
      }
      incident_actions: {
        Row: {
          description: string | null
          id: string
          incident_id: string | null
          steps: Json | null
          title: string
        }
        Insert: {
          description?: string | null
          id?: string
          incident_id?: string | null
          steps?: Json | null
          title: string
        }
        Update: {
          description?: string | null
          id?: string
          incident_id?: string | null
          steps?: Json | null
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "incident_actions_incident_id_fkey"
            columns: ["incident_id"]
            isOneToOne: false
            referencedRelation: "incidents"
            referencedColumns: ["id"]
          },
        ]
      }
      incident_logs: {
        Row: {
          description: string | null
          id: string
          incident_id: string | null
          level: string
          message: string | null
          service: string | null
          timestamp: string
          title: string | null
          type: string | null
        }
        Insert: {
          description?: string | null
          id?: string
          incident_id?: string | null
          level: string
          message?: string | null
          service?: string | null
          timestamp: string
          title?: string | null
          type?: string | null
        }
        Update: {
          description?: string | null
          id?: string
          incident_id?: string | null
          level?: string
          message?: string | null
          service?: string | null
          timestamp?: string
          title?: string | null
          type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "incident_logs_incident_id_fkey"
            columns: ["incident_id"]
            isOneToOne: false
            referencedRelation: "incidents"
            referencedColumns: ["id"]
          },
        ]
      }
      incidents: {
        Row: {
          affected_services: Json | null
          created_at: string
          created_timestamp: string
          customer: string | null
          description: string | null
          id: string
          impact: string | null
          preventive_actions: string | null
          root_cause: string | null
          severity: string
          sources: Json | null
          status: string
          teams: Json | null
          title: string
          updated_at: string
        }
        Insert: {
          affected_services?: Json | null
          created_at: string
          created_timestamp?: string
          customer?: string | null
          description?: string | null
          id: string
          impact?: string | null
          preventive_actions?: string | null
          root_cause?: string | null
          severity: string
          sources?: Json | null
          status: string
          teams?: Json | null
          title: string
          updated_at: string
        }
        Update: {
          affected_services?: Json | null
          created_at?: string
          created_timestamp?: string
          customer?: string | null
          description?: string | null
          id?: string
          impact?: string | null
          preventive_actions?: string | null
          root_cause?: string | null
          severity?: string
          sources?: Json | null
          status?: string
          teams?: Json | null
          title?: string
          updated_at?: string
        }
        Relationships: []
      }
      investigation_steps: {
        Row: {
          description: string | null
          id: string
          incident_id: string | null
          investigation_details: Json | null
          step_number: number
          timestamp: string
          title: string
        }
        Insert: {
          description?: string | null
          id?: string
          incident_id?: string | null
          investigation_details?: Json | null
          step_number: number
          timestamp: string
          title: string
        }
        Update: {
          description?: string | null
          id?: string
          incident_id?: string | null
          investigation_details?: Json | null
          step_number?: number
          timestamp?: string
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "investigation_steps_incident_id_fkey"
            columns: ["incident_id"]
            isOneToOne: false
            referencedRelation: "incidents"
            referencedColumns: ["id"]
          },
        ]
      }
      metrics: {
        Row: {
          data: Json
          id: string
          incident_id: string | null
          name: string
          threshold: number | null
          unit: string | null
        }
        Insert: {
          data?: Json
          id?: string
          incident_id?: string | null
          name: string
          threshold?: number | null
          unit?: string | null
        }
        Update: {
          data?: Json
          id?: string
          incident_id?: string | null
          name?: string
          threshold?: number | null
          unit?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "metrics_incident_id_fkey"
            columns: ["incident_id"]
            isOneToOne: false
            referencedRelation: "incidents"
            referencedColumns: ["id"]
          },
        ]
      }
      site_pages: {
        Row: {
          chunk_number: number
          content: string
          created_at: string
          embedding: string | null
          id: number
          metadata: Json
          summary: string
          title: string
          url: string
        }
        Insert: {
          chunk_number: number
          content: string
          created_at?: string
          embedding?: string | null
          id?: number
          metadata?: Json
          summary: string
          title: string
          url: string
        }
        Update: {
          chunk_number?: number
          content?: string
          created_at?: string
          embedding?: string | null
          id?: number
          metadata?: Json
          summary?: string
          title?: string
          url?: string
        }
        Relationships: []
      }
      topology_links: {
        Row: {
          curvature: number | null
          data_source: string | null
          id: string
          incident_id: string | null
          link_type: string | null
          properties: Json | null
          source: string
          target: string
          value: number | null
        }
        Insert: {
          curvature?: number | null
          data_source?: string | null
          id?: string
          incident_id?: string | null
          link_type?: string | null
          properties?: Json | null
          source: string
          target: string
          value?: number | null
        }
        Update: {
          curvature?: number | null
          data_source?: string | null
          id?: string
          incident_id?: string | null
          link_type?: string | null
          properties?: Json | null
          source?: string
          target?: string
          value?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "topology_links_incident_id_fkey"
            columns: ["incident_id"]
            isOneToOne: false
            referencedRelation: "incidents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "topology_links_source_fkey"
            columns: ["source"]
            isOneToOne: false
            referencedRelation: "topology_nodes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "topology_links_target_fkey"
            columns: ["target"]
            isOneToOne: false
            referencedRelation: "topology_nodes"
            referencedColumns: ["id"]
          },
        ]
      }
      topology_nodes: {
        Row: {
          alt_names: Json | null
          color: string | null
          data_source: string | null
          domain: string | null
          entity_id: string | null
          entity_type: string | null
          group_number: number | null
          icon: string | null
          id: string
          incident_id: string | null
          issue: boolean | null
          name: string
          properties: Json | null
          type: string | null
          x: number | null
          y: number | null
        }
        Insert: {
          alt_names?: Json | null
          color?: string | null
          data_source?: string | null
          domain?: string | null
          entity_id?: string | null
          entity_type?: string | null
          group_number?: number | null
          icon?: string | null
          id: string
          incident_id?: string | null
          issue?: boolean | null
          name: string
          properties?: Json | null
          type?: string | null
          x?: number | null
          y?: number | null
        }
        Update: {
          alt_names?: Json | null
          color?: string | null
          data_source?: string | null
          domain?: string | null
          entity_id?: string | null
          entity_type?: string | null
          group_number?: number | null
          icon?: string | null
          id?: string
          incident_id?: string | null
          issue?: boolean | null
          name?: string
          properties?: Json | null
          type?: string | null
          x?: number | null
          y?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "topology_nodes_incident_id_fkey"
            columns: ["incident_id"]
            isOneToOne: false
            referencedRelation: "incidents"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      binary_quantize:
        | {
            Args: {
              "": string
            }
            Returns: unknown
          }
        | {
            Args: {
              "": unknown
            }
            Returns: unknown
          }
      halfvec_avg: {
        Args: {
          "": number[]
        }
        Returns: unknown
      }
      halfvec_out: {
        Args: {
          "": unknown
        }
        Returns: unknown
      }
      halfvec_send: {
        Args: {
          "": unknown
        }
        Returns: string
      }
      halfvec_typmod_in: {
        Args: {
          "": unknown[]
        }
        Returns: number
      }
      hnsw_bit_support: {
        Args: {
          "": unknown
        }
        Returns: unknown
      }
      hnsw_halfvec_support: {
        Args: {
          "": unknown
        }
        Returns: unknown
      }
      hnsw_sparsevec_support: {
        Args: {
          "": unknown
        }
        Returns: unknown
      }
      hnswhandler: {
        Args: {
          "": unknown
        }
        Returns: unknown
      }
      ivfflat_bit_support: {
        Args: {
          "": unknown
        }
        Returns: unknown
      }
      ivfflat_halfvec_support: {
        Args: {
          "": unknown
        }
        Returns: unknown
      }
      ivfflathandler: {
        Args: {
          "": unknown
        }
        Returns: unknown
      }
      l2_norm:
        | {
            Args: {
              "": unknown
            }
            Returns: number
          }
        | {
            Args: {
              "": unknown
            }
            Returns: number
          }
      l2_normalize:
        | {
            Args: {
              "": string
            }
            Returns: string
          }
        | {
            Args: {
              "": unknown
            }
            Returns: unknown
          }
        | {
            Args: {
              "": unknown
            }
            Returns: unknown
          }
      match_site_pages: {
        Args: {
          query_embedding: string
          match_count?: number
          filter?: Json
        }
        Returns: {
          id: number
          url: string
          chunk_number: number
          title: string
          summary: string
          content: string
          metadata: Json
          similarity: number
        }[]
      }
      sparsevec_out: {
        Args: {
          "": unknown
        }
        Returns: unknown
      }
      sparsevec_send: {
        Args: {
          "": unknown
        }
        Returns: string
      }
      sparsevec_typmod_in: {
        Args: {
          "": unknown[]
        }
        Returns: number
      }
      vector_avg: {
        Args: {
          "": number[]
        }
        Returns: string
      }
      vector_dims:
        | {
            Args: {
              "": string
            }
            Returns: number
          }
        | {
            Args: {
              "": unknown
            }
            Returns: number
          }
      vector_norm: {
        Args: {
          "": string
        }
        Returns: number
      }
      vector_out: {
        Args: {
          "": string
        }
        Returns: unknown
      }
      vector_send: {
        Args: {
          "": string
        }
        Returns: string
      }
      vector_typmod_in: {
        Args: {
          "": unknown[]
        }
        Returns: number
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
