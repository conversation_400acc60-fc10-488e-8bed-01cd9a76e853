
import { Header } from '@/components/Header';
import { IncidentStats } from '@/components/incidents/IncidentStats';
import { IncidentList } from '@/components/incidents/IncidentList';
import { IncidentTrend } from '@/components/incidents/IncidentTrend';
import { incidents, getIncidentStats } from '@/utils/mock-data';
import { incidentTrendData } from '@/utils/trend-data';
import { format } from 'date-fns';
import { Activity, AlertTriangle } from 'lucide-react';
import { useTheme } from '@/hooks/use-theme';

const Index = () => {
  const stats = getIncidentStats();
  const currentDate = new Date();
  const formattedDate = format(currentDate, 'MMMM d, yyyy');
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <div className={`min-h-screen flex flex-col ${isDark ? 'bg-gray-950' : 'bg-[#f8fafc]'}`}>
      <Header />
      <main className="flex-1 container py-4 md:py-6 animate-fade-in">
        {/* Hero section without Globe */}
        <div className={`relative overflow-hidden rounded-lg mb-6 shadow-sm ${
          isDark ? 'bg-gray-900 border-gray-800' : 'bg-white border border-gray-200'
        }`}>
          <div className="relative z-10 pt-6 pb-8 px-4 md:px-8 text-center">
            <div className={`inline-flex items-center px-4 py-2 mb-4 rounded-full border ${
              isDark ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-100 border-gray-200 text-gray-700'
            }`}>
              <Activity className="h-4 w-4 mr-2 text-gray-500" />
              <span className="text-sm font-medium">
                {formattedDate}
              </span>
            </div>
            
            <h1 className={`text-4xl md:text-5xl font-bold mb-3 tracking-tight ${
              isDark ? 'text-gray-100' : 'text-gray-800'
            }`}>
              Ivanti Incident Management
            </h1>
            
            <p className={`text-xl max-w-2xl mx-auto mb-6 ${
              isDark ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Monitor and respond to incidents in real-time with advanced analytics
            </p>
          </div>
          
          {stats.active > 0 && (
            <div className={`absolute top-4 right-4 flex items-center gap-2 px-3 py-1.5 rounded-full text-sm ${
              isDark 
                ? 'bg-gray-800/90 backdrop-blur-sm border border-red-900 text-red-400' 
                : 'bg-white/90 backdrop-blur-sm border border-red-100 text-red-600'
            }`}>
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <span className="font-medium">{stats.active} Active Incidents</span>
            </div>
          )}
        </div>

        {/* Stats section */}
        <section className="mb-5 animate-fade-in animation-delay-200">
          <IncidentStats
            total={stats.total}
            active={stats.active}
            resolved={stats.resolved}
            critical={stats.critical}
          />
        </section>
        
        {/* Incident Trend Chart */}
        <section className="mb-5 animate-fade-in animation-delay-250">
          <IncidentTrend data={incidentTrendData} />
        </section>

        {/* Incidents list section */}
        <section className={`animate-fade-in animation-delay-300 p-5 rounded-lg shadow-sm ${
          isDark 
            ? 'bg-gray-900 border border-gray-800' 
            : 'bg-white border border-gray-200'
        }`}>
          <div className="flex items-center justify-between mb-5">
            <h2 className={`text-2xl font-bold ${isDark ? 'text-gray-100' : 'text-gray-800'}`}>
              Incidents
            </h2>
            <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              Showing {incidents.length} incidents
            </div>
          </div>
          <IncidentList incidents={incidents} />
        </section>
      </main>
    </div>
  );
};

export default Index;
