import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { Header } from '@/components/Header';
import { TabNavigation } from '@/components/tabs/TabNavigation';
import { Button } from '@/components/ui/button';
import { StatusBadge, SeverityBadge, TeamBadge } from '@/components/incidents/IncidentBadge';
import { SystemHealthChecks } from '@/components/analysis/SystemHealthChecks';
import { InvestigationSteps } from '@/components/analysis/InvestigationSteps';
import { Timeline } from '@/components/timeline/TimelineEvent';
import { MetricsGrid } from '@/components/metrics/MetricsChart';
import { NetworkTopology } from '@/components/topology/NetworkTopology';
import { TopologySnapshot } from '@/components/topology/TopologySnapshot';
import { RemediationActions } from '@/components/incident/RemediationActions';
import { LogViewer } from '@/components/logs/LogViewer';
import { AlertCircle, ArrowLeft, ExternalLink, ZoomIn, ZoomOut, RefreshCw, Loader2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useEffect, useState, useRef } from 'react';
import { useIncident } from '@/hooks/useIncident';
import { Card, CardContent } from '@/components/ui/card';
import { setDatabaseConfig } from '@/services/apiService';
import { toast } from '@/components/ui/use-toast';

// Set default database config (this could be moved to a configuration file or init function)
setDatabaseConfig({
  type: 'supabase', // or 'azure'
  endpoint: import.meta.env.VITE_DB_ENDPOINT || 'https://your-supabase-url.supabase.co',
  apiKey: import.meta.env.VITE_DB_API_KEY || '',
});

const IncidentDetails = () => {
  const { id, tab } = useParams<{ id: string, tab?: string }>();
  const navigate = useNavigate();
  const [currentTab, setCurrentTab] = useState<string>('overview');
  const topologyRef = useRef<HTMLDivElement>(null);
  
  // Use our custom hook to fetch incident data
  const { incident, topology, isLoading, error, refetch } = useIncident(id || '');
  
  useEffect(() => {
    // Extract tab from URL if present
    if (tab && ['overview', 'root-cause', 'remediation', 'metrics', 'timeline', 'topology', 'logs', 'chat'].includes(tab)) {
      setCurrentTab(tab);
    } else {
      setCurrentTab('overview');
    }
  }, [tab]);
  
  useEffect(() => {
    if (error) {
      toast({
        title: "Error loading incident data",
        description: "There was a problem fetching the incident information. Please try again.",
        variant: "destructive"
      });
    }
  }, [error]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container py-8 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mb-4 mx-auto text-primary" />
            <h2 className="text-xl font-medium mb-2">Loading incident data...</h2>
            <p className="text-muted-foreground">Please wait while we fetch the details</p>
          </div>
        </main>
      </div>
    );
  }

  if (!incident) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container py-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold mb-4">Incident Not Found</h1>
            <button
              onClick={() => navigate('/')}
              className="text-primary hover:underline inline-flex items-center"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Incidents
            </button>
          </div>
        </main>
      </div>
    );
  }

  const formattedDate = formatDistanceToNow(new Date(incident.createdAt), { addSuffix: true });

  const tabs = [
    { label: 'Overview', value: 'overview', href: `/incident/${id}/overview` },
    { label: 'Root Cause', value: 'root-cause', href: `/incident/${id}/root-cause` },
    { label: 'Remediation', value: 'remediation', href: `/incident/${id}/remediation` },
    { label: 'Metrics', value: 'metrics', href: `/incident/${id}/metrics` },
    { label: 'Timeline', value: 'timeline', href: `/incident/${id}/timeline` },
    { label: 'Topology', value: 'topology', href: `/incident/${id}/topology` },
    { label: 'Logs', value: 'logs', href: `/incident/${id}/logs` },
    { label: 'Chat', value: 'chat', href: `/incident/${id}/chat` },
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 container py-8 animate-fade-in">
        <div className="mb-4">
          <button
            onClick={() => navigate('/')}
            className="text-muted-foreground hover:text-foreground inline-flex items-center text-sm"
          >
            <ArrowLeft className="mr-1 h-4 w-4" />
            Back to Incidents
          </button>
        </div>

        <div className={`p-6 mb-6 rounded-lg ${incident.severity === 'critical' ? 'bg-alert-criticalBg/50' : 'bg-secondary'}`}>
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <AlertCircle className="mt-1 h-6 w-6 text-alert-critical" />
              <div>
                <h1 className="text-2xl font-bold mb-2">{incident.title}</h1>
                <div className="text-sm text-muted-foreground mb-3">
                  ID: {incident.id}
                </div>
                <p className="text-sm mb-4">{incident.description}</p>
                
                <div className="flex flex-wrap gap-2 items-center">
                  <StatusBadge status={incident.status} />
                  <SeverityBadge severity={incident.severity} />
                  {incident.teams.map((team) => (
                    <TeamBadge key={team.id} name={team.name} />
                  ))}
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground">{formattedDate}</div>
              <button className="mt-2 text-xs text-primary hover:underline inline-flex items-center">
                <span>View in {incident.source.name}</span>
                <ExternalLink className="ml-1 h-3 w-3" />
              </button>
            </div>
          </div>
        </div>

        <TabNavigation tabs={tabs} className="mb-6" />

        <div className="py-4 animate-fade-in">
          {currentTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <section>
                  <h2 className="text-xl font-bold mb-4">Incident Details</h2>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Issue</h3>
                      <p className="mt-1">{incident.description}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Alert Details</h3>
                      <div className="mt-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm text-muted-foreground">Severity:</div>
                          <div><SeverityBadge severity={incident.severity} /></div>
                        </div>
                        <div>
                          <div className="text-sm text-muted-foreground">Status:</div>
                          <div><StatusBadge status={incident.status} /></div>
                        </div>
                        <div>
                          <div className="text-sm text-muted-foreground">Customer:</div>
                          <div>{incident.customer}</div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Topology snapshot in the overview */}
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">Service Topology</h3>
                      {topology ? (
                        <TopologySnapshot data={topology} incidentId={incident.id} height={250} />
                      ) : (
                        <Card>
                          <CardContent className="flex items-center justify-center p-8">
                            <p className="text-muted-foreground">No topology data available</p>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                    
                    {incident.timeline && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-2">Timeline</h3>
                        <Timeline events={incident.timeline.slice(0, 3)} />
                        <button 
                          className="text-sm text-primary hover:underline mt-2"
                          onClick={() => navigate(`/incident/${id}/timeline`)}
                        >
                          View full timeline
                        </button>
                      </div>
                    )}
                  </div>
                </section>
              </div>
              
              <div className="space-y-6">
                <section>
                  <h2 className="text-xl font-bold mb-4">Primary Impact Metric</h2>
                  {incident.metrics && incident.metrics.length > 0 && (
                    <MetricsGrid metrics={[incident.metrics[0]]} />
                  )}
                </section>
              </div>
            </div>
          )}
          
          {currentTab === 'root-cause' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {incident.healthChecks && (
                  <SystemHealthChecks checks={incident.healthChecks} />
                )}
                {incident.investigationSteps && (
                  <InvestigationSteps steps={incident.investigationSteps} />
                )}
              </div>
              
              {/* Root Cause Analysis Card */}
              {incident.rootCause && (
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-semibold mb-2">Root Cause Analysis</h3>
                    <p className="whitespace-pre-line">{incident.rootCause}</p>
                    
                    {incident.preventiveActions && (
                      <div className="mt-6">
                        <h3 className="text-lg font-semibold mb-2">Preventive Actions</h3>
                        <p className="whitespace-pre-line">{incident.preventiveActions}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          )}
          
          {currentTab === 'metrics' && (
            <div>
              <h2 className="text-xl font-bold mb-4">Metrics</h2>
              {incident.metrics && (
                <MetricsGrid metrics={incident.metrics} />
              )}
            </div>
          )}
          
          {currentTab === 'timeline' && (
            <div>
              <h2 className="text-xl font-bold mb-4">Timeline</h2>
              {incident.timeline && (
                <div className="bg-card rounded-lg p-6">
                  <Timeline events={incident.timeline} />
                </div>
              )}
            </div>
          )}
          
          {currentTab === 'topology' && (
            <div ref={topologyRef}>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">Service Topology</h2>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="text-xs">
                    <ZoomIn className="h-3.5 w-3.5 mr-1" />
                    Zoom In
                  </Button>
                  <Button variant="outline" size="sm" className="text-xs">
                    <ZoomOut className="h-3.5 w-3.5 mr-1" />
                    Zoom Out
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="text-xs" 
                    onClick={() => refetch()}
                  >
                    <RefreshCw className="h-3.5 w-3.5 mr-1" />
                    Refresh
                  </Button>
                </div>
              </div>
              
              <div className="bg-card rounded-lg p-2 mb-4">
                {topology ? (
                  <NetworkTopology data={topology} />
                ) : (
                  <div className="h-[600px] flex items-center justify-center">
                    <p className="text-muted-foreground">No topology data available</p>
                  </div>
                )}
              </div>
              
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h3 className="text-sm font-medium mb-3">Topology Legend</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center">
                    <span className="w-3 h-3 rounded-full bg-[#3b82f6] mr-2"></span>
                    <span className="text-sm">Host/Node</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-3 h-3 rounded-full bg-[#10b981] mr-2"></span>
                    <span className="text-sm">Service</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-3 h-3 rounded-full bg-[#f59e0b] mr-2"></span>
                    <span className="text-sm">Database/Network</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-3 h-3 rounded-full bg-[#dc2626] border-2 border-[#fee2e2] mr-2"></span>
                    <span className="text-sm">Issue Detected</span>
                  </div>
                </div>
                
                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium mb-2">Connection Types</h3>
                    <ul className="text-sm space-y-1">
                      <li className="flex items-center gap-2">
                        <span className="w-8 h-[2px] bg-gray-400"></span>
                        <span>Standard Connection</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-8 h-[2px] bg-gray-400 relative">
                          <span className="absolute top-[-3px] right-0 w-2 h-2 rounded-full bg-gray-400"></span>
                        </span>
                        <span>Directional Connection</span>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-2">Tips</h3>
                    <ul className="text-sm list-disc pl-5 space-y-1">
                      <li>Drag nodes to reposition</li>
                      <li>Hover over nodes for detailed information</li>
                      <li>Click on connections to zoom in</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {currentTab === 'remediation' && (
            <div>
              <h2 className="text-xl font-bold mb-4">Remediation</h2>
              {incident.remediationActions ? (
                <RemediationActions actions={incident.remediationActions} />
              ) : (
                <div className="text-center py-12">
                  <h2 className="text-xl font-bold mb-2">No Remediation Actions</h2>
                  <p className="text-muted-foreground">No remediation steps have been defined for this incident yet.</p>
                </div>
              )}
            </div>
          )}
          
          {currentTab === 'logs' && (
            <div>
              <h2 className="text-xl font-bold mb-4">System Logs</h2>
              {incident.logs ? (
                <LogViewer logs={incident.logs} />
              ) : (
                <div className="text-center py-12">
                  <h2 className="text-xl font-bold mb-2">No Logs Available</h2>
                  <p className="text-muted-foreground">No system logs have been collected for this incident yet.</p>
                </div>
              )}
            </div>
          )}
          
          {currentTab === 'chat' && (
            <div className="text-center py-12">
              <h2 className="text-xl font-bold mb-2">Coming Soon</h2>
              <p className="text-muted-foreground">The chat tab is under development</p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default IncidentDetails;
