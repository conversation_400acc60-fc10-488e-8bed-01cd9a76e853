
import { createClient } from '@supabase/supabase-js';
import { Incident } from '@/types/incident';
import { TopologyData } from '@/types/topology';
import { supabase } from '@/integrations/supabase/client';
import { J<PERSON> } from '@/integrations/supabase/types';

// Interface for different database providers
export interface DatabaseConfig {
  type: 'supabase' | 'azure';
  endpoint: string;
  apiKey?: string;
  username?: string;
  password?: string;
  database?: string;
}

// Default config (can be overridden in environment variables or app settings)
let dbConfig: DatabaseConfig = {
  type: 'supabase',
  endpoint: import.meta.env.VITE_DB_ENDPOINT || 'https://wrlhncdvzswfqavovugs.supabase.co',
  apiKey: import.meta.env.VITE_DB_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qzONBk0WiTwb50JbfH4UTkc7g5Sm_9GT2z017CTZTDw',
};

// Create a supabase client
const createSupabaseClient = (config: DatabaseConfig) => {
  return createClient(config.endpoint, config.apiKey || '');
};

// Azure client would be implemented here - using a mock implementation for now
const createAzureClient = (config: DatabaseConfig) => {
  console.log('Creating Azure client with config:', config);
  // This would be replaced with actual Azure PostgreSQL client in production
  return {
    from: (table: string) => ({
      select: () => ({
        eq: () => ({
          single: async () => {
            // For now, use our mock data
            const { getIncidentById } = await import('@/utils/mock-data');
            const { getTopologyData } = await import('@/utils/topology-data');
            
            if (table === 'incidents') {
              return { 
                data: getIncidentById('763222ee-0a9e-4dce-a14e-8b24b3a11020'),
                error: null
              };
            } else if (table === 'topology') {
              return { 
                data: getTopologyData(),
                error: null 
              };
            }
            return { data: null, error: null };
          }
        })
      })
    })
  };
};

// Set database configuration
export const setDatabaseConfig = (config: DatabaseConfig) => {
  dbConfig = config;
  console.log('Database configuration updated:', dbConfig.type);
};

// Get the current database client based on configuration
const getClient = () => {
  if (dbConfig.type === 'supabase') {
    return createSupabaseClient(dbConfig);
  } else if (dbConfig.type === 'azure') {
    return createAzureClient(dbConfig);
  }
  throw new Error('Unsupported database type');
};

// Fetch an incident by ID
export const fetchIncidentById = async (id: string): Promise<Incident | null> => {
  try {
    if (dbConfig.type === 'supabase') {
      // First try to fetch from Supabase
      const { data: incidentData, error } = await supabase
        .from('incidents')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.warn('Error fetching from Supabase, falling back to mock data:', error.message);
      } else if (incidentData) {
        // If we have incident data, fetch related data
        const { data: healthChecks } = await supabase
          .from('health_checks')
          .select('*')
          .eq('incident_id', id);
          
        const { data: investigationSteps } = await supabase
          .from('investigation_steps')
          .select('*')
          .eq('incident_id', id);
          
        const { data: metrics } = await supabase
          .from('metrics')
          .select('*')
          .eq('incident_id', id);
          
        const { data: logs } = await supabase
          .from('incident_logs')
          .select('*')
          .eq('incident_id', id);

        // Map database data to our Incident type
        return mapToIncidentFromDb({
          ...incidentData,
          healthChecks,
          investigationSteps,
          metrics,
          logs
        });
      }
    } else {
      // For other database types, use the configured client
      const client = getClient();
      const response = await client
        .from('incidents')
        .select('*')
        .eq('id', id)
        .single();

      if (response.data) {
        console.log('Successfully fetched incident data from database');
        return mapToIncident(response.data);
      }
      
      if (response.error) {
        console.warn('Error fetching from database, falling back to mock data:', response.error);
      }
    }
    
    // Fall back to mock data if database query fails or returns no data
    const { getIncidentById } = await import('@/utils/mock-data');
    const mockData = getIncidentById(id);
    
    if (mockData) {
      console.log('Using mock incident data as fallback');
      return mockData;
    }
    
    console.error('No incident data available from any source');
    return null;
  } catch (error) {
    console.error('Failed to fetch incident:', error);
    
    // Final fallback to mock data
    try {
      const { getIncidentById } = await import('@/utils/mock-data');
      const mockData = getIncidentById(id);
      console.log('Using mock incident data after error');
      return mockData;
    } catch (mockError) {
      console.error('Could not load mock data either:', mockError);
      return null;
    }
  }
};

// Save an incident to the database
export const saveIncident = async (incident: Incident): Promise<{ success: boolean, error?: string }> => {
  try {
    if (dbConfig.type !== 'supabase') {
      console.warn('Saving incidents is currently only supported with Supabase');
      return { success: false, error: 'Unsupported database type' };
    }

    // Convert the Source object to a string array for Supabase (fixing the Json type issue)
    const sourceNames = incident.source ? [incident.source.name] : [];
    const teamNames = incident.teams.map(team => team.name);

    // First, save the main incident data
    const { error: incidentError } = await supabase
      .from('incidents')
      .upsert({
        id: incident.id,
        title: incident.title,
        description: incident.description,
        status: incident.status,
        severity: incident.severity,
        created_at: incident.createdAt,
        updated_at: incident.updatedAt,
        customer: incident.customer,
        impact: incident.description, // Since our example doesn't have an impact field
        root_cause: incident.rootCause || '', 
        preventive_actions: incident.preventiveActions || '', 
        sources: sourceNames as unknown as Json, 
        teams: teamNames as unknown as Json,
        affected_services: (incident.affectedServices || []) as unknown as Json
      }, { onConflict: 'id' });

    if (incidentError) {
      console.error('Error saving incident:', incidentError);
      return { success: false, error: incidentError.message };
    }

    // Save health checks if they exist
    if (incident.healthChecks && incident.healthChecks.length > 0) {
      // First delete existing health checks to avoid duplicates
      await supabase
        .from('health_checks')
        .delete()
        .eq('incident_id', incident.id);

      // Now insert the new health checks
      const { error: healthChecksError } = await supabase
        .from('health_checks')
        .insert(incident.healthChecks.map(check => ({
          incident_id: incident.id,
          category: check.type,
          status: check.status === 'issues_found' ? 'ISSUES_FOUND' : 'ALL_GOOD',
          description: check.message,
          details: check.details ? [check.details] as unknown as Json : null
        })));

      if (healthChecksError) {
        console.error('Error saving health checks:', healthChecksError);
      }
    }

    // Save investigation steps if they exist
    if (incident.investigationSteps && incident.investigationSteps.length > 0) {
      // First delete existing steps to avoid duplicates
      await supabase
        .from('investigation_steps')
        .delete()
        .eq('incident_id', incident.id);

      // Now insert the new steps
      const { error: stepsError } = await supabase
        .from('investigation_steps')
        .insert(incident.investigationSteps.map(step => ({
          incident_id: incident.id,
          step_number: step.id,
          timestamp: step.timestamp,
          title: step.title,
          description: step.description,
          investigation_details: {} as Json
        })));

      if (stepsError) {
        console.error('Error saving investigation steps:', stepsError);
      }
    }

    // Save metrics if they exist
    if (incident.metrics && incident.metrics.length > 0) {
      // First delete existing metrics to avoid duplicates
      await supabase
        .from('metrics')
        .delete()
        .eq('incident_id', incident.id);

      // Now insert the new metrics
      const metricsToInsert = incident.metrics.map(metric => ({
        incident_id: incident.id,
        name: metric.name,
        unit: metric.unit,
        threshold: metric.threshold,
        data: metric.data as unknown as Json
      }));

      const { error: metricsError } = await supabase
        .from('metrics')
        .insert(metricsToInsert);

      if (metricsError) {
        console.error('Error saving metrics:', metricsError);
      }
    }

    // Save timeline/logs if they exist
    if (incident.timeline && incident.timeline.length > 0) {
      // First delete existing logs to avoid duplicates
      await supabase
        .from('incident_logs')
        .delete()
        .eq('incident_id', incident.id);

      // Now insert the new logs
      const { error: logsError } = await supabase
        .from('incident_logs')
        .insert(incident.timeline.map(event => ({
          incident_id: incident.id,
          timestamp: event.timestamp,
          level: mapLogLevelFromType(event.type),
          type: event.type,
          title: event.title,
          description: event.description,
          service: '',
          message: event.description
        })));

      if (logsError) {
        console.error('Error saving timeline/logs:', logsError);
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Failed to save incident:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error saving incident' 
    };
  }
};

// Map log type to log level
const mapLogLevelFromType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'alert': 'error',
    'action': 'info',
    'update': 'info',
    'investigation': 'debug'
  };
  return typeMap[type] || 'info';
};

// Fetch topology data for an incident
export const fetchTopologyData = async (incidentId: string): Promise<TopologyData | null> => {
  try {
    if (dbConfig.type === 'supabase') {
      // First get the nodes
      const { data: nodes, error: nodesError } = await supabase
        .from('topology_nodes')
        .select('*')
        .eq('incident_id', incidentId);
        
      if (nodesError) {
        console.warn('Error fetching topology nodes from Supabase:', nodesError.message);
      } else if (nodes && nodes.length > 0) {
        // If we got nodes, also get the links
        const { data: links, error: linksError } = await supabase
          .from('topology_links')
          .select('*')
          .eq('incident_id', incidentId);
          
        if (linksError) {
          console.warn('Error fetching topology links from Supabase:', linksError.message);
        } else {
          // Map from database format to our TopologyData type
          return {
            nodes: nodes.map(node => ({
              id: node.id,
              name: node.name,
              issue: node.issue,
              group: node.group_number,
              icon: node.icon,
              properties: node.properties as unknown as Record<string, any>,
              alt_names: node.alt_names as unknown as string[],
              entity_id: node.entity_id,
              type: node.type,
              entityType: node.entity_type,
              domain: node.domain,
              data_source: node.data_source,
              color: node.color,
              x: node.x,
              y: node.y
            })),
            links: links.map(link => ({
              id: link.id,
              source: link.source,
              target: link.target,
              link_type: link.link_type,
              properties: link.properties as unknown as Record<string, any>,
              data_source: link.data_source,
              value: link.value,
              curvature: link.curvature
            }))
          };
        }
      }
    } else {
      // For other database types, use the configured client
      const client = getClient();
      const response = await client
        .from('topology')
        .select('*')
        .eq('incidentId', incidentId)
        .single();
      
      if (response.data) {
        console.log('Successfully fetched topology data from database');
        return response.data as TopologyData;
      }
      
      if (response.error) {
        console.warn('Error fetching topology from database, falling back to mock data:', response.error);
      }
    }
    
    // Fall back to mock data
    const { getTopologyData } = await import('@/utils/topology-data');
    const mockData = getTopologyData();
    
    if (mockData) {
      console.log('Using mock topology data as fallback');
      return mockData;
    }
    
    console.error('No topology data available from any source');
    return null;
  } catch (error) {
    console.error('Failed to fetch topology data:', error);
    
    // Final fallback to mock data
    try {
      const { getTopologyData } = await import('@/utils/topology-data');
      const mockData = getTopologyData();
      console.log('Using mock topology data after error');
      return mockData;
    } catch (mockError) {
      console.error('Could not load mock topology data either:', mockError);
      return null;
    }
  }
};

// Save topology data to the database
export const saveTopologyData = async (
  incidentId: string, 
  topology: TopologyData
): Promise<{ success: boolean, error?: string }> => {
  try {
    if (dbConfig.type !== 'supabase') {
      console.warn('Saving topology data is currently only supported with Supabase');
      return { success: false, error: 'Unsupported database type' };
    }

    // First, save all the nodes
    // We need to make sure all nodes exist before saving links due to foreign key constraints
    if (topology.nodes.length > 0) {
      // First delete existing nodes to avoid duplicates
      // This will cascade delete links as well due to our FK constraints
      await supabase
        .from('topology_nodes')
        .delete()
        .eq('incident_id', incidentId);

      // Now insert the new nodes
      const { error: nodesError } = await supabase
        .from('topology_nodes')
        .insert(topology.nodes.map(node => ({
          id: node.id,
          name: node.name,
          incident_id: incidentId,
          issue: node.issue || false,
          group_number: node.group,
          icon: node.icon,
          properties: (node.properties || {}) as unknown as Json,
          alt_names: (node.alt_names || []) as unknown as Json,
          entity_id: node.entity_id,
          type: node.type,
          entity_type: node.entityType,
          domain: node.domain,
          data_source: node.data_source,
          color: node.color,
          x: node.x,
          y: node.y
        })));

      if (nodesError) {
        console.error('Error saving topology nodes:', nodesError);
        return { success: false, error: nodesError.message };
      }
    }

    // Then save all the links
    if (topology.links.length > 0) {
      const { error: linksError } = await supabase
        .from('topology_links')
        .insert(topology.links.map(link => ({
          incident_id: incidentId,
          source: link.source,
          target: link.target,
          link_type: link.link_type,
          properties: (link.properties || {}) as unknown as Json,
          data_source: link.data_source,
          value: link.value,
          curvature: link.curvature
        })));

      if (linksError) {
        console.error('Error saving topology links:', linksError);
        return { success: false, error: linksError.message };
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Failed to save topology data:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error saving topology data' 
    };
  }
};

// Fetch all incidents
export const fetchAllIncidents = async (): Promise<Incident[]> => {
  try {
    if (dbConfig.type === 'supabase') {
      const { data, error } = await supabase
        .from('incidents')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.warn('Error fetching incidents from Supabase:', error.message);
      } else if (data && data.length > 0) {
        // Map the data to our Incident type
        return data.map(incident => mapToIncidentFromDb(incident));
      }
    }

    // Fall back to mock data
    const { getIncidentById } = await import('@/utils/mock-data');
    // Create a few sample incidents as fallback
    return [
      getIncidentById('763222ee-0a9e-4dce-a14e-8b24b3a11020'),
      getIncidentById('663222ee-0a9e-4dce-a14e-8b24b3a11019')
    ].filter(Boolean) as Incident[];
  } catch (error) {
    console.error('Failed to fetch all incidents:', error);
    
    // Fallback to mock data
    try {
      const { getIncidentById } = await import('@/utils/mock-data');
      return [
        getIncidentById('763222ee-0a9e-4dce-a14e-8b24b3a11020'),
        getIncidentById('663222ee-0a9e-4dce-a14e-8b24b3a11019')
      ].filter(Boolean) as Incident[];
    } catch (mockError) {
      console.error('Could not load mock data either:', mockError);
      return [];
    }
  }
};

// Helper function to map database response to Incident type
const mapToIncidentFromDb = (data: any): Incident => {
  if (!data) return null as unknown as Incident;

  // Extract teams from the JSONB array
  const teams = Array.isArray(data.teams) 
    ? data.teams.map((name: string) => ({ 
        id: name.toLowerCase().replace(/\s+/g, '-'), 
        name 
      }))
    : [];

  // Extract source from the JSONB array (taking the first one)
  const sources = Array.isArray(data.sources) ? data.sources : [];
  const source = {
    id: sources.length > 0 
      ? sources[0].toLowerCase().replace(/\s+/g, '-') 
      : 'unknown',
    name: sources.length > 0 
      ? sources[0] 
      : 'Unknown'
  };

  // Map health checks
  const healthChecks = data.healthChecks && Array.isArray(data.healthChecks)
    ? data.healthChecks.map((check: any) => ({
        id: check.id,
        type: check.category,
        status: check.status === 'ISSUES_FOUND' ? 'issues_found' : 'all_good',
        message: check.description,
        details: Array.isArray(check.details) ? check.details.join('\n') : check.details
      }))
    : [];

  // Map investigation steps
  const investigationSteps = data.investigationSteps && Array.isArray(data.investigationSteps)
    ? data.investigationSteps.map((step: any) => ({
        id: step.step_number,
        title: step.title,
        description: step.description,
        timestamp: step.timestamp
      }))
    : [];

  // Map metrics
  const metrics = data.metrics && Array.isArray(data.metrics)
    ? data.metrics.map((metric: any) => ({
        id: metric.id,
        name: metric.name,
        unit: metric.unit || 'count',
        threshold: metric.threshold,
        data: metric.data || []
      }))
    : [];

  // Map logs/timeline
  const timeline = data.logs && Array.isArray(data.logs)
    ? data.logs.map((log: any) => ({
        id: log.id,
        type: mapLogTypeFromLevel(log.level),
        timestamp: log.timestamp,
        title: log.title || `${log.service || 'System'} - ${log.level.toUpperCase()}`,
        description: log.description || log.message
      }))
    : [];

  return {
    id: data.id,
    title: data.title,
    description: data.description || '',
    status: mapStatus(data.status),
    severity: mapSeverity(data.severity),
    createdAt: data.created_at,
    updatedAt: data.updated_at,
    customer: data.customer,
    teams,
    source,
    healthChecks,
    investigationSteps,
    timeline,
    metrics,
    affectedServices: Array.isArray(data.affected_services) ? data.affected_services : [],
    rootCause: data.root_cause,
    preventiveActions: data.preventive_actions
  };
};

// Map log level to event type
const mapLogTypeFromLevel = (level: string): 'alert' | 'action' | 'update' | 'investigation' => {
  const levelMap: Record<string, 'alert' | 'action' | 'update' | 'investigation'> = {
    'error': 'alert',
    'warn': 'alert',
    'info': 'update',
    'debug': 'investigation'
  };
  return levelMap[level] || 'update';
};

// Helper function to map the database response to our Incident type (legacy format)
const mapToIncident = (data: any): Incident => {
  if (!data) return null as unknown as Incident;

  // Map database fields to our Incident type
  return {
    id: data.id || data.incidentId,
    title: data.title,
    description: data.description,
    status: mapStatus(data.status),
    severity: mapSeverity(data.severity),
    createdAt: data.startTime || data.createdAt || data.created_at,
    updatedAt: data.endTime || data.updatedAt || data.updated_at,
    customer: data.customer,
    teams: Array.isArray(data.teams) 
      ? data.teams.map((name: string) => ({ id: name.toLowerCase().replace(/\s+/g, '-'), name }))
      : [],
    source: {
      id: Array.isArray(data.sources) && data.sources.length > 0 
        ? data.sources[0].toLowerCase().replace(/\s+/g, '-') 
        : 'unknown',
      name: Array.isArray(data.sources) && data.sources.length > 0 
        ? data.sources[0] 
        : 'Unknown'
    },
    healthChecks: mapSystemChecks(data.systemChecks),
    investigationSteps: mapInvestigationSteps(data.investigationSteps),
    timeline: mapLogs(data.logs),
    metrics: mapMetrics(data.metrics),
    affectedServices: data.affectedServices || [],
    rootCause: data.rootCause,
    preventiveActions: data.preventiveActions
  };
};

// Helper functions to map specific fields
const mapStatus = (status: string): 'active' | 'resolved' | 'investigating' => {
  const statusMap: Record<string, 'active' | 'resolved' | 'investigating'> = {
    'Active': 'active',
    'Resolved': 'resolved',
    'Investigating': 'investigating',
    'active': 'active',
    'resolved': 'resolved',
    'investigating': 'investigating'
  };
  return statusMap[status] || 'active';
};

const mapSeverity = (severity: string): 'critical' | 'warning' | 'info' | 'low' => {
  const severityMap: Record<string, 'critical' | 'warning' | 'info' | 'low'> = {
    'Critical': 'critical',
    'Warning': 'warning',
    'Info': 'info',
    'Low': 'low',
    'critical': 'critical',
    'warning': 'warning',
    'info': 'info',
    'low': 'low'
  };
  return severityMap[severity] || 'info';
};

const mapSystemChecks = (checks: any[] = []): any[] => {
  return checks ? checks.map(check => ({
    id: check.id || `check-${Math.random().toString(36).substr(2, 9)}`,
    type: check.category,
    status: check.status === 'ISSUES_FOUND' ? 'issues_found' : 'all_good',
    message: check.description,
    details: Array.isArray(check.details) ? check.details.join('\n') : check.details,
  })) : [];
};

const mapInvestigationSteps = (steps: any[] = []): any[] => {
  return steps ? steps.map(step => ({
    id: step.id || step.step,
    title: step.title,
    description: step.description,
    timestamp: step.timestamp,
  })) : [];
};

const mapLogs = (logs: any[] = []): any[] => {
  return logs ? logs.map(log => ({
    id: log.id || `log-${Math.random().toString(36).substr(2, 9)}`,
    type: mapLogType(log.level),
    timestamp: log.timestamp,
    title: log.title || `${log.service} - ${log.level.toUpperCase()}`,
    description: log.message || log.description,
  })) : [];
};

const mapLogType = (level: string): 'alert' | 'action' | 'update' | 'investigation' => {
  const typeMap: Record<string, 'alert' | 'action' | 'update' | 'investigation'> = {
    'error': 'alert',
    'warn': 'alert',
    'info': 'update',
    'debug': 'investigation',
  };
  return typeMap[level] || 'update';
};

const mapMetrics = (metrics: any[] = []): any[] => {
  return metrics ? metrics.map(metric => ({
    id: metric.id || `metric-${Math.random().toString(36).substr(2, 9)}`,
    name: metric.name,
    unit: metric.unit || (metric.name && metric.name.includes('Utilization') ? '%' : 'count'),
    data: metric.data,
    threshold: metric.threshold || (metric.name === 'Evicted Pods' ? 2 : undefined),
  })) : [];
};
