
// Sample topology data for the Topology tab visualization
export const topologyData = {
  "nodes": [
    {
      "id": "load-balancer",
      "name": "Load Balancer",
      "issue": false,
      "group": 3,
      "type": "NETWORK",
      "data_source": "live"
    },
    {
      "id": "api-gateway-service",
      "name": "API Gateway",
      "issue": false,
      "group": 2,
      "type": "SERVICE",
      "data_source": "live"
    },
    {
      "id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0",
      "name": "aks-default-********-vmss0007qj",
      "issue": false,
      "group": 1,
      "icon": "static/icons/node.svg",
      "properties": {
        "account": [
          "IvantiCloud - US"
        ],
        "accountId": [
          "1093620"
        ],
        "domain": "INFRA",
        "entityType": "INFRASTRUCTURE_HOST_ENTITY",
        "type": "HOST",
        "hostStatus": [
          "running"
        ],
        "instanceType": [
          "Standard_D8s_v3"
        ],
        "operatingSystem": [
          "linux"
        ]
      },
      "entity_id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0",
      "type": "HOST",
      "entityType": "INFRASTRUCTURE_HOST_ENTITY",
      "domain": "INFRA",
      "data_source": "live"
    },
    {
      "id": "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2",
      "name": "rollout-update-handler-container",
      "issue": true,
      "group": 1,
      "icon": "static/icons/containerinstances.png",
      "properties": {
        "account": [
          "IvantiCloud - US"
        ],
        "accountId": [
          "1093620"
        ],
        "domain": "INFRA",
        "entityType": "GENERIC_INFRASTRUCTURE_ENTITY",
        "type": "CONTAINER",
        "container.state": [
          "Waiting"
        ],
        "k8s.containerName": [
          "rollout-update-handler-container"
        ],
        "k8s.status": [
          "Waiting"
        ]
      },
      "entity_id": "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2",
      "type": "CONTAINER",
      "entityType": "GENERIC_INFRASTRUCTURE_ENTITY",
      "domain": "INFRA",
      "data_source": "live"
    },
    {
      "id": "database-cluster",
      "name": "Database Cluster",
      "issue": false,
      "group": 3,
      "type": "DATABASE",
      "data_source": "live"
    },
    {
      "id": "auth-service",
      "name": "Auth Service",
      "issue": false,
      "group": 2,
      "type": "SERVICE",
      "data_source": "live"
    },
    {
      "id": "frontend-app",
      "name": "Frontend App",
      "issue": false,
      "group": 2,
      "type": "SERVICE",
      "data_source": "live"
    }
  ],
  "links": [
    {
      "id": "load-balancer->api-gateway-service",
      "source": "load-balancer",
      "target": "api-gateway-service",
      "link_type": "routes_to",
      "data_source": "live",
      "value": 1
    },
    {
      "id": "api-gateway-service->auth-service",
      "source": "api-gateway-service",
      "target": "auth-service",
      "link_type": "depends_on",
      "data_source": "live",
      "value": 1
    },
    {
      "id": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0->MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2",
      "source": "MTA5MzYyMHxJTkZSQXxOQXwzMDkwNzA1NDg5OTY3NjM4MjU0",
      "target": "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2",
      "link_type": "runs",
      "properties": {},
      "data_source": "live",
      "value": 1
    },
    {
      "id": "api-gateway-service->frontend-app",
      "source": "api-gateway-service",
      "target": "frontend-app",
      "link_type": "serves",
      "data_source": "live",
      "value": 1
    },
    {
      "id": "auth-service->database-cluster",
      "source": "auth-service",
      "target": "database-cluster",
      "link_type": "connects_to",
      "data_source": "live",
      "value": 1
    },
    {
      "id": "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2->api-gateway-service",
      "source": "MTA5MzYyMHxJTkZSQXxOQXw3MTIwMjg1NjgyOTE5MDM3MTI2",
      "target": "api-gateway-service",
      "link_type": "reports_to",
      "data_source": "live",
      "value": 1,
      "curvature": 0.3
    }
  ]
};

// Helper function to get topology data
export const getTopologyData = () => {
  return topologyData;
};
