
import { Incident, Severity, Status, HealthCheck, InvestigationStep, TimelineEvent, Metric } from '../types/incident';

// Health checks for the first incident
const podEvictionHealthChecks: HealthCheck[] = [
  {
    id: '1',
    type: 'APPLICATION HEALTH',
    status: 'issues_found',
    message: 'Multiple pod evictions detected for login-app pods',
    details: 'Evicted Pods threshold exceeded for over 30 minutes. Possible service disruption for login-app message handler.',
  },
  {
    id: '2',
    type: 'CLUSTER HEALTH',
    status: 'issues_found',
    message: 'Disk pressure detected on node aks-default-39789032-vmss000ef4',
    details: 'File system utilization consistently above 90%. Kubelet cannot reclaim enough space (ImageGCFailed, FreeDiskSpaceFailed).',
  },
  {
    id: '3',
    type: 'RECENT DEPLOYMENT',
    status: 'all_good',
    message: 'No new deployments in the last 24 hours',
    details: 'No correlation between a code release and disk pressure.',
  },
  {
    id: '4',
    type: 'SYNTHETIC MONITORS',
    status: 'all_good',
    message: 'Endpoint synthetic checks for UKU cluster is fine',
    details: 'No Synthetic failure observed. Synthetics Success Rate: 100%.',
  },
];

// Investigation steps for the first incident
const podEvictionSteps: InvestigationStep[] = [
  {
    id: 1,
    title: 'Initial Alert Triggered',
    description: 'New Relic indicated Evicted Pods exceeded threshold (>2 pods) for 30+ minutes.',
    timestamp: '2025-01-06T20:10:00.000Z',
  },
  {
    id: 2,
    title: 'Check K8s Events for Eviction Threshold',
    description: 'Queried InfrastructureEvent in New Relic for EvictionThresholdMet on the node.',
    timestamp: '2025-01-06T20:15:00.000Z',
  },
  {
    id: 3,
    title: 'Analyze Disk Utilization',
    description: 'Found fsCapacityUtilization consistently above 90% on node.',
    timestamp: '2025-01-06T20:20:00.000Z',
  },
  {
    id: 4,
    title: 'Corroborate with Node Logs',
    description: "Logs show repeated 'ImageGCFailed' and 'FreeDiskSpaceFailed' events.",
    timestamp: '2025-01-06T20:30:00.000Z',
  },
];

// Timeline events for the first incident
const podEvictionTimeline: TimelineEvent[] = [
  {
    id: '1',
    type: 'alert',
    timestamp: '2025-01-06T09:00:00.000Z',
    title: 'Alert Triggered',
    description: 'New Relic alert triggered for pod evictions exceeding threshold',
  },
  {
    id: '2',
    type: 'investigation',
    timestamp: '2025-01-06T09:15:00.000Z',
    title: 'Investigation Started',
    description: 'SRE team began investigating the pod eviction issue',
  },
  {
    id: '3',
    type: 'investigation',
    timestamp: '2025-01-06T09:30:00.000Z',
    title: 'Root Cause Identified',
    description: 'Disk pressure on node due to excessive logs and image buildup',
  },
  {
    id: '4',
    type: 'action',
    timestamp: '2025-01-06T10:00:00.000Z',
    title: 'Remediation Started',
    description: 'Applied cleanup scripts to remove unused Docker images',
  },
  {
    id: '5',
    type: 'update',
    timestamp: '2025-01-06T10:30:00.000Z',
    title: 'Status Update',
    description: 'Disk usage reduced to 70%, monitoring for recurrence',
  },
  {
    id: '6',
    type: 'update',
    timestamp: '2025-01-06T20:00:00.000Z',
    title: 'Problem Persisting',
    description: 'Disk usage climbing again, deeper investigation needed',
  },
  {
    id: '7',
    type: 'action',
    timestamp: '2025-01-06T20:30:00.000Z',
    title: 'Scaling Node Pool',
    description: 'Increasing node count to distribute disk usage more evenly',
  },
];

// Metrics for the first incident - using real data from the provided input
const generatePodEvictionMetric = (): Metric => {
  return {
    id: 'evicted-pods',
    name: 'Evicted Pods',
    data: [
      { timestamp: '2025-01-05T21:00:00.000Z', value: 1 },
      { timestamp: '2025-01-05T22:00:00.000Z', value: 1 },
      { timestamp: '2025-01-05T23:00:00.000Z', value: 1 },
      { timestamp: '2025-01-06T00:00:00.000Z', value: 1 },
      { timestamp: '2025-01-06T01:00:00.000Z', value: 1 },
      { timestamp: '2025-01-06T02:00:00.000Z', value: 1 },
      { timestamp: '2025-01-06T03:00:00.000Z', value: 1 },
      { timestamp: '2025-01-06T04:00:00.000Z', value: 1 },
      { timestamp: '2025-01-06T05:00:00.000Z', value: 1 },
      { timestamp: '2025-01-06T06:00:00.000Z', value: 1 },
      { timestamp: '2025-01-06T07:00:00.000Z', value: 1 },
      { timestamp: '2025-01-06T08:00:00.000Z', value: 1 },
      { timestamp: '2025-01-06T09:00:00.000Z', value: 64 },
      { timestamp: '2025-01-06T10:00:00.000Z', value: 64 },
      { timestamp: '2025-01-06T11:00:00.000Z', value: 64 },
      { timestamp: '2025-01-06T12:00:00.000Z', value: 64 },
      { timestamp: '2025-01-06T13:00:00.000Z', value: 64 },
      { timestamp: '2025-01-06T14:00:00.000Z', value: 64 },
      { timestamp: '2025-01-06T15:00:00.000Z', value: 64 },
      { timestamp: '2025-01-06T16:00:00.000Z', value: 64 },
      { timestamp: '2025-01-06T17:00:00.000Z', value: 64 },
      { timestamp: '2025-01-06T18:00:00.000Z', value: 64 },
      { timestamp: '2025-01-06T19:00:00.000Z', value: 64 },
      { timestamp: '2025-01-06T20:00:00.000Z', value: 64 },
    ],
    threshold: 2,
    unit: 'count'
  };
};

const generateDiskUtilizationMetric = (): Metric => {
  return {
    id: 'disk-utilization',
    name: 'Node Filesystem Utilization',
    data: [
      { timestamp: '2025-01-05T21:00:00.000Z', value: 85 },
      { timestamp: '2025-01-05T22:00:00.000Z', value: 85 },
      { timestamp: '2025-01-05T23:00:00.000Z', value: 85 },
      { timestamp: '2025-01-06T00:00:00.000Z', value: 85 },
      { timestamp: '2025-01-06T01:00:00.000Z', value: 85 },
      { timestamp: '2025-01-06T02:00:00.000Z', value: 85 },
      { timestamp: '2025-01-06T03:00:00.000Z', value: 85 },
      { timestamp: '2025-01-06T04:00:00.000Z', value: 85 },
      { timestamp: '2025-01-06T05:00:00.000Z', value: 85 },
      { timestamp: '2025-01-06T06:00:00.000Z', value: 85 },
      { timestamp: '2025-01-06T07:00:00.000Z', value: 85 },
      { timestamp: '2025-01-06T08:00:00.000Z', value: 85 },
      { timestamp: '2025-01-06T09:00:00.000Z', value: 95 },
      { timestamp: '2025-01-06T10:00:00.000Z', value: 95 },
      { timestamp: '2025-01-06T11:00:00.000Z', value: 95 },
      { timestamp: '2025-01-06T12:00:00.000Z', value: 95 },
      { timestamp: '2025-01-06T13:00:00.000Z', value: 95 },
      { timestamp: '2025-01-06T14:00:00.000Z', value: 95 },
      { timestamp: '2025-01-06T15:00:00.000Z', value: 95 },
      { timestamp: '2025-01-06T16:00:00.000Z', value: 95 },
      { timestamp: '2025-01-06T17:00:00.000Z', value: 92 },
      { timestamp: '2025-01-06T18:00:00.000Z', value: 92 },
      { timestamp: '2025-01-06T19:00:00.000Z', value: 92 },
      { timestamp: '2025-01-06T20:00:00.000Z', value: 92 },
    ],
    threshold: 85,
    unit: '%'
  };
};

const podEvictionMetrics = [
  generatePodEvictionMetric(),
  generateDiskUtilizationMetric(),
];

// Remediation actions for the first incident
const podEvictionActions = [
  {
    title: "Clean Up Disk Space",
    description: "Manually remove unnecessary images or logs on the node to alleviate disk pressure",
    steps: [
      "kubectl exec -it <pod-name> --namespace kube-system -- rm -rf /var/log/old-logs",
      "kubectl delete pod <old-completed-pod-name> --grace-period=0 --force --namespace login-app"
    ]
  },
  {
    title: "Optimize Image Garbage Collection",
    description: "Adjust Kubelet GC thresholds for images on node aks-default-39789032-vmss000ef4",
    steps: [
      "vim /etc/kubernetes/kubeletconfig.yaml to set lower imageGCHighThresholdPercent",
      "systemctl restart kubelet to apply the updated Kubelet configuration"
    ]
  },
  {
    title: "Scale Disk or Use Larger Node Pools",
    description: "Expand node storage capacity or add more nodes to distribute disk usage",
    steps: [
      "az aks nodepool scale --resource-group <rg> --cluster-name <cluster> --node-count 5",
      "az aks update --resource-group <rg> --name <cluster> --enable-managed-identity --attach-acr <new-storage>"
    ]
  }
];

// Logs for the first incident
const podEvictionLogs = [
  {
    timestamp: "2025-01-06T09:00:00.000Z",
    level: "warn",
    service: "kubelet",
    message: "Eviction manager: Eviction thresholds have been met for node aks-default-39789032-vmss000ef4, attempting to reclaim nodefs"
  },
  {
    timestamp: "2025-01-06T09:00:05.000Z",
    level: "error",
    service: "kubelet",
    message: "Failed to garbage collect required amount of images. Attempted to free 2608931635 bytes, but only found 372659173 bytes eligible to free."
  },
  {
    timestamp: "2025-01-06T09:00:10.000Z",
    level: "error",
    service: "kubelet",
    message: "FreeDiskSpaceFailed: failed to garbage collect required amount of images. Attempted to free 2458092339 bytes, but only found 628200529 bytes eligible to free."
  },
  {
    timestamp: "2025-01-06T09:00:15.000Z",
    level: "warn",
    service: "kubelet",
    message: "Eviction manager: attempting to reclaim nodefs"
  },
  {
    timestamp: "2025-01-06T09:00:20.000Z",
    level: "error",
    service: "kubelet",
    message: "Node aks-default-39789032-vmss000ef4 status is now: NodeHasDiskPressure"
  },
  {
    timestamp: "2025-01-06T09:00:25.000Z",
    level: "warn",
    service: "kube-controller",
    message: "Pod login-app-message-handler-service-6466d6dd8-5fvms is being evicted from node aks-default-39789032-vmss000ef4 due to disk pressure"
  },
  {
    timestamp: "2025-01-06T09:00:30.000Z",
    level: "info",
    service: "kube-scheduler",
    message: "Successfully deleted pod login-app-message-handler-service-6466d6dd8-5fvms"
  },
  {
    timestamp: "2025-01-06T09:00:35.000Z",
    level: "info",
    service: "kube-scheduler",
    message: "Attempting to schedule pod login-app-message-handler-service-6466d6dd8-new"
  },
  {
    timestamp: "2025-01-06T09:00:40.000Z",
    level: "warn",
    service: "kubelet",
    message: "Node aks-default-39789032-vmss000ef4 fs usage is above 90%, threshold is 85%"
  },
  {
    timestamp: "2025-01-06T09:00:45.000Z",
    level: "error",
    service: "kubelet",
    message: "ImageGCFailed: failed to garbage collect required amount of images"
  },
  {
    timestamp: "2025-01-06T09:00:50.000Z",
    level: "debug",
    service: "kubelet",
    message: "Summary: total=64 evicted pods on node aks-default-39789032-vmss000ef4"
  }
];

// Health checks for the second incident
const containerCrashHealthChecks: HealthCheck[] = [
  {
    id: '1',
    type: 'APPLICATION HEALTH',
    status: 'issues_found',
    message: 'Container crash loop detected in deployment-handler service',
  },
  {
    id: '2',
    type: 'CLUSTER HEALTH',
    status: 'all_good',
    message: 'No cluster-level issues detected',
  },
  {
    id: '3',
    type: 'RECENT DEPLOYMENT',
    status: 'issues_found',
    message: 'Recent deployment 15 minutes before crashes started',
  },
  {
    id: '4',
    type: 'SYNTHETIC MONITORS',
    status: 'issues_found',
    message: 'API endpoints failing with 500 errors',
  },
];

// Sample incidents data
export const incidents: Incident[] = [
  {
    id: '763222ee-0a9e-4dce-a14e-8b24b3a11020',
    title: 'Login App Message Handler Pod Eviction Alert',
    description: 'Multiple login-app-message-handler-service pods have been evicted in AKS https://aks-rg-uku-prd-neurons-a398f44f.hcp.uksouth.azmk8s.io:443. Alert triggered when Evicted Pods count exceeded threshold of 2.0 for over 30 minutes.',
    status: 'active',
    severity: 'critical',
    createdAt: '2025-01-06T09:00:00.000Z',
    updatedAt: '2025-01-06T20:30:00.000Z',
    teams: [
      { id: '1', name: 'Platform' },
      { id: '2', name: 'Login App' }
    ],
    source: { id: '1', name: 'New Relic' },
    healthChecks: podEvictionHealthChecks,
    investigationSteps: podEvictionSteps,
    timeline: podEvictionTimeline,
    metrics: podEvictionMetrics,
    customer: 'Neurons',
    affectedServices: ['login-app-message-handler-service'],
    rootCause: 'Pods evicted on node aks-default-39789032-vmss000ef4 due to high disk usage (>90% fs capacity utilization). Disk pressure triggered Kubernetes EvictionThresholdMet event.',
    preventiveActions: '1) Use larger or additional nodes to avoid high disk usage in the future \n2) Implement automated cleanup jobs for stale images/logs \n3) Monitor disk usage metrics with a stricter threshold (<80%) to prevent repeated evictions \n4) Consider ephemeral storage best practices \n e.g. ephemeral volumes only for short-lived data',
    remediationActions: podEvictionActions,
    logs: podEvictionLogs
  },
  {
    id: 'faace227-3b01-4c2d-b7a9-c8ff6bb9a712',
    title: 'Rollout Update Handler Container CrashLoopBackOff',
    description: 'Deployment handler containers are experiencing CrashLoopBackOff after recent update. Multiple containers are failing to start properly, causing service disruption.',
    status: 'active',
    severity: 'critical',
    createdAt: '2023-11-14T15:30:00Z',
    updatedAt: '2023-11-14T18:45:00Z',
    teams: [
      { id: '1', name: 'Platform' },
      { id: '3', name: 'Deployment Team' }
    ],
    source: { id: '1', name: 'New Relic' },
    healthChecks: containerCrashHealthChecks,
    customer: 'Neurons',
    affectedServices: ['deployment-handler-service', 'rollout-controller']
  }
];

// Summary stats
export const getIncidentStats = () => {
  const total = incidents.length;
  const active = incidents.filter(i => i.status === 'active').length;
  const resolved = incidents.filter(i => i.status === 'resolved').length;
  const critical = incidents.filter(i => i.severity === 'critical').length;
  
  return { total, active, resolved, critical };
};

// Helper function to get incident by ID
export const getIncidentById = (id: string): Incident | undefined => {
  return incidents.find(incident => incident.id === id);
};
