
import { subDays, startOfDay, addDays } from 'date-fns';

// Generate realistic incident trend data for the last 30 days
export interface DailyIncidentData {
  date: Date;
  total: number;
  critical: number;
  resolved: number;
}

// Generate 30 days of incident data with realistic patterns
export const generateIncidentTrendData = (days = 30): DailyIncidentData[] => {
  const today = startOfDay(new Date());
  const data: DailyIncidentData[] = [];
  
  // Base values and pattern factors
  let baseTrend = 8; // Average number of incidents
  const weekendFactor = 0.7; // Weekends have fewer incidents
  const randomVariance = 0.3; // Random daily variance
  
  // Spike factors (simulating a major incident period)
  const spikeStart = 7; // Start spike 7 days ago
  const spikeDuration = 3; // Spike lasts 3 days
  const spikeFactor = 2; // Spike doubles the number of incidents
  
  // Trend factors (simulating changing conditions over time)
  const trendFactor = 0.02; // Gradual increase trend
  
  // Seasonal weekly pattern (midweek peak)
  const weekdayFactors = [0.85, 1.1, 1.2, 1.1, 0.95, 0.75, 0.7]; // Sun-Sat
  
  // Resolution rate improvement (gradually improving over time)
  const resolutionImprovement = 0.005; // Small daily improvement
  
  // Generate data for each day
  for (let i = days - 1; i >= 0; i--) {
    const date = subDays(today, i);
    const dayOfWeek = date.getDay();
    const weekdayFactor = weekdayFactors[dayOfWeek];
    
    // Calculate time-based trend (gradual increase over time)
    const timeTrend = 1 + ((days - i) * trendFactor);
    
    // Calculate daily factors
    const randomFactor = 1 + (Math.random() * randomVariance * 2 - randomVariance);
    
    // Check if this is during the spike period
    const isSpike = i <= spikeStart && i > spikeStart - spikeDuration;
    const spikeDayFactor = isSpike ? spikeFactor : 1;
    
    // Calculate the base incident count
    const totalBase = Math.round(baseTrend * weekdayFactor * randomFactor * spikeDayFactor * timeTrend);
    
    // Critical incidents are about 15-25% of total
    // Higher on weekends (less staffing means more criticals)
    const criticalPercent = 0.15 + Math.random() * 0.1 + (dayOfWeek === 0 || dayOfWeek === 6 ? 0.05 : 0);
    const critical = Math.round(totalBase * criticalPercent);
    
    // Resolved is typically 70-90% of total for past days, but less for recent days
    // The more recent, the fewer resolved incidents
    const daysFromNow = i;
    const baseResolutionRate = 0.7 + (0.2 * (1 - (daysFromNow / days)));
    // Resolution rate improves over time (better processes)
    const improvedResolutionRate = baseResolutionRate + ((days - i) * resolutionImprovement);
    const resolutionRate = Math.min(0.95, improvedResolutionRate);
    const resolved = Math.round(totalBase * resolutionRate);
    
    data.push({
      date,
      total: totalBase,
      critical,
      resolved,
    });
  }
  
  return data;
};

// Add function to generate future projection data
export const generateProjectionData = (days = 7, pastData: DailyIncidentData[]): DailyIncidentData[] => {
  const lastDate = pastData[pastData.length - 1].date;
  const projectionData: DailyIncidentData[] = [];
  
  // Simple projection based on last 7 days average
  const last7Days = pastData.slice(-7);
  const avgTotal = Math.round(last7Days.reduce((acc, day) => acc + day.total, 0) / 7);
  const avgCritical = Math.round(last7Days.reduce((acc, day) => acc + day.critical, 0) / 7);
  const avgResolved = Math.round(last7Days.reduce((acc, day) => acc + day.resolved, 0) / 7);
  
  // Add small random variance to make it look natural
  for (let i = 1; i <= days; i++) {
    const date = addDays(lastDate, i);
    const dayOfWeek = date.getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    
    // Weekends have fewer incidents
    const dayFactor = isWeekend ? 0.7 : 1;
    // Random variance factor
    const randomFactor = 0.9 + (Math.random() * 0.2);
    
    projectionData.push({
      date,
      total: Math.round(avgTotal * dayFactor * randomFactor),
      critical: Math.round(avgCritical * dayFactor * randomFactor),
      resolved: Math.round(avgResolved * dayFactor * randomFactor * 1.1), // Slightly better resolution rate in projections
    });
  }
  
  return projectionData;
};

// Export the generated data
export const incidentTrendData = generateIncidentTrendData();

// Export projected data
export const projectedTrendData = [...incidentTrendData, ...generateProjectionData(7, incidentTrendData)];
