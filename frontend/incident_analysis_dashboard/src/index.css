
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 271.4 91.7% 57.8%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 271.4 91.7% 57.8%;

    --radius: 0.5rem;

    --alert-critical: 0 84.2% 60.2%;
    --alert-warning: 38 92% 50%;
    --alert-info: 221.2 83.2% 53.3%;
    --alert-success: 142.1 76.2% 36.3%;

    --alert-criticalBg: 0 85.7% 97.3%;
    --alert-warningBg: 48 96.5% 97.3%;
    --alert-infoBg: 217.2 100% 97.3%;
    --alert-successBg: 138.5 76.5% 97.3%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 271.4 91.7% 57.8%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 263.4 70% 50.4%;

    --alert-critical: 0 62.8% 30.6%;
    --alert-warning: 38 92% 50%;
    --alert-info: 217.2 91.2% 59.8%;
    --alert-success: 142.1 70.6% 45.3%;

    --alert-criticalBg: 0 85.7% 97.3%;
    --alert-warningBg: 48 96.5% 97.3%;
    --alert-infoBg: 217.2 100% 97.3%;
    --alert-successBg: 138.5 76.5% 97.3%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Custom animations */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.text-alert-critical {
  color: hsl(var(--alert-critical));
}

.text-alert-warning {
  color: hsl(var(--alert-warning));
}

.text-alert-info {
  color: hsl(var(--alert-info));
}

.text-alert-success {
  color: hsl(var(--alert-success));
}

.bg-alert-criticalBg {
  background-color: hsl(var(--alert-criticalBg));
}

.bg-alert-warningBg {
  background-color: hsl(var(--alert-warningBg));
}

.bg-alert-infoBg {
  background-color: hsl(var(--alert-infoBg));
}

.bg-alert-successBg {
  background-color: hsl(var(--alert-successBg));
}

.timeline-marker {
  @apply absolute top-0 left-0 w-6 h-6 rounded-full bg-secondary flex items-center justify-center z-10;
}

.timeline-line {
  @apply absolute top-0 left-3 w-px h-full bg-border -z-10;
}

/* Dotted pattern for topology backgrounds */
.dot-pattern {
  background-color: #f8fafc;
  background-image: radial-gradient(#cbd5e1 0.5px, transparent 0.5px);
  background-size: 12px 12px;
}

.dark .dot-pattern {
  background-color: #1e293b;
  background-image: radial-gradient(#475569 0.5px, transparent 0.5px);
  background-size: 12px 12px;
}

/* Red to purple gradient text */
.text-gradient {
  @apply bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent;
}

/* Glow effects */
.glow {
  box-shadow: 0 0 15px rgba(156, 39, 176, 0.3);
}

.dark .glow {
  box-shadow: 0 0 15px rgba(156, 39, 176, 0.5);
}

.glow-red {
  box-shadow: 0 0 15px rgba(220, 38, 38, 0.3);
}

.dark .glow-red {
  box-shadow: 0 0 15px rgba(220, 38, 38, 0.5);
}

/* Timeline background when printed */
@media print {
  .timeline-line {
    @apply bg-gray-300;
  }
  
  .timeline-marker {
    @apply bg-white border border-gray-300;
  }
}
