
export interface TopologyNode {
  id: string;
  name: string;
  issue?: boolean;
  group?: number;
  icon?: string;
  properties?: Record<string, any>;
  alt_names?: string[];
  entity_id?: string;
  type?: string;
  entityType?: string;
  domain?: string;
  data_source?: string;
  color?: string;
  x?: number;
  y?: number;
}

export interface TopologyLink {
  id?: string;
  source: string;
  target: string;
  link_type?: string;
  properties?: Record<string, any>;
  data_source?: string;
  value?: number;
  curvature?: number;
}

export interface TopologyData {
  nodes: TopologyNode[];
  links: TopologyLink[];
}
