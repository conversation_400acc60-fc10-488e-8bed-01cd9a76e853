
export type Severity = 'critical' | 'warning' | 'info' | 'low';
export type Status = 'active' | 'resolved' | 'investigating';

export interface Team {
  id: string;
  name: string;
}

export interface Source {
  id: string;
  name: string;
}

export interface HealthCheck {
  id: string;
  type: string;
  status: 'issues_found' | 'all_good';
  message: string;
  details?: string;
}

export interface InvestigationStep {
  id: number;
  title: string;
  description: string;
  timestamp: string;
}

export interface TimelineEvent {
  id: string;
  type: 'alert' | 'action' | 'update' | 'investigation';
  timestamp: string;
  title: string;
  description: string;
}

export interface MetricPoint {
  timestamp: string;
  value: number;
}

export interface Metric {
  id: string;
  name: string;
  data: MetricPoint[];
  threshold?: number;
  unit: string;
}

export interface RemediationAction {
  title: string;
  description: string;
  steps: string[];
}

export interface LogEntry {
  timestamp: string;
  level: string;
  service: string;
  message: string;
}

export interface Incident {
  id: string;
  title: string;
  description: string;
  status: Status;
  severity: Severity;
  createdAt: string;
  updatedAt: string;
  teams: Team[];
  source: Source;
  healthChecks?: HealthCheck[];
  investigationSteps?: InvestigationStep[];
  timeline?: TimelineEvent[];
  metrics?: Metric[];
  customer?: string;
  affectedServices?: string[];
  rootCause?: string;
  preventiveActions?: string;
  remediationActions?: RemediationAction[];
  logs?: LogEntry[];
}
