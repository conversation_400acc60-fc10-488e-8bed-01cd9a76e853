
import { useQuery } from '@tanstack/react-query';
import { fetchIncidentById, fetchTopologyData } from '@/services/apiService';
import { Incident } from '@/types/incident';
import { TopologyData } from '@/types/topology';

export function useIncident(id: string) {
  const incidentQuery = useQuery({
    queryKey: ['incident', id],
    queryFn: () => fetchIncidentById(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const topologyQuery = useQuery({
    queryKey: ['topology', id],
    queryFn: () => fetchTopologyData(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    incident: incidentQuery.data,
    topology: topologyQuery.data,
    isLoading: incidentQuery.isLoading || topologyQuery.isLoading,
    error: incidentQuery.error || topologyQuery.error,
    refetch: () => {
      incidentQuery.refetch();
      topologyQuery.refetch();
    }
  };
}
