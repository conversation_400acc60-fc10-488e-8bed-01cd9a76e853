# Incident Analysis Dashboard

## Project Overview

This is a React-based incident analysis dashboard built with modern web technologies.

## Prerequisites

Make sure you have Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

## Getting Started

Follow these steps to run the project locally:

```sh
# Step 1: Clone the repository
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory
cd incident_analysis_dashboard

# Step 3: Install the necessary dependencies
npm i

# Step 4: Start the development server with auto-reloading and an instant preview
npm run dev
```

## Technologies Used

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the project for production
- `npm run preview` - Preview the production build locally

## Development

The development server will start on `http://localhost:5173` by default. The page will automatically reload when you make changes to the code.

## Building for Production

To create a production build:

```sh
npm run build
```

The built files will be in the `dist` directory.
