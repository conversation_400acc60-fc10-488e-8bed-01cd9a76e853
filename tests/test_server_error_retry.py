"""
Minimal test script for testing the retry functionality with server errors
"""
import os
import dotenv
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.utils import retry_on_error
from lib.new_relic.base import NewRelicGraphQLError

dotenv.load_dotenv()

# Get API key from environment
API_KEY = os.environ.get('NEW_RELIC_API_KEY')
ACCOUNT_ID = os.environ.get('NEW_RELIC_ACCOUNT_ID')

# Test entity GUID
ENTITY_GUID = "MTA5MzYyMHxJTkZSQXxOQXwtNDcwMjA1MjUyNDEyNjYwNDEwNg"

# Example function that would trigger a server error
@retry_on_error(max_attempts=3, initial_delay=1, backoff_factor=2)
def test_query_that_causes_server_error(client):
    """Execute a query that will likely cause a SERVER_ERROR to test the retry mechanism"""
    print("Executing a query that might cause a server error...")
    
    # This intentionally complex query across many tables often causes server errors
    query = """
    query($nrql: Nrql!) {
        actor {
            account(id: 1093620) {
                nrql(query: $nrql) {
                    results
                }
            }
        }
    }
    """
    
    variables = {
        "nrql": "SELECT timestamp, level, message, entity.name, entity.type FROM Log, Log_Access_AZ_CA1, Log_Access_AZ_UK1, Log_CNS, Log_GlobalProd, Log_ISM, Log_Incapptic, Log_Landesk, Log_MDM_Sandbox, Log_MI_AP1, Log_MI_AP2, Log_MI_AP2_Access, Log_MI_AP_GS_Azure, Log_MI_NA1, Log_MI_NA2, Log_MI_NA2_Access, Log_MI_NA_GS_Azure, Log_MI_SB_GS_Azure, Log_MI_Sandbox_Access, Log_NMDM_AZ_CA1, Log_NMDM_AZ_CA1_Managed, Log_NMDM_AZ_SB, Log_NMDM_AZ_SB_Managed, Log_NMDM_AZ_UK1, Log_NMDM_AZ_UK1_Managed, Log_Neurons_MLU, Log_Neurons_NVU, Log_Neurons_TKU, Log_Neurons_TTU, Log_Neurons_UKU, Log_RiskSense, Log_VNS, Log_GlobalStaging, Log_MI_AP_GS, Log_MI_NA_GS, Log_MiProdCluster, Log_MI_SB_GS, Log_MiSbStaging, Log_MI_STAGING, Log_MI_STAGING_GS, Log_nmdm_az_sc_eu2 WHERE `entity.guid` = 'MTA5MzYyMHxJTkZSQXxOQXwtNDcwMjA1MjUyNDEyNjYwNDEwNg' SINCE '1 hour ago' LIMIT 500"
    }
    
    response = client.execute_query(query, variables, debug=True)
    return response.data

# Main test function
def main():
    if not API_KEY or not ACCOUNT_ID:
        print("ERROR: Environment variables NEW_RELIC_API_KEY and NEW_RELIC_ACCOUNT_ID must be set.")
        return 1
    
    print("Creating NewRelicGraphQLClient...")
    client = NewRelicGraphQLClient(
        api_key=API_KEY, 
        account_id=ACCOUNT_ID,
        debug=True
    )
    
    print("Testing retry functionality with a query that may cause server errors...")
    try:
        result = test_query_that_causes_server_error(client)
        print("Query succeeded! (This is unexpected)")
        print(result)
    except NewRelicGraphQLError as e:
        print(f"Query failed after retries as expected: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
    
    print("Test completed.")
    return 0

if __name__ == "__main__":
    main() 