"""
Integration test for entity relationship service using real entity GUIDs.

This test uses actual New Relic entity GUIDs to test the relationship services.
"""

import os
import unittest
import logging
from datetime import datetime, timedelta
import dotenv

# Load environment variables
dotenv.load_dotenv()

# Add project root to path
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.analyzer import EntityAnalyzer
from ai_incident_manager.services.entity_relationship_service import (
    EntityRelationshipService,
)
from ai_incident_manager.services.alert_to_entity_resolver import AlertToEntityResolver

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestRealEntityRelationships(unittest.TestCase):
    """
    Integration tests for the entity relationship service using real entity GUIDs.

    Note: These tests require valid New Relic API credentials to run.
    """

    @classmethod
    def setUpClass(cls):
        """
        Set up test fixtures for the class.
        Initializes New Relic clients and services.
        """
        # Check for New Relic API credentials
        api_key = os.environ.get("NEWRELIC_API_KEY")
        account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")

        if not api_key or not account_id:
            logger.warning(
                "NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID environment variables are required to run integration tests."
            )
            raise unittest.SkipTest("Missing New Relic API credentials")

        # Set up New Relic clients and analyzers
        cls.graphql_client = NewRelicGraphQLClient(
            api_key=api_key, account_id=account_id
        )
        cls.query_client = NewRelicQueryClient(cls.graphql_client)
        cls.analyzer = EntityAnalyzer(cls.graphql_client, debug=True)

        # Set up entity relationship service
        cls.relationship_service = EntityRelationshipService(
            config_path="config/entity_relationships.yaml"
        )
        cls.relationship_service.query_client = cls.query_client

        # Set up alert to entity resolver
        cls.entity_resolver = AlertToEntityResolver()
        cls.entity_resolver.relationship_service = cls.relationship_service

        # Test entity GUID and info
        cls.test_entity_guid = "MTA5MzYyMHxJTkZSQXxOQXwtNTY4MTU0NzYxNTQ2NjE1NDQ1Nw"  # example pod entity GUID

        # Get entity details for the test GUID to use in tests
        try:
            cls.entity_details = cls.query_client.get_entity_details(
                cls.test_entity_guid
            )
            if not cls.entity_details:
                logger.warning(
                    f"Could not get entity details for GUID: {cls.test_entity_guid}"
                )
                raise unittest.SkipTest(
                    f"Could not get entity details for GUID: {cls.test_entity_guid}"
                )

            cls.entity_name = cls.entity_details.get("name", "unknown")
            cls.entity_type = cls.entity_details.get("type", "unknown")

            # Extract cluster name from tags
            cls.cluster_name = "unknown"
            for tag in cls.entity_details.get("tags", []):
                if (
                    tag.get("key") == "clusterName"
                    or tag.get("key") == "k8s.clusterName"
                ):
                    cls.cluster_name = tag.get("values", ["unknown"])[0]
                    break

            logger.info(
                f"Set up test with entity: {cls.entity_name} ({cls.entity_type}) in cluster: {cls.cluster_name}"
            )

        except Exception as e:
            logger.error(f"Error during setup: {str(e)}")
            raise unittest.SkipTest(f"Error during setup: {str(e)}")

    def test_entity_details(self):
        """Test fetching entity details from New Relic."""
        self.assertIsNotNone(self.entity_details)
        self.assertEqual(self.entity_details.get("guid"), self.test_entity_guid)

        logger.info(f"Entity details: {self.entity_name} ({self.entity_type})")
        # Print some entity information for verification
        print(f"\nEntity Name: {self.entity_name}")
        print(f"Entity Type: {self.entity_type}")
        print(f"Cluster Name: {self.cluster_name}")

        # Print some tags for context
        print("\nEntity Tags:")
        for tag in self.entity_details.get("tags", [])[:5]:  # First 5 tags
            print(f"  {tag.get('key')}: {tag.get('values')}")

    def test_nr_relationships(self):
        """Test getting New Relic relationships for the entity."""
        # Get time window in milliseconds
        now = datetime.now()
        since_time = now - timedelta(hours=1)
        since_ms = int(since_time.timestamp() * 1000)
        until_ms = int(now.timestamp() * 1000)

        # Create a params dictionary with all necessary parameters
        params = {
            "entity_name": self.entity_name,
            "cluster_name": self.cluster_name,
            "since_time_ms": since_ms,
            "until_time_ms": until_ms,
        }

        # Get New Relic relationships with all parameters
        relationships = self.relationship_service.get_nr_relationships(
            self.entity_type,
            self.entity_name,
            cluster_name=self.cluster_name,
            since_time_ms=since_ms,
            until_time_ms=until_ms,
            additional_params=params,
        )

        # Log for visibility
        logger.info(f"Found {len(relationships)} New Relic relationships")

        # Print relationship details for verification
        print(f"\nNew Relic Relationships for {self.entity_name}:")
        for i, rel in enumerate(relationships):
            print(f"  Relationship {i + 1}:")
            print(f"    Type: {rel.get('related_type')}")
            print(f"    Relation: {rel.get('relation')}")
            print(f"    Source: {rel.get('relationship_source')}")

            # Print some query results
            query_results = rel.get("query_results", [])
            if query_results:
                print(f"    Results: {len(query_results)} entities")                
                # Print all results
                if len(query_results) > 0:
                    for result in query_results:
                        print(f"      Result: {result}")

    def test_get_entity_details_async(self):
        """Test the asynchronous method for getting entity details."""
        import asyncio

        async def run_test():
            result = await self.analyzer.get_entity_details(self.test_entity_guid)
            return result

        # Run the async test
        entity_details = asyncio.run(run_test())

        self.assertIsNotNone(entity_details)
        self.assertEqual(entity_details.get("guid"), self.test_entity_guid)

    def test_get_related_entities_async(self):
        """Test the asynchronous method for getting related entities."""
        import asyncio

        async def run_test():
            # Get entity details first
            entity_details = await self.analyzer.get_entity_details(
                self.test_entity_guid
            )

            if not entity_details:
                logger.warning(
                    f"Could not get entity details for test_get_related_entities_async"
                )
                return []

            # Then manually create params dictionary with all fields needed
            now = datetime.now()
            since_time = now - timedelta(hours=1)

            # Convert to milliseconds
            since_ms = int(since_time.timestamp() * 1000)
            until_ms = int(now.timestamp() * 1000)

            # Create params with cluster name
            params = {
                "entity_name": entity_details.get("name", ""),
                "cluster_name": self.cluster_name,
                "since_time_ms": since_ms,
                "until_time_ms": until_ms,
            }

            # Use relationship service directly with complete parameters
            if hasattr(self.analyzer, "relationship_service"):
                result = self.analyzer.relationship_service.get_related_entities(
                    self.test_entity_guid,
                    entity_details.get("type", ""),
                    entity_details.get("name", ""),
                    cluster_name=self.cluster_name,
                    since_time_ms=since_ms,
                    until_time_ms=until_ms,
                )
                return result
            else:
                # Original method (might fail due to missing cluster_name)
                result = await self.analyzer.get_related_entities(self.test_entity_guid)
                return result

        # Run the async test
        related_entities = asyncio.run(run_test())

        logger.info(f"Found {len(related_entities)} related entities in async call")

        # Print some related entities for verification
        print(f"\nRelated Entities for {self.entity_name}:")
        for i, entity in enumerate(related_entities[:3]):  # First 3 related entities
            print(f"  Related Entity {i + 1}:")
            print(f"    Name: {entity.get('name', 'unknown')}")
            print(f"    Type: {entity.get('type', 'unknown')}")
            print(f"    Relationship: {entity.get('relationship_type', 'unknown')}")

    def test_alert_to_entity_resolution(self):
        """Test resolving an alert with this entity GUID."""
        # Create a mock alert containing the test entity GUID
        alert_data = {
            "title": "Test Alert",
            "condition_name": "Test Condition",
            "entity": {"guid": self.test_entity_guid},
        }

        # Resolve the alert to entities
        result = self.entity_resolver.resolve_alert_to_entities(alert_data)

        self.assertIsNotNone(result)
        self.assertTrue(len(result) > 0)

        # Check that the first entity has the correct GUID
        self.assertEqual(result[0]["entity_guid"], self.test_entity_guid)

        # Print the resolution results
        print(f"\nAlert Resolution Results:")
        for i, entity in enumerate(result):
            print(f"  Entity {i + 1}:")
            print(f"    GUID: {entity.get('entity_guid', 'unknown')}")
            print(f"    Name: {entity.get('entity_name', 'unknown')}")
            print(f"    Source: {entity.get('relationship_source', 'unknown')}")

            # Print metrics to collect if available
            if "metrics_to_collect" in entity:
                print(f"    Metrics to collect: {entity.get('metrics_to_collect')}")

    def test_analyze_entity(self):
        """Test analyzing the entity with the EntityAnalyzer."""
        import asyncio

        async def run_test():
            # Define time window
            now = datetime.now()
            since_time = now - timedelta(minutes=30)

            # Analyze the entity without collecting metrics, logs, events
            result = await self.analyzer.analyze_entity(
                entity_guid=self.test_entity_guid,
                since_time=since_time,
                until_time=now,
                collect_metrics=False,
                collect_logs=False,
                collect_events=False,
            )
            return result

        # Run the async test
        result = asyncio.run(run_test())

        self.assertIsNotNone(result)
        self.assertEqual(result.get("entity_guid"), self.test_entity_guid)

        # Print some analysis results for verification
        print(f"\nEntity Analysis Results:")
        print(f"  Entity Name: {result.get('entity_name', 'unknown')}")
        print(f"  Entity Type: {result.get('entity_type', 'unknown')}")

        # Print relationships
        relationships = result.get("relationships", [])
        print(f"  Relationships: {len(relationships)}")
        for i, rel in enumerate(relationships[:2]):  # First 2 relationships
            print(
                f"    Relationship {i + 1}: {rel.get('related_type')} - {rel.get('relation')}"
            )


if __name__ == "__main__":
    unittest.main()
