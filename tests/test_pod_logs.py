#!/usr/bin/env python3
"""
Test script for retrieving logs from New Relic for a specific pod entity.
"""

import os
import logging
from datetime import datetime, timezone, timedelta
import dotenv
import json
import sys

# Import New Relic client classes
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.base import Region
from lib.new_relic.logs import NewRelicLogsClient

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def main():
    """Main function to test New Relic logs retrieval."""
    # Get required environment variables
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        logger.error("Missing required environment variables: NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set.")
        return
    
    # Set pod name to test (the one from the logs that showed success)
    pod_name = "nalp-patch-deployment-history-ft-job-dgpqt-zg5sr"
    
    # Define time range for logs (last 48 hours)
    until_time = datetime.now(timezone.utc)
    since_time = until_time - timedelta(hours=48)
    
    logger.info(f"Testing log retrieval for pod name: {pod_name}")
    logger.info(f"Time range: {since_time.isoformat()} to {until_time.isoformat()}")
    
    # Initialize the New Relic GraphQL client
    nr_client = NewRelicGraphQLClient(
        api_key=api_key,
        account_id=account_id,
        region=Region.US,  # Use US region by default - change if your account is in EU
        debug=True,
        debug_request=True,
        debug_response=True
    )
    
    # Initialize the New Relic Logs client
    logs_client = NewRelicLogsClient(client=nr_client, debug=True)
    
    try:
        # Try with single partitions to avoid query length issues
        logger.info("Testing log retrieval with single partitions")
        
        # List of partitions to try
        partitions_to_try = ["Log", "Log_Neurons_NVU", "Log_MI_NA1", "Log_MI_NA2"]
        
        for partition in partitions_to_try:
            logger.info(f"\n===== Testing partition: {partition} =====")
            
            try:
                # Use a direct NRQL query with explicit time range
                query = f"""
                SELECT timestamp, level, message, pod_name, container_name
                FROM {partition}
                WHERE pod_name = '{pod_name}' OR kubernetes.pod.name = '{pod_name}'
                SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
                UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
                LIMIT 10
                """
                
                logger.info(f"Executing direct query on partition {partition}")
                logs = logs_client.query_logs(
                    query=query,
                    limit=10,
                    account_id=account_id
                )
                
                logger.info(f"Retrieved {len(logs)} logs from {partition}")
                
                # If we found logs, print them and exit the loop
                if logs:
                    print_logs(logs)
                    break
            except Exception as partition_e:
                logger.warning(f"Error querying partition {partition}: {str(partition_e)}")
        
        # If we didn't find any logs from individual partitions, try get_pod_logs with default partition
        logger.info("\n===== Testing get_pod_logs with default partition =====")
        pod_logs = logs_client.get_pod_logs(
            pod_name=pod_name,
            cluster_partition="default",  # Use default partition instead of "all"
            since=since_time,
            until=until_time,
            limit=10,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(pod_logs)} logs using get_pod_logs with default partition")
        print_logs(pod_logs)
        
    except Exception as e:
        logger.error(f"Error retrieving logs: {str(e)}", exc_info=True)

def print_logs(logs):
    """Pretty print a subset of logs for readability."""
    if not logs:
        logger.info("No logs found to print")
        return
    
    for i, log in enumerate(logs):
        logger.info(f"Log {i+1}:")
        # Format the log as a pretty-printed JSON string
        formatted_log = json.dumps(log, indent=2, default=str)
        logger.info(formatted_log)
        logger.info("-" * 80)

if __name__ == "__main__":
    main() 