'''
This script fetches all conditions from New Relic which are enabled and have Signal Loss set to true and saves them to a CSV file.
'''
import requests
import json
import os
import csv
import argparse
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

from dotenv import load_dotenv

load_dotenv()

console = Console()

class NewRelicConditionFetcher:
    def __init__(self, api_key, account_id, region='US'):
        self.api_key = api_key
        self.account_id = account_id
        self.api_endpoint = "https://api.newrelic.com/graphql" if region == 'US' else "https://api.eu.newrelic.com/graphql"

    def fetch_conditions(self, cursor=None, search_text=None, fetch_type='signal_loss'):
        query = self._get_query(fetch_type)
        variables = {
            "accountId": self.account_id,
            "cursor": cursor
        }
        if search_text:
            variables["searchText"] = search_text

        headers = {
            "Content-Type": "application/json",
            "API-Key": self.api_key
        }

        response = requests.post(self.api_endpoint, json={"query": query, "variables": variables}, headers=headers)
        return response.json()

    def _get_query(self, fetch_type):
        if fetch_type == 'signal_loss':
            return '''
            query ($accountId: Int!, $cursor: String) {
              actor {
                account(id: $accountId) {
                  alerts {
                    nrqlConditionsSearch(cursor: $cursor) {
                      nrqlConditions {
                        expiration {
                          openViolationOnExpiration
                        }
                        nrql {
                          query
                        }
                        id
                        name
                        policyId
                        enabled
                      }
                      nextCursor
                    }
                  }
                }
              }
            }
            '''
        elif fetch_type == 'search_query':
            return '''
            query ($accountId: Int!, $cursor: String, $searchText: String!) {
              actor {
                account(id: $accountId) {
                  alerts {
                    nrqlConditionsSearch(cursor: $cursor, searchCriteria: {queryLike: $searchText}) {
                      nrqlConditions {
                        id
                        name
                        nrql {
                          query
                        }
                        policyId
                        enabled
                      }
                      nextCursor
                    }
                  }
                }
              }
            }
            '''

def save_to_csv(conditions, filename="new_relic_conditions.csv"):
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['id', 'name', 'policyId', 'query', 'enabled']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for condition in conditions:
            writer.writerow({
                'id': condition['id'],
                'name': condition['name'],
                'policyId': condition.get('policyId', 'N/A'),
                'query': condition['nrql']['query'],
                'enabled': condition['enabled']
            })
    console.print(f"CSV file '{filename}' has been created.", style="green")

def print_conditions(conditions):
    table = Table(title="New Relic Conditions")
    
    table.add_column("ID", style="cyan")
    table.add_column("Name", style="magenta")
    table.add_column("Policy ID", style="green")
    table.add_column("Query", style="yellow")
    table.add_column("Enabled", style="blue")
    
    for condition in conditions:
        table.add_row(
            condition['id'],
            condition['name'],
            condition.get('policyId', 'N/A'),
            condition['nrql']['query'],
            str(condition['enabled'])
        )
    
    console.print(Panel(table, expand=False))

def main(api_key, account_id, region, fetch_type, search_text):
    fetcher = NewRelicConditionFetcher(api_key, account_id, region)
    all_conditions = []
    cursor = None
    request_count = 0
    total_conditions_processed = 0
    
    console.print(f"Starting to fetch New Relic conditions for account {account_id}...", style="bold blue")
    
    while True:
        request_count += 1
        console.print(f"Sending request #{request_count}...", style="dim")
        
        data = fetcher.fetch_conditions(cursor, search_text, fetch_type)
        conditions = data["data"]["actor"]["account"]["alerts"]["nrqlConditionsSearch"]["nrqlConditions"]
        
        total_conditions_processed += len(conditions)
        console.print(f"Processed {len(conditions)} conditions in this batch.", style="dim")
        console.print(f"Total conditions processed so far: {total_conditions_processed}", style="dim")
        
        matching_conditions = 0
        for condition in conditions:
            if fetch_type == 'signal_loss':
                if condition["enabled"] and condition["expiration"]["openViolationOnExpiration"]:
                    all_conditions.append(condition)
                    matching_conditions += 1
            elif fetch_type == 'search_query':
                if condition["enabled"]:
                    all_conditions.append(condition)
                    matching_conditions += 1
        
        console.print(f"Found {matching_conditions} matching conditions in this batch.", style="dim")
        console.print(f"Total matching conditions so far: {len(all_conditions)}", style="dim")
        
        cursor = data["data"]["actor"]["account"]["alerts"]["nrqlConditionsSearch"]["nextCursor"]
        
        if not cursor:
            console.print("No more pages to fetch. Completed fetching all conditions.", style="dim green")
            break
        else:
            console.print(f"Next cursor: {cursor}", style="dim")
    
    console.print(f"Total API requests sent: {request_count}", style="dim")
    console.print(f"Total conditions processed: {total_conditions_processed}", style="dim")
    console.print(f"Total matching conditions: {len(all_conditions)}", style="dim")
    
    # Save to CSV
    save_to_csv(all_conditions)
    
    # Print conditions using rich
    print_conditions(all_conditions)

if __name__ == "__main__":
    # parser = argparse.ArgumentParser(description="Fetch New Relic conditions based on specified criteria.")
    # parser.add_argument("--account-id", type=int, help="New Relic account ID", default=1093620)
    # parser.add_argument("--region", choices=['US', 'EU'], default='US', help="New Relic region (US or EU)")
    # parser.add_argument("--fetch-type", choices=['signal_loss', 'search_query'], required=True, help="Type of fetch operation", default='signal_loss')
    # parser.add_argument("--search-text", help="Search text for query (required if fetch-type is search_query)")
    # args = parser.parse_args()

    # api_key = os.getenv("NEWRELIC_API_KEY")
    # if not api_key:
    #     console.print("Error: NEWRELIC_API_KEY environment variable is not set.", style="bold red")
    #     exit(1)

    # if args.fetch_type == 'search_query' and not args.search_text:
    #     console.print("Error: --search-text is required when fetch-type is search_query", style="bold red")
    #     exit(1)

    api_key = os.getenv("NEWRELIC_API_KEY")
    if not api_key:
        console.print("Error: NEWRELIC_API_KEY environment variable is not set.", style="bold red")
        exit(1)
    
    
    # List of accounts to fetch conditions for
    accounts = [
        {"account_id": 1093620, "region": "US"}
    ]
    # search_text_list = [
    #     "CM_AKSNodePoolCount",
    #     "elasticPoolDataStorage"
    #     "rapidApiStatistics",
    #     "salesforceApiEvents",
    #     "serviceBusMessageCountCM",
    #     "serviceBusQueueMessageCountCM",
    #     "serviceBusTopicUsageCM",
    #     "serviceBusQueueUsageCM",
    # ]
    search_text_list = [
        "AzureServiceBusTopicSample"
    ]

    # Fetch conditions for each account and search text
    for account in accounts:
        for search_text in search_text_list:
            main(api_key, account["account_id"], account["region"], "search_query", search_text)

    # Fetch condition for signal loss
    for account in accounts:
        main(api_key, account["account_id"], account["region"], "signal_loss", None)

    # main(api_key, args.account_id, args.region, args.fetch_type, args.search_text)