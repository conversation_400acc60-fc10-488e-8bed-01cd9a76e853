#!/usr/bin/env python3
"""
Incremental test for the incident analysis workflow using LangGraph.

This script runs a test of the incident analysis workflow using a real
CrashLoopBackOff alert to verify the entity relationship traversal functionality.
"""

import os
import logging
import asyncio
from datetime import datetime, timezone
import uuid
import dotenv

from langgraph.checkpoint.memory import MemorySaver


# Import the workflow components from test_incremental_graph.py
from ai_incident_manager.workflow.test_incremental_graph import (
    create_test_workflow, 
    IncidentState
)

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_crashloop_workflow():
    """Test the workflow execution with a CrashLoopBackOff alert."""
    # Create the workflow
    workflow = create_test_workflow()
    
    # Test data for a Pod with CrashLoopBackOff alert
    # test_data = {"issueId": "58eaa4fc-4a6b-4012-b0f2-a55c01bcd1d6", "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/58eaa4fc-4a6b-4012-b0f2-a55c01bcd1d6?notifier=WEBHOOK", "title": "agent-management-background-container-service query result is > 10.0 on 'Pod with CrashLoopBackOff -- '", "priority": "CRITICAL", "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtOTAzODY4MDkwNTY4NDkwMDMxMg"], "impactedEntities": ["agent-management-background-container-service"], "totalIncidents": "1", "state": "ACTIVATED", "trigger": "STATE_CHANGE", "isCorrelated": "false", "createdAt": *************, "updatedAt": *************, "sources": ["newrelic"], "alertPolicyNames": ["Neurons k8s Infra - Critical"], "alertConditionNames": ["Pod with CrashLoopBackOff -- "], "workflowName": "obv-ai-processing-neurons", "chartLink": "https://gorgon.nr-assets.net/image/a6e635a7-80be-445d-825b-2f3ca0a37651?config.legend.enabled=false&width=400&height=210", "product": "neurons", "nr_region": "us", "alert_condition_id": "********"}
    test_data = {"issueId": "ea8ecc6f-83e8-4751-8b22-d1f9e0059030", "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/ea8ecc6f-83e8-4751-8b22-d1f9e0059030?notifier=WEBHOOK", "title": "twistlock-defender query result is > 10.0 on 'Pod with CrashLoopBackOff -- '", "priority": "CRITICAL", "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwxNzgwOTE2OTQ1MDM3ODI1NTI4"], "impactedEntities": ["twistlock-defender"], "totalIncidents": "1", "state": "ACTIVATED", "trigger": "STATE_CHANGE", "isCorrelated": "false", "createdAt": *************, "updatedAt": *************, "sources": ["newrelic"], "alertPolicyNames": ["Neurons k8s Infra - Critical"], "alertConditionNames": ["Pod with CrashLoopBackOff -- "], "workflowName": "obv-ai-processing-neurons", "chartLink": "https://gorgon.nr-assets.net/image/fc2ad5de-7364-4dbe-a563-3ed4daa40ed1?config.legend.enabled=false&width=400&height=210", "product": "neurons", "nr_region": "us", "alert_condition_id": "********"}
    # test_data = {"issueId": "4ba5ca43-e0aa-4974-b1fa-81f76d74bf1e", "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/4ba5ca43-e0aa-4974-b1fa-81f76d74bf1e?notifier=WEBHOOK", "title": "devices_workers_dbserver1.public.mdm_channel_8 query result is > 15000.0 for 30 minutes on 'Debezium Lagging High on AP1'", "priority": "CRITICAL", "EntityId": [""], "impactedEntities": ["devices_workers_dbserver1.public.mdm_channel_8"], "totalIncidents": "1", "state": "ACTIVATED", "trigger": "STATE_CHANGE", "isCorrelated": "false", "createdAt": *************, "updatedAt": *************, "sources": ["newrelic"], "alertPolicyNames": ["MI Alert - Kafka "], "alertConditionNames": ["Debezium Lagging High on AP1"], "workflowName": "obv-ai-processing", "chartLink": "https://gorgon.nr-assets.net/image/15305e03-1063-4a56-bbd7-8b1eb2e198dd?config.legend.enabled=false&width=400&height=210", "product": "MDM", "nr_region": "us", "alert_condition_id": "********"}

    # Initialize proper state structure for the workflow
    incident_id = test_data.get("issueId")
    
    # Extract alert creation timestamp from the raw alert (in milliseconds)
    created_at_ms = test_data.get("createdAt", 0)
    # Convert to ISO format for display purposes but keep the original value for calculations
    start_time = datetime.fromtimestamp(created_at_ms / 1000, timezone.utc)

    # Generate a unique run ID for this workflow execution
    run_id = str(uuid.uuid4())
    
    logger.info(f"Alert created at: {start_time.isoformat()} (timestamp: {created_at_ms})")
    logger.info(f"Run ID: {run_id}")
    
    initial_state: IncidentState = {
        "incident_id": incident_id,
        "run_id": run_id,
        "raw_alert": test_data,
        "title": test_data.get("title", "Unknown"),
        "description": f"Alert from {test_data.get('alertPolicyNames', ['Unknown'])[0]}", 
        "severity": test_data.get("priority", "UNKNOWN"),
        "start_time": start_time.isoformat(),
        "current_phase": "initialize",
        "investigation_notes": [],
        "timeline": [],  # Initialize empty timeline events list
        # Initialize time window fields
        "since_time": None,
        "until_time": None,
        "since_time_ms": None,
        "until_time_ms": None,
        "primary_entity_guid": test_data.get("EntityId", [""])[0],
        "primary_entity_type": None,
        "entity_details": [],
        "related_entity_details": [],
        "metrics": [],
        "logs": [],
        "system_checks": [],
        "root_cause": None,
        "remediation_actions": [],
        "output_incident": None,
        "alert_category": "", # Will be filled by alert_parser
        "alert_runbook": "",  # Will be filled by alert_parser
        "entities": [],       # Will be filled by alert_parser
        "alert_title": test_data.get("title", "Unknown"),  # Required field
        "condition_name": test_data.get("alertConditionNames", ["Unknown"])[0],  # Required field
        "policy_name": test_data.get("alertPolicyNames", ["Unknown"])[0],  # Optional but commonly used
        "product": test_data.get("product", "Unknown"),  # Used in topology generation
        "nr_region": test_data.get("nr_region", "us"),  # Often needed for API calls
        "newrelic_incident_link": test_data.get("issueUrl", "Unknown"),
        "dashboard_url": os.environ.get("IIM_DASHBOARD_BASE_URL", "http://obv-ai-compute.ivantiai.com:8080/incident/"),
        "issue_url": test_data.get("issueUrl", "Unknown")
    }
    
    # Special handling for this alert - the EntityId is empty but we have impactedEntities
    if not initial_state["primary_entity_guid"] and test_data.get("impactedEntities"):
        logger.info(f"No EntityId provided, using first impactedEntity as identifier")
        # We'll let the alert_parser_agent handle the entity discovery
    
    # Execute the workflow with the test data
    logger.info(f"Starting workflow for incident {incident_id}")
    logger.info(f"Initial phase: {initial_state['current_phase']}")
    
    try:
        # Compile the workflow before using it
        checkpointer = MemorySaver()
        app = workflow.compile(checkpointer=checkpointer)

        config = {"configurable": {"thread_id": "1"}}
        # Call invoke() instead of ainvoke()
        result = await app.ainvoke(initial_state, config=config)
        last_state = app.get_state(config)
        
        # Pretty print the last state for better readability
        import json
        import pprint
        logger.info("Last state:")
        pprint.pformat(last_state, indent=2, width=120)
        
        # Alternative JSON pretty print
        # logger.info(json.dumps(last_state, indent=2, default=str))
        
        # history = list(app.get_state_history(config))
        # logger.info(f"History: {history}")
        
        # Print key results
        logger.info("-" * 60)
        logger.info("WORKFLOW EXECUTION RESULTS")
        logger.info("-" * 60)
        
        # if "error" in result:
        #     logger.error(f"Workflow error: {result['error']}")
        # else:
        #     logger.info(f"Final phase: {result.get('current_phase', 'unknown')}")
        #     logger.info(f"Alert Category: {result.get('alert_category', 'Unknown')}")
        #     logger.info(f"Condition Name: {result.get('condition_name', 'Unknown')}")
        #     logger.info(f"Policy Name: {result.get('policy_name', 'Unknown')}")
        #     logger.info(f"Cluster Name: {result.get('cluster_name', 'Unknown')}")
            
        #     # Print entities
        #     entities = result.get('entities', [])
        #     if entities:
        #         logger.info(f"\nEntities ({len(entities)}):")
        #         for entity in entities:
        #             logger.info(f"  - {entity.get('entity_name', 'Unknown')} ({entity.get('entity_guid', '')})")
            
        #     # Print entity relationships if available
        #     entity_relationships = result.get('entity_relationships', {})
        #     if entity_relationships:
        #         logger.info(f"\nEntity Relationships:")
        #         logger.info(f"  Primary Entity: {entity_relationships.get('primary_entity_name', 'Unknown')} ({entity_relationships.get('primary_entity_guid', '')})")
        #         logger.info(f"  Entity Type: {entity_relationships.get('primary_entity_type', 'Unknown')}")
                
        #         related_entities = entity_relationships.get('related_entities', [])
        #         if related_entities:
        #             logger.info(f"  Related Entities ({len(related_entities)}):")
        #             for entity in related_entities:
        #                 logger.info(f"    - {entity.get('name', 'Unknown')} ({entity.get('guid', '')})")
        #                 logger.info(f"      Type: {entity.get('type', 'Unknown')}, Relationship: {entity.get('relationship', 'Unknown')}")
        #                 logger.info(f"      Importance: {entity.get('importance', 'medium')}")
            
        #     # Print runbook results if available
        #     runbook_results = result.get('runbook_results', [])
        #     if runbook_results:
        #         logger.info(f"\nRunbook Execution Results ({len(runbook_results)}):")
        #         for rb_result in runbook_results:
        #             logger.info(f"  - {rb_result.get('runbook_name', 'Unknown')} (ID: {rb_result.get('runbook_id', '')})")
        #             logger.info(f"    Summary: {rb_result.get('summary', 'N/A')}")
        #             if rb_result.get('issues_found', False):
        #                 logger.info(f"    Issues found: Yes")
                    
        #             # Print recommendations
        #             recommendations = rb_result.get('recommendations', [])
        #             if recommendations:
        #                 logger.info(f"    Recommendations:")
        #                 for rec in recommendations:
        #                     logger.info(f"      * {rec}")
        
        # # # Print investigation notes
        # # logger.info("\nInvestigation Notes:")
        # # for note in result.get("investigation_notes", []):
        # #     # logger.info(f"  - {note['timestamp']}: {note['agent']}")
        # #     logger.info(f"    {note['note']}")
        
        # # # Print timeline events (sorted by timestamp)
        # # timeline_events = result.get("timeline", [])
        # # if timeline_events:
        #     timeline_events = sorted(timeline_events, key=lambda x: x["timestamp"])
        #     logger.info("\nTimeline Events:")
        #     for event in timeline_events:
        #         tags_str = ", ".join([f"{tag['label']}:{tag['variant']}" for tag in event["tags"]]) if event.get("tags") else ""
        #         logger.info(f"  - [{event['timestamp']}] {event['title']}")
        #         logger.info(f"    Type: {event['type']}, Source: {event['source']}, Tags: {tags_str}")
        #         logger.info(f"    {event['description']}")
        #         logger.info(f"")
        
        # logger.info("-" * 60)
        
        return result
    except Exception as e:
        logger.error(f"Error executing workflow: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    asyncio.run(test_crashloop_workflow()) 