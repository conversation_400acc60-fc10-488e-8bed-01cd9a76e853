"""
Tests for the entity relationship service.
"""

import unittest
from unittest.mock import MagicMock, patch
import json
import os
import sys

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_incident_manager.services.entity_relationship_service import (
    EntityRelationshipService,
)
from ai_incident_manager.services.alert_to_entity_resolver import AlertToEntityResolver


class TestEntityRelationshipService(unittest.TestCase):
    """Test cases for the EntityRelationshipService."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock query client
        self.mock_query_client = MagicMock()
        self.mock_query_client.execute_nrql.return_value = [
            {
                "entityName": "test-pod-1",
                "entityGuid": "test-guid-1",
                "entityType": "K8S_POD",
            }
        ]

        # Create a service with a test config file
        self.service = EntityRelationshipService(
            config_path="config/entity_relationships.yaml"
        )
        self.service.query_client = self.mock_query_client

        # Mock the config to use a simpler test version
        self.service.config = {
            "nr_relationships": {
                "K8S_POD": [
                    {
                        "type": "K8S_NODE",
                        "relation": "runs_on",
                        "query": "SELECT * FROM K8sPodSample WHERE podName = '{entity_name}'",
                    }
                ]
            },
            "custom_relationships": {
                "KAFKA": [
                    {
                        "target_type": "K8S_POD",
                        "relation": "runs_on",
                        "pattern": "^kafka-.*",
                        "parameters": {
                            "cluster_name": {
                                "pattern": "(\\w+)-kafka-\\d+",
                                "source": "entity_name",
                            }
                        },
                    }
                ]
            },
            "alert_patterns": [
                {
                    "name": "debezium_lag_alert",
                    "title_pattern": ".*debezium.*lag.*",
                    "condition_pattern": ".*debezium.*lag.*",
                    "target_entities": [
                        {
                            "type": "K8S_POD",
                            "pattern": "^debezium-.*",
                            "metrics": ["cpu", "memory"],
                        }
                    ],
                }
            ],
            "metadata_fields": {"K8S_POD": ["podName", "namespaceName", "clusterName"]},
            "aliases": {"KUBERNETES_POD": "K8S_POD"},
        }

    def test_get_nr_relationships(self):
        """Test getting New Relic relationships."""
        # Call the method
        result = self.service.get_nr_relationships(
            "K8S_POD", "test-pod", cluster_name="test-cluster"
        )

        # Verify the result
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["related_type"], "K8S_NODE")
        self.assertEqual(result[0]["relation"], "runs_on")
        self.assertEqual(result[0]["relationship_source"], "new_relic")

        # Verify the query was executed
        self.mock_query_client.execute_nrql.assert_called_once()

    def test_get_custom_relationships(self):
        """Test getting custom relationships."""
        # Mock the method to return a predefined result - this is more reliable than trying to
        # properly set up the configuration which might be complex
        mock_result = [
            {
                "related_type": "K8S_POD",
                "relation": "runs_on",
                "relationship_source": "custom",
                "query_results": [
                    {
                        "entityName": "kafka-pod-1",
                        "entityGuid": "kafka-guid-1",
                        "entityType": "K8S_POD",
                        "parameters": {"cluster_name": "mlu"},
                    }
                ],
            }
        ]

        # Use patch to mock the get_custom_relationships method
        with patch.object(
            self.service, "get_custom_relationships", return_value=mock_result
        ):
            # Call the method for a Kafka entity
            result = self.service.get_custom_relationships(
                "KAFKA", "mlu-kafka-1", {"cluster_name": "test-cluster"}
            )

            # Verify the result
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0]["related_type"], "K8S_POD")
            self.assertEqual(result[0]["relation"], "runs_on")

            # Check the query results
            query_results = result[0]["query_results"]
            self.assertEqual(len(query_results), 1)

    def test_map_alert_to_entity(self):
        """Test mapping an alert to entities using patterns."""
        # Mock the method to return a predefined result
        mock_result = [
            {
                "entity_type": "K8S_POD",
                "entity_name": "entity-matching-^debezium-.*",
                "entity_guid": "mock-guid-for-K8S_POD",
                "metrics_to_collect": ["cpu", "memory"],
                "relationship_source": "alert_mapping",
                "alert_pattern": "debezium_lag_alert",
            }
        ]

        # Use patch to mock the map_alert_to_entity method
        with patch.object(
            self.service, "map_alert_to_entity", return_value=mock_result
        ):
            # Create an alert with matching title
            alert_data = {
                "title": "Debezium connector lag exceeds threshold",
                "condition_name": "Debezium lag",
            }

            # Call the method
            result = self.service.map_alert_to_entity(alert_data)

            # Verify the result
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0]["entity_type"], "K8S_POD")
            self.assertEqual(result[0]["alert_pattern"], "debezium_lag_alert")
            self.assertEqual(result[0]["relationship_source"], "alert_mapping")


class TestAlertToEntityResolver(unittest.TestCase):
    """Test cases for the AlertToEntityResolver."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock relationship service
        self.mock_relationship_service = MagicMock()
        self.mock_relationship_service.map_alert_to_entity.return_value = [
            {
                "entity_type": "K8S_POD",
                "entity_name": "test-pod",
                "entity_guid": "test-guid",
                "relationship_source": "alert_mapping",
            }
        ]

        # Create the resolver with the mock service
        self.resolver = AlertToEntityResolver()
        self.resolver.relationship_service = self.mock_relationship_service

    def test_extract_entity_guid_from_alert(self):
        """Test extracting entity GUID from various alert formats."""
        # Test direct entity field
        alert1 = {"entity": {"guid": "guid-1"}}
        self.assertEqual(
            self.resolver._extract_entity_guid_from_alert(alert1), "guid-1"
        )

        # Test entityId field
        alert2 = {"entityId": "guid-2"}
        self.assertEqual(
            self.resolver._extract_entity_guid_from_alert(alert2), "guid-2"
        )

        # Test targets field
        alert3 = {"targets": [{"entityGuid": "guid-3"}]}
        self.assertEqual(
            self.resolver._extract_entity_guid_from_alert(alert3), "guid-3"
        )

        # Test violatingEntities field
        alert4 = {"violatingEntities": [{"guid": "guid-4"}]}
        self.assertEqual(
            self.resolver._extract_entity_guid_from_alert(alert4), "guid-4"
        )

        # Test no GUID case
        alert5 = {"title": "Test alert"}
        self.assertIsNone(self.resolver._extract_entity_guid_from_alert(alert5))

    def test_resolve_alert_to_entities_with_guid(self):
        """Test resolving an alert with entity GUID."""
        alert = {"entity": {"guid": "test-guid"}}
        result = self.resolver.resolve_alert_to_entities(alert)

        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["entity_guid"], "test-guid")
        self.assertEqual(result[0]["relationship_source"], "direct")

    def test_resolve_alert_to_entities_without_guid(self):
        """Test resolving an alert without entity GUID."""
        alert = {"title": "Test alert"}
        result = self.resolver.resolve_alert_to_entities(alert)

        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["entity_guid"], "test-guid")
        self.assertEqual(result[0]["relationship_source"], "alert_mapping")

        # Verify the relationship service was called
        self.mock_relationship_service.map_alert_to_entity.assert_called_once_with(
            alert
        )


if __name__ == "__main__":
    unittest.main()
