'''
This script fetches all conditions from New Relic which are enabled and have Signal Loss set to true and saves them to a CSV file.
Also, it fetches conditions based on a search query and saves them to a CSV file.
'''
import requests
import json
import os
import csv
import argparse
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.theme import Theme
from rich.live import Live
from dotenv import load_dotenv

load_dotenv()

console = Console()

custom_theme = Theme({
    "info": "cyan",
    "warning": "yellow",
    "danger": "bold red",
    "success": "bold green",
    "highlight": "magenta",
})

console = Console(theme=custom_theme)

global debug
debug = False

class NewRelicConditionFetcher:
    def __init__(self, api_key, account_id, region='US'):
        self.api_key = api_key
        self.account_id = account_id
        self.api_endpoint = "https://api.newrelic.com/graphql" if region == 'US' else "https://api.eu.newrelic.com/graphql"

    def fetch_conditions(self, cursor=None, search_text=None, fetch_type='signal_loss'):
        query = self._get_query(fetch_type)
        variables = {
            "accountId": self.account_id,
            "cursor": cursor
        }
        if search_text:
            variables["searchText"] = search_text

        headers = {
            "Content-Type": "application/json",
            "API-Key": self.api_key
        }

        response = requests.post(self.api_endpoint, json={"query": query, "variables": variables}, headers=headers)
        return response.json()

    def _get_query(self, fetch_type):
        if fetch_type == 'signal_loss':
            return '''
            query ($accountId: Int!, $cursor: String) {
              actor {
                account(id: $accountId) {
                  alerts {
                    nrqlConditionsSearch(cursor: $cursor) {
                      nrqlConditions {
                        expiration {
                          openViolationOnExpiration
                        }
                        nrql {
                          query
                        }
                        id
                        name
                        policyId
                        enabled
                      }
                      nextCursor
                    }
                  }
                }
              }
            }
            '''
        elif fetch_type == 'search_query':
            return '''
            query ($accountId: Int!, $cursor: String, $searchText: String!) {
              actor {
                account(id: $accountId) {
                  alerts {
                    nrqlConditionsSearch(cursor: $cursor, searchCriteria: {queryLike: $searchText}) {
                      nrqlConditions {
                        id
                        name
                        nrql {
                          query
                        }
                        policyId
                        enabled
                      }
                      nextCursor
                    }
                  }
                }
              }
            }
            '''

def save_to_csv(all_conditions, filename="new_relic_conditions.csv"):
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['id', 'name', 'policyId', 'query', 'enabled']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for condition in all_conditions:
            writer.writerow({
                'id': condition['id'],
                'name': condition['name'],
                'policyId': condition.get('policyId', 'N/A'),
                'query': condition['nrql']['query'],
                'enabled': condition['enabled']
            })
    console.print(f"CSV file '{filename}' has been created.", style="success")

def print_conditions(conditions, search_text=None):
    title = "New Relic Conditions"
    if search_text:
        title += f" - Search: {search_text}"
    table = Table(title=title)
    
    table.add_column("ID", style="cyan")
    table.add_column("Name", style="magenta")
    table.add_column("Policy ID", style="green")
    table.add_column("Query", style="yellow")
    table.add_column("Enabled", style="blue")
    
    for condition in conditions:
        table.add_row(
            condition['id'],
            condition['name'],
            condition.get('policyId', 'N/A'),
            condition['nrql']['query'],
            str(condition['enabled'])
        )
    
    console.print(Panel(table, expand=False))

def print_debug(message, style="dim"):
    if debug:
        console.print(message, style=style)

def main(api_key, account_id, region, fetch_type, search_text):
    fetcher = NewRelicConditionFetcher(api_key, account_id, region)
    all_conditions = []
    cursor = None
    
    progress = Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    )
    
    with Live(progress, console=console, refresh_per_second=4):
        task = progress.add_task(description="Fetching conditions...", total=None)
        
        while True:
            data = fetcher.fetch_conditions(cursor, search_text, fetch_type)
            conditions = data["data"]["actor"]["account"]["alerts"]["nrqlConditionsSearch"]["nrqlConditions"]
            
            for condition in conditions:
                if fetch_type == 'signal_loss':
                    if condition["enabled"] and condition["expiration"]["openViolationOnExpiration"]:
                        all_conditions.append(condition)
                elif fetch_type == 'search_query':
                    if condition["enabled"]:
                        all_conditions.append(condition)
            
            cursor = data["data"]["actor"]["account"]["alerts"]["nrqlConditionsSearch"]["nextCursor"]
            
            if not cursor:
                break
            
            progress.update(task, advance=1)
    
    progress.remove_task(task)
    console.print(f"Total matching conditions: {len(all_conditions)}", style="info")
    
    print_conditions(all_conditions, search_text)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Fetch New Relic conditions based on specified criteria.")
    parser.add_argument("--debug", action="store_true", help="Enable debug output")
    args = parser.parse_args()

    debug = args.debug

    api_key = os.getenv("NEWRELIC_API_KEY")
    if not api_key:
        console.print("Error: NEWRELIC_API_KEY environment variable is not set.", style="bold red")
        exit(1)
    
    accounts = [
        {"account_id": 1093620, "region": "US"}
    ]
    search_text_list = [
        "CM_AKSNodePoolCount",
        "elasticPoolDataStorage"
        "rapidApiStatistics",
        "salesforceApiEvents",
        "serviceBusMessageCountCM",
        "serviceBusQueueMessageCountCM",
        "serviceBusTopicUsageCM",
        "serviceBusQueueUsageCM",
    ]

    # Fetch conditions for each account and search text
    for account in accounts:
        for search_text in search_text_list:
            console.print(f"\nFetching conditions for account {account['account_id']} with search text: {search_text}", style="highlight")
            conditions = main(api_key, account["account_id"], account["region"], "search_query", search_text)

    # Fetch condition for signal loss
    for account in accounts:
        console.print(f"\nFetching signal loss conditions for account {account['account_id']}", style="highlight")
        conditions = main(api_key, account["account_id"], account["region"], "signal_loss", None)

    save_to_csv(conditions)
