#!/usr/bin/env python3
"""
Test script for the New Relic logs module with comprehensive debugging.
This script tests various log queries with detailed debug output.
"""

import os
import logging
from datetime import datetime, timezone, timedelta
import dotenv
import json
import argparse
import sys

# Import New Relic client classes
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.base import Region
from lib.new_relic.logs import NewRelicLogsClient

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test New Relic logs with detailed debugging.")
    parser.add_argument("--pod-name", type=str, required=True, 
                      help="Pod name to query for logs")
    parser.add_argument("--hours", type=int, default=48, 
                      help="Time window in hours (default: 48)")
    parser.add_argument("--partition", type=str, default="Log", 
                      help="Single log partition to query (default: Log)")
    parser.add_argument("--region", choices=["US", "EU"], default="US", 
                      help="New Relic region (default: US)")
    return parser.parse_args()

def test_pod_logs():
    """Test pod logs with detailed debugging."""
    args = parse_args()
    
    # Get required environment variables
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        logger.error("Missing required environment variables: NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set.")
        return

    # Define time range
    until_time = datetime.now(timezone.utc)
    since_time = until_time - timedelta(hours=args.hours)
    
    logger.info("===== New Relic Logs Test with Debug Output =====")
    logger.info(f"Pod name: {args.pod_name}")
    logger.info(f"Partition: {args.partition}")
    logger.info(f"Time range: {since_time.isoformat()} to {until_time.isoformat()} ({args.hours} hours)")
    logger.info(f"Account ID: {account_id}")
    logger.info(f"Region: {args.region}")
    logger.info("================================================")
    
    # Initialize the New Relic GraphQL client with debug mode enabled
    region = Region.US if args.region == "US" else Region.EU
    nr_client = NewRelicGraphQLClient(
        api_key=api_key,
        account_id=account_id,
        region=region,
        debug=True,
        debug_request=True,
        debug_response=True,
        debug_errors=True
    )
    
    # Initialize the New Relic Logs client with debug mode
    logs_client = NewRelicLogsClient(client=nr_client, debug=True)
    
    try:
        # Test direct query with a single partition
        logger.info("\n===== TEST 1: Single partition direct NRQL query =====")
        
        direct_query = f"""
        SELECT timestamp, level, message, pod_name, kubernetes.pod.name, container_name
        FROM {args.partition}
        WHERE pod_name = '{args.pod_name}' OR kubernetes.pod.name = '{args.pod_name}'
        SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
        UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
        LIMIT 10
        """
        
        logger.info(f"Executing query:\n{direct_query}")
        
        direct_logs = logs_client.query_logs(
            query=direct_query,
            limit=10,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(direct_logs)} logs from direct query")
        print_logs(direct_logs)
        
        # Test using the get_pod_logs method
        logger.info("\n===== TEST 2: Using get_pod_logs method =====")
        
        pod_logs = logs_client.get_pod_logs(
            pod_name=args.pod_name,
            cluster_partition=args.partition,
            since=since_time,
            until=until_time,
            limit=10,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(pod_logs)} logs using get_pod_logs")
        print_logs(pod_logs)
        
    except Exception as e:
        logger.error(f"Error in test: {str(e)}", exc_info=True)
        return

def print_logs(logs):
    """Pretty print logs for readability."""
    if not logs:
        logger.info("No logs found")
        return
    
    logger.info(f"Found {len(logs)} logs:")
    
    for i, log in enumerate(logs):
        logger.info(f"Log {i+1}:")
        # Format the log as a pretty-printed JSON string
        formatted_log = json.dumps(log, indent=2, default=str)
        logger.info(formatted_log)
        logger.info("-" * 80)

if __name__ == "__main__":
    test_pod_logs() 