#!/usr/bin/env python3
"""
Final test script for the New Relic logs module.
This script uses a wide time window and queries all logs to test functionality.
"""

import os
import logging
from datetime import datetime, timezone, timedelta
import dotenv
import json
import argparse

# Import New Relic client classes
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.base import Region
from lib.new_relic.logs import NewRelicLogsClient

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test New Relic logs with wide time window.")
    parser.add_argument("--days", type=int, default=7, 
                      help="Time window in days (default: 7)")
    parser.add_argument("--limit", type=int, default=10, 
                      help="Maximum number of logs to retrieve (default: 10)")
    parser.add_argument("--region", choices=["US", "EU"], default="US", 
                      help="New Relic region (default: US)")
    parser.add_argument("--debug", action="store_true", 
                      help="Enable debug mode")
    parser.add_argument("--pod-name", type=str, default="agent-management-background-container-service", 
                      help="Pod name to query (default: agent-management-background-container-service)")
    parser.add_argument("--partition", type=str, 
                      help="Log partition to query (e.g., 'neurons_all', 'neurons_nvu')")
    return parser.parse_args()

def main():
    """Test New Relic logs with a wide time window."""
    args = parse_args()
    
    # Get required environment variables
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        logger.error("Missing required environment variables: NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set.")
        return
    
    # Define time range for logs (last N days)
    until_time = datetime.now(timezone.utc)
    since_time = until_time - timedelta(days=args.days)
    
    logger.info(f"Testing logs with wide time window")
    logger.info(f"Time range: {since_time.isoformat()} to {until_time.isoformat()} (last {args.days} days)")
    
    # Initialize the New Relic GraphQL client
    region = Region.US if args.region == "US" else Region.EU
    nr_client = NewRelicGraphQLClient(
        api_key=api_key,
        account_id=account_id,
        region=region,
        debug=args.debug
    )
    
    # Initialize the New Relic Logs client
    logs_client = NewRelicLogsClient(client=nr_client, debug=args.debug)
    
    try:
        # Test 1: Query all logs without filters
        logger.info("Test 1: Query all logs (sample)")
        all_logs_query = f"""
        SELECT timestamp, level, message
        FROM Log
        SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
        UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
        LIMIT {args.limit}
        """
        
        all_logs = logs_client.query_logs(
            query=all_logs_query,
            limit=args.limit,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(all_logs)} logs total")
        print_logs(all_logs)
        
        # Test 2: List all partitions with data
        logger.info("Test 2: Find partitions with data")
        # Hardcoded list of partitions to check
        partitions_to_check = [
            "Log",
            "Log_Neurons_NVU", 
            "Log_Neurons_MLU",
            "Log_Neurons_TKU", 
            "Log_Neurons_TTU", 
            "Log_Neurons_UKU",
            "Log_Access_AZ_CA1", 
            "Log_Access_AZ_UK1"
        ]
        
        partitions_with_data = []
        
        for partition in partitions_to_check:
            partition_query = f"""
            SELECT count(*) as log_count
            FROM {partition}
            SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
            UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
            LIMIT 1
            """
            
            try:
                partition_result = logs_client.query_logs(
                    query=partition_query,
                    limit=1,
                    account_id=account_id
                )
                
                if partition_result and len(partition_result) > 0 and partition_result[0].get('log_count', 0) > 0:
                    partitions_with_data.append({
                        'partition': partition,
                        'count': partition_result[0].get('log_count', 0)
                    })
                    logger.info(f"Partition {partition} has {partition_result[0].get('log_count', 0)} logs")
            except Exception as e:
                logger.warning(f"Error querying partition {partition}: {str(e)}")
        
        logger.info(f"Found {len(partitions_with_data)} partitions with data")
        
        # Test 3: Query a specific pod if partitions were found
        if args.pod_name and partitions_with_data:
            logger.info(f"Test 3: Query logs for pod {args.pod_name}")
            
            # Use the first partition with data, or the specified partition
            partition_to_use = args.partition if args.partition else partitions_with_data[0]['partition']
            
            pod_query = f"""
            SELECT timestamp, level, message, pod_name, kubernetes.container.name
            FROM {partition_to_use}
            WHERE pod_name = '{args.pod_name}'
            SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
            UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
            LIMIT {args.limit}
            """
            
            pod_logs = logs_client.query_logs(
                query=pod_query,
                limit=args.limit,
                account_id=account_id
            )
            
            logger.info(f"Retrieved {len(pod_logs)} logs for pod {args.pod_name} from partition {partition_to_use}")
            print_logs(pod_logs)
            
            if not pod_logs:
                # Query for available pods in this partition
                logger.info(f"Searching for available pods in {partition_to_use}")
                
                pods_query = f"""
                SELECT uniqueCount(pod_name) as pod_count, latest(pod_name) as pod_name
                FROM {partition_to_use}
                WHERE pod_name IS NOT NULL
                SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
                UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
                FACET pod_name
                LIMIT 10
                """
                
                pods = logs_client.query_logs(
                    query=pods_query,
                    limit=10,
                    account_id=account_id
                )
                
                if pods:
                    logger.info(f"Found {len(pods)} pods in partition {partition_to_use}")
                    for pod in pods:
                        logger.info(f"Pod: {pod.get('pod_name', 'unknown')}")
        
        # Test 4: Entity logs if we have a valid entity GUID
        entity_guid = "MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"
        logger.info(f"Test 4: Query logs for entity GUID {entity_guid}")
        
        # Try several options to query entity logs
        # Option 1: Basic entity query
        entity_query = f"""
        SELECT timestamp, level, message
        FROM Log
        WHERE entity.guid = '{entity_guid} or pod_name = '{args.pod_name}'
        SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
        UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
        LIMIT {args.limit}
        """
        
        entity_logs = logs_client.query_logs(
            query=entity_query,
            limit=args.limit,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(entity_logs)} logs for entity GUID")
        print_logs(entity_logs)
        
        # Option 2: Try specific partitions for entity
        if partitions_with_data and not entity_logs:
            for partition_info in partitions_with_data:
                partition = partition_info['partition']
                logger.info(f"Trying entity query on partition {partition}")
                
                partition_entity_query = f"""
                SELECT timestamp, level, message
                FROM {partition}
                WHERE entity.guid = '{entity_guid}' or pod_name = '{args.pod_name}'
                SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
                UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
                LIMIT {args.limit}
                """
                
                try:
                    partition_entity_logs = logs_client.query_logs(
                        query=partition_entity_query,
                        limit=args.limit,
                        account_id=account_id
                    )
                    
                    if partition_entity_logs and len(partition_entity_logs) > 0:
                        logger.info(f"Found {len(partition_entity_logs)} logs for entity in partition {partition}")
                        print_logs(partition_entity_logs)
                        break
                except Exception as e:
                    logger.warning(f"Error querying entity in partition {partition}: {str(e)}")
        
    except Exception as e:
        logger.error(f"Error retrieving logs: {str(e)}", exc_info=True)

def print_logs(logs):
    """Pretty print logs for readability."""
    if not logs:
        logger.info("No logs found")
        return
    
    logger.info(f"Found {len(logs)} logs:")
    
    for i, log in enumerate(logs):
        logger.info(f"Log {i+1}:")
        # Format the log as a pretty-printed JSON string
        formatted_log = json.dumps(log, indent=2, default=str)
        logger.info(formatted_log)
        logger.info("-" * 80)

if __name__ == "__main__":
    main() 