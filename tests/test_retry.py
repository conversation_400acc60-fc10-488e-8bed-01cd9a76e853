"""
Test script for retry decorator
"""

from lib.new_relic.utils import retry_on_error
from lib.new_relic.base import NewRelicGraphQLError
import time
import dotenv

dotenv.load_dotenv()

# Test with a server error that should be retried
@retry_on_error(max_attempts=3, initial_delay=1, backoff_factor=2)
def test_server_error():
    print("Raising server error...")
    raise NewRelicGraphQLError("An error occurred resolving this field")

# Test with a non-server error that should not be retried
@retry_on_error(max_attempts=3, initial_delay=1, backoff_factor=2)
def test_other_error():
    print("Raising other error...")
    raise NewRelicGraphQLError("Invalid query")

print("Testing retry with server error (should retry 3 times):")
try:
    test_server_error()
except Exception as e:
    print(f"Final error after retries: {e}")

print("\nTesting retry with non-server error (should not retry):")
try:
    test_other_error()
except Exception as e:
    print(f"Error (should not retry): {e}") 