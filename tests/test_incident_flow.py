#!/usr/bin/env python3
"""
Test script to demonstrate the AI Incident Manager functionality.

This script simulates an incident from a New Relic alert and runs the analysis workflow.
"""

import os
import json
import uuid
import sys
import asyncio
import logging
from datetime import datetime

import dotenv

from ai_incident_manager.incident_manager import IncidentManager
from ai_incident_manager.parsers.extract_incident_info import extract_incident_info
from ai_incident_manager.workflow.flow import run_analysis_workflow
from ai_incident_manager.services.metrics_collector import MetricsCollector

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Test alert provided by the user
TEST_ALERT = {
    "issueId": "a4d2a1ea-0ee8-4264-a4e8-ac16d7538388", 
    "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/a4d2a1ea-0ee8-4264-a4e8-ac16d7538388?notifier=WEBHOOK", 
    "title": "castai-workload-autoscaler query result is > 10.0 on 'Pod with CrashLoopBackOff -- '", 
    "priority": "CRITICAL", 
    "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtNDcwMjA1MjUyNDEyNjYwNDEwNg"], 
    "impactedEntities": ["castai-workload-autoscaler"], 
    "totalIncidents": "1", 
    "state": "ACTIVATED", 
    "trigger": "STATE_CHANGE", 
    "isCorrelated": "false", 
    "createdAt": *************, 
    "updatedAt": *************, 
    "sources": ["newrelic"], 
    "alertPolicyNames": ["Neurons k8s Infra - Critical"], 
    "alertConditionNames": ["Pod with CrashLoopBackOff -- "], 
    "workflowName": "obv-ai-processing-neurons", 
    "chartLink": "https://gorgon.nr-assets.net/image/82ebc3bf-dbef-4616-b285-7ed24195ba0b?config.legend.enabled=false&width=400&height=210", 
    "product": "neurons", 
    "nr_region": "us"
}


async def test_incident_workflow():
    """Run a test of the incident analysis workflow."""
    logger.info("Starting test of incident analysis workflow")
    
    # Check for required API credentials
    newrelic_api_key = os.environ.get("NEWRELIC_API_KEY")
    newrelic_account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
    
    if not newrelic_api_key or not newrelic_account_id:
        logger.warning(
            "New Relic API credentials not set. Set NEWRELIC_API_KEY and "
            "NEWRELIC_ACCOUNT_ID environment variables for full functionality."
        )
    
    # Check for Azure OpenAI credentials
    required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
    azure_openai_enabled = all(var in os.environ for var in required_azure_vars)
    
    if not azure_openai_enabled:
        logger.warning(
            "Azure OpenAI credentials not set. Set AZURE_OPENAI_ENDPOINT and "
            "AZURE_OPENAI_API_KEY environment variables for full functionality."
        )
        logger.warning(
            "Missing environment variables: " + 
            str([var for var in required_azure_vars if var not in os.environ])
        )
        return "FAILED: Azure OpenAI credentials required"
    
    # Initialize the incident manager
    incident_manager = IncidentManager()
    
    # Use the test alert provided by the user
    test_alert = TEST_ALERT
    logger.info(f"Using test alert: {test_alert['title']}")
    
    # Extract incident information using the new AI parser
    incident_data = extract_incident_info(test_alert)
    logger.info(f"Extracted incident data: {incident_data['title']} (Severity: {incident_data['severity']})")
    
    if 'alert_category' in incident_data:
        logger.info(f"Alert category: {incident_data['alert_category']}")
    
    if 'runbook' in incident_data:
        logger.info(f"Runbook available: {bool(incident_data['runbook'])}")
    
    # Create the incident
    incident_id = incident_manager.create_incident(incident_data)
    logger.info(f"Created incident with ID: {incident_id}")
    
    # Prepare workflow state
    state = incident_manager.prepare_workflow_state(incident_data)
    logger.info("Prepared workflow state")
    
    # Run the analysis workflow
    logger.info("Starting analysis workflow...")
    result_state = await run_analysis_workflow(state)
    logger.info("Analysis workflow completed")
    
    # Print some results
    if 'output' in result_state and 'root_cause' in result_state['output']:
        logger.info(f"Root cause analysis: {result_state['output']['root_cause'][:200]}...")
    if 'output' in result_state and 'recommendations' in result_state['output']:
        logger.info(f"Recommended actions: {result_state['output']['recommendations'][:200]}...")
    
    # Update the incident with the analysis results
    incident_manager.update_incident_status(incident_id, "ANALYZED")
    
    # Add investigation steps
    if 'investigation_state' in result_state and 'notes' in result_state['investigation_state']:
        for note in result_state['investigation_state']['notes']:
            incident_manager.add_investigation_step(
                incident_id,
                step_number=note.get('step', 0),
                title=note.get('title', 'Investigation Step'),
                content=note.get('content', ''),
                timestamp=note.get('timestamp', datetime.utcnow().isoformat())
            )
    
    # Get the incident from the database
    incident = incident_manager.get_incident(incident_id)
    logger.info(f"Retrieved incident from database: {incident['title']}")
    
    # Print investigation steps
    logger.info("Investigation steps:")
    if 'investigationSteps' in incident:
        for step in incident['investigationSteps']:
            logger.info(f"Step {step['step_number']}: {step['title']}")
    else:
        logger.info("No investigation steps found in the database record")
    
    logger.info("Test completed successfully")
    
    return incident_id


def test_metrics_collector():
    """Test the metrics collector with an entity from the test alert."""
    logger.info("Testing metrics collector")
    
    # Check for New Relic API credentials
    newrelic_api_key = os.environ.get("NEWRELIC_API_KEY")
    newrelic_account_id = os.environ.get("NEWRELIC_ACCOUNT_ID")
    
    if not newrelic_api_key or not newrelic_account_id:
        logger.error(
            "New Relic API credentials not set. Cannot test metrics collector. "
            "Set NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID environment variables."
        )
        return
    
    # Initialize metrics collector
    metrics_collector = MetricsCollector()
    
    # Use entity from test alert
    entity_guid = TEST_ALERT["EntityId"][0] if TEST_ALERT["EntityId"] else None
    entity_name = TEST_ALERT["impactedEntities"][0] if TEST_ALERT["impactedEntities"] else "unknown"
    
    if not entity_guid:
        logger.error("No entity GUID found in test alert")
        return
    
    logger.info(f"Getting entity details for: {entity_name} ({entity_guid})")
    
    # Test entity metrics collection
    try:
        # First, get entity type from Entity Analyzer
        entity_details = metrics_collector.analyzer.get_entity(entity_guid)
        
        if entity_details:
            entity_type = entity_details.get("type", "KUBERNETES_POD")  # Assume pod if unknown
            logger.info(f"Entity type: {entity_type}")
            
            # Collect metrics
            metrics = metrics_collector.collect_entity_metrics(
                entity_guid=entity_guid,
                entity_type=entity_type
            )
            
            logger.info(f"Collected {len(metrics)} metrics for {entity_name}")
            for metric_name in metrics:
                logger.info(f"  - {metric_name}")
            
            # Collect logs if it's a Kubernetes entity
            if "KUBERNETES" in entity_type:
                logs = metrics_collector.collect_entity_logs(
                    entity_guid=entity_guid,
                    entity_name=entity_name,
                    entity_type=entity_type,
                    limit=10
                )
                
                logger.info(f"Collected {len(logs)} log entries")
        else:
            logger.warning(f"Could not get entity details for {entity_guid}")
            
    except Exception as e:
        logger.error(f"Error testing metrics collector: {str(e)}")


def test_incident_parser():
    """Test the incident parser with the test alert."""
    # Use the test alert provided by the user
    test_alert = TEST_ALERT
    
    # Extract incident information
    incident_data = extract_incident_info(test_alert)
    
    # Print the extracted information
    print("\nAlert Parser Test:")
    print(f"Title: {incident_data['title']}")
    print(f"Severity: {incident_data['severity']}")
    
    if 'alert_category' in incident_data:
        print(f"Alert Category: {incident_data['alert_category']}")
    
    if 'entities' in incident_data and incident_data['entities']:
        entity = incident_data['entities'][0]
        print(f"Entity: {entity.get('type', 'Unknown')} - {entity.get('name', 'Unknown')}")
    
    print(f"Description: {incident_data.get('description', 'No description')}")
    
    if 'metrics' in incident_data and incident_data['metrics']:
        for metric in incident_data['metrics']:
            print(f"Metric: {metric['name']} - {metric.get('threshold', 'No threshold')}")
    
    if 'likely_causes' in incident_data and incident_data['likely_causes']:
        print("\nLikely causes:")
        for cause in incident_data['likely_causes']:
            print(f"- {cause}")
    
    if 'runbook' in incident_data and incident_data['runbook']:
        print("\nRunbook:")
        print(incident_data['runbook'])
    
    return incident_data


def main():
    """Main entry point for the test script."""
    print("===== AI Incident Manager Test =====")
    
    # Check for Azure OpenAI credentials
    required_azure_vars = ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_API_KEY"]
    azure_openai_enabled = all(var in os.environ for var in required_azure_vars)
    
    if not azure_openai_enabled:
        print("Azure OpenAI credentials required. Please set the following environment variables:")
        for var in required_azure_vars:
            if var not in os.environ:
                print(f"- {var}")
        return
    
    # Test the incident parser
    test_incident_parser()
    
    # Test metrics collector if --test-metrics flag is provided
    if "--test-metrics" in sys.argv:
        test_metrics_collector()
    
    # Run the full workflow test
    incident_id = asyncio.run(test_incident_workflow())
    
    print(f"\nTest completed. Incident ID: {incident_id}")
    print("Check the logs for detailed workflow information.")


if __name__ == "__main__":
    main() 