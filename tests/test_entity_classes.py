#!/usr/bin/env python
"""
Test script for directly testing the entity classes with the specific entity GUID.
This script tests the specialized entity classes (PodEntity, NodeEntity, etc.)
with the entity GUID: MTA5MzYyMHxJTkZSQXxOQXwtNTY4MTU0NzYxNTQ2NjE1NDQ1Nw
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pprint import pprint

import dotenv

# Load environment variables
dotenv.load_dotenv()

# Add the parent directory to the path so we can import the library
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.new_relic.base import UTC
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.analyzer import EntityAnalyzer
from lib.new_relic.nrql_manager import NRQLManager
from lib.new_relic.entities import PodEntity, NodeEntity, HostEntity, ApplicationEntity

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('entity_classes_test')

# Entity GUID to analyze
TEST_ENTITY_GUID = "MTA5MzYyMHxJTkZSQXxOQXwtNTY4MTU0NzYxNTQ2NjE1NDQ1Nw"

def determine_entity_type_from_guid(analyzer, entity_guid):
    """Determine the entity type from its GUID"""
    entity_details = analyzer._get_entity_details(entity_guid)
    if not entity_details:
        logger.error(f"Entity not found: {entity_guid}")
        return None, None, None
    
    entity_type = entity_details.get('type', 'unknown')
    entity_name = entity_details.get('name', 'unknown')
    
    # Extract cluster name if it's a Kubernetes entity
    cluster_name = None
    if 'KUBERNETES' in entity_type:
        for tag in entity_details.get('tags', []):
            if tag.get('key') == 'clusterName':
                cluster_name = tag.get('values', ['unknown'])[0]
                break
    
    logger.info(f"Entity: {entity_name} (Type: {entity_type})")
    if cluster_name:
        logger.info(f"Cluster: {cluster_name}")
    
    return entity_type, entity_name, cluster_name

def test_pod_entity(query_client, entity_name, cluster_name, since_time_ms, until_time_ms):
    """Test PodEntity methods"""
    logger.info("Testing PodEntity...")
    nrql_manager = NRQLManager()
    pod_entity = PodEntity(query_client, nrql_manager)
    
    results = {}
    
    logger.info(f"Testing get_cpu_usage for pod: {entity_name}")
    results['cpu_usage'] = pod_entity.get_cpu_usage(
        pod_name=entity_name,
        cluster_name=cluster_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    logger.info(f"Testing get_memory_usage for pod: {entity_name}")
    results['memory_usage'] = pod_entity.get_memory_usage(
        pod_name=entity_name,
        cluster_name=cluster_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    logger.info(f"Testing get_restart_count for pod: {entity_name}")
    results['restart_count'] = pod_entity.get_restart_count(
        pod_name=entity_name,
        cluster_name=cluster_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    logger.info(f"Testing get_container_status for pod: {entity_name}")
    results['container_status'] = pod_entity.get_container_status(
        pod_name=entity_name,
        cluster_name=cluster_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    return results

def test_node_entity(query_client, entity_name, cluster_name, since_time_ms, until_time_ms):
    """Test NodeEntity methods"""
    logger.info("Testing NodeEntity...")
    nrql_manager = NRQLManager()
    node_entity = NodeEntity(query_client, nrql_manager)
    
    results = {}
    
    logger.info(f"Testing get_cpu_usage for node: {entity_name}")
    results['cpu_usage'] = node_entity.get_cpu_usage(
        node_name=entity_name,
        cluster_name=cluster_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    logger.info(f"Testing get_memory_usage for node: {entity_name}")
    results['memory_usage'] = node_entity.get_memory_usage(
        node_name=entity_name,
        cluster_name=cluster_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    logger.info(f"Testing get_pod_count for node: {entity_name}")
    results['pod_count'] = node_entity.get_pod_count(
        node_name=entity_name,
        cluster_name=cluster_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    logger.info(f"Testing get_condition for node: {entity_name}")
    results['condition'] = node_entity.get_condition(
        node_name=entity_name,
        cluster_name=cluster_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    return results

def test_host_entity(query_client, entity_name, since_time_ms, until_time_ms):
    """Test HostEntity methods"""
    logger.info("Testing HostEntity...")
    nrql_manager = NRQLManager()
    host_entity = HostEntity(query_client, nrql_manager)
    
    results = {}
    
    logger.info(f"Testing get_cpu_memory_disk for host: {entity_name}")
    results['system'] = host_entity.get_cpu_memory_disk(
        hostname=entity_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    logger.info(f"Testing get_network_io for host: {entity_name}")
    results['network'] = host_entity.get_network_io(
        hostname=entity_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    logger.info(f"Testing get_processes for host: {entity_name}")
    results['processes'] = host_entity.get_processes(
        hostname=entity_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    return results

def test_application_entity(query_client, entity_name, since_time_ms, until_time_ms):
    """Test ApplicationEntity methods"""
    logger.info("Testing ApplicationEntity...")
    nrql_manager = NRQLManager()
    app_entity = ApplicationEntity(query_client, nrql_manager)
    
    results = {}
    
    logger.info(f"Testing get_response_time for application: {entity_name}")
    results['response_time'] = app_entity.get_response_time(
        app_name=entity_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    logger.info(f"Testing get_throughput for application: {entity_name}")
    results['throughput'] = app_entity.get_throughput(
        app_name=entity_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    logger.info(f"Testing get_error_rate for application: {entity_name}")
    results['error_rate'] = app_entity.get_error_rate(
        app_name=entity_name,
        since_time_ms=since_time_ms,
        until_time_ms=until_time_ms
    )
    
    return results

def save_to_json(data, filename="entity_class_data.json"):
    """Save data to a JSON file"""
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2, default=str)
    logger.info(f"Data saved to {filename}")

def main():
    """Main function to test the entity classes"""
    # Check for API key and account ID
    api_key = os.environ.get('NEWRELIC_API_KEY')
    account_id = os.environ.get('NEWRELIC_ACCOUNT_ID')
    
    if not api_key or not account_id:
        logger.error("API key or account ID not set. Please set NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID environment variables.")
        sys.exit(1)
    
    # Create New Relic client
    client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
    query_client = NewRelicQueryClient(client)
    
    # Create EntityAnalyzer to help with entity identification
    analyzer = EntityAnalyzer(client, debug=True)
    
    # Define time window for analysis
    now = datetime.now(UTC)
    since_time = now - timedelta(minutes=60)  # 1 hour ago
    until_time = now
    
    # Convert to epoch milliseconds
    since_time_ms = int(since_time.timestamp() * 1000)
    until_time_ms = int(until_time.timestamp() * 1000)
    
    logger.info(f"Analyzing entity {TEST_ENTITY_GUID}")
    logger.info(f"Time window: {since_time} to {until_time}")
    logger.info(f"In epoch milliseconds: {since_time_ms} to {until_time_ms}")
    
    try:
        # First, determine the entity type
        entity_type, entity_name, cluster_name = determine_entity_type_from_guid(analyzer, TEST_ENTITY_GUID)
        
        if not entity_type or not entity_name:
            logger.error("Could not determine entity type or name.")
            sys.exit(1)
        
        # Test the appropriate entity class based on entity type
        results = {}
        
        if 'KUBERNETES_POD' in entity_type or entity_type == 'K8S_POD':
            results = test_pod_entity(query_client, entity_name, cluster_name, since_time_ms, until_time_ms)
        elif 'KUBERNETES_NODE' in entity_type or entity_type == 'K8S_NODE':
            results = test_node_entity(query_client, entity_name, cluster_name, since_time_ms, until_time_ms)
        elif 'HOST' in entity_type:
            results = test_host_entity(query_client, entity_name, since_time_ms, until_time_ms)
        elif 'APPLICATION' in entity_type:
            results = test_application_entity(query_client, entity_name, since_time_ms, until_time_ms)
        else:
            logger.warning(f"No specific entity class for type: {entity_type}")
            logger.info("Using EntityAnalyzer to get metrics...")
            entity_data = analyzer._analyze_entity_sync(
                entity_guid=TEST_ENTITY_GUID,
                since_time=since_time,
                until_time=until_time,
                collect_metrics=True
            )
            results = entity_data.get('metrics', {})
        
        # Print summary of results
        print("\n" + "="*80)
        print(f"RESULTS SUMMARY FOR: {entity_name} ({entity_type})")
        print("="*80)
        
        for metric_type, metric_data in results.items():
            if isinstance(metric_data, list):
                data_points = len(metric_data)
                print(f"{metric_type}: {data_points} data points")
                if data_points > 0:
                    # Print first data point as example
                    print(f"  Sample: {metric_data[0]}")
            else:
                print(f"{metric_type}: {metric_data}")
        
        print("="*80 + "\n")
        
        # Save results to JSON
        save_to_json(results, f"entity_class_{TEST_ENTITY_GUID.replace('|', '_')}.json")
        
    except Exception as e:
        logger.error(f"Error testing entity classes: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 