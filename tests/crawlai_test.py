import os
from crawl4ai import <PERSON><PERSON>raw<PERSON>, AsyncWebCrawler
from crawl4ai.extraction_strategy import LLMExtractionStrategy
from pydantic import BaseModel, Field
import asyncio
from typing import List

os.environ["AZURE_API_KEY"] = "********************************"
os.environ["AZURE_API_BASE"] = "https://obv-gpt-v4.openai.azure.com/"
os.environ["AZURE_API_VERSION"] = (
    "2025-01-01-preview"  # This is just an example, please replace with the correct version
)


async def main():
    class Entity(BaseModel):
        name: str
        description: str

    class Relationship(BaseModel):
        entity1: Entity
        entity2: Entity
        description: str
        relation_type: str

    class KnowledgeGraph(BaseModel):
        entities: List[Entity]
        relationships: List[Relationship]

    extraction_strategy = LLMExtractionStrategy(
        provider="azure/gpt-4o",
        api_base=os.environ["AZURE_API_BASE"],
        api_token=os.environ["AZURE_API_KEY"],
        api_version=os.environ["AZURE_API_VERSION"],
        schema=KnowledgeGraph.model_json_schema(),
        extraction_type="schema",
        instruction="""Extract entities and relationships from the given text.""",
    )
    async with AsyncWebCrawler() as crawler:
        url = "https://en.wikipedia.org/wiki/Knowledge_graph"
        result = await crawler.arun(
            url=url,
            bypass_cache=True,
            extraction_strategy=extraction_strategy,
        )
        print(result.extracted_content)
        # with open(os.path.join(__data__, "kb_test.json"), "w") as f:
        #     f.write(result.extracted_content)

    print("Done")


if __name__ == "__main__":
    asyncio.run(main())
