"""
Tests for New Relic NerdGraph GraphQL client.
"""

import json
import os
from datetime import datetime, timedelta
import pytest
from lib.new_relic import NewRelicGraphQLClient, Region, GraphQLResponse

from dotenv import load_dotenv
load_dotenv(override=True)

# Test incident data
TEST_INCIDENT = {
    "issueId": "8622b333-69af-467e-a649-e21769026f6e",
    "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/8622b333-69af-467e-a649-e21769026f6e?notifier=WEBHOOK",
    "title": "ism-sync-handler-service-deployment-9dc76cf5d-rxnqj query result is > 2.0 for 30 minutes on 'Evicted Pods'",
    "priority": "CRITICAL",
    "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXw3NDg3NDIwNTI2NzUyMTk4OTk1"],
    "impactedEntities": ["ism-sync-handler-service-deployment-9dc76cf5d-rxnqj"],
    "totalIncidents": "1",
    "state": "ACTIVATED",
    "trigger": "STATE_CHANGE",
    "isCorrelated": "false",
    "createdAt": *************,
    "updatedAt": *************,
    "sources": ["newrelic"],
    "alertPolicyNames": ["Neurons k8s Infra - Critical"],
    "alertConditionNames": ["Evicted Pods"],
    "workflowName": "obv-ai-processing-neurons",
    "chartLink": "https://gorgon.nr-assets.net/image/b5848176-4f67-413c-a02e-44f501286761?config.legend.enabled=false&width=400&height=210",
    "product": "neurons",
    "nr_region": "us"
}

class TestNewRelicGraphQLClient:
    @pytest.fixture
    def client(self):
        """Create a New Relic GraphQL client instance."""
        return NewRelicGraphQLClient(
            api_key=os.getenv("NEWRELIC_API_KEY"),
            region=Region.US,
            account_id=os.getenv("NEWRELIC_ACCOUNT_ID")
        )

    def test_fetch_pod_details(self, client):
        """Test fetching pod details for the incident."""
        # Convert timestamps to datetime for query
        incident_time = datetime.fromtimestamp(TEST_INCIDENT["createdAt"] / 1000)
        start_time = incident_time - timedelta(hours=5)
        end_time = incident_time + timedelta(minutes=30)
        
        pod_name = TEST_INCIDENT["impactedEntities"][0]
        
        query = """
        query($accountId: Int!, $podName: String!, $startTime: EpochMilliseconds!, $endTime: EpochMilliseconds!) {
            actor {
                account(id: $accountId) {
                    nrql(query: "SELECT * FROM K8sPodSample WHERE podName = '$podName' SINCE $startTime UNTIL $endTime") {
                        results
                        metadata {
                            timeWindow {
                                start
                                end
                            }
                        }
                    }
                }
            }
        }
        """
        
        variables = {
            "accountId": int(client.account_id),
            "podName": pod_name,
            "startTime": int(start_time.timestamp() * 1000),
            "endTime": int(end_time.timestamp() * 1000)
        }
        
        response = client.query(query, variables)
        assert isinstance(response, GraphQLResponse)
        assert response.errors is None
        
        # Verify pod data structure
        pod_data = response.data["actor"]["account"]["nrql"]["results"]
        assert isinstance(pod_data, list)
        if pod_data:
            assert "podName" in pod_data[0]
            assert "status" in pod_data[0]
            assert "namespace" in pod_data[0]

    def test_fetch_infrastructure_events(self, client):
        """Test fetching infrastructure events for node disk pressure."""
        # Convert timestamps to datetime for query
        incident_time = datetime.fromtimestamp(TEST_INCIDENT["createdAt"] / 1000)
        start_time = incident_time - timedelta(days=1)  # Look back 1 day
        end_time = incident_time + timedelta(minutes=30)
        
        node_name = "aks-default-********-vmss000bmq"
        cluster_name = "uku-prd-neurons"
        
        query = """
        query($accountId: Int!, $nodeName: String!, $clusterName: String!, $startTime: EpochMilliseconds!, $endTime: EpochMilliseconds!) {
            actor {
                account(id: $accountId) {
                    nrql(query: "FROM InfrastructureEvent SELECT event.involvedObject.name, event.involvedObject.kind, event.reason, event.message WHERE category = 'kubernetes' AND (clusterName LIKE '%$clusterName%' OR k8s.cluster.name LIKE '%$clusterName%') AND event.type = 'Warning' AND event.involvedObject.name = '$nodeName' LIMIT MAX SINCE $startTime UNTIL $endTime") {
                        results
                        metadata {
                            timeWindow {
                                start
                                end
                            }
                        }
                    }
                }
            }
        }
        """
        
        variables = {
            "accountId": int(client.account_id),
            "nodeName": node_name,
            "clusterName": cluster_name,
            "startTime": int(start_time.timestamp() * 1000),
            "endTime": int(end_time.timestamp() * 1000)
        }
        
        response = client.query(query, variables)
        assert isinstance(response, GraphQLResponse)
        assert response.errors is None
        
        # Verify infrastructure event data structure
        events = response.data["actor"]["account"]["nrql"]["results"]
        assert isinstance(events, list)
        if events:
            event = events[0]
            assert "event.involvedObject.name" in event
            assert "event.involvedObject.kind" in event
            assert "event.reason" in event
            assert "event.message" in event

    def test_fetch_incident_details(self, client):
        """Test fetching incident details."""
        query = """
        query($accountId: Int!, $issueId: String!) {
            actor {
                account(id: $accountId) {
                    aiIssue(id: $issueId) {
                        title
                        state
                        priority
                        createdAt
                        updatedAt
                        impactedEntities {
                            name
                            type
                            guid
                        }
                    }
                }
            }
        }
        """
        
        variables = {
            "accountId": int(client.account_id),
            "issueId": TEST_INCIDENT["issueId"]
        }
        
        response = client.query(query, variables)
        assert isinstance(response, GraphQLResponse)
        assert response.errors is None
        
        # Verify incident data structure
        issue = response.data["actor"]["account"]["aiIssue"]
        assert issue["title"] == TEST_INCIDENT["title"]
        assert issue["state"] == TEST_INCIDENT["state"]
        assert issue["priority"] == TEST_INCIDENT["priority"]
        assert issue["createdAt"] == TEST_INCIDENT["createdAt"]
        assert issue["updatedAt"] == TEST_INCIDENT["updatedAt"]
