#!/usr/bin/env python3
"""
Focused test script for retrieving logs from New Relic for a specific pod entity with a defined time window.
This script tests the get_entity_logs method of the NewRelicLogsClient.
"""

import os
import sys
import logging
from datetime import datetime, timezone, timedelta
import argparse
import dotenv
import json

# Import New Relic client classes
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.base import Region
from lib.new_relic.logs import NewRelicLogsClient

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test New Relic logs retrieval for a specific entity.")
    parser.add_argument("--entity-guid", default="MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg", 
                      help="Entity GUID to query (default: MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg)")
    parser.add_argument("--entity-type", default="K8S_POD", 
                      help="Entity type (default: K8S_POD)")
    parser.add_argument("--hours", type=int, default=1, 
                      help="Time window in hours from now (default: 1)")
    parser.add_argument("--limit", type=int, default=50, 
                      help="Maximum number of logs to retrieve (default: 50)")
    parser.add_argument("--region", choices=["US", "EU"], default="US", 
                      help="New Relic region (default: US)")
    parser.add_argument("--output", choices=["log", "json"], default="log", 
                      help="Output format - log to console or save as JSON (default: log)")
    parser.add_argument("--output-file", default="entity_logs.json", 
                      help="Output file for JSON format (default: entity_logs.json)")
    parser.add_argument("--debug", action="store_true", 
                      help="Enable debug mode")
    return parser.parse_args()

def test_entity_logs(args):
    """Test New Relic logs retrieval for a specific entity."""
    # Get required environment variables
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        logger.error("Missing required environment variables: NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set.")
        return
    
    # Define time range for logs
    until_time = datetime.now(timezone.utc)
    since_time = until_time - timedelta(hours=args.hours)
    
    logger.info(f"Testing log retrieval for entity GUID: {args.entity_guid}")
    logger.info(f"Entity type: {args.entity_type}")
    logger.info(f"Time range: {since_time.isoformat()} to {until_time.isoformat()} (last {args.hours} hours)")
    
    # Initialize the New Relic GraphQL client
    region = Region.US if args.region == "US" else Region.EU
    nr_client = NewRelicGraphQLClient(
        api_key=api_key,
        account_id=account_id,
        region=region,
        debug=args.debug
    )
    
    # Initialize the New Relic Logs client
    logs_client = NewRelicLogsClient(client=nr_client, debug=args.debug)
    
    try:
        # Get logs using entity_guid
        logger.info("Retrieving logs using get_entity_logs()")
        
        # Get entity-type specific log configuration if available
        logs_config = logs_client.get_logs_config_for_entity_type(args.entity_type)
        if logs_config:
            logger.info(f"Using entity-specific log configuration for {args.entity_type}")
            logger.info(f"Log filters: {logs_config.get('filters', [])}")
            logger.info(f"Log partition: {logs_config.get('partition', 'default')}")
        else:
            logs_config = None
            logger.info(f"No specific log configuration found for entity type {args.entity_type}")
        
        # Retrieve logs
        entity_logs = logs_client.get_entity_logs(
            entity_guid=args.entity_guid,
            since=since_time,
            until=until_time,
            entity_type=args.entity_type,
            logs_config=logs_config,
            limit=args.limit,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(entity_logs)} logs")
        
        # Handle output
        if args.output == "json":
            # Save logs to JSON file
            with open(args.output_file, 'w') as f:
                json.dump(entity_logs, f, indent=2, default=str)
            logger.info(f"Logs saved to {args.output_file}")
        else:
            # Print logs to console
            print_logs(entity_logs)
        
        return entity_logs
        
    except Exception as e:
        logger.error(f"Error retrieving logs: {str(e)}", exc_info=True)
        return []

def print_logs(logs):
    """Pretty print logs for readability."""
    if not logs:
        logger.info("No logs found")
        return
    
    logger.info(f"Found {len(logs)} logs:")
    
    for i, log in enumerate(logs):
        logger.info(f"Log {i+1}:")
        # Format the log as a pretty-printed JSON string
        formatted_log = json.dumps(log, indent=2, default=str)
        logger.info(formatted_log)
        logger.info("-" * 80)

if __name__ == "__main__":
    args = parse_args()
    logs = test_entity_logs(args)
    logger.info(f"Test completed with {len(logs)} logs retrieved") 