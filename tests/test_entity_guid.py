#!/usr/bin/env python
"""
Test script for analyzing a specific New Relic entity using EntityAnalyzer.
This script analyzes the entity with GUID: MTA5MzYyMHxJTkZSQXxOQXwtNTY4MTU0NzYxNTQ2NjE1NDQ1Nw
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pprint import pprint

import dotenv

# Load environment variables
dotenv.load_dotenv()

# Add the parent directory to the path so we can import the library
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.new_relic.base import UTC
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.analyzer import EntityAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('entity_analyzer_test')

# Entity GUID to analyze
TEST_ENTITY_GUID = "MTA5MzYyMHxJTkZSQXxOQXwtNTY4MTU0NzYxNTQ2NjE1NDQ1Nw"

def print_entity_summary(entity_data):
    """Print a summary of the entity data"""
    print("\n" + "="*80)
    print(f"ENTITY SUMMARY: {entity_data.get('entity_name')} ({entity_data.get('entity_type')})")
    print("="*80)
    
    print(f"GUID: {entity_data.get('entity_guid')}")
    print(f"Type: {entity_data.get('entity_type')}")
    print(f"Cluster: {entity_data.get('cluster_id')}")
    
    if entity_data.get('metadata'):
        print("\nMETADATA:")
        for key, value in entity_data.get('metadata', {}).items():
            print(f"  {key}: {value}")
    
    if entity_data.get('relationships'):
        print("\nRELATIONSHIPS:")
        for rel in entity_data.get('relationships', []):
            rel_type = rel.get('relation', 'unknown')
            target_type = rel.get('related_type', 'unknown')
            entity_count = len(rel.get('query_results', []))
            print(f"  {rel_type} {target_type} ({entity_count} entities)")
            
            # Print up to 5 related entities
            for i, entity in enumerate(rel.get('query_results', [])[:5]):
                name = entity.get('name', 'unknown')
                guid = entity.get('entityGuid', 'unknown')
                print(f"    - {name} ({guid})")
            
            if entity_count > 5:
                print(f"    ... and {entity_count - 5} more")
    
    if entity_data.get('metrics'):
        print("\nMETRICS:")
        for metric_type, metric_data in entity_data.get('metrics', {}).items():
            result_count = len(metric_data) if isinstance(metric_data, list) else 1
            print(f"  {metric_type}: {result_count} data points")
    
    if entity_data.get('logs'):
        print("\nLOGS:")
        log_count = len(entity_data.get('logs', []))
        print(f"  {log_count} log entries found")
        
        # Print up to 5 logs
        for i, log in enumerate(entity_data.get('logs', [])[:5]):
            timestamp = log.get('timestamp', 'unknown')
            message = log.get('message', 'no message')[:100]
            print(f"    - [{timestamp}] {message}")
        
        if log_count > 5:
            print(f"    ... and {log_count - 5} more")
    
    if entity_data.get('events'):
        print("\nEVENTS:")
        event_count = len(entity_data.get('events', []))
        print(f"  {event_count} events found")
        
        # Print up to 5 events
        for i, event in enumerate(entity_data.get('events', [])[:5]):
            timestamp = event.get('timestamp', 'unknown')
            message = event.get('message', 'no message')[:100]
            print(f"    - [{timestamp}] {message}")
        
        if event_count > 5:
            print(f"    ... and {event_count - 5} more")
    
    print("="*80 + "\n")

def save_to_json(entity_data, filename="entity_data.json"):
    """Save entity data to a JSON file"""
    with open(filename, 'w') as f:
        json.dump(entity_data, f, indent=2, default=str)
    logger.info(f"Entity data saved to {filename}")

def main():
    """Main function to test the entity analyzer"""
    # Check for API key and account ID
    api_key = os.environ.get('NEWRELIC_API_KEY')
    account_id = os.environ.get('NEWRELIC_ACCOUNT_ID')
    
    if not api_key or not account_id:
        logger.error("API key or account ID not set. Please set NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID environment variables.")
        sys.exit(1)
    
    # Create New Relic client
    client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
    
    # Create EntityAnalyzer
    analyzer = EntityAnalyzer(client, debug=True)
    
    # Define time window for analysis
    now = datetime.now(UTC)
    since_time = now - timedelta(minutes=60)  # 1 hour ago
    until_time = now
    
    logger.info(f"Analyzing entity {TEST_ENTITY_GUID}")
    logger.info(f"Time window: {since_time} to {until_time}")
    
    try:
        # First, get basic entity details to show what we're analyzing
        entity_details = analyzer._get_entity_details(TEST_ENTITY_GUID)
        if entity_details:
            logger.info(f"Entity found: {entity_details.get('name')} ({entity_details.get('type')})")
        else:
            logger.error(f"Entity not found: {TEST_ENTITY_GUID}")
            sys.exit(1)
        
        # Perform comprehensive analysis using the synchronous version of analyze_entity
        entity_data = analyzer._analyze_entity_sync(
            entity_guid=TEST_ENTITY_GUID,
            since_time=since_time,
            until_time=until_time,
            collect_metrics=True,
            collect_logs=True,
            collect_events=True
        )
        
        # Print summary
        print_entity_summary(entity_data)
        
        # Save to JSON for further analysis
        save_to_json(entity_data, f"entity_{TEST_ENTITY_GUID.replace('|', '_')}.json")
        
    except Exception as e:
        logger.error(f"Error analyzing entity: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 