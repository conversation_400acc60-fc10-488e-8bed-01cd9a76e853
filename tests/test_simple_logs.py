#!/usr/bin/env python3
"""
Very simple test script for the New Relic logs module.
This script uses basic NRQL queries to test the functionality.
"""

import os
import logging
from datetime import datetime, timezone, timedelta
import dotenv
import json

# Import New Relic client classes
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.base import Region
from lib.new_relic.logs import NewRelicLogsClient

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Test basic New Relic logs functionality."""
    # Get required environment variables
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        logger.error("Missing required environment variables: NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set.")
        return
    
    # Define time range for logs (last hour)
    until_time = datetime.now(timezone.utc)
    since_time = until_time - timedelta(hours=1)
    
    logger.info(f"Testing basic log queries")
    logger.info(f"Time range: {since_time.isoformat()} to {until_time.isoformat()}")
    
    # Initialize the New Relic GraphQL client
    nr_client = NewRelicGraphQLClient(
        api_key=api_key,
        account_id=account_id,
        region=Region.US,
        debug=True
    )
    
    # Initialize the New Relic Logs client
    logs_client = NewRelicLogsClient(client=nr_client, debug=True)
    
    try:
        # Test 1: Simple error logs query
        logger.info("Test 1: Simple error logs query")
        simple_query = f"""
        SELECT timestamp, level, message
        FROM Log
        WHERE level = 'error'
        SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
        UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
        LIMIT 5
        """
        
        error_logs = logs_client.query_logs(
            query=simple_query,
            limit=5,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(error_logs)} error logs")
        print_logs(error_logs)
        
        # Test 2: Kubernetes pods query
        logger.info("Test 2: Kubernetes pods query")
        k8s_query = f"""
        SELECT timestamp, level, message, kubernetes.pod.name
        FROM Log
        WHERE kubernetes.pod.name IS NOT NULL
        SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
        UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
        LIMIT 5
        """
        
        k8s_logs = logs_client.query_logs(
            query=k8s_query,
            limit=5,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(k8s_logs)} Kubernetes logs")
        print_logs(k8s_logs)
        
        # Test 3: List available pod names
        logger.info("Test 3: List available pod names")
        pods_query = f"""
        SELECT uniqueCount(kubernetes.pod.name) as pod_count, latest(kubernetes.pod.name) as pod_names
        FROM Log
        WHERE kubernetes.pod.name IS NOT NULL
        SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
        UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
        FACET kubernetes.pod.name
        LIMIT 20
        """
        
        pods = logs_client.query_logs(
            query=pods_query,
            limit=20,
            account_id=account_id
        )
        
        logger.info(f"Found {len(pods)} unique pod names")
        print_logs(pods)
        
        # If we found any pods, try to get logs for the first one
        if pods and len(pods) > 0 and 'pod_names' in pods[0]:
            pod_name = pods[0]['pod_names']
            logger.info(f"Test 4: Getting logs for pod {pod_name}")
            
            pod_logs_query = f"""
            SELECT timestamp, level, message, kubernetes.pod.name, kubernetes.container.name
            FROM Log
            WHERE kubernetes.pod.name = '{pod_name}'
            SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
            UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
            LIMIT 10
            """
            
            pod_logs = logs_client.query_logs(
                query=pod_logs_query,
                limit=10,
                account_id=account_id
            )
            
            logger.info(f"Retrieved {len(pod_logs)} logs for pod {pod_name}")
            print_logs(pod_logs)
        
    except Exception as e:
        logger.error(f"Error retrieving logs: {str(e)}", exc_info=True)

def print_logs(logs):
    """Pretty print logs for readability."""
    if not logs:
        logger.info("No logs found")
        return
    
    logger.info(f"Found {len(logs)} logs:")
    
    for i, log in enumerate(logs):
        logger.info(f"Log {i+1}:")
        # Format the log as a pretty-printed JSON string
        formatted_log = json.dumps(log, indent=2, default=str)
        logger.info(formatted_log)
        logger.info("-" * 80)

if __name__ == "__main__":
    main() 