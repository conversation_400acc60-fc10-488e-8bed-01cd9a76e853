#!/usr/bin/env python
"""
Test script for the EntityAnalyzer class.
This script allows you to test the analyzer with a given entity GUID.
"""

import os
import sys
import json
import argparse
from datetime import datetime, timed<PERSON>ta
from pprint import pprint

import dotenv

dotenv.load_dotenv()

# Add the parent directory to the path so we can import the library
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.new_relic.base import UTC
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.logs import NewRelicLogsClient
from lib.new_relic.analyzer import EntityAnalyzer
from lib.new_relic.config import MetricsConfigManager


def setup_argument_parser():
    """Set up command line argument parser"""
    parser = argparse.ArgumentParser(description='Test the New Relic Entity Analyzer')
    
    parser.add_argument('--entity-guid', '-e',
                        help='The New Relic entity GUID to analyze')
    
    parser.add_argument('--api-key', '-k',
                        default=os.environ.get('NEWRELIC_API_KEY'),
                        help='New Relic API key (defaults to NEWRELIC_API_KEY env var)')
    
    parser.add_argument('--account-id', '-a',
                        default=os.environ.get('NEWRELIC_ACCOUNT_ID'),
                        help='New Relic account ID (defaults to NEWRELIC_ACCOUNT_ID env var)')
    
    # Time window options
    parser.add_argument('--window', '-w', type=int, default=30,
                        help='Time window in minutes to look before and after (default: 30)')
    
    # Custom time range options (as alternatives to window)
    parser.add_argument('--since', '-s', 
                        help='Start time (ISO format or epoch milliseconds). Overrides --window')
    
    parser.add_argument('--until', '-u',
                        help='End time (ISO format or epoch milliseconds). Overrides --window')
    
    # Entity-specific options
    parser.add_argument('--product', '-p',
                        help='Product (used for filtering metrics and logs)')
    
    parser.add_argument('--region', '-r',
                        help='Region (used for filtering metrics and logs)')
    
    # Collection options
    parser.add_argument('--collect-metrics', action='store_true',
                        help='Collect metrics for the entity')
    
    parser.add_argument('--collect-logs', action='store_true',
                        help='Collect logs for the entity')
    
    parser.add_argument('--collect-events', action='store_true',
                        help='Collect Kubernetes events for the entity (if applicable)')
    
    parser.add_argument('--collect-all', action='store_true',
                        help='Collect all available data (metrics, logs, and events)')
    
    # Output options
    parser.add_argument('--output', '-o',
                        help='Output file to save full analysis results (JSON format)')
    
    # Debug options
    parser.add_argument('--debug', action='store_true',
                        help='Enable debug output')
    
    parser.add_argument('--debug-full', action='store_true',
                        help='Enable full debug output (more verbose)')
    
    return parser
    

def parse_time_arg(time_arg):
    """Parse a time argument in various formats"""
    if not time_arg:
        return None
        
    # If it's a numeric string, interpret as epoch milliseconds
    if time_arg.isdigit():
        epoch_ms = int(time_arg)
        return datetime.fromtimestamp(epoch_ms / 1000.0, UTC)
    
    # Otherwise, try to parse as ISO format
    try:
        return datetime.fromisoformat(time_arg.replace('Z', '+00:00'))
    except ValueError:
        raise ValueError(f"Invalid time format: {time_arg}. Use ISO format (YYYY-MM-DDThh:mm:ss) or epoch milliseconds.")


def print_entity_summary(entity_data):
    """Print a summary of the entity analysis results"""
    print("\n" + "="*80)
    print(f"ENTITY ANALYSIS SUMMARY FOR: {entity_data['entity_name']}")
    print("="*80)
    
    print(f"\nBasic Information:")
    print(f"  Entity GUID: {entity_data['entity_guid']}")
    print(f"  Entity Type: {entity_data['entity_type']}")
    print(f"  Cluster ID:  {entity_data['cluster_id'] or 'Unknown'}")
    print(f"  Product:     {entity_data['product'] or 'Unknown'}")
    print(f"  Region:      {entity_data['region'] or 'Unknown'}")
    
    # Print metadata
    print("\nMetadata:")
    for key, value in entity_data['metadata'].items():
        if key == 'tags':
            print(f"  Tags:")
            for tag_key, tag_values in value.items():
                print(f"    {tag_key}: {', '.join(tag_values) if tag_values else 'N/A'}")
        else:
            print(f"  {key}: {value}")
    
    # Print relationships summary
    print("\nRelationships:")
    for rel in entity_data.get('relationships', []):
        rel_type = rel.get('relation', 'unknown')
        target_type = rel.get('related_type', 'unknown')
        entity_count = len(rel.get('query_results', []))
        print(f"  {rel_type} {target_type} ({entity_count} entities)")
        
        # Print up to 3 related entities
        for i, entity in enumerate(rel.get('query_results', [])[:3]):
            name = entity.get('name', 'unknown')
            guid = entity.get('entityGuid', 'unknown')
            print(f"    - {name} ({guid})")
        
        if entity_count > 3:
            print(f"    ... and {entity_count - 3} more")
    
    # Print metrics summary
    print("\nMetrics:")
    for metric_name, metric_data in entity_data['metrics'].items():
        data_points = len(metric_data) if isinstance(metric_data, list) else 1
        print(f"  {metric_name}: {data_points} data points collected")
    
    # Print logs summary
    log_count = len(entity_data['logs'])
    print(f"\nLogs: {log_count} entries collected")
    if log_count > 0:
        # Print a few sample log entries
        print("  Sample log entries:")
        for i, log in enumerate(entity_data['logs'][:3]):
            timestamp = log.get('timestamp', 'N/A')
            level = log.get('level', 'N/A')
            message = log.get('message', 'N/A')
            print(f"    [{timestamp}] {level}: {message[:100]}...")
    
    # Print events summary
    event_count = len(entity_data['events'])
    print(f"\nKubernetes Events: {event_count} events collected")
    if event_count > 0:
        # Group events by source
        pod_events = [e for e in entity_data['events'] if e.get('source') == 'pod']
        node_events = [e for e in entity_data['events'] if e.get('source') == 'node']
        
        print(f"  Pod events: {len(pod_events)}")
        print(f"  Node events: {len(node_events)}")
        
        # Print a few sample events
        print("  Sample events:")
        for i, event in enumerate(entity_data['events'][:3]):
            timestamp = event.get('timestamp', 'N/A')
            reason = event.get('reason', 'N/A')
            message = event.get('message', 'N/A')
            source = event.get('source', 'N/A')
            print(f"    [{timestamp}] [{source}] {reason}: {message[:100]}...")
    
    print("="*80)


def determine_time_window(args):
    """Determine the time window for analysis based on command line arguments"""
    # If explicit since/until are provided, use those
    if args.since or args.until:
        since_time = parse_time_arg(args.since) if args.since else datetime.now(UTC) - timedelta(minutes=args.window)
        until_time = parse_time_arg(args.until) if args.until else datetime.now(UTC)
    else:
        # Use the window argument to create a time window around the current time
        now = datetime.now(UTC)
        since_time = now - timedelta(minutes=args.window)
        until_time = now
        
    return since_time, until_time


def main():
    """Main function"""
    # Parse command line arguments
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # If collect_all is specified, collect everything
    if args.collect_all:
        args.collect_metrics = True
        args.collect_logs = True
        args.collect_events = True
    
    # Check that an entity GUID was provided
    if not args.entity_guid:
        parser.error("--entity-guid is required")
    
    # Check for required credentials
    if not args.api_key:
        parser.error("New Relic API key is required (use --api-key or set NEWRELIC_API_KEY environment variable)")
        
    if not args.account_id:
        parser.error("New Relic account ID is required (use --account-id or set NEWRELIC_ACCOUNT_ID environment variable)")
    
    # Create the New Relic client and analyzer
    client = NewRelicGraphQLClient(
        api_key=args.api_key,
        account_id=args.account_id
    )
    
    # Determine debug mode
    debug_enabled = args.debug or args.debug_full
    full_debug = args.debug_full
    
    # Create the analyzer with debug mode if enabled
    analyzer = EntityAnalyzer(client, debug=debug_enabled)
    
    # Determine the time window for analysis
    since_time, until_time = determine_time_window(args)
    
    print(f"Analyzing entity: {args.entity_guid}")
    print(f"Time window: {since_time.isoformat()} to {until_time.isoformat()}")
    
    if debug_enabled:
        if full_debug:
            print("Debug mode: Enabled (Full Debug)")
        else:
            print("Debug mode: Enabled")
    else:
        print("Debug mode: Disabled")
    
    try:
        # Analyze the entity using the synchronous method
        entity_data = analyzer._analyze_entity_sync(
            entity_guid=args.entity_guid,
            since_time=since_time,
            until_time=until_time,
            product=args.product,
            region=args.region,
            collect_metrics=args.collect_metrics,
            collect_logs=args.collect_logs,
            collect_events=args.collect_events
        )
        
        # Print a summary of the results
        if entity_data.get('found'):
            print_entity_summary(entity_data)
        else:
            print(f"Entity not found: {args.entity_guid}")
        
        # Save full results to a file if requested
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(entity_data, f, indent=2, default=str)
            print(f"\nFull analysis results saved to: {args.output}")
        
    except Exception as e:
        print(f"\n" + "="*80)
        print(f"ERROR: {str(e)}")
        print("="*80)
        if debug_enabled:
            import traceback
            traceback.print_exc()
        sys.exit(1)
    
    return 0


if __name__ == "__main__":
    sys.exit(main()) 