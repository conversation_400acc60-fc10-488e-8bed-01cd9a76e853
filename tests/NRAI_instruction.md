This document outlines the APIs used to build a LLM chat experience with NRAI. Note that currently these APIs are only available to New Relic, however should a customer want to use them, we can open these APIs up to the public.

# Core Concepts

### LLM

As you may have heard, the LLM or Large Language Model is a generative AI model. New Relic leverages LLMs along with a set of tools available to the LLM to generate responses to prompts created by our users.The number of tools the LLM has at its disposal is constantly changing and expanding.

### Tools

A tool is something an LLM can use to find out more information to more accurately answer a given prompt. Tools take input and deliver output to the LLM. For example, a tool might allow the LLM to query your data with NRQL. Another tool might be able to view the errors your system is experiencing. Another tool might specialize in translating natural language into NRQL.

#### Agents

Agents have access to an LLM and a subset of tools. They also serve a particular purpose and respond to prompts with that purpose in mind. For Ask NRAI, the agent's purpose is to be a helpful New Relic assistant and has access to a wide variety of tools.

# Prompt

A prompt is simply input for the LLM to respond to. It can be a question, command, or any general statement.

# NRAI

NRAI is the system that marries tools, agents, and LLMs to take a prompt and generate a response.

### Context

In this document, the term 'context' will be used to describe an area where conversations can take place. The context describes the topic of conversation. A single context can house multiple conversations.

This context is taken into account when NRAI responds to prompts.

### Thread

A thread is simply a representation of a conversation for a given context. A thread can have many comments associated with it. When dealing with NRAI, thread history is also taken into account when responding to the latest user prompt.

### Comment

A comment serves as a container for both prompts and responses.

# Mention

The collaboration system was originally built to facilitate conversations between New Relic users. As with most modern chat interfaces, various things can be referenced in conversations: other users, files, code, links, just about anything.

While Ask NRAI is very chat-centric, NRAI itself is not tied to conversations. These messages are stored separately and thus are 'mentioned' when a response is received after a prompt has been provided.

# "Grok" Message

A "Grok" message is actually one of the messages previously discussed. NRAI was originally going to be dubbed "Grok" until Twitter went live with their own. Unfortunately there were a few places tied to this name and have yet to be rebranded from a code perspective.

Anyway, these messages, while very similar to comments, are not exactly the same. These messages act as a historical log of all of the interactions that are done with the LLM. Things like temporary 'intermediate messages' are stored permanently here. LLM function call inputs are also stored here.

#### Card

A 'card' is a data set that represents a visual, sometimes interactive, component. Cards can contain instructions to show a visualization, forms, follow up

questions, etc.

### Message State

Since cards can perform actions that are meant to be executed a single time. Think of a form that creates new infrastructure for instance - something like that shouldn't accidentally be run more than once. To remember that this form has been filled out, leaving the card rendered differently than it was originally displayed, we need a data set to represent the current state of that card.

Message state fulfills that need and works hand in hand with cards to ensure a good experience. When developing a chat experience outside of Ask NRAI, these 2 concepts should be taken into account.

#### Message Stream

For maximum performance, streaming data directly from the agents is the best way to ensure timely delivery of agent responses. This message stream contains a series of individually packaged messages with varying purposes. Some messages are meant to be consumed only briefly, some are meant to be permanent and others are strictly for the agent's consumption on follow up prompts and not meant to be shown to users.

Your chat client will need to take this into consideration.

# APIs and Chat Client Flow

Your chat client will need to use a combination of NerdGraph queries and mutations along with the nrai-streaming endpoint.

#### Contexts

The first step in starting a conversation is to define the context in which it will take place.

```
JavaScript
mutation {
     collaborationCreateContext(
           account: 2345,
           contextMetadata: { "key": "value", "free": "form },
           id: "some-uuid"
     ) {
           id, creatorId, latestThreadId, latestThreadCommentId, latestThreadCommentCreatorId
     }
}
```

```
JavaScript
{
       actor: {
              collaboration: {
                     contextById(id: "<your context id>") {
                            id accountId contextMetadata
                     }
              }
       }
}
```
Note that you define your own id for a context. Also note that contextMetadata is freeform - add key value pairs or even complex data structures to define the context.

```
JavaScript
mutation {
      collaborationCreateThread(
             contextId: "some-uuid",
             contextMetadata: { "can": "be", "different": "than", "context": "value" },
             visibility: "PRIVATE"
      ) {
             id, commentCount, subscribers, status
      }
}
```
You can fetch your context with the following query:

#### Threads

```
JavaScript
{
       actor {
              collaboration {
                     threadsByContextId(
                            contextId: "<your context id>"
                            first: 10
                            last: 0
                            nextCursor: "<if paginating, use value from previous query>",
                            prevCursor: "<if paginating, use value from previous query>",
                            visibility: "PRIVATE"
                     ) {
                            entities {
                                   id commentCount
                            }
                            hasNextPage
                            hasPrevPage
                            nextCursor
                            prevCursor
                            totalCount
                     }
              }
       }
}
```
Now that we have a context, we can begin creating threads that belong to it:

You can also fetch all threads for a given context like this:

As you can see, this returns a paginated list of threads. The value of each member of 'entities' will be a thread and all thread attributes are accessible.

#### Comments

Use the following query to fetch comments from a given thread id:

JavaScript

```
JavaScript
{
  actor {
    collaboration {
      commentsByThreadId(
        first: 10
        last: 0
        nextCursor: "<use if paginating>"
        prevCursor: "<use if paginating>"
        threadId: "<your thread id>"
      ) {
        hasNextPage
        hasPrevPage
        nextCursor
        prevCursor
        totalCount
        entities {
          body
          id
          mentions {
            mentionableItemId
            type
            id
          }
        }
      }
    }
  }
}
```

```
JavaScript
{
  actor {
    collaboration {
      grokMessagesByIds(ids: ["id1","id2","id3"]) {
        card
        content
        role
        creatorId
        id
        createdAt
      }
    }
  }
}
```
Responses from the Ask NRAI agent will have mentions of 'grok messages'. Note in the query above, we gather the mentionableItemId for each comment's mentioned items. Once we have that, we can query the actual grok messages.

# Grok Messages

To query grok messages by ids gathered from the comment query, do the following:

#### Stream

Now that we have an established context and a thread belonging to it, we have a proper "place" where a conversation can take place. To interact with the Ask NRAI agent, we simply need to perform a request like the following:

POST <https://nrai-streaming.staging-service.newrelic.com/nrai-streaming>

```
X-Api-Key: "<your new relic api key>"
```
{

```
assistant: "grok",
body: "What is apdex?",
contextMetadata: { key: "value" },
threadId: "<id from thread you just created>"
```
}

### Direct calling Tools

There may be a time where you want to directly call a tool. In the coming days, a nerdgraph API for fetching the list of tools will be available. This document will be updated when that happens. The following example shows how to use a direct tool in your request to the streaming endpoint:

JavaScript

```
{
      "assistant":"grok",
      "assistantConfig":{
             "name":"direct_tool_config",
             "value":"{\"tool_name\":\"summarize_stack_trace\",\"tool_input\":{}}"
      },
      "body":"Summarize the root cause and potential solutions for this stack trace. Provide specific next steps for resolving the
issue, including links to relevant documentation or resources where applicable.<br>",
      "contextMetadata":{
             "id":"a9272546-7aa3-8d18-e4dc-d1b0e684d9c7",
             "entityGuid":"MTA2NzEyODl8QVBNfEFQUExJQ0FUSU9OfDE4MDcxMTc0",
             "metaData":{
                    "errorDetails":{
                           "language":"ruby",
                           "stackTrace":"/data/app/engines/core_api/lib/core_api/v2/resources/hawthorne/location_failure_conditions.rb:72
                           :in `block (4 levels) in
                           <class:LocationFailureConditions>'\n/usr/local/rbenv/versions/3.2.1/lib/ruby/gems/3.2.0/gems/grape-0.9.0/lib/g
                           rape/endpoint.rb:45:....<truncated>",
                           "message":"undefined method `collect' for nil:NilClass",
                           "class":"NoMethodError"
                    },
                    "capabilityId":"ERRORS_INBOX"
             },
             "accountId":1234,
             "capabilityId":"ERRORS_INBOX",
             "displayName":"NoMethodError",
             "nerdletId":"errors-inbox.error-group-details",
             "platformContextId":"c7863c96-e438-cdc2-befe-27a36202df62"
      },
      "threadId":"cb316805-aeb3-414e-9895-a732591f82fe"}
```

```
JavaScript
  {
    "type": "comment",
    "comment": {
      "id": "24699c96-e63e-474b-93aa-632b70eba30c",
      "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
      "contextId": "a9272546-7aa3-8d18-e4dc-d1b0e684d9c7",
      "organizationId": "404742df-e710-4f68-9edb-d22829da2e67",
      "accountId": "1",
      "creatorId": "**********",
      "createdAt": "*************",
      "modifiedAt": "*************",
      "body": "how do I pass my api to a new relic api request?<br>",
      "deactivated": false,
      "replyTo": null,
      "externalCreatorId": null,
      "externalApplicationType": null,
      "timePickerFrom": null,
      "timePickerTo": null,
      "edited": false,
      "referenceUrl":
"https://staging-one.newrelic.com/admin-portal/api-keys/home?account=********&state=72eaeae2-8bf8-adf3-ef67-6cddf992fac7",
      "capabilityId": null,
      "displayName": null,
      "attributes": "{\"transactionId\":\"3f37022d-392d-4e44-8ead-18135b84f4f1\"}"
    }
```
# Stream Response

The response you receive will look something like the following (apologies, it is very long):

{

```
},
  "type": "intermediate",
  "message": {
    "role": "user",
    "content": "how do I pass my api to a new relic api request?<br>",
    "context": {
      "id": "a9272546-7aa3-8d18-e4dc-d1b0e684d9c7",
      "nerdletId": "api-keys.home",
      "platformContextId": "269e9169-c98f-713f-b0b0-750c19a919d8",
      "accountId": 1
    },
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
```

```
"replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "intermediate",
  "message": {
    "role": "assistant",
    "content": "",
    "context": {},
    "additional_kwargs": {
      "function_call": {
        "name": "search_the_new_relic_documentation",
        "arguments": "{\"user_query\":\"pass API key to New Relic API request\"}",
        "type": "function"
      }
    },
    "card": null,
    "name": null,
    "visible_to_user": false,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": "intermediate_step"
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "intermediate",
  "message": {
    "role": "intermediate_step",
    "content": "I'll search our docs for: 'pass API key to New Relic API request'",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": "search_the_new_relic_documentation",
    "visible_to_user": true,
    "visible_to_assistant": false,
    "application_args": null,
    "message_type": "intermediate_step"
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "To pass",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 0
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " an API key to a",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 1
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " New Relic API request",
    "context": {},
    "additional_kwargs": null,
```

```
"card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 2
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": ", follow these steps:\n\n",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 3
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "1. **Generate an",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 4
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " API Key:**\n ",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 5
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
```

```
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " - Go to the [",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 6
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
```

```
"message": {
    "role": "assistant",
    "content": "API keys page](https",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 7
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "://one.newrelic",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 8
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": ".com/api-keys)",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 9
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " in your New Relic",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 10
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " account.\n - Click",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 11
  },
```

```
"threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " **Create a key**",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 12
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " and select the appropriate key",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 13
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " type (e.g.,",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 14
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " ingest-license key).\n\n2",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 15
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": ". **Use the API",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
```

```
"visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 16
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " Explorer:**\n -",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 17
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " Access the [API Explorer",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 18
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "](https://rpm.new",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 19
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "relic.com/api/ex",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 20
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "plore) to test and",
```

```
"context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 21
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " send requests.\n -",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 22
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " If signed in, select",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 23
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " your API key from the",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 24
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " top of the UI,",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 25
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
```

```
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " which will automatically populate in",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 26
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " the **Request** and",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 27
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " **Parameters** sections.\n",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 28
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " - If not signed",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 29
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " in, manually paste your",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
```

```
"sequence_id": 30
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " API key into the **",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 31
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "Parameters** section.\n\n3",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 32
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": ". **API Key in",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 33
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " Requests:**\n -",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 34
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " Include the API key in",
    "context": {},
    "additional_kwargs": null,
    "card": null,
```

```
"name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 35
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " the header of your API",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 36
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " requests. For example:\n",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 37
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " ```http\n ",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 38
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " GET /v2/app",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 39
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
```

```
"role": "assistant",
    "content": "lications.json\n X",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 40
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "-Api-Key: YOUR",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 41
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "_API_KEY\n ``",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 42
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "`\n\nFor more details,",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 43
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " refer to the [New",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 44
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
```

```
"replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": " Relic API Explorer documentation",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 45
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "](https://docs.new",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 46
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "relic.com/docs/apis",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 47
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "/rest-api-v2/api",
    "context": {},
    "additional_kwargs": null,
```
"card": null,

```
"name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": null,
    "sequence_id": 48
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
},
{
  "type": "stream-chunk",
  "message": {
    "role": "assistant",
    "content": "-explorer-v2/use",
    "context": {},
    "additional_kwargs": null,
    "card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
```

```
"application_args": null,
     "message_type": null,
     "sequence_id": 49
   },
   "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
   "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
 },
 {
   "type": "stream-chunk",
   "message": {
     "role": "assistant",
     "content": "-api-explorer).",
     "context": {},
     "additional_kwargs": null,
     "card": null,
     "name": null,
     "visible_to_user": true,
     "visible_to_assistant": true,
     "application_args": null,
     "message_type": null,
     "sequence_id": 50
   },
   "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
   "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
 },
 {
   "type": "intermediate",
   "message": {
     "role": "function",
     "content": "To pass an API key to a New Relic API request, follow these steps:\n\n1. **Generate an API Key:**\n - Go to the [API
keys page](https://one.newrelic.com/api-keys) in your New Relic account.\n - Click **Create a key** and select the appropriate key type
(e.g., ingest-license key).\n\n2. **Use the API Explorer:**\n - Access the [API Explorer](https://rpm.newrelic.com/api/explore) to test
and send requests.\n - If signed in, select your API key from the top of the UI, which will automatically populate in the **Request**
and **Parameters** sections.\n - If not signed in, manually paste your API key into the **Parameters** section.\n\n3. **API Key in
Requests:**\n - Include the API key in the header of your API requests. For example:\n ```http\n GET /v2/applications.json\n
X-Api-Key: YOUR_API_KEY\n ```\n\nFor more details, refer to the [New Relic API Explorer
documentation](https://docs.newrelic.com/docs/apis/rest-api-v2/api-explorer-v2/use-api-explorer).",
     "context": {},
     "additional_kwargs": {},
     "card": null,
     "name": "search_the_new_relic_documentation",
     "visible_to_user": false,
     "visible_to_assistant": true,
     "application_args": null,
     "message_type": "result"
   },
   "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
   "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
 },
 {
   "type": "final-message",
   "message": {
     "role": "assistant",
     "content": "To pass an API key to a New Relic API request, follow these steps:\n\n1. **Generate an API Key:**\n - Go to the [API
keys page](https://one.newrelic.com/api-keys) in your New Relic account.\n - Click **Create a key** and select the appropriate key type
(e.g., ingest-license key).\n\n2. **Use the API Explorer:**\n - Access the [API Explorer](https://rpm.newrelic.com/api/explore) to test
and send requests.\n - If signed in, select your API key from the top of the UI, which will automatically populate in the **Request**
and **Parameters** sections.\n - If not signed in, manually paste your API key into the **Parameters** section.\n\n3. **API Key in
Requests:**\n - Include the API key in the header of your API requests. For example:\n ```http\n GET /v2/applications.json\n
X-Api-Key: YOUR_API_KEY\n ```\n\nFor more details, refer to the [New Relic API Explorer
documentation](https://docs.newrelic.com/docs/apis/rest-api-v2/api-explorer-v2/use-api-explorer).",
     "context": {},
```

```
"additional_kwargs": null,
```

```
"card": null,
    "name": null,
    "visible_to_user": true,
    "visible_to_assistant": true,
    "application_args": null,
    "message_type": "result"
  },
  "threadId": "243e455d-b00e-4e2c-89f5-2affef2ccf9e",
  "replyTo": "24699c96-e63e-474b-93aa-632b70eba30c"
}
```
Note that each message will be streamed to you as it is generated. Also IMPORTANT to note that each message is not comma separate as depicted above, this was simply for visual assistance. As you can see, there are a a few different types of payloads:

- 1. Type: "comment": the value of "comment" will be the full collaboration comment of the user's prompt
- 2. Type: "intermediate": The value of "message" will be a "grok message". Each grok message will have a specific "message\_type" except where "role": "user".
	- a. Message\_type: "intermediate\_step": Some of these are meant to be consumed by the LLM, others are meant to notify the user of progress on their request
	- b. Message\_type: "result": These "result" message types will generate a comment and are meant to be a permanent part of what the user sees as conversation history
- 3. Type: "stream-chunk": The value of the message here will contain a small part of a result. They can be concatenated together to form a message in real time for the user's consumption
- 4. Type: "final-message": Contains a grok message and indicates that there are no further messages expected in a response to this prompt.