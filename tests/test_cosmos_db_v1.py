from azure.cosmos import CosmosClient
from azure.cosmos.exceptions import CosmosHttpResponseError
import pandas as pd
from rich.console import Console
from rich.table import Table
import csv
import os

# Initialize CosmosDB client
timestamp_value = 1725052800  # Replace with the _ts value you want to filter by
work_item_id = "1447773"

# Connect to CosmosDB
client = CosmosClient(os.getenv("COSMOS_ENDPOINT"), os.getenv("COSMOS_KEY"))
database_name = "alert"
container_name = "alert"
database = client.get_database_client(database_name)
container = database.get_container_client(container_name)

# Query to get documents after a specific timestamp
ts_query = f"SELECT * FROM c WHERE c._ts > {timestamp_value}"
work_item_query = f"SELECT * FROM c WHERE c.work_item_id > {work_item_id}"

# Execute the query
documents = list(container.query_items(query=work_item_query, enable_cross_partition_query=True))

# Prepare data for displaying with rich and saving to CSV
data = []
for doc in documents:
    data.append(doc)

# Create a table with rich to display
console = Console()
table = Table(title="CosmosDB Documents After _ts")

# Add columns (adjust based on your document structure)

# Example
# {'id': '3562acf3-ba34-46dd-a08c-f65b49099e80', 'work_item_id': 1447894, 'nodes': [], 'links': [], 'rca': '', 'issue_info': {'issueId': '3562acf3-ba34-46dd-a08c-f65b49099e80', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/3562acf3-ba34-46dd-a08c-f65b49099e80?notifier=WEBHOOK', 'title': "infrastructure-notification query result is > 10.0 on 'Pod with CrashLoopBackOff'", 'priority': 'CRITICAL', 'EntityId': [...], 'impactedEntities': [...], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': [...], 'alertPolicyNames': [...], 'alertConditionNames': [...], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/9a8539ca-2f5d-43be-8ce5-f1dc38496957?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}, 'autogen_message': 'Process following Issue Detail {\'issueId\': \'3562acf3-ba34-46dd-a08c-f65b49099e80\', \'issueUrl\': \'https://radar-api.service.newrelic.com/accounts/1093620/issues/3562acf3-ba34-46dd-a08c-f65b49099e80?notifier=WEBHOOK\', \'title\': "infrastructure-notification query result is > 10.0 on \'Pod with CrashLoopBackOff\'", \'priority\': \'CRITICAL\', \'EntityId\': [\'MTA5MzYyMHxJTkZSQXxOQXwtMzA2Mzk4Nzk5MjU1MjA5MDYwMA\'], \'impactedEntities\': [\'infrastructure-notification\'], \'totalIncidents\': \'1\', \'state\': \'ACTIVATED\', \'trigger\': \'STATE_CHANGE\', \'isCorrelated\': \'false\', \'createdAt\': *************, \'updatedAt\': *************, \'sources\': [\'newrelic\'], \'alertPolicyNames\': [\'Neurons k8s Infra - Critical\'], \'alertConditionNames\': [\'Pod with CrashLoopBackOff\'], \'workflowName\': \'obv-ai-processing-neurons\', \'chartLink\': \'https://gorgon.nr-assets.net/image/9a8539ca-2f5d-43be-8ce5-f1dc38496957?config.legend.enabled=false&width=400&height=210\', \'product\': \'neurons\', \'nr_region\': \'us\'} from NewRelic. Perform following in order\n            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.\n            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.\n            3) Root Cause Analysis should be in following json format:\n            {"customer_affected": "<customer_affected>",\n                "date_incident_began": "<date_incident_began>",\n                "date_incident_resolved": "<date_incident_resolved>",\n                "teams_involved": "<teams_involved>",\n                "customer_impact": "<customer_impact> (e.g., downtime, performance degradation, etc.)",\n                "root_cause": "<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)",\n                "remedy": "<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)",\n                "preventive_action": "<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)"\n            } AND also issue detail in following json format:\n            issue_details = {\n                "issue_id": "<issue_id>",\n                "issue_url": "<issue_url>",\n                "title": "<title>",\n                "priority": "<priority>",\n                "impacted_entities": "<impacted_entities>",\n                "total_incidents": "<total_incidents>",\n                "state": "<state>",\n                "trigger": "<trigger>",\n                "is_correlated": "<is_correlated>",\n                "created_at": "<created_at>",\n                "updated_at": "<updated_at>",\n                "sources": "<sources>",\n                "alert_policy_names": "<alert_policy_names>",\n                "alert_condition_names": "<alert_condition_names>",\n                "workflow_name": "<workflow_name>",\n                "chart_url": "<chart_url>"\n            }.\n            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1447894 using "generate_topology_report" function with help of functioncallingagent.\n            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.\n            IMPORTANT: UserProxy can\'t help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.\n            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.\n            ', 'status': 'completed', '_rid': '1MBWANbAdvT4AAAAAAAAAA==', '_self': 'dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvT4AAAAAAAAAA==/', '_etag': '"0e0286ce-0000-4d00-0000-6708f0180000"', '_attachments': 'attachments/', '_ts': 1728639000}
table.add_column("ID", justify="center")
table.add_column("Work Item ID", justify="center")
table.add_column("Timestamp", justify="center")
table.add_column("Status", justify="center")
table.add_column("Product", justify="center")
table.add_column("Region", justify="center")

# Add rows
for doc in documents:
    table.add_row(
        doc.get('id', 'N/A'),
        str(doc.get('work_item_id', 'N/A')),
        str(doc.get('_ts', 'N/A')),
        doc.get('status', 'N/A'),
        doc.get('product', 'N/A'),
        doc.get('region', 'N/A')
    )

# Print the table
console.print(table)

# Save data to a CSV file using pandas
df = pd.DataFrame(documents)  # Adjust if needed, based on document structure
df.to_csv('cosmosdb_documents_temp.csv', index=False)

print("Data saved to cosmosdb_documents_temp.csv")
