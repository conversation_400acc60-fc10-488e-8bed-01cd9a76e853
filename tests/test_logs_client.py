"""
Test script for testing the logs client retry functionality
"""
import sys
import os
import argparse
from datetime import datetime, timedelta
import dotenv

dotenv.load_dotenv()

from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.logs import NewRelicLogsClient
from lib.new_relic.base import UTC
from lib.new_relic.base import NewRelicGraphQLError

def setup_argument_parser():
    parser = argparse.ArgumentParser(description="Test New Relic Logs Client")
    
    # Authentication and account parameters
    parser.add_argument('-k', '--api-key', default=os.environ.get('NEWRELIC_API_KEY'), 
                        help='New Relic API key (defaults to NEWRELIC_API_KEY env var)')
    parser.add_argument('-a', '--account-id', default=os.environ.get('NEWRELIC_ACCOUNT_ID'),
                        help='New Relic account ID (defaults to NEWRELIC_ACCOUNT_ID env var)')
    
    # Entity parameters
    parser.add_argument('-e', '--entity-guid', required=True,
                        help='GUID of the entity to analyze')
    
    # Time parameters
    parser.add_argument('-w', '--window', type=int, default=15,
                        help='Window in minutes to search before and after the current time (default: 15)')
    
    # Debug options
    parser.add_argument('--debug', action='store_true', 
                        help='Enable debug mode for all components')
    
    return parser

def main():
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Validate required parameters
    if not args.api_key:
        print("ERROR: New Relic API key not provided. Use --api-key or set NEWRELIC_API_KEY env var.")
        return 1
    
    if not args.account_id:
        print("ERROR: New Relic account ID not provided. Use --account-id or set NEWRELIC_ACCOUNT_ID env var.")
        return 1
    
    # Create time window
    now = datetime.now(UTC)
    since_time = now - timedelta(minutes=args.window)
    until_time = now + timedelta(minutes=args.window)
    
    print(f"Testing logs client with entity: {args.entity_guid}")
    print(f"Time window: {since_time.isoformat()} to {until_time.isoformat()}")
    print(f"Debug mode: {'Enabled' if args.debug else 'Disabled'}")
    
    # Initialize clients
    graphql_client = NewRelicGraphQLClient(
        api_key=args.api_key,
        account_id=args.account_id,
        debug=args.debug,
    )
    
    logs_client = NewRelicLogsClient(graphql_client, debug=args.debug)
    
    # Test the get_entity_logs method with retry
    try:
        print("\nFetching logs for the entity...")
        logs = logs_client.get_entity_logs(
            entity_guid=args.entity_guid,
            partitions="all",
            since=since_time,
            until=until_time,
            limit=500,
            account_id=args.account_id
        )
        
        print(f"Successfully retrieved {len(logs)} log entries.")
        
        # Display a sample of logs
        if logs:
            print("\nSample log entries:")
            for i, log in enumerate(logs[:3]):
                print(f"\nLog {i+1}:")
                # Print a few key fields for each log
                for key in ['timestamp', 'level', 'message', 'entity.name']:
                    if key in log:
                        print(f"  {key}: {log[key]}")
        
    except NewRelicGraphQLError as e:
        print(f"\nERROR: Failed to retrieve logs: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 