"""
New Relic entity analyzer for comprehensive information gathering about affected entities.
"""

import os
import re
import yaml
from typing import Any, Dict, List, Optional, Union, Set, Tuple, Pattern
from datetime import datetime, timedelta
import logging

from .base import UTC, Alert, NewRelicGraphQLError
from .client import NewRelicGraphQLClient
from .query import NewRelicQueryClient
from .metrics import NewRelicMetricsClient
from .logs import NewRelicLogsClient

logger = logging.getLogger(__name__)


class EntityDetails:
    """Container for entity details and associated data"""

    def __init__(
        self,
        entity_guid: str,
        entity_name: str,
        entity_type: str,
        cluster_id: Optional[str] = None,
        product: Optional[str] = None,
        region: Optional[str] = None
    ):
        self.entity_guid = entity_guid
        self.entity_name = entity_name
        self.entity_type = entity_type
        self.cluster_id = cluster_id
        self.product = product
        self.region = region
        self.metrics = {}
        self.logs = []
        self.events = []
        self.related_entities = []
        self.metadata = {}
        self.relationships = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert the entity details to a dictionary"""
        return {
            "entity_guid": self.entity_guid,
            "entity_name": self.entity_name,
            "entity_type": self.entity_type,
            "cluster_id": self.cluster_id,
            "product": self.product,
            "region": self.region,
            "metrics": self.metrics,
            "logs": self.logs,
            "events": self.events,
            "related_entities": self.related_entities,
            "metadata": self.metadata,
            "relationships": self.relationships
        }


class EntityRelationshipMapper:
    """
    Maps relationships between different New Relic entities.
    Uses a configuration file to define relationship patterns.
    """

    # Common entity types and their corresponding New Relic entity type values
    ENTITY_TYPES = {
        "pod": ["KUBERNETES_POD", "K8S_POD"],
        "node": ["KUBERNETES_NODE", "K8S_NODE", "HOST"],
        "cronjob": ["KUBERNETES_CRONJOB", "K8S_CRONJOB"],
        "deployment": ["KUBERNETES_DEPLOYMENT", "K8S_DEPLOYMENT"],
        "service": ["KUBERNETES_SERVICE", "K8S_SERVICE"],
        "container": ["CONTAINER"],
        "application": ["APPLICATION"]
    }

    # Common cluster prefixes for different products
    CLUSTER_PATTERNS = {
        "neurons": {
            "nvu": ["aks-edge-rg-nvu-prd-neurons", "aks-rg-nvu-prd-neurons"],
            "uku": ["aks-rg-uku-prd-neurons", "aks-edge-rg-uku-prd-neurons"],
            "mlu": ["aks-edge-rg-mlu-prd-neurons", "aks-rg-mlu-prd-neurons"],
            "ttu": ["aks-edge-rg-ttu-prd-neurons", "aks-rg-ttu-prd-neurons"],
            "tku": ["aks-edge-rg-tku-prd-neurons", "aks-rg-tku-prd-neurons"]
        },
        "mdm": {
            "na1": ["na1", "primary-na1", "na1-eks", "north-america-1"],
            "na2": ["na2", "primary-na2", "na2-eks", "north-america-2"],
            "ap1": ["ap1", "primary-ap1", "ap1-eks", "asia-pacific-1"],
            "ap2": ["ap2", "primary-ap2", "ap2-eks", "asia-pacific-2"]
        }
    }

    def __init__(
        self,
        query_client: NewRelicQueryClient,
        config_path: Optional[str] = None,
        debug: bool = False
    ):
        """
        Initialize the entity relationship mapper.

        Args:
            query_client: An initialized NewRelicQueryClient
            config_path: Path to the entity relationships config file
            debug: Enable debug mode for verbose output
        """
        self.query = query_client
        self.debug = debug

        # Load relationship configuration
        if config_path is None:
            module_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(module_dir, '..', '..', 'config', 'entity_relationships.yaml')

        self.relationships_config = self._load_relationships_config(config_path)

    def _load_relationships_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load entity relationships configuration from YAML file.

        Args:
            config_path: Path to the configuration file

        Returns:
            Parsed configuration dictionary
        """
        if not os.path.exists(config_path):
            logger.warning(f"Entity relationships config file not found at {config_path}")
            return {"relationships": []}

        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"Error loading entity relationships config: {str(e)}")
            return {"relationships": []}

    def is_entity_type(self, entity_type: str, generic_type: str) -> bool:
        """
        Check if an entity is of a specific generic type.

        Args:
            entity_type: Entity type string from New Relic
            generic_type: Generic type to check ("pod", "node", etc.)

        Returns:
            True if the entity matches the generic type, False otherwise
        """
        generic_type = generic_type.lower()
        if generic_type in self.ENTITY_TYPES:
            return entity_type in self.ENTITY_TYPES[generic_type]
        return False

    def map_entity_relationships(
        self,
        entity_details: Dict[str, Any],
        query_client: NewRelicQueryClient,
        since_time: datetime,
        until_time: datetime
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Map relationships for a specific entity.

        Args:
            entity_details: Entity details dictionary
            query_client: NewRelicQueryClient for querying related entities
            since_time: Start time for relationship mapping
            until_time: End time for relationship mapping

        Returns:
            Dictionary of relationships by type
        """
        entity_type = entity_details.get("type", "")
        entity_name = entity_details.get("name", "")
        relationships = {}

        # Handle Kubernetes pod -> node relationship
        if self.is_entity_type(entity_type, "pod"):
            # Find the node this pod is running on
            node = self._find_pod_node_relationship(
                entity_name,
                entity_details.get("cluster_id", "unknown"),
                query_client,
                since_time,
                until_time
            )

            if node:
                relationships["node"] = [node]

                # Find the cluster this node belongs to
                cluster = self._find_node_cluster_relationship(
                    node["name"],
                    query_client,
                    since_time,
                    until_time
                )

                if cluster:
                    relationships["cluster"] = [cluster]

        # Handle Kubernetes node -> cluster relationship
        elif self.is_entity_type(entity_type, "node"):
            # Find the cluster this node belongs to
            cluster = self._find_node_cluster_relationship(
                entity_name,
                query_client,
                since_time,
                until_time
            )

            if cluster:
                relationships["cluster"] = [cluster]

            # Find pods running on this node
            pods = self._find_node_pods_relationship(
                entity_name,
                query_client,
                since_time,
                until_time
            )

            if pods:
                relationships["pods"] = pods

        return relationships

    def _find_pod_node_relationship(
        self,
        pod_name: str,
        cluster_id: str,
        query_client: NewRelicQueryClient,
        since_time: datetime,
        until_time: datetime
    ) -> Optional[Dict[str, Any]]:
        """
        Find the node relationship for a pod.

        Args:
            pod_name: Name of the pod
            cluster_id: Cluster ID
            query_client: Query client for executing NRQL
            since_time: Start time for the query
            until_time: End time for the query

        Returns:
            Node relationship details or None if not found
        """
        # Query New Relic for the node name using the query client
        query = f"""
        SELECT nodeName, entityGuid as nodeEntityGuid
        FROM K8sPodSample
        WHERE podName = '{pod_name}'
        {"AND clusterName LIKE '%" + cluster_id + "%'" if cluster_id and cluster_id != "unknown" else ""}
        SINCE '{since_time.isoformat()}'
        UNTIL '{until_time.isoformat()}'
        LIMIT 1
        """

        try:
            # Use query_client to execute the query instead of direct execution
            results = query_client.execute_nrql(query)

            if results and "nodeName" in results[0]:
                return {
                    "type": "node",
                    "name": results[0]["nodeName"],
                    "entity_guid": results[0].get("nodeEntityGuid"),
                    "relationship": "runs_on"
                }
        except Exception as e:
            if self.debug:
                logger.error(f"Error finding pod node relationship: {str(e)}")

        return None

    def _find_node_cluster_relationship(
        self,
        node_name: str,
        query_client: NewRelicQueryClient,
        since_time: datetime,
        until_time: datetime
    ) -> Optional[Dict[str, Any]]:
        """
        Find the cluster relationship for a node.

        Args:
            node_name: Name of the node
            query_client: Query client for executing NRQL
            since_time: Start time for the query
            until_time: End time for the query

        Returns:
            Cluster relationship details or None if not found
        """
        query = f"""
        SELECT clusterName
        FROM K8sNodeSample
        WHERE nodeName = '{node_name}'
        SINCE '{since_time.isoformat()}'
        UNTIL '{until_time.isoformat()}'
        LIMIT 1
        """

        try:
            # Use query_client to execute the query
            results = query_client.execute_nrql(query)

            if results and "clusterName" in results[0]:
                return {
                    "type": "cluster",
                    "name": results[0]["clusterName"],
                    "relationship": "belongs_to"
                }
        except Exception as e:
            if self.debug:
                logger.error(f"Error finding node cluster relationship: {str(e)}")

        return None

    def _find_node_pods_relationship(
        self,
        node_name: str,
        query_client: NewRelicQueryClient,
        since_time: datetime,
        until_time: datetime,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Find pods relationship for a node.

        Args:
            node_name: Name of the node
            query_client: Query client for executing NRQL
            since_time: Start time for the query
            until_time: End time for the query
            limit: Maximum number of pods to return

        Returns:
            List of pod relationship details
        """
        query = f"""
        SELECT podName, entityGuid
        FROM K8sPodSample
        WHERE nodeName = '{node_name}'
        SINCE '{since_time.isoformat()}'
        UNTIL '{until_time.isoformat()}'
        LIMIT {limit}
        """

        try:
            # Use query_client to execute the query
            results = query_client.execute_nrql(query)

            pods = []
            for pod in results:
                if "podName" in pod:
                    pods.append({
                        "type": "pod",
                        "name": pod["podName"],
                        "entity_guid": pod.get("entityGuid"),
                        "relationship": "runs_on"
                    })

            return pods
        except Exception as e:
            if self.debug:
                logger.error(f"Error finding node pods relationship: {str(e)}")

        return []

    def get_entity_relationships_for_alert(
        self,
        alert_condition: str,
        entity_name: Optional[str] = None,
        entity_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get potential related entities for an alert based on configuration.

        Args:
            alert_condition: Alert condition name or description
            entity_name: Optional entity name to filter relationships
            entity_type: Optional entity type to filter relationships

        Returns:
            List of potential related entities
        """
        potential_entities = []

        # Go through the relationships
"""
New Relic entity analyzer for comprehensive information gathering about affected entities.
"""

from typing import Any, Dict, List, Optional, Union, Set, Tuple
from datetime import datetime, timedelta
import json
import time
import random

from .base import UTC, Alert, NewRelicGraphQLError
from .client import NewRelicGraphQLClient
from .query import NewRelicQueryClient
from .logs import NewRelicLogsClient


class EntityDetails:
    """Container for entity details and associated data"""

    def __init__(
        self,
        entity_guid: str,
        entity_name: str,
        entity_type: str,
        cluster_id: Optional[str] = None,
        product: Optional[str] = None,
        region: Optional[str] = None
    ):
        self.entity_guid = entity_guid
        self.entity_name = entity_name
        self.entity_type = entity_type
        self.cluster_id = cluster_id
        self.product = product
        self.region = region
        self.metrics = {}
        self.logs = []
        self.events = []
        self.related_entities = []
        self.metadata = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert the entity details to a dictionary"""
        return {
            "entity_guid": self.entity_guid,
            "entity_name": self.entity_name,
            "entity_type": self.entity_type,
            "cluster_id": self.cluster_id,
            "product": self.product,
            "region": self.region,
            "metrics": self.metrics,
            "logs": self.logs,
            "events": self.events,
            "related_entities": self.related_entities,
            "metadata": self.metadata
        }


class EntityAnalyzer:
    """
    Performs comprehensive analysis of New Relic entities.
    Gets details, metrics, logs, events, and related entities.
    """

    # Common entity types and their corresponding New Relic entity type values
    ENTITY_TYPES = {
        "pod": ["KUBERNETES_POD", "K8S_POD"],
        "node": ["KUBERNETES_NODE", "K8S_NODE", "HOST"],
        "cronjob": ["KUBERNETES_CRONJOB", "K8S_CRONJOB"],
        "deployment": ["KUBERNETES_DEPLOYMENT", "K8S_DEPLOYMENT"],
        "service": ["KUBERNETES_SERVICE", "K8S_SERVICE"],
        "container": ["CONTAINER"]
    }

    # Common cluster prefixes for different products
    CLUSTER_PATTERNS = {
        "neurons": {
            "nvu": ["aks-edge-rg-nvu-prd-neurons", "aks-rg-nvu-prd-neurons"],
            "uku": ["aks-rg-uku-prd-neurons", "aks-edge-rg-uku-prd-neurons"],
            "mlu": ["aks-edge-rg-mlu-prd-neurons", "aks-rg-mlu-prd-neurons"],
            "ttu": ["aks-edge-rg-ttu-prd-neurons", "aks-rg-ttu-prd-neurons"],
            "tku": ["aks-edge-rg-tku-prd-neurons", "aks-rg-tku-prd-neurons"]
        },
        "mdm": {
            "na1": ["na1", "primary-na1", "na1-eks", "north-america-1"],
            "na2": ["na2", "primary-na2", "na2-eks", "north-america-2"],
            "ap1": ["ap1", "primary-ap1", "ap1-eks", "asia-pacific-1"],
            "ap2": ["ap2", "primary-ap2", "ap2-eks", "asia-pacific-2"]
        }
    }

    # Log partitions to use for different clusters
    CLUSTER_LOG_PARTITIONS = {
        "nvu": "neurons_nvu",
        "uku": "neurons_all",
        "mlu": "neurons_all",
        "ttu": "neurons_all",
        "tku": "neurons_all",
        "na1": "default",
        "na2": "default",
        "ap1": "default",
        "ap2": "default",
        "unknown": "all"
    }

    def __init__(
        self,
        client: NewRelicGraphQLClient,
        debug: bool = False
    ):
        """
        Initialize the entity analyzer.

        Args:
            client: An initialized NewRelicGraphQLClient
            debug: Enable debug mode for verbose output
        """
        self.client = client
        self.query = NewRelicQueryClient(client)
        self.logs = NewRelicLogsClient(client)
        self.debug = debug

    def analyze_entity(
        self,
        entity_guid: str,
        since_time: datetime,
        until_time: datetime,
        product: Optional[str] = None,
        region: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze an entity to get comprehensive information.

        Args:
            entity_guid: Entity GUID
            since_time: Start time for data collection
            until_time: End time for data collection
            product: Product (optional)
            region: Region (optional)

        Returns:
            Dictionary with entity details and associated data
        """
        # Get entity details
        entity_details = self._get_entity_details(entity_guid)

        if not entity_details:
            raise ValueError(f"Entity with GUID {entity_guid} not found")

        # Extract entity details
        entity_name = entity_details.get("name", "unknown")
        entity_type = entity_details.get("type", "unknown")

        # Extract cluster ID from tags if it's a Kubernetes entity
        cluster_id = "unknown"
        if self._is_pod_entity(entity_type) or self._is_node_entity(entity_type):
            for tag in entity_details.get("tags", []):
                if tag.get("key") == "clusterName":
                    cluster_id = tag.get("values", [])[0] if tag.get("values") else "unknown"
                    break

        # Create entity data object
        entity_data = EntityDetails(
            entity_guid=entity_guid,
            entity_name=entity_name,
            entity_type=entity_type,
            cluster_id=cluster_id,
            product=product,
            region=region
        )

        # Get metadata
        entity_data.metadata = self._get_entity_metadata(entity_details)

        # Get metrics
        entity_data.metrics = self._get_entity_metrics(
            entity_guid,
            entity_type,
            since_time,
            until_time
        )

        # Get logs
        try:
            entity_data.logs = self.logs.get_entity_logs(
                entity_guid=entity_guid,
                since=since_time,
                until=until_time,
                limit=100
            )
        except Exception as e:
            if self.debug:
                print(f"Error fetching logs for {entity_guid}: {str(e)}")

        # Get Kubernetes events if applicable
        if self._is_pod_entity(entity_type) or self._is_node_entity(entity_type):
            # Get events for the entity
            events = []

            entity_events = self._get_kubernetes_events(
                entity_name,
                entity_type,
                cluster_id,
                since_time,
                until_time
            )

            events.extend(entity_events)

            # For pods, also get events for the underlying node
            if self._is_pod_entity(entity_type):
                node_name = self._find_pod_node(entity_name, cluster_id, since_time, until_time)
                if node_name:
                    # Store node information in metadata
                    entity_data.metadata["node_name"] = node_name

                    # Get events for this node
                    node_events = self._get_kubernetes_events(
                        node_name,
                        "Node",  # Explicitly specify this is a Node
                        cluster_id,
                        since_time,
                        until_time
                    )

                    # Add a source field to distinguish node events
                    for event in node_events:
                        event["source"] = "node"

                    # Add a source field to distinguish pod events
                    for event in entity_events:
                        event["source"] = "pod"

                    events.extend(node_events)

            entity_data.events = events

        # For pod entities, collect container information
        if self._is_pod_entity(entity_type):
            containers = self._get_pod_containers(entity_name, cluster_id)
            entity_data.metadata["containers"] = containers

        return entity_data.to_dict()

    def _get_entity_details(self, entity_guid: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about an entity from New Relic.

        Args:
            entity_guid: New Relic entity GUID

        Returns:
            Entity details dictionary or None if not found
        """
        graphql_query = """
        query($entityGuid: EntityGuid!) {
          actor {
            entity(guid: $entityGuid) {
              domain
              entityType
              guid
              name
              type
              ... on InfrastructureHostEntity {
                name
                hostSummary {
                  cpuUtilizationPercent
                  diskUsedPercent
                  memoryUsedPercent
                }
              }
              ... on InfrastructureIntegrationEntity {
                integrationTypeCode
              }
              tags {
                key
                values
              }
              recentAlertViolations {
                alertSeverity
                label
                closedAt
                openedAt
              }
            }
          }
        }
        """

        variables = {
            "entityGuid": entity_guid
        }

        try:
            response = self.client.execute_query(graphql_query, variables)
            entity = response.data["actor"]["entity"]
            return entity
        except Exception as e:
            if self.debug:
                print(f"Error fetching entity details for {entity_guid}: {str(e)}")
            return None

    def _is_pod_entity(self, entity_type: str) -> bool:
        """
        Check if an entity is a Kubernetes pod.

        Args:
            entity_type: Entity type string

        Returns:
            True if the entity is a pod, False otherwise
        """
        return entity_type in ["KUBERNETES_POD", "K8S_POD"]

    def _is_node_entity(self, entity_type: str) -> bool:
        """
        Check if an entity is a Kubernetes node.

        Args:
            entity_type: Entity type string

        Returns:
            True if the entity is a node, False otherwise
        """
        return entity_type in ["KUBERNETES_NODE", "K8S_NODE"]

    def _get_entity_metadata(self, entity_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract metadata from entity details based on the entity type.

        Args:
            entity_details: Entity details dictionary

        Returns:
            Metadata dictionary
        """
        metadata = {}

        # Extract tags
        tags = {}
        for tag in entity_details.get("tags", []):
            tags[tag.get("key")] = tag.get("values")
        metadata["tags"] = tags

        # Extract type-specific metadata
        entity_type = entity_details.get("type")

        if self._is_pod_entity(entity_type):
            metadata.update({
                "pod_name": entity_details.get("podName"),
                "namespace": entity_details.get("namespaceName"),
                "cluster_name": entity_details.get("clusterName")
            })
        elif self._is_node_entity(entity_type):
            metadata.update({
                "node_name": entity_details.get("nodeName"),
                "cluster_name": entity_details.get("clusterName")
            })
        elif entity_type == "CONTAINER":
            metadata.update({
                "container_id": entity_details.get("containerId"),
                "image_name": entity_details.get("imageName")
            })

        return metadata

    def _get_entity_metrics(
        self,
        entity_guid: str,
        entity_type: str,
        since_time: datetime,
        until_time: datetime
    ) -> Dict[str, Any]:
        """
        Get metrics for an entity based on its type.

        Args:
            entity_guid: New Relic entity GUID
            entity_type: Entity type
            since_time: Start time for metrics
            until_time: End time for metrics

        Returns:
            Dictionary of metrics data
        """
        try:
            # Use the new configurable metrics client method
            return self.query.get_entity_metrics_by_type(
                entity_guid=entity_guid,
                entity_type=entity_type,
                since=since_time,
                until=until_time
            )
        except Exception as e:
            if self.debug:
                print(f"Error fetching metrics for {entity_guid}: {str(e)}")
            return {}

    def _get_kubernetes_events(
        self,
        entity_name: str,
        entity_type: str,
        cluster_id: str,
        since_time: datetime,
        until_time: datetime
    ) -> List[Dict[str, Any]]:
        """
        Get Kubernetes events for the entity.

        Args:
            entity_name: Entity name
            entity_type: Entity type
            cluster_id: Cluster ID
            since_time: Start time for events
            until_time: End time for events

        Returns:
            List of Kubernetes events
        """
        object_kind = "Pod" if self._is_pod_entity(entity_type) else "Node"

        try:
            return self.query.get_kubernetes_events(
                object_name=entity_name,
                object_kind=object_kind,
                cluster_name=cluster_id,
                since=since_time,
                until=until_time
            )
        except Exception as e:
            if self.debug:
                print(f"Error fetching Kubernetes events: {str(e)}")
            return []

    def _find_pod_node(
        self,
        pod_name: str,
        cluster_id: str,
        since_time: datetime,
        until_time: datetime
    ) -> Optional[str]:
        """
        Find the node that a pod is running on.

        Args:
            pod_name: Name of the pod
            cluster_id: Cluster ID
            since_time: Start time for the query
            until_time: End time for the query

        Returns:
            Name of the node if found, None otherwise
        """
        # Query New Relic for the node name
        query = f"""
        SELECT nodeName
        FROM K8sPodSample
        WHERE podName = '{pod_name}'
        {"AND clusterName LIKE '%" + cluster_id + "%'" if cluster_id and cluster_id != "unknown" else ""}
        SINCE '{since_time.isoformat()}'
        UNTIL '{until_time.isoformat()}'
        LIMIT 1
        """

        try:
            results = self._query_metric(query, debug=self.debug)
            if results and "nodeName" in results[0]:
                return results[0]["nodeName"]
        except Exception as e:
            if self.debug:
                print(f"Error finding pod node: {str(e)}")

        return None

    def _get_pod_containers(
        self,
        pod_name: str,
        cluster_id: str
    ) -> List[Dict[str, Any]]:
        """
        Get container information for a pod.

        Args:
            pod_name: Pod name
            cluster_id: Cluster ID

        Returns:
            List of container details
        """
        # This would require additional queries to get container details
        # For now, return empty list
        return []

    def _query_metric(
        self,
        query: str,
        account_id: Optional[str] = None,
        debug: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Execute a metric query and return the results.

        Args:
            query: NRQL query string
            account_id: Optional account ID override
            debug: Enable debug output for this query

        Returns:
            List of query result rows

        Raises:
            Exception: If the query fails
        """
        account_id = account_id or self.client._verify_account_id()

        graphql_query = """
        query($accountId: Int!, $nrql: Nrql!) {
          actor {
            account(id: $accountId) {
              nrql(query: $nrql) {
                results
                metadata {
                  facets
                }
              }
            }
          }
        }
        """

        variables = {
            "accountId": int(account_id),
            "nrql": query
        }

        if debug:
            print(f"Executing query: {query}")

        try:
            response = self.client.execute_query(graphql_query, variables)
            return response.data["actor"]["account"]["nrql"]["results"]
        except Exception as e:
            if debug:
                print(f"Query failed: {str(e)}")
            raise
