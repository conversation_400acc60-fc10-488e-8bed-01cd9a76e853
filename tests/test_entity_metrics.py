#!/usr/bin/env python
"""
Test script for demonstrating the configurable entity metrics functionality.
"""

import os
import sys
import json
import argparse
from datetime import datetime, timedelta
import dotenv

dotenv.load_dotenv()

# Add the parent directory to the path so we can import the library
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.new_relic.base import UTC
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.config import MetricsConfigManager


def setup_argument_parser():
    """Set up command line argument parser"""
    parser = argparse.ArgumentParser(description='Test the New Relic Entity Metrics with configuration')
    
    parser.add_argument('--entity-guid', '-e',
                        help='The New Relic entity GUID to analyze')
    
    parser.add_argument('--entity-type', '-t', required=True,
                        help='The entity type (e.g., KUBERNETES_POD, KUBERNETES_NODE)')
    
    parser.add_argument('--api-key', '-k',
                        default=os.environ.get('NEWRELIC_API_KEY'),
                        help='New Relic API key (defaults to NEWRELIC_API_KEY env var)')
    
    parser.add_argument('--account-id', '-a',
                        default=os.environ.get('NEWRELIC_ACCOUNT_ID'),
                        help='New Relic account ID (defaults to NEWRELIC_ACCOUNT_ID env var)')
    
    # Time window options
    parser.add_argument('--window', '-w', type=int, default=30,
                        help='Time window in minutes to look back (default: 30)')
    
    parser.add_argument('--metrics', '-m', nargs='+',
                        help='Specific metrics to collect (default: all default metrics)')
    
    parser.add_argument('--list-metrics', '-l', action='store_true',
                        help='List available metrics for the entity type and exit')
    
    parser.add_argument('--output', '-o',
                        help='Output file to write results to (JSON format)')
    
    # Debug options
    parser.add_argument('--debug', '-d', action='store_true',
                        help='Enable debug output')
    
    return parser


def validate_args(args):
    """Validate command line arguments"""
    # Check if we're just listing metrics
    if args.list_metrics:
        # No need to validate API key or entity_guid when just listing metrics
        return
        
    if not args.entity_guid:
        print("ERROR: Entity GUID is required when not using --list-metrics. Provide it with --entity-guid/-e.")
        sys.exit(1)
        
    if not args.api_key:
        print("ERROR: New Relic API key is required. Provide it with --api-key or set NEWRELIC_API_KEY env var.")
        sys.exit(1)
    
    if not args.account_id:
        print("ERROR: New Relic account ID is required. Provide it with --account-id or set NEWRELIC_ACCOUNT_ID env var.")
        sys.exit(1)


def print_metrics_info(metrics_config, entity_type):
    """Print information about available metrics for an entity type"""
    print(f"\nAvailable metrics for entity type '{entity_type}':")
    metrics = metrics_config.get_entity_metrics(entity_type)
    
    if not metrics:
        print(f"  No metrics defined for entity type '{entity_type}'")
        resolved_type = metrics_config._resolve_entity_type(entity_type)
        if resolved_type != entity_type:
            print(f"  Entity type '{entity_type}' is aliased to '{resolved_type}'")
            metrics = metrics_config.get_entity_metrics(resolved_type)
            if not metrics:
                print(f"  No metrics defined for resolved entity type '{resolved_type}' either")
                return
            else:
                print(f"  Found metrics for resolved entity type '{resolved_type}':")
        else:
            return
    
    default_metrics = metrics_config.get_default_metrics(entity_type)
    print(f"  Default metrics: {', '.join(default_metrics)}")
    print("\n  All available metrics:")
    
    for metric_name, config in metrics.items():
        is_default = config.get('default', False)
        display_name = config.get('name', metric_name)
        unit = config.get('unit', 'unknown')
        print(f"    - {metric_name}: {display_name} ({unit}) {'[DEFAULT]' if is_default else ''}")


def print_metric_results(metric_results):
    """Print a summary of the metric results"""
    print("\nMetric Results Summary:")
    
    for metric_name, results in metric_results.items():
        print(f"\n  {metric_name}:")
        
        if not results:
            print("    No data found")
            continue
            
        for result in results:
            # Get metadata
            unit = result.metadata.get("unit", "unknown")
            facet = result.metadata.get("facet", "")
            facet_info = f" ({facet})" if facet else ""
            
            # Count datapoints and calculate range
            datapoints = len(result.timeseries)
            
            if datapoints > 0:
                # Calculate statistics
                values = [dp.value for dp in result.timeseries]
                min_val = min(values)
                max_val = max(values)
                avg_val = sum(values) / len(values)
                
                # Get time range
                start_time = result.timeseries[0].timestamp
                end_time = result.timeseries[-1].timestamp
                
                print(f"    {result.name}{facet_info}: {datapoints} datapoints")
                print(f"      Time range: {start_time.isoformat()} to {end_time.isoformat()}")
                print(f"      Values: min={min_val:.2f}, avg={avg_val:.2f}, max={max_val:.2f} {unit}")
            else:
                print(f"    {result.name}{facet_info}: No datapoints")


def serialize_metric_results(metric_results):
    """Serialize metric results to a JSON-compatible format"""
    serialized = {}
    
    for metric_name, results in metric_results.items():
        serialized[metric_name] = []
        
        for result in results:
            # Serialize timeseries data
            timeseries_data = [
                {
                    "timestamp": dp.timestamp.isoformat(),
                    "value": dp.value
                }
                for dp in result.timeseries
            ]
            
            # Create serialized result
            serialized_result = {
                "name": result.name,
                "metadata": result.metadata,
                "timeseries": timeseries_data
            }
            
            serialized[metric_name].append(serialized_result)
    
    return serialized


def main():
    """Main function"""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Initialize metrics configuration manager
    metrics_config = MetricsConfigManager()
    
    # If list-metrics flag is set, just print metrics info and exit
    if args.list_metrics:
        print_metrics_info(metrics_config, args.entity_type)
        return 0
    
    # Validate required arguments
    validate_args(args)

    # Initialize the New Relic client
    graphql_client = NewRelicGraphQLClient(
        api_key=args.api_key,
        account_id=args.account_id,
        debug=args.debug
    )
    
    # Initialize the query client
    query_client = NewRelicQueryClient(graphql_client)
    
    # Calculate time window
    now = datetime.now(UTC)
    since_time = now - timedelta(minutes=args.window)
    
    print(f"Retrieving metrics for entity: {args.entity_guid}")
    print(f"Entity type: {args.entity_type}")
    print(f"Time window: last {args.window} minutes ({since_time.isoformat()} to {now.isoformat()})")
    
    if args.metrics:
        print(f"Requested metrics: {', '.join(args.metrics)}")
    else:
        default_metrics = metrics_config.get_default_metrics(args.entity_type)
        print(f"Using default metrics: {', '.join(default_metrics)}")
    
    try:
        # Get entity metrics
        metric_results = query_client.get_entity_metrics_by_type(
            entity_guid=args.entity_guid,
            entity_type=args.entity_type,
            metrics=args.metrics,
            since=since_time,
            until=now
        )
        
        # Print results
        print_metric_results(metric_results)
        
        # Save to file if requested
        if args.output:
            serialized_results = serialize_metric_results(metric_results)
            with open(args.output, 'w') as f:
                json.dump(serialized_results, f, indent=2)
            print(f"\nResults saved to {args.output}")
        
    except Exception as e:
        print(f"ERROR: {str(e)}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main()) 