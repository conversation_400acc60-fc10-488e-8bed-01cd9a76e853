{"issueId": "afb1be25-3714-4115-9005-9929ef244089", "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/afb1be25-3714-4115-9005-9929ef244089?notifier=WEBHOOK", "title": "castai-workload-autoscaler query result is > 10.0 on 'Pod with CrashLoopBackOff -- '", "priority": "CRITICAL", "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXw2MTczNTc0MjAzNzg0NTI3Mzc5"], "impactedEntities": ["aquila-na1"], "totalIncidents": "1", "state": "ACTIVATED", "trigger": "STATE_CHANGE", "isCorrelated": "false", "createdAt": *************, "updatedAt": *************, "sources": ["newrelic"], "alertPolicyNames": ["Neurons k8s Infra - Critical"], "alertConditionNames": ["Pod with CrashLoopBackOff -- "], "workflowName": "obv-ai-processing-neurons", "chartLink": "https://gorgon.nr-assets.net/image/5f52ac1f-eabc-4086-ba53-77a6ee9322f8?config.legend.enabled=false&width=400&height=210", "product": "neurons", "nr_region": "us"}