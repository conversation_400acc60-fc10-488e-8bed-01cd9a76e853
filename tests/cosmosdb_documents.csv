id,work_item_id,nodes,links,rca,issue_info,autogen_message,status,_rid,_self,_etag,_attachments,_ts
3562acf3-ba34-46dd-a08c-f65b49099e80,1447894,[],[],,"{'issueId': '3562acf3-ba34-46dd-a08c-f65b49099e80', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/3562acf3-ba34-46dd-a08c-f65b49099e80?notifier=WEBHOOK', 'title': ""infrastructure-notification query result is > 10.0 on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMzA2Mzk4Nzk5MjU1MjA5MDYwMA'], 'impactedEntities': ['infrastructure-notification'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/9a8539ca-2f5d-43be-8ce5-f1dc38496957?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '3562acf3-ba34-46dd-a08c-f65b49099e80', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/3562acf3-ba34-46dd-a08c-f65b49099e80?notifier=WEBHOOK', 'title': ""infrastructure-notification query result is > 10.0 on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMzA2Mzk4Nzk5MjU1MjA5MDYwMA'], 'impactedEntities': ['infrastructure-notification'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/9a8539ca-2f5d-43be-8ce5-f1dc38496957?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1447894 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvT4AAAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvT4AAAAAAAAAA==/,"""0e0286ce-0000-4d00-0000-6708f0180000""",attachments/,**********
e1d0c1ad-4e4b-4d8e-b3ef-3b160a3322d3,1447975,[],[],,"{'issueId': 'e1d0c1ad-4e4b-4d8e-b3ef-3b160a3322d3', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/e1d0c1ad-4e4b-4d8e-b3ef-3b160a3322d3?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/7df4132a-4c8d-4620-b2f4-0041bde922c5?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'e1d0c1ad-4e4b-4d8e-b3ef-3b160a3322d3', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/e1d0c1ad-4e4b-4d8e-b3ef-3b160a3322d3?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/7df4132a-4c8d-4620-b2f4-0041bde922c5?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1447975 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvT5AAAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvT5AAAAAAAAAA==/,"""1702da1d-0000-4d00-0000-670927ce0000""",attachments/,**********
56e83927-d37c-4cd8-a703-c8114be9f26a,1448029,[],[],,"{'issueId': '56e83927-d37c-4cd8-a703-c8114be9f26a', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/56e83927-d37c-4cd8-a703-c8114be9f26a?notifier=WEBHOOK', 'title': ""Is Scheduled = 0.0 for at least 10 minutes on 'curateddocumentsvc-69c9bf47d-jlm8c'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTU2OTA1MTI5NDQ4NzY3ODE3MA', 'MTA5MzYyMHxJTkZSQXxOQXw2MzI1MTk5NjY3NDM1Nzg3MDY0', 'MTA5MzYyMHxJTkZSQXxOQXw3MTQ2MzE2NTIxMjg3MTEzODI3', 'MTA5MzYyMHxJTkZSQXxOQXwtMTAzNzYwNDQ0Nzc5ODUxNTczNQ'], 'impactedEntities': ['curateddocumentsvc-69c9bf47d-jlm8c', 'curateddocumentsvc-69c9bf47d-dhp6k', 'curateddocumentsvc-69c9bf47d-g4gvb', 'curateddocumentsvc-69c9bf47d-dzr5t'], 'totalIncidents': '4', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Pod is in Pending state for more than 10 minutes '], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/fe60ebbe-f42a-4253-a2ed-c63b8d792868?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '56e83927-d37c-4cd8-a703-c8114be9f26a', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/56e83927-d37c-4cd8-a703-c8114be9f26a?notifier=WEBHOOK', 'title': ""Is Scheduled = 0.0 for at least 10 minutes on 'curateddocumentsvc-69c9bf47d-jlm8c'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTU2OTA1MTI5NDQ4NzY3ODE3MA', 'MTA5MzYyMHxJTkZSQXxOQXw2MzI1MTk5NjY3NDM1Nzg3MDY0', 'MTA5MzYyMHxJTkZSQXxOQXw3MTQ2MzE2NTIxMjg3MTEzODI3', 'MTA5MzYyMHxJTkZSQXxOQXwtMTAzNzYwNDQ0Nzc5ODUxNTczNQ'], 'impactedEntities': ['curateddocumentsvc-69c9bf47d-jlm8c', 'curateddocumentsvc-69c9bf47d-dhp6k', 'curateddocumentsvc-69c9bf47d-g4gvb', 'curateddocumentsvc-69c9bf47d-dzr5t'], 'totalIncidents': '4', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Pod is in Pending state for more than 10 minutes '], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/fe60ebbe-f42a-4253-a2ed-c63b8d792868?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448029 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvT6AAAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvT6AAAAAAAAAA==/,"""1b0291ce-0000-4d00-0000-670944b00000""",attachments/,**********
350d4961-aa18-4fb9-8fb9-2a806fd5d741,1448073,[],[],,"{'issueId': '350d4961-aa18-4fb9-8fb9-2a806fd5d741', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/350d4961-aa18-4fb9-8fb9-2a806fd5d741?notifier=WEBHOOK', 'title': ""polaris-challenger query result is > 25.0 for 30 minutes on 'Percentage of Unavailable pods > 25%'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw3OTQ4NzEzOTA0OTk0MjkzMjIw'], 'impactedEntities': ['polaris-challenger'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Percentage of Unavailable pods > 25%'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/55a47824-b65f-40f2-82ab-714f4cd596b7?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '350d4961-aa18-4fb9-8fb9-2a806fd5d741', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/350d4961-aa18-4fb9-8fb9-2a806fd5d741?notifier=WEBHOOK', 'title': ""polaris-challenger query result is > 25.0 for 30 minutes on 'Percentage of Unavailable pods > 25%'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw3OTQ4NzEzOTA0OTk0MjkzMjIw'], 'impactedEntities': ['polaris-challenger'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Percentage of Unavailable pods > 25%'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/55a47824-b65f-40f2-82ab-714f4cd596b7?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448073 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvT7AAAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvT7AAAAAAAAAA==/,"""0100781d-0000-4d00-0000-670969e80000""",attachments/,**********
6d4eea4f-4a0c-473e-8fb0-4a4b118b10d0,1448097,[],[],,"{'issueId': '6d4eea4f-4a0c-473e-8fb0-4a4b118b10d0', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/6d4eea4f-4a0c-473e-8fb0-4a4b118b10d0?notifier=WEBHOOK', 'title': ""na2migrdspolaris-read-replica2 query result is > 1200.0 for 10 minutes on 'Increase in Replication Lag of RDS Replication Instance'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwxNDg3NDY0NzgyMjEwNzQ3NTcz'], 'impactedEntities': ['na2migrdspolaris-read-replica2'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - RDS'], 'alertConditionNames': ['Increase in Replication Lag of RDS Replication Instance'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '6d4eea4f-4a0c-473e-8fb0-4a4b118b10d0', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/6d4eea4f-4a0c-473e-8fb0-4a4b118b10d0?notifier=WEBHOOK', 'title': ""na2migrdspolaris-read-replica2 query result is > 1200.0 for 10 minutes on 'Increase in Replication Lag of RDS Replication Instance'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwxNDg3NDY0NzgyMjEwNzQ3NTcz'], 'impactedEntities': ['na2migrdspolaris-read-replica2'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - RDS'], 'alertConditionNames': ['Increase in Replication Lag of RDS Replication Instance'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448097 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvT8AAAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvT8AAAAAAAAAA==/,"""0800b27c-0000-4d00-0000-************""",attachments/,**********
c5e7c33f-a295-43c1-9854-5add6fd46dc2,1448111,[],[],,"{'issueId': 'c5e7c33f-a295-43c1-9854-5add6fd46dc2', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/c5e7c33f-a295-43c1-9854-5add6fd46dc2?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 25.0 for 30 minutes on 'Percentage of Unavailable pods > 25%'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtODI2MTUxMTExNTQxNjY4ODU1OA'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Percentage of Unavailable pods > 25%'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/3f2906e7-98b0-4682-b49e-6f8cd7fb2bdc?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'c5e7c33f-a295-43c1-9854-5add6fd46dc2', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/c5e7c33f-a295-43c1-9854-5add6fd46dc2?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 25.0 for 30 minutes on 'Percentage of Unavailable pods > 25%'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtODI2MTUxMTExNTQxNjY4ODU1OA'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Percentage of Unavailable pods > 25%'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/3f2906e7-98b0-4682-b49e-6f8cd7fb2bdc?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448111 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvT9AAAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvT9AAAAAAAAAA==/,"""1c00080a-0000-4d00-0000-6709ac710000""",attachments/,**********
300b8e9e-c645-4ed6-87ba-0d254edc7688,1448114,[],[],,"{'issueId': '300b8e9e-c645-4ed6-87ba-0d254edc7688', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/300b8e9e-c645-4ed6-87ba-0d254edc7688?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNDg1MTQ3NDY4OTM0MDE2NDM3MQ'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/64052edc-7808-4304-a1a9-8f32e471dad4?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '300b8e9e-c645-4ed6-87ba-0d254edc7688', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/300b8e9e-c645-4ed6-87ba-0d254edc7688?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNDg1MTQ3NDY4OTM0MDE2NDM3MQ'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/64052edc-7808-4304-a1a9-8f32e471dad4?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448114 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvT+AAAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvT+AAAAAAAAAA==/,"""1d000baf-0000-4d00-0000-6709af230000""",attachments/,**********
a90b8461-efcc-496f-8e5c-38ecd85fae34,1448115,[],[],,"{'issueId': 'a90b8461-efcc-496f-8e5c-38ecd85fae34', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/a90b8461-efcc-496f-8e5c-38ecd85fae34?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw1MTEyMTE3MzcyNjEyNTcwNTYz'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/c4e67e42-eca0-410e-927a-f71d7af5d39d?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'a90b8461-efcc-496f-8e5c-38ecd85fae34', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/a90b8461-efcc-496f-8e5c-38ecd85fae34?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw1MTEyMTE3MzcyNjEyNTcwNTYz'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/c4e67e42-eca0-410e-927a-f71d7af5d39d?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448115 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvT-AAAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvT-AAAAAAAAAA==/,"""1f008c58-0000-4d00-0000-6709b1d80000""",attachments/,**********
e7ac30de-06c4-40aa-bb95-40f5690af438,1448116,[],[],,"{'issueId': 'e7ac30de-06c4-40aa-bb95-40f5690af438', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/e7ac30de-06c4-40aa-bb95-40f5690af438?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNzkxMjEzNDk2NjU3OTQwODk1OQ'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/e517d0ae-f96e-4b5a-b422-f2f3d9d62572?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'e7ac30de-06c4-40aa-bb95-40f5690af438', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/e7ac30de-06c4-40aa-bb95-40f5690af438?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNzkxMjEzNDk2NjU3OTQwODk1OQ'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/e517d0ae-f96e-4b5a-b422-f2f3d9d62572?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448116 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQAAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQAAQAAAAAAAA==/,"""********-0000-4d00-0000-6709b4c50000""",attachments/,**********
dbf28316-e7c3-4bcc-b3d1-acc6107f4ff5,1448118,[],[],,"{'issueId': 'dbf28316-e7c3-4bcc-b3d1-acc6107f4ff5', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/dbf28316-e7c3-4bcc-b3d1-acc6107f4ff5?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNjg5MDQ4NjUwNDQzMDkwMjA0NQ'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'dbf28316-e7c3-4bcc-b3d1-acc6107f4ff5', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/dbf28316-e7c3-4bcc-b3d1-acc6107f4ff5?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNjg5MDQ4NjUwNDQzMDkwMjA0NQ'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448118 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQBAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQBAQAAAAAAAA==/,"""22000db6-0000-4d00-0000-6709b7eb0000""",attachments/,**********
13629b3e-e67e-4835-a518-9612fbea418d,1448120,[],[],,"{'issueId': '13629b3e-e67e-4835-a518-9612fbea418d', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/13629b3e-e67e-4835-a518-9612fbea418d?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtODU4NDY0OTUwOTAxMzUxOTI2Mw'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/ae7efb9b-3787-4673-8681-668ba87855b9?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '13629b3e-e67e-4835-a518-9612fbea418d', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/13629b3e-e67e-4835-a518-9612fbea418d?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtODU4NDY0OTUwOTAxMzUxOTI2Mw'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/ae7efb9b-3787-4673-8681-668ba87855b9?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448120 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQCAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQCAQAAAAAAAA==/,"""240026d1-0000-4d00-0000-6709bb5f0000""",attachments/,**********
2858757e-8036-4da6-b8a3-97804f0ffd13,1448131,[],[],,"{'issueId': '2858757e-8036-4da6-b8a3-97804f0ffd13', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/2858757e-8036-4da6-b8a3-97804f0ffd13?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/442d9570-e6a4-4ce1-af13-6426398a92d4?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '2858757e-8036-4da6-b8a3-97804f0ffd13', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/2858757e-8036-4da6-b8a3-97804f0ffd13?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/442d9570-e6a4-4ce1-af13-6426398a92d4?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448131 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQDAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQDAQAAAAAAAA==/,"""b2005ab4-0000-4d00-0000-670aaedc0000""",attachments/,**********
61ab7eb6-c99b-445a-8f96-b78353717cb9,1448136,[],[],,"{'issueId': '61ab7eb6-c99b-445a-8f96-b78353717cb9', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/61ab7eb6-c99b-445a-8f96-b78353717cb9?notifier=WEBHOOK', 'title': ""action-svc query result is > 10.0 on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTU2ODY4MDE2MTQ0NzQ1Nzk0Mg'], 'impactedEntities': ['action-svc'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/a24bc657-4212-4694-8dcf-bce50f51ba37?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '61ab7eb6-c99b-445a-8f96-b78353717cb9', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/61ab7eb6-c99b-445a-8f96-b78353717cb9?notifier=WEBHOOK', 'title': ""action-svc query result is > 10.0 on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTU2ODY4MDE2MTQ0NzQ1Nzk0Mg'], 'impactedEntities': ['action-svc'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/a24bc657-4212-4694-8dcf-bce50f51ba37?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448136 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQEAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQEAQAAAAAAAA==/,"""f80086bf-0000-4d00-0000-670b291a0000""",attachments/,**********
8df173c9-7242-4b4c-925e-40845a91810e,1448142,[],[],,"{'issueId': '8df173c9-7242-4b4c-925e-40845a91810e', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/8df173c9-7242-4b4c-925e-40845a91810e?notifier=WEBHOOK', 'title': ""polaris-challenger query result is > 25.0 for 30 minutes on 'Percentage of Unavailable pods > 25%'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw2ODk4NDE2MDk5MjUwNzI3NjAw'], 'impactedEntities': ['polaris-challenger'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Percentage of Unavailable pods > 25%'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/28bcc0c4-a67f-452b-a078-9ac96ae7c3c4?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '8df173c9-7242-4b4c-925e-40845a91810e', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/8df173c9-7242-4b4c-925e-40845a91810e?notifier=WEBHOOK', 'title': ""polaris-challenger query result is > 25.0 for 30 minutes on 'Percentage of Unavailable pods > 25%'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw2ODk4NDE2MDk5MjUwNzI3NjAw'], 'impactedEntities': ['polaris-challenger'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Percentage of Unavailable pods > 25%'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/28bcc0c4-a67f-452b-a078-9ac96ae7c3c4?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448142 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQFAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQFAQAAAAAAAA==/,"""********-0000-4d00-0000-670b71700000""",attachments/,**********
11bbfe69-3d37-4d64-bc90-bb561689a43e,1448146,[],[],,"{'issueId': '11bbfe69-3d37-4d64-bc90-bb561689a43e', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/11bbfe69-3d37-4d64-bc90-bb561689a43e?notifier=WEBHOOK', 'title': ""polaris-champion-866d4fb649-h77lf query result is > 0.0 for 10 minutes on 'Pod not Scheduled'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtODkyOTEzODk1MjkwNDA0MTE3Nw'], 'impactedEntities': ['polaris-champion-866d4fb649-h77lf'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod not Scheduled'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/85e80ad6-9ead-49c7-b227-e15aedf455ba?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '11bbfe69-3d37-4d64-bc90-bb561689a43e', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/11bbfe69-3d37-4d64-bc90-bb561689a43e?notifier=WEBHOOK', 'title': ""polaris-champion-866d4fb649-h77lf query result is > 0.0 for 10 minutes on 'Pod not Scheduled'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtODkyOTEzODk1MjkwNDA0MTE3Nw'], 'impactedEntities': ['polaris-champion-866d4fb649-h77lf'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod not Scheduled'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/85e80ad6-9ead-49c7-b227-e15aedf455ba?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448146 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQGAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQGAQAAAAAAAA==/,"""390162b4-0000-4d00-0000-670b97930000""",attachments/,**********
49edda43-0c1b-4c02-8b6b-d3249e3a477c,1448147,[],[],,"{'issueId': '49edda43-0c1b-4c02-8b6b-d3249e3a477c', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/49edda43-0c1b-4c02-8b6b-d3249e3a477c?notifier=WEBHOOK', 'title': ""polaris-challenger-ui-65d884f646-8r5w2 query result is > 0.0 for 10 minutes on 'Pod not Scheduled'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNDY1MjcwNTQ4OTMyNzgzNzQ4OQ'], 'impactedEntities': ['polaris-challenger-ui-65d884f646-8r5w2'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod not Scheduled'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/4a2704ef-2a90-4fd0-aaaf-151ba1e00669?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '49edda43-0c1b-4c02-8b6b-d3249e3a477c', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/49edda43-0c1b-4c02-8b6b-d3249e3a477c?notifier=WEBHOOK', 'title': ""polaris-challenger-ui-65d884f646-8r5w2 query result is > 0.0 for 10 minutes on 'Pod not Scheduled'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNDY1MjcwNTQ4OTMyNzgzNzQ4OQ'], 'impactedEntities': ['polaris-challenger-ui-65d884f646-8r5w2'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod not Scheduled'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/4a2704ef-2a90-4fd0-aaaf-151ba1e00669?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448147 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQHAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQHAQAAAAAAAA==/,"""4001b8f1-0000-4d00-0000-670ba42e0000""",attachments/,**********
e8909e4a-c6e6-42aa-91a4-12d39fc62623,1448151,[],[],,"{'issueId': 'e8909e4a-c6e6-42aa-91a4-12d39fc62623', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/e8909e4a-c6e6-42aa-91a4-12d39fc62623?notifier=WEBHOOK', 'title': ""polaris-challenger-ui-65d884f646-zng55 query result is > 0.0 for 10 minutes on 'Pod not Scheduled'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw4Mzc3MjYxMzgzNTE5OTc1NTg5'], 'impactedEntities': ['polaris-challenger-ui-65d884f646-zng55'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod not Scheduled'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'e8909e4a-c6e6-42aa-91a4-12d39fc62623', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/e8909e4a-c6e6-42aa-91a4-12d39fc62623?notifier=WEBHOOK', 'title': ""polaris-challenger-ui-65d884f646-zng55 query result is > 0.0 for 10 minutes on 'Pod not Scheduled'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw4Mzc3MjYxMzgzNTE5OTc1NTg5'], 'impactedEntities': ['polaris-challenger-ui-65d884f646-zng55'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod not Scheduled'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448151 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQIAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQIAQAAAAAAAA==/,"""4401b325-0000-4d00-0000-670ba96f0000""",attachments/,**********
efde6962-11a7-444d-b6a6-d37d06898176,1448152,[],[],,"{'issueId': 'efde6962-11a7-444d-b6a6-d37d06898176', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/efde6962-11a7-444d-b6a6-d37d06898176?notifier=WEBHOOK', 'title': ""na2migrdspolaris-read-replica2 query result is > 1200.0 for 10 minutes on 'Increase in Replication Lag of RDS Replication Instance'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwxNDg3NDY0NzgyMjEwNzQ3NTcz'], 'impactedEntities': ['na2migrdspolaris-read-replica2'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - RDS'], 'alertConditionNames': ['Increase in Replication Lag of RDS Replication Instance'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/b91a48aa-e4cd-49ec-951d-3142a4ac4654?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'efde6962-11a7-444d-b6a6-d37d06898176', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/efde6962-11a7-444d-b6a6-d37d06898176?notifier=WEBHOOK', 'title': ""na2migrdspolaris-read-replica2 query result is > 1200.0 for 10 minutes on 'Increase in Replication Lag of RDS Replication Instance'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwxNDg3NDY0NzgyMjEwNzQ3NTcz'], 'impactedEntities': ['na2migrdspolaris-read-replica2'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - RDS'], 'alertConditionNames': ['Increase in Replication Lag of RDS Replication Instance'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/b91a48aa-e4cd-49ec-951d-3142a4ac4654?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448152 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQJAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQJAQAAAAAAAA==/,"""4901f531-0000-4d00-0000-670bb22d0000""",attachments/,**********
d0c592d6-38c1-49b3-bc2b-f7feaf4cd365,1448153,[],[],,"{'issueId': 'd0c592d6-38c1-49b3-bc2b-f7feaf4cd365', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/d0c592d6-38c1-49b3-bc2b-f7feaf4cd365?notifier=WEBHOOK', 'title': ""device-sentiment-service-86b5fcb6f4-bvd9t query result is > 2.0 for 30 minutes on 'Evicted Pods'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwxNDAzODYzOTY3MTEzNjI5MzAy'], 'impactedEntities': ['device-sentiment-service-86b5fcb6f4-bvd9t'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Evicted Pods'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/594ecb0e-be91-496b-ac4c-dc1292071f3a?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'd0c592d6-38c1-49b3-bc2b-f7feaf4cd365', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/d0c592d6-38c1-49b3-bc2b-f7feaf4cd365?notifier=WEBHOOK', 'title': ""device-sentiment-service-86b5fcb6f4-bvd9t query result is > 2.0 for 30 minutes on 'Evicted Pods'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwxNDAzODYzOTY3MTEzNjI5MzAy'], 'impactedEntities': ['device-sentiment-service-86b5fcb6f4-bvd9t'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Evicted Pods'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/594ecb0e-be91-496b-ac4c-dc1292071f3a?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448153 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQKAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQKAQAAAAAAAA==/,"""4c01e7e4-0000-4d00-0000-670bb8360000""",attachments/,**********
f346aafd-67a0-429d-ae89-bc49f6520d06,1448154,[],[],,"{'issueId': 'f346aafd-67a0-429d-ae89-bc49f6520d06', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/f346aafd-67a0-429d-ae89-bc49f6520d06?notifier=WEBHOOK', 'title': ""app_inventory_workers_dbserver1.public.mdm_channel_35_na2-msk query result is > 15000.0 for 30 minutes on 'Debezium Lagging High'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['app_inventory_workers_dbserver1.public.mdm_channel_35_na2-msk'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Kafka '], 'alertConditionNames': ['Debezium Lagging High'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'f346aafd-67a0-429d-ae89-bc49f6520d06', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/f346aafd-67a0-429d-ae89-bc49f6520d06?notifier=WEBHOOK', 'title': ""app_inventory_workers_dbserver1.public.mdm_channel_35_na2-msk query result is > 15000.0 for 30 minutes on 'Debezium Lagging High'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['app_inventory_workers_dbserver1.public.mdm_channel_35_na2-msk'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Kafka '], 'alertConditionNames': ['Debezium Lagging High'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448154 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQLAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQLAQAAAAAAAA==/,"""500132d8-0000-4d00-0000-670bbea70000""",attachments/,**********
ca20df63-889f-473c-aa30-1369678bdfc3,1448156,[],[],,"{'issueId': 'ca20df63-889f-473c-aa30-1369678bdfc3', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/ca20df63-889f-473c-aa30-1369678bdfc3?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/de29cf4a-8691-43f5-88ce-c8ce26ae8275?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'ca20df63-889f-473c-aa30-1369678bdfc3', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/ca20df63-889f-473c-aa30-1369678bdfc3?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/de29cf4a-8691-43f5-88ce-c8ce26ae8275?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448156 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQMAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQMAQAAAAAAAA==/,"""5501a801-0000-4d00-0000-670bc5700000""",attachments/,**********
d4bee506-26d9-4a1d-8dc7-799f7af2199c,1448157,[],[],,"{'issueId': 'd4bee506-26d9-4a1d-8dc7-799f7af2199c', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/d4bee506-26d9-4a1d-8dc7-799f7af2199c?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 25.0 for 30 minutes on 'Percentage of Unavailable pods > 25%'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMzAxNTE5MTYxODQyMzIzMDM5NA'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Percentage of Unavailable pods > 25%'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/e1246395-fc9c-4859-9b3c-030e24073dd3?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'd4bee506-26d9-4a1d-8dc7-799f7af2199c', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/d4bee506-26d9-4a1d-8dc7-799f7af2199c?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 25.0 for 30 minutes on 'Percentage of Unavailable pods > 25%'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMzAxNTE5MTYxODQyMzIzMDM5NA'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Percentage of Unavailable pods > 25%'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/e1246395-fc9c-4859-9b3c-030e24073dd3?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448157 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQNAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQNAQAAAAAAAA==/,"""60018e6c-0000-4d00-0000-670bd8080000""",attachments/,**********
abbb1d1a-ef5f-4f0c-a67c-e59352c8ebfd,1448158,[],[],,"{'issueId': 'abbb1d1a-ef5f-4f0c-a67c-e59352c8ebfd', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/abbb1d1a-ef5f-4f0c-a67c-e59352c8ebfd?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwxNzM4Mzg5NDMxMzA0NTcxNDc0'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/48589c91-dfb7-4db0-b788-66f91b0a499f?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'abbb1d1a-ef5f-4f0c-a67c-e59352c8ebfd', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/abbb1d1a-ef5f-4f0c-a67c-e59352c8ebfd?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwxNzM4Mzg5NDMxMzA0NTcxNDc0'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/48589c91-dfb7-4db0-b788-66f91b0a499f?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448158 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQOAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQOAQAAAAAAAA==/,"""6101d68e-0000-4d00-0000-670bd9e20000""",attachments/,**********
********-d1ac-43fc-aab6-8b9fa3f961f2,1448159,[],[],,"{'issueId': '********-d1ac-43fc-aab6-8b9fa3f961f2', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/********-d1ac-43fc-aab6-8b9fa3f961f2?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTc0NjE3MTcyNTIxNDczNTg2MA'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/550e929f-0bb8-4141-a3b1-77602deab1de?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '********-d1ac-43fc-aab6-8b9fa3f961f2', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/********-d1ac-43fc-aab6-8b9fa3f961f2?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTc0NjE3MTcyNTIxNDczNTg2MA'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/550e929f-0bb8-4141-a3b1-77602deab1de?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448159 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQPAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQPAQAAAAAAAA==/,"""********-0000-4d00-0000-670bd9ed0000""",attachments/,**********
583205f3-1e64-4ff0-825b-aaa51b545742,1448160,[],[],,"{'issueId': '583205f3-1e64-4ff0-825b-aaa51b545742', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/583205f3-1e64-4ff0-825b-aaa51b545742?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw2ODQxODgzNTIwOTAwMzIxNzU3'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/3149dbbf-5a01-4de9-bb2c-9d507c89a237?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '583205f3-1e64-4ff0-825b-aaa51b545742', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/583205f3-1e64-4ff0-825b-aaa51b545742?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw2ODQxODgzNTIwOTAwMzIxNzU3'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/3149dbbf-5a01-4de9-bb2c-9d507c89a237?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448160 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQQAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQQAQAAAAAAAA==/,"""6101909c-0000-4d00-0000-670bd9f90000""",attachments/,**********
fb7e1038-65f1-46a1-bdbf-c60018ee0c78,1448161,[],[],,"{'issueId': 'fb7e1038-65f1-46a1-bdbf-c60018ee0c78', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/fb7e1038-65f1-46a1-bdbf-c60018ee0c78?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwyMTA1MzMxMzA0OTIyMTk4MjU0'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'fb7e1038-65f1-46a1-bdbf-c60018ee0c78', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/fb7e1038-65f1-46a1-bdbf-c60018ee0c78?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwyMTA1MzMxMzA0OTIyMTk4MjU0'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448161 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQRAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQRAQAAAAAAAA==/,"""610180a3-0000-4d00-0000-670bda040000""",attachments/,**********
f641c388-e580-4b6f-b414-751702dbce03,1448162,[],[],,"{'issueId': 'f641c388-e580-4b6f-b414-751702dbce03', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/f641c388-e580-4b6f-b414-751702dbce03?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw1ODUzMDExMzEzNDA4NjI1MDg2'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/46199c94-bc0a-4118-8a5b-dc687cb8e185?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'f641c388-e580-4b6f-b414-751702dbce03', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/f641c388-e580-4b6f-b414-751702dbce03?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw1ODUzMDExMzEzNDA4NjI1MDg2'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/46199c94-bc0a-4118-8a5b-dc687cb8e185?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448162 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQSAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQSAQAAAAAAAA==/,"""61015baa-0000-4d00-0000-670bda0f0000""",attachments/,**********
2a645d06-8703-4ccd-9d05-9a3009d83a12,1448163,[],[],,"{'issueId': '2a645d06-8703-4ccd-9d05-9a3009d83a12', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/2a645d06-8703-4ccd-9d05-9a3009d83a12?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNjI1MjQyOTA3MTc2NDg5ODEyMQ'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '2a645d06-8703-4ccd-9d05-9a3009d83a12', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/2a645d06-8703-4ccd-9d05-9a3009d83a12?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNjI1MjQyOTA3MTc2NDg5ODEyMQ'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'Not Available', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448163 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQTAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQTAQAAAAAAAA==/,"""61013db1-0000-4d00-0000-670bda1b0000""",attachments/,**********
a781c6ac-fe0a-47fc-95ac-178034248d3e,1448164,[],[],,"{'issueId': 'a781c6ac-fe0a-47fc-95ac-178034248d3e', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/a781c6ac-fe0a-47fc-95ac-178034248d3e?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwzMDgwMzAxOTQ0MTU4NjU4NjAx'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/8d05cafd-6910-488e-98da-4b5f3247483e?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'a781c6ac-fe0a-47fc-95ac-178034248d3e', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/a781c6ac-fe0a-47fc-95ac-178034248d3e?notifier=WEBHOOK', 'title': ""kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwzMDgwMzAxOTQ0MTU4NjU4NjAx'], 'impactedEntities': ['kong-data-plane'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/8d05cafd-6910-488e-98da-4b5f3247483e?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448164 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQUAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQUAQAAAAAAAA==/,"""6101fdb7-0000-4d00-0000-670bda260000""",attachments/,**********
ddf677fd-48e9-4d8c-9b09-6fbb24be9e65,1448167,[],[],,"{'issueId': 'ddf677fd-48e9-4d8c-9b09-6fbb24be9e65', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/ddf677fd-48e9-4d8c-9b09-6fbb24be9e65?notifier=WEBHOOK', 'title': ""r9pyqp1yvbqe7vv-0003-002 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['r9pyqp1yvbqe7vv-0003-002'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/9e50a525-a0d5-47eb-88f3-70ce8e848ddb?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'ddf677fd-48e9-4d8c-9b09-6fbb24be9e65', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/ddf677fd-48e9-4d8c-9b09-6fbb24be9e65?notifier=WEBHOOK', 'title': ""r9pyqp1yvbqe7vv-0003-002 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['r9pyqp1yvbqe7vv-0003-002'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/9e50a525-a0d5-47eb-88f3-70ce8e848ddb?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448167 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQVAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQVAQAAAAAAAA==/,"""710139fd-0000-4d00-0000-670bf6710000""",attachments/,**********
2fbb98be-a3eb-491f-8ebd-3c2ccb602908,1448168,[],[],,"{'issueId': '2fbb98be-a3eb-491f-8ebd-3c2ccb602908', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/2fbb98be-a3eb-491f-8ebd-3c2ccb602908?notifier=WEBHOOK', 'title': ""r9pyqp1yvbqe7vv-0003-001 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['r9pyqp1yvbqe7vv-0003-001'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/2316af53-4bba-4821-a8bf-a3d19466eaf3?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '2fbb98be-a3eb-491f-8ebd-3c2ccb602908', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/2fbb98be-a3eb-491f-8ebd-3c2ccb602908?notifier=WEBHOOK', 'title': ""r9pyqp1yvbqe7vv-0003-001 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['r9pyqp1yvbqe7vv-0003-001'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/2316af53-4bba-4821-a8bf-a3d19466eaf3?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448168 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQWAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQWAQAAAAAAAA==/,"""7201f912-0000-4d00-0000-670bf6940000""",attachments/,**********
1db94675-242a-47f1-a863-1a6a4e87f78e,1448169,[],[],,"{'issueId': '1db94675-242a-47f1-a863-1a6a4e87f78e', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/1db94675-242a-47f1-a863-1a6a4e87f78e?notifier=WEBHOOK', 'title': ""r9pyqp1yvbqe7vv-0002-001 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['r9pyqp1yvbqe7vv-0002-001'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/e84a279a-3e8d-4c8b-8d74-d5b5db9efbd6?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '1db94675-242a-47f1-a863-1a6a4e87f78e', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/1db94675-242a-47f1-a863-1a6a4e87f78e?notifier=WEBHOOK', 'title': ""r9pyqp1yvbqe7vv-0002-001 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['r9pyqp1yvbqe7vv-0002-001'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/e84a279a-3e8d-4c8b-8d74-d5b5db9efbd6?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448169 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQXAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQXAQAAAAAAAA==/,"""********-0000-4d00-0000-670bf8be0000""",attachments/,**********
6f7391ac-f327-4acc-90c0-c23ebda84b46,1448171,[],[],,"{'issueId': '6f7391ac-f327-4acc-90c0-c23ebda84b46', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/6f7391ac-f327-4acc-90c0-c23ebda84b46?notifier=WEBHOOK', 'title': ""r9pyqp1yvbqe7vv-0002-002 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['r9pyqp1yvbqe7vv-0002-002'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/c44892e6-4c44-4036-81b9-de008d9933f6?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '6f7391ac-f327-4acc-90c0-c23ebda84b46', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/6f7391ac-f327-4acc-90c0-c23ebda84b46?notifier=WEBHOOK', 'title': ""r9pyqp1yvbqe7vv-0002-002 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['r9pyqp1yvbqe7vv-0002-002'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/c44892e6-4c44-4036-81b9-de008d9933f6?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448171 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQYAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQYAQAAAAAAAA==/,"""730177af-0000-4d00-0000-670bf93c0000""",attachments/,**********
d0ffa79e-8424-4c9c-a8f0-1a0c3eb20f2c,1448181,[],[],,"{'issueId': 'd0ffa79e-8424-4c9c-a8f0-1a0c3eb20f2c', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/d0ffa79e-8424-4c9c-a8f0-1a0c3eb20f2c?notifier=WEBHOOK', 'title': ""nri-bundle-newrelic-logging-vl9qx query result is > 0.0 for 10 minutes on 'Pod not Scheduled'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTE2NTAyNDEwMTIxMjg3MTEzNg'], 'impactedEntities': ['nri-bundle-newrelic-logging-vl9qx'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['SRE - MI Azure Alert - Infrastructure'], 'alertConditionNames': ['Pod not Scheduled'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/71b192ad-e2d5-4172-8dd4-81855d12adb4?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'd0ffa79e-8424-4c9c-a8f0-1a0c3eb20f2c', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/d0ffa79e-8424-4c9c-a8f0-1a0c3eb20f2c?notifier=WEBHOOK', 'title': ""nri-bundle-newrelic-logging-vl9qx query result is > 0.0 for 10 minutes on 'Pod not Scheduled'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTE2NTAyNDEwMTIxMjg3MTEzNg'], 'impactedEntities': ['nri-bundle-newrelic-logging-vl9qx'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['SRE - MI Azure Alert - Infrastructure'], 'alertConditionNames': ['Pod not Scheduled'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/71b192ad-e2d5-4172-8dd4-81855d12adb4?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448181 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQZAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQZAQAAAAAAAA==/,"""c2019edb-0000-4d00-0000-670c7a880000""",attachments/,**********
ff466f5e-6efb-44ef-b65d-12c3b05b420a,1448182,[],[],,"{'issueId': 'ff466f5e-6efb-44ef-b65d-12c3b05b420a', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/ff466f5e-6efb-44ef-b65d-12c3b05b420a?notifier=WEBHOOK', 'title': ""https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443_action-svc query result is > 10.0 on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443_action-svc'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/5a7f872c-2d82-4e2a-9305-ab8c5da58966?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'ff466f5e-6efb-44ef-b65d-12c3b05b420a', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/ff466f5e-6efb-44ef-b65d-12c3b05b420a?notifier=WEBHOOK', 'title': ""https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443_action-svc query result is > 10.0 on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443_action-svc'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/5a7f872c-2d82-4e2a-9305-ab8c5da58966?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448182 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQaAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQaAQAAAAAAAA==/,"""c501d39b-0000-4d00-0000-670c7f030000""",attachments/,**********
6098cb04-d3b1-4426-aaba-4b98508b3b8b,1448362,[],[],,"{'issueId': '6098cb04-d3b1-4426-aaba-4b98508b3b8b', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/6098cb04-d3b1-4426-aaba-4b98508b3b8b?notifier=WEBHOOK', 'title': ""r9p186va1qnx6you-0001-001_us-east-1 query result is > 98.0 for 10 minutes on 'Redis Engine CPU utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['r9p186va1qnx6you-0001-001_us-east-1'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Engine CPU utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/4a784b26-77cd-4ac6-8856-6d32c02bf77f?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '6098cb04-d3b1-4426-aaba-4b98508b3b8b', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/6098cb04-d3b1-4426-aaba-4b98508b3b8b?notifier=WEBHOOK', 'title': ""r9p186va1qnx6you-0001-001_us-east-1 query result is > 98.0 for 10 minutes on 'Redis Engine CPU utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['r9p186va1qnx6you-0001-001_us-east-1'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Engine CPU utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/4a784b26-77cd-4ac6-8856-6d32c02bf77f?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448362 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQbAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQbAQAAAAAAAA==/,"""e701a83a-0000-4d00-0000-670cc55d0000""",attachments/,**********
f2b5ce51-05c4-46c6-ac53-a28b9025e945,1448366,[],[],,"{'issueId': 'f2b5ce51-05c4-46c6-ac53-a28b9025e945', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/f2b5ce51-05c4-46c6-ac53-a28b9025e945?notifier=WEBHOOK', 'title': ""polaris-challenger query result is > 25.0 for 30 minutes on 'Percentage of Unavailable pods > 25%'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw2ODk4NDE2MDk5MjUwNzI3NjAw'], 'impactedEntities': ['polaris-challenger'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Percentage of Unavailable pods > 25%'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/d5d29f98-fdf6-46c8-97f5-eee752e2612a?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'f2b5ce51-05c4-46c6-ac53-a28b9025e945', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/f2b5ce51-05c4-46c6-ac53-a28b9025e945?notifier=WEBHOOK', 'title': ""polaris-challenger query result is > 25.0 for 30 minutes on 'Percentage of Unavailable pods > 25%'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw2ODk4NDE2MDk5MjUwNzI3NjAw'], 'impactedEntities': ['polaris-challenger'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Percentage of Unavailable pods > 25%'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/d5d29f98-fdf6-46c8-97f5-eee752e2612a?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448366 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQcAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQcAQAAAAAAAA==/,"""e7016477-0000-4d00-0000-670cc5ea0000""",attachments/,**********
dd8c6ee2-cbc1-4088-8876-8a8a1185050c,1448743,[],[],,"{'issueId': 'dd8c6ee2-cbc1-4088-8876-8a8a1185050c', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/dd8c6ee2-cbc1-4088-8876-8a8a1185050c?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/4cdf1ae2-9986-4d2a-9a8e-0535e193852f?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'dd8c6ee2-cbc1-4088-8876-8a8a1185050c', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/dd8c6ee2-cbc1-4088-8876-8a8a1185050c?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/4cdf1ae2-9986-4d2a-9a8e-0535e193852f?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448743 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQdAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQdAQAAAAAAAA==/,"""2e022266-0000-4d00-0000-670d3fa80000""",attachments/,**********
0ff1d8d2-116c-44ba-9afc-59a1ad318966,1448817,[],[],,"{'issueId': '0ff1d8d2-116c-44ba-9afc-59a1ad318966', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/0ff1d8d2-116c-44ba-9afc-59a1ad318966?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/ce3d0579-528f-4d14-8fd7-894c7e3fe0ab?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '0ff1d8d2-116c-44ba-9afc-59a1ad318966', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/0ff1d8d2-116c-44ba-9afc-59a1ad318966?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/ce3d0579-528f-4d14-8fd7-894c7e3fe0ab?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448817 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQeAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQeAQAAAAAAAA==/,"""42020e5a-0000-4d00-0000-670d60730000""",attachments/,**********
124128fd-e5f2-4e52-99ab-f59a8bc5cea9,1448887,[],[],,"{'issueId': '124128fd-e5f2-4e52-99ab-f59a8bc5cea9', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/124128fd-e5f2-4e52-99ab-f59a8bc5cea9?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/405a3fb9-301a-480a-8721-e8d4deb0989e?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '124128fd-e5f2-4e52-99ab-f59a8bc5cea9', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/124128fd-e5f2-4e52-99ab-f59a8bc5cea9?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/405a3fb9-301a-480a-8721-e8d4deb0989e?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448887 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQfAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQfAQAAAAAAAA==/,"""6e02833c-0000-4d00-0000-670da9190000""",attachments/,**********
f2168394-9e73-4de5-b621-58f798a0cf50,1448893,[],[],,"{'issueId': 'f2168394-9e73-4de5-b621-58f798a0cf50', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/f2168394-9e73-4de5-b621-58f798a0cf50?notifier=WEBHOOK', 'title': ""action-svc query result is > 10.0 on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTU2ODY4MDE2MTQ0NzQ1Nzk0Mg'], 'impactedEntities': ['action-svc'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/3faedcfb-9ac1-4253-9fef-5a4790672ad1?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'f2168394-9e73-4de5-b621-58f798a0cf50', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/f2168394-9e73-4de5-b621-58f798a0cf50?notifier=WEBHOOK', 'title': ""action-svc query result is > 10.0 on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTU2ODY4MDE2MTQ0NzQ1Nzk0Mg'], 'impactedEntities': ['action-svc'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/3faedcfb-9ac1-4253-9fef-5a4790672ad1?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448893 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQgAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQgAQAAAAAAAA==/,"""8402f413-0000-4d00-0000-670dced20000""",attachments/,**********
66d9aa50-0765-41ca-b3a6-ac8d6eb34792,1448992,[],[],,"{'issueId': '66d9aa50-0765-41ca-b3a6-ac8d6eb34792', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/66d9aa50-0765-41ca-b3a6-ac8d6eb34792?notifier=WEBHOOK', 'title': ""na1prodrdspolaris-read-replica query result is > 1200.0 for 10 minutes on 'Increase in Replication Lag of RDS Replication Instance'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw3MDUzNzYzNjIxODY2MjU0NDIy'], 'impactedEntities': ['na1prodrdspolaris-read-replica'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - RDS'], 'alertConditionNames': ['Increase in Replication Lag of RDS Replication Instance'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/69af0293-27b5-48a4-8939-63228222edde?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '66d9aa50-0765-41ca-b3a6-ac8d6eb34792', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/66d9aa50-0765-41ca-b3a6-ac8d6eb34792?notifier=WEBHOOK', 'title': ""na1prodrdspolaris-read-replica query result is > 1200.0 for 10 minutes on 'Increase in Replication Lag of RDS Replication Instance'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw3MDUzNzYzNjIxODY2MjU0NDIy'], 'impactedEntities': ['na1prodrdspolaris-read-replica'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - RDS'], 'alertConditionNames': ['Increase in Replication Lag of RDS Replication Instance'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/69af0293-27b5-48a4-8939-63228222edde?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1448992 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQhAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQhAQAAAAAAAA==/,"""a3023db3-0000-4d00-0000-670e0ba90000""",attachments/,**********
d71d31c2-c199-4988-b550-a6e1d2d0a636,1449061,[],[],,"{'issueId': 'd71d31c2-c199-4988-b550-a6e1d2d0a636', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/d71d31c2-c199-4988-b550-a6e1d2d0a636?notifier=WEBHOOK', 'title': ""authservice query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTM1NDM0MTEwNzczMDU3MDU3Nw'], 'impactedEntities': ['authservice'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/9b91050c-a4f6-4b15-acbf-9b350f36c038?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'd71d31c2-c199-4988-b550-a6e1d2d0a636', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/d71d31c2-c199-4988-b550-a6e1d2d0a636?notifier=WEBHOOK', 'title': ""authservice query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtMTM1NDM0MTEwNzczMDU3MDU3Nw'], 'impactedEntities': ['authservice'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/9b91050c-a4f6-4b15-acbf-9b350f36c038?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449061 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQiAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQiAQAAAAAAAA==/,"""ab02a3ab-0000-4d00-0000-670e1e1f0000""",attachments/,**********
fff71997-f0bf-4f51-b964-5045909daa29,1449123,[],[],,"{'issueId': 'fff71997-f0bf-4f51-b964-5045909daa29', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/fff71997-f0bf-4f51-b964-5045909daa29?notifier=WEBHOOK', 'title': ""authservice query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw4ODc1ODE0MDcxMzA0NjE0NzYw'], 'impactedEntities': ['authservice'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/********-f442-47be-9d74-************?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'fff71997-f0bf-4f51-b964-5045909daa29', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/fff71997-f0bf-4f51-b964-5045909daa29?notifier=WEBHOOK', 'title': ""authservice query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw4ODc1ODE0MDcxMzA0NjE0NzYw'], 'impactedEntities': ['authservice'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/********-f442-47be-9d74-************?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449123 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQjAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQjAQAAAAAAAA==/,"""b90229f7-0000-4d00-0000-670e36d30000""",attachments/,**********
4c082648-0237-4c5c-baf6-86b712cc06e5,1449395,[],[],,"{'issueId': '4c082648-0237-4c5c-baf6-86b712cc06e5', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/4c082648-0237-4c5c-baf6-86b712cc06e5?notifier=WEBHOOK', 'title': ""AKS-RG-UKU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-UKU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/1fcda238-05f7-40f0-a0ee-f5666119dd94?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '4c082648-0237-4c5c-baf6-86b712cc06e5', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/4c082648-0237-4c5c-baf6-86b712cc06e5?notifier=WEBHOOK', 'title': ""AKS-RG-UKU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-UKU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/1fcda238-05f7-40f0-a0ee-f5666119dd94?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449395 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQkAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQkAQAAAAAAAA==/,"""eb0266dd-0000-4d00-0000-670e8ecc0000""",attachments/,**********
eafdbb23-5df3-4b05-97d5-afaf2f408251,1449418,[],[],,"{'issueId': 'eafdbb23-5df3-4b05-97d5-afaf2f408251', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/eafdbb23-5df3-4b05-97d5-afaf2f408251?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/00354c0e-7782-4fac-bf2b-b73a7e5c3e33?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'eafdbb23-5df3-4b05-97d5-afaf2f408251', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/eafdbb23-5df3-4b05-97d5-afaf2f408251?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/00354c0e-7782-4fac-bf2b-b73a7e5c3e33?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449418 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQlAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQlAQAAAAAAAA==/,"""f102ddbb-0000-4d00-0000-670e98280000""",attachments/,**********
7e85a9af-0cf6-41d2-abd4-54f8906db277,1449458,[],[],,"{'issueId': '7e85a9af-0cf6-41d2-abd4-54f8906db277', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/7e85a9af-0cf6-41d2-abd4-54f8906db277?notifier=WEBHOOK', 'title': ""polaris-enc-ap1-read-replica2 query result is > 90.0 for 15 minutes on 'High CPU Usage on Polaris RDS Cluster'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw0MTQ3NzgyMTAwNDQwNTI3MDAz'], 'impactedEntities': ['polaris-enc-ap1-read-replica2'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - RDS'], 'alertConditionNames': ['High CPU Usage on Polaris RDS Cluster'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/b519db69-a079-4a17-92be-8c81febc9811?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '7e85a9af-0cf6-41d2-abd4-54f8906db277', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/7e85a9af-0cf6-41d2-abd4-54f8906db277?notifier=WEBHOOK', 'title': ""polaris-enc-ap1-read-replica2 query result is > 90.0 for 15 minutes on 'High CPU Usage on Polaris RDS Cluster'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXw0MTQ3NzgyMTAwNDQwNTI3MDAz'], 'impactedEntities': ['polaris-enc-ap1-read-replica2'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - RDS'], 'alertConditionNames': ['High CPU Usage on Polaris RDS Cluster'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/b519db69-a079-4a17-92be-8c81febc9811?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449458 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQmAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQmAQAAAAAAAA==/,"""fb026981-0000-4d00-0000-670ea79e0000""",attachments/,**********
a7c536d1-a4f7-4f02-9b9f-8c02ed66f2ca,1449484,[],[],,"{'issueId': 'a7c536d1-a4f7-4f02-9b9f-8c02ed66f2ca', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/a7c536d1-a4f7-4f02-9b9f-8c02ed66f2ca?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0002-001 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0002-001'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/238f7708-ccce-45d7-b640-f8bf8483f519?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'a7c536d1-a4f7-4f02-9b9f-8c02ed66f2ca', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/a7c536d1-a4f7-4f02-9b9f-8c02ed66f2ca?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0002-001 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0002-001'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/238f7708-ccce-45d7-b640-f8bf8483f519?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449484 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQnAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQnAQAAAAAAAA==/,"""04032de8-0000-4d00-0000-670eb6670000""",attachments/,**********
4eb1dafb-16e7-49a8-85f2-a524bada4d86,1449487,[],[],,"{'issueId': '4eb1dafb-16e7-49a8-85f2-a524bada4d86', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/4eb1dafb-16e7-49a8-85f2-a524bada4d86?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0002-002 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0002-002'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/88e03bdf-0251-42d6-ac94-1f456dbf6626?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '4eb1dafb-16e7-49a8-85f2-a524bada4d86', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/4eb1dafb-16e7-49a8-85f2-a524bada4d86?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0002-002 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0002-002'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/88e03bdf-0251-42d6-ac94-1f456dbf6626?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449487 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQoAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQoAQAAAAAAAA==/,"""0603602b-0000-4d00-0000-670eb8fa0000""",attachments/,**********
d51f4469-a40e-45eb-998f-e36e1aa3a4c2,1449488,[],[],,"{'issueId': 'd51f4469-a40e-45eb-998f-e36e1aa3a4c2', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/d51f4469-a40e-45eb-998f-e36e1aa3a4c2?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0003-001 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0003-001'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/a934b008-1daa-4f89-ab0a-fc4efc6d4a67?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'd51f4469-a40e-45eb-998f-e36e1aa3a4c2', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/d51f4469-a40e-45eb-998f-e36e1aa3a4c2?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0003-001 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0003-001'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/a934b008-1daa-4f89-ab0a-fc4efc6d4a67?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449488 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQpAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQpAQAAAAAAAA==/,"""0603c2c8-0000-4d00-0000-670eb9f80000""",attachments/,**********
87b561c5-aff4-4143-8f58-a38184264b01,1449489,[],[],,"{'issueId': '87b561c5-aff4-4143-8f58-a38184264b01', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/87b561c5-aff4-4143-8f58-a38184264b01?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0001-001 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0001-001'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/b80048cf-1303-4890-841e-7ca9872d4dc6?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '87b561c5-aff4-4143-8f58-a38184264b01', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/87b561c5-aff4-4143-8f58-a38184264b01?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0001-001 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0001-001'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/b80048cf-1303-4890-841e-7ca9872d4dc6?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449489 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQqAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQqAQAAAAAAAA==/,"""07033d0c-0000-4d00-0000-670eba630000""",attachments/,**********
b6101698-c51d-4169-bd6c-5c5ff5142e52,1449490,[],[],,"{'issueId': 'b6101698-c51d-4169-bd6c-5c5ff5142e52', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/b6101698-c51d-4169-bd6c-5c5ff5142e52?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0003-002 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0003-002'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/8020fd8a-7fed-4617-9f8d-917bd0cb263a?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'b6101698-c51d-4169-bd6c-5c5ff5142e52', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/b6101698-c51d-4169-bd6c-5c5ff5142e52?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0003-002 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0003-002'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/8020fd8a-7fed-4617-9f8d-917bd0cb263a?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449490 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQrAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQrAQAAAAAAAA==/,"""********-0000-4d00-0000-670ebc820000""",attachments/,**********
60ca7102-1d6b-4b72-8395-da821ed06ab3,1449492,[],[],,"{'issueId': '60ca7102-1d6b-4b72-8395-da821ed06ab3', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/60ca7102-1d6b-4b72-8395-da821ed06ab3?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0001-002 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0001-002'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/dd40865f-3fb0-419a-a82e-9c0a9f350d63?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '60ca7102-1d6b-4b72-8395-da821ed06ab3', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/60ca7102-1d6b-4b72-8395-da821ed06ab3?notifier=WEBHOOK', 'title': ""appakuziuvzg1ha-0001-002 query result is > 90.0 for 60 minutes on 'Redis Memory utilization is High '"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['appakuziuvzg1ha-0001-002'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Redis'], 'alertConditionNames': ['Redis Memory utilization is High '], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/dd40865f-3fb0-419a-a82e-9c0a9f350d63?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449492 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQsAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQsAQAAAAAAAA==/,"""08034dca-0000-4d00-0000-670ebd260000""",attachments/,**********
c780502b-596b-458b-bf87-78f33161e2c9,1449760,[],[],,"{'issueId': 'c780502b-596b-458b-bf87-78f33161e2c9', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1/issues/0ea2df1c-adab-45d2-aae0-042b609d2322?notifier=SLACK', 'title': ""k8s:https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443:access-control-scopes:hpa:keda-hpa-scopes-msg-service-keda-hpa query result is > 4.0 on 'Unhealthy HPA'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNDA3OTI1NzM1MjU4OTY1MzE3Mg'], 'impactedEntities': ['k8s:https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443:access-control-scopes:hpa:keda-hpa-scopes-msg-service-keda-hpa'], 'totalIncidents': 1, 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': False, 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Obv'], 'alertConditionNames': ['Unhealthy HPA'], 'workflowName': 'DBA Team workflow', 'chartLink': 'https://gorgon.nr-assets.net/image/aefdbc7f-ab4c-45c5-b4e1-4e63613edc1c?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'c780502b-596b-458b-bf87-78f33161e2c9', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1/issues/0ea2df1c-adab-45d2-aae0-042b609d2322?notifier=SLACK', 'title': ""k8s:https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443:access-control-scopes:hpa:keda-hpa-scopes-msg-service-keda-hpa query result is > 4.0 on 'Unhealthy HPA'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtNDA3OTI1NzM1MjU4OTY1MzE3Mg'], 'impactedEntities': ['k8s:https://aks-rg-nvu-prd-neurons-5b3af7ff.hcp.eastus.azmk8s.io:443:access-control-scopes:hpa:keda-hpa-scopes-msg-service-keda-hpa'], 'totalIncidents': 1, 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': False, 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Obv'], 'alertConditionNames': ['Unhealthy HPA'], 'workflowName': 'DBA Team workflow', 'chartLink': 'https://gorgon.nr-assets.net/image/aefdbc7f-ab4c-45c5-b4e1-4e63613edc1c?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1449760 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQtAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQtAQAAAAAAAA==/,"""65039e45-0000-4d00-0000-670f5bc40000""",attachments/,**********
c9571226-89dc-4d69-a6cd-7d231af322e9,1450980,[],[],,"{'issueId': 'c9571226-89dc-4d69-a6cd-7d231af322e9', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/c9571226-89dc-4d69-a6cd-7d231af322e9?notifier=WEBHOOK', 'title': ""authservice query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtODg0MTE1OTcxMDE4MjMzNjM5Nw'], 'impactedEntities': ['authservice'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/afe266c6-8f76-43f0-89d1-c745426c912d?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'}","Process following Issue Detail {'issueId': 'c9571226-89dc-4d69-a6cd-7d231af322e9', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/c9571226-89dc-4d69-a6cd-7d231af322e9?notifier=WEBHOOK', 'title': ""authservice query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'"", 'priority': 'CRITICAL', 'EntityId': ['MTA5MzYyMHxJTkZSQXxOQXwtODg0MTE1OTcxMDE4MjMzNjM5Nw'], 'impactedEntities': ['authservice'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['MI Alert - Infrastructure'], 'alertConditionNames': ['Pod with CrashLoopBackOff'], 'workflowName': 'obv-ai-processing', 'chartLink': 'https://gorgon.nr-assets.net/image/afe266c6-8f76-43f0-89d1-c745426c912d?config.legend.enabled=false&width=400&height=210', 'product': 'MDM', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1450980 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQuAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQuAQAAAAAAAA==/,"""8503c96f-0000-4d00-0000-670f9c560000""",attachments/,**********
2e74b07c-e7c2-4eef-9c28-4f2e28d56233,1450988,[],[],,"{'issueId': '2e74b07c-e7c2-4eef-9c28-4f2e28d56233', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/2e74b07c-e7c2-4eef-9c28-4f2e28d56233?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/2067dbb5-4bc0-4312-9cbb-070b67d1b3b7?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '2e74b07c-e7c2-4eef-9c28-4f2e28d56233', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/2e74b07c-e7c2-4eef-9c28-4f2e28d56233?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/2067dbb5-4bc0-4312-9cbb-070b67d1b3b7?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1450988 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQvAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQvAQAAAAAAAA==/,"""86034cf9-0000-4d00-0000-670fa0450000""",attachments/,**********
7ea87115-4a0a-48d9-b1b5-a19a319b3e0a,1451225,[],[],,"{'issueId': '7ea87115-4a0a-48d9-b1b5-a19a319b3e0a', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/7ea87115-4a0a-48d9-b1b5-a19a319b3e0a?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/acb7424a-7084-4288-bbc5-eadef591ff78?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'}","Process following Issue Detail {'issueId': '7ea87115-4a0a-48d9-b1b5-a19a319b3e0a', 'issueUrl': 'https://radar-api.service.newrelic.com/accounts/1093620/issues/7ea87115-4a0a-48d9-b1b5-a19a319b3e0a?notifier=WEBHOOK', 'title': ""AKS-RG-MLU-PRD-Neurons.default query result is > 99.0 for 30 minutes on 'AKS Nodepool Scale Out Percantage above 99%'"", 'priority': 'CRITICAL', 'EntityId': [''], 'impactedEntities': ['AKS-RG-MLU-PRD-Neurons.default'], 'totalIncidents': '1', 'state': 'ACTIVATED', 'trigger': 'STATE_CHANGE', 'isCorrelated': 'false', 'createdAt': *************, 'updatedAt': *************, 'sources': ['newrelic'], 'alertPolicyNames': ['Neurons k8s Infra - Critical'], 'alertConditionNames': ['AKS Nodepool Scale Out Percantage above 99%'], 'workflowName': 'obv-ai-processing-neurons', 'chartLink': 'https://gorgon.nr-assets.net/image/acb7424a-7084-4288-bbc5-eadef591ff78?config.legend.enabled=false&width=400&height=210', 'product': 'neurons', 'nr_region': 'us'} from NewRelic. Perform following in order
            1) From the gathered issue detail generate root cause analysis, get and analyse error logs for entity within the time frame of the issue (use function_calling agent) to enhance the RCA report and also use web surfer agent to search web for detailed information on the error logs for root cause, remedy and preventive action.
            2) Search Web for detailed information on the error logs for root cause, remedy and preventive action.
            3) Root Cause Analysis should be in following json format:
            {""customer_affected"": ""<customer_affected>"",
                ""date_incident_began"": ""<date_incident_began>"",
                ""date_incident_resolved"": ""<date_incident_resolved>"",
                ""teams_involved"": ""<teams_involved>"",
                ""customer_impact"": ""<customer_impact> (e.g., downtime, performance degradation, etc.)"",
                ""root_cause"": ""<root_cause> (Detailed description of what the root cause can be from the given issue detail and error logs, search internet/web if needed, Important: This should atleast be 5 lines long)"",
                ""remedy"": ""<remedy> (Detailed description of what can be done to fix the issue, search internet/web if needed)"",
                ""preventive_action"": ""<preventive_action> (Detailed description of what can be done to prevent the issue from happening again, search internet/web if needed)""
            } AND also issue detail in following json format:
            issue_details = {
                ""issue_id"": ""<issue_id>"",
                ""issue_url"": ""<issue_url>"",
                ""title"": ""<title>"",
                ""priority"": ""<priority>"",
                ""impacted_entities"": ""<impacted_entities>"",
                ""total_incidents"": ""<total_incidents>"",
                ""state"": ""<state>"",
                ""trigger"": ""<trigger>"",
                ""is_correlated"": ""<is_correlated>"",
                ""created_at"": ""<created_at>"",
                ""updated_at"": ""<updated_at>"",
                ""sources"": ""<sources>"",
                ""alert_policy_names"": ""<alert_policy_names>"",
                ""alert_condition_names"": ""<alert_condition_names>"",
                ""workflow_name"": ""<workflow_name>"",
                ""chart_url"": ""<chart_url>""
            }.
            4) Send the affected entity detail, Issue Detail Detail and generated Root Cause Analysis (RCA) detail for topology generation and also ADO comment for ticket id 1451225 using ""generate_topology_report"" function with help of functioncallingagent.
            5) Once topology is generated, you are done with your task, you can exit. No need to continue or do additional steps.
            IMPORTANT: UserProxy can't help you with this task, you need to do it yourself. Continue with information you have on hand, no manual steps are required.
            IMPORTANT: Root Cause should not be shorter than 5 lines, make sure to search internet/web for detailed information on the root cause, remedy and preventive action.
            ",completed,1MBWANbAdvQwAQAAAAAAAA==,dbs/1MBWAA==/colls/1MBWANbAdvQ=/docs/1MBWANbAdvQwAQAAAAAAAA==/,"""8b032214-0000-4d00-0000-670fb2920000""",attachments/,1729082002
