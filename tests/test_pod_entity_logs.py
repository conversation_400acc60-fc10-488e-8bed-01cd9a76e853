#!/usr/bin/env python3
"""
Test script for retrieving logs from New Relic for the specific pod entity 
from the crashloop workflow test.

This script extracts the entity GUID from the test data and retrieves logs for it.
"""

import os
import logging
from datetime import datetime, timezone, timedelta
import dotenv
import json

# Import New Relic client classes
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.base import Region
from lib.new_relic.logs import NewRelicLogsClient

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test data from test_crashloop_workflow.py
TEST_DATA = {
    "issueId": "e50de03b-4464-4c8a-9fb0-a890f73035f6",
    "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/e50de03b-4464-4c8a-9fb0-a890f73035f6?notifier=WEBHOOK",
    "title": "agent-management-background-container-service query result is > 10.0 on 'Pod with CrashLoopBackOff -- '",
    "priority": "CRITICAL",
    "EntityId": [
        "MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"
    ],
    "impactedEntities": [
        "agent-management-background-container-service"
    ],
    "totalIncidents": "1",
    "state": "ACTIVATED",
    "trigger": "STATE_CHANGE",
    "isCorrelated": "false",
    "createdAt": *************,
    "updatedAt": *************,
    "sources": [
        "newrelic"
    ],
    "alertPolicyNames": [
        "Neurons k8s Infra - Critical"
    ],
    "alertConditionNames": [
        "Pod with CrashLoopBackOff -- "
    ],
    "workflowName": "obv-ai-processing-neurons",
    "chartLink": "https://gorgon.nr-assets.net/image/b0e0b79d-b76e-4ed5-9677-ca4a12b86f7d?config.legend.enabled=false&width=400&height=210",
    "product": "neurons",
    "nr_region": "us"
}

def get_pod_logs():
    """Retrieve and display logs for the pod entity."""
    # Extract pod entity GUID from test data
    pod_entity_guid = TEST_DATA.get("EntityId", [""])[0]
    
    if not pod_entity_guid:
        logger.error("No pod entity GUID found in test data")
        return
    
    # Get required environment variables
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        logger.error("Missing required environment variables: NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set.")
        return
    
    # Determine time range based on alert timestamps
    # Convert milliseconds to seconds for datetime
    if TEST_DATA.get("createdAt") and TEST_DATA.get("updatedAt"):
        alert_created_time = datetime.fromtimestamp(TEST_DATA["createdAt"] / 1000, timezone.utc)
        alert_updated_time = datetime.fromtimestamp(TEST_DATA["updatedAt"] / 1000, timezone.utc)
        
        # Add buffer before and after alert times
        since_time = alert_created_time - timedelta(minutes=30)
        until_time = alert_updated_time + timedelta(minutes=30)
    else:
        # Fallback to recent time range if alert times not available
        until_time = datetime.now(timezone.utc)
        since_time = until_time - timedelta(hours=2)
    
    logger.info(f"Testing log retrieval for pod entity GUID: {pod_entity_guid}")
    logger.info(f"Alert created at: {datetime.fromtimestamp(TEST_DATA['createdAt'] / 1000, timezone.utc).isoformat()}")
    logger.info(f"Alert updated at: {datetime.fromtimestamp(TEST_DATA['updatedAt'] / 1000, timezone.utc).isoformat()}")
    logger.info(f"Using time range: {since_time.isoformat()} to {until_time.isoformat()}")
    
    # Determine New Relic region based on test data
    region = Region.US if TEST_DATA.get("nr_region", "us").lower() == "us" else Region.EU
    
    # Initialize the New Relic GraphQL client
    nr_client = NewRelicGraphQLClient(
        api_key=api_key,
        account_id=account_id,
        region=region,
        debug=False
    )
    
    # Initialize the New Relic Logs client
    logs_client = NewRelicLogsClient(client=nr_client, debug=True)
    
    try:
        # Get entity type from alert info - K8S_POD is a reasonable assumption based on "Pod with CrashLoopBackOff"
        entity_type = "K8S_POD"
        
        # Get impacted entity name(s)
        impacted_entities = TEST_DATA.get("impactedEntities", [])
        pod_name = impacted_entities[0] if impacted_entities else None
        
        # Get cluster info from alert policy name if available
        alert_policy = TEST_DATA.get("alertPolicyNames", [""])[0]
        product = TEST_DATA.get("product", "").lower()
        
        # Determine appropriate log partition based on product and region
        cluster_partition = None
        if product == "neurons":
            nr_region = TEST_DATA.get("nr_region", "us").lower()
            if nr_region == "us":
                cluster_partition = "neurons_nvu"  # Default for US Neurons
            else:
                cluster_partition = "neurons_all"  # Default for other Neurons regions
                
        logger.info(f"Using entity type: {entity_type}")
        logger.info(f"Using cluster partition: {cluster_partition}")
        
        # Method 1: Get logs using entity_guid
        logger.info("Method 1: Retrieving logs using get_entity_logs()")
        
        # Get entity-type specific log configuration
        logs_config = logs_client.get_logs_config_for_entity_type(entity_type, 
                                                                  cluster_id=pod_name if pod_name else "unknown")
        
        entity_logs = logs_client.get_entity_logs(
            entity_guid=pod_entity_guid,
            since=since_time,
            until=until_time,
            entity_type=entity_type,
            logs_config=logs_config,
            limit=50,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(entity_logs)} logs using get_entity_logs()")
        
        # Print logs (limit to first 10)
        print_logs(entity_logs[:10])
        
        # Method 2: If pod name is available, use get_pod_logs
        if pod_name:
            logger.info(f"Method 2: Retrieving logs using get_pod_logs() for pod: {pod_name}")
            pod_logs = logs_client.get_pod_logs(
                pod_name=pod_name,
                cluster_partition=cluster_partition,
                since=since_time, 
                until=until_time,
                log_level="error",  # Focus on error logs for crash loop issues
                limit=50,
                account_id=account_id
            )
            
            logger.info(f"Retrieved {len(pod_logs)} logs using get_pod_logs()")
            
            # Print logs (limit to first 10)
            print_logs(pod_logs[:10])
        
        return entity_logs
        
    except Exception as e:
        logger.error(f"Error retrieving logs: {str(e)}", exc_info=True)
        return []

def print_logs(logs):
    """Pretty print logs for readability."""
    if not logs:
        logger.info("No logs found")
        return
    
    logger.info(f"Found {len(logs)} logs:")
    
    for i, log in enumerate(logs):
        logger.info(f"Log {i+1}:")
        # Format the log as a pretty-printed JSON string
        formatted_log = json.dumps(log, indent=2, default=str)
        logger.info(formatted_log)
        logger.info("-" * 80)

if __name__ == "__main__":
    get_pod_logs() 