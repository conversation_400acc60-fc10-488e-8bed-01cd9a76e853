import json
import os
import sys

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.agent_functions import generate_topology_report

def test_generate_topology_report_manual(issue_details_json):

    issue_details = json.loads(issue_details_json)
    issue_details['issue_id'] = issue_details['issueId']
    issue_details['issue_url'] = issue_details['issueUrl']
    issue_details['priority'] = issue_details['priority']
    issue_details['impacted_entities'] = issue_details['impactedEntities']
    issue_details['total_incidents'] = issue_details['totalIncidents']
    issue_details['state'] = issue_details['state']
    issue_details['trigger'] = issue_details['trigger']
    issue_details['is_correlated'] = issue_details['isCorrelated']
    issue_details['created_at'] = issue_details['createdAt']
    issue_details['updated_at'] = issue_details['updatedAt']
    issue_details['sources'] = issue_details['sources']
    issue_details['alert_policy_names'] = issue_details['alertPolicyNames']
    issue_details['alert_condition_names'] = issue_details['alertConditionNames']
    issue_details['workflow_name'] = issue_details['workflowName']
    issue_details['chart_url'] = issue_details['chartLink']
    issue_details['product'] = issue_details['product']
    issue_details['nr_region'] = issue_details['nr_region']
    issue_details['entity_id'] = issue_details['EntityId'][0]
    issue_details['impacted_entities'] = issue_details['impactedEntities']
    issue_details['total_incidents'] = issue_details['totalIncidents']

    # json
    issue_details_json = json.dumps(issue_details)

    # Input data
    entity_id = issue_details["EntityId"][0]
    if not entity_id:
        entity_id = issue_details["impactedEntities"][0]
    
    rca_details = {
        "customer_affected": "Test Customer",
        "date_incident_began": "2024-03-02T10:00:00Z",
        "date_incident_resolved": "2024-03-02T11:00:00Z",
        "teams_involved": "DevOps, Platform",
        "customer_impact": "Service degradation",
        "root_cause": "Pod eviction due to resource constraints",
        "remedy": "Increased resource allocation",
        "preventive_action": "Implement better resource monitoring"
    }
    rca_details_json = json.dumps(rca_details)
    
    ticket_id = "TEST-1234"
    product = issue_details["product"]

    # Call the function
    result = generate_topology_report(entity_id, issue_details_json, rca_details_json, ticket_id, product, ado_comment=False )

    # print local topo server issue link
    print(f"Local Topo Server Issue Link: http://localhost:8006/{issue_details['issue_id']}")

    # Print the result
    print("Result:", result)

    # You can add more print statements here to inspect various aspects of the function's behavior

if __name__ == '__main__':
    issue_details_json = """
{"issueId": "be0bfcbe-78b6-484a-bfb5-848fbf426f5c", "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/be0bfcbe-78b6-484a-bfb5-848fbf426f5c?notifier=WEBHOOK", "title": "kong-data-plane query result is > 10.0 for 45 minutes on 'Pod with CrashLoopBackOff'", "priority": "CRITICAL", "EntityId": ["MTA5MzYyMHxJTkZSQXxOQXwtNjEyMTI3MjQyMTU1NjQ3NjQ2Ng"], "impactedEntities": ["kong-data-plane"], "totalIncidents": "1", "state": "ACTIVATED", "trigger": "STATE_CHANGE", "isCorrelated": "false", "createdAt": *************, "updatedAt": *************, "sources": ["newrelic"], "alertPolicyNames": ["MI Alert - Infrastructure"], "alertConditionNames": ["Pod with CrashLoopBackOff"], "workflowName": "obv-ai-processing", "chartLink": "https://gorgon.nr-assets.net/image/009eaeb6-a8ec-4a71-a4bf-dc36df54ec67?config.legend.enabled=false&width=400&height=210", "product": "MDM", "nr_region": "us"}
    """
    test_generate_topology_report_manual(issue_details_json)