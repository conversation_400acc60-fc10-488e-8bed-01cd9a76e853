from data_processor.lib.data_factory import DataFactory
import yaml
import json
import os

def load_config(file_path):
    with open(file_path, 'r') as file:
        return yaml.safe_load(file)


def trunc_dict(dictionary_output, limit: int = 100):
    return json.dumps(dictionary_output)[0:min(len(json.dumps(dictionary_output)), limit)]


if __name__ == '__main__':

    config = load_config(os.path.join(os.path.dirname(__file__), "test_credentials.yaml"))
    ## create an array of parent keys from the config file
    parent_keys = list(config.keys())
    connectors_count = len(parent_keys)
    print(connectors_count, " keys found in the credentials.yaml file.")
    connectors_tested_count = 0
    failed_tests = []

    for tool_cred in parent_keys:
        print(f"Testing {tool_cred}...")
        try:
            if tool_cred == 'new_relic':
                print("\n Testing New Relic Connector")
                nr_api_key = config.get('new_relic', {}).get('nr_api_key')
                nr_account_id = config.get('new_relic', {}).get('nr_account_id')
                nr_api_domain = config.get('new_relic', {}).get('nr_api_domain', 'api.newrelic.com')

                nr_client = DataFactory.get_new_relic_client(nr_api_key, nr_account_id, nr_api_domain)
                if not nr_client.test_connection():
                    raise Exception(f"Connection to {tool_cred} failed")
                else:
                    print(f"\n Credentials successfully tested for {tool_cred}. Now running a sample query")
                    # Add your query here
                    nrql_expression = "SELECT rate(count(newrelic.goldenmetrics.apm.application.throughput), 1 MINUTES) AS 'Throughput' FROM Metric WHERE entity.guid in ('**********************************************') LIMIT MAX TIMESERIES"
                    nr_output = nr_client.execute_nrql_query(nrql_expression)
                    print("\n Sample output from New Relic:\n", trunc_dict(nr_output))
                    connectors_tested_count += 1
        except Exception as e:
            print(f"Error in testing code in {tool_cred} connector: ", e)
            failed_tests.append(tool_cred)
            continue
    print("\n\n", connectors_tested_count, " connectors tested successfully out of ", connectors_count)
    print("Failed tests: ", failed_tests)