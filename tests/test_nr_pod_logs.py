#!/usr/bin/env python3
"""
Simplified test script for retrieving logs from New Relic for a specific pod entity.
This version fixes the NRQL syntax errors in the previous test scripts.
"""

import os
import logging
from datetime import datetime, timezone, timedelta
import dotenv
import json

# Import New Relic client classes
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.base import Region
from lib.new_relic.logs import NewRelicLogsClient

# Load environment variables
dotenv.load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Test New Relic logs retrieval with fixed query syntax."""
    # Get required environment variables
    api_key = os.getenv("NEWRELIC_API_KEY")
    account_id = os.getenv("NEWRELIC_ACCOUNT_ID")
    
    if not api_key or not account_id:
        logger.error("Missing required environment variables: NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID must be set.")
        return
    
    # Set pod entity GUID to test
    pod_entity_guid = "MTA5MzYyMHxJTkZSQXxOQXwtNzgxNzA4NTkzNDM0NjkxMjk5Mg"
    pod_name = "agent-management-background-container-service"  # From the test data
    
    # Define time range for logs (last 24 hours)
    until_time = datetime.now(timezone.utc)
    since_time = until_time - timedelta(hours=24)
    
    logger.info(f"Testing log retrieval for pod entity GUID: {pod_entity_guid}")
    logger.info(f"Pod name: {pod_name}")
    logger.info(f"Time range: {since_time.isoformat()} to {until_time.isoformat()}")
    
    # Initialize the New Relic GraphQL client
    nr_client = NewRelicGraphQLClient(
        api_key=api_key,
        account_id=account_id,
        region=Region.US,
        debug=True  # Enable debug mode to see the queries
    )
    
    # Initialize the New Relic Logs client
    logs_client = NewRelicLogsClient(client=nr_client, debug=True)
    
    try:
        # Method 1: Direct NRQL query for entity logs with fixed syntax
        logger.info("Method 1: Direct NRQL query for entity logs")
        
        # Build a query with valid syntax (avoiding the colon inside quotes issue)
        query = f"""
        SELECT timestamp, level, message, pod_name, kubernetes.pod.name 
        FROM Log 
        WHERE entity.guid = '{pod_entity_guid}'
        SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
        UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
        LIMIT 10
        """
        
        entity_logs = logs_client.query_logs(
            query=query,
            limit=10,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(entity_logs)} logs using direct query")
        print_logs(entity_logs)
        
        # Method 2: Query by pod name directly
        logger.info("Method 2: Query logs by pod name")
        
        pod_query = f"""
        SELECT timestamp, level, message, pod_name, kubernetes.pod.name, container_name, kubernetes.container.name
        FROM Log
        WHERE pod_name = '{pod_name}' OR kubernetes.pod.name = '{pod_name}'
        SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
        UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
        LIMIT 10
        """
        
        pod_logs = logs_client.query_logs(
            query=pod_query,
            limit=10,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(pod_logs)} logs using pod name query")
        print_logs(pod_logs)
        
        # Method 3: Try the neurons specific log partition
        logger.info("Method 3: Query logs from Neurons partitions")
        
        neurons_query = f"""
        SELECT timestamp, level, message, pod_name, kubernetes.pod.name, container_name, kubernetes.container.name
        FROM Log_Neurons_NVU
        WHERE pod_name = '{pod_name}' OR kubernetes.pod.name = '{pod_name}'
        SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
        UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
        LIMIT 10
        """
        
        neurons_logs = logs_client.query_logs(
            query=neurons_query,
            limit=10,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(neurons_logs)} logs from Neurons partition")
        print_logs(neurons_logs)
        
        # Method 4: Query error logs specifically
        logger.info("Method 4: Query error logs specifically")
        
        error_query = f"""
        SELECT timestamp, level, message, pod_name, kubernetes.pod.name
        FROM Log
        WHERE (pod_name = '{pod_name}' OR kubernetes.pod.name = '{pod_name}')
        AND (level = 'error' OR level = 'warning' OR level = 'critical')
        SINCE '{since_time.strftime("%Y-%m-%d %H:%M:%S")}' 
        UNTIL '{until_time.strftime("%Y-%m-%d %H:%M:%S")}'
        LIMIT 10
        """
        
        error_logs = logs_client.query_logs(
            query=error_query,
            limit=10,
            account_id=account_id
        )
        
        logger.info(f"Retrieved {len(error_logs)} error logs")
        print_logs(error_logs)
        
    except Exception as e:
        logger.error(f"Error retrieving logs: {str(e)}", exc_info=True)

def print_logs(logs):
    """Pretty print logs for readability."""
    if not logs:
        logger.info("No logs found")
        return
    
    logger.info(f"Found {len(logs)} logs:")
    
    for i, log in enumerate(logs):
        logger.info(f"Log {i+1}:")
        # Format the log as a pretty-printed JSON string
        formatted_log = json.dumps(log, indent=2, default=str)
        logger.info(formatted_log)
        logger.info("-" * 80)

if __name__ == "__main__":
    main() 