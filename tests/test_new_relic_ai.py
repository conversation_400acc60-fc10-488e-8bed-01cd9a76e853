import requests
# import browser_cookie3
import json
import re
import sys
import os
from rich.console import Console
from rich.table import Table
from rich import box
# from sseclient import SSEClient
from urllib.parse import urlencode
from rich.markdown import Markdown
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.syntax import Syntax
from rich.live import Live
from rich.text import Text

from dotenv import load_dotenv
import uuid
import shutil
import time

load_dotenv()

# Get environment variables
NEWRELIC_API_KEY = os.environ.get('NEWRELIC_API_KEY')
NEWRELIC_ACCOUNT_ID = os.environ.get('NEWRELIC_ACCOUNT_ID')

if not NEWRELIC_API_KEY:
    raise ValueError("NEWRELIC_API_KEY environment variable is required")

if not NEWRELIC_ACCOUNT_ID:
    raise ValueError("NEWRELIC_ACCOUNT_ID environment variable is required")

console = Console()

class NewRelicAI:
    def __init__(self):
        self.api_key = NEWRELIC_API_KEY
        self.account_id = NEWRELIC_ACCOUNT_ID
        self.nerdgraph_url = "https://api.newrelic.com/graphql"
        self.streaming_url = "https://nrai-streaming.service.newrelic.com/nrai-streaming"
        self.context_id = None
        self.thread_id = None
        
    def create_context(self, context_metadata=None):
        """Create a new context for conversation"""
        if not context_metadata:
            context_metadata = {}
            
        context_id = str(uuid.uuid4())
        
        query = """
        mutation CreateContext($contextMetadata: CollaborationRawContextMetadata!, $id: ID!) {
            collaborationCreateContext(
                contextMetadata: $contextMetadata, 
                id: $id
            ) {
                id
                creatorId
                latestThreadId
                latestThreadCommentId
                latestThreadCommentCreatorId
            }
        }
        """
        
        variables = {
            "contextMetadata": context_metadata,
            "id": context_id
        }
        
        response = self._execute_nerdgraph_query(query, variables)
        
        if response and "data" in response and "collaborationCreateContext" in response["data"]:
            self.context_id = context_id
            console.print(f"[green]Context created with ID: {context_id}[/green]")
            return context_id
        else:
            console.print("[red]Failed to create context[/red]")
            console.print(response)
            return None
    
    def create_thread(self, context_id=None, context_metadata=None, visibility="PRIVATE"):
        """Create a new thread in the given context"""
        if not context_id:
            if not self.context_id:
                console.print("[yellow]No context ID provided. Creating new context...[/yellow]")
                context_id = self.create_context()
                if not context_id:
                    return None
            else:
                context_id = self.context_id
        
        # Even more simplified query without visibility parameter
        query = """
        mutation {
            collaborationCreateThread(
                contextId: "%s"
            ) {
                id
            }
        }
        """ % context_id
        
        # No variables needed for this simplified approach
        console.print("[yellow]Attempting to create thread with minimal query...[/yellow]")
        response = self._execute_nerdgraph_query(query)
        
        # Check if response is valid and contains the expected data
        if response and "data" in response and "collaborationCreateThread" in response["data"]:
            thread_id = response["data"]["collaborationCreateThread"]["id"]
            self.thread_id = thread_id
            console.print(f"[green]Thread created with ID: {thread_id}[/green]")
            return thread_id
        else:
            # If the simplified query failed, return None
            console.print("[red]Failed to create thread with minimal query.[/red]")
            # Just create a UUID as fallback
            thread_id = str(uuid.uuid4())
            self.thread_id = thread_id
            console.print(f"[yellow]Using UUID as thread ID: {thread_id}[/yellow]")
            return thread_id
    
    def get_threads_by_context_id(self, context_id=None, first=10):
        """Get threads for a given context"""
        if not context_id:
            if not self.context_id:
                console.print("[red]No context ID provided or available[/red]")
                return None
            context_id = self.context_id
            
        # Fix hasPrevPage to hasPreviousPage and remove visibility parameter
        query = """
        query GetThreads($contextId: ID!, $first: Int!) {
            actor {
                collaboration {
                    threadsByContextId(
                        contextId: $contextId
                        first: $first
                    ) {
                        entities {
                            id
                            commentCount
                        }
                        hasNextPage
                        hasPreviousPage
                        totalCount
                    }
                }
            }
        }
        """
        
        variables = {
            "contextId": context_id,
            "first": first
        }
        
        response = self._execute_nerdgraph_query(query, variables)
        
        if response and "data" in response and "actor" in response["data"]:
            return response["data"]["actor"]["collaboration"]["threadsByContextId"]["entities"]
        else:
            console.print("[red]Failed to get threads[/red]")
            console.print(response)
            return None
    
    def get_comments_by_thread_id(self, thread_id=None, first=10):
        """Get comments for a given thread"""
        if not thread_id:
            if not self.thread_id:
                console.print("[red]No thread ID provided or available[/red]")
                return None
            thread_id = self.thread_id
            
        query = """
        query GetComments($threadId: ID!, $first: Int!) {
            actor {
                collaboration {
                    commentsByThreadId(
                        first: $first
                        threadId: $threadId
                    ) {
                        hasNextPage
                        hasPrevPage
                        totalCount
                        entities {
                            body
                            id
                            mentions {
                                mentionableItemId
                                type
                                id
                            }
                        }
                    }
                }
            }
        }
        """
        
        variables = {
            "threadId": thread_id,
            "first": first
        }
        
        response = self._execute_nerdgraph_query(query, variables)
        
        if response and "data" in response and "actor" in response["data"]:
            return response["data"]["actor"]["collaboration"]["commentsByThreadId"]["entities"]
        else:
            console.print("[red]Failed to get comments[/red]")
            console.print(response)
            return None
    
    def get_grok_messages_by_ids(self, message_ids):
        """Get Grok messages by their IDs"""
        if not message_ids:
            console.print("[red]No message IDs provided[/red]")
            return None
            
        query = """
        query GetGrokMessages($messageIds: [ID!]!) {
            actor {
                collaboration {
                    grokMessagesByIds(ids: $messageIds) {
                        id
                        card
                        content
                        role
                        creatorId
                        createdAt
                    }
                }
            }
        }
        """
        
        variables = {
            "messageIds": message_ids
        }
        
        response = self._execute_nerdgraph_query(query, variables)
        
        if response and "data" in response and "actor" in response["data"]:
            return response["data"]["actor"]["collaboration"]["grokMessagesByIds"]
        else:
            console.print("[red]Failed to get Grok messages[/red]")
            console.print(response)
            return None
    
    def send_streaming_request(self, message, thread_id=None, context_metadata=None, debug_raw=False):
        """Send a request to the streaming API"""
        if not thread_id:
            if not self.thread_id:
                console.print("[yellow]No thread ID provided. Creating new thread...[/yellow]")
                thread_id = self.create_thread()
                if not thread_id:
                    console.print("[yellow]Failed to create thread via API. Generating a UUID instead.[/yellow]")
                    thread_id = str(uuid.uuid4())
                    self.thread_id = thread_id
            else:
                thread_id = self.thread_id
        
        # Keep context metadata simple
        if not context_metadata:
            context_metadata = {
                "source": "cli_chat_interface"
            }
            
        headers = {
            'accept': 'application/json',
            'content-type': 'application/json',
            'X-Api-Key': self.api_key,
        }
        
        data = {
            "assistant": "grok",
            "body": message,
            "contextMetadata": context_metadata,
            "threadId": thread_id
        }

        try:
            console.print("[dim]Sending request to streaming API...[/dim]")
            
            response = requests.post(self.streaming_url, headers=headers, json=data, timeout=30)
            
            # Check the status code before processing response
            if response.status_code != 200:
                console.print(f"[red]Streaming API returned non-200 status code: {response.status_code}[/red]")
                console.print(f"[red]Response: {response.text[:200]}...[/red]")
                return None
                
            response.raise_for_status()
            
            # Save raw response for debugging if requested
            raw_response_text = response.text
            if debug_raw:
                # Save to file for detailed inspection
                debug_file = f"nrai_raw_response_{int(time.time())}.json"
                with open(debug_file, "w") as f:
                    f.write(raw_response_text)
                console.print(f"[yellow]Raw response saved to {debug_file}[/yellow]")
                
                # Print the content for immediate inspection
                console.print("[bold yellow]Raw response from NRAI:[/bold yellow]")
                console.print(f"Length: {len(raw_response_text)} characters")
                console.print(Panel(raw_response_text[:500], title="First 500 characters", border_style="yellow"))
                console.print(Panel(raw_response_text[-500:] if len(raw_response_text) > 500 else "", title="Last 500 characters", border_style="yellow"))
            
            # Try to parse the JSON response
            try:
                parsed_response = list(self._parse_multiple_json(raw_response_text))
                if not parsed_response:
                    console.print("[yellow]Warning: Parsed response is empty[/yellow]")
                return parsed_response
            except Exception as e:
                console.print(f"[red]Error parsing streaming response: {str(e)}[/red]")
                console.print(f"[red]Raw response first 200 chars: {raw_response_text[:200]}...[/red]")
                return None
                
        except requests.exceptions.JSONDecodeError:
            console.print("[red]Error: Received an invalid JSON response[/red]")
            console.print(f"Response first 200 chars: {response.text[:200]}...")
        except requests.exceptions.RequestException as e:
            console.print(f"[red]Request failed: {str(e)}[/red]")
            if 'response' in locals():
                console.print(f"Response first 200 chars: {response.text[:200]}...")
        return None
    
    def _execute_nerdgraph_query(self, query, variables=None):
        """Execute a NerdGraph GraphQL query"""
        headers = {
            'Content-Type': 'application/json',
            'API-Key': self.api_key,
        }
        
        data = {
            "query": query
        }
        
        if variables is not None:
            data["variables"] = variables
        
        # Debug output (simplified)
        if variables:
            console.print("[dim]Sending GraphQL query with variables...[/dim]")
        else:
            console.print("[dim]Sending GraphQL query...[/dim]")
        
        try:
            response = requests.post(self.nerdgraph_url, headers=headers, json=data)
            
            # Check for error in the response JSON even if status code is OK
            response_json = response.json()
            
            if "errors" in response_json:
                console.print("[red]GraphQL errors received:[/red]")
                for error in response_json["errors"]:
                    console.print(f"[red]Error: {error.get('message', 'Unknown error')}[/red]")
            
            response.raise_for_status()
            return response_json
            
        except requests.exceptions.HTTPError as e:
            console.print(f"[red]HTTP error occurred: {str(e)}[/red]")
            if 'response' in locals():
                status_code = response.status_code
                console.print(f"[red]Status code: {status_code}[/red]")
                try:
                    error_json = response.json()
                    console.print(f"[red]Response JSON: {json.dumps(error_json, indent=2)}[/red]")
                except:
                    console.print(f"[red]Response text: {response.text}[/red]")
            return None
        except requests.exceptions.RequestException as e:
            console.print(f"[red]Request error occurred: {str(e)}[/red]")
            if 'response' in locals():
                console.print(f"[red]Response content: {response.text}[/red]")
            return None
        except json.JSONDecodeError:
            console.print("[red]Error: Received an invalid JSON response from NerdGraph[/red]")
            if 'response' in locals():
                console.print(f"[red]Response content: {response.text}[/red]")
            return None
    
    def _parse_multiple_json(self, raw_data):
        """Parse multiple JSON objects in sequence"""
        raw_data = raw_data.strip()
        while raw_data:
            try:
                obj, idx = json.JSONDecoder().raw_decode(raw_data)
                yield obj
                raw_data = raw_data[idx:].strip()
            except json.JSONDecodeError as e:
                console.print(f"[red]Error decoding JSON: {e}[/red]")
                break
                
    def extract_message_content(self, response_data):
        """Extract content from response data, handling different message types."""
        result_content = ""
        final_card_data = None
        intermediate_content = ""
        stream_chunks = []
        
        # Log the full response structure for debugging
        console.print(f"[dim]Processing {len(response_data)} response items[/dim]")
        
        # First pass: find the final-message and get complete content
        for item in response_data:
            item_type = item.get("type")
            message = item.get("message", {})
            
            # Final messages contain the complete and definitive content
            if item_type == "final-message":
                final_content = message.get("content", "")
                if final_content:
                    # Use final message content as the definitive content
                    result_content = final_content
                    console.print(f"[dim]Found final message with {len(final_content)} characters[/dim]")
                
                # Get final card data if present
                if "card" in message and message["card"]:
                    final_card_data = message["card"]
                    
                # No need to process further after finding final message
                break
        
        # If no final message was found, collect content from other message types
        if not result_content:
            console.print("[yellow]No final message found, reconstructing from stream chunks and intermediates[/yellow]")
            
            # First collect all stream chunks to reconstruct the content
            for item in response_data:
                item_type = item.get("type")
                message = item.get("message", {})
                
                # Collect from intermediate messages
                if item_type == "intermediate":
                    content = message.get("content", "")
                    if content:
                        console.print(f"[dim]Found intermediate message: {len(content)} chars[/dim]")
                        intermediate_content = content
                
                # Collect all stream chunks
                elif item_type == "stream-chunk":
                    chunk_content = message.get("content", "")
                    if chunk_content:
                        stream_chunks.append(chunk_content)
                
                # Check for card data
                if "card" in message and message["card"]:
                    final_card_data = message["card"]
            
            # Merge all stream chunks into a single content
            if stream_chunks:
                result_content = "".join(stream_chunks)
                console.print(f"[dim]Reconstructed {len(result_content)} characters from {len(stream_chunks)} stream chunks[/dim]")
            
            # If we have intermediate content but no result content, use the intermediate
            if not result_content and intermediate_content:
                result_content = intermediate_content
                console.print(f"[dim]Using intermediate content: {len(intermediate_content)} chars[/dim]")
        
        # Display final content length for debugging
        console.print(f"[dim]Final content length: {len(result_content)} characters[/dim]")
        
        # If we actually found content, yield it
        if result_content:
            # Yield the final complete result
            yield {
                "type": "final",
                "content": result_content,
                "complete": True,
                "card": final_card_data
            }
        else:
            console.print("[red]Warning: Could not extract any content from response[/red]")
        
        # Check for mentions in comments (sometimes cards are embedded here)
        for item in response_data:
            if "comment" in item:
                comment = item["comment"]
                
                # Process mentions in comments
                if "mentions" in comment:
                    for mention in comment["mentions"]:
                        if "cardMention" in mention and mention["cardMention"]:
                            card_mention = mention["cardMention"]
                            if "card" in card_mention and card_mention["card"]:
                                # Found a card in a mention
                                card_data = card_mention["card"]
                                
                                # Yield as a card result only if it's different from the one we already found
                                if not final_card_data or card_data != final_card_data:
                                    yield {
                                        "type": "card_mention",
                                        "content": result_content,
                                        "complete": True,
                                        "card": card_data
                                    }

def display_chat_message(role, content):
    """Display a chat message with proper formatting"""
    if role == "user":
        console.print("\n[bold blue]You:[/bold blue]")
        console.print(content)
    elif role == "assistant" or role == "ai":
        console.print("\n[bold green]New Relic AI:[/bold green]")
        console.print(Markdown(content))

def display_code(code, language="sql"):
    """Display code with syntax highlighting"""
    syntax = Syntax(code, language, theme="monokai", line_numbers=True)
    console.print(Panel(syntax, border_style="yellow", expand=False))

def display_card_content(card):
    """Display card content based on its type."""
    if not card:
        return
    
    console.print("[bold purple]Card Content:[/bold purple]")
    
    # Get card type
    card_type = card.get("type", "")
    
    # Process SimpleCard type which may contain Visualizations and CodeBlocks
    if card_type == "SimpleCard" and "body" in card:
        # Track if we've already displayed an NRQL query to avoid duplicates
        nrql_displayed = False
        
        # First, try to find and display CodeBlock with NRQL (preferred)
        for body_item in card["body"]:
            item_type = body_item.get("type", "")
            
            # Extract code from CodeBlock element (may contain NRQL)
            if item_type == "CodeBlock":
                language = body_item.get("languageType", "")
                code = body_item.get("code", "")
                
                if code:
                    # If it's NRQL, use the NRQL formatting
                    if language.lower() == "nrql":
                        # Just print the NRQL query without duplicate headers
                        print_nrql_query(code)
                        nrql_displayed = True
                    else:
                        console.print(f"[bold cyan]Code Block ({language}):[/bold cyan]")
                        console.print(Syntax(code, language.lower(), theme="monokai"))
        
        # Only if we haven't found NRQL in CodeBlock, check Visualization
        if not nrql_displayed:
            for body_item in card["body"]:
                item_type = body_item.get("type", "")
                
                # Extract NRQL queries from Visualization element as fallback
                if item_type == "Visualization" and "nrqlQueries" in body_item:
                    for query in body_item["nrqlQueries"]:
                        query_text = query.get("query", "")
                        if query_text:
                            # Print without duplicate headers
                            print_nrql_query(query_text)
                            break  # Only show the first one to avoid duplicates
    
    # Process other card types (existing logic)
    elif "title" in card:
        console.print(f"[bold]Title:[/bold] {card['title']}")
    
    if "description" in card:
        console.print(f"[bold]Description:[/bold] {card['description']}")
    
    # Display code if present (existing logic)
    if "code" in card:
        console.print("[bold]Code:[/bold]")
        language = card.get("language", "python")
        console.print(Syntax(card["code"], language, theme="monokai"))

def print_nrql_query(query_text):
    """Helper function to print NRQL queries with consistent formatting"""
    # Get terminal width for optimal display
    terminal_width = shutil.get_terminal_size().columns
    display_width = max(min(terminal_width - 5, 120), 80)  # Between 80 and 120, based on terminal
    
    # Single consistent header for NRQL queries
    console.print("[bold cyan]NRQL Query:[/bold cyan]")
    
    # Check if query is multi-line or exceeds reasonable length
    if len(query_text) > 100 or '\n' in query_text:
        # For long or multi-line queries, use expanded display
        console.print(Panel(
            Syntax(query_text, "sql", theme="monokai", word_wrap=True),
            border_style="cyan",
            expand=True,
            width=display_width
        ))
    else:
        # For shorter queries, print them directly
        console.print(Syntax(query_text, "sql", theme="monokai"))

def stream_response(nr_ai, response_data, stream_live=True):
    """Stream and display the response from New Relic AI."""
    console.print("\n[bold green]New Relic AI:[/bold green]")
    
    live_display = None
    displayed_cards = set()  # Track displayed cards to prevent duplicates
    
    if stream_live:
        # Create a Live display for streaming output
        live_display = Live("", refresh_per_second=4, auto_refresh=True)
        live_display.start()
    
    try:
        # Process content from extract_message_content
        for result in nr_ai.extract_message_content(response_data):
            message_type = result.get("type", "")
            content = result.get("content", "")
            card_data = result.get("card")
            
            # Only display the content for the final message
            if message_type == "final":
                # Update live display with final content
                if stream_live and live_display and live_display.is_started:
                    live_display.update(Markdown(content))
                    live_display.stop()
                
                # Display the final content (no repeating the header)
                console.print(Markdown(content))
                
                # Display card data if available
                if card_data:
                    card_hash = hash(json.dumps(card_data, sort_keys=True))
                    if card_hash not in displayed_cards:
                        display_card_content(card_data)
                        displayed_cards.add(card_hash)
            
            # Handle additional card mentions separately
            elif message_type == "card_mention" and card_data:
                card_hash = hash(json.dumps(card_data, sort_keys=True))
                if card_hash not in displayed_cards:
                    console.print("\n[bold purple]Additional Information:[/bold purple]")
                    display_card_content(card_data)
                    displayed_cards.add(card_hash)
    
    except Exception as e:
        console.print(f"[bold red]Error streaming response: {str(e)}[/bold red]")
    finally:
        # Ensure the live display is stopped
        if stream_live and live_display and live_display.is_started:
            live_display.stop()

def debug_response_data(response_data):
    """Helper function to debug response data structure"""
    if not response_data:
        console.print("[yellow]No response data to debug[/yellow]")
        return
        
    console.print("[bold yellow]Debugging Response Data Structure:[/bold yellow]")
    
    # Create a debug file to log all response data
    debug_file = f"debug_response_{int(time.time())}.txt"
    with open(debug_file, "w") as f:
        f.write(f"Complete Response Data Debug\n{'='*50}\n")
        
        for idx, item in enumerate(response_data):
            f.write(f"\nItem {idx + 1}:\n")
            item_type = item.get("type", "Unknown")
            f.write(f"  Type: {item_type}\n")
            
            # Print to console
            console.print(f"[bold]Item {idx + 1}:[/bold]")
            console.print(f"  Type: {item_type}")
            
            if "message" in item:
                message = item["message"]
                role = message.get("role", "Unknown")
                
                # Console output
                console.print(f"  Message Role: {role}")
                
                # File output
                f.write(f"  Message Role: {role}\n")
                
                # Check for and log content
                if "content" in message:
                    content = message.get("content", "")
                    content_length = len(content)
                    
                    # Console info
                    console.print(f"  Content Length: {content_length} characters")
                    if content:
                        # Show first/last portion in console
                        console.print(f"  Content Preview: {content[:100]}..." if content_length > 100 else f"  Content: {content}")
                        
                    # Write full content to file
                    f.write(f"  Content Length: {content_length} characters\n")
                    f.write(f"  Content: {content}\n")
                
                # Check for card data
                if "card" in message and message["card"]:
                    card = message["card"]
                    card_type = card.get("type", "Unknown")
                    
                    # Console output
                    console.print(f"  Card Type: {card_type}")
                    
                    # File output
                    f.write(f"  Card Type: {card_type}\n")
                    
                    # Write full card data to file
                    f.write(f"  Full Card Data: {json.dumps(card, indent=2)}\n")
                    
                    if "body" in card:
                        console.print("  Card Body Items:")
                        f.write("  Card Body Items:\n")
                        
                        for body_idx, body_item in enumerate(card["body"]):
                            body_type = body_item.get("type", "Unknown")
                            console.print(f"    Body Item {body_idx + 1} Type: {body_type}")
                            f.write(f"    Body Item {body_idx + 1} Type: {body_type}\n")
                            
                            # Special handling for nrqlQueries
                            if "nrqlQueries" in body_item:
                                console.print("    Contains nrqlQueries:")
                                f.write("    Contains nrqlQueries:\n")
                                
                                for query_idx, query in enumerate(body_item["nrqlQueries"]):
                                    query_text = query.get("query", "No query")
                                    console.print(f"      Query {query_idx + 1}: {query_text[:50]}..." if len(query_text) > 50 else f"      Query {query_idx + 1}: {query_text}")
                                    f.write(f"      Query {query_idx + 1}: {query_text}\n")
                                    
                            # Special handling for CodeBlock
                            if body_item.get("type") == "CodeBlock":
                                language = body_item.get("languageType", "Unknown")
                                code = body_item.get("code", "No code")
                                console.print(f"    Language: {language}")
                                console.print(f"    Code: {code[:50]}..." if len(code) > 50 else f"    Code: {code}")
                                f.write(f"    Language: {language}\n")
                                f.write(f"    Code: {code}\n")
            
            # Check for comments
            if "comment" in item:
                comment = item["comment"]
                console.print("  Contains comment data")
                f.write("  Contains comment data\n")
                
                # Write the full comment data to the debug file
                f.write(f"  Full Comment Data: {json.dumps(comment, indent=2)}\n")
            
            console.print()  # Empty line for separation
    
    console.print(f"[green]Complete debug information saved to {debug_file}[/green]")

def main():
    try:
        # Initialize New Relic AI client
        nr_ai = NewRelicAI()
        
        # Create a new context for the conversation
        context_id = nr_ai.create_context({"source": "cli_chat_interface"})
        if not context_id:
            console.print("[bold red]Failed to create context. Exiting.[/bold red]")
            return
        
        # Try to get existing threads from the context
        try:
            existing_threads = nr_ai.get_threads_by_context_id(context_id)
        except Exception as e:
            console.print(f"[yellow]Error getting existing threads: {str(e)}. Will create new thread.[/yellow]")
            existing_threads = None
        
        thread_id = None
        if existing_threads and len(existing_threads) > 0:
            # Use the first existing thread
            thread_id = existing_threads[0]["id"]
            console.print(f"[green]Using existing thread: {thread_id}[/green]")
            nr_ai.thread_id = thread_id
        else:
            # Create a new thread for this conversation
            thread_id = nr_ai.create_thread(context_id)
            if not thread_id:
                # This shouldn't happen now due to fallback in create_thread
                console.print("[yellow]Thread creation failed completely. Using UUID.[/yellow]")
                thread_id = str(uuid.uuid4())
                nr_ai.thread_id = thread_id
        
        # Welcome message
        console.print(Panel.fit(
            "[bold blue]Welcome to the New Relic AI Chat Interface![/bold blue]\n"
            "Type [bold red]'exit'[/bold red] to end the conversation.\n"
            "Type [bold yellow]'debug'[/bold yellow] to see detailed response structure.\n"
            "Type [bold magenta]'rawdebug'[/bold magenta] to capture and save the complete raw API response.",
            box=box.DOUBLE
        ))
        
        # Always stream live by default
        stream_live = False
        
        # Main chat loop
        while True:
            message = Prompt.ask("\n[bold blue]You[/bold blue]")
            
            if message.lower() == 'exit':
                console.print("[bold red]Exiting the chat. Goodbye![/bold red]")
                break
                
            # Special command to toggle debug modes
            debug_mode = False
            debug_raw = False
            
            if message.lower() == 'debug':
                debug_mode = True
                message = Prompt.ask("[bold yellow]Debug mode ON. Enter your query[/bold yellow]")
            elif message.lower() == 'rawdebug':
                debug_raw = True
                debug_mode = True 
                message = Prompt.ask("[bold magenta]Raw Debug mode ON. Enter your query[/bold magenta]")
            
            # Display user message
            display_chat_message("user", message)
            
            # Send request to New Relic AI and get response
            with console.status("[bold green]New Relic AI is thinking...[/bold green]"):
                response_data = nr_ai.send_streaming_request(message, debug_raw=debug_raw)
            
            if response_data is None:
                console.print("[bold red]Failed to get response from New Relic AI. Please try again.[/bold red]")
                continue
                
            # Debug the response structure if in debug mode
            if debug_mode:
                debug_response_data(response_data)
            
            # Stream and display the response
            stream_response(nr_ai, response_data, stream_live)

    except KeyboardInterrupt:
        console.print("[bold yellow]Interrupted by user. Exiting...[/bold yellow]")
    except Exception as e:
        console.print(f"[bold red]An unexpected error occurred: {str(e)}[/bold red]")
        import traceback
        console.print(Syntax(traceback.format_exc(), "python", theme="monokai"))

if __name__ == "__main__":
    main()