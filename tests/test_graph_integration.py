"""
Comprehensive test suite for graph-based entity relationship and cascading failure detection.

This test suite validates the functionality of the graph service, algorithms,
and integration with the existing entity relationship system.
"""

import pytest
import asyncio
import networkx as nx
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timezone
from typing import Dict, List, Any

from ai_incident_manager.services.graph_service import GraphService, get_graph_service
from ai_incident_manager.services.graph_integration import GraphIntegrationService, get_graph_integration_service
from ai_incident_manager.utils.graph_algorithms import GraphAlgorithms, get_graph_algorithms
from ai_incident_manager.models.graph_models import (
    GraphNodeModel, GraphEdgeModel, NodeTypeEnum, RelationshipTypeEnum,
    CascadingFailureAnalysisModel, GraphAnalysisConfig
)
from ai_incident_manager.models.workflow_state import IncidentState


class TestGraphService:
    """Test suite for GraphService functionality."""
    
    @pytest.fixture
    def graph_service(self):
        """Create a GraphService instance for testing."""
        return GraphService()
    
    @pytest.fixture
    def sample_graph(self):
        """Create a sample graph for testing."""
        G = nx.DiGraph()
        
        # Add nodes
        G.add_node("pod1", name="web-pod-1", node_type=NodeTypeEnum.KUBERNETES_POD, 
                   criticality_score=0.7, failure_probability=0.2)
        G.add_node("pod2", name="web-pod-2", node_type=NodeTypeEnum.KUBERNETES_POD, 
                   criticality_score=0.6, failure_probability=0.1)
        G.add_node("node1", name="worker-node-1", node_type=NodeTypeEnum.KUBERNETES_NODE, 
                   criticality_score=0.9, failure_probability=0.05)
        G.add_node("deploy1", name="web-deployment", node_type=NodeTypeEnum.KUBERNETES_DEPLOYMENT, 
                   criticality_score=0.8, failure_probability=0.1)
        
        # Add edges
        G.add_edge("pod1", "node1", relationship_type=RelationshipTypeEnum.RUNS_ON, 
                   weight=1.5, failure_propagation_probability=0.8)
        G.add_edge("pod2", "node1", relationship_type=RelationshipTypeEnum.RUNS_ON, 
                   weight=1.5, failure_propagation_probability=0.8)
        G.add_edge("deploy1", "pod1", relationship_type=RelationshipTypeEnum.MANAGES, 
                   weight=1.2, failure_propagation_probability=0.6)
        G.add_edge("deploy1", "pod2", relationship_type=RelationshipTypeEnum.MANAGES, 
                   weight=1.2, failure_propagation_probability=0.6)
        
        return G
    
    @pytest.fixture
    def mock_relationship_service(self):
        """Create a mock relationship service."""
        mock_service = Mock()
        mock_service.traverse_relationships.return_value = {
            "primary_entity": {
                "entityGuid": "test-guid",
                "entityType": "KUBERNETES_POD",
                "entityName": "test-pod"
            },
            "related_entities": [
                {
                    "entityGuid": "node-guid",
                    "entityType": "KUBERNETES_NODE",
                    "entityName": "test-node",
                    "relationship": "RUNS_ON"
                }
            ]
        }
        return mock_service
    
    def test_graph_service_initialization(self, graph_service):
        """Test GraphService initialization."""
        assert graph_service is not None
        assert graph_service.cache_timeout == 300
        assert graph_service.default_failure_probability == 0.1
        assert graph_service.default_propagation_probability == 0.5
    
    def test_analyze_cascading_failures(self, graph_service, sample_graph):
        """Test cascading failure analysis."""
        # Test with pod1 as primary failing entity
        analysis = graph_service.analyze_cascading_failures(
            graph=sample_graph,
            primary_entity_guid="pod1",
            failure_threshold=0.1,
            propagation_decay=0.8,
            max_hops=3
        )
        
        assert analysis.primary_entity == "pod1"
        assert isinstance(analysis.affected_entities, list)
        assert isinstance(analysis.failure_paths, list)
        assert isinstance(analysis.risk_scores, dict)
        assert analysis.potential_blast_radius >= 0
        assert analysis.estimated_impact_score >= 0.0
        assert isinstance(analysis.recommended_actions, list)
        
        # Should find the node as affected due to high propagation probability
        assert "node1" in analysis.affected_entities or len(analysis.affected_entities) > 0
    
    def test_find_critical_paths(self, graph_service, sample_graph):
        """Test critical path identification."""
        critical_paths = graph_service.find_critical_paths(
            graph=sample_graph,
            source="deploy1",
            target="node1"
        )
        
        assert isinstance(critical_paths, list)
        
        if critical_paths:
            path = critical_paths[0]
            assert hasattr(path, 'source')
            assert hasattr(path, 'target')
            assert hasattr(path, 'shortest_path')
            assert path.source == "deploy1"
            assert path.target == "node1"
            assert isinstance(path.shortest_path, list)
    
    def test_calculate_graph_metrics(self, graph_service, sample_graph):
        """Test graph metrics calculation."""
        metrics = graph_service.calculate_graph_metrics(sample_graph)
        
        assert metrics.node_count == 4
        assert metrics.edge_count == 4
        assert metrics.density > 0.0
        assert isinstance(metrics.centrality_metrics, dict)
        assert 'degree' in metrics.centrality_metrics
        assert 'betweenness' in metrics.centrality_metrics
    
    def test_find_entities_by_criteria(self, graph_service, sample_graph):
        """Test entity search by criteria."""
        # Find high criticality entities
        high_criticality = graph_service.find_entities_by_criteria(
            graph=sample_graph,
            min_criticality=0.8
        )
        
        assert isinstance(high_criticality, list)
        assert "node1" in high_criticality  # Should find the node with 0.9 criticality
        assert "deploy1" in high_criticality  # Should find the deployment with 0.8 criticality
        
        # Find by node type
        pods = graph_service.find_entities_by_criteria(
            graph=sample_graph,
            node_type=NodeTypeEnum.KUBERNETES_POD
        )
        
        assert isinstance(pods, list)
        assert "pod1" in pods
        assert "pod2" in pods
    
    @pytest.mark.asyncio
    async def test_build_entity_graph(self, graph_service):
        """Test entity graph building."""
        # Mock the relationship service
        with patch('ai_incident_manager.services.graph_service.get_entity_relationship_service') as mock_get_service:
            mock_service = Mock()
            mock_service.traverse_relationships.return_value = {
                "primary_entity": {
                    "entityGuid": "test-guid",
                    "entityType": "KUBERNETES_POD",
                    "entityName": "test-pod"
                },
                "related_entities": [
                    {
                        "entityGuid": "node-guid",
                        "entityType": "KUBERNETES_NODE",
                        "entityName": "test-node",
                        "relationship": "RUNS_ON"
                    }
                ]
            }
            mock_get_service.return_value = mock_service
            
            # Build graph
            graph = await graph_service.build_entity_graph(
                primary_entity_guid="test-guid",
                entity_type="KUBERNETES_POD",
                entity_name="test-pod",
                alert_category="kubernetes_crashloopbackoff",
                max_depth=2
            )
            
            assert isinstance(graph, nx.DiGraph)
            assert graph.number_of_nodes() >= 1
            assert "test-guid" in graph.nodes()


class TestGraphAlgorithms:
    """Test suite for GraphAlgorithms functionality."""
    
    @pytest.fixture
    def graph_algorithms(self):
        """Create a GraphAlgorithms instance for testing."""
        return GraphAlgorithms()
    
    @pytest.fixture
    def complex_graph(self):
        """Create a more complex graph for algorithm testing."""
        G = nx.DiGraph()
        
        # Create a more complex topology
        nodes = [
            ("app1", {"node_type": NodeTypeEnum.APPLICATION, "criticality_score": 0.9, "failure_probability": 0.1}),
            ("db1", {"node_type": NodeTypeEnum.DATABASE, "criticality_score": 0.95, "failure_probability": 0.05}),
            ("cache1", {"node_type": NodeTypeEnum.APPLICATION, "criticality_score": 0.7, "failure_probability": 0.15}),
            ("queue1", {"node_type": NodeTypeEnum.KAFKA, "criticality_score": 0.8, "failure_probability": 0.1}),
            ("worker1", {"node_type": NodeTypeEnum.KUBERNETES_POD, "criticality_score": 0.6, "failure_probability": 0.2}),
            ("worker2", {"node_type": NodeTypeEnum.KUBERNETES_POD, "criticality_score": 0.6, "failure_probability": 0.2}),
            ("lb1", {"node_type": NodeTypeEnum.APPLICATION, "criticality_score": 0.85, "failure_probability": 0.05})
        ]
        
        for node_id, attrs in nodes:
            G.add_node(node_id, name=node_id, **attrs)
        
        # Create edges representing dependencies
        edges = [
            ("app1", "db1", {"relationship_type": RelationshipTypeEnum.DEPENDS_ON, "weight": 2.0, "failure_propagation_probability": 0.9}),
            ("app1", "cache1", {"relationship_type": RelationshipTypeEnum.DEPENDS_ON, "weight": 1.5, "failure_propagation_probability": 0.7}),
            ("app1", "queue1", {"relationship_type": RelationshipTypeEnum.COMMUNICATES_WITH, "weight": 1.3, "failure_propagation_probability": 0.6}),
            ("worker1", "queue1", {"relationship_type": RelationshipTypeEnum.DEPENDS_ON, "weight": 1.8, "failure_propagation_probability": 0.8}),
            ("worker2", "queue1", {"relationship_type": RelationshipTypeEnum.DEPENDS_ON, "weight": 1.8, "failure_propagation_probability": 0.8}),
            ("lb1", "app1", {"relationship_type": RelationshipTypeEnum.MANAGES, "weight": 1.4, "failure_propagation_probability": 0.7})
        ]
        
        for source, target, attrs in edges:
            G.add_edge(source, target, **attrs)
        
        return G
    
    def test_simulate_failure_propagation(self, graph_algorithms, complex_graph):
        """Test failure propagation simulation."""
        results = graph_algorithms.simulate_failure_propagation(
            graph=complex_graph,
            initial_failures=["db1"],
            propagation_steps=5,
            threshold=0.1
        )
        
        assert isinstance(results, dict)
        assert "db1" in results  # Initial failure should be included
        
        # Check that app1 is affected due to high dependency
        if "app1" in results:
            result = results["app1"]
            assert result.failure_probability > 0.1
            assert result.propagation_time > 0
            assert isinstance(result.source_path, list)
            assert isinstance(result.contributing_factors, list)
    
    def test_analyze_criticality(self, graph_algorithms, complex_graph):
        """Test criticality analysis."""
        results = graph_algorithms.analyze_criticality(
            graph=complex_graph,
            include_centrality=True
        )
        
        assert isinstance(results, dict)
        assert len(results) == complex_graph.number_of_nodes()
        
        # Check that database has high criticality
        if "db1" in results:
            db_result = results["db1"]
            assert db_result.criticality_score > 0.8
            assert isinstance(db_result.centrality_scores, dict)
            assert db_result.dependency_count >= 0
            assert db_result.dependent_count >= 0
    
    def test_find_critical_paths_advanced(self, graph_algorithms, complex_graph):
        """Test advanced critical path finding."""
        critical_paths = graph_algorithms.find_critical_paths_advanced(
            graph=complex_graph,
            source="lb1",
            target="db1",
            max_paths=5
        )
        
        assert isinstance(critical_paths, list)
        
        if critical_paths:
            path = critical_paths[0]
            assert hasattr(path, 'source')
            assert hasattr(path, 'target')
            assert hasattr(path, 'paths')
            assert path.source == "lb1"
            assert path.target == "db1"
    
    def test_detect_graph_clusters(self, graph_algorithms, complex_graph):
        """Test graph clustering detection."""
        cluster_result = graph_algorithms.detect_graph_clusters(
            graph=complex_graph,
            method='spectral'
        )
        
        assert isinstance(cluster_result.clusters, dict)
        assert isinstance(cluster_result.cluster_centralities, dict)
        assert isinstance(cluster_result.inter_cluster_edges, list)
        assert cluster_result.modularity >= 0.0
        assert -1.0 <= cluster_result.silhouette_score <= 1.0
    
    def test_analyze_graph_resilience(self, graph_algorithms, complex_graph):
        """Test graph resilience analysis."""
        resilience_data = graph_algorithms.analyze_graph_resilience(
            graph=complex_graph,
            removal_strategy='targeted',
            num_removals=2
        )
        
        assert isinstance(resilience_data, dict)
        assert 'resilience_score' in resilience_data
        assert 'removal_impact' in resilience_data
        assert 'original_components' in resilience_data
        assert 'original_largest_component' in resilience_data
        assert 0.0 <= resilience_data['resilience_score'] <= 1.0
    
    def test_find_bottlenecks(self, graph_algorithms, complex_graph):
        """Test bottleneck identification."""
        bottlenecks = graph_algorithms.find_bottlenecks(
            graph=complex_graph,
            flow_threshold=0.5
        )
        
        assert isinstance(bottlenecks, list)
        
        if bottlenecks:
            bottleneck = bottlenecks[0]
            assert 'node' in bottleneck
            assert 'bottleneck_score' in bottleneck
            assert 'betweenness_centrality' in bottleneck
            assert 'flow_centrality' in bottleneck
            assert bottleneck['bottleneck_score'] >= 0.5


class TestGraphIntegration:
    """Test suite for GraphIntegrationService functionality."""
    
    @pytest.fixture
    def integration_service(self):
        """Create a GraphIntegrationService instance for testing."""
        return GraphIntegrationService()
    
    @pytest.fixture
    def sample_config(self):
        """Create sample configuration for testing."""
        return {
            'graph_analysis': {
                'default_criticality_score': 0.5,
                'default_failure_probability': 0.1,
                'default_propagation_probability': 0.5,
                'critical_path_threshold': 0.7,
                'failure_threshold': 0.1,
                'propagation_decay': 0.8,
                'max_hops': 5,
                'max_graph_depth': 3
            },
            'entity_types': {
                'KUBERNETES_POD': {
                    'default_criticality': 0.6,
                    'default_failure_probability': 0.2,
                    'relationships': []
                }
            },
            'alert_categories': {
                'kubernetes_crashloopbackoff': {
                    'failure_characteristics': {
                        'initial_failure_probability': 0.9,
                        'time_to_propagate': 300,
                        'blast_radius_multiplier': 1.2,
                        'mitigation_strategies': ['increase_resources', 'check_logs']
                    }
                }
            },
            'graph_analysis_rules': {
                'alert_enrichment': {
                    'urgency_scoring_factors': {
                        'blast_radius': 0.2,
                        'recovery_time': 0.15,
                        'relationship_strength': 0.15
                    }
                }
            }
        }
    
    def test_integration_service_initialization(self, integration_service):
        """Test GraphIntegrationService initialization."""
        assert integration_service is not None
        assert integration_service.graph_service is not None
        assert integration_service.relationship_service is not None
        assert integration_service.graph_algorithms is not None
    
    def test_get_graph_analysis_config(self, integration_service):
        """Test getting graph analysis configuration."""
        config = integration_service.get_graph_analysis_config()
        
        assert isinstance(config, GraphAnalysisConfig)
        assert config.max_depth > 0
        assert 0.0 <= config.failure_threshold <= 1.0
        assert 0.0 <= config.propagation_decay <= 1.0
        assert config.max_hops > 0
    
    def test_get_entity_config(self, integration_service):
        """Test getting entity configuration."""
        with patch.object(integration_service, 'config', {
            'entity_types': {
                'KUBERNETES_POD': {
                    'default_criticality': 0.6,
                    'default_failure_probability': 0.2
                }
            }
        }):
            config = integration_service.get_entity_config('KUBERNETES_POD')
            
            assert isinstance(config, dict)
            assert config.get('default_criticality') == 0.6
            assert config.get('default_failure_probability') == 0.2
    
    def test_get_failure_characteristics(self, integration_service):
        """Test getting failure characteristics."""
        with patch.object(integration_service, 'config', {
            'alert_categories': {
                'kubernetes_crashloopbackoff': {
                    'failure_characteristics': {
                        'initial_failure_probability': 0.9,
                        'time_to_propagate': 300
                    }
                }
            }
        }):
            characteristics = integration_service.get_failure_characteristics('kubernetes_crashloopbackoff')
            
            assert isinstance(characteristics, dict)
            assert characteristics.get('initial_failure_probability') == 0.9
            assert characteristics.get('time_to_propagate') == 300
    
    def test_enhance_alert_with_graph_context(self, integration_service):
        """Test alert enhancement with graph context."""
        alert_data = {
            'title': 'Test Alert',
            'severity': 'CRITICAL',
            'entity_guid': 'test-guid'
        }
        
        graph_analysis = {
            'cascading_failure': {
                'potential_blast_radius': 5,
                'estimated_impact_score': 0.8,
                'affected_entities': ['entity1', 'entity2'],
                'critical_dependencies': ['dep1']
            },
            'metrics': {
                'node_count': 10,
                'edge_count': 15,
                'density': 0.3,
                'diameter': 4
            }
        }
        
        enhanced = integration_service.enhance_alert_with_graph_context(alert_data, graph_analysis)
        
        assert 'enrichment' in enhanced
        assert 'graph_enrichment' in enhanced['enrichment']
        assert 'cascading_risk' in enhanced['enrichment']['graph_enrichment']
        assert 'topology_health' in enhanced['enrichment']['graph_enrichment']
        assert 'urgency_score' in enhanced['enrichment']['graph_enrichment']
        assert 'recommendations' in enhanced['enrichment']['graph_enrichment']
    
    def test_get_monitoring_recommendations(self, integration_service):
        """Test getting monitoring recommendations."""
        with patch.object(integration_service, 'config', {
            'alert_categories': {
                'kubernetes_crashloopbackoff': {
                    'failure_characteristics': {
                        'mitigation_strategies': ['increase_resources', 'check_logs']
                    }
                }
            }
        }):
            graph_analysis = {
                'cascading_failure': {
                    'critical_dependencies': ['dep1', 'dep2'],
                    'affected_entities': ['entity1']
                },
                'bottlenecks': [
                    {'node': 'bottleneck1', 'bottleneck_score': 0.8}
                ]
            }
            
            recommendations = integration_service.get_monitoring_recommendations(
                'kubernetes_crashloopbackoff', graph_analysis
            )
            
            assert isinstance(recommendations, list)
            assert len(recommendations) > 0
            
            # Check that we have mitigation recommendations
            mitigation_recs = [r for r in recommendations if r.get('type') == 'mitigation']
            assert len(mitigation_recs) > 0
    
    @pytest.mark.asyncio
    async def test_enhanced_relationship_traversal(self, integration_service):
        """Test enhanced relationship traversal."""
        # Mock the relationship service
        with patch.object(integration_service, 'relationship_service') as mock_rel_service:
            mock_rel_service.traverse_relationships.return_value = {
                'primary_entity': {
                    'entityGuid': 'test-guid',
                    'entityType': 'KUBERNETES_POD',
                    'entityName': 'test-pod'
                },
                'related_entities': []
            }
            
            # Mock the graph service
            with patch.object(integration_service, 'graph_service') as mock_graph_service:
                mock_graph = nx.DiGraph()
                mock_graph.add_node('test-guid', name='test-pod')
                mock_graph_service.build_entity_graph.return_value = mock_graph
                mock_graph_service.calculate_graph_metrics.return_value = Mock(
                    node_count=1, edge_count=0, density=0.0, average_clustering=0.0, diameter=0
                )
                mock_graph_service.analyze_cascading_failures.return_value = Mock(
                    affected_entities=[], critical_dependencies=[], potential_blast_radius=0,
                    estimated_impact_score=0.0, failure_paths=[]
                )
                mock_graph_service.find_critical_paths.return_value = []
                
                # Mock the graph algorithms
                with patch.object(integration_service, 'graph_algorithms') as mock_algorithms:
                    mock_algorithms.find_bottlenecks.return_value = []
                    mock_algorithms.analyze_graph_resilience.return_value = {
                        'resilience_score': 1.0,
                        'removal_impact': []
                    }
                    
                    result = await integration_service.enhanced_relationship_traversal(
                        entity_guid='test-guid',
                        entity_type='KUBERNETES_POD',
                        entity_name='test-pod',
                        alert_category='kubernetes_crashloopbackoff'
                    )
                    
                    assert isinstance(result, dict)
                    assert 'primary_entity' in result
                    assert 'related_entities' in result
                    assert 'graph_analysis' in result


class TestWorkflowIntegration:
    """Test suite for workflow integration."""
    
    @pytest.fixture
    def sample_incident_state(self):
        """Create a sample incident state for testing."""
        return IncidentState(
            incident_id="test-incident-001",
            raw_alert={
                "issueId": "test-issue-001",
                "title": "Test Alert",
                "priority": "CRITICAL"
            },
            title="Test Alert",
            description="Test alert description",
            severity="CRITICAL",
            start_time=datetime.now(timezone.utc).isoformat(),
            alert_category="kubernetes_crashloopbackoff",
            alert_runbook="Test runbook",
            alert_title="Test Alert",
            condition_name="Test Condition",
            current_phase="graph_analysis",
            entities=[{
                "entity_guid": "test-guid",
                "entity_name": "test-pod",
                "entity_type": "KUBERNETES_POD",
                "is_primary": True
            }],
            since_time="2023-08-01T00:00:00Z",
            until_time="2023-08-01T01:00:00Z"
        )
    
    @pytest.mark.asyncio
    async def test_graph_analysis_workflow_node(self, sample_incident_state):
        """Test the graph analysis workflow node."""
        from ai_incident_manager.workflow.nodes.graph_analysis import graph_analysis_node
        
        # Mock the agent and its dependencies
        with patch('ai_incident_manager.workflow.nodes.graph_analysis.cascading_failure_agent') as mock_agent:
            mock_result = Mock()
            mock_result.data = Mock()
            mock_result.data.analysis_summary = "Test analysis summary"
            mock_result.data.risk_assessment = "High risk"
            mock_result.data.critical_insights = ["Insight 1", "Insight 2"]
            mock_result.data.recommended_actions = ["Action 1", "Action 2"]
            mock_result.data.entities_to_monitor = ["entity1", "entity2"]
            mock_result.data.estimated_resolution_time = "30 minutes"
            mock_result.data.confidence_score = 0.85
            mock_result.data.analysis_methodology = "Graph-based analysis"
            mock_result.data.limitations = ["Limitation 1"]
            mock_result.data.cascading_failure_analysis = Mock()
            mock_result.data.cascading_failure_analysis.primary_entity = "test-guid"
            mock_result.data.cascading_failure_analysis.affected_entities = ["entity1"]
            mock_result.data.cascading_failure_analysis.critical_dependencies = ["dep1"]
            mock_result.data.cascading_failure_analysis.potential_blast_radius = 3
            mock_result.data.cascading_failure_analysis.estimated_impact_score = 0.7
            mock_result.data.cascading_failure_analysis.failure_paths = []
            
            mock_agent.run.return_value = mock_result
            
            # Mock the workflow dependencies
            with patch('ai_incident_manager.workflow.nodes.graph_analysis.get_openai_client') as mock_openai:
                with patch('ai_incident_manager.workflow.nodes.graph_analysis.get_query_client') as mock_query:
                    with patch('ai_incident_manager.workflow.nodes.graph_analysis.store_workflow_result') as mock_store:
                        mock_openai.return_value = Mock()
                        mock_query.return_value = Mock()
                        mock_store.return_value = None
                        
                        # Run the workflow node
                        result_state = await graph_analysis_node(sample_incident_state)
                        
                        assert result_state is not None
                        assert hasattr(result_state, 'graph_analysis')
                        assert result_state.graph_analysis is not None
                        assert 'agent_analysis' in result_state.graph_analysis
                        assert result_state.graph_analysis['agent_analysis']['analysis_summary'] == "Test analysis summary"


# Integration tests
class TestEndToEndIntegration:
    """End-to-end integration tests."""
    
    @pytest.mark.asyncio
    async def test_full_graph_analysis_pipeline(self):
        """Test the full graph analysis pipeline."""
        # This test would run the complete pipeline from entity discovery
        # through graph analysis to recommendations
        
        # Mock the external dependencies
        with patch('ai_incident_manager.services.graph_service.get_entity_relationship_service') as mock_rel_service:
            mock_rel_service.return_value.traverse_relationships.return_value = {
                'primary_entity': {
                    'entityGuid': 'test-guid',
                    'entityType': 'KUBERNETES_POD',
                    'entityName': 'test-pod'
                },
                'related_entities': [
                    {
                        'entityGuid': 'node-guid',
                        'entityType': 'KUBERNETES_NODE',
                        'entityName': 'test-node',
                        'relationship': 'RUNS_ON'
                    }
                ]
            }
            
            # Get services
            graph_service = get_graph_service()
            integration_service = get_graph_integration_service()
            
            # Build graph
            graph = await graph_service.build_entity_graph(
                primary_entity_guid='test-guid',
                entity_type='KUBERNETES_POD',
                entity_name='test-pod',
                alert_category='kubernetes_crashloopbackoff',
                max_depth=2
            )
            
            assert isinstance(graph, nx.DiGraph)
            assert graph.number_of_nodes() >= 1
            
            # Analyze cascading failures
            cascading_analysis = graph_service.analyze_cascading_failures(
                graph=graph,
                primary_entity_guid='test-guid',
                failure_threshold=0.1
            )
            
            assert cascading_analysis is not None
            assert cascading_analysis.primary_entity == 'test-guid'
            
            # Get recommendations
            recommendations = integration_service.get_monitoring_recommendations(
                'kubernetes_crashloopbackoff',
                {
                    'cascading_failure': {
                        'critical_dependencies': ['dep1'],
                        'affected_entities': ['entity1']
                    }
                }
            )
            
            assert isinstance(recommendations, list)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])