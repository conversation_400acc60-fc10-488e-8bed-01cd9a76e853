import pytest
from unittest.mock import patch, MagicMock
import json
from datetime import datetime, timedelta
import os

from agents.agent_functions import (
    fetch_entity_metrics,
    fetch_newrelic_metrics,
    fetch_kubernetes_metrics,
    fetch_azure_metrics,
    fetch_kubernetes_pod_metrics,
    fetch_kubernetes_node_metrics
)

# Test data fixtures
@pytest.fixture
def mock_env_variables():
    return {
        "NEWRELIC_API_KEY": "test-api-key",
        "NEWRELIC_ACCOUNT_ID": "test-account-id",
        "AZURE_SUBSCRIPTION_ID": "test-subscription",
        "AZURE_TENANT_ID": "test-tenant",
        "AZURE_CLIENT_ID": "test-client",
        "AZURE_CLIENT_SECRET": "test-secret"
    }

@pytest.fixture
def sample_metrics_response():
    return {
        "data": {
            "actor": {
                "entity": {
                    "type": "APPLICATION"
                },
                "account": {
                    "nrql": {
                        "results": [
                            {
                                "timestamp": **********,
                                "Throughput": 100,
                                "Error Rate": 0.5
                            }
                        ]
                    }
                }
            }
        }
    }

# Tests for fetch_entity_metrics
def test_fetch_entity_metrics_success(mock_env_variables, sample_metrics_response):
    with patch('requests.post') as mock_post:
        # Configure mock response
        mock_post.return_value.status_code = 200
        mock_post.return_value.json.return_value = sample_metrics_response
        
        # Test parameters
        entity_id = "test-entity-id"
        start_time = "*************"
        end_time = "*************"
        
        # Execute function
        result = fetch_entity_metrics(entity_id, start_time, end_time)
        result_dict = json.loads(result)
        
        # Assertions
        assert isinstance(result_dict, dict)
        assert result_dict['entity_id'] == entity_id
        assert result_dict['entity_type'] == "APPLICATION"
        assert 'metrics' in result_dict
        assert 'time_window' in result_dict

def test_fetch_entity_metrics_invalid_input():
    with pytest.raises(ValueError):
        fetch_entity_metrics("", "*************", "*************")

# Tests for fetch_newrelic_metrics
def test_fetch_newrelic_metrics_success(mock_env_variables):
    with patch('data_processor.lib.data_factory.DataFactory.get_new_relic_client') as mock_client:
        # Configure mock client
        mock_client.return_value.get_entity_metrics.return_value = {
            "metrics": {
                "cpu": [{"timestamp": "2023-01-01T00:00:00Z", "value": 50}],
                "memory": [{"timestamp": "2023-01-01T00:00:00Z", "value": 75}]
            }
        }
        
        # Test parameters
        entity_id = "test-entity-id"
        start_time = "*************"
        end_time = "*************"
        
        # Execute function
        result = fetch_newrelic_metrics(entity_id, start_time, end_time)
        result_dict = json.loads(result)
        
        # Assertions
        assert isinstance(result_dict, dict)
        assert "metrics" in result_dict
        assert "cpu" in result_dict["metrics"]
        assert "memory" in result_dict["metrics"]

# Tests for fetch_kubernetes_metrics
def test_fetch_kubernetes_metrics_success(mock_env_variables):
    with patch('data_processor.lib.data_factory.DataFactory.get_kubernetes_client') as mock_client:
        # Configure mock client
        mock_client.return_value.get_pod_metrics.return_value = {
            "pod_metrics": {
                "cpu_usage": [{"timestamp": "2023-01-01T00:00:00Z", "value": 0.5}],
                "memory_usage": [{"timestamp": "2023-01-01T00:00:00Z", "value": 1024}]
            }
        }
        
        # Test parameters
        namespace = "test-namespace"
        pod_name = "test-pod"
        start_time = "2023-01-01T00:00:00Z"
        end_time = "2023-01-01T01:00:00Z"
        
        # Execute function
        result = fetch_kubernetes_metrics(namespace, pod_name, start_time, end_time)
        result_dict = json.loads(result)
        
        # Assertions
        assert isinstance(result_dict, dict)
        assert "pod_metrics" in result_dict
        assert "cpu_usage" in result_dict["pod_metrics"]
        assert "memory_usage" in result_dict["pod_metrics"]

# Tests for fetch_kubernetes_pod_metrics
def test_fetch_kubernetes_pod_metrics_success(mock_env_variables):
    with patch('data_processor.lib.kubernetes_metrics.KubernetesMetrics') as MockKubernetesMetrics:
        # Configure mock
        mock_instance = MockKubernetesMetrics.return_value
        mock_instance.get_pod_metrics.return_value = {
            "pod": "test-pod",
            "metrics": {
                "cpu": [{"timestamp": "2023-01-01T00:00:00Z", "value": 0.5}],
                "memory": [{"timestamp": "2023-01-01T00:00:00Z", "value": 1024}]
            }
        }
        
        # Test parameters
        namespace = "test-namespace"
        pod_name = "test-pod"
        start_time = "2023-01-01T00:00:00Z"
        end_time = "2023-01-01T01:00:00Z"
        
        # Execute function
        result = fetch_kubernetes_pod_metrics(namespace, pod_name, start_time, end_time)
        result_dict = json.loads(result)
        
        # Assertions
        assert isinstance(result_dict, dict)
        assert "pod" in result_dict
        assert "metrics" in result_dict
        assert "cpu" in result_dict["metrics"]
        assert "memory" in result_dict["metrics"]

# Tests for fetch_kubernetes_node_metrics
def test_fetch_kubernetes_node_metrics_success(mock_env_variables):
    with patch('data_processor.lib.kubernetes_metrics.KubernetesMetrics') as MockKubernetesMetrics:
        # Configure mock
        mock_instance = MockKubernetesMetrics.return_value
        mock_instance.get_node_metrics.return_value = {
            "node": "test-node",
            "metrics": {
                "cpu": [{"timestamp": "2023-01-01T00:00:00Z", "value": 0.5}],
                "memory": [{"timestamp": "2023-01-01T00:00:00Z", "value": 1024}]
            }
        }
        
        # Test parameters
        node_name = "test-node"
        start_time = "2023-01-01T00:00:00Z"
        end_time = "2023-01-01T01:00:00Z"
        
        # Execute function
        result = fetch_kubernetes_node_metrics(node_name, start_time, end_time)
        result_dict = json.loads(result)
        
        # Assertions
        assert isinstance(result_dict, dict)
        assert "node" in result_dict
        assert "metrics" in result_dict
        assert "cpu" in result_dict["metrics"]
        assert "memory" in result_dict["metrics"]

# Tests for fetch_azure_metrics
def test_fetch_azure_metrics_success(mock_env_variables):
    with patch('data_processor.lib.data_factory.DataFactory.get_azure_client') as mock_client:
        # Configure mock client
        mock_client.return_value.get_resource_metrics.return_value = {
            "metrics": {
                "Percentage CPU": [{"timestamp": "2023-01-01T00:00:00Z", "value": 75}],
                "Available Memory Bytes": [{"timestamp": "2023-01-01T00:00:00Z", "value": **********}]
            }
        }
        
        # Test parameters
        resource_id = "/subscriptions/test/resourceGroups/test/providers/Microsoft.Test/testResources/test"
        metric_names = ["Percentage CPU", "Available Memory Bytes"]
        start_time = "2023-01-01T00:00:00Z"
        end_time = "2023-01-01T01:00:00Z"
        
        # Execute function
        result = fetch_azure_metrics(resource_id, metric_names, start_time, end_time)
        result_dict = json.loads(result)
        
        # Assertions
        assert isinstance(result_dict, dict)
        assert "metrics" in result_dict
        assert "Percentage CPU" in result_dict["metrics"]
        assert "Available Memory Bytes" in result_dict["metrics"] 