from gremlin_python.driver import client, serializer
import sys
import traceback
import json

def get_gremlin_client():
    # Replace with your Cosmos DB Gremlin API endpoint details
    endpoint = "wss://obv-ai-servicemap.gremlin.cosmos.azure.com:443/"
    database = "graphdb"
    collection = "Persons"
    primary_key = "AKnBOPQMLN9gCOXIscvGJi0wzBDLWHyHzYm9w35vr02KKp98R6Mz6VUpB42EsMDgAX52EreYh4tRACDbBLRN7Q=="

    return client.Client(
        endpoint, 'g',
        username=f"/dbs/{database}/colls/{collection}",
        password=primary_key,
        message_serializer=serializer.GraphSONSerializersV2d0()
    )

def add_or_update_vertex(gremlin_client, vertex, partition_key='id'):
    query = (
        "g.V().has('id', '{}').fold().coalesce("
        "unfold(), "
        "addV('node').property(single, 'id', '{}'))."
        "property(single, 'name', '{}')."
        "property(single, 'group', {})."
        "property(single, 'icon', '{}')."
        "property(single, 'additionalInfo', '{}')."
        "property(single, 'issue', {})"
    ).format(
        vertex['id'], vertex['id'], vertex['name'],
        vertex['group'], vertex['icon'], vertex['additionalInfo'],
        str(vertex['issue']).lower()
    )
    
    gremlin_client.submitAsync(query)

def add_or_update_edge(gremlin_client, edge):
    query = (
        "g.V('{}').as('a')."
        "V('{}').as('b')."
        "coalesce("
        "__.inE('connects').where(outV().as('a')).where(inV().as('b')),"
        "addE('connects').from('a').to('b')"
        ").property('value', {})"
    ).format(edge['source'], edge['target'], edge['value'])
    
    gremlin_client.submitAsync(query)

def process_json_data(json_data, partition_key='id'):
    gremlin_client = get_gremlin_client()
    
    try:
        # Add or update vertices
        for node in json_data['nodes']:
            add_or_update_vertex(gremlin_client, node, partition_key)
        
        # Add or update edges
        for link in json_data['links']:
            add_or_update_edge(gremlin_client, link)
        
        print("Data successfully processed and stored in the graph database.")
    
    except Exception as e:
        print("Error processing data:", e)
        traceback.print_exc(file=sys.stdout)
    
    finally:
        gremlin_client.close()

# Example usage
json_data = json.loads('''
{
"nodes": [
        {
            "id": "SA_CFA831",
            "name": "SA_CFA831",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SmartAdvisor Collector",
            "issue": false
        },
        {
            "id": "smartadvisors-service",
            "name": "smartadvisors-service",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "SA_CFA831 | collector-config-SA-AppUsage_01.json",
            "name": "SA_CFA831 | collector-config-SA-AppUsage_01.json",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SmartAdvisor Collector",
            "issue": false
        },
        {
            "id": "SA_44D407",
            "name": "SA_44D407",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SmartAdvisor Collector",
            "issue": false
        },
        {
            "id": "SA_44D407 | collector-config-SA-DeviceStability_01.json",
            "name": "SA_44D407 | collector-config-SA-DeviceStability_01.json",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SmartAdvisor Collector",
            "issue": false
        },
        {
            "id": "SA_CFA831 | collector-config-SA-AppUsage_02.json",
            "name": "SA_CFA831 | collector-config-SA-AppUsage_02.json",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SmartAdvisor Collector",
            "issue": false
        },
        {
            "id": "sawu2devdefaul0x02532708.table.core.windows.net",
            "name": "sawu2devdefaul0x02532708.table.core.windows.net",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure table",
            "issue": false
        },
        {
            "id": "af-devprod-deploymentservice-deploy",
            "name": "af-devprod-deploymentservice-deploy",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/people-smt-people-view/Subscriptions/people-smt-people-view-sub",
            "name": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/people-smt-people-view/Subscriptions/people-smt-people-view-sub",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure Service Bus",
            "issue": false
        },
        {
            "id": "ITAnalyst.PeopleView.Server",
            "name": "ITAnalyst.PeopleView.Server",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "events.launchdarkly.com",
            "name": "events.launchdarkly.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "IvantiCloudWebApp",
            "name": "IvantiCloudWebApp",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-health-smt-ih/Subscriptions/agent-health-smt-ih-sub",
            "name": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-health-smt-ih/Subscriptions/agent-health-smt-ih-sub",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure Service Bus",
            "issue": false
        },
        {
            "id": "IvantiCloud.AccessControl",
            "name": "IvantiCloud.AccessControl",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "devprod-sfc.ivanticlouddev.com",
            "name": "devprod-sfc.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "settings-webapi",
            "name": "settings-webapi",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "dataservices-dataimport-admin",
            "name": "dataservices-dataimport-admin",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "AssistantsWebApp",
            "name": "AssistantsWebApp",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "devprod-afs7.ivanticlouddev.com",
            "name": "devprod-afs7.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "app.launchdarkly.com",
            "name": "app.launchdarkly.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "rbac-service",
            "name": "rbac-service",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "ems-webapi-service",
            "name": "ems-webapi-service",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "assistants-bots-devprod.ivanticlouddev.com",
            "name": "assistants-bots-devprod.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "assistants-devprod.ivanticlouddev.com",
            "name": "assistants-devprod.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "config-service.dev-ops.svc.cluster.local",
            "name": "config-service.dev-ops.svc.cluster.local",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: HTTP",
            "issue": false
        },
        {
            "id": "config-service-webhost",
            "name": "config-service-webhost",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "tenant-preferences-webhost",
            "name": "tenant-preferences-webhost",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "devprod-scorpio3.ivanticlouddev.com",
            "name": "devprod-scorpio3.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "automation-management-webhost-service",
            "name": "automation-management-webhost-service",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "devprod-integration-ism.ivanticlouddev.com",
            "name": "devprod-integration-ism.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "af-devprod-deploymentservice-policy",
            "name": "af-devprod-deploymentservice-policy",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "sb://sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ | aaa-gmt",
            "name": "sb://sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ | aaa-gmt",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure Service Bus",
            "issue": false
        },
        {
            "id": "sawu2devdefaul0x02532708.blob.core.windows.net",
            "name": "sawu2devdefaul0x02532708.blob.core.windows.net",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure blob",
            "issue": false
        },
        {
            "id": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/aaa-gmt",
            "name": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/aaa-gmt",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure Service Bus",
            "issue": false
        },
        {
            "id": "lcm-web-api",
            "name": "lcm-web-api",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "inventory-software-query-api-service",
            "name": "inventory-software-query-api-service",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "landing-page-webhost",
            "name": "landing-page-webhost",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "connectivity-store-server",
            "name": "connectivity-store-server",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "dpr-apim.ivanticlouddev.com",
            "name": "dpr-apim.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ems-dmt",
            "name": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ems-dmt",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Queue Message | Azure Service Bus",
            "issue": false
        },
        {
            "id": "ems-checkstalledbackups-backend",
            "name": "ems-checkstalledbackups-backend",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "SA_44D407 | collector-config-SA-DeviceStability_03.json",
            "name": "SA_44D407 | collector-config-SA-DeviceStability_03.json",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SmartAdvisor Collector",
            "issue": false
        },
        {
            "id": "SA_44D407 | collector-config-SA-DeviceStability_02.json",
            "name": "SA_44D407 | collector-config-SA-DeviceStability_02.json",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SmartAdvisor Collector",
            "issue": false
        },
        {
            "id": "discovery-webclient",
            "name": "discovery-webclient",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "discovery-agents-server-backend-service",
            "name": "discovery-agents-server-backend-service",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/rg-wu2-dev-discoagentmgmt/providers/microsoft.insights/components/AI-DiscoAgentMgmt-dpr",
            "issue": false
        },
        {
            "id": "devprod-saas-t1.ivanticlouddev.com",
            "name": "devprod-saas-t1.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "dragnet-spendintel.ivanticlouddev.com",
            "name": "dragnet-spendintel.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "application-control-http-api",
            "name": "application-control-http-api",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "ems-schedulereconcile-backend",
            "name": "ems-schedulereconcile-backend",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "devprod-tenant1.ivanticlouddev.com",
            "name": "devprod-tenant1.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "aether-devprod-tenant-1.ivanticlouddev.com",
            "name": "aether-devprod-tenant-1.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "inventory-software-client-api-service",
            "name": "inventory-software-client-api-service",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "login-app-login-service-non-agent",
            "name": "login-app-login-service-non-agent",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "devprod-t1.ivanticlouddev.com",
            "name": "devprod-t1.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-auto2-a3aa.ivanticlouddev.com",
            "name": "devprod-auto2-a3aa.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-auto2-a4aa.ivanticlouddev.com",
            "name": "devprod-auto2-a4aa.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "dsl-query-webhost",
            "name": "dsl-query-webhost",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": true
        },
        {
            "id": "tcp:sq-wu2-dev-defaul-0x0-25.database.windows.net,1433 | dsl",
            "name": "tcp:sq-wu2-dev-defaul-0x0-25.database.windows.net,1433 | dsl",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SQL",
            "issue": false
        },
        {
            "id": "data.eu.pendo.io",
            "name": "data.eu.pendo.io",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "SA_7A1D0F | collector-config-SA-ActiveDirectoryUsers_01.json",
            "name": "SA_7A1D0F | collector-config-SA-ActiveDirectoryUsers_01.json",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SmartAdvisor Collector",
            "issue": false
        },
        {
            "id": "SA_D45AF5",
            "name": "SA_D45AF5",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SmartAdvisor Collector",
            "issue": false
        },
        {
            "id": "SA_7A1D0F",
            "name": "SA_7A1D0F",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SmartAdvisor Collector",
            "issue": false
        },
        {
            "id": "SA_D45AF5 | collector-config-SA-Office365_01.json",
            "name": "SA_D45AF5 | collector-config-SA-Office365_01.json",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: SmartAdvisor Collector",
            "issue": false
        },
        {
            "id": "inventory-saas-web-api",
            "name": "inventory-saas-web-api",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "mll-neurons-devprod.ivanticlouddev.com",
            "name": "mll-neurons-devprod.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "filereputation-webhost",
            "name": "filereputation-webhost",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/application-control-dmt",
            "name": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/application-control-dmt",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Queue Message | Azure Service Bus",
            "issue": false
        },
        {
            "id": "application-control-maintenance-message-scheduler",
            "name": "application-control-maintenance-message-scheduler",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "devprod-auto2-a0bb.ivanticlouddev.com",
            "name": "devprod-auto2-a0bb.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-afs2.ivanticlouddev.com",
            "name": "devprod-afs2.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-rc.ivanticlouddev.com:45345",
            "name": "devprod-rc.ivanticlouddev.com:45345",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Fetch",
            "issue": false
        },
        {
            "id": "devprod-rcauth.ivanticlouddev.com",
            "name": "devprod-rcauth.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Fetch",
            "issue": false
        },
        {
            "id": "js.monitor.azure.com",
            "name": "js.monitor.azure.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Fetch",
            "issue": false
        },
        {
            "id": "devprod-rcviewer.ivanticlouddev.com",
            "name": "devprod-rcviewer.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-patchdev.ivanticlouddev.com",
            "name": "devprod-patchdev.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "dev-prd-edge.ivanticlouddev.com",
            "name": "dev-prd-edge.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "mlldevprodinf.blob.core.windows.net",
            "name": "mlldevprodinf.blob.core.windows.net",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "team-spitfire-devprod.ivanticlouddev.com",
            "name": "team-spitfire-devprod.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "login.microsoftonline.com",
            "name": "login.microsoftonline.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Fetch",
            "issue": false
        },
        {
            "id": "graph.microsoft.com",
            "name": "graph.microsoft.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-scorpio4.ivanticlouddev.com",
            "name": "devprod-scorpio4.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "stream.launchdarkly.com",
            "name": "stream.launchdarkly.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: HTTP",
            "issue": false
        },
        {
            "id": "agent-management-signalr-notifications-devprod.service.signalr.net",
            "name": "agent-management-signalr-notifications-devprod.service.signalr.net",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Fetch",
            "issue": false
        },
        {
            "id": "device-discovery-signalr-notifications-devprod.service.signalr.net",
            "name": "device-discovery-signalr-notifications-devprod.service.signalr.net",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Fetch",
            "issue": false
        },
        {
            "id": "globalsettings-pentest.ivanticlouddev.com",
            "name": "globalsettings-pentest.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "exposures-api",
            "name": "exposures-api",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "maps.googleapis.com",
            "name": "maps.googleapis.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "mapsresources-pa.googleapis.com",
            "name": "mapsresources-pa.googleapis.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "pendo-eu-static-6329527733649408.storage.googleapis.com",
            "name": "pendo-eu-static-6329527733649408.storage.googleapis.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-phantom.ivanticlouddev.com",
            "name": "devprod-phantom.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-easm-demo.ivanticlouddev.com",
            "name": "devprod-easm-demo.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-cyborgtest30.ivanticlouddev.com",
            "name": "devprod-cyborgtest30.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-sctr-os-1.ivanticlouddev.com",
            "name": "devprod-sctr-os-1.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-sctr-2.ivanticlouddev.com",
            "name": "devprod-sctr-2.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "aether-devprod-test-tenant.ivanticlouddev.com",
            "name": "aether-devprod-test-tenant.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "e2e-spitfire-devprod.ivanticlouddev.com",
            "name": "e2e-spitfire-devprod.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "AppsMEM.API",
            "name": "AppsMEM.API",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "devprod-harrier.ivanticlouddev.com",
            "name": "devprod-harrier.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-ism-unomatrix2.ivanticlouddev.com",
            "name": "devprod-ism-unomatrix2.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-vitara.ivanticlouddev.com",
            "name": "devprod-vitara.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-rc.ivanticlouddev.com:45344",
            "name": "devprod-rc.ivanticlouddev.com:45344",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Fetch",
            "issue": false
        },
        {
            "id": "localhost:5012",
            "name": "localhost:5012",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "stappctrldevprod.blob.core.windows.net",
            "name": "stappctrldevprod.blob.core.windows.net",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure blob",
            "issue": false
        },
        {
            "id": "application-control-events-message-scheduler",
            "name": "application-control-events-message-scheduler",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "devprod-ism-unomatrix1.ivanticlouddev.com",
            "name": "devprod-ism-unomatrix1.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "aether-e2e-devprod-tenant.ivanticlouddev.com",
            "name": "aether-e2e-devprod-tenant.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "easm-api",
            "name": "easm-api",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "OTHER",
            "name": "OTHER",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: HTTP",
            "issue": false
        },
        {
            "id": "devprod-integration-servicenow.ivanticlouddev.com",
            "name": "devprod-integration-servicenow.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: HTTP",
            "issue": false
        },
        {
            "id": "integration-servicenow-service",
            "name": "integration-servicenow-service",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "devprod-chance.ivanticlouddev.com",
            "name": "devprod-chance.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-kronos1.ivanticlouddev.com",
            "name": "devprod-kronos1.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "rs.fullstory.com",
            "name": "rs.fullstory.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "ivanti.aha.io",
            "name": "ivanti.aha.io",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Fetch",
            "issue": false
        },
        {
            "id": "edge.fullstory.com",
            "name": "edge.fullstory.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "devprod-sctr-3.ivanticlouddev.com",
            "name": "devprod-sctr-3.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "neurons-processor",
            "name": "neurons-processor",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "af-devprod-deploymentservice-status",
            "name": "af-devprod-deploymentservice-status",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "management.azure.com",
            "name": "management.azure.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: HTTP",
            "issue": false
        },
        {
            "id": "ems-elasticpoolmaintenance-backend",
            "name": "ems-elasticpoolmaintenance-backend",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "Ivanti.Patch.Policy.DbMigration.Jobs.Container",
            "name": "Ivanti.Patch.Policy.DbMigration.Jobs.Container",
            "group": 1,
            "icon": "static/App-Services.svg",
            "additionalInfo": "ResourceId: /subscriptions/de75b74e-a237-4202-bb61-7c65a76b811a/resourceGroups/RG-WU2-DEV-Main/providers/microsoft.insights/components/AI-WU2-DEV-DEFAUL-0X0-25",
            "issue": false
        },
        {
            "id": "ac-demo-tenant-1.ivanticlouddev.com",
            "name": "ac-demo-tenant-1.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "alpha-local-testing.ivanticlouddev.com",
            "name": "alpha-local-testing.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "oil-itsm-pentest.ivanticlouddev.com",
            "name": "oil-itsm-pentest.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "cdn.plot.ly",
            "name": "cdn.plot.ly",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Ajax",
            "issue": false
        },
        {
            "id": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/nalp-smt-nalp-supersetmgmtsvc/Subscriptions/nalp-smt-nalp-supersetmgmtsvc-sub",
            "name": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/nalp-smt-nalp-supersetmgmtsvc/Subscriptions/nalp-smt-nalp-supersetmgmtsvc-sub",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure Service Bus",
            "issue": false
        },
        {
            "id": "ivanti-internal-eventhub-namespace-devprod.servicebus.windows.net/nalp_devices_batch_init_eventhub",
            "name": "ivanti-internal-eventhub-namespace-devprod.servicebus.windows.net/nalp_devices_batch_init_eventhub",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure Event Hubs",
            "issue": false
        },
        {
            "id": "**********:9200",
            "name": "**********:9200",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: HTTP",
            "issue": false
        },
        {
            "id": "curateddocdevprod.blob.core.windows.net",
            "name": "curateddocdevprod.blob.core.windows.net",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure blob",
            "issue": false
        },
        {
            "id": "nalp-internal-eventhub-namespace-devprod.servicebus.windows.net/nalp-initdb-people",
            "name": "nalp-internal-eventhub-namespace-devprod.servicebus.windows.net/nalp-initdb-people",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure Event Hubs",
            "issue": false
        },
        {
            "id": "devprod-ss.ivanticlouddev.com",
            "name": "devprod-ss.ivanticlouddev.com",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: HTTP",
            "issue": false
        },
        {
            "id": "ivanti-internal-eventhub-namespace-devprod.servicebus.windows.net/nalp_tenant_init_eventhub",
            "name": "ivanti-internal-eventhub-namespace-devprod.servicebus.windows.net/nalp_tenant_init_eventhub",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure Event Hubs",
            "issue": false
        },
        {
            "id": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-policy-management-smt-as/Subscriptions/agent-policy-management-smt-as-sub",
            "name": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-policy-management-smt-as/Subscriptions/agent-policy-management-smt-as-sub",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure Service Bus",
            "issue": false
        },
        {
            "id": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-policy-management-smt-as",
            "name": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-policy-management-smt-as",
            "group": 1,
            "icon": "static/External-Service.svg",
            "additionalInfo": "Type: Azure Service Bus",
            "issue": false
        }
    ],
    "links": [
        {
            "source": "SA_CFA831",
            "target": "smartadvisors-service",
            "value": 1
        },
        {
            "source": "SA_CFA831",
            "target": "smartadvisors-service",
            "value": 3
        },
        {
            "source": "SA_CFA831 | collector-config-SA-AppUsage_01.json",
            "target": "smartadvisors-service",
            "value": 1
        },
        {
            "source": "SA_CFA831 | collector-config-SA-AppUsage_01.json",
            "target": "smartadvisors-service",
            "value": 44
        },
        {
            "source": "SA_44D407",
            "target": "smartadvisors-service",
            "value": 1
        },
        {
            "source": "SA_44D407",
            "target": "smartadvisors-service",
            "value": 76
        },
        {
            "source": "SA_44D407 | collector-config-SA-DeviceStability_01.json",
            "target": "smartadvisors-service",
            "value": 1
        },
        {
            "source": "SA_44D407 | collector-config-SA-DeviceStability_01.json",
            "target": "smartadvisors-service",
            "value": 59
        },
        {
            "source": "SA_CFA831 | collector-config-SA-AppUsage_02.json",
            "target": "smartadvisors-service",
            "value": 1
        },
        {
            "source": "SA_CFA831 | collector-config-SA-AppUsage_02.json",
            "target": "smartadvisors-service",
            "value": 98
        },
        {
            "source": "sawu2devdefaul0x02532708.table.core.windows.net",
            "target": "af-devprod-deploymentservice-deploy",
            "value": 1
        },
        {
            "source": "sawu2devdefaul0x02532708.table.core.windows.net",
            "target": "af-devprod-deploymentservice-deploy",
            "value": 56
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/people-smt-people-view/Subscriptions/people-smt-people-view-sub",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/people-smt-people-view/Subscriptions/people-smt-people-view-sub",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 63
        },
        {
            "source": "events.launchdarkly.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "events.launchdarkly.com",
            "target": "IvantiCloudWebApp",
            "value": 14
        },
        {
            "source": "events.launchdarkly.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "events.launchdarkly.com",
            "target": "IvantiCloud.AccessControl",
            "value": 53
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "settings-webapi",
            "value": 12
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "dataservices-dataimport-admin",
            "value": 24
        },
        {
            "source": "events.launchdarkly.com",
            "target": "AssistantsWebApp",
            "value": 1
        },
        {
            "source": "events.launchdarkly.com",
            "target": "AssistantsWebApp",
            "value": 89
        },
        {
            "source": "devprod-afs7.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-afs7.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 55
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 12
        },
        {
            "source": "app.launchdarkly.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "app.launchdarkly.com",
            "target": "IvantiCloudWebApp",
            "value": 31
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "rbac-service",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "ems-webapi-service",
            "value": 28
        },
        {
            "source": "sawu2devdefaul0x02532708.table.core.windows.net",
            "target": "rbac-service",
            "value": 1
        },
        {
            "source": "sawu2devdefaul0x02532708.table.core.windows.net",
            "target": "rbac-service",
            "value": 38
        },
        {
            "source": "assistants-bots-devprod.ivanticlouddev.com",
            "target": "AssistantsWebApp",
            "value": 1
        },
        {
            "source": "assistants-bots-devprod.ivanticlouddev.com",
            "target": "AssistantsWebApp",
            "value": 35
        },
        {
            "source": "assistants-devprod.ivanticlouddev.com",
            "target": "AssistantsWebApp",
            "value": 1
        },
        {
            "source": "assistants-devprod.ivanticlouddev.com",
            "target": "AssistantsWebApp",
            "value": 28
        },
        {
            "source": "app.launchdarkly.com",
            "target": "AssistantsWebApp",
            "value": 1
        },
        {
            "source": "app.launchdarkly.com",
            "target": "AssistantsWebApp",
            "value": 71
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "AssistantsWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "AssistantsWebApp",
            "value": 93
        },
        {
            "source": "config-service.dev-ops.svc.cluster.local",
            "target": "rbac-service",
            "value": 1
        },
        {
            "source": "config-service.dev-ops.svc.cluster.local",
            "target": "config-service-webhost",
            "value": 84
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "tenant-preferences-webhost",
            "value": 15
        },
        {
            "source": "devprod-scorpio3.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-scorpio3.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 78
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "automation-management-webhost-service",
            "value": 82
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "settings-webapi",
            "value": 23
        },
        {
            "source": "sawu2devdefaul0x02532708.table.core.windows.net",
            "target": "af-devprod-deploymentservice-policy",
            "value": 1
        },
        {
            "source": "sawu2devdefaul0x02532708.table.core.windows.net",
            "target": "af-devprod-deploymentservice-policy",
            "value": 77
        },
        {
            "source": "sb://sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ | aaa-gmt",
            "target": "af-devprod-deploymentservice-policy",
            "value": 1
        },
        {
            "source": "sb://sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ | aaa-gmt",
            "target": "af-devprod-deploymentservice-policy",
            "value": 51
        },
        {
            "source": "sawu2devdefaul0x02532708.blob.core.windows.net",
            "target": "af-devprod-deploymentservice-policy",
            "value": 1
        },
        {
            "source": "sawu2devdefaul0x02532708.blob.core.windows.net",
            "target": "af-devprod-deploymentservice-policy",
            "value": 45
        },
        {
            "source": "config-service.dev-ops.svc.cluster.local",
            "target": "config-service-webhost",
            "value": 5
        },
        {
            "source": "app.launchdarkly.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "app.launchdarkly.com",
            "target": "IvantiCloud.AccessControl",
            "value": 63
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "lcm-web-api",
            "value": 82
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "inventory-software-query-api-service",
            "value": 99
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "landing-page-webhost",
            "value": 94
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "tenant-preferences-webhost",
            "value": 39
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "connectivity-store-server",
            "value": 67
        },
        {
            "source": "dpr-apim.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "dpr-apim.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 14
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ems-dmt",
            "target": "ems-checkstalledbackups-backend",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ems-dmt",
            "target": "ems-checkstalledbackups-backend",
            "value": 85
        },
        {
            "source": "SA_44D407 | collector-config-SA-DeviceStability_03.json",
            "target": "smartadvisors-service",
            "value": 1
        },
        {
            "source": "SA_44D407 | collector-config-SA-DeviceStability_03.json",
            "target": "smartadvisors-service",
            "value": 94
        },
        {
            "source": "SA_44D407 | collector-config-SA-DeviceStability_02.json",
            "target": "smartadvisors-service",
            "value": 1
        },
        {
            "source": "SA_44D407 | collector-config-SA-DeviceStability_02.json",
            "target": "smartadvisors-service",
            "value": 61
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "lcm-web-api",
            "value": 84
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "landing-page-webhost",
            "value": 42
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "tenant-preferences-webhost",
            "value": 83
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "discovery-webclient",
            "value": 3
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "inventory-software-query-api-service",
            "value": 54
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "rbac-service",
            "value": 14
        },
        {
            "source": "config-service.dev-ops.svc.cluster.local",
            "target": "config-service-webhost",
            "value": 81
        },
        {
            "source": "config-service.dev-ops.svc.cluster.local",
            "target": "discovery-agents-server-backend-service",
            "value": 1
        },
        {
            "source": "config-service.dev-ops.svc.cluster.local",
            "target": "config-service-webhost",
            "value": 16
        },
        {
            "source": "devprod-saas-t1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-saas-t1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 17
        },
        {
            "source": "dragnet-spendintel.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "dragnet-spendintel.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 10
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "application-control-http-api",
            "value": 94
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ems-dmt",
            "target": "ems-schedulereconcile-backend",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ems-dmt",
            "target": "ems-schedulereconcile-backend",
            "value": 19
        },
        {
            "source": "devprod-tenant1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-tenant1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 97
        },
        {
            "source": "aether-devprod-tenant-1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "aether-devprod-tenant-1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 4
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "inventory-software-client-api-service",
            "value": 2
        },
        {
            "source": "dpr-apim.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "dpr-apim.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 74
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "login-app-login-service-non-agent",
            "value": 20
        },
        {
            "source": "devprod-t1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-t1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 25
        },
        {
            "source": "devprod-auto2-a3aa.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-auto2-a3aa.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 30
        },
        {
            "source": "devprod-auto2-a4aa.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-auto2-a4aa.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 32
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "dataservices-dataimport-admin",
            "value": 31
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "settings-webapi",
            "value": 87
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "rbac-service",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "login-app-login-service-non-agent",
            "value": 10
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "dsl-query-webhost",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "login-app-login-service-non-agent",
            "value": 92
        },
        {
            "source": "tcp:sq-wu2-dev-defaul-0x0-25.database.windows.net,1433 | dsl",
            "target": "dsl-query-webhost",
            "value": 1
        },
        {
            "source": "tcp:sq-wu2-dev-defaul-0x0-25.database.windows.net,1433 | dsl",
            "target": "dsl-query-webhost",
            "value": 32
        },
        {
            "source": "data.eu.pendo.io",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "data.eu.pendo.io",
            "target": "IvantiCloudWebApp",
            "value": 76
        },
        {
            "source": "SA_7A1D0F | collector-config-SA-ActiveDirectoryUsers_01.json",
            "target": "smartadvisors-service",
            "value": 1
        },
        {
            "source": "SA_7A1D0F | collector-config-SA-ActiveDirectoryUsers_01.json",
            "target": "smartadvisors-service",
            "value": 82
        },
        {
            "source": "SA_D45AF5",
            "target": "smartadvisors-service",
            "value": 1
        },
        {
            "source": "SA_D45AF5",
            "target": "smartadvisors-service",
            "value": 94
        },
        {
            "source": "SA_7A1D0F",
            "target": "smartadvisors-service",
            "value": 1
        },
        {
            "source": "SA_7A1D0F",
            "target": "smartadvisors-service",
            "value": 11
        },
        {
            "source": "SA_D45AF5 | collector-config-SA-Office365_01.json",
            "target": "smartadvisors-service",
            "value": 1
        },
        {
            "source": "SA_D45AF5 | collector-config-SA-Office365_01.json",
            "target": "smartadvisors-service",
            "value": 79
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "rbac-service",
            "value": 33
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "inventory-saas-web-api",
            "value": 80
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "connectivity-store-server",
            "value": 27
        },
        {
            "source": "mll-neurons-devprod.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "mll-neurons-devprod.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 64
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ems-dmt",
            "target": "ems-schedulereconcile-backend",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ems-dmt",
            "target": "ems-schedulereconcile-backend",
            "value": 66
        },
        {
            "source": "assistants-devprod.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "assistants-devprod.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 80
        },
        {
            "source": "assistants-devprod.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "assistants-devprod.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 22
        },
        {
            "source": "app.launchdarkly.com",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 1
        },
        {
            "source": "app.launchdarkly.com",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 29
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "connectivity-store-server",
            "value": 44
        },
        {
            "source": "sawu2devdefaul0x02532708.table.core.windows.net",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 1
        },
        {
            "source": "sawu2devdefaul0x02532708.table.core.windows.net",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 48
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "filereputation-webhost",
            "value": 68
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/application-control-dmt",
            "target": "application-control-maintenance-message-scheduler",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/application-control-dmt",
            "target": "application-control-maintenance-message-scheduler",
            "value": 60
        },
        {
            "source": "devprod-auto2-a0bb.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-auto2-a0bb.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 82
        },
        {
            "source": "devprod-afs2.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-afs2.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 51
        },
        {
            "source": "devprod-patchdev.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-patchdev.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 34
        },
        {
            "source": "devprod-patchdev.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-patchdev.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 55
        },
        {
            "source": "dev-prd-edge.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "dev-prd-edge.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 61
        },
        {
            "source": "mlldevprodinf.blob.core.windows.net",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "mlldevprodinf.blob.core.windows.net",
            "target": "IvantiCloudWebApp",
            "value": 23
        },
        {
            "source": "team-spitfire-devprod.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "team-spitfire-devprod.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 6
        },
        {
            "source": "login.microsoftonline.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "login.microsoftonline.com",
            "target": "IvantiCloudWebApp",
            "value": 47
        },
        {
            "source": "devprod-scorpio4.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-scorpio4.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 34
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ems-dmt",
            "target": "ems-checkstalledbackups-backend",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ems-dmt",
            "target": "ems-checkstalledbackups-backend",
            "value": 74
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/application-control-dmt",
            "target": "application-control-maintenance-message-scheduler",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/application-control-dmt",
            "target": "application-control-maintenance-message-scheduler",
            "value": 24
        },
        {
            "source": "stream.launchdarkly.com",
            "target": "dsl-query-webhost",
            "value": 1
        },
        {
            "source": "stream.launchdarkly.com",
            "target": "dsl-query-webhost",
            "value": 11
        },
        {
            "source": "agent-management-signalr-notifications-devprod.service.signalr.net",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "agent-management-signalr-notifications-devprod.service.signalr.net",
            "target": "IvantiCloudWebApp",
            "value": 66
        },
        {
            "source": "device-discovery-signalr-notifications-devprod.service.signalr.net",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "device-discovery-signalr-notifications-devprod.service.signalr.net",
            "target": "IvantiCloudWebApp",
            "value": 42
        },
        {
            "source": "globalsettings-pentest.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "globalsettings-pentest.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 70
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "exposures-api",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "login-app-login-service-non-agent",
            "value": 28
        },
        {
            "source": "maps.googleapis.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "maps.googleapis.com",
            "target": "IvantiCloudWebApp",
            "value": 63
        },
        {
            "source": "mapsresources-pa.googleapis.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "mapsresources-pa.googleapis.com",
            "target": "IvantiCloudWebApp",
            "value": 11
        },
        {
            "source": "devprod-phantom.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-phantom.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 19
        },
        {
            "source": "devprod-easm-demo.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-easm-demo.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 71
        },
        {
            "source": "assistants-bots-devprod.ivanticlouddev.com",
            "target": "AssistantsWebApp",
            "value": 1
        },
        {
            "source": "assistants-bots-devprod.ivanticlouddev.com",
            "target": "AssistantsWebApp",
            "value": 52
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/aaa-gmt",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/aaa-gmt",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 6
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/aaa-gmt",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/aaa-gmt",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 78
        },
        {
            "source": "config-service.dev-ops.svc.cluster.local",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 1
        },
        {
            "source": "config-service.dev-ops.svc.cluster.local",
            "target": "config-service-webhost",
            "value": 55
        },
        {
            "source": "devprod-cyborgtest30.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-cyborgtest30.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 89
        },
        {
            "source": "devprod-sctr-os-1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sctr-os-1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 69
        },
        {
            "source": "devprod-sctr-2.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sctr-2.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 60
        },
        {
            "source": "aether-devprod-test-tenant.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "aether-devprod-test-tenant.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 35
        },
        {
            "source": "e2e-spitfire-devprod.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "e2e-spitfire-devprod.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 31
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "AppsMEM.API",
            "value": 30
        },
        {
            "source": "devprod-harrier.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-harrier.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 44
        },
        {
            "source": "devprod-ism-unomatrix2.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-ism-unomatrix2.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 23
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "discovery-agents-server-backend-service",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "login-app-login-service-non-agent",
            "value": 15
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "inventory-software-client-api-service",
            "value": 64
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "filereputation-webhost",
            "value": 45
        },
        {
            "source": "devprod-vitara.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-vitara.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 50
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "discovery-webclient",
            "value": 18
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "AppsMEM.API",
            "value": 43
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 78
        },
        {
            "source": "localhost:5012",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "localhost:5012",
            "target": "IvantiCloudWebApp",
            "value": 31
        },
        {
            "source": "stappctrldevprod.blob.core.windows.net",
            "target": "application-control-events-message-scheduler",
            "value": 1
        },
        {
            "source": "stappctrldevprod.blob.core.windows.net",
            "target": "application-control-events-message-scheduler",
            "value": 25
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "application-control-maintenance-message-scheduler",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "ems-webapi-service",
            "value": 29
        },
        {
            "source": "devprod-ism-unomatrix1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-ism-unomatrix1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 22
        },
        {
            "source": "aether-e2e-devprod-tenant.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "aether-e2e-devprod-tenant.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 89
        },
        {
            "source": "agent-management-signalr-notifications-devprod.service.signalr.net",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "agent-management-signalr-notifications-devprod.service.signalr.net",
            "target": "IvantiCloud.AccessControl",
            "value": 4
        },
        {
            "source": "device-discovery-signalr-notifications-devprod.service.signalr.net",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "device-discovery-signalr-notifications-devprod.service.signalr.net",
            "target": "IvantiCloud.AccessControl",
            "value": 88
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "easm-api",
            "value": 34
        },
        {
            "source": "OTHER",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 1
        },
        {
            "source": "OTHER",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 83
        },
        {
            "source": "devprod-integration-servicenow.ivanticlouddev.com",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 1
        },
        {
            "source": "devprod-integration-servicenow.ivanticlouddev.com",
            "target": "integration-servicenow-service",
            "value": 37
        },
        {
            "source": "devprod-integration-ism.ivanticlouddev.com",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 1
        },
        {
            "source": "devprod-integration-ism.ivanticlouddev.com",
            "target": "integration-servicenow-service",
            "value": 77
        },
        {
            "source": "assistants-devprod.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "assistants-devprod.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 59
        },
        {
            "source": "assistants-devprod.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "assistants-devprod.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 18
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/application-control-dmt",
            "target": "application-control-events-message-scheduler",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/application-control-dmt",
            "target": "application-control-events-message-scheduler",
            "value": 19
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "ITAnalyst.PeopleView.Server",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "inventory-saas-web-api",
            "value": 72
        },
        {
            "source": "devprod-chance.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-chance.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 55
        },
        {
            "source": "pendo-eu-static-6329527733649408.storage.googleapis.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "pendo-eu-static-6329527733649408.storage.googleapis.com",
            "target": "IvantiCloudWebApp",
            "value": 9
        },
        {
            "source": "devprod-kronos1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-kronos1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 90
        },
        {
            "source": "rs.fullstory.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "rs.fullstory.com",
            "target": "IvantiCloudWebApp",
            "value": 24
        },
        {
            "source": "ivanti.aha.io",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "ivanti.aha.io",
            "target": "IvantiCloudWebApp",
            "value": 52
        },
        {
            "source": "edge.fullstory.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "edge.fullstory.com",
            "target": "IvantiCloudWebApp",
            "value": 21
        },
        {
            "source": "sb://sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ | aaa-gmt",
            "target": "af-devprod-deploymentservice-deploy",
            "value": 1
        },
        {
            "source": "sb://sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ | aaa-gmt",
            "target": "af-devprod-deploymentservice-deploy",
            "value": 25
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "af-devprod-deploymentservice-deploy",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "af-devprod-deploymentservice-deploy",
            "value": 44
        },
        {
            "source": "devprod-sctr-3.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "devprod-sctr-3.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 28
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "neurons-processor",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "ems-webapi-service",
            "value": 34
        },
        {
            "source": "sawu2devdefaul0x02532708.table.core.windows.net",
            "target": "af-devprod-deploymentservice-status",
            "value": 1
        },
        {
            "source": "sawu2devdefaul0x02532708.table.core.windows.net",
            "target": "af-devprod-deploymentservice-status",
            "value": 85
        },
        {
            "source": "sb://sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ | aaa-gmt",
            "target": "af-devprod-deploymentservice-status",
            "value": 1
        },
        {
            "source": "sb://sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/ | aaa-gmt",
            "target": "af-devprod-deploymentservice-status",
            "value": 97
        },
        {
            "source": "management.azure.com",
            "target": "ems-elasticpoolmaintenance-backend",
            "value": 1
        },
        {
            "source": "management.azure.com",
            "target": "ems-elasticpoolmaintenance-backend",
            "value": 72
        },
        {
            "source": "events.launchdarkly.com",
            "target": "Ivanti.Patch.Policy.DbMigration.Jobs.Container",
            "value": 1
        },
        {
            "source": "events.launchdarkly.com",
            "target": "Ivanti.Patch.Policy.DbMigration.Jobs.Container",
            "value": 92
        },
        {
            "source": "stream.launchdarkly.com",
            "target": "Ivanti.Patch.Policy.DbMigration.Jobs.Container",
            "value": 1
        },
        {
            "source": "stream.launchdarkly.com",
            "target": "Ivanti.Patch.Policy.DbMigration.Jobs.Container",
            "value": 60
        },
        {
            "source": "ac-demo-tenant-1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "ac-demo-tenant-1.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 21
        },
        {
            "source": "alpha-local-testing.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "alpha-local-testing.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 20
        },
        {
            "source": "oil-itsm-pentest.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "oil-itsm-pentest.ivanticlouddev.com",
            "target": "IvantiCloudWebApp",
            "value": 37
        },
        {
            "source": "cdn.plot.ly",
            "target": "IvantiCloudWebApp",
            "value": 1
        },
        {
            "source": "cdn.plot.ly",
            "target": "IvantiCloudWebApp",
            "value": 29
        },
        {
            "source": "cdn.plot.ly",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "cdn.plot.ly",
            "target": "IvantiCloud.AccessControl",
            "value": 18
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "IvantiCloud.AccessControl",
            "value": 1
        },
        {
            "source": "devprod-sfc.ivanticlouddev.com",
            "target": "application-control-http-api",
            "value": 100
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-policy-management-smt-as/Subscriptions/agent-policy-management-smt-as-sub",
            "target": "discovery-agents-server-backend-service",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-policy-management-smt-as/Subscriptions/agent-policy-management-smt-as-sub",
            "target": "discovery-agents-server-backend-service",
            "value": 85
        },
        {
            "source": "events.launchdarkly.com",
            "target": "discovery-agents-server-backend-service",
            "value": 1
        },
        {
            "source": "events.launchdarkly.com",
            "target": "discovery-agents-server-backend-service",
            "value": 86
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/aaa-gmt",
            "target": "discovery-agents-server-backend-service",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/aaa-gmt",
            "target": "discovery-agents-server-backend-service",
            "value": 53
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/aaa-gmt",
            "target": "discovery-agents-server-backend-service",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/aaa-gmt",
            "target": "discovery-agents-server-backend-service",
            "value": 81
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-policy-management-smt-as",
            "target": "discovery-agents-server-backend-service",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-policy-management-smt-as",
            "target": "discovery-agents-server-backend-service",
            "value": 71
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-policy-management-smt-as",
            "target": "discovery-agents-server-backend-service",
            "value": 1
        },
        {
            "source": "sb-wu2-dev-prem-defaul-0x1.servicebus.windows.net/agent-policy-management-smt-as",
            "target": "discovery-agents-server-backend-service",
            "value": 10
        },
        {
            "source": "stream.launchdarkly.com",
            "target": "discovery-agents-server-backend-service",
            "value": 1
        },
        {
            "source": "stream.launchdarkly.com",
            "target": "discovery-agents-server-backend-service",
            "value": 85
        }
    ]
}
''')

process_json_data(json_data, partition_key='id')