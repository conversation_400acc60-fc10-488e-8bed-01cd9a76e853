# New Relic Entity Analyzer Tests

This directory contains test scripts for validating and exploring the New Relic Entity Analyzer and Entity Classes functionality.

## Test Scripts

### 1. General Entity Analyzer Test (`test_entity_guid.py`)

This script tests the overall `EntityAnalyzer` class functionality with a specific entity GUID. It performs comprehensive analysis including metrics, logs, and events collection.

#### Usage:

```bash
# Set your New Relic credentials as environment variables
export NEWRELIC_API_KEY="your_api_key"
export NEWRELIC_ACCOUNT_ID="your_account_id"

# Run the test script
./test_entity_guid.py
```

The script will:
- Retrieve the entity details for the specified GUID
- Perform comprehensive analysis using `EntityAnalyzer._analyze_entity_sync()`
- Print a summary of the entity metadata, relationships, metrics, logs, and events
- Save the complete entity data to a JSON file for further analysis

### 2. Entity Classes Test (`test_entity_classes.py`)

This script tests the individual entity classes (PodEntity, NodeEntity, HostEntity, ApplicationEntity) directly. It automatically detects the entity type from the GUID and runs the appropriate entity class tests.

#### Usage:

```bash
# Set your New Relic credentials as environment variables
export NEWRELIC_API_KEY="your_api_key"
export NEWRELIC_ACCOUNT_ID="your_account_id"

# Run the test script
./test_entity_classes.py
```

The script will:
- Determine the entity type from the GUID
- Run the appropriate entity class tests based on the entity type
- Print a summary of the results
- Save the metrics data to a JSON file for further analysis

## Important Note on Async Methods

The `EntityAnalyzer` class has both synchronous and asynchronous methods:

- `analyze_entity()` - Asynchronous method that returns a coroutine (must be awaited)
- `_analyze_entity_sync()` - Synchronous version used in these test scripts

In these test scripts, we use the synchronous `_analyze_entity_sync()` method to avoid having to set up an async runtime. If you need to use the async version in your own code, you'll need to use it in an async function and await the result:

```python
async def async_test():
    entity_data = await analyzer.analyze_entity(
        entity_guid=TEST_ENTITY_GUID,
        since_time=since_time,
        until_time=until_time,
        collect_metrics=True
    )
    return entity_data

# Then run this with an async event loop
import asyncio
entity_data = asyncio.run(async_test())
```

## Modifying the Tests

### Changing the Entity GUID

To test a different entity, edit the `TEST_ENTITY_GUID` constant at the beginning of each script:

```python
# Entity GUID to analyze
TEST_ENTITY_GUID = "your_new_entity_guid"
```

### Modifying the Time Window

You can adjust the time window for analysis by modifying the following section in each script:

```python
# Define time window for analysis
now = datetime.now(UTC)
since_time = now - timedelta(minutes=60)  # 1 hour ago
until_time = now
```

## Analyzing Results

Both scripts save their results to JSON files for further analysis:

- `test_entity_guid.py` saves to `entity_<GUID>.json`
- `test_entity_classes.py` saves to `entity_class_<GUID>.json`

These files contain the full results of the analysis, including metrics, logs, events, and entity relationships.

## Troubleshooting

### Missing Environment Variables

If you see an error about missing API key or account ID, make sure you've set the environment variables:

```bash
export NEWRELIC_API_KEY="your_api_key"
export NEWRELIC_ACCOUNT_ID="your_account_id"
```

### Entity Not Found

If the entity cannot be found, verify that:
1. The GUID is correct
2. Your API key has access to the entity
3. The entity still exists in your New Relic account

### No Metrics Data

If you're not seeing any metrics data, try:
1. Increasing the time window (e.g., set `since_time = now - timedelta(hours=24)`)
2. Verifying that the entity has reported data in the New Relic UI
3. Checking that your NRQL queries in the configuration files match the entity type 