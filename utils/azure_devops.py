"""Module to handle Azure Devops."""
import base64
import os
import textwrap
import urllib
# from .logger_presentation import PLog
import requests
from io import String<PERSON>
from loguru import logger

WI_LIST_BATCH_SIZE = 100


def get_resp(exception):
    """Get response text and status code from error instance."""
    try:
        status_code = exception.response.status_code
        resp_text = exception.response.text
    except Exception:
        status_code = resp_text = "No response"

    return (status_code, resp_text)


class AzureDevops:
    """AzureDevops API."""

    def __init__(
        self,
        api_token: str,
        organization: str,
        project: str,
        api_version: str,
    ) -> None:
        """Connect to Azure Devops API."""
        self._auth_bearer = "Basic " + str(
            base64.b64encode(bytes(":" + api_token, "ascii")),
            "ascii",
        )
        self._base_url = f"https://dev.azure.com/{organization}/{project}/"
        self._api_version = api_version

    def get_workitems_ids(self, query: str, limit: int = 1000) -> list[int]:
        """Get project workitems."""
        try:
            response = requests.post(
                urllib.parse.urljoin(
                    self._base_url,
                    "_apis/wit/wiql",
                ),
                headers={
                    "Content-Type": "application/json",
                    "Authorization": self._auth_bearer,
                },
                params={
                    "api-version": self._api_version,
                    "$top": limit,
                },
                json={"query": query},
                timeout=(10, 20),
            )
            response.raise_for_status()
        except requests.exceptions.RequestException as exc:
            resp_status, resp_text = get_resp(exc)
            logger.exception(
                "Azure Devops API request failed",
                response_status=resp_status,
                response_content=resp_text,
            )
            raise ValueError from exc
        try:
            return [
                item["id"] for item in response.json().get("workItems", [])
            ]
        except (KeyError, requests.exceptions.JSONDecodeError) as exc:
            logger.error("Invalid response", response=response.text)
            raise ValueError from exc

    def get_workitems_by_ids(self, ids:  list[int]) -> dict:
        """Get workitems by given ids."""
        workitems = {}

        for i in range(0, len(ids), WI_LIST_BATCH_SIZE):
            ids_batch = ids[i : i + WI_LIST_BATCH_SIZE]

            try:
                response = requests.get(
                    urllib.parse.urljoin(
                        self._base_url,
                        "_apis/wit/workitems",
                    ),
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": self._auth_bearer,
                    },
                    params={
                        "api-version": self._api_version,
                        "ids": ",".join(map(str, ids_batch)),
                        "$expand": "All",
                    },
                    timeout=(10, 20),
                )
                response.raise_for_status()
            except requests.exceptions.RequestException as exc:
                response = getattr(exc, "response", None)
                logger.exception(
                    "Azure Devops API request failed",
                    response_status=response.status_code
                    if response
                    else "No response",
                    response_content=response.text
                    if response
                    else "No response",
                )
            try:
                workitems.update(
                    {
                        item["id"]: item
                        for item in response.json().get("value", [])
                    },
                )
            except (KeyError, requests.exceptions.JSONDecodeError):
                logger.error("Invalid response", response=response.text)

        return workitems

    def set_tags(self, workitem_id: int, tags: list[str]) -> dict:
        """
        Append tags to ticket. Tags should be provided as a list of strings.

        Returns:
            Response from API.

        Raises:
            ValueError: if request failed.
        """
        try:
            # First, get the current work item to retrieve existing tags
            get_response = requests.get(
                urllib.parse.urljoin(
                    self._base_url,
                    f"_apis/wit/workitems/{workitem_id}",
                ),
                headers={
                    "Content-Type": "application/json",
                    "Authorization": self._auth_bearer,
                },
                params={"api-version": self._api_version, "$expand": "All"},
                timeout=(10, 20),
            )
            get_response.raise_for_status()
            current_tags = get_response.json().get("fields", {}).get("System.Tags", "")
            
            # Combine existing tags with new tags
            existing_tags = set(tag.strip() for tag in current_tags.split(";") if tag.strip())
            all_tags = list(existing_tags.union(set(tags)))

            # Update the work item with all tags
            response = requests.patch(
                urllib.parse.urljoin(
                    self._base_url,
                    "_apis/wit/workitems",
                ),
                headers={
                    "Content-Type": "application/json-patch+json",
                    "Authorization": self._auth_bearer,
                },
                params={"api-version": self._api_version, "id": workitem_id},
                json=[
                    {
                        "op": "replace",
                        "path": "/fields/System.Tags",
                        "value": "; ".join(all_tags),
                    },
                ],
                timeout=(10, 20),
            )
            response.raise_for_status()
        except requests.exceptions.RequestException as exc:
            resp_status, resp_text = get_resp(exc)
            logger.exception(
                "Azure Devops API request failed",
                response_status=resp_status,
                response_content=resp_text,
            )
            raise ValueError from exc
        try:
            logger.info(f"Added tags to Task #{workitem_id}: {tags}")
            return response.json()
        except requests.exceptions.JSONDecodeError as exc:
            logger.error("Invalid response", response=response.text)
            msg = "Unable to perform the operation."
            raise ValueError(msg) from exc

    def assign_ticket(self, workitem_id: int, assignee: str) -> dict:
        """
        Assign ticket to person.

        Returns:
            Response from API.

        Raises:
            ValueError: if request failed.
        """
        try:
            response = requests.patch(
                urllib.parse.urljoin(
                    self._base_url,
                    "_apis/wit/workitems",
                ),
                headers={
                    "Content-Type": "application/json-patch+json",
                    "Authorization": self._auth_bearer,
                },
                params={"api-version": self._api_version, "id": workitem_id},
                json=[
                    {
                        "op": "add",
                        "path": "/fields/System.AssignedTo",
                        "value": assignee,
                    },
                ],
                timeout=(10, 20),
            )
            response.raise_for_status()
        except requests.exceptions.RequestException as exc:
            resp_status, resp_text = get_resp(exc)
            logger.exception(
                "Azure Devops API request failed",
                response_status=resp_status,
                response_content=resp_text,
            )
            raise ValueError from exc
        try:
            return response.json()
        except requests.exceptions.JSONDecodeError as exc:
            logger.error("Invalid response", response=response.text)
            msg = "Unable to perform the operation."
            raise ValueError(msg) from exc

    def add_comment(self, workitem_id: int, text: str) -> dict:
        """
        Add comment to a workitem.
        Formats markdown as HTML.

        Returns:
            Response from API.

        Raises:
            ValueError: if request failed.
        """
        try:
            response = requests.post(
                urllib.parse.urljoin(
                    self._base_url,
                    f"_apis/wit/workItems/{workitem_id}/comments",
                ),
                headers={
                    "Content-Type": "application/json",
                    "Authorization": self._auth_bearer,
                },
                params={"api-version": "7.0-preview.3"},
                json={"text": text},
                timeout=(10, 20),
            )
            response.raise_for_status()
        except requests.exceptions.RequestException as exc:
            resp_status, resp_text = get_resp(exc)
            logger.exception(
                "Azure Devops API request failed",
                response_status=resp_status,
                response_content=resp_text,
            )
            raise ValueError from exc
        try:
            # PLog.write(
            #     f"<p><b><font color=blue>Added a comment to Task#{workitem_id}</font></b></p>"
            # )
            # PLog.write(text.replace("\n", " "))
            return response.json()
        except requests.exceptions.JSONDecodeError as exc:
            logger.error("Invalid response", response=response.text)
            msg = "Unable to perform the operation."
            raise ValueError(msg) from exc

    def link_duplicate_work_items(self, original_workitem_id: int, duplicate_workitem_id: int) -> dict:
        """
        Link two work items as duplicates.

        Args:
            original_workitem_id (int): The ID of the original work item.
            duplicate_workitem_id (int): The ID of the work item to mark as a duplicate.

        Returns:
            dict: Response from the API.

        Raises:
            ValueError: if the request failed.
        """
        work_item_update_url = urllib.parse.urljoin(
            self._base_url,
            f"_apis/wit/workItems/{original_workitem_id}"
        )
        link_url = urllib.parse.urljoin(
            self._base_url,
            f"_apis/wit/workItems/{duplicate_workitem_id}"
        )

        patch_document = [
            {
                "op": "add",
                "path": "/relations/-",
                "value": {
                    "rel": "System.LinkTypes.Duplicate-Forward",
                    "url": link_url,
                    "attributes": {
                        "comment": "Marked as duplicate by AI"
                    },
                },
            },
        ]

        try:
            response = requests.patch(
                work_item_update_url,
                headers={
                    "Content-Type": "application/json-patch+json",
                    "Authorization": self._auth_bearer,
                },
                params={"api-version": "7.1-preview.3"},
                json=patch_document,
                timeout=(10, 20),
            )
            response.raise_for_status()
        except requests.exceptions.RequestException as exc:
            resp_status, resp_text = get_resp(exc)
            logger.exception(
                "Azure Devops API request failed",
                response_status=resp_status,
                response_content=resp_text,
            )
            raise ValueError from exc
        try:
            return response.json()
        except requests.exceptions.JSONDecodeError as exc:
            logger.error("Invalid response", response=response.text)
            msg = "Unable to perform the operation."
            raise ValueError(msg) from exc


    def create_attachment(self, datafile, filename) -> dict:
        """Upload file and link to tiket."""
        try:
            response = requests.post(
                urllib.parse.urljoin(
                    self._base_url,
                    "_apis/wit/attachments",
                ),
                headers={
                    "Content-Type": "application/octet-stream",
                    "Authorization": self._auth_bearer,
                },
                params={"filename": filename, "api-version": "7.0-preview.3"},
                data=datafile,
                timeout=(10, 20),
            )
            response.raise_for_status()
            return response.json().get("url")
        except requests.exceptions.RequestException as exc:
            resp_status, resp_text = get_resp(exc)
            logger.exception(
                "Azure Devops API request failed",
                response_status=resp_status,
                response_content=resp_text,
            )
            raise ValueError from exc

    def get_attachment_by_id(self, atid: str):
        """Get workitems by given ids."""
        try:
            return StringIO(requests.get(
                urllib.parse.urljoin(
                    self._base_url,
                    f"_apis/wit/attachments/{atid}",
                ),
                headers={
                    "Content-Type": "application/json",
                    "Authorization": self._auth_bearer,
                },
                params={
                    "api-version": self._api_version,
                },
                timeout=(10, 20),
            ).text)
        except Exception as exc:
            logger.exception("Invalid response")
            raise ValueError("Cannot fulfill the operation") from exc


    def link_attachment(
        self,
        workitem_id: int,
        att_url: str,
        comment: str = "simplified CSV with scan report",
    ) -> dict:
        """
        Attach preloaded attachment by id to workitem.

        Returns:
            Response from API.

        Raises:
            ValueError: if request failed.
        """
        try:
            response = requests.patch(
                urllib.parse.urljoin(
                    self._base_url,
                    "_apis/wit/workitems",
                ),
                headers={
                    "Content-Type": "application/json-patch+json",
                    "Authorization": self._auth_bearer,
                },
                params={"api-version": self._api_version, "id": workitem_id},
                json=[
                    {
                        "op": "add",
                        "path": "/relations/-",
                        "value": {
                            "rel": "AttachedFile",
                            "url": att_url,
                            "attributes": {
                                "comment": comment,
                            },
                        },
                    }
                ],
                timeout=(10, 20),
            )
            response.raise_for_status()
        except requests.exceptions.RequestException as exc:
            resp_status, resp_text = get_resp(exc)
            logger.exception(
                "Azure Devops API request failed",
                response_status=resp_status,
                response_content=resp_text,
            )
            raise ValueError from exc
        try:
            return response.json()
        except requests.exceptions.JSONDecodeError as exc:
            msg = "Unable to perform the operation."
            raise ValueError(msg) from exc

    def create_workitem(
        self, workitem_type: str, title: str, description: str, area_path: str
    ) -> dict:
        """
        Creates a work item with a given title, description, and area path.

        Args:
            workitem_type (str): The work item type to create. (USE Task please, it's simplest one)
            title (str): The title of the work item.
            description (str): The description of the work item.
            area_path (str): The area path for the work item.

        Returns:
            Response from API.

        Raises:
            ValueError: if request failed.
        """
        url = f"_apis/wit/workitems/${workitem_type}"
        headers = {
            "Content-Type": "application/json-patch+json",
            "Authorization": self._auth_bearer,
        }
        params = {"api-version": self._api_version}
        payload = [
            {"op": "add", "path": "/fields/System.Title", "value": title},
            {
                "op": "add",
                "path": "/fields/System.Description",
                "value": description,
            },
            {
                "op": "add",
                "path": "/fields/System.AreaPath",
                "value": area_path,
            },
        ]

        try:
            response = requests.post(
                urllib.parse.urljoin(self._base_url, url),
                headers=headers,
                params=params,
                json=payload,
                timeout=(10, 20),
            )
            response.raise_for_status()
        except requests.exceptions.RequestException as exc:
            resp_status, resp_text = get_resp(exc)
            logger.exception(
                "Azure Devops API request failed",
                response_status=resp_status,
                response_content=resp_text,
            )
            raise ValueError from exc
        try:
            return response.json()
        except requests.exceptions.JSONDecodeError as exc:
            msg = "Failed to decode response as JSON."
            raise ValueError(msg) from exc

    def update_workitem_title(self, workitem_id: int, new_title: str) -> dict:
        """
        Update the title of an existing work item.

        Args:
            workitem_id (int): The ID of the work item to update.
            new_title (str): The new title for the work item.

        Returns:
            dict: Response from the API.

        Raises:
            ValueError: if the request failed.
        """
        url = f"_apis/wit/workitems/{workitem_id}"
        headers = {
            "Content-Type": "application/json-patch+json",
            "Authorization": self._auth_bearer,
        }
        params = {"api-version": self._api_version}
        payload = [
            {
                "op": "replace",
                "path": "/fields/System.Title",
                "value": new_title
            }
        ]

        try:
            response = requests.patch(
                urllib.parse.urljoin(self._base_url, url),
                headers=headers,
                params=params,
                json=payload,
                timeout=(10, 20),
            )
            response.raise_for_status()
        except requests.exceptions.RequestException as exc:
            resp_status, resp_text = get_resp(exc)
            logger.exception(
                "Azure Devops API request failed",
                response_status=resp_status,
                response_content=resp_text,
            )
            raise ValueError from exc
        try:
            return response.json()
        except requests.exceptions.JSONDecodeError as exc:
            msg = "Failed to decode response as JSON."
            raise ValueError(msg) from exc

if __name__ == "__main__":

    assignee = "<EMAIL>"

    token = os.environ["ADO_PERSONAL_ACCESS_TOKEN"]
    organization = "Ivanti"
    project = "AI Automation and Observability"

    az_devops = AzureDevops(token, organization, project, api_version="7.0")
    
    title = "Incident Report: Error Percentage Exceeds Threshold in IRWPRD-WEBAPP" 
    description_html = "<div class=\"bg-white p-6 rounded-lg shadow-md\">\n  <h2 class=\"text-xl font-bold mb-4\">Incident Report: Error Percentage Exceeds Threshold in IRWPRD-WEBAPP</h2>\n  <table class=\"w-full mb-8 table-auto border-collapse border border-gray-300\">\n    <tr class=\"border-b\">\n      <td class=\"font-bold w-1/2 pr-4 pb-2\">Problem Reference:</td>\n      <td class=\"w-1/2 pb-2\">8206d61b-3a17-4bdc-bea7-890d7f9cad3a</td>\n    </tr>\n    <tr>\n      <td class=\"font-bold w-1/2 pr-4 pb-2\">Customer affected:</td>\n      <td class=\"w-1/2 pb-2\">IvantiCloud - US</td>\n    </tr>\n    <tr>\n      <td class=\"font-bold w-1/2 pr-4 pb-2\">Date Incident Began:</td>\n      <td class=\"w-1/2 pb-2\">01 Jun 2024, 10:00 AM</td>\n    </tr>\n    <tr>\n      <td class=\"font-bold w-1/2 pr-4 pb-2\">Date Incident Resolved:</td>\n      <td class=\"w-1/2 pb-2\">01 Jun 2024, 10:06 AM</td>\n    </tr>\n    <tr>\n      <td class=\"font-bold w-1/2 pr-4\">Teams(s) Involved:</td>\n      <td class=\"w-1/2\">Application Development, Infrastructure, DevOps</td>\n    </tr>\n  </table>\n\n  <div class=\"border-t-2 border-black pt-6 mb-6\">\n    <h3 class=\"bg-gray-200 p-2 font-bold\">Customer Impact</h3>\n    <div class=\"border border-gray-300 p-4 mb-4\">\n      <p>During the incident period, customers experienced higher than usual error rates while accessing the IRWPRD-WEBAPP application. This led to intermittent failures and degraded performance, impacting user experience and operational efficiency.</p>\n    </div>\n  </div>\n\n  <div class=\"border-t-2 border-black pt-6 mb-6\">\n    <h3 class=\"bg-gray-200 p-2 font-bold\">Root Cause</h3>\n    <div class=\"border border-gray-300 p-4 mb-4\">\n      <p>The incident was triggered by an abnormal spike in error rates, exceeding the predefined threshold of 2.0% for a duration of 5 minutes. The root cause analysis revealed that the increase in error rates was due to a recent deployment that introduced a configuration error in the application's error handling logic. This error resulted in unhandled exceptions for certain user actions, leading to an increased error percentage.</p>\n      <p>The NRQL query used for monitoring was:</p>\n      <pre class=\"bg-gray-100 p-3\">\nSELECT ((filter(count(newrelic.timeslice.value), where metricTimesliceName = 'Errors/all') / filter(count(newrelic.timeslice.value), WHERE metricTimesliceName IN ('HttpDispatcher', 'OtherTransaction/all'))) OR 0) * 100 FROM Metric WHERE appName like '%WEBAPP%' AND appName NOT LIKE '%CZERO%' AND metricTimesliceName IN ('Errors/all', 'HttpDispatcher', 'OtherTransaction/all', 'Agent/MetricsReported/count') FACET appName\n      </pre>\n    </div>\n  </div>\n\n  <div class=\"border-t-2 border-black pt-6 mb-6\">\n    <h3 class=\"bg-gray-200 p-2 font-bold\">Remedy</h3>\n    <div class=\"border border-gray-300 p-4 mb-4\">\n      <p>The immediate action taken to resolve the incident involved rolling back the recent deployment to a previous stable version. This action was taken promptly, leading to a quick recovery and normalization of error rates.</p>\n    </div>\n  </div>\n\n  <div class=\"border-t-2 border-black pt-6\">\n    <h3 class=\"bg-gray-200 p-2 font-bold\">Preventive Action</h3>\n    <div class=\"border border-gray-300 p-4 mb-4\">\n      <p>To prevent similar incidents in the future, the following measures have been implemented:</p>\n      <ul class=\"list-disc pl-5\">\n        <li>Enhanced review and testing procedures for configuration changes, especially those affecting error handling logic.</li>\n        <li>Introduction of additional monitoring and alerting mechanisms to detect and address anomalies in error rates more rapidly.</li>\n        <li>Conducting a thorough post-mortem analysis to document the incident and share learnings across teams.</li>\n      </ul>\n    </div>\n  </div>\n</div>"
    workitem_html = az_devops.create_workitem("task", title, description_html)
    az_devops.assign_ticket(workitem_html["id"], assignee)

    logger.info(
        textwrap.dedent(
            f"""
                Created workitem details:
                id: {workitem_html['id']}
                url: {workitem_html['url']}
                """
        ).strip()
    )

    