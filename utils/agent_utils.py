"""
This file contains utility functions specific to autogen agents.
"""

import autogen
from autogen import OpenAIWrapper, config_list_from_json
from .misc import fix_broken_json

from dotenv import load_dotenv

load_dotenv(override=True)

import os
import json
import logging

logger = logging.getLogger(__name__)

# here going to use azure openai gpt
config_list3 = config_list_from_json(
    "OAI_CONFIG_LIST", filter_dict={"model": os.environ.get("AZURE_GPT3_MODEL_NAME", "gpt-35-turbo")}
)

config_list4 = config_list_from_json(
    "OAI_CONFIG_LIST", filter_dict={"model": os.environ.get("AZURE_GPT4_MODEL_NAME", "obv-gpt4t0125")}
)


def get_end_intent(message):

    IS_TERMINATE_SYSTEM_PROMPT = """You are an expert in text and sentiment analysis. Based on the provided text, please respond with whether the intent is to end/pause the conversation or contintue the conversation. If the text provides all-caps statements such as "TERMINATE" or "CONTINUE", prioritize these when assesing intent. Your response MUST be in JSON format, with the following format:
    {{
        "analysis": <your analysis of the text>,
        "intent": "end" or "continue"
    }}

    NOTE: If the intent is to get feedback from the User or UserProxy, the intent should be "end".

    IMPORTANT: ONLY respond with the JSON object, and nothing else. If you respond with anything else, the system will not be able to understand your response.

    """

    # TODO: Ensure JSON response with return_json param
    client = OpenAIWrapper(config_list=config_list4)
    response = client.create(
        messages=[
            {"role": "system", "content": IS_TERMINATE_SYSTEM_PROMPT},
            {"role": "user", "content": message["content"]},
        ]
    )
    json_response = autogen.ConversableAgent._format_json_str(
        response.choices[0].message.content
    )
    try:
        json_response = json.loads(json_response)
    except Exception as error:
        json_response = fix_broken_json(json_response)
    logger.info("Termination analysis: %s", json_response["analysis"])
    logger.info("Termination intent: %s", json_response["intent"])
    return json_response["intent"]
