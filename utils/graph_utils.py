"""
This module contains utilities for working with graph databases.
"""

import os
import json
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor
from gremlin_python.driver import client, serializer
from gremlin_python.driver.protocol import GremlinServerError
from gremlin_python.driver.aiohttp.transport import AiohttpTransport

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global thread pool executor and semaphore
thread_pool = ThreadPoolExecutor(max_workers=10)
concurrency_semaphore = asyncio.Semaphore(10)

def get_client(cluster_id):
    """Creates and returns a Gremlin client for the specified cluster."""
    endpoint = os.getenv("COSMOS_GREMLIN_ENDPOINT")
    database = "neurons"
    collection = cluster_id
    key = os.getenv("COSMOS_GREMLIN_KEY")

    return client.Client(
        f'{endpoint}',
        'g',
        username=f"/dbs/{database}/colls/{collection}",
        password=key,
        message_serializer=serializer.GraphSONSerializersV2d0(),
        transport_factory=lambda: AiohttpTransport(call_from_event_loop=True)
    )

async def execute_query_with_retry(client, query, bindings=None, max_retries=5):
    """Executes a Gremlin query with retry logic for rate limiting."""
    retry_count = 0
    while retry_count < max_retries:
        try:
            async with concurrency_semaphore:
                loop = asyncio.get_running_loop()
                future = await loop.run_in_executor(thread_pool, client.submitAsync, query, bindings)
                result_set = await loop.run_in_executor(thread_pool, future.result)
                results = await loop.run_in_executor(thread_pool, result_set.all().result)
            return results
        except GremlinServerError as e:
            if e._status_attributes['x-ms-status-code'] == 429:
                retry_count += 1
                retry_after_ms = e.status_attributes.get('x-ms-retry-after-ms', 1000)
                retry_after_seconds = float(retry_after_ms) / 1000.0
                consumed_ru = e.status_attributes.get('x-ms-total-request-charge', 0.0)
                logger.warning(f"Rate limited. Consumed {consumed_ru} RUs. Retrying in {retry_after_seconds:.2f} seconds... (Attempt {retry_count}/{max_retries})")
                await asyncio.sleep(retry_after_seconds)
            else:
                logger.error(f"Query failed: {e.status_code} {e.status_message}")
                raise
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            raise
    logger.error(f"Max retries exceeded for query: {query}")
    raise Exception(f"Max retries exceeded for query: {query}")

async def get_subgraph(client, start_node_id, depth, batch_size=100):
    """Gets a subgraph starting from a given node ID up to a specified depth."""
    try:
        nodes = set()
        edges = []

        # Get the starting node
        query = f"g.V('{start_node_id}').valueMap(true)"
        result = await execute_query_with_retry(client, query)
        if result:
            nodes.add((start_node_id, json.dumps(result[0])))

        # BFS to get nodes and edges
        current_depth = 0
        frontier = {start_node_id}
        while current_depth < depth and frontier:
            next_frontier = set()
            for node_id in frontier:
                # Get connected nodes
                query = f"""
                g.V('{node_id}').both().dedup()
                    .project('id', 'properties')
                    .by(id())
                    .by(valueMap(true))
                """
                connected_nodes = await execute_query_with_retry(client, query)

                for connected_node in connected_nodes:
                    node_id = connected_node['id']
                    if node_id not in nodes:
                        nodes.add((node_id, json.dumps(connected_node['properties'])))
                        next_frontier.add(node_id)

                # Get edges
                query = f"""
                g.V('{node_id}').bothE()
                    .project('id', 'source', 'target', 'label', 'properties')
                    .by(id())
                    .by(outV().id())
                    .by(inV().id())
                    .by(label())
                    .by(valueMap())
                """
                connected_edges = await execute_query_with_retry(client, query)
                edges.extend(connected_edges)

            frontier = next_frontier
            current_depth += 1

        # Convert sets to lists for JSON serialization and process node properties
        nodes_list = []
        node_ids = set()
        for node_id, node_properties in nodes:
            node_data = {'id': node_id, **json.loads(node_properties)}
            for key, value in node_data.items():
                if isinstance(value, list):
                    if key == 'properties':
                        # Parse the 'properties' string and convert its values
                        props = json.loads(value[0])
                        for prop_key, prop_value in props.items():
                            if isinstance(prop_value, list):
                                props[prop_key] = prop_value[0] if prop_value else ''
                        node_data[key] = json.dumps(props)
                    else:
                        node_data[key] = value[0] if value else ''
            nodes_list.append(node_data)
            node_ids.add(node_id)

        # Filter and simplify edge properties
        filtered_edges = []
        for edge in edges:
            if edge['source'] in node_ids and edge['target'] in node_ids:
                for key, value in edge['properties'].items():
                    if isinstance(value, list) and len(value) == 1:
                        edge['properties'][key] = value[0]
                filtered_edges.append(edge)

        return {
            'nodes': nodes_list,
            'edges': filtered_edges
        }

    except Exception as e:
        logger.error(f"Error getting subgraph for node {start_node_id}: {str(e)}")
        return None

async def get_subgraph_wrapper(cluster_id, start_node_id, depth):
    """Wrapper function to handle client lifecycle."""
    client = None
    try:
        client = get_client(cluster_id)
        subgraph = await get_subgraph(client, start_node_id, depth)
        return subgraph
    finally:
        if client:
            await client.close()

def get_subgraph_sync(cluster_id, start_node_id, depth):
    """Synchronous version of get_subgraph."""
    return asyncio.run(get_subgraph_wrapper(cluster_id, start_node_id, depth))

def get_subgraph_json(cluster_id, start_node_id, depth):
    """Gets a subgraph and returns it as a JSON string."""
    subgraph = get_subgraph_sync(cluster_id, start_node_id, depth)
    return json.dumps(subgraph, indent=2) 