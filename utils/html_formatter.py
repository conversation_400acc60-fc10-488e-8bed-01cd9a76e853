import textwrap

def nr_issue_to_html(issue):
    html_content = f"""
    <div>
        <h2 style="background-color: #0078d4; color: white; padding: 10px; border-radius: 8px 8px 0 0; margin: 0;">Issue Details</h2>
        <div style="border: 1px solid #ddd; border-radius: 8px; padding: 20px; background-color: #f9f9f9;">
            <p><strong>ID:</strong> {issue['id']}</p>
            <p><strong>Title:</strong> {issue['title']}</p>
            <p><strong>Priority:</strong> {issue['priority']}</p>
            <p><strong>Impacted Entities:</strong></p>
            <ul style="padding-left: 20px;">
    """
    
    for entity in issue['impactedEntities']:
        html_content += f"<li>{entity}</li>"
    
    html_content += f"""
            </ul>
            <p><strong>Total Incidents:</strong> {issue['totalIncidents']}</p>
            <p><strong>State:</strong> {issue['state']}</p>
            <p><strong>Trigger:</strong> {issue['trigger']}</p>
            <p><strong>Is Correlated:</strong> {issue['isCorrelated']}</p>
            <p><strong>Created At:</strong> {issue['createdAt']}</p>
            <p><strong>Updated At:</strong> {issue['updatedAt']}</p>
            <p><strong>Sources:</strong> {', '.join(issue['sources'])}</p>
            <p><strong>Alert Policy Names:</strong></p>
            <ul style="padding-left: 20px;">
    """
    
    for policy in issue['alertPolicyNames']:
        html_content += f"<li>{policy}</li>"
    
    html_content += f"""
            </ul>
            <p><strong>Alert Condition Names:</strong></p>
            <ul style="padding-left: 20px;">
    """
    
    for condition in issue['alertConditionNames']:
        html_content += f"<li>{condition}</li>"
    
    html_content += f"""
            </ul>
            <p><strong>Workflow Name:</strong> {issue['workflowName']}</p>
            <p><strong>Issue URL:</strong> <a href="{issue['issueUrl']}" target="_blank">Link to Issue</a></p>
        </div>
    </div>
    """

    issue_html = {"title": issue['title'], "description": html_content}
    
    return issue_html

def nr_issue_to_html2(data):
    html_template = f"""
    <div style="font-family: Arial, sans-serif;">
        <h2>Closed Issue</h2>
        <div style="border: 1px solid #ddd; padding: 10px; margin-bottom: 20px;">
            <p><strong>Severity:</strong> {data['priority']}</p>
            <p><strong>Title:</strong> {data['title']}</p>
            <p><strong>Issue URL:</strong> <a href="{data['issueUrl']}">{data['issueUrl']}</a></p>
            <p><strong>Created at:</strong> {data['createdAt']}</p>
            <p><strong>Updated at:</strong> {data['updatedAt']}</p>
            <p><strong>Condition:</strong> {data['alertConditionNames'][0]}</p>
            <p><strong>Source:</strong> {", ".join(data['sources'])}</p>
        </div>

        <h3>Incidents ({data['totalIncidents']})</h3>
        <div style="border: 1px solid #ddd; padding: 10px;">
            <p><strong>Severity:</strong> {data['priority']}</p>
            <p><strong>State:</strong> {data['state']}</p>
            <p><strong>Trigger:</strong> {data['trigger']}</p>
            <p><strong>Alert Policy:</strong> {", ".join(data['alertPolicyNames'])}</p>
            <p><strong>Alert Condition:</strong> {data['alertConditionNames'][0]}</p>
            <div>
                <img src="{data['chartLink']}" alt="Incident Chart" style="width: 100%; max-width: 800px;">
            </div>
            <div style="margin-top: 20px;">
                <p><strong>Impacted Entities:</strong></p>
                <ul>
                    {"".join(f"<li>{entity}</li>" for entity in data['impactedEntities'])}
                </ul>
            </div>
        </div>
    </div>
    """

    issue_html = {"title": data['title'], "description": html_template}
    return issue_html



if __name__ == "__main__":
    issue = {
        "id": "785304ef-2d90-45be-8cab-9022a9a95948",
        "issueUrl": "https://radar-api.service.newrelic.com/accounts/1093620/issues/785304ef-2d90-45be-8cab-9022a9a95948?notifier=WEBHOOK",
        "title": "Metric query deviated from the baseline for at least 5 minutes on 'High Application Response Time'",
        "priority": "CRITICAL",
        "impactedEntities": ["LDZ_Devprod_AppServer_APM_Agent-apirouting"],
        "totalIncidents": "1",
        "state": "CLOSED",
        "trigger": "INCIDENT_CLOSED",
        "isCorrelated": "false",
        "createdAt": *************,
        "updatedAt": *************,
        "sources": ["newrelic"],
        "alertPolicyNames": ["Golden Signals"],
        "alertConditionNames": ["High Application Response Time"],
        "workflowName": "obv-ai-processing"
    }

    issue = {"id": "33f9085e-2fd6-48a2-9537-6612ac3ef9cb", "issueUrl": "https://radar-api.service.newrelic.com/accounts/1/issues/0ea2df1c-adab-45d2-aae0-042b609d2322?notifier=SLACK", "title": "aks-default-********-vmss0006ui query result is > 85.0 for 5 minutes on 'High CPU'", "priority": "CRITICAL", "impactedEntities": ["aks-default-********-vmss0006ui"], "totalIncidents": 1, "state": "CLOSED", "trigger": "INCIDENT_CLOSED", "isCorrelated": "false", "createdAt": *************, "updatedAt": *************, "sources": ["newrelic"], "alertPolicyNames": ["Golden Signals"], "alertConditionNames": ["High CPU"], "workflowName": "DBA Team workflow", "chartLink": "https://gorgon.nr-assets.net/image/aefdbc7f-ab4c-45c5-b4e1-4e63613edc1c?config.legend.enabled=false&width=400&height=210"}

    html_output = nr_issue_to_html(issue)
    print(html_output)
