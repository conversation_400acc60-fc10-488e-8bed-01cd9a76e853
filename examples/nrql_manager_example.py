#!/usr/bin/env python3
"""
Example script demonstrating usage of the NRQL Manager and entity classes.
"""

import os
import sys
import logging
from datetime import datetime, timedelta, UTC
from dotenv import load_dotenv
from pprint import pprint

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import New Relic modules
from lib.new_relic.client import NewRelicGraphQLClient
from lib.new_relic.query import NewRelicQueryClient
from lib.new_relic.nrql_manager import NRQLManager
from lib.new_relic.entities import PodEntity, NodeEntity, HostEntity, ApplicationEntity

# Load environment variables and configure logging
load_dotenv()
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('example')

def main():
    """Main entry point for the example script."""
    
    # Get API key and account ID from environment variables
    api_key = os.environ.get('NEWRELIC_API_KEY')
    account_id = os.environ.get('NEWRELIC_ACCOUNT_ID')
    
    if not api_key or not account_id:
        logger.error("NEWRELIC_API_KEY and NEWRELIC_ACCOUNT_ID environment variables must be set")
        sys.exit(1)
    
    # Initialize New Relic clients
    client = NewRelicGraphQLClient(api_key=api_key, account_id=account_id)
    query_client = NewRelicQueryClient(client)
    
    # Initialize NRQL Manager
    nrql_manager = NRQLManager()
    
    # Example 1: Use the NRQL Manager directly
    logger.info("Example 1: Using the NRQL Manager directly")
    try:
        # Get available entity types
        entity_types = nrql_manager.get_entity_types()
        logger.info(f"Available entity types: {', '.join(entity_types)}")
        
        # Get available metrics for Kubernetes pods
        pod_metrics = nrql_manager.get_available_queries('kubernetes_pod')
        logger.info(f"Available metrics for kubernetes_pod: {', '.join(pod_metrics.keys())}")
        
        # Define time window
        now = datetime.now(UTC)
        since_time = now - timedelta(minutes=10)
        until_time = now
        
        # Convert to epoch milliseconds
        since_time_ms = int(since_time.timestamp() * 1000)
        until_time_ms = int(until_time.timestamp() * 1000)
        
        logger.info(f"Using time window: {since_time} to {until_time}")
        logger.info(f"In epoch milliseconds: {since_time_ms} to {until_time_ms}")
        
        # Get and format a specific query
        pod_cpu_query = nrql_manager.get_query(
            'kubernetes_pod',
            'cpu_usage',
            pod_name='example-pod',
            cluster_name='example-cluster',
            since_time_ms=since_time_ms,
            until_time_ms=until_time_ms
        )
        logger.info(f"Generated NRQL query:\n{pod_cpu_query}")
    except Exception as e:
        logger.error(f"Error in Example 1: {str(e)}")
    
    print("\n" + "-" * 80 + "\n")
    
    # Example 2: Use the entity classes
    logger.info("Example 2: Using entity classes")
    try:
        # Initialize entity objects
        pod_entity = PodEntity(query_client, nrql_manager)
        node_entity = NodeEntity(query_client, nrql_manager)
        host_entity = HostEntity(query_client, nrql_manager)
        
        # Define time window for entity queries
        now = datetime.now(UTC)
        since_time = now - timedelta(minutes=30)
        until_time = now
        
        # Convert to epoch milliseconds
        since_time_ms = int(since_time.timestamp() * 1000)
        until_time_ms = int(until_time.timestamp() * 1000)
        
        logger.info(f"Entity query time window: {since_time} to {until_time}")
        
        # Uncomment to execute actual queries against New Relic
        # Note: This requires valid entity names in your New Relic account
        
        # # Get pod CPU usage
        # logger.info("Getting CPU usage for example-pod")
        # cpu_usage = pod_entity.get_cpu_usage(
        #     "example-pod", 
        #     "example-cluster", 
        #     since_time_ms=since_time_ms,
        #     until_time_ms=until_time_ms
        # )
        # pprint(cpu_usage)
        # 
        # # Get node disk usage
        # logger.info("Getting disk usage for example-node")
        # disk_usage = node_entity.get_disk_usage(
        #     "example-node", 
        #     since_time_ms=since_time_ms,
        #     until_time_ms=until_time_ms
        # )
        # pprint(disk_usage)
        # 
        # # Get host metrics
        # logger.info("Getting metrics for example-host")
        # host_metrics = host_entity.get_cpu_memory_disk(
        #     "example-host", 
        #     since_time_ms=since_time_ms,
        #     until_time_ms=until_time_ms
        # )
        # pprint(host_metrics)
        
        logger.info("Entity classes initialized successfully. Uncomment the query execution code to run actual queries.")
    except Exception as e:
        logger.error(f"Error in Example 2: {str(e)}")

if __name__ == "__main__":
    main() 